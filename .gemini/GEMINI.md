## Gemini Added Memories
- The user prefers to speak to me in Chinese.

## 项目核心知识备忘

### 身份与原则
- **角色**: 龙族MMORPG游戏服务器开发工程师。
- **核心指南**: `docs/guidelines.md` 是首要指导文档。
- **首要原则**: **最小改动原则**。避免大规模重构，优先新增接口，保持代码风格与上下文一致。

### 架构与技术
- **架构**: 多进程分布式系统。
  - `glinkd`: 网关
  - `gdeliveryd`: 会话及非场景业务
  - `gs`: 场景逻辑
  - `gamedbd`: 数据库代理
- **协议**:
  - **GNET**: 用于基础架构和数据库RPC (`rpcalls.xml`)。
  - **Protobuf**: 用于新业务逻辑和客户端通信 (`.proto` files)。
- **核心库**: `zslib`，提供了`Octets`, `Marshal`, 线程池, 网络IO等基础功能。

### 开发实践要点
- **业务归属**:
  - 场景内逻辑 -> `game/gs/`
  - 场景外逻辑 -> `net/gdeliveryd/`
- **数据库操作**:
  - 必须通过 `gamedbd` 进程提供的 **GNET RPC** 进行。
  - 遵循 `db.md` 中定义的事务和数据访问模式。
- **技能系统**:
  - 位于 `skill/` 目录，作为动态库被 `gs` 加载。
  - `skill/parser` 用于将配置生成C++代码。
  - `skill` 模块与 `gs` 模块通过 `object_interface` 接口解耦。
- **代码风格**:
  - `game/` 目录与 `net/` 目录风格有差异，需遵循各自上下文。
  - `net/` 目录下的代码遵循 `GNET` 命名空间和 `CamelCase` 风格。
