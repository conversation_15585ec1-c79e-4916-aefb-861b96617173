# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 开发命令和工作流程

### 构建和编译
```bash
# 配置构建环境（会自动更新外部依赖）
./configure.sh -t clang-outside

# 编译项目（支持并发编译，JOB_NUM最大不超过40）
./buildall.sh -j16
```

### 部署和运行
```bash
# 生成运行环境
./tools/gen_game_package.sh <TARGET_DIR> dev

# 安装配置
cd <TARGET_DIR>/dev_game && ./gameinstall

# 启动所有服务
cd workdir && ./service.sh start

# 停止所有服务
./service.sh stop
```

## 项目架构概述

这是一个成熟的大型MMORPG游戏服务器，采用单服多进程分布式架构：

### 核心服务进程架构
- **glinkd** - 网关进程：处理客户端连接和协议转发
- **gdeliveryd** - 会话服务进程：管理玩家会话和非场景业务（登录、聊天、邮件、公会等）
- **gs** - 场景进程：处理游戏核心逻辑（战斗、任务、副本、活动等）
- **gamedbd** - 数据库代理进程：统一数据库访问接口，封装事务和RPC
- **ghubd** - 跨服中转进程：处理跨服数据传输
- **unamed** - 唯一名服务：管理全局唯一标识符
- **zdir** - 服务发现进程：维护服务注册与发现
- **gonlineinfod** - 在线信息服务：统计在线玩家信息

### 目录结构和职责
- **game/** - 场景内业务代码（gs相关），包含战斗、任务、副本等核心游戏逻辑
- **net/** - 场景外业务代码（其他进程），包含登录、聊天、邮件等系统业务
- **skill/** - 技能系统核心模块，独立的技能逻辑和数据处理
- **data/** - 游戏模板配置，支持XML和Convex格式
- **zslib/** - 祖龙公用基础库，提供序列化、多线程、网络IO、DB操作等基础组件
- **externals/** - 外部依赖库
- **tools/** - 开发工具脚本

### 协议和通信
- 新业务优先使用Protobuf协议
- 老系统使用GNET协议框架
- 协议定义：`net/rpcalls.xml`（GNET）或`.proto`文件（Protobuf）
- 通信模式：进程间RPC、客户端-服务器、数据库访问

## 开发规范和重要原则

### 历史代码处理策略（极其重要）
作为运营多年的成熟项目，必须严格遵循以下原则：
1. **最小改动原则** - 新功能尽量通过已有代码实现，避免大规模重构
2. **接口谨慎修改** - 对于被大量调用的接口，如非必要不要改动，而是额外添加接口
3. **渐进式改进** - 在修bug时顺带改进局部代码，但要避免引入新的bug
4. **风格一致性** - 新功能尽量贴合附近代码的风格，不要过分发挥新风格
5. **学习借鉴** - 从优秀模块和历史提交中学习，并应用到新功能中

### 代码风格规范
- **game/目录**：snake_case类名（如`gplayer_imp`），混合函数命名风格
- **net/目录**：CamelCase风格，GNET命名空间，类名如`GRoleInfo`
- **通用规范**：左花括号另起一行，成员变量使用下划线，常量全大写
- 错误处理使用明确的错误码系统，日志保持英文简洁格式

### 技术栈和依赖
- **语言**：C++11/C++14
- **构建系统**：CMake (>= 3.16)
- **编译工具**：默认使用Clang，支持GCC
- **核心依赖**：Protobuf v2.6.1, MySQL, Lua, Python3, Perl
- **系统要求**：Rocky9 Linux

## 重要文档参考

项目包含完整的文档体系，位于`docs/`目录：
- `guidelines.md` - 核心开发指导和架构理解
- `protocol.md` - 协议设计与进程间通信开发
- `db.md` - 数据库操作规范和事务处理
- `systems.md` - 游戏系统开发指南
- `zslib.md` - 祖龙基础库使用说明
- `quick_reference.md` - 快速参考手册
- `code_archaeology.md` - 历史经验记录和踩坑记录

## 开发注意事项

1. **稳定性优先**：这是一个拥有极高在线人数的生产环境MMORPG服务器，绝不可拿线上代码冒险
2. **业务归属分析**：场景内逻辑归属game/gs目录，场景外逻辑归属net/gdeliveryd目录
3. **性能考虑**：内存、网络、计算三个维度的优化，避免O(n²)以上复杂度
4. **安全性检查**：所有外部输入必须验证，操作前检查玩家权限
5. **历史兼容**：始终提醒最小改动原则和接口兼容性

遵循"正确完成开发需求"、"确保系统稳健高效"、"保持代码清晰易懂"的总体原则。