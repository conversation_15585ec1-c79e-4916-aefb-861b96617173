#ifndef CONF_DEFCONVEX_H
#define CONF_DEFCONVEX_H

#include <string>
#include <vector>
#include <memory>
#include <map>

#ifdef _USE_LUA_CONF
#include "luaconfread.h"
#endif
#include <hashmap.h>

#ifdef _USE_XML_CONF
#include "pugixml.hpp"
#endif

#include "define.h"


enum ITEMCOMMONPROP_ENUM {
	ITEMCOMPROP_ICONPATH = 0, // ITEMCOMPROP_ICONPATH
	ITEMCOMPROP_QUALITY = 1, // ITEMCOMPROP_QUALITY
	ITEMCOMPROP_PLAYER_MIN_LEVEL = 2, // ITEMCOMPROP_PLAYER_MIN_LEVEL
	ITEMCOMPROP_SELL2SHOP_MODE = 3, // ITEMCOMPROP_SELL2SHOP_MODE
	ITEMCOMPROP_PRICE = 4, // ITEMCOMPROP_PRICE
	ITEMCOMPROP_SHOPSELL_MODE = 5, // ITEMCOMPROP_SHOPSELL_MODE
	ITEMCOMPROP_SHOP_PRICE = 6, // ITEMCOMPROP_SHOP_PRICE
	ITEMCOMPROP_SHOP_REPU_TYPE = 7, // ITEMCOMPROP_SHOP_REPU_TYPE
	ITEMCOMPROP_SHOP_REPU_VALUE = 8, // ITEMCOMPROP_SHOP_REPU_VALUE
	ITEMCOMPROP_SHOP_ITEM_ID = 9, // ITEMCOMPROP_SHOP_ITEM_ID
	ITEMCOMPROP_SHOP_ITEM_NUM = 10, // ITEMCOMPROP_SHOP_ITEM_NUM
	ITEMCOMPROP_SELL_TO_SHOP_REPU_TYPE = 11, // ITEMCOMPROP_SELL_TO_SHOP_REPU_TYPE
	ITEMCOMPROP_SELL_TO_SHOP_REPU_VALUE = 12, // ITEMCOMPROP_SELL_TO_SHOP_REPU_VALUE
	ITEMCOMPROP_SELL_TO_SHOP_ITEM_ID = 13, // ITEMCOMPROP_SELL_TO_SHOP_ITEM_ID
	ITEMCOMPROP_SELL_TO_SHOP_ITEM_NUM = 14, // ITEMCOMPROP_SELL_TO_SHOP_ITEM_NUM
	ITEMCOMPROP_PILE_NUM_MAX = 15, // ITEMCOMPROP_PILE_NUM_MAX
	ITEMCOMPROP_PROC_TYPE = 16, // ITEMCOMPROP_PROC_TYPE
	ITEMCOMPROP_DROP_DESC_TEXT = 17, // ITEMCOMPROP_DROP_DESC_TEXT
	ITEMCOMPROP_DROP_MODEL_TYPE = 18, // ITEMCOMPROP_DROP_MODEL_TYPE
	ITEMCOMPROP_HIDE_FOR_SEARCH = 19, // ITEMCOMPROP_HIDE_FOR_SEARCH
	ITEMCOMPROP_COMPOSE_ITEM_COUNT = 20, // ITEMCOMPROP_COMPOSE_ITEM_COUNT
	ITEMCOMPROP_COMPOSE_OUTPUT_ID = 21, // ITEMCOMPROP_COMPOSE_OUTPUT_ID
	ITEMCOMPROP_COMPOSE_COST_MONEY = 22, // ITEMCOMPROP_COMPOSE_COST_MONEY
	ITEMCOMPROP_AUCTION_CLASS1 = 23, // ITEMCOMPROP_AUCTION_CLASS1
	ITEMCOMPROP_AUCTION_CLASS2 = 24, // ITEMCOMPROP_AUCTION_CLASS2
	ITEMCOMPROP_AUCTION_CLASS3 = 25, // ITEMCOMPROP_AUCTION_CLASS3
	ITEMCOMPROP_AUCTION_LEAST_COST_CASH = 26, // ITEMCOMPROP_AUCTION_LEAST_COST_CASH
	ITEMCOMPROP_CAN_BATCH_USE = 27, // ITEMCOMPROP_CAN_BATCH_USE
	ITEMCOMPROP_PROF_FILTER = 28, // ITEMCOMPROP_PROF_FILTER
	ITEMCOMPROP_COUNT = 29, // ITEMCOMPROP_COUNT

};

enum MISC {
	EXP_WING_MAX_POS = 5, // 幻化最大位置数
	WEHICLE_POS_MAX = 5, // 交通工具最多位置数
	PRODSKILL_MAX_LEVEL = 20, // 生产技能等级最大值
	PRODSKILL_CORRES_LIFEPROFID_COUNT = 4, // 生产技能对应的差事ID个数
	RECIPE_MAX_LEVEL = 10, // 配方等级最大值
	PROD_MAXCOUNT_PER_LEVEL = 6, // 每个生产等级生成产物个数最大值
	RECIPEPROD_MAX_QUALITY = 10, // 配方成品的品质上限
	EXP_TASKLIB_COUNT = 8, // 库任务组数（这个以后会被删掉！！！！改用下面那个64的）
	EXP_TASKLIB_CONFIG_TASK_COUNT = 64, // 新的库任务数量
	EXP_INSTANCE_PRE_TASK_COUNT = 4, // 副本前置任务个数
	EXP_KAIGUANG_PROP_COUNT = 8, // 开光属性个数
	EXP_EQUIP_ADDON_COUNT = 8, // 装备附加属性个数
	EXP_EQUIP_REINFORCE_MAX_LEVEL = 20, // 装备强化最高等级
	EXP_EQUIP_BREAK_ITEM_COUNT = 4, // 装备拆分获得物品个数
	EXP_GEMSTONE_MAX_LEVEL = 10, // 宝石最高等级
	EXP_PROF_ZHUANSHU_WUXUE_NUM = 5, // 职业专属武学数量
	EXP_PLANT_PRODUCT_COUNT = 8, // 种植产物个数
	EXP_GROW_PHASE_COUNT = 3, // 成长阶段个数
	EXP_BREED_PHASE_COUNT = 4, // 养殖阶段个数
	EXP_BREEDPHASE_PRODUCTCOUNT = 3, // 各养殖阶段产物个数
	EXP_MAIL_ATTACH_ITEM_NUM = 3, // 邮件携带道具数量
	EXP_APTITUDE_CONFIG_COUNT = 32, // 资质配置数组个数
	EXP_EQUIP_PROFICIENT_CFG_COUNT = 8, // 装备的精通配置数组个数
	EXP_PROF_APTITUDE_MAX = 32, // 职业涉及的资质个数上限
	EXP_XIULIAN_PREITEM_CFG_MAX = 4, // 修炼开启前提道具配置数组个数
	EXP_MARRIAGE_ANNIVERSARY_CFG_MAX = 6, // 结婚纪念日配置个数
	EXP_XIULIAN_PRESKILL_LEVEL_CFG_MAX = 21, // 修炼前提技能等级配置个数
	EXP_REINFORCE_WAY_COUNT = 4, // 强化途径数组个数
	EXP_GENITEM_EQUIP_ESTONE_COUNT = 4, // 装备生成时镶嵌的宝石个数
	EXP_XIULIAN_RESULT_SKILL_COUNT = 4, // 修炼时自动学会的技能个数
	EXP_XIULIAN_RESULT_SKILLSEQ_COUNT = 4, // 修炼时自动学会的技能包个数
	EXP_ADDONGRP_BASIC_COUNT = 8, // 附加属性组中的基础附加属性个数
	EXP_ADDON_GROUP_ADDON_COUNT = 40, // 附加属性组中的属性个数
	EXP_ADDONGRP_GEN_COUNT = 8, // 附加属性组中的可能出现属性的个数
	EXP_ADDONGRP_SKILLPROP_COUNT = 8, // 附加属性组中的技能属性组个数
	EXP_EQUIPPROP_SKILL_EFFECT_SKILL_COUNT = 32, // 装备属性技能组中受影响的技能个数
	EXP_SUITE_EQUIPMENT_COUNT = 24, // 套装中的装备物品个数
	EXP_LOTTERY_CANDIDATE_COUNT = 32, // 彩票的备选图案个数
	EXP_MEDICINE_VALUE_COUNT = 6, // 药品的回复值个数
	EXP_TASKDICE_TASK_COUNT = 8, // 任务发生器的任务个数
	EXP_ITEMDICE_ITEM_COUNT = 8, // 物品发生器的物品个数
	EXP_PSTONE_ADDON_GROUP_COUNT = 16, // 属性石的附加属性组个数
	EXP_PET_POWERPROB_ADJUST_PROB_COUNT = 12, // 宠物修为概率调整道具的成功几率个数
	EXP_BREEDCUB_COLIVE_CUB_COUNT = 4, // 养殖幼崽的共生动物个数
	EXP_RECIPE_REQ_BIND_COUNT = 3, // 配方生产时需要绑定的交通工具个数（多选一）
	EXP_RECIPE_MATERIAL_COUNT = 7, // 配方生产时需要的原料个数
	EXP_RECIPE_LUCKY_ITEM_COUNT = 4, // 配方生产时可用的幸运剂个数
	EXP_PRODUCE_SKILL_INIT_RECIPE_COUNT = 16, // 生产技能学会自动获得的配方个数
	EXP_DROPTABLE_DROP_COUNT = 128, // 等级掉落表的可掉落物品种数
	EXP_TASK_GRANT_REWARD_CONFIG_MAX = 8, // 任务奖励发放模板的奖励模板最大个数
	EXP_MONSTER_SPEC_DROP_COUNT = 4, // 怪物的特殊掉落个数
	EXP_MONSTER_AUTO_SKILL_COUNT = 4, // 怪物的普攻技能个数
	EXP_MONSTER_CAST_SKILL_COUNT = 8, // 怪物可施放技能个数
	EXP_MONSTER_SPEC_SKILL_COUNT = 4, // 怪物特殊施放技能个数
	EXP_MONSTER_TARGET_COUNT = 32, // 怪物可攻击目标个数
	EXP_MONSTER_DIE2TRANSBOX_COUNT = 8, // 怪物阵亡时生成传送盒子的个数
	EXP_MONSTER_DIE2BUFFBOX_COUNT = 8, // 怪物阵亡时生成状态盒子的个数
	EXP_MONSTER_PROP_FIXED_SPECIALTY_COUNT = 2, // 怪物随机属性中固定出现的特性个数
	EXP_MONSTER_PROP_RANDOM_SPECIALTY_COUNT = 16, // 怪物随机属性的备选特性个数
	EXP_MONSTER_PROP_MAX_RANDOM_SPECIALTY = 2, // 怪物随机属性的最多可能出现的随机特性个数
	EXP_LOOTTABLE_SOLO_ITEM_COUNT = 8, // Loot表单独掉落物品个数
	EXP_LOOTTABLE_SPECIAL_ITEM_COUNT = 16, // Loot表特殊掉落物品个数
	EXP_LOOTTABLE_NORMAL_ITEM_COUNT = 128, // Loot表普通掉落物品个数
	EXP_LOOTTABLE_MAX_SPECIAL_ITEM = 4, // Loot表最多可掉落的特殊物品个数
	EXP_LOOTTABLE_MAX_NORMAL_ITEM = 12, // Loot表最多可掉落的普通物品个数
	EXP_NPC_SELL_PAGE_GOODS_COUNT = 100, // NPC出售列表每页的商品个数
	EXP_NPC_SELL_PAGE_COUNT = 48, // NPC出售列表页数
	EXP_NPC_OUT_TASK_COUNT = 256, // NPC可发放的任务个数
	EXP_NPC_IN_TASK_COUNT = 256, // NPC可验证完成的任务个数
	EXP_NPC_MATTER_TASK_COUNT = 16, // NPC发放物品任务个数
	EXP_NPC_MATTER_TASK_MATTER_COUNT = 4, // NPC发放物品任务可发放的物品个数
	EXP_NPC_SKILL_COUNT = 512, // NPC可教授的技能个数
	EXP_NPC_SKILLSEQ_COUNT = 512, // NPC可教授的技能包个数
	EXP_NPC_TRANSMIT_TARGET_COUNT = 8, // NPC可传送目标个数
	EXP_NPC_WAR_TOWERBUILD_INFO_COUNT = 4, // NPC城战炮塔建造信息个数
	EXP_NPC_RESETPROP_PROP_COUNT = 6, // NPC可提供的洗点服务种类
	EXP_NPC_RECIPE_LEARN_RECIPE_COUNT = 64, // NPC处可学习的配方个数
	EXP_NPC_ESPECIAL_SELL_GOODS_COUNT = 32, // NPC可出售的特殊物品个数
	EXP_NPC_INSTANCE_CONFIG_COUNT = 16, // NPC副本服务的配置项个数
	EXP_NPC_ADJUST_OPEN_CTRL_COUNT = 8, // NPC可调整参数的开启控制器个数
	EXP_NPC_ADJUST_CLOSE_CTRL_COUNT = 8, // NPC可调整参数的关闭控制器个数
	EXP_NPC_ADJUST_SCENE_COUNT = 16, // NPC可调整参数的场景个数
	EXP_NPC_TASK_EVENT_COUNT = 256, // N任务触发事件服务的任务个数
	EXP_NPC_MIRROR_NPC_COUNT = 8, // NPC的镜像NPC个数
	EXP_MINE_OPEN_CTRL_COUNT = 4, // 矿物的采集完成开启控制器个数
	EXP_MINE_CLOSE_CTRL_COUNT = 4, // 矿物的采集完成关闭控制器个数
	EXP_MINE_RLT_COUNT = 4, // EXP_MINE_RLT_COUNT
	EXP_MINE_RLT_OPEN_CTRL_COUNT = 4, // 矿物的特殊采集完成开启控制器个数
	EXP_MINE_RLT_CLOSE_CTRL_COUNT = 4, // 矿物的特殊采集完成关闭控制器个数
	EXP_RUNEWORD_STONE_COUNT = 12, // 神符之语的宝石个数
	EXP_SKILLSEQ_SKILL_COUNT = 8, // 技能包包含的技能个数
	EXP_SKILLSEQ_RUNE_COUNT = 8, // 技能包包含的符文个数
	EXP_RUNE_MAX_LEVEL = 255, // 符文最大等级
	EXP_RUNE_SAVE_SLOT_MAX = 8, // 符文方案最大数量
	EXP_WUSHU_SKILLSEQ_COUNT = 32, // 武学包含套路个数
	EXP_WUSHU_SKILL_COUNT = 32, // 武学包含技能个数
	EXP_WUSHU_POWERFUL_SKILL_COUNT = 16, // 武学包含绝招个数
	EXP_SUBOBJ_MOVING_PARAM_COUNT = 16, // 子物体运动参数个数
	EXP_HARNESS_ADDON_GROUP_COUNT = 10, // 马具的附加属性组个数
	EXPHOMESTYLE_COUNT = 64, // 家园风格类型个数
	EXP_SKILLPROPGRP_LEVEL_COUNT = 5, // 技能属性组中技能等级个数
	EXP_SKILLPROPGRP_GEN_COUNT = 3, // 技能属性组中出现技能属性的最大个数
	EXP_WEDDING_INVITATION_NUM = 2, // 婚庆发放请帖个数
	EXP_INSTANCE_CONFIG_TASK_COUNT = 16, // 副本的关联任务个数
	EXP_WULINLEVEL_NUM_EACH = 10, // 武林等级各阶段等级数
	EXP_RANKAWARD_COUNT = 20, // 排名奖励表配置个数
	EXP_SKILLTOME_LEARN_SKILL_NUM = 4, // 技能书使用后学会技能个数
	EXP_LIFEPROF_LEVEL_MAX = 20, // 差事等级数
	EXP_REPU_LEVEL_MAX = 10, // 声望等级数
	EXP_PETBEDGE_BORN_SKILL_NUM = 5, // 宠物牌出生携带技能个数
	EXP_BUFFBOX_SKILL_NUM = 8, // 状态盒子对应技能个数
	EXP_NPCBUY_SERVICE_ITEM_NUM = 16, // 收购商品服务的物品个数12.9.4从8个改为了16个
	EXP_NPCBUY_SERVICE_PRIZE_NUM_EACH_ITEM = 4, // 收购商品服务每个物品的奖励个数
	EXP_ACTIVITY_CTRL_COUNT = 128, // 活动总配置控制器个数
	EXP_ACTIVITY_TASK_COUNT = 128, // 活动总配置任务个数
	EXP_ACTIVITY_SERVICE_COUNT = 32, // 活动总配置服务个数
	EXP_ACTIVITY_ITEM_COUNT = 128, // 活动总配置物品个数
	EXP_FACTIONBLD_ITEM_COUNT = 3, // 帮会建筑需要物品个数
	EXP_FACTION_LOGISTIC_ITEM_COUNT = 32, // 帮会后勤招工物品个数
	EXP_TREASUREMAP_PRIZE_MAX = 8, // 藏宝图奖励组最大个数
	EXP_TREASUREMAP_ORIGIN_POS_MAX = 64, // 未鉴定藏宝图坐标最大个数
	EXP_ALLIANCEWAR_KILLAREA_COUNT = 8, // 盟主战击杀区间个数
	EXP_TRANSFORM_MANUAL_SKILL_COUNT = 15, // 变身的手动技能个数
	EXP_TRANSFORM_PASSIVE_SKILL_COUNT = 8, // 变身的手动技能个数
	EXP_MON_ATTACH_REPU_COUNT = 4, // 怪物附带的声望数
	EXP_SCENE_SPECITEM_ADDON_COUNT = 4, // 场景专用物品的附加属性组个数
	EXP_SUTRASKILL_LEVEL_MAX = 20, // 心法技能最大等级
	EXP_SUTRASKILL_TYPE_COUNT = 4, // 心法技能类型个数
	EXP_SKILLSEQ_ATTRIB_COUNT = 3, // 技能包预留属性个数
	BATTLEFIELD_SIDES_COUNT = 4, // 战场势力个数
	QUIT_INST_CLEAR_REPU_COUNT = 4, // 退出副本/战场时清空声望的个数
	INSTANCE_CREATER_REPU_COUNT = 4, // 副本创建者声望的个数
	PLAYERDIY_ACTIVITY_CTRL_COUNT = 4, // 玩家开启活动控制器个数
	PLAYERDIY_ACTIVITY_TASK_COUNT = 4, // 玩家开启活动任务个数
	PLAYERDIY_ACTIVITY_SERVICE_COUNT = 4, // 玩家开启活动服务个数
	EXP_SELECT_ROLE_CFG_COUNT = 10, // 选人界面配置id个数
	EXP_SELECT_ROLE_TRANSFORM_SKILL_COUNT = 32, // 选人界面各阵营变身个数
	EXP_PLAYERDIY_ACT_SERVICE_CFG_COUNT = 8, // 玩家开启活动服务配置id个数
	EXP_PLAYERDIY_ACT_LEVEL_COUNT = 8, // 玩家开启活动等级个数
	EXP_ACTI_DROPTABLE_CFG_MONSTER_NUM = 100, // 活动掉落表怪物ID个数
	EXP_EQUIPLEVELUP_CONFIG_LEVELMAX = 20, // 装备升级模板最多等级
	EXP_ITEM_EQUIVALENCE_CONFIG_MAX = 64, // 物品等价表最大个数
	EXP_EQUIP_GEN_HOLE_MAX = 4, // 装备出生时最大孔数
	EXP_HOMEBLD_LEVEL_COUNT = 10, // 家园建筑等级个数
	CONSTRUCTION_HOME_LEVEL_COUNT = 10, // 家园建设度升级等级个数
	EXP_GET_SOULS_MAX = 4, // 最大出魂数
	EXP_BATTLEFIELD_SUBSCENE_COUNT = 32, // 战场最大子场景个数
	EXP_DART_CAR_COUNT = 8, // 押镖服务最大镖车数量
	EXP_MONSTER_MODE_NUM = 100, // 怪物模版模式数量
	EXP_MAX_KIDNAP_MONSTER_NUM = 128, // 劫持怪物ID数量
	EXP_MAX_GEN_HOSTAGE_NUM = 8, // 刷新肉票最大数量
	EXP_TRAP_GEN_MONSTER_NUM = 4, // 陷阱生成怪物数量
	EXP_POISON_GEN_MONSTER_NUM = 4, // 下毒生成怪物数量
	EXP_TREASUREITEM_MAX_LEVEL = 9, // 藏宝阁宝物等级
	EXP_TITLE_ITEM_MAX = 99, // 爵位拍卖竞价物品最大数量
	EXP_INSTANCE_PLATFORM_TYPE_NUM = 6, // 副本平台分类数
	EXP_INSTANCE_PLATFORM_TYPE_INSTANCE_NUM = 32, // 副本平台每类的副本数量
	EXP_DARTCARLEVEL_NUM = 5, // 运镖服务镖车等级数量
	EXP_DARTCARTNUM_BY_LEVEL = 16, // 运镖服务每级镖车的镖车ID数量
	EXP_PET_CHUANGONG_METHOD_NUM = 4, // 宠物传功4个途径
	EXP_PET_CHUANGONG_AIM_NUM = 20, // 宠物传功每个途径有20个目标
	EXP_NPC_SELL_SERV_NUM = 10, // NPC的sell_serviceNum;
	EXP_NPC_PRODUCESKILL_LVUP_SERV_NUM = 5, // NPC的生产技能升级服务
	EXP_NPC_LOTTERY_SERVICE_NUM = 32, // NPC的抽奖服务数量
	EXP_PARTY_CONTROLLER_NUM = 12, // 每组宴会配置中可以开启的控制器数量
	EXP_PARTY_LEVEL_NUM = 9, // 宴会等级
	EXP_PET_RANDOM_PROP_QUALITY_NUM = 10, // 宠物牌中，随机随到的宠物品质数量
	EXP_PET_INTELLI_NUM = 5, // 宠物
	EXP_SPECIALID_LOCKSKILL_NUM = 11, // 特殊ID表中，锁定技能的数量
	EXP_SPECIALID_BADGE_NUM = 4, // 特殊ID表中的徽章数量
	EXP_BADGE_LEVEL_NUM = 4, // 徽章等级数量
	EXP_PET_RANDOM_CHARACTOR_NUM = 4, // 宠物随机的性格数量
	EXP_FACTION_PROP_FACTION_LEVEL_NUM = 6, // 社团属性表中，社团等级数量
	EXP_FACTION_ACTIVITY_LEVEL_NUM = 7, // 社团活跃度等级上限
	EXP_FACTION_KICK_MEMBER_COST_REPU_STAGE = 3, // 社团踢人消耗声望挡位数量
	EXP_FACTION_PROP_MEMBER_LEVEL_NUM = 10, // 社团属性表中，成员阶数量
	EXP_FACTION_PROP_GYM_MAX_LEVEL = 6, // 社团属性表中，练功房最大等级
	EXP_FACTION_PROP_GYM_ADDON_GROUP_MAX = 5, // 社团属性表中，练功房附加属性组最大数量
	EXP_FACTION_PROP_GYM_ADDON_GROUP_MAX_LEVEL = 5, // 社团属性表中，练功房附加属性组最大等级
	EXP_FACTION_PROP_SHOP_MAX_LEVEL = 6, // 社团属性表中，商店最大等级
	EXP_FACTION_PROP_COFFER_MAX_LEVEL = 6, // 社团属性表中，金库最大等级
	EXP_FACITON_PROP_VITALITY_MAX_LEVEL = 5, // 社团属性表中，活跃度最大等级
	EXP_GATHER_INFOMATION_OPTIONS_NUM = 8, // 打探消息的答案数量
	EXP_INSTANCE_MODE_NUM = 100, // 副本模式数量(与EXP_MONSTER_MODE_NUM值相同)
	EXP_PRODUCE_SKILL_MAX_LEVEL = 255, // 生产技能最高等级
	EXP_RECIPE_MATERIAL_COUNT_MAX = 4, // 配方生产原料种类
	EXP_RECIPE_OUTPUT_NORMAL_COUNT_MAX = 20, // 配方普通产出物种类
	EXP_RECIPE_OUTPUT_LUCKY_COUNT_MAX = 6, // 配方幸运产出物种类
	EXP_APTITUDE_LEVEL_MAX = 9, // 资质第n重
	EXP_XINFA_LEVELUP_NUM = 255, // 心法升级的最高等级
	EXP_MARRAIGE_ITEM_FAMOUSE_NUM = 10, // 结婚物品的明星配置数量
	EXP_TIGUAN_TARGET_NUM = 3, // 踢馆的目标NPC对象数量
	EXP_SAME_FAMILY_MEMBER_IN_TEAM_MAX = 6, // 队伍中同结义的人员上限
	EXP_MAX_ROOM_FOR_TOURNAMENT = 10, // 竞技赛最大房间数
	EXP_MAX_MATCH_STAGES_COUNT = 5, // 竞技赛比赛阶段数量
	EXP_FACTION_ADV_PROP_ITEM_MAX = 200, // EXP_FACTION_ADV_PROP_ITEM_MAX
	EXP_TIGUAN_FORBID_CITY_MAX = 4, // EXP_TIGUAN_FORBID_CITY_MAX
	EXP_GRAFFITI_ITEM_MAX = 5, // 涂鸦道具数量上限
	EXP_GRAFFITI_PEN_BRUSH_PAIR_MAX = 10, // 笔和刷子对应关系
	EXP_PROF_DEFAULT_ACTIVE_SKILL_NUM = 32, // 该职业的默认激活技能总数
	EXP_PARADING_SERVICE_CONFIG_NUM = 3, // 游街服务最多配置数目
	EXP_INTERSERVER_COMBO_KILL_CFG_NUM = 16, // 跨服玩家连杀数目的配置数量
	EXP_FLIGHT_FLAG_TRANS_POS_NUM = 10, // 飞行旗的坐标数目
	EXP_DETECTION_ENDING_ITEM_NUM = 10, // 线索物品数量
	EXP_DETECTION_NOT_ENDING_ITEM_NUM = 5, // 非结局线索物品数量
	EXP_SPECIALID_FLIGHT_FLAG_MAX = 10, // 特殊ID表，飞行旗配置数量
	EXP_SPECIALID_RESET_PROP_POINT_ITEM_NUM = 6, // 特殊ID表，洗点配置道具数量
	EXP_GATHERFOOD_TASK_OUT_COUNT = 8, // 收粮任务发放服务，最多发放的任务数量
	EXP_MAP_MONSTER_GEN_POS_MAX_COUNT = 12, // 刷怪配置表中，刷怪位置数量
	EXP_ADDON_EQUIPMENT_REINFORCE_COUNT = 4, // 强化附加属性类型个数
	EXP_SCENE_LEVEL = 6, // 场景级别
	EXP_CELEBRITY_COUPON_ODDS_COUNT = 8, // 名人兑换几率数量
	EXP_CELEBRITY_SKILL_COUNT = 8, // 名人技能数量
	EXP_CELEBRITY_UNIQUE_SKILL_COUNT = 4, // 名人绝招数量
	EXP_ARENA_RANK_MONSTER_COUNT = 10, // 玩家竞技场排名数
	EXP_ELIXIR_COMPOUND_MAX = 10, // 丹药合成层数
	EXP_ELIXIR_COMPOUND_NUM = 5, // 丹药合成需要物品数
	EXP_CYXB_INSTANCE_NUM_MAX = 16, // 残页寻宝活动的副本个数
	EXP_FRIEND_SUMMON_PLAYER_LEVEL_RANGE = 26, // 玩家(1～10).(11～20)...(191～200)级时每日最多可召唤好友次数
	EXP_CELEBRITY_INTIMACY_LEVEL_MAX = 20, // 名人好感度级别
	EXP_YUANBAO_CHARGE_LEVEL_MAX = 10, // 元宝冲值等级
	EXP_FIXED_DISASSEMBLE_ITEM_MAX = 2, // 单独拆出物品
	EXP_NORMAL_DISASSEMBLE_ITEM_MAX = 4, // 普通拆出物品
	EXP_ITEM_COMPOSE_NEEDED_ITEM_MAX = 8, // 物品合成
	EXP_RETINUE_QUALITY_MAX = 4, // 随从品质
	EXP_RETINUE_FORMATION_POS_COUNT_MAX = 5, // 随从阵法中随从位置个数
	EXP_RETINUE_FORMATION_HIDE_COMBINE_COUNT_MAX = 2, // 隐藏随从阵法组合个数
	EXP_RETINUE_FORMATION_CONFIG_COUNT_MAX = 50, // 阵法配置个数
	EXP_EQUIPMENT_QUALITY_MAX = 8, // 装备品阶(同物品品质)
	EXP_EQUIP_LIANXING_LEVEL_MAX = 48, // 炼星等级
	EXP_EQUIP_REFRESH_LOCK_PROP_MAX = 3, // 装备洗炼锁定属性数量
	EXP_EQUIP_REFRESH_ACTIVATE_ADDON_MAX = 5, // 装备开启附加属性属性数量
	EXP_NATION_WAR_ATTACK_OCCUPY_REVIVE_POINT_MAX = 4, // 国战攻方可占领复活点数量
	EXP_NATION_WAR_WINNER_OFFICER_EXP_BONUS_MAX = 3, // 国战胜方官员经验奖励
	EXP_TALENT_GROUP_TALENT_MAX = 16, // TALENT_GROUP_CONFIG最大天赋数量
	EXP_TALENT_GROUP_STAGE_MAX = 8, // TALENT_GROUP_CONFIG最大天赋阶段
	EXP_TALENT_LEVEL_MAX = 8, // TALENT_CONFIG最大天赋等级
	EXP_FRIEND_BLESSING_LOTTERY_SERVICE_MAX = 3, // 好友祝福抽奖
	EXP_CARD_COMBO_CARDS_MAX = 5, // 卡牌组合中的卡牌数量
	EXP_CARD_COMBO_UNLOCK_NEED_COMBO_MAX = 5, // 卡牌组合中的前置卡牌组合数量
	EXP_CARD_COMBO_GROUP_COMBO_MAX = 200, // 卡牌组合分组中的组合数量
	EXP_BLACK_MARKET_SHOP_AUTO_REFRESH_TIME_MAX = 8, // 黑市自动刷新时间数量
	EXP_BLACK_MARKET_SHOP_LIBRARY_MAX = 8, // 黑市商品库数量
	EXP_BLACK_MARKET_SHOP_LIBRARY_ITEM_MAX = 500, // 黑市商品库商品数量
	EXP_INSIDE_EQUIP_HOLE_MAX = 6, // 内装孔数上限
	EXP_INSIDE_EQUIP_GRADE_MAX = 255, // 内装等级上限
	EXP_EQUIP_ADDON_GROUP_COUNT_MAX = 8, // 装备附加属性组最大个数
	EXP_EQUIP_UNLOCK_FASHION_MAX = 4, // 装备解锁时装图鉴上限
	EXP_EQUIP_SKILL_GRADE_COUNT_MAX = 20, // 装备附加技能属性组个数
	EXP_INSIDE_EQUIP_GRADEUP_COST_ITME_MAX = 3, // 内装升级消耗物品种类
	EXP_JUEWEI_SKILL_MAX = 3, // 爵位技能数量
	EXP_JUEWEI_MAX = 24, // 爵位最大数量
	EXP_INSTANCE_RANDAM_LEVEL_MAX = 128, // 副本随机难度数量
	EXP_MINIGAME_RESULT_GRADE_MAX = 8, // 小游戏结果种类
	EXP_EQUIP_ADDON_MAX = 8, // 装备附加属性最大数目
	EXP_CELEBRITY_ELIXIR_EXP_RANDOM_MAX = 8, // 名人功力丹随机经验最大数目
	EXP_CHARIOT_LEVEL_MAX = 50, // 战车等级上限
	EXP_CHARIOT_CAMP_LEVEL_MAX = 5, // 战车营等级上限
	EXP_WUHUN_PAGE_MAX = 3, // 武魂页数
	EXP_WUHUN_PAGE_NODE_MAX = 5, // 武魂每页节点数
	EXP_WUHUN_NODE_LEVEL_LIMIT = 20, // 武魂节点等级上限
	EXP_PRACTICE_SKILL_LEVEL_MAX = 255, // 修炼技能等级上限
	MAX_GUILD_BUILDING_LEVEL = 6, // 修炼帮派练功房金币折扣之练功房总等级
	EXP_PRACTICE_PAGE_SKILL_COUNT = 10, // 修炼页可修炼技能个数
	EXP_INPLACE_REVIVE_CONFIG_COUNT = 10, // 原地复活次数
	EXP_FORTUNE_CAT_COST_CONFIG_COUNT = 20, // 招财猫模板档位
	EXP_MAX_WING_PAINT_PART_COUNT = 2, // 翅膀染色部分最大个数
	EXP_UNLOCK_COLORS_ADDON_CFG_COUNT = 5, // 开启颜色解锁属性组个数
	EXP_PAINT_COLOR_CFG_NUM = 32, // 染色颜色数量
	EXP_SHARK_DEVICE_TASK_COUNT = 5, // 摇一摇任务个数
	EXP_ITEM_USE_SCENE_ID_COUNT = 10, // 物品可使用场景ID个数
	EXP_PET_LOW_INNERGEM_MAX_COUNT = 8, // 宠物低级内丹最大个数
	EXP_HOME_MAX_LEVEL = 8, // 家园最大等级
	EXP_WING_INVOKE_MAX_COUNT = 5, // 坐骑培养(进化)品质配置上限
	EXP_WING_INVOKE_PART_COUNT = 6, // 坐骑培养(进化)部位数量
	EXP_WING_CONTAIN_MAX_COUNT = 5, // 坐骑多人骑乘时最大容纳人数
	EXP_WING_POS_MAX_COUNT = 5, // 坐骑位置配置上限
	EXP_WING_PAINT_MAX_COUNT = 4, // 坐骑染色区数量配置上限
	EXP_WING_FASHION_MAX_COUNT = 4, // 坐骑改装区数量配置上限
	EXP_WING_PART_VIEW_MAX_COUNT = 4, // 坐骑单一部位观察点数量配置上限
	EXP_WING_TEXTURE_MAX_COUNT = 5, // 坐骑特殊材质数量配置上限
	EXP_WING_FASHION_EACH_POS_MAX_COUNT = 4, // 坐骑改装区单一位置数量配置上限
	EXP_WING_PAINT_PART_MATERIAL_MAX_COUNT = 4, // 坐骑染色区最大材质数量
	EXP_WING_FLY_SKILL_MAX = 20, // 飞行技能最大个数
	EXP_WING_GRADE_MAX = 121, // 翅膀进阶最大阶数
	EXP_WING_UPGRADE_PROFS_COUNT = 17, // 翅膀单次进化属性的个数
	EXP_PET_LOCK_SKILL_MAX_COUNT = 4, // 宠物锁定技能个数上限
	EXP_WORLD_CHNL_SPEAK_COST_CFG_COUNT = 3, // 世界频道消耗配置个数
	EXP_BABY_MAX_LEVEL = 255, // 孩子最高等级
	EXP_BABY_STONE_MAX_LEVEL = 30, // 孩子天命最高等级
	EXP_BABY_STAGE_COUNT = 4, // 孩子时期个数
	EXP_BABY_TALENT_TYPE_COUNT = 6, // 孩子根骨类型个数
	EXP_GLOBAL_RED_POCKEDT_CONFIG_NUM = 8, // 全服红包配置项
	EXP_EQUIP_BASIC_ADDON_MAX = 5, // 装备basic附加属性最大数目
	EXP_EQUIP_ADDITIONAL_ADDON_MAX = 6, // 装备additional附加属性最大数目
	EXP_EQUIP_STONE_MAX = 4, // 装备宝石最大数目
	EQUIP_MULTI_REFRESH_COUNT = 3, // 多次洗练次数限制
	COMBINE_ITEM_NEED_COUNT = 3, // 合成物品所需材料数量
	EXP_SCENEINTERACTION_POS_NUM = 5, // 场景交互模板位置个数
	EXP_ENHANCE_LEVEL_COUNT = 96, // 装备强化最高等级
	RETINUE_STATS_COUNT = 5, // 伙伴能力值个数
	RETINUE_PRIVATE_NORMAL_COUNT = 20, // 伙伴普通私有物上限
	RETINUE_PRIVATE_RARE_COUNT = 10, // 伙伴稀有私有物上限
	RETINUE_PRIVATE_SPECIAL_COUNT = 5, // 伙伴专属私有物上限
	RETINUE_PRIVATE_ADDITION_COUNT = 3, // 伙伴私有物属性加成个数
	RETINUE_FASHION_COUNT_MAX = 10, // 伙伴时装最大个数
	RETINUE_FASHION_COLOR_COUNT_MAX = 10, // 伙伴时装染色最大个数
	RETINUE_FASHION_COLOR_ADDITION_COUNT = 2, // 伙伴时装染色属性加成个数
	RETINUE_RELATION_MAX = 10, // 伙伴羁绊对象上限
	RETINUE_DECOMPOSE_COUNT = 3, // 伙伴拆分奖励个数
	EXP_VALID_DEFAULT_SURFACE_COUNT = 2, // 默认座驾幻化数量
	EXP_INTERACTION_OP_GROUP_MAX_NUM = 7, // 交互最大操作组长度
	EXP_CAREER_ACTIVATE_MAX_NUM = 3, // 身份激活等级配置最大个数
	RETINUE_CHAT_MAX_COUNT = 60, // 伙伴聊天信息最大个数
	RETINUE_MEMOIR_MAX_COUNT = 3, // 伙伴传记最大个数
	RETINUE_STATUS_PICTURE_COUNT = 4, // 伙伴状态最大附加图片个数
	RETINUE_STATUS_MAX_FAVOR = 20, // 伙伴状态最大伙伴点赞个数
	RETINUE_INITCOMMENT_GROUP_COUNT = 10, // 伙伴状态初始评论分组最大个数
	RETINUE_COMMENT_GROUP_COUNT = 3, // 伙伴状态评论分组最大个数
	RETINUE_COMMENT_GROUP_ELE_COUNT = 2, // 伙伴状态评论分组里面的评论最大个数
	RETINUE_STATUS_COMMENT_COUNT = 50, // 伙伴状态评论最大个数
	EXP_GUARD_MAX_LEVEL = 100, // 守护灵等级上限
	RETINUE_MEMOIR_MAX_PAGE_COUNT = 8, // 伙伴传记最大页数
	GUARD_STAR_SLOT_COUNT = 6, // 守护灵星阵位点个数
	OVERCOOK_ITEM_MATERIAL_TYPE_MAX = 4, // 分手厨房材料原料类型数量
	DRAGONBORN_EVOLUTION_LIMIT = 30, // 龙裔进化次数上限
	DRAGONBORN_BREAK_LIMIT = 12, // 龙裔突破次数上限
	DRAGONBORN_PASSIVE_SKILL_POOL_LIMIT = 20, // 龙裔被动技能随机池子上限
	LOTTERY_GOD_LEVEL_UPLIMIT = 200, // 神迹探索总层数
	LOTTERY_GOD_DEFAULT_REWARD_NUM = 21, // 神迹探索每层默认自选奖励数量
	HONEY_GARDEN_FOLOWER_TOTAL_STAGE = 4, // 甜蜜花园植物阶段总数
	THUNDERSTRIKE_NEED_CLEAR_LEVEL	= 100,// 天谴计划需要每周清声望的等级
	SIM_RESTAURANT_PRODUCER_LEVEL = 6, // 模拟餐厅生产者等级上限
	HUANCAI_UPGRADE_PROP_NUM = 5, // 焕彩升级相关属性数量上限
	HUANCAI_UPGRADE_LEVEL_NUM = 5, // 焕彩升级最大等级上限
	PLAYER_LEVELEXP_UPLIMIT = 255, // 玩家等级上限

};

enum IVTRTYPE_ENUM {
	IVTRTYPE_INVALID = -1, // IVTRTYPE_INVALID
	IVTRTYPE_EQUIPPACK = 0, // Equipment
	IVTRTYPE_PACK = 1, // Normalpack
	IVTRTYPE_NORMALPACK_END = 2, // 普通包裹栏结束
	IVTRTYPE_TASKITEM = 2, // TaskItempack
	IVTRTYPE_MATERIAL = 3, // Materialpack
	IVTRTYPE_TAKEWITH_END = 4, // 随身包裹结束
	IVTRTYPE_TRASHBOX = 4, // Trashbox个人仓库
	IVTRTYPE_FACTIONSTORE = 5, // 帮派包裹
	IVTRTYPE_RECYCLE = 6, // Recyclepack
	IVTRTYPE_TEMPBACK = 7, // 临时包裹，过关奖励
	IVTRTYPE_HERO_EQUIP0 = 8, // 名人装备栏
	IVTRTYPE_HERO_EQUIP1 = 9, // IVTRTYPE_HERO_EQUIP1
	IVTRTYPE_HERO_EQUIP2 = 10, // IVTRTYPE_HERO_EQUIP2
	IVTRTYPE_HERO_EQUIP3 = 11, // IVTRTYPE_HERO_EQUIP3
	IVTRTYPE_HERO_EQUIP4 = 12, // IVTRTYPE_HERO_EQUIP4
	IVTRTYPE_HERO_EQUIP5 = 13, // IVTRTYPE_HERO_EQUIP5
	IVTRTYPE_HERO_EQUIP6 = 14, // IVTRTYPE_HERO_EQUIP6
	IVTRTYPE_HERO_EQUIP7 = 15, // IVTRTYPE_HERO_EQUIP7
	IVTRTYPE_COUNT = 16, // 

};

enum MP_TYPE {
	MPT_MP = 0, // 内力槽
	MPT_RAGE = 1, // 怒气槽
	MPT_DEFENSE = 2, // 防御槽
	MPT_STRENGTH = 3, // 体力槽
	MPT_COUNT = 8, // 

};

enum VIGOR_TYPE {
	VIGOR_PRODUCE = 0, // 生产点
	VIGOR_SOCIAL = 1, // 社交点
	VIGOR_ACTIVITY = 2, // 活动点
	VIGOR_COUNT = 3, // VIGOR_COUNT
	VIGOR_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum GEN_TYPE {
	GT_NONE_COMBAT = 0, // 非战斗状态回复速度
	GT_COMBAT = 1, // 战斗状态回复速度
	GT_SIT = 2, // 打坐状态回复速度
	GT_COUNT = 3, // 

};

enum ABILITY_TYPE {
	ABT_PHY = 0, // 外功
	ABT_MAG = 1, // 内功
	ABT_COUNT = 2, // 

};

enum POTENTIAL_TYPE {
	PT_STRENGTH = 0, // 力量
	PT_AGILITY = 1, // 敏捷
	PT_COUNT = 8, // 

};

enum ATTR_TYPE {
	ATT_ICE = 0, // 冰
	ATT_FIRE = 1, // 火
	ATT_POISON = 2, // 毒
	ATT_EARCH = 3, // 土
	ATT_COUNT = 8, // 

};

enum DEBUFF_TYPE {
	DT_COUNT = 8, // DT_COUNT

};

enum FEATURE_TYPE {
	FEATURE_TYPE_COUNT = 6, // FEATURE_TYPE_COUNT

};

enum JUDGEMENT_TYPE {
	JT_COUNT = 16, // JT_COUNT

};

enum ABILITY_PROP_TYPE {
	APT_ATTACK_LOW = 0, // 攻击下限
	APT_ATTACK_HIGH = 1, // 攻击上限
	APT_ATTACK_POWER = 2, // 攻击强度
	APT_DEFENSE = 3, // 防御
	APT_RESIST = 4, // 抗性
	APT_HIT = 5, // 命中
	APT_EVADE = 6, // 闪躲
	APT_CRIT_RATE = 7, // 暴击值
	APT_CRIT_DAMAGE_SCALE = 8, // 暴击发生的话产生额外伤害(比率)
	APT_CRIT_DAMAGE_POINT = 9, // 暴击发生的话产生额外伤害(点数)
	APT_CRIT_RESIST = 10, // 暴击抗性
	APT_DAMAGE_REDUCE = 11, // 伤害点数减免修正
	APT_BLOCK = 12, // 格挡值
	ABILITY_NUM = 13, // 

};

enum GENDER_ENUM {
	GENDER_MALE = 0, // 男
	GENDER_FEMALE = 1, // 女
	GENDER_BOTH = 2, // 男女不限
	GENDER_COUNT = 2, // 
	GENDER_FORCE_INT = 0x7fffffff, // 

};

enum GENDER_LIMIT {
	GENDER_LIMIT_NONE = 0, // 男女均不限制
	GENDER_LIMIT_MALE = 1, // 男
	GENDER_LIMINT_FEMALE = 2, // 女
	GENDER_LIMIT_FORCE_INT = 0x7fffffff, // 

};

enum RACE_ENUM {
	RACE_MAN = 0, // RACE_MAN
	RACE_SPIRIT = 1, // RACE_SPIRIT
	RACE_FORCE_INT = 0x7fffffff, // 

};

enum PROF_TYPE_ENUM {
	PROFTYPE_STANDARD = 0, // 0标准
	PROFTYPE_BLADE = 1, // 刀天罡
	PROFTYPE_SPEAR = 2, // 枪夜叉
	PROFTYPE_SWORD = 3, // 剑琼华
	PROFTYPE_RING = 4, // 环巫月
	PROFTYPE_UNBRELLA = 5, // 伞霓裳
	PROFTYPE_BOW = 6, // 弓
	PROFTYPE_YULING = 7, // 御灵
	PROFTYPE_TIANSHA = 8, // 天煞
	PROFTYPE_PLAYER_9 = 9, // 逆潮
	PLAYER_PROF_COUNT = 19, // 
	PROFTYPE_10 = 10, // 魔术师
	PROFTYPE_11 = 11, // PROFTYPE_11
	PROFTYPE_12 = 12, // PROFTYPE_12
	PROFTYPE_13 = 13, // PROFTYPE_13
	PROFTYPE_14 = 14, // PROFTYPE_14
	PROFTYPE_15 = 15, // PROFTYPE_15
	PROFTYPE_16 = 16, // PROFTYPE_16
	PROFTYPE_17 = 17, // PROFTYPE_17
	PROFTYPE_18 = 18, // PROFTYPE_18
	PROFTYPE_19 = 19, // PROFTYPE_19
	PROFTYPE_CHILD = 20, // 孩子职业
	PROFTYPE_BIGWORLD_ELITE = 21, // 大地图精英怪
	PROFTYPE_BIGWORLD_BOSS = 22, // 大地图BOSS
	PROFTYPE_INSTANCE_NORMAL = 23, // 副本普通怪
	PROFTYPE_INSTANCE_ELITE = 24, // 副本精英怪
	PROFTYPE_INSTANCE_BOSS = 25, // 副本BOSS
	PROFTYPE_WORLD_BOSS = 26, // 世界BOSS
	PROFTYPE_MAGIC_DEF_ELITE = 27, // 法防精英怪
	PROFTYPE_PHYSIC_DEF_ELITE = 28, // 物防精英怪
	PROFTYPE_DOUBLE_DEF_ELITE = 29, // 双防精英怪
	PROFTYPE_COUNT = 30, // 

};

enum ROLE_PHYSIC_ENUM {
	ROLE_PHYSIC_INVALID = -1, // ROLE_PHYSIC_INVALID
	ROLE_PHYSIC_NORMAL = 0, // 标准
	ROLE_PHYSIC_TINY = 1, // 小
	ROLE_PHYSIC_COUNT = 2, // 

};

enum EXP_ATTACK_TYPE {
	EXPATKTYPE_NEWBIE = 0, // 0空手
	EXPATKTYPE_BLADE = 1, // 刀
	EXPATKTYPE_2 = 2, // 攻击类型2
	EXPATKTYPE_3 = 3, // 攻击类型3
	EXPATKTYPE_4 = 4, // 攻击类型4
	EXPATKTYPE_5 = 5, // 攻击类型5
	EXPATKTYPE_6 = 6, // 攻击类型6
	EXPATKTYPE_7 = 7, // 攻击类型7
	EXPATKTYPE_8 = 8, // 攻击类型8
	EXPATKTYPE_9 = 9, // 攻击类型9
	EXPATKTYPE_10 = 10, // 攻击类型10
	EXPATKTYPE_MONSTER = 11, // 怪物
	EXPATKTYPE_METAL_SHARP = 12, // 金属锐器
	EXPATKTYPE_METAL_BLUNT = 13, // 金属钝器
	EXPATKTYPE_METAL_KEEN = 14, // 金属利器
	EXPATKTYPE_METAL_SOFT = 15, // 金属软体
	EXPATKTYPE_WOOD_SHARP = 16, // 木质锐器
	EXPATKTYPE_WOOD_BLUNT = 17, // 木质钝器
	EXPATKTYPE_WOOD_KEEN = 18, // 木质利器
	EXPATKTYPE_WOOD_SOFT = 19, // 木质软体
	EXPATKTYPE_STONE_SHARP = 20, // 石制锐器
	EXPATKTYPE_STONE_BLUNT = 21, // 石制钝器
	EXPATKTYPE_STONE_KEEN = 22, // 石制利器
	EXPATKTYPE_STONE_SOFT = 23, // 石制软体
	EXPATKTYPE_FLESH_SHARP = 24, // 肉身锐器
	EXPATKTYPE_FLESH_BLUNT = 25, // 肉身钝器
	EXPATKTYPE_FLESH_KEEN = 26, // 肉身利器
	EXPATKTYPE_FLESH_SOFT = 27, // 肉身软体
	EXPATKTYPE_UNUSED_3 = 28, // 待定3
	EXPATKTYPE_UNUSED_2 = 29, // 待定2
	EXPATKTYPE_UNUSED_1 = 30, // 待定1
	EXPATKTYPE_UNUSED_0 = 31, // 待定0
	EXPATKTYPE_11 = 32, // 攻击类型11
	EXPATKTYPE_12 = 33, // 攻击类型12
	EXPATKTYPE_13 = 34, // 攻击类型13
	EXPATKTYPE_14 = 35, // 攻击类型14
	EXPATKTYPE_15 = 36, // 攻击类型15
	EXPATKTYPE_16 = 37, // 攻击类型16
	EXPATKTYPE_17 = 38, // 攻击类型17
	EXPATKTYPE_18 = 39, // 攻击类型18
	EXPATKTYPE_19 = 40, // 攻击类型19
	EXPATKTYPE_20 = 41, // 攻击类型20
	EXPATKTYPE_COUNT = 42, // 
	EXPATKTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_DEFENCE_TYPE {
	EXPDEFTYPE_FLESH = 0, // 0肉体
	EXPDEFTYPE_CLOTH = 1, // 布衣
	EXPDEFTYPE_LEATHER = 2, // 皮甲
	EXPDEFTYPE_COTTON = 3, // 棉甲
	EXPDEFTYPE_FUR = 4, // 毛皮
	EXPDEFTYPE_SCALE = 5, // 5鳞片
	EXPDEFTYPE_FETHER = 6, // 羽毛
	EXPDEFTYPE_IRON = 7, // 铁
	EXPDEFTYPE_COPPER = 8, // 铜
	EXPDEFTYPE_STONE = 9, // 石头
	EXPDEFTYPE_WOOD = 10, // 10木头
	EXPDEFTYPE_STRAW = 11, // 稻草
	EXPDEFTYPE_GHOST = 12, // 幽灵
	EXPDEFTYPE_UNUSED_18 = 13, // 待定18
	EXPDEFTYPE_UNUSED_17 = 14, // 待定17
	EXPDEFTYPE_UNUSED_16 = 15, // 15待定16
	EXPDEFTYPE_UNUSED_15 = 16, // 待定15
	EXPDEFTYPE_UNUSED_14 = 17, // 待定14
	EXPDEFTYPE_UNUSED_13 = 18, // 待定13
	EXPDEFTYPE_UNUSED_12 = 19, // 待定12
	EXPDEFTYPE_UNUSED_11 = 20, // 20待定11
	EXPDEFTYPE_UNUSED_10 = 21, // 待定10
	EXPDEFTYPE_UNUSED_9 = 22, // 待定9
	EXPDEFTYPE_UNUSED_8 = 23, // 待定8
	EXPDEFTYPE_UNUSED_7 = 24, // 待定7
	EXPDEFTYPE_UNUSED_6 = 25, // 25待定6
	EXPDEFTYPE_UNUSED_5 = 26, // 待定5
	EXPDEFTYPE_UNUSED_4 = 27, // 待定4
	EXPDEFTYPE_UNUSED_3 = 28, // 待定3
	EXPDEFTYPE_UNUSED_2 = 29, // 待定2
	EXPDEFTYPE_UNUSED_1 = 30, // 30待定1
	EXPDEFTYPE_UNUSED_0 = 31, // 待定0
	EXPDEFTYPE_COUNT = 32, // 
	EXPDEFTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ROLEINWAR_ENUM {
	EXPROLEINWAR_NONE = 0, // 0无
	EXPROLEINWAR_CENTERBUILDING = 1, // 中心建筑
	EXPROLEINWAR_GUN_TURRET = 2, // 炮塔
	EXPROLEINWAR_BOW_TURRET = 3, // 箭塔
	EXPROLEINWAR_CATAPULT = 4, // 投石车
	EXPROLEINWAR_TRANSFER_POINT = 5, // 5传送点
	EXPROLEINWAR_REVIVE_POINT = 6, // 复活点
	EXPROLEINWAR_SERVER_NPC = 7, // 服务NPC
	EXPROLEINWAR_OCCUPY_FLAG = 8, // 占领标志物
	EXPROLEINWAR_ENTER_POINT = 9, // 进入点
	EXPROLEINWAR_LANDMARK_BUILDING = 10, // 10标志性建筑
	EXPROLEINWAR_NORMAL_BUILDING = 11, // 普通建筑
	EXPROLEINWAR_STRATAGEM_MONSTER = 12, // 机关兽
	EXPROLEINWAR_SHOUSHA = 13, // 冲车
	EXPROLEINWAR_CLEARBOARD_OBJ = 14, // 清版对象
	EXPROLEINWAR_VS_SOLDIER = 15, // 15(名人对战小兵)
	EXPROLEINWAR_VS_DEFENCE_TOWER = 16, // 16(名人对战防御塔)
	EXPROLEINWAR_COUNT = 17, // 
	EXPROLEINWAR_FORCE_INT = 0x7fffffff, // 

};

enum CANBEATTACK_ENUM {
	CANBEATTACK_NO = 0, // 不可被攻击
	CANBEATTACK_1 = 1, // 可被攻击攻击变粉名
	CANBEATTACK_2 = 2, // 可被攻击攻击变红名
	CANBEATTACK_COUNT = 3, // CANBEATTACK_COUNT
	CANBEATTACK_FORCE_INT = 0x7fffffff, // 

};

enum MONSTER_PROP_TYPE {
	MONPROPTYPE_HUMANSHAPE = 0, // 人形
	MONPROPTYPE_BEAST = 1, // 野兽
	MONPROPTYPE_GEAR = 2, // 机关
	MONPROPTYPE_GHOST = 3, // 鬼怪
	MONPROPTYPE_PLANT = 4, // 植物
	MONPROPTYPE_SPIRITBEAST = 5, // 神兽
	MONPROPTYPE_COUNT = 6, // 

};

enum MONSTER_ATTACK_STRATEGY {
	MONATKSTRATEGY_FIGHT_RANGE = 0, // 肉搏＋远程类怪物
	MONATKSTRATEGY_FORTRESS = 1, // 堡垒类
	MONATKSTRATEGY_SCENE_CREATURE = 2, // 场景活物类
	MONATKSTRATEGY_STUB = 3, // 树桩类
	MONATKSTRATEGY_ARROWTOWER = 4, // 不转向箭塔类
	MONATKSTRATEGY_ESCORT_MACHINE = 5, // 镖车
	MONATKSTRATEGY_CATAPULT = 6, // 投石车
	MONATKSTRATEGY_TACTIC = 7, // 战术怪
	MONATKSTRATEGE_BATTLE_MOVEABLE = 8, // 战场可移动
	MONATKSTRATEGE_BATTLE_UN_MOVEABLE = 9, // 战场不可移动
	MONATKSTRATEGE_PERIODIC_ATTACK = 10, // 周期攻击怪
	MONATKSTRATEGY_COUNT = 11, // 

};

enum MONSTER_AGGRESIVE_MODE {
	MONAGGRE_PASSIVE = 0, // 被动
	MONAGGRE_INITIATIVE = 1, // 主动
	MONAGGRE_NOFIGHTBACK_PASSIVE = 2, // 不反击且被动
	MONAGGRE_NOFIGHTBACK_INITIATIVE = 3, // 不反击且主动
	MONAGGRE_COUNT = 4, // 
	MONAGGRE_FORCE_INT = 0x7fffffff, // 

};

enum MONSTER_AGGRO_TYPE {
	MONSTERAGGRO_FIRST_DAMAGE = 0, // 首次伤害
	MONSTERAGGRO_LIST = 1, // 仇恨列表
	MONSTERAGGRO_FINAL_DAMAGE = 2, // 致死一击
	MONSTERAGGRO_TASK_BOSS = 3, // 任务BOSS
	MONSTERAGGRO_INSTANCE = 4, // 副本怪物
	MONSTERAGGRO_MAX_DAMAGE = 5, // 最高伤害
	MONSTERAGGRO_OWNER = 6, // 怪物的召出者
	MONSTERAGGRO_LIST_TEAM = 7, // 仇恨列表队伍
	MONSTERAGGRO_COUNT = 8, // 
	MONSTERAGGRO_FORCE_INT = 0x7fffffff, // 

};

enum MONSTER_CASTSKILL_CONDITION {
	MONCASTSKILL_CONDITION_INVALID = 0, // 0_不生效
	MONCASTSKILL_CONDITION_INBABBLE = 1, // 1_进入战斗释放
	MONCASTSKILL_CONDITION_HPBELOW_100 = 2, // 2_自身HP低于100%时释放（次）
	MONCASTSKILL_CONDITION_HPBELOW_75 = 3, // 3_自身HP低于75%时释放（次）
	MONCASTSKILL_CONDITION_HPBELOW_50 = 4, // 4_自身HP低于50%时释放（次）
	MONCASTSKILL_CONDITION_HPBELOW_30 = 5, // 5_自身HP低于30%时释放（次）
	MONCASTSKILL_CONDITION_HPBELOW_25 = 6, // 6_自身HP低于25%时释放（次）
	MONCASTSKILL_CONDITION_HPBELOW_20 = 7, // 7_自身HP低于20%时释放（次）
	MONCASTSKILL_CONDITION_HPBELOW_10 = 8, // 8_自身HP低于10%时释放（次）
	MONCASTSKILL_CONDITION_9 = 9, // 9_时间间隔限次打断不重置（秒）
	MONCASTSKILL_CONDITION_10 = 10, // 10_时间间隔限次打断重置（秒）
	MONCASTSKILL_CONDITION_11 = 11, // 11_时间间隔不限次打断重置（秒）
	MONCASTSKILL_CONDITION_12 = 12, // 12_时间间隔不限次打断不重置（秒）
	MONCASTSKILL_CONDITION_13 = 13, // 13_技能间隔不限次打断重置（秒）
	MONCASTSKILL_CONDITION_14 = 14, // 14_技能间隔不限次打断不重置（秒）
	MONCASTSKILL_CONDITION_SELF_DIED = 15, // 15_自身死亡（死亡方式）
	MONCASTSKILL_CONDITION_16 = 16, // 16_于目标超过某距离连续释放（距离）
	MONCASTSKILL_CONDITION_COUNT = 17, // 
	MONCASTSKILL_FORCE_INT = 0x7fffffff, // 

};

enum MONSTER_SPECIALTY_TYPE {
	MONSPECTYPE_NULL = 0, // 空属性
	MONSPECTYPE_SIGHT = 1, // 更改怪物视野
	MONSPECTYPE_FIGHT_RANGE = 2, // 更改怪物攻击距离
	MONSPECTYPE_SET_INITIATIVE = 3, // 把怪物设为主动
	MONSPECTYPE_SET_PASSIVE = 4, // 把怪物设为被动
	MONSPECTYPE_HATRED_RANGE = 5, // 更改怪物的仇恨距离
	MONSPECTYPE_HATRED_TIME = 6, // 更改怪物的仇恨时间
	MONSPECTYPE_HP_RATE = 7, // 比例更改怪物的HP
	MONSPECTYPE_HP_LEVEL = 8, // 按等级更改怪物HP
	MONSPECTYPE_WEAKPOINT_RATE = 9, // 比例更改怪物的破绽
	MONSPECTYPE_DEFENCE_VALUE = 10, // 数值更改怪物的破绽
	MONSPECTYPE_PHY_MIN_ATTACK_RATE = 11, // 比例更改本体外攻最小攻击力
	MONSPECTYPE_PHY_MAX_ATTACK_RATE = 12, // 比例更改本体外攻最大攻击力
	MONSPECTYPE_PHY_MINMAX_ATTACK_RATE = 13, // 比例同时更改本体外攻最小最大攻击力
	MONSPECTYPE_PHY_DEFENCE_RATE = 14, // 比例更改本体外攻防御力
	MONSPECTYPE_PHY_CRIT = 15, // 更改本体外攻致命一击率
	MONSPECTYPE_PHY_CRITDMG_RATE = 16, // 比例更改本体外攻致命一击附加伤害
	MONSPECTYPE_MAG_MIN_ATTACK_RATE = 17, // 比例更改本体内攻最小攻击力
	MONSPECTYPE_MAG_MAX_ATTACK_RATE = 18, // 比例更改本体内攻最大攻击力
	MONSPECTYPE_MAG_MINMAX_ATTACK_RATE = 19, // 比例同时更改本体内攻最小最大攻击力
	MONSPECTYPE_MAG_DEFENCE_RATE = 20, // 比例更改本体内攻防御力
	MONSPECTYPE_MAG_CRIT = 21, // 更改本体内攻致命一击率
	MONSPECTYPE_MAG_CRITDMG_RATE = 22, // 比例更改本体内攻致命一击附加伤害
	MONSPECTYPE_FIGHT_HP_RECOVER_VALUE = 23, // 更改本体战斗状态下HP回复速度数值
	MONSPECTYPE_FIGHT_WEAKPOINT_RECOVER_VALUE = 24, // 更改本体战斗状态下破绽恢复速度数值
	MONSPECTYPE_FIGHT_HP_RECOVER_SPEED = 25, // 比例更改本体战斗状态下HP回复速度
	MONSPECTYPE_FIGHT_WEAKPOINT_RECOVER_SPEED = 26, // 比例更改本体战斗状态下破绽恢复速度
	MONSPECTYPE_MINMAX_MOVESPEED_RATE = 27, // 比例同时更改本体慢和快速移动速度
	MONSPECTYPE_NORMALATK_INTERVAL_RATE = 28, // 比例更改本体普通攻击间隔
	MONSPECTYPE_SCORE_RATE_ADD = 29, // 怪物积分倍率增加
	MONSPECTYPE_COUNT = 30, // 

};

enum MONSTER_USAGE_TYPE {
	MONSTERUSAGE_BIGWORLD_NORMAL = 0, // 大地图普通
	MONSTERUSAGE_BIGWORLD_TASK_BOSS = 1, // 大地图任务BOSS
	MONSTERUSAGE_COUNT = 2, // 
	MONSTERUSAGE_FORCE_INT = 0x7fffffff, // 

};

enum LIFELESS_TYPE {
	LIFELESS_TYPE_NORMAL = 0, // 0-普通怪物
	LIFELESS_TYPE_CANNOT_SEL = 1, // 1-无生命(不可被选但可被打)，且不显示名称
	LIFELESS_TYPE_CAN_SEL = 2, // 2-中间状态(可被选中，选中后同0-普通怪物)
	LIFELESS_TYPE_STATUE = 3, // 3-雕像（同1，但显示名称，还有一些特殊的显示逻辑）
	LIFELESS_TYPE_CANNOT_SEL_OR_PASS = 4, // 4-同1，但不可被穿越

};

enum MONSTER_COLLISION {
	NONE = 0, // 无碰撞
	SKILL = 1, // 有技能碰撞
	SKILL_AND_MOVE = 2, // 有技能和移动碰撞

};

enum MINE_TYPE_ENUM {
	MATTER_TYPE_DEFAULT = 0, // 普通矿
	MATTER_TYPE_EVA_MAP_MODEL = 1, // EVA活动地图模型
	MATTER_TYPE_DISNEY_CLOCK = 2, // 迪士尼钟
	MATTER_TYPE_DOODLE = 3, // 涂鸦墙

};

enum EXP_OBJ_STAND_MODE {
	EXP_OBJSTANDMODE_TWOFOOTED = 0, // 双足站立
	EXP_OBJSTANDMODE_FOURFOOTED = 1, // 四足站立
	EXP_OBJSTANDMODE_FLY = 2, // 悬空飞行等
	EXP_OBJSTANDMODE_COUNT = 3, // 

};

enum MON_ACTIVITION_TYPE {
	MONACTI_TYPE_NORMAL = 0, // 普通怪
	MONACTI_TYPE_ACTIVITION = 1, // 活动怪
	MONACTI_TYPE_INSTANCE = 2, // 副本怪
	MONACTI_TYPE_FACTION = 3, // 帮派怪
	MONACTI_TYPE_COUNT = 4, // 
	MONACTI_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum MON_SPECDMG_REDUCE_TYPE {
	MON_SPECDMG_REDUCE_NORMAL = 0, // 普通
	MON_SPECDMG_REDUCE_FIXED = 1, // 造成固定伤害
	MON_SPECDMG_REDUCE_COUNT = 2, // 

};

enum MONSTER_ATTACK_PLAYER_STRATEGY {
	NO_EFFECT = 0, // 不影响
	NO_ATTACK_PLAYER = 1, // 不攻击玩家
	ONLY_ATTACK_PLAYER = 2, // 只攻击玩家
	ONLY_ATTACK_DIFF_FACTION_PLAYER = 3, // 只攻击非本帮玩家
	ATTACK_PLAYER_STRATEGY_COUNT = 4, // 
	ATTACK_PLAYER_STRATEGY_INT = 0x7fffffff, // 

};

enum ITEM_DROP_MODEL_TYPE {
	ITEM_DROP_MODEL_GOLD = 0, // 钱袋
	ITEM_DROP_MODEL_EQUIP = 1, // 装备
	ITEM_DROP_MODEL_MATERIAL = 2, // 材料
	ITEM_DROP_MODEL_MEDICINE = 3, // 药
	ITEM_DROP_MODEL_SWORDMAN_EQUIP = 4, // 刀
	ITEM_DROP_MODEL_PIKEMAN_EQUIP = 5, // 枪
	ITEM_DROP_MODEL_MAGICIAN_EQUIP = 6, // 术
	ITEM_DROP_MODEL_ARCHER_EQUIP = 7, // 弓
	ITEM_DROP_MODEL_SCROLL = 8, // 卷轴
	ITEM_DROP_MODEL_TREASURE_BOX = 9, // 宝箱
	ITEM_DROP_MODEL_PAPER_MONEY = 10, // 纸币
	ITEM_DROP_MODEL_BLUE_GEM = 11, // 蓝宝石
	ITEM_DROP_MODEL_RED_GEM = 12, // 红宝石

};

enum CLIENT_ITEM_STATE {
	CIPT_NONE = 0, // CIPT_NONE
	CIPT_CAN_GIFT = 0x00000001, // 可赠送
	CIPT_CAN_DEAL = 0x00000002, // 可交易
	CIPT_NULL_1 = 0x00000004, // no_use
	CIPT_NULL_2 = 0x00000008, // no_use
	CIPT_SECURITY_LOCKED = 0x00000010, // 安全锁定

};

enum MEDICINE_TYPE_ENUM {
	MEDICINETYPE_INSTANT_HP = 0, // 瞬回HP
	MEDICINETYPE_INSTANT_MP = 1, // 瞬回MP
	MEDICINETYPE_CONTINUOUS_HP = 2, // 持续回HP
	MEDICINETYPE_CONTINUOUS_MP = 3, // 持续回MP
	MEDICINETYPE_INSTANT_AP = 4, // 持续回AP
	MEDICINETYPE_CONTINUOUS_AP = 5, // 持续回AP
	MEDICINETYPE_CLEAR_PK_VALUE = 6, // 洗pk值
	MEDICINETYPE_VIP_TRIAL_CARD = 7, // VIP体验卡
	MEDICINETYPE_EXP = 8, // 人物经验
	MEDICINETYPE_FORCE = 9, // 人物功力
	MEDICINETYPE_RUNE = 10, // 解锁符文
	MEDICINETYPE_EXP_MULTIPLIER = 11, // 多倍杀怪经验
	MEDICINETYPE_STORE_HP = 12, // 存储血量
	MEDICINETYPE_ACTION_HP = 13, // 读条后瞬回HP
	MEDICINETYPE_COUNT = 14, // 
	MEDICINETYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_COOL_TYPE {
	EXPCOOLTYPE_I = 0, // 类型I
	EXPCOOLTYPE_II = 1, // 类型II
	EXPCOOLTYPE_III = 2, // 类型III
	EXPCOOLTYPE_IV = 3, // 类型IV
	EXPCOOLTYPE_V = 4, // 类型V
	EXPCOOLTYPE_COUNT = 5, // 
	EXPCOOLTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_COOL_CLASS {
	EXP_COOLCLASS_INDEPENDENT = 0, // 独立
	EXP_COOLCLASS_SHARE = 1, // 共享
	EXP_COOLCLASS_COUNT = 2, // 
	EXP_COOLCLASS_FORCE_INT = 0x7fffffff, // 

};

enum LOOT_TYPE_ENUM {
	LOOTTYPE_NORMAL = 0, // 普通掉落
	LOOTTYPE_BOSS = 1, // BOSS掉落
	LOOTTYPE_BATTLE = 2, // 战场怪掉落
	LOOTTYPE_COUNT = 3, // LOOTTYPE_COUNT
	LOOTTYPE_FORCE_INT = 0x7fffffff, // 

};

enum INSTANCE_TYPE_ENUM {
	INSTTYPE_PERSONAL = 0, // 个人副本
	INSTTYPE_ACTIVITY = 1, // 活动副本
	INSTTYPE_NORMAL = 2, // 常规副本
	INSTTYPE_FACTION_BASE = 3, // 帮派基地
	INSTTYPE_FACTION_INST = 4, // 帮派副本
	INSTTYPE_TOURNAMENT = 5, // 团体竞赛副本
	INSTTYPE_DUEL = 6, // 决斗副本
	INSTTYPE_MELEE = 7, // 混战副本
	INSTTYPE_ARENA = 8, // 竞技场
	INSTTYPE_CORPS_BATTLE = 9, // 帮派竞赛
	INSTTYPE_CLIMB_TOWER = 10, // 个人爬塔
	INSTTYPE_CORPS_ARENA = 11, // 帮派攻城战
	INSTTYPE_HOMETOWN = 12, // 家园
	INSTTYPE_CORPS_TOWER = 13, // 帮派爬塔
	INSTTYPE_REVENGE_BATTLE = 14, // 仇杀约战
	INSTTYPE_MULTI_ARENA = 15, // 多人竞技场
	INSTTYPE_CENTER_BATTLE = 16, // 跨服战场
	INSTTYPE_CENTER_ARENA_SINGLE_BATTLE = 17, // 跨服单人竞技场
	INSTTYPE_CENTER_ARENA_TEAM_BATTLE = 18, // 跨服组队竞技场
	INSTTYPE_SECOND_HOMETOWN = 19, // 第二家园
	INSTTYPE_CORPS_CENTER_BATTLE = 20, // 帮派中心战场
	INSTTYPE_CENTER_SERVER_BATTLE = 21, // 中心服服务器对战
	INSTTYPE_QIANKUNJING_BATTLE = 22, // 乾坤镜
	INSTTYPE_NO_DIFFER_BATTLE = 23, // 无属性差异乱斗
	INSTTYPE_THIRD_HOMETOWN = 24, // 第三家园
	INSTTYPE_CONTEST_MARRIAGE = 25, // 比武招亲
	INSTTYPE_CENTER_COUPLE_SCORE_BATTLE = 26, // 跨服情侣积分赛
	INSTTYPE_CENTER_COUPLE_KNOCKOUT_BATTLE = 27, // 跨服情侣淘汰赛
	INSTTYPE_COUPLE_ARENA = 28, // 情侣竞技场
	INSTTYPE_CONTINUOUS = 29, // 连续副本
	INSTTYPE_CAR_RACE = 30, // 赛车副本
	INSTTYPE_CAR_RACE_PVE = 31, // 赛车副本PVE
	INSTTYPE_CAR_RACE_PVP = 32, // 赛车副本PVP
	INSTTYPE_SHOP = 33, // 商店副本
	INSTTYPE_PUBG_SINGLE_BATTLE = 34, // 大逃杀单排
	INSTTYPE_PUBG_TEAM_BATTLE = 35, // 大逃杀组队
	INSTTYPE_HOMETOWN_DESIGN = 36, // 家园设计
	INSTTYPE_ELIMINATE_1 = 37, // 淘汰赛阶段1
	INSTTYPE_ELIMINATE_2 = 38, // 淘汰赛阶段2
	INSTTYPE_ELIMINATE_3 = 39, // 淘汰赛阶段3
	INSTTYPE_CHESS = 40, // 自走棋
	INSTTYPE_FAIR = 41, // 公平1v1
	INSTTYPE_OVERCOOK = 42, // 胡闹厨房
	INSTTYPE_ROAMMINIGAME = 43, // 电玩大对决
	INSTTYPE_WOLF = 44, // 狼人杀
	INSTTYPE_CORPS_DUEL = 45, // 社团约战
	INSTTYPE_HOMETOWN_CONTRACT = 46, // 契约家园
	INSTTYPE_BAIDISHENGONG = 47, // 白帝神宫
	INSTTYPE_HONEYGARDEN = 48, // 甜蜜花园
	INSTTYPE_ELIMINATE_3v3_1 = 49, // 3v3淘汰赛阶段1
	INSTTYPE_ELIMINATE_3v3_2 = 50, // 3v3淘汰赛阶段2
	INSTTYPE_ELIMINATE_3v3_3 = 51, // 3v3淘汰赛阶段3
	INSTTYPE_RAINBOW = 52, // 彩虹航线
	INSTTYPE_PhotoExhibition = 53, // 摄影展厅
	INSTTYPE_HUNDRED_CENTER_CORPS_BATTLE = 54, // 新社团联赛跨服战场
	INSTTYPE_CORPS_ROAM_COMMUNITY_BATTLE = 55, // 跨服圣殿
	INSTTYPE_CENTER_PEEKABOO = 56, // 跨服躲猫猫
	INSTTYPE_ELIMINATE_2v2_1 = 57, // 2v2淘汰赛阶段1
	INSTTYPE_ELIMINATE_2v2_2 = 58, // 2v2淘汰赛阶段2
	INSTTYPE_ELIMINATE_2v2_3 = 59, // 2v2淘汰赛阶段3
	INSTTYPE_PHOTO_VEHICLE = 60, // 极影空间副本
	INSTTYPE_CROSS_MULTI_PVP = 61, // 跨服多人PVP
	INSTTYPE_TANGYUAN_STORE = 62, // 龙龙元宵铺
	INSTTYPE_CENTER_ARENA_TEAM_BATTLE_NEW = 63, // 新浩瀚天梯
	INSTTYPE_CENTER_ARENA = 64, // 浩瀚实训竞技场
	INSTTYPE_TOWNLET = 65, // 小镇
	INSTTYPE_ROUGE = 66, // 新肉鸽玩法副本
	INSTTYPE_RAID = 67, // 团队副本
	INSTTYPE_COUNT = 68, // 
	INSTTYPE_FORCE_INT = 0x7fffffff, // 

};

enum NORMALINST_ENTER_TYPE {
	NORMALINSTENTER_TEAM = 0, // 队伍
	NORMALINSTENTER_SWORN = 1, // 结义
	NORMALINSTENTER_FACTION = 2, // 帮派
	NORMALINSTENTER_COUNT = 3, // 
	NORMALINSTENTER_FORCE_INT = 0x7fffffff, // 

};

enum INSTANCE_BATTLE_TYPE {
	INSTBATTLETYPE_KILLMONSTER = 0, // 杀怪
	INSTBATTLETYPE_DEFENSE = 1, // 防守
	INSTBATTLETYPE_RESOURCE = 2, // 资源
	INSTBATTLETYPE_ACCPOINTS = 3, // 积分
	INSTBATTLETYPE_COUNT = 4, // 
	INSTBATTLETYPE_FORCE_INT = 0x7fffffff, // 

};

enum INST_TIMELIMIT_TYPE {
	INSTTIMELIMIT_EVERYDAY = 0, // 每天
	INSTTIMELIMIT_EVERYWEEK = 1, // 每周
	INSTTIMELIMIT_EVERYWEEKDAY = 2, // 每周每天
	INSTTIMELIMIT_EVERYHOUR = 3, // 每小时
	INSTTIMELIMIT_COUNT = 4, // 
	INSTTIMELIMIT_FORCE_INT = 0x7fffffff, // 

};

enum INST_SPECMODE_ENUM {
	INSTSPECMODE_NEWBIE = 0, // 新手模式
	INSTSPECMODE_MASTERDECIPLE = 1, // 师徒模式
	INSTSPECMODE_FACTION = 2, // 帮派模式
	INSTSPECMODE_COUNT = 6, // 
	INSTSPECMODE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_INSTWATCH_TYPE {
	EXP_INSTWATCH_CANNOT = 0, // 不可观战
	EXP_INSTWATCH_CHALLENGE_FAMOUS = 1, // 名人副本挑战
	EXP_INSTWATCH_COUNT = 2, // 
	EXP_INSTWATCH_FORCE_INT = 0x7fffffff, // 

};

enum TIMINGTOOL_ENUM {
	TIMINGTOOL_INCENSESTICK = 0, // 烧香
	TIMINGTOOL_SANDGLASS = 1, // 沙漏
	TIMINGTOOL_COUNT = 2, // 
	TIMINGTOOL_FORCE_INT = 0x7fffffff, // 

};

enum SPEC_TYPE {
	SPEC_CELEB = 1, // 观战名人挑战
	SPEC_BATTLE_RING = 2, // 观战擂台
	SPEC_TYPE_COUNT = 3, // 

};

enum BATTLEFIELD_CLASS {
	BATTLEFIELDCLASS_RING = 0, // 擂台
	BATTLEFIELDCLASS_ARENA = 1, // 战场
	BATTLEFIELDCLASS_BATTLEGROUND = 2, // 天梯
	BATTLEFIELDCLASS_COUNT = 3, // 
	BATTLEFIELDCLASS_FORCE_INT = 0x7fffffff, // 

};

enum SEARCH_SELECT_LIMIT {
	BATTLE_WITHOUT_PASSWORLD = 0x00000001, // 不需要密码进入
	BATTLE_NOT_PLAYING = 0x00000002, // 游戏未开始
	BATTLE_NEED_SPECTE = 0x00000004, // 可以观战
	BATTLE_WITH_NAME = 0x00000008, // 匹配间名
	BATTLE_CLASS = 0x00000010, // 战场类型

};

enum BATTLE_RESULT_ENUM {
	BRE_NONE = 0, // 未比赛
	BRE_VICTORY = 1, // BRE_VICTORY
	BRE_LOSE = 2, // BRE_LOSE
	BRE_DRAW = 3, // 平局

};

enum INST_BATTLE_TYPE {
	T_SPEC_CELEB = 1, // 观战名人挑战
	T_SPEC_BATTLE_RING = 2, // 观战擂台
	T_SPEC_BATTLE_ARENA = 3, // 观战竞技场
	T_SPEC_BATTLE_GROUND = 4, // 观战战场
	T_SPEC_TYPE_COUNT = 5, // 

};

enum BATTLEFIELD_ENTER_TYPE {
	BATTLEFIELDENTER_PERSONAL = 0, // 个人
	BATTLEFIELDENTER_TEAM = 1, // 队伍
	BATTLEFIELDENTER_SWORN = 2, // 结义
	BATTLEFIELDENTER_FACTION = 3, // 帮派
	BATTLEFIELDENTER_COUNT = 4, // 
	BATTLEFIELDENTER_FORCE_INT = 0x7fffffff, // 

};

enum BATTLEFIELD_OPEN_TYPE {
	BATTLEFIELDOPENTYPE_SINGLE = 0, // 单人
	BATTLEFIELDOPENTYPE_MULTI = 1, // 多方
	BATTLEFIELDOPENTYPE_COUNT = 2, // 
	BATTLEFIELDOPENTYPE_FORCE_INT = 0x7fffffff, // 

};

enum BATTLEFIELD_UI_TYPE {
	BATTLEFIELD_UI_NORMAL = 0, // 普通界面
	BATTLEFIELD_UI_1V1_DUEL = 1, // 1V1专用决斗界面
	BATTLEFIELD_UI_1V1_PUSH_CAR = 2, // 推车界面
	BATTLEFIELD_UI_5V5_TEAM_ARENA = 3, // 组队竞技场界面
	BATTLEFIELD_UI_5V5_PUSH_CAR = 4, // 组队推车界面
	BATTLEFIELD_UI_RECIPE_RESEARCH = 5, // 自研食谱界面
	BATTLEFIELD_UI_LIELONG_TEAM = 6, // 猎龙小队界面
	BATTLEFIELD_UI_ARENA_SOLO = 7, // 单人竞技场界面
	BATTLEFIELD_UI_REGIMENTAL_COMMANDER = 8, // 守卫团长
	BATTLEFIELD_UI_TANGYUAN_STORE = 9, // 龙龙元宵铺
	BATTLEFIELD_UI_COUNT = 9, // 
	BATTLEFIELD_UI_FORCE_INT = 0x7fffffff, // 

};

enum INSTSERV_TYPE {
	INSTSERVTYPE_INSTANCE = 0, // 副本服务
	INSTSERVTYPE_BATTLEFIELD = 1, // 战场服务
	INSTSERVTYPE_COUNT = 2, // 
	INSTSERVTYPE_FORCE_INT = 0x7fffffff, // 

};

enum TRANSMIT_CONDITION_ENUM {
	TRANSCONDITION_BETWEENWORLD = 0, // 大世界互传
	TRANSCONDITION_JUMPOUT_INST = 1, // 传出副本
	TRANSCONDITION_JUMPTO_INST = 2, // 传入副本
	TRANSCONDITION_JUMPOUT_SUBWORLD = 3, // 传出室内
	TRANSCONDITION_JUMPTO_SUBWORLD = 4, // 传入室内
	TRANSCONDITION_JUMPTO_OTHER_SERVER = 5, // 跨服传送
	TRANSCONDITION_JUMPTO_OTHER_COUNTRY = 6, // 跨国传送
	TRANSCONDITION_COUNT = 7, // 
	TRANSCONDITION_FORCE_INT = 0x7fffffff, // 

};

enum TRANSMIT_CONFIRM_TYPE {
	TRANSCONFIRM_TOUCH = 0, // 触碰传送
	TRANSCONFIRM_CLICK = 1, // 点击立即传送
	TRANSCONFIRM_CLICK_AND_CONFIRM = 2, // 点击且确认传送
	TRANSCONFIRM_FIND_PATH_TOUCH = 3, // 寻路触碰传送
	TRANSCONFIRM_COUNT = 4, // 
	TRANSCONFIRM_FORCE_INT = 0x7fffffff, // 

};

enum ROLL_TRANSMIT_CONDITION_ENUM {
	ROLL_TRANSCONDITION_BETWEENWORLD = 0, // 大世界互传
	ROLL_TRANSCONDITION_JUMPOUT_INST = 1, // 传出副本
	ROLL_TRANSCONDITION_TO_FACTION = 2, // 传送回帮
	ROLL_TRANSCONDITION_COUNT = 3, // 
	ROLL_TRANSCONDITION_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SHAPE_ENUM {
	EXPSHAPE_NONE = 0, // 无效
	EXPSHAPE_SPHERE = 1, // 球体
	EXPSHAPE_CYLINDER = 2, // 圆柱
	EXPSHAPE_COUNT = 3, // 
	EXPSHAPE_FORCE_INT = 0x7fffffff, // 

};

enum PET_TYPE_ENUM {
	PETTYPE_PET = 0, // 0(宠物)、
	PETTYPE_RETINUE = 1, // 1(随从)、
	PETTYPE_FRIEND = 2, // 2(朋友)
	PETTYPE_COUNT = 3, // 
	PETTYPE_FORCE_INT = 0x7fffffff, // 

};

enum PETFOLLOW_TYPE {
	PETFOLLOW_FLY = 0, // 飞行类
	PETFOLLOW_BEAST_WALK = 1, // 走兽
	PETFOLLOW_HUMAN = 2, // 人形
	PETFOLLOW_BEAST_JUMP = 3, // 跳兽
	PETFOLLOW_COUNT = 4, // 
	PETFOLLOW_FORCE_INT = 0x7fffffff, // 

};

enum PETTALENT_TYPE {
	PETTALENT_NOTMAL = 0, // 普通
	PETTALENT_LIDAO = 1, // 力道天赋异能
	PETTALENT_TIPO = 2, // 体魄天赋异能
	PETTALENT_JINGU = 3, // 筋骨天赋异能
	PETTALENT_QIHAI = 4, // 气海天赋异能
	PETTALENT_COUNT = 5, // 
	PETTALENT_FORCE_INT = 0x7fffffff, // 

};

enum PET_MEDICINE_TYPE_ENUM {
	PETMEDICINE_INSTANT_HP = 0, // 瞬回HP
	PETMEDICINE_EXP = 1, // 增加经验
	PETMEDICINE_APTITUDE = 2, // 增加资质
	PETMEDICINE_GROWTH = 3, // 增加成长
	PETMEDICINE_PROP = 4, // 增加属性
	PETMEDICINE_DESTINY = 5, // 增加天命点数
	PETMEDICINE_COUNT = 6, // 
	PETMEDICINE_FORCE_INT = 0x7fffffff, // 

};

enum SELTARGET_TYPE {
	SELTARGET_NONE = 0, // 无需选中
	SELTARGET_MONSTER = 1, // 需选中怪物
	SELTARGET_NPC = 2, // 需选中NPC
	SELTARGET_PLAYER = 3, // 需选中玩家
	SELTARGET_TEAMMEMBER = 4, // 需选中队友
	SELTARGET_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SKILLSEQ_TYPE {
	EXPSKILLSEQTYPE_NORMAL = 0, // 普通连招
	EXPSKILLSEQTYPE_RUNE = 1, // 符文生效的连招
	EXPSKILLSEQTYPE_PLAYER_NORMAL_SKILL = 2, // 人物普攻
	EXPSKILLSEQTYPE_COUNT = 3, // 
	EXPSKILLSEQTYPE_FORCE_INT = 0x7fffffff, // 

};

enum SKILLSEQ_LRBIND_TYPE {
	SKILLSEQ_LRBIND_BOTH = 0, // SKILLSEQ_LRBIND_BOTH
	SKILLSEQ_LRBIND_ONLYLEFT = 1, // SKILLSEQ_LRBIND_ONLYLEFT
	SKILLSEQ_LRBIND_ONLYRIGHT = 2, // SKILLSEQ_LRBIND_ONLYRIGHT
	SKILLSEQ_LRBIND_NONE = 3, // SKILLSEQ_LRBIND_NONE
	SKILLSEQ_LRBIND_COUNT = 4, // 
	SKILLSEQ_LRBIND_FORCE_INT = 0x7fffffff, // 

};

enum EQUIPTYPE_ENUM {
	EQUIPTYPE_UNKNOWN = -1, // EQUIPTYPE_UNKNOWN
	EQUIPTYPE_WEAPON_AND_ARMOUR = 0, // 兵甲
	EQUIPTYPE_INSIDE = 1, // 内装
	EQUIPTYPE_HORSE = 2, // 坐骑装备
	EQUIPTYPE_WING = 3, // 翅膀
	EQUIPTYPE_PEDANT_1 = 4, // 挂件1
	EQUIPTYPE_PEDANT_2 = 5, // 挂件2
	EQUIPTYPE_PEDANT_3 = 6, // 挂件3
	EQUIPTYPE_FASION = 7, // 时装
	EQUIPTYPE_COUNT = 8, // 
	EQUIPTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_EQUIP_PRODUCE_SOURCE {
	EQUIP_PROD_SOURCE_NONE = 0, // 非制造
	EQUIP_PROD_SOURCE_METALSTONE = 1, // 金石制造
	EQUIP_PROD_SOURCE_WOODLEATHER = 2, // 木革制造
	EQUIP_PROD_SOURCE_CLOTHSILK = 3, // 布帛制造
	EQUIP_PROD_SOURCE_COUNT = 4, // 
	EQUIP_PROD_SOURCE_FORCE_INT = 0x7fffffff, // 

};

enum ADDONGRP_TYPE {
	ADDONGRPTYPE_EQUIP_ESSENCE = 0, // 装备本体
	ADDONGRPTYPE_EQUIP_GROW = 1, // 装备成长
	ADDONGRPTYPE_STONE = 2, // 宝石单体
	ADDONGRPTYPE_SUIT = 3, // 套装
	ADDONGRPTYPE_TITLE = 4, // 称号
	ADDONGRPTYPE_COUNT = 5, // 
	ADDONGRPTYPE_FORCE_INT = 0x7fffffff, // 

};

enum ADDON_GEN_TYPE {
	ADDONGENTYPE_ALL_INDEPENDENT = 0, // 全部独立生成
	ADDONGENTYPE_SELECTONE = 1, // 随机属性择一生成
	ADDONGENTYPE_BY_COUNT = 2, // 按个数计算生成
	ADDONGENTYPE_BY_COUNT2 = 3, // 按个数计算生成
	ADDONGENTYPE_COUNT = 4, // 
	ADDONGENTYPE_FORCE_INT = 0x7fffffff, // 

};

enum ADDONDETAIL_GEN_TYPE {
	ADDONDETAILGEN_RANDOM = 0, // 随机生成
	ADDONDETAILGEN_MIN = 1, // 按最小值生成
	ADDONDETAILGEN_MAX = 2, // 按最大值生成
	ADDONDETAILGEN_COUNT = 3, // 
	ADDONDETAILGEN_FORCE_INT = 0x7fffffff, // 

};

enum EQUIPIVTR_ENUM {
	EQUIPIVTR_INVALID = -1, // EQUIPIVTR_INVALID
	EQUIPIVTR_WEAPON = 0, // 武器
	EQUIPIVTR_HEAD = 1, // 帽子
	EQUIPIVTR_ARMOUR = 2, // 衣服
	EQUIPIVTR_HAND = 3, // 手套
	EQUIPIVTR_LEG = 4, // 裤子
	EQUIPIVTR_FOOT = 5, // 鞋子
	EQUIPIVTR_NECK = 6, // 项链
	EQUIPIVTR_FINGER = 7, // 戒指
	EQUIPIVTR_WRIST = 8, // 手环
	EQUIPIVTR_WAIST = 9, // 腰带
	EQUIPIVTR_TEMP_LONGHUI = 10, // 限时龙辉
	EQUIPIVTR_ENHANCE_SIZE = 11, // 
	EQUIPIVTR_SIZE = 11, // 
	EQUIPIVTR_SIZE_SHOW = 10, // 显示的装备数量
	EQUIPIVTR_PUBG_MAIN_WEAPON = 16, // 大逃杀主武器
	EQUIPIVTR_PUBG_SUB_WEAPON = 17, // 大逃杀副武器
	EQUIPIVTR_PUBG_HEAD = 18, // 大逃杀头盔
	EQUIPIVTR_PUBG_ARMOUR = 19, // 大逃杀护甲
	EQUIPIVTR_PUBG_NECK = 20, // 大逃杀护符
	EQUIPIVTR_PUBG_SIZE = 20, // 
	EQUIPIVTR_FORCE_INT = 0x7fffffff, // 

};

enum EQUIPLOCATION_ENUM {
	enumSkinShowNone = 0, // 不显示
	enumSkinShowHead = 1, // 头部
	enumSkinShowGlasses = 2, // 眼镜
	enumSkinShowNose = 3, // 鼻子
	enumSkinShowMustache = 4, // 胡子
	enumSkinShowUpperBody = 5, // 上衣
	enumSkinShowLowerBody = 6, // 下衣
	enumSkinShowFoot = 7, // 鞋子
	enumSkinShowWrist = 8, // 护腕
	enumSkinShowUpperAndLower = 9, // 全身(上衣带下衣)
	enumSkinShowFace = 10, // 面部
	enumSkinShowBack = 11, // 背部
	enumNumSkinShow = 12, // enumNumSkinShow
	EQUIPLOCATION_FORCE_INT = 0x7fffffff, // 

};

enum EQUIP_HH_ENUM {
	EXP_EQUIPHH_None = 0, // 无
	EXP_EQUIPHH_Head = 1, // HH_Head
	EXP_EQUIPHH_toufa = 2, // HH_toufa
	EXP_EQUIPHH_jian01 = 3, // HH_jian01
	EXP_EQUIPHH_jian02 = 4, // HH_jian02
	EXP_EQUIPHH_bind = 5, // HH_bind
	EXP_EQUIPHH_spine = 6, // HH_spine
	EXP_EQUIPHH_shou01 = 7, // HH_shou01
	EXP_EQUIPHH_shou02 = 8, // HH_shou02
	EXP_EQUIPHH_righthandweapon = 9, // HH_righthandweapon
	EXP_EQUIPHH_lefthandweapon = 10, // HH_lefthandweapon
	EXP_EQUIPHH_base = 11, // HH_base
	EXP_EQUIPHH_tui01 = 12, // HH_tui01
	EXP_EQUIPHH_tui02 = 13, // HH_tui02
	EXP_EQUIPHH_xiaotui01 = 14, // HH_xiaotui01
	EXP_EQUIPHH_xiaotui02 = 15, // HH_xiaotui02
	EXP_EQUIPHH_jiao01 = 16, // HH_jiao01
	EXP_EQUIPHH_jiao02 = 17, // HH_jiao02
	EXP_EQUIPHH_chest = 18, // HH_胸
	EXP_EQUIPHH_wrist = 19, // HH_腰
	EXP_EQUIPHH_belly = 20, // HH_腹
	EXP_EQUIPHH_pifeng = 21, // HH_pifeng
	EXP_EQUIPHH_center = 22, // HH_置心
	EXP_EQUIPHH_left_wrist = 23, // HH_左腰部
	EXP_EQUIPHH_right_wrist = 24, // HH_右腰部
	EXP_EQUIPHH_COUNT = 25, // 

};

enum VEHICLETYPE_ENUM {
	VEHICLETYPE_INVALID = -1, // VEHICLETYPE_INVALID
	VEHICLETYPE_MOUNT = 0, // 骑乘（主体为虚拟，客体为实体）
	VEHICLETYPE_CARRY = 1, // 携带（绑定物虚拟。绑定关系由动作决定）
	VEHICLETYPE_BETWEENROLE = 2, // 角色间绑定（绑定双方均为实体）
	VEHICLETYPE_SEAT = 3, // 凳子（主体为实体；且只设置位置、不真正挂上挂点）
	VEHICLETYPE_TRANSPORT = 4, // 传送（类似凳子，绑定结束传送到特定位置）
	VEHICLETYPE_COUNT = 5, // 
	VEHICLETYPE_FORCE_INT = 0x7fffffff, // 

};

enum VEHECLE_RELATIONREQ_TYPE {
	VEHICLE_RELREQ_NONE = 0, // 无需求
	VEHICLE_RELREQ_SWORN = 1, // 要求结义关系
	VEHICLE_RELREQ_MARRIAGE = 2, // 要求夫妻关系
	VEHICLE_RELREQ_TEAM = 3, // 要求组队关系
	VEHICLE_RELREQ_COUNT = 4, // 
	VEHICLE_RELREQ_FORCE_INT = 0x7fffffff, // 

};

enum SUBOBJ_TYPE {
	SUBOBJTYPE_IMMOVABLE = 0, // 不可移动的子物体
	SUBOBJTYPE_MOVABLE = 1, // 可移动的子物体
	SUBOBJTYPE_COUNT = 2, // SUBOBJTYPE_COUNT
	SUBOBJTYPE_FORCE_INT = 0x7fffffff, // 

};

enum SUBOBJ_MOVING_TYPE {
	SUBOBJMOVETYPE_LINE = 0, // 直线型
	SUBOBJMOVETYPE_CIRCLE = 1, // 圆周
	SUBOBJMOVETYPE_MISSILE = 2, // 类导弹
	SUBOBJMOVETYPE_TRACE = 3, // 跟踪
	SUBOBJMOVETYPE_PENDULUM = 4, // 钟摆
	SUBOBJMOVETYPE_LINE_POS = 5, // 直线运动到目标点
	SUBOBJMOVETYPE_ROTATE = 6, // 原地旋转
	SUBOBJMOVETYPE_LINE_DIR = 7, // 停留一会再直线运动
	SUBOBJMOVETYPE_COUNT = 8, // 
	SUBOBJMOVETYPE_FORCE_INT = 0x7fffffff, // 

};

enum SUBOBJ_MOVETARGET_TYPE {
	SUBOBJTARGETTYPE_SELFFIX = 0, // 本体固定
	SUBOBJTARGETTYPE_TARGETFIX = 1, // 目标固定
	SUBOBJTARGETTYPE_SELF2TARGET = 2, // 本体到目标
	SUBOBJTARGETTYPE_TARGET2SELF = 3, // 目标到本体
	SUBOBJTARGETTYPE_SELF_CURDIR = 4, // 本体当前方向
	SUBOBJTARGETTYPE_COUNT = 5, // 
	SUBOBJTARGETTYPE_FORCE_INT = 0x7fffffff, // 

};

enum SUBOBJ_JUDGEEFFECT_TYPE {
	SUBOBJEFFCTTYPE_NONE = 0, // SUBOBJEFFCTTYPE_NONE
	SUBOBJEFFCTTYPE_COLLIDE = 1, // 碰撞
	SUBOBJEFFCTTYPE_TIMER = 2, // 定时
	SUBOBJEFFCTTYPE_ENDING = 3, // 结束
	SUBOBJEFFCTTYPE_STARTING = 4, // 初始
	SUBOBJEFFCTTYPE_REMOTECTRL = 5, // 遥控
	SUBOBJEFFCTTYPE_COLLIDE_2 = 6, // 碰撞后不消失
	SUBOBJEFFCTTYPE_COUNT = 7, // 
	SUBOBJEFFCTTYPE_FORCE_INT = 0x7fffffff, // 

};

enum SUBOBJ_LIMIT_TYPE {
	SUBOBJLIMITTYPE_NONE = 0, // 无限制
	SUBOBJLIMITTYPE_REACHMAX_NOTGEN = 1, // 如果达到数量上限则无法生成
	SUBOBJLIMITTYPE_REACHMAX_REMOVEFIRST = 2, // 如果达到数量上限则去掉最开始已存在的子物体
	SUBOBJLIMITTYPE_COUNT = 3, // 
	SUBOBJLIMITTYPE_FORCE_INT = 0x7fffffff, // 

};

enum SUBOBJ_IMITATE_TYPE {
	SUBOBJIMITATE_NONE = 0, // 无模拟
	SUBOBJIMITATE_PLAYER_WEAPON = 1, // 模拟人物武器
	SUBOBJIMITATE_GRENADE = 2, // 模拟手雷
	SUBOBJIMITATE_OWNERSELF = 3, // 模拟本体
	SUBOBJIMITATE_OWNERSELF_ALLTHEWAY = 4, // 全程模拟本体
	SUBOBJIMITATE_COUNT = 5, // 
	SUBOBJIMITATE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_LOTTERY_TYPE {
	LOTTERYTYPE_OPEN_DIRECTLY = 0, // 0直接开启
	LOTTERYTYPE_HORSE_RACE = 1, // 跑马机
	LOTTERYTYPE_SLOT_MACHINE = 2, // 老虎机
	LOTTERYTYPE_MINMAX_GUESS = 3, // 猜大小
	LOTTERYTYPE_FINGER_GUESS = 4, // 猜拳
	LOTTERYTYPE_DRAW = 5, // 5抽签
	LOTTERYTYPE_DIVINE = 6, // 求卦
	LOTTERYTYPE_POETRY_PUZZLE = 7, // 诗迷
	LOTTERYTYPE_SMASH_EGG = 8, // 砸蛋
	LOTTERYTYPE_GIFT_BAG = 9, // 礼包
	LOTTERYTYPE_TREASUREMAP_IDENTIFY = 10, // 10藏宝图辨识
	LOTTERYTYPE_TREASUREMAP_DIG = 11, // 藏宝图挖掘
	LOTTERYTYPE_FLOP = 12, // 通关翻牌
	LOTTERYTYPE_AUTODRAW = 13, // 自动抽奖
	LOTTERYTYPE_INSTANCE_DRAW = 14, // 副本抽奖
	LOTTERYTYPE_MALL = 15, // 商城抽奖
	LOTTERYTYPE_LOTTERY_POOL = 16, // 奖池抽奖
	LOTTERYTYPE_BAODI_ZHENLONG = 17, // 保底珍珑
	LOTTERYTYPE_LOW_CUBE = 18, // 低级魔方
	LOTTERYTYPE_HIGH_CUBE = 19, // 高级魔方
	LOTTERYTYPE_ACTIVITY_CUBE = 20, // 活动魔方
	LOTTERYTYPE_JOKER_TOUR = 21, // 王牌之旅彩票
	LOTTERYTYPE_COUNT = 22, // 
	LOTTERYTYPE_RETURN = 23, // 回归彩票
	LOTTERYTYPE_QXQY = 24, // 千寻奇遇
	LOTTERYTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_MAIL_TYPE {
	EXPMAILTYPE_HELP = 0, // 帮助信息
	EXPMAILTYPE_SYSINFO = 1, // 系统公告
	EXPMAILTYPE_TASK = 2, // 任务奖励
	EXPMAILTYPE_OTHERS = 3, // 其他
	EXPMAILTYPE_COUNT = 4, // 
	EXPMAILTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_FACTION_DYN_SERVICE_TYPE {
	EXP_FACTION_DYN_SERVICE_TYPE_NONE = 0, // 无
	EXP_FACTION_DYN_SERVICE_TYPE_LOGISTIC = 1, // 招工服务
	EXP_FACTION_DYN_SERVICE_TYPE_BUY = 2, // 收购服务
	EXP_FACTION_DYN_SERVICE_TYPE_SELL = 3, // 出售服务
	EXP_FACTION_DYN_SERVICE_TYPE_ACTIVITY = 4, // 活动服务
	EXP_FACTION_DYN_SERVICE_TYPE_COUNT = 5, // 
	EXP_FACTION_DYN_SERVICE_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_NPC_FACTIONSPEC_TYPE {
	EXP_NPC_FACTIONSPEC_TYPE_NONE = 0, // 非帮派特有
	EXP_NPC_FACTIONSPEC_TYPE_MAINBASE = 1, // 帮派主基地
	EXP_NPC_FACTIONSPEC_TYPE_SUBBASE1 = 2, // 分舵1
	EXP_NPC_FACTIONSPEC_TYPE_SUBBASE2 = 3, // 分舵2
	EXP_NPC_FACTIONSPEC_TYPE_SUBBASE3 = 4, // 分舵3
	EXP_NPC_FACTIONSPEC_TYPE_COUNT = 5, // 
	EXP_NPC_FACTIONSPEC_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_NPC_SHOW_TYPE {
	EXPNPCSHOW_FUNCTIONAL = 0, // 功能NPC
	EXPNPCSHOW_NORMAL = 1, // 普通NPC
	EXPNPCSHOW_TRANSMIT = 2, // 传送NPC
	EXPNPCSHOW_MONSTER = 3, // 怪物NPC
	EXPNPCSHOW_COUNT = 4, // 
	EXPNPCSHOW_FORCE_INT = 0x7fffffff, // 

};

enum EXP_MINE_GATHER_TYPE {
	EXPMINEGATHRERTYPE_SINGLE_LIMIT_TIMES = 0, // 单人有限次采
	EXPMINEGATHRERTYPE_SINGLE_INFINITE_TIMES = 1, // 单人无限次采
	EXPMINEGATHRERTYPE_MULTI_INFINITE_TIMES = 2, // 多人无限次采
	EXPMINEGATHRERTYPE_MULTI_LIMIT_TIMES = 3, // 多人有限次采
	EXPMINEGATHRERTYPE_COUNT = 4, // 
	EXPMINEGATHRERTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_MINE_SHOW_TYPE {
	EXPMINESHOW_NONE = 0, // 不显示
	EXPMINESHOW_PRODUCE_MINE = 1, // 生产用矿脉
	EXPMINESHOW_PRODUCE_WOOD = 2, // 生产用数目
	EXPMINESHOW_PRODUCE_FISH = 3, // 生产用鱼群
	EXPMINESHOW_COUNT = 4, // 
	EXPMINESHOW_FORCE_INT = 0x7fffffff, // 

};

enum CHAT_CHANNEL_ENUM {
	CHAT_CHANNEL_LOCAL = 0, // 普通频道
	CHAT_CHANNEL_MAP = 1, // 地图频道
	CHAT_CHANNEL_WORLD = 2, // 狮吼频道
	CHAT_CHANNEL_TEAM = 3, // 队伍频道
	CHAT_CHANNEL_FACTION = 4, // 帮派频道
	CHAT_CHANNEL_BROADCAST = 5, // 广播频道
	CHAT_CHANNEL_SYSTEM = 6, // 系统频道
	CHAT_CHANNEL_NATION = 7, // 世界频道
	CHAT_CHANNEL_MISC = 8, // 其它频道
	CHAT_CHANNEL_COMMANDER = 9, // 指挥频道
	CHAT_CHANNEL_WHISPER = 10, // 密语频道
	CHAT_CHANNEL_ALLIANCE = 11, // 盟国频道
	CHAT_CHANNEL_SEARCHTEAM = 12, // 寻队频道
	CHAT_CHANNEL_MESSAGE = 13, // 消息频道
	CHAT_CHANNEL_ARENA = 14, // 竞技场频道
	CHAT_CHANNEL_MANIFESTO = 15, // 系统公告频道
	CHAT_CHANNEL_GRADE = 16, // 新手频道
	CHAT_CHANNEL_CAMP = 17, // 阵营频道
	CHAT_CHANNEL_ROAM = 18, // 跨服狮吼频道
	CHAT_CHANNEL_CG = 19, // CG弹幕频道
	CHAT_CHANNEL_GROUP = 20, // 群聊
	CHAT_CHANNEL_COUPLEMSG = 21, // 暗恋信鸽
	CHAT_CHANNEL_CORPS_RECURIT = 22, // 社团招聘频道
	CHAT_CHANNEL_SYSTEM_SPECIAL = 23, // 特殊系统频道
	CHAT_CHANNEL_PRECREATECHAT = 24, // 预创建角色聊天室频道
	CHAT_CHANNEL_VIP_HELPER = 25, // 充值特权助手频道
	CHAT_CHANNEL_GROBOT = 26, // 知己机器人频道
	CHAT_CHANNEL_TEAM_2 = 27, // 队伍频道2
	CHAT_CHANNEL_MINIGAME_WHISPER = 28, // 小游戏私聊
	CHAT_CHANNEL_WINTER_PROJECT_PUBLIC = 29, // 冬日计划公开频道
	CHAT_CHANNEL_WINTER_PROJECT_UNDERWORLD = 30, // 冬日计划阴间频道
	CHAT_CHANNEL_WINTER_PROJECT_INTERCOM_1 = 31, // 冬日计划对讲机1
	CHAT_CHANNEL_WINTER_PROJECT_INTERCOM_2 = 32, // 冬日计划对讲机2
	CHAT_CHANNEL_WINTER_PROJECT_INTERCOM_3 = 33, // 冬日计划对讲机3
	CHAT_CHANNEL_ROAM_NORMAL = 34, // 跨服频道(普通中心服)
	CHAT_CHANNEL_ANONYMOUS_CHAT = 35, // 匿名聊天
	CHAT_CHANNEL_CROSS_SERVER_TEAM = 36, // 跨服队伍
	CHAT_CHANNEL_ROAM_COMMUNITY = 37, // 跨服社区
	CHAT_CHANNEL_INTERACT_FISH = 38, // 钓鱼频道
	CHAT_CHANNEL_ROAM_GROUP = 39, // 跨服群组(浩瀚天梯)
	CHAT_CHANNEL_MAX = 39, // 
	CHAT_CHANNEL_CLIENT_LOCAL = 100, // 剧情频道
	CHAT_CHANNEL_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SPEAK_TARGET {
	SPKTAR_SIGHT = 0, // 怪物视野
	SPKTAR_CUR_WORLD = 1, // 同一地图
	SPKTAR_ALL_WORLD = 2, // 全世界
	SPKTAR_DIS_RANGE = 3, // 怪物周围限定范围
	SPKTAR_ESCORT_HOST = 4, // 护送主体
	SPKTAR_INST_FRIEND = 5, // 副本友方阵营
	SPKTAR_COUNT = 6, // 
	SPKTAR_FORCE_INT = 0x7fffffff, // 

};

enum EXP_TRANSFORCE_TYPE {
	EXPTRANSFORCETYPE_TRANSFORCE = 0, // 传功
	EXPTRANSFORCETYPE_HEAL = 1, // 疗伤
	EXPTRANSFORCETYPE_GETTHROUGH_MERIDIAN = 2, // 打通经脉
	EXPTRANSFORCETYPE_COUNT = 3, // 
	EXPTRANSFORCETYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_TEACHSKILL_TYPE {
	EXPTEACHSKILLTYPE_NOLIMIT = 0, // 无限制
	EXPTEACHSKILLTYPE_ONLY_LEVELUP = 1, // 1—只升级技能
	EXPTEACHSKILLTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_TITLE_CLASS {
	EXPTITLECLASS_0 = 0, // EXPTITLECLASS_0
	EXPTITLECLASS_1 = 1, // EXPTITLECLASS_1
	EXPTITLECLASS_2 = 2, // EXPTITLECLASS_2
	EXPTITLECLASS_COUNT = 3, // 
	EXPTITLECLASS_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ACHIEVEMENT_TYPE {
	EXPACHIEVETYPE_PERMANENT = 0, // 永久成就
	EXPACHIEVETYPE_DAILY = 1, // 每日成就
	EXPACHIEVETYPE_NATION_WAR = 2, // 国战成就
	EXPACHIEVETYPE_ACTIVITY_LIMIT = 3, // 活动成就
	EXPACHIEVETYPE_DAY_14 = 4, // 限时14天成就
	EXPACHIEVETYPE_COUNT = 5, // 
	EXPACHIEVETYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ACHIEVEMENT_LIB_TYPE {
	EXP_ACHIEVEMENT_LIB_NORMAL = 0, // 普通成就
	EXP_ACHIEVEMENT_LIB_ACTIVITY = 1, // 活跃活动成就
	EXP_ACHIEVEMENT_LIB_AWARD = 2, // 福利计次成就

};

enum EXP_ACHIEVEMENT_CLASS {
	EXPACHIEVECLASS_EXPERIENCE = 0, // 历程（自身成长类）
	EXPACHIEVECLASS_GEOGRAPHY = 1, // 地理（场景相关类）
	EXPACHIEVECLASS_STORY = 2, // 笑傲（原著剧情类）
	EXPACHIEVECLASS_WUSHU = 3, // 武道（门派武学类）
	EXPACHIEVECLASS_COMMERCE = 4, // 商业（生计经商类）
	EXPACHIEVECLASS_JIANGHU = 5, // 江湖（社会交互类）
	EXPACHIEVECLASS_RANKING = 6, // 武林（排行类）
	EXPACHIEVECLASS_COUNT = 7, // 
	EXPACHIEVECLASS_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ACHIEVEDISP_COND {
	EXP_ACHIEVEDISP_COND_NONE = 0, // 无
	EXP_ACHIEVEDISP_COND_UNLOCK = 1, // 解锁
	EXP_ACHIEVEDISP_COND_FINISH = 2, // 完成
	EXP_ACHIEVEDISP_COND_COUNT = 3, // 
	EXP_ACHIEVEDISP_COND_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ACHIEVE_MATCH_NUM {
	EXP_ACHIEVE_MATCH_ALL = 0, // 完成全部
	EXP_ACHIEVE_MATCH_ONE = 1, // 任意一个

};

enum EXP_ACHIEVE_STATE_CONDITION {
	EXP_ACHIEVE_STATECOND_NONE = 0, // 无条件
	EXP_ACHIEVE_STATECOND_LEVEL = 1, // 人物等级达到
	EXP_ACHIEVE_STATECOND_MONEY = 2, // 钱数达到
	EXP_ACHIEVE_STATECOND_PK_VALUE = 3, // pk值达到
	EXP_ACHIEVE_STATECOND_NORMAL_TASK = 4, // 完成普通任务
	EXP_ACHIEVE_STATECOND_LEARN_SKILL = 5, // 学习技能
	EXP_ACHIEVE_STATECOND_MAKE_FRIEND = 6, // 增加好友
	EXP_ACHIEVE_STATECOND_JOIN_MAFIA = 7, // 加入帮派
	EXP_ACHIEVE_STATECOND_ACHIEVEMENT_GRADE = 8, // 成就评分
	EXP_ACHIEVE_STATECOND_EQUIP_QUALITY_COUNT = 9, // 穿戴某品阶装备数量
	EXP_ACHIEVE_STATECOND_EQUIP_START_COUNT = 10, // 穿戴某炼星等级装备数量
	EXP_ACHIEVE_STATECOND_INVENTORY_CAPACITY = 11, // 包裹格子数量
	EXP_ACHIEVE_STATECOND_FIGHTING = 12, // 人物战斗力
	EXP_ACHIEVE_STATECOND_MOUNT = 13, // 拥有坐骑
	EXP_ACHIEVE_STATECOND_LIMITNUM_TASK = 14, // 完成限次任务
	EXP_ACHIEVE_STATECOND_LOGIN = 15, // 登录
	EXP_ACHIEVE_STATECOND_REPUTATION = 16, // 声望达到
	EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO_GROUP = 17, // 集齐卡牌组合分组
	EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO = 18, // 激活卡牌组合
	EXP_ACHIEVE_STATECOND_ACCOMP_ACHIEVE = 19, // 完成成就
	EXP_ACHIEVE_STATECOND_PET_FIGHTING = 20, // 宠物战斗力
	EXP_ACHIEVE_STATECOND_MARRY = 21, // 结婚
	EXP_ACHIEVE_STATECOND_EQUIP_STONE = 22, // 宝石镶嵌等级达到
	EXP_ACHIEVE_STATECOND_UPGRADE_FLYSWORD = 23, // 升级飞剑
	EXP_ACHIEVE_STATECOND_UPGRADE_RRACTICE = 24, // 修炼升级
	EXP_ACHIEVE_STATECOND_TALISMAN = 25, // 法宝状态
	EXP_ACHIEVE_STATECOND_UNUSE = 26, // 无用
	EXP_ACHIEVE_STATECOND_RETINUE_FORMATION = 27, // 随从阵法
	EXP_ACHIEVE_STATECOND_UNLOCK_FASHION = 28, // 解锁时装
	EXP_ACHIEVE_STATECOND_INVOKE_WING_SOUL = 29, // 飞剑铸灵
	EXP_ACHIEVE_STATECOND_INVENTORY_EXTRA_CAPACITY = 30, // 包裹扩展格子数量
	EXP_ACHIEVE_STATECOND_FASHION_COLOR = 31, // 开启时装色块数量
	EXP_ACHIEVE_STATECOND_GUARD_COUNT = 32, // 解锁守护灵数量
	EXP_ACHIEVE_STATECOND_HAS_TITLE = 33, // 拥有特定称号
	EXP_ACHIEVE_STATECOND_TOP_RANK = 34, // 排行榜历史最高排名达到多少
	EXP_ACHIEVE_STATECOND_RETINUE = 35, // 解锁伙伴
	EXP_ACHIEVE_STATECOND_PHOTO = 36, // 解锁头像
	EXP_ACHIEVE_STATECOND_EQUIP_AFFIX_QUALITY = 37, // 解锁词缀套装
	EXP_ACHIEVE_STATECOND_LONGYU = 38, // 解锁龙语
	EXP_ACHIEVE_STATECOND_RETINUE_AMITY = 39, // 伙伴友好度
	EXP_ACHIEVE_STATECOND_RETINUE_LEVEL = 40, // 伙伴等级
	EXP_ACHIEVE_STATECOND_RETINUE_QUALITY = 41, // 伙伴品质
	EXP_ACHIEVE_STATECOND_RETINUE_PRIVATE_ITEM = 42, // 伙伴私有物
	EXP_ACHIEVE_STATECOND_RETINUE_FASHION_COLOR = 43, // 伙伴时装配色
	EXP_ACHIEVE_STATECOND_RETINUE_COMPOSE_GROUP = 44, // 伙伴组合
	EXP_ACHIEVE_STATECOND_GUARD_PHASE = 45, // 守护灵进阶
	EXP_ACHIEVE_STATECOND_GUARD_SLOT = 46, // 守护灵上阵
	EXP_ACHIEVE_STATECOND_GUARD_TRAIN_MAX = 47, // 守护灵培养到满
	EXP_ACHIEVE_STATECOND_SURFACE_FASHION = 48, // 改装部位
	EXP_ACHIEVE_STATECOND_UPGRADE_SURFACE = 49, // 坐骑升级
	EXP_ACHIEVE_STATECOND_KOTODAMA_LEARN = 50, // 言灵学习
	EXP_ACHIEVE_STATECOND_KOTODAMA_LEVELUP = 51, // 言灵升级
	EXP_ACHIEVE_STATECOND_PRACTICE_LEVELUP = 52, // 修炼等级
	EXP_ACHIEVE_STATECOND_SURFACE_COLOR = 53, // 坐骑解锁色块
	EXP_ACHIEVE_STATECOND_SECT = 54, // 新师徒系统
	EXP_ACHIEVE_STATECOND_ACTIVE_LONGYU = 55, // 激活龙语
	EXP_ACHIEVE_STATECOND_ENHANCE_LEVEL = 56, // 赋能最高等级
	EXP_ACHIEVE_STATECOND_GUARD_STAR_SLOT_LEVEL = 57, // 守护灵星阵位点等级
	EXP_ACHIEVE_STATECOND_CHILD_COUNT = 58, // 拥有等阶继承者数量
	EXP_ACHIEVE_STATECOND_DESTINY_CHILD_COUNT = 59, // 拥有天命继承者数量
	EXP_ACHIEVE_STATECOND_DEVICE_CHILD_COUNT = 60, // 拥有共鸣水晶继承者数量
	EXP_ACHIEVE_STATECOND_SKILL_CHILD_COUNT = 61, // 拥有技能继承者数量
	EXP_ACHIEVE_STATECOND_TALENT_CHILD_COUNT = 62, // 拥有专长继承者数量
	EXP_ACHIEVE_STATECOND_EQUIP_CHILD_COUNT = 63, // 拥有装备继承者数量
	EXP_ACHIEVE_STATECOND_BREED_PROFICIENCY_LEVEL = 64, // 育宠达人等级达到
	EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SWORD_LEVEL_AVERAGE = 65, // 龙威平均等级达到
	EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SACRIFICE_QUALITY_AVERAGE = 66, // 罪责平均品质达到
	EXP_ACHIEVE_STATECOND_DRAGONBORN_EVO_BREAK_COST = 67, // 上阵龙裔进化突破消耗物品达到
	EXP_ACHIEVE_STATECOND_DRAGONBORN_APTITUDE = 68, // 上阵龙裔平均资质达到
	EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_ANY_SUIT = 69, // 炼金激活任意一个套装
	EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT = 70, // 炼金激活指定套装
	EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT_LEVEL = 71, // 炼金任意套装升到n级
	EXP_ACHIEVE_STATECOND_ALCHEMY_REFRESH_SLOT = 72, // 炼金总计n个槽位洗练出x以上的数值
	EXP_ACHIEVE_STATECOND_ALCHEMY_LEVEL = 73, // 指定龙纹等级达到n级
	EXP_ACHIEVE_STATECOND_RUNE_STONE_GET = 74, // 获得n个X品质符石
	EXP_ACHIEVE_STATECOND_RUNE_STONE_ARRAY = 75, // 指定遗阵升级到N级
	EXP_ACHIEVE_STATECOND_HOLY_GHOST_AMITY = 76, // 英灵X好友度达到n
	EXP_ACHIEVE_STATECOND_HOLY_GHOST_LEVEL = 77, // 英灵X等级到达N级
	EXP_ACHIEVE_STATECOND_HOLY_GHOST_UNLOCK_COUNT = 78, // 解锁英灵N个
	EXP_ACHIEVE_STATECOND_HOLY_GHOST_TREE_HEIGHT = 79, // 灵树高度到达n米
	EXP_ACHIEVE_STATECOND_PARTNER_UNLOCK = 80, // 伙伴解锁状态
	EXP_ACHIEVE_STATECOND_PARTNER_GROUP_UNLOCK = 81, // 伙伴全套解锁状态
	EXP_ACHIEVE_STATECOND_COUNT = 82, // 
	EXP_ACHIEVE_STATECOND_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ACHIEVE_EVENT_CONDITION {
	EXP_ACHIEVE_EVENTCOND_NONE = 0, // 无条件
	EXP_ACHIEVE_EVENTCOND_GET_ITEM = 1, // 获取物品
	EXP_ACHIEVE_EVENTCOND_FINISH_CANREDO_TASK = 2, // 完成重复任务
	EXP_ACHIEVE_EVENTCOND_KILL_MONSTER = 3, // 杀怪
	EXP_ACHIEVE_EVENTCOND_USE_ITEM = 4, // 使用物品
	EXP_ACHIEVE_EVENTCOND_SPEND_MONEY = 5, // 消费货币
	EXP_ACHIEVE_EVENTCOND_EQUIP_FORCE = 6, // 装备锻造
	EXP_ACHIEVE_EVENTCOND_EQUIP_INC_STAR = 7, // 装备炼星
	EXP_ACHIEVE_EVENTCOND_EQUIP_INHERIT = 8, // 装备炼星继承
	EXP_ACHIEVE_EVENTCOND_EQUIP_ATTACH = 9, // 装备镶嵌
	EXP_ACHIEVE_EVENTCOND_EQUIP_REFINE = 10, // 装备洗练
	EXP_ACHIEVE_EVENTCOND_ITEM_MERGE = 11, // 合成物品
	EXP_ACHIEVE_EVENTCOND_NATION_WAR_KILL = 12, // 国战杀人
	EXP_ACHIEVE_EVENTCOND_NATION_WAR_WIN = 13, // 国战胜利
	EXP_ACHIEVE_EVENTCOND_NATION_WAR_REVIVE = 14, // 国战复活
	EXP_ACHIEVE_EVENTCOND_TALENT_UPGRADE = 15, // 天赋升级
	EXP_ACHIEVE_EVENTCOND_ATTEND_NATION_WAR = 16, // 参加国战
	EXP_ACHIEVE_EVENTCOND_ENTER_INSTANCE = 17, // 进入副本
	EXP_ACHIEVE_EVENTCOND_FINISH_TASK = 18, // 完成任务
	EXP_ACHIEVE_EVENTCOND_GET_RETINUE = 19, // 获得随从
	EXP_ACHIEVE_EVENTCOND_ACTIVE_FORMATION = 20, // 激活阵法
	EXP_ACHIEVE_EVENTCOND_GET_GRANT_REWARD = 21, // 获得奖励
	EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION = 22, // 解锁时装
	EXP_ACHIEVE_EVENTCOND_INVOKE_WING_SOUL = 23, // 飞剑铸灵
	EXP_ACHIEVE_EVENTCOND_EQUIP_AFFIX_TRANSFER = 24, // 词缀转移
	EXP_ACHIEVE_EVENTCOND_RETINUE_GIFT = 25, // 伙伴赠送礼物
	EXP_ACHIEVE_EVENTCOND_RETINUE_FINISH_TASK = 26, // 伙伴完成悬赏任务
	EXP_ACHIEVE_EVENTCOND_GUARD_LEARN_SKILL = 27, // 守护灵学习技能
	EXP_ACHIEVE_EVENTCOND_GUARD_TRAIN = 28, // 守护灵培养
	EXP_ACHIEVE_EVENTCOND_FRIEND_BLESS = 29, // 好友送花票
	EXP_ACHIEVE_EVENTCOND_SURFACE_COLOR = 30, // 坐骑解锁色块
	EXP_ACHIEVE_EVENTCOND_LONGYU = 31, // 解锁龙语
	EXP_ACHIEVE_EVENTCOND_CAST_SKILL = 32, // 释放技能
	EXP_ACHIEVE_EVENTCOND_XYXW = 33, // 完成双人共乘
	EXP_ACHIEVE_EVENTCOND_UPGRADE_SURFACE = 34, // 座驾升级
	EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGYU = 35, // 激活龙语
	EXP_ACHIEVE_EVENTCOND_EQUIP_CHAIJIE = 36, // 装备拆解
	EXP_ACHIEVE_EVENTCOND_CAR_RACE_PVP_RANK_TIMES = 37, // 获得赛车PVP指定名次的次数
	EXP_ACHIEVE_EVENTCOND_ENHANCE_COUNT = 38, // 赋能次数
	EXP_ACHIEVE_EVENTCOND_PRODUCE_SURFACE = 39, // 制作座驾
	EXP_ACHIEVE_EVENTCOND_ACTIVE_CAREER = 40, // 激活身份
	EXP_ACHIEVE_EVENTCOND_AUCTION_OPEN = 41, // 上架出售道具
	EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_GENE_EXPRESS = 42, // 育宠达人获取指定类型基因表达萌宠数量
	EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_HAIR_COLOUR_ID = 43, // 育宠达人获取指定萌宠毛色数量
	EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_NATURE_QUALITY_ID = 44, // 育宠达人获取指定萌宠性格数量
	EXP_ACHIEVE_EVENTCOND_ALCHEMY_REFRESH_SLOT_TIMES = 45, // 炼金槽位洗练n次
	EXP_ACHIEVE_EVENTCOND_ALCHEMY_GET_STONE = 46, // 炼金获取n个x级石头
	EXP_ACHIEVE_EVENTCOND_ALCHEMY_SLOT_LEVEL = 47, // 炼金n个槽位升级到x级
	EXP_ACHIEVE_EVENTCOND_ALCHEMY_RUNESTONE_LEVEL = 48, // 炼金n个符文石升级到x级
	EXP_ACHIEVE_EVENTCOND_SYSTEM_SCORE = 49, // 系统评分
	EXP_ACHIEVE_EVENTCOND_DRAGON_COCOON_REFRESH_SLOT = 50, // 龙茧槽位洗练等级
	EXP_ACHIEVE_EVENTCOND_DRAGON_COCOON_EQUIPED_LEVEL = 51, // 装备龙茧装备等级
	EXP_ACHIEVE_EVENTCOND_VEHICLE_LEVEL = 52, // 座驾解析
	EXP_ACHIEVE_EVENTCOND_VEHICLE_BREAK = 53, // 座驾重构
	EXP_ACHIEVE_EVENTCOND_VEHICLE_EFFECT = 54, // 座驾焕彩
	EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_ORANGE = 55, // 合成一个橙色龙语
	EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_GOLDEN = 56, // 合成一个金色龙语
	EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_FAILURE = 57, // 合成龙语失败一次
	EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_CONTINUOUS = 58, // 连续N次（成功/失败）合成金色龙语
	EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_TOTALL = 59, // 合成N次金色龙语
	EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGYU_NEW = 60, // 新龙语激活
	EXP_ACHIEVE_EVENTCOND_UPGRADE_LONGYU = 61, // 升级新龙语红（每个新的都对应一个）
	EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGHUI_NEW = 62, // 新龙辉激活
	EXP_ACHIEVE_EVENTCOND_UPGRADE_LONGHUI = 63, // 升级新龙辉红（每个新的都对应一个）
	EXP_ACHIEVE_EVENTCOND_ENHANCE_CONTINUOUS_RESULT = 64, // 连续N次赋能失败
	EXP_ACHIEVE_EVENTCOND_UNLOCK_RUNE = 65, // 新红色符咒解锁
	EXP_ACHIEVE_EVENTCOND_UPGRADE_RUNE = 66, // 新红色符咒达到N星
	EXP_ACHIEVE_EVENTCOND_UNLOCK_PARTNER = 67, // 新伙伴解锁
	EXP_ACHIEVE_EVENTCOND_UNLOCK_PARTNER_GROUP = 68, // 新伙伴全套解锁
	EXP_ACHIEVE_EVENTCOND_UNLOCK_PET = 69, // 新宠物解锁
	EXP_ACHIEVE_EVENTCOND_SMELT_HERES_SKILL = 70, // 熔炼获得N个红色继承者技能
	EXP_ACHIEVE_EVENTCOND_SMELT_PET_SKILL = 71, // 熔炼获得N个金色宠物技能
	EXP_ACHIEVE_EVENTCOND_SMELT_PET_CHIP = 72, // 熔炼获得N个红色宠物芯片
	EXP_ACHIEVE_EVENTCOND_UNLOCK_VEHICLE = 73, // 新座驾解锁
	EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION_NEW = 74, // 新时装解锁（衣服、头发、配饰、时装武器）
	EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION_SPECIAL = 75, // 环影每个加一个成就
	EXP_ACHIEVE_EVENTCOND_SEND_FLOWERS = 76, // 赠送花束次数N次
	EXP_ACHIEVE_EVENTCOND_GET_BOTTLE7 = 77, // 获得7种漂流瓶(按使用算)
	EXP_ACHIEVE_EVENTCOND_RELEASE_FISH = 78, // 放生鱼计次N次
	EXP_ACHIEVE_EVENTCOND_REPUTATION_REACH_TARGET = 79, // 声望达到X值
	EXP_ACHIEVE_EVENTCOND_FISH = 80, // 钓鱼成就
	EXP_ACHIEVE_EVENTCOND_COUNT = 81, // 
	EXP_ACHIEVE_EVENTCOND_FORCE_INT = 0x7fffffff, // 

};

enum EXP_PLANT_TYPE {
	EXPPLANTTYPE_NORMAL = 0, // 普通种植
	EXPPLANTTYPE_INTENSIVE = 1, // 精耕
	EXPPLANTTYPE_COUNT = 2, // 
	EXPPLANTTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SELL2SHOP_MODE {
	EXPSELL2SHOPMODE_ACCORDING_BINDPROP = 0, // 按绑定属性
	EXPSELL2SHOPMODE_MUSTBE_BOUNDMONEY = 1, // 必定绑定币
	EXPSELL2SHOPMODE_MUSTBE_TRADEMONEY = 2, // 必定交易币
	EXPSELL2SHOPMODE_MUSTBE_BOUNDCASH = 3, // 必定绑定元宝
	EXPSELL2SHOPMODE_BOUNDCASH_FRIST = 4, // 优先绑定元宝
	EXPSELL2SHOPMODE_COUNT = 5, // 
	EXPSELL2SHOPMODE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SHOPSELL_MODE {
	EXPSHOPSELLMODE_BOUNDMONEY_FIRST = 0, // 优先绑定币
	EXPSHOPSELLMODE_MUSTBE_BOUNDMONEY = 1, // 必定绑定币
	EXPSHOPSELLMODE_MUSTBE_TRADEMONEY = 2, // 必定交易币
	EXPSHOPSELLMODE_MUSTBE_BOUNDCASH = 3, // 必定绑定元宝
	EXPSHOPSELLMODE_BOUNDCASH_FRIST = 4, // 优先绑定元宝
	EXPSHOPSELLMODE_COUNT = 5, // 
	EXPSHOPSELLMODE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_NPCSELL_MODE {
	EXPNPCSELLMODE_BOUNDMONEY_FIRST = 0, // 优先绑定币
	EXPNPCSELLMODE_ONLY_TRADEMONEY = 1, // 仅交易币
	EXPNPCSELLMODE_BOTH_BOUND_TRADE = 2, // 二者均有
	EXPNPCSELLMODE_CUSTOM = 3, // 自定义出售服务显示名
	EXPNPCSELLMODE_COUNT = 4, // 
	EXPNPCSELLMODE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_NPCSELL_COST_TYPE {
	EXP_NPCSELL_COST_MONEY = 0, // 钱币
	EXP_NPCSELL_COST_REPUTATION = 1, // 声望
	EXP_NPCSELL_COST_ITEM = 2, // 物品

};

enum EXP_MONEY_TYPE {
	EXP_MONEY_TYPE_BIND = 0, // 绑定币
	EXP_MONEY_TYPE_TRADE = 1, // 交易币
	EXP_MONEY_TYPE_COUNT = 2, // 
	EXP_MONEY_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_KAIGUANG_STATE {
	EXPKAIGUANGST_NOTYET_CANNOT = 0, // 没开光且完全不可开光
	EXPKAIGUANGST_NOTYET_SEALED = 1, // 没开光且封印
	EXPKAIGUANGST_NOTYET_ANYTIME = 2, // 没开光但可随时开光
	EXPKAIGUANGST_YES = 3, // 已经开光
	EXPKAIGUANGST_COUNT = 4, // 
	EXPKAIGUANGST_FORCE_INT = 0x7fffffff, // 

};

enum EXP_EQUIPSTONE_SHAPE {
	EXPEQUIPSTONESHAPE_ROUND = 0, // 圆形
	EXPEQUIPSTONESHAPE_TRIANGLE = 1, // 三角形
	EXPEQUIPSTONESHAPE_SQUARE = 2, // 方形
	EXPEQUIPSTONESHAPE_HALFMOON = 3, // 半月形
	EXPEQUIPSTONESHAPE_COUNT = 4, // 
	EXPEQUIPSTONESHAPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ESTONE_TYPE {
	EXPESTONETYPE_EARTH = 0, // 地
	EXPESTONETYPE_FIRE = 1, // 火
	EXPESTONETYPE_WATER = 2, // 水
	EXPESTONETYPE_WIND = 3, // 风
	EXPESTONETYPE_SPIRIT = 4, // 精神
	EXPESTONETYPE_COUNT = 5, // 
	EXPESTONETYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_GENITEMCFG_TYPE {
	EXPGENITEMCFG_TYPE_EQUIP = 0, // 装备
	EXPGENITEMCFG_TYPE_OTHERS = 1, // 其他物品
	EXPGENITEMCFG_TYPE_COUNT = 2, // 
	EXPGENITEMCFG_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_GENITEMCFG_TIMELIMIT {
	EXPGENITEMCFG_TIMELIMIT_NONE = 0, // 无限制
	EXPGENITEMCFG_TIMELIMIT_SERVERTIME = 1, // 服务器时间
	EXPGENITEMCFG_TIMELIMIT_COUNT = 2, // 
	EXPGENITEMCFG_TIMELIMIT_FORCE_INT = 0x7fffffff, // 

};

enum EXP_GENITEMCFG_BINDSTATE {
	EXPGENITEMCFG_BINDSTATE_NONE = 0, // 不绑定
	EXPGENITEMCFG_BINDSTATE_BOUND = 1, // 已绑定
	EXPGENITEMCFG_BINDSTATE_AS_MONEYMATERIAL = 2, // 按货币/材料判断
	EXPGENITEMCFG_BINDSTATE_COUNT = 3, // 
	EXPGENITEMCFG_BINDSTATE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_BREED_FENCE_TYPE {
	EXPBREEDFENCE_1 = 0, // 口
	EXPBREEDFENCE_2 = 1, // ┗
	EXPBREEDFENCE_3 = 2, // 田
	EXPBREEDFENCE_4 = 3, // |___
	EXPBREEDFENCE_5 = 4, // -|_
	EXPBREEDFENCE_6 = 5, // ┻
	EXPBREEDFENCE_7 = 6, // ----
	EXPBREEDFENCE_8 = 7, // 十
	EXPBREEDFENCE_COUNT = 8, // 
	EXPBREEDFENCE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_APTITUDE_PREPROP {
	EXP_APTITUDEPREPROP_A = 0, // 资质前提二级属性A
	EXP_APTITUDEPREPROP_B = 1, // 资质前提二级属性B
	EXP_APTITUDEPREPROP_C = 2, // 资质前提二级属性C
	EXP_APTITUDEPREPROP_D = 3, // 资质前提二级属性D
	EXP_APTITUDEPREPROP_E = 4, // 资质前提二级属性E
	EXP_APTITUDEPREPROP_COUNT = 5, // 
	EXP_APTITUDEPREPROP_FORCE_INT = 0x7fffffff, // 

};

enum EXP_APTITUDE_TYPE {
	EXP_APTITUDETYPE_ADDON = 0, // 附加属性
	EXP_APTITUDETYPE_EQUIP = 1, // 装备专精
	EXP_APTITUDETYPE_POSITIVE = 2, // 被动引用
	EXP_APTITUDETYPE_COUNT = 3, // 
	EXP_APTITUDETYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_XIULIAN_CLASS {
	EXP_XIULIANCLASS_JINGU = 0, // 筋骨
	EXP_XIULIANCLASS_WUXUE = 1, // 武学
	EXP_XIULIANCLASS_SHENFA = 2, // 身法
	EXP_XIULIANCLASS_XINFA = 3, // 心法
	EXP_XIULIANCLASS_MIJI = 4, // 秘笈
	EXP_XIULIANCLASS_UNUSED_C = 5, // 待定
	EXP_XIULIANCLASS_UNUSED_B = 6, // 待定
	EXP_XIULIANCLASS_UNUSED_A = 7, // 待定
	EXP_XIULIANCLASS_COUNT = 8, // 
	EXP_XIULIANCLASS_FORCE_INT = 0x7fffffff, // 

};

enum EXP_XIULIAN_TYPE {
	EXP_XIULIANTYPE_ONLINE = 0, // 在线修炼
	EXP_XIULIANTYPE_OFFLINE = 1, // 离线修炼
	EXP_XIULIANTYPE_COUNT = 2, // 
	EXP_XIULIANTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_XIULIAN_CONFUMEFORCE_TYPE {
	EXP_XIULIAN_CONSUMEFORCE_ALWAYS = 0, // 必定消耗
	EXP_XIULIAN_CONSUMEFORCE_ONLY_SUCC = 1, // 修炼成功才消耗
	EXP_XIULIAN_CONSUMEFORCE_COUNT = 2, // 
	EXP_XIULIAN_CONSUMEFORCE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_REINFORCE_FAILRESULT {
	EXP_REINFORCE_FAILRESULT_NOTCHANGE = 0, // 不变
	EXP_REINFORCE_FAILRESULT_DECONE = 1, // 降1级
	EXP_REINFORCE_FAILRESULT_ZERO = 2, // 降到0
	EXP_REINFORCE_FAILRESULT_DISAPPEAR = 3, // 消失
	EXP_REINFORCE_FAILRESULT_COUNT = 4, // 
	EXP_REINFORCE_FAILRESULT_FORCE_INT = 0x7fffffff, // 

};

enum EXP_COMMUNITY_CLASS {
	EXP_COMMUNITY_CLASS_A = 0, // 豪华别墅
	EXP_COMMUNITY_CLASS_B = 1, // 贵族庄园
	EXP_COMMUNITY_CLASS_C = 2, // 永恒契约别墅
	EXP_COMMUNITY_CLASS_FORCE_INT = 0x7fffffff, // 

};

enum EXP_HOMESTYLE_EFFECT {
	EXP_HOMESTYLE_EFFECT_NONE = 0, // EXP_HOMESTYLE_EFFECT_NONE
	EXP_HOMESTYLE_EFFECT_WATERWHEEL = 1, // 水车
	EXP_HOMESTYLE_EFFECT_BIRDS = 2, // 飞鸟
	EXP_HOMESTYLE_EFFECT_PUMP = 3, // 抽水
	EXP_HOMESTYLE_EFFECT_FERTILIZER = 4, // 农家肥
	EXP_HOMESTYLE_EFFECT_DOG = 5, // 小狗
	EXP_HOMESTYLE_EFFECT_COUNT = 6, // 
	EXP_HOMESTYLE_EFFECT_FORCE_INT = 0x7fffffff, // 

};

enum EXP_HOMEBLD_PLACE_ENUM {
	EXPHOMEBLDPLACE_MAIN = 0, // 主屋处
	EXPHOMEBLDPLACE_INTERACT = 1, // 交互处
	EXPHOMEBLDPLACE_MARK = 2, // 标志处
	EXPHOMEBLDPLACE_GARDEN = 3, // 花园处
	EXPHOMEBLDPLACE_FENCE = 4, // 栅栏
	EXPHOMEBLDPLACE_EMPTY1 = 5, // 空地1
	EXPHOMEBLDPLACE_EMPTY2 = 6, // 空地2
	EXPHOMEBLDPLACE_EMPTY3 = 7, // 空地3
	EXPHOMEBLDPLACE_FARMING = 8, // 牧场处
	EXPHOMEBLDPLACE_FOUNDATION = 9, // 地基
	EXPHOMEBLDPLACE_COUNT = 10, // 
	EXPHOMEBLDPLACE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_HOMEBLD_PLACEMASK {
	EXPHOMEBLDPLACEMASK_NONE = 0, // EXPHOMEBLDPLACEMASK_NONE
	EXPHOMEBLDPLACEMASK_MAIN = 0x00000001, // 主屋处
	EXPHOMEBLDPLACEMASK_INTERACT = 0x00000002, // 交互处
	EXPHOMEBLDPLACEMASK_MARK = 0x00000004, // 标志处
	EXPHOMEBLDPLACEMASK_GARDEN = 0x00000008, // 花园处
	EXPHOMEBLDPLACEMASK_FENCE = 0x00000010, // 栅栏
	EXPHOMEBLDPLACEMASK_EMPTY1 = 0x00000020, // 空地1
	EXPHOMEBLDPLACEMASK_EMPTY2 = 0x00000040, // 空地2
	EXPHOMEBLDPLACEMASK_EMPTY3 = 0x00000080, // 空地3
	EXPHOMEBLDPLACEMASK_FARMING = 0x00000100, // 牧场处
	EXPHOMEBLDPLACEMASK_FOUNDATION = 0x00000200, // 地基
	EXPHOMEBLDPLACEMASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_FACTION_TYPE {
	EXP_FACTION_TYPE_ESCORT_AGENCY = 0, // 镖局
	EXP_FACTION_TYPE_CARAVAN = 1, // 马帮
	EXP_FACTION_TYPE_FORTRESS = 2, // 山寨
	EXP_FACTION_TYPE_WORKSHOP = 3, // 工坊
	EXP_FACTION_TYPE_COUNT = 4, // 
	EXP_FACTION_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_FACTIONBLD_PLACE_ENUM {
	EXP_FACTIONBLD_PLACE_FOUNDATION = 0, // 地基
	EXP_FACTIONBLD_PLACE_MAIN = 1, // 议事厅
	EXP_FACTIONBLD_PLACE_LIVING = 2, // 后勤
	EXP_FACTIONBLD_PLACE_TREASURER = 3, // 专有建筑1（原来的“账房”）
	EXP_FACTIONBLD_PLACE_SPECIAL1 = 4, // 特色建筑1
	EXP_FACTIONBLD_PLACE_WING = 5, // 厢房
	EXP_FACTIONBLD_PLACE_SACRIFICE = 6, // 藏宝阁（原来的祭祀）
	EXP_FACTIONBLD_PLACE_SQUARE = 7, // 广场
	EXP_FACTIONBLD_PLACE_SPECIAL2 = 8, // 特色建筑2
	EXP_FACTIONBLD_PLACE_SPECIAL3 = 9, // 特色建筑3
	EXP_FACTIONBLD_PLACE_SPECIAL4 = 10, // 特色建筑4
	EXP_FACTIONBLD_PLACE_SPECBUILDING2 = 11, // 专有建筑2
	EXP_FACTIONBLD_PLACE_COUNT = 12, // 
	EXP_FACTIONBLD_PLACE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_FACTION_LEVEL {
	EXP_FACTION_LEVEL_1 = 0, // EXP_FACTION_LEVEL_1
	EXP_FACTION_LEVEL_2 = 1, // EXP_FACTION_LEVEL_2
	EXP_FACTION_LEVEL_3 = 2, // EXP_FACTION_LEVEL_3
	EXP_FACTION_LEVEL_4 = 3, // EXP_FACTION_LEVEL_4
	EXP_FACTION_LEVEL_5 = 4, // EXP_FACTION_LEVEL_5
	EXP_FACTION_LEVEL_6 = 5, // EXP_FACTION_LEVEL_6
	EXP_FACTION_LEVEL_7 = 6, // EXP_FACTION_LEVEL_7
	EXP_FACTION_LEVEL_8 = 7, // EXP_FACTION_LEVEL_8
	EXP_FACTION_LEVEL_9 = 8, // EXP_FACTION_LEVEL_9
	EXP_FACTION_LEVEL_COUNT = 9, // 
	EXP_FACTION_LEVEL_FORCE_INT = 0x7fffffff, // 

};

enum EXP_WEDDING_CLASS_ENUM {
	EXPWEDDINGCLASS_NORMAL = 0, // 普通
	EXPWEDDINGCLASS_HOT = 1, // 火热
	EXPWEDDINGCLASS_LUXURIOUS = 2, // 奢华
	EXPWEDDINGCLASS_COUNT = 3, // 
	EXPWEDDINGCLASS_FORCE_INT = 0x7fffffff, // 

};

enum EXP_WULINLEVEL_CLASS {
	EXPWULINLEVEL_CLASS_1 = 0, // 级
	EXPWULINLEVEL_CLASS_2 = 1, // 段
	EXPWULINLEVEL_CLASS_3 = 2, // 位
	EXPWULINLEVEL_CLASS_COUNT = 3, // 
	EXPWULINLEVEL_CLASS_FORCE_INT = 0x7fffffff, // 

};

enum NPCSKILLSEQ_SERVTYPE {
	NPCSKILLSEQ_SERVTYPE_NOLIMIT = 0, // 无限制
	NPCSKILLSEQ_SERVTYPE_ONLY_PREVIEW = 1, // 只预览
	NPCSKILLSEQ_SERVTYPE_COUNT = 2, // 
	NPCSKILLSEQ_SERVTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_QINGGONG_TYPE {
	EXPQINGGONGTYPE_INVALID = -1, // EXPQINGGONGTYPE_INVALID
	EXPQINGGONGTYPE_ACC_RUN = 0, // 加速跑
	EXPQINGGONGTYPE_NORMAL_START = 1, // EXPQINGGONGTYPE_NORMAL_START
	EXPQINGGONGTYPE_NORMAL_1 = 1, // 通用轻功1
	EXPQINGGONGTYPE_NORMAL_2 = 2, // 通用轻功2
	EXPQINGGONGTYPE_NORMAL_3 = 3, // 通用轻功3
	EXPQINGGONGTYPE_NORMAL_4 = 4, // 通用轻功4
	EXPQINGGONGTYPE_NORMAL_5 = 5, // 通用轻功5
	EXPQINGGONGTYPE_NORMAL_6 = 6, // 通用轻功6
	EXPQINGGONGTYPE_NORMAL_7 = 7, // 通用轻功7
	EXPQINGGONGTYPE_NORMAL_8 = 8, // 通用轻功8
	EXPQINGGONGTYPE_NORMAL_END = 8, // EXPQINGGONGTYPE_NORMAL_END
	EXPQINGGONGTYPE_ACC_RUN_ONMOUNT = 9, // 骑乘加速跑
	EXPQINGGONGTYPE_ACC_RUN_ONWATER = 10, // 水上漂（水上加速跑）
	EXPQINGGONGTYPE_COUNT = 11, // 
	EXPQINGGONGTYPE_FORCE_INT = 0x7fffffff, // 

};

enum DirAct_Enum {
	DIRACT_FRONT = 0, // 前
	DIRACT_BACK = 1, // 后
	DIRACT_LEFT = 2, // 左
	DIRACT_RIGHT = 3, // 右
	DIRACT_COUNT = 4, // 
	DIRACT_FORCE_INT = 0x7fffffff, // 

};

enum EXP_PRODUCE_TYPE {
	EXPPRODUCETYPE_METALSTONE = 0, // 金石制造
	EXPPRODUCETYPE_WOODLEATHER = 1, // 木革制造
	EXPPRODUCETYPE_CLOTHSILK = 2, // 布帛制造
	EXPPRODUCETYPE_PHARMACY = 3, // 制药制造
	EXPPRODUCETYPE_COOKING = 4, // 烹饪制造
	EXPPRODUCETYPE_MINE = 5, // 采矿
	EXPPRODUCETYPE_LUMBERING = 6, // 伐木
	EXPPRODUCETYPE_FISHING = 7, // 钓鱼
	EXPPRODUCETYPE_PLANT = 8, // 种植
	EXPPRODUCETYPE_BREED = 9, // 养殖
	EXPPRODUCETYPE_COUNT = 10, // 
	EXPPRODUCETYPE_COUNT_FOR_POINT = 3, // 
	EXPPRODUCETYPE_FORCE_INT = 0x7fffffff, // 

};

enum HIRE_INFO_TYPE {
	HIT_ITEM_PRODUCE = 0, // 生产物品招工信息
	HIT_BLD_UPDATE = 1, // 建筑升级招工信息

};

enum EXP_LIFEPROF_TYPE {
	EXPLIFEPROFTYPE_HEAL = 0, // 治疗
	EXPLIFEPROFTYPE_SOCIAL = 1, // 社交
	EXPLIFEPROFTYPE_HANDWORK = 2, // 手工
	EXPLIFEPROFTYPE_FELLOW = 3, // 门徒
	EXPLIFEPROFTYPE_WARRIOR = 4, // 武者
	EXPLIFEPROFTYPE_HOME = 5, // 家园
	EXPLIFEPROFTYPE_COUNT = 6, // 
	EXPLIFEPROFTYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_LIFEPROF_COLOR_TYPE {
	EXP_LIFEPROFCOL_WHITE = 0, // 白色签名
	EXP_LIFEPROFCOL_BLUE = 1, // 蓝色签名
	EXP_LIFEPROFCOL_PURPLE = 2, // 紫色签名
	EXP_LIFEPROFCOL_ORANGE = 3, // 橙色签名
	EXP_LIFEPROFCOL_COUNT = 4, // 
	EXP_LIFEPROFCOL_FORCE_INT = 0x7fffffff, // 

};

enum EXP_NPCBUYSEV_PRIZETYPE {
	EXP_NPCBUYSERV_PRIZE_TASK = 0, // 任务
	EXP_NPCBUYSERV_PRIZE_SCENEPARAM = 1, // 场景参数
	EXP_NPCBUYSERV_PRIZE_NPCPARAM = 2, // NPC参数
	EXP_NPCBUYSERV_PRIZE_REPU = 3, // 声望
	EXP_NPCBUYSERV_PRIZE_COUNT = 4, // 
	EXP_NPCBUYSERV_PRIZE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_WUSHU_SEQ_SHOW_TYPE {
	EXP_WUSHU_SEQ_SHOW_ALWAYS = 0, // 一直显示
	EXP_WUSHU_SEQ_SHOW_LEARNED = 1, // 学会才显示
	EXP_WUSHU_SEQ_SHOW_HIDE = 2, // 隐藏
	EXP_WUSHU_SEQ_SHOW_ACTIVATED = 3, // 激活才显示
	EXP_WUSHU_SEQ_SHOW_COUNT = 4, // 
	EXP_WUSHU_SEQ_SHOW_FORCE_INT = 0x7fffffff, // 

};

enum EXP_WUSHU_SEQ_TYPE {
	EXP_WUSHU_SEQ_TYPE_USELESS = 0, // EXP_WUSHU_SEQ_TYPE_USELESS
	EXP_WUSHU_SEQ_TYPE_NO_COMBINE = 1, // EXP_WUSHU_SEQ_TYPE_NO_COMBINE
	EXP_WUSHU_SEQ_TYPE_COMBINE = 2, // EXP_WUSHU_SEQ_TYPE_COMBINE
	EXP_WUSHU_SEQ_TYPE_INT = 0x7fffffff, // 

};

enum EXP_CMN_USELIMIT_TYPE {
	EXP_CMN_USELIMIT_PER_DAY = 0, // 每日
	EXP_CMN_USELIMIT_PER_WEEK = 1, // 每周
	EXP_CMN_USELIMIT_PER_MONTH = 2, // 每月
	EXP_CMN_USELIMIT_PERMANENT = 3, // 永久
	EXP_CMN_USELIMIT_PER_WEEK_HOLD_ONE_ON_WEEKEND = 4, // 每周，保留一次在周末
	EXP_CMN_USELIMIT_COUNT = 5, // 
	EXP_CMN_USELIMIT_FORCE_INT = 0x7fffffff, // 

};

enum EXP_MON_REPU_EFFECT_TYPE {
	EXP_MONREPU_EFFECT_LOOT_PRIVILEGE = 0, // 获得怪物LOOT权限
	EXP_MONREPU_EFFECT_RANGE = 1, // 指定范围
	EXP_MONREPU_EFFECT_COUNT = 2, // 
	EXP_MONREPU_EFFECT_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SCENE_SPECITEM_USE_TYPE {
	EXP_SCENESPECITEM_USETYPE_NONE = 0, // 无法使用
	EXP_SCENESPECITEM_USETYPE_MEDICINE = 1, // 药
	EXP_SCENESPECITEM_USETYPE_SKILL = 2, // 技能物品
	EXP_SCENESPECITEM_USETYPE_COUNT = 3, // 
	EXP_SCENESPECITEM_USETYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_PROF_SUTRACOMBINE_TYPE {
	EXP_PROF_SUTRACOMB_DEFAULT = 0, // 默认
	EXP_PROF_SUTRACOMB_ADVANCED_1 = 1, // 高级1
	EXP_PROF_SUTRACOMB_ADVANCED_2 = 2, // 高级2
	EXP_PROF_SUTRACOMB_ADVANCED_3 = 3, // 高级3
	EXP_PROF_SUTRACOMB_ADVANCED_4 = 4, // 高级4
	EXP_PROF_SUTRACOMB_COUNT = 5, // 
	EXP_PROF_SUTRACOMB_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SKILLSEQ_SUTRA_ACTIVATE_TYPE {
	EXP_SKILLSEQ_SUTRA_ACTIV_NOLIMIT = 0, // 不限
	EXP_SKILLSEQ_SUTRA_ACTIV_TYPE1 = 1, // 类型1
	EXP_SKILLSEQ_SUTRA_ACTIV_TYPE2 = 2, // 类型2
	EXP_SKILLSEQ_SUTRA_ACTIV_TYPE3 = 3, // 类型3
	EXP_SKILLSEQ_SUTRA_ACTIV_TYPE4 = 4, // 类型4
	EXP_SKILLSEQ_SUTRA_ACTIV_COUNT = 5, // 
	EXP_SKILLSEQ_SUTRA_ACTIV_FORCE_INT = 0x7fffffff, // 

};

enum EXP_TREASUREMAP_TYPE {
	EXP_TREASUREMAP_TYPE_LEVEL1 = 0, // 1级藏宝图
	EXP_TREASUREMAP_TYPE_LEVEL2 = 1, // 2级藏宝图
	EXP_TREASUREMAP_TYPE_LEVEL3 = 2, // 3级藏宝图
	EXP_TREASUREMAP_TYPE_COUNT = 3, // 
	EXP_TREASUREMAP_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_FACTION_IDENTITY_ENUM {
	EXP_FACTION_IDENTITY_BANGZHU = 0, // 帮主
	EXP_FACTION_IDENTITY_VICE_BANGZHU = 1, // 副帮主
	EXP_FACTION_IDENTITY_ZHANGLAO = 2, // 长老
	EXP_FACTION_IDENTITY_HUFA = 3, // 护法
	EXP_FACTION_IDENTITY_MENGZHU = 4, // 盟主
	EXP_FACTION_IDENTITY_FORCE_INT = 0x7fffffff, // 

};

enum EXP_PLAYERDIY_ACTIVITY_TYPE {
	EXP_PLAYERDIY_ACTIVITY_TYPE_FACTION = 0, // 帮派普通活动
	EXP_PLAYERDIY_ACTIVITY_TYPE_ALLIANCE = 1, // 盟主
	EXP_PLAYERDIY_ACTIVITY_TYPE_PARTY = 2, // 宴会活动
	EXP_PLAYERDIY_ACTIVITY_TYPE_COUNT = 3, // 
	EXP_PLAYERDIY_ACTIVITY_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum NPC_GROUP_TYPE {
	NPCGROUPTYPE_NONE = 0, // NPCGROUPTYPE_NONE
	NPCGROUPTYPE_CONVOY = 1, // 护送
	NPCGROUPTYPE_ESCORT = 2, // 押镖
	NPCGROUPTYPE_BECKON = 3, // 召唤
	NPCGROUPTYPE_COUNT = 4, // 

};

enum EXP_SERVANT_TYPE {
	EXP_SERVANT_TYPE_LONGTERM = 0, // 长工
	EXP_SERVANT_TYPE_BOOKCHILD = 1, // 书童
	EXP_SERVANT_TYPE_WAITER = 2, // 侍应
	EXP_SERVANT_TYPE_MISTER = 3, // 先生
	EXP_SERVANT_TYPE_COUNT = 4, // 
	EXP_SERVANT_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum EXP_SERVANT_TALENT_TYPE {
	EXP_SERVANT_TALENT_NONE = 0, // 无
	EXP_SERVANT_TALENT_LEAD = 1, // 统率
	EXP_SERVANT_TALENT_TEA = 2, // 茶道
	EXP_SERVANT_TALENT_MUSIC = 3, // 音律
	EXP_SERVANT_TALENT_BEAUTY = 4, // 美貌
	EXP_SERVANT_TALENT_MASSAGE = 5, // 推拿
	EXP_SERVANT_TALENT_MEDICAL = 6, // 医理
	EXP_SERVANT_TALENT_POETRY = 7, // 诗歌
	EXP_SERVANT_TALENT_PAINTING = 8, // 国画
	EXP_SERVANT_TALENT_DANCE = 9, // 歌舞
	EXP_SERVANT_TALENT_COOKING = 10, // 厨艺
	EXP_SERVANT_TALENT_NEGOTIATE = 11, // 谈判
	EXP_SERVANT_TALENT_BUSINESS = 12, // 经营
	EXP_SERVANT_TALENT_FLOWER = 13, // 插花
	EXP_SERVANT_TALENT_CHESS = 14, // 棋艺
	EXP_SERVANT_TALENT_DIVINE = 15, // 观星
	EXP_SERVANT_TALENT_COUNT = 16, // 
	EXP_SERVANT_TALENT_FORCE_INT = 0x7fffffff, // 

};

enum INSTANCE_RETURN_OP {
	IRO_CANCEL = 0, // 删除进入信息，关闭重回面板
	IRO_ENTER = 1, // 进入当前副本

};

enum END_ACTIVITY_TYPE {
	END_ACTIVITY_BY_CONTROLLOR = 0, // 控制器
	END_ACTIVITY_BY_TASK = 1, // 任务完成
	END_ACTIVITY_BY_INSTSCORE = 2, // 副本打分
	END_ACTIVITY_BY_TIMEOUT = 3, // 超时
	END_ACTIVITY_COUNT = 4, // 
	END_ACTIVITY_FORCE_INT = 0x7fffffff, // 

};

enum LOGIC_OPERATION_TYPE {
	LOGIC_OP_OR = 0, // LOGIC_OP_OR
	LOGIC_OP_AND = 1, // LOGIC_OP_AND
	LOGIC_OP_NUM = 2, // LOGIC_OP_NUM
	LOGIC_OP_FORCE_INT = 0x7fffffff, // 

};

enum DARTCAR_TEAM_SHAPE {
	DARTCAR_TEAM_SHAPE_DEFAULT = 0, // DARTCAR_TEAM_SHAPE_DEFAULT
	DARTCAR_TEAM_SHAPE_INT = 0x7fffffff, // 

};

enum GATHER_INFO_TYPE {
	GATHER_INFO_SPY = 0, // 打探
	GATHER_INFO_TORTURE = 1, // 刑讯
	GATHER_INFO_STORY = 2, // 剧情
	GATHER_INFO_DETECTION = 3, // 侦破
	GATHER_INFO_DINNER = 4, // 宴席
	GATHER_INFO_INT = 0x7fffffff, // 

};

enum PERIOD_TYPE {
	PERIOD_DAY = 0, // 每天
	PERIOD_WEEK = 1, // 每周
	PERIOD_TYPE_INT = 0x7fffffff, // 

};

enum INFO_FRAGMENT_TYPE {
	INFO_FRAGMENT_CORE = 0, // 核心消息
	INFO_FRAGMENT_GRAPEVINE = 1, // 小道消息
	INFO_FRAGMENT_INT = 0x7fffffff, // 

};

enum INSTANCE_PHASE_TYPE {
	INSTANCE_PHASE_NORMAL = 0, // 普通副本
	INSTANCE_PHASE_PHASE = 1, // 相位副本
	INSTANCE_PHASE_INT = 0x7fffffff, // 

};

enum AFFECT_SCENE_PARAM_TYPE {
	AFFECT_SCENE_PARAM_PLUS = 0, // ＋
	AFFECT_SCENE_PARAM_MINUS = 1, // -
	AFFECT_SCENE_PARAM_MULTIPLY = 2, // ×
	AFFECT_SCENE_PARAM_DIVIDE = 3, // ÷
	AFFECT_SCENE_PARAM_CUSTOM = 4, // AFFECT_SCENE_PARAM_CUSTOM
	AFFECT_SCENE_PARAM_INT = 0x7fffffff, // 

};

enum GRANT_REWARD_TYPE {
	GRANT_REWARD_TYPE_NO = 0, // 无
	GRANT_REWARD_TYPE_FACTION_DEL_DART = 1, // 帮派运镖
	GRANT_REWARD_TYPE_FACTION_ROB_DART = 2, // 帮派劫镖
	GRANT_REWARD_TYPE_FACTION_WORK = 3, // 帮派打工
	GRANT_REWARD_TYPE_FACTION_ACTIVITY = 4, // 帮派活动
	GRANT_REWARD_TYPE_ONLINE_REWARD = 5, // 在线奖励
	GRANT_REWARD_TYPE_UPGRADE_REWARD = 6, // 升级奖励
	GRANT_REWARD_TYPE_STAR_GIFT = 7, // 明星礼包
	GRANT_REWARD_TYPE_ARENA_RANK_REWARD = 8, // 竞技场排名发奖
	GRANT_REWARD_AUTO_REWARD = 9, // 自动礼包（签到，在线礼包，月卡礼包，等级礼包，VIP礼包）
	GRANT_REWARD_TYPE_ACHIEVEMENT = 10, // 成就奖励
	GRANT_REWARD_TYPE_NATION_WAR = 11, // 国战奖励
	GRANT_REWARD_TYPE_INSTANCE = 12, // 副本奖励
	GRANT_REWARD_TYPE_NATION_OFFICER_GIFT = 13, // 官员福利
	GRANT_REWARD_TYPE_PLANT = 14, // 种植奖励
	GRANT_REWARD_TYPE_EXTERIOR_DECOMPOSE = 15, // 兵甲分解
	GRANT_REWARD_TYPE_RETRIEVE = 16, // 找回奖励
	GRANT_REWARD_TYPE_RED_PACKET = 17, // 红包奖励
	GRANT_REWARD_TYPE_FACTION_DAILY = 18, // 帮派每日福利
	GRANT_REWARD_TYPE_MONTH_CARD = 19, // 月卡奖励
	GRANT_REWARD_TYPE_COMPENSATION = 20, // 补偿奖励
	GRANT_REWARD_TYPE_ARENA = 21, // 竞技场奖励
	GRANT_REWARD_TYPE_TASK = 22, // 任务奖励
	GRANT_REWARD_TYPE_CORPS_TOWER_BATTLE = 23, // 帮派爬塔
	GRANT_REWARD_TYPE_BIND_MOBILE_PHONE = 24, // 绑定手机
	GRANT_REWARD_TYPE_FARM = 25, // 家园农场种植
	GRANT_REWARD_TYPE_PARK = 26, // 乐园奖励
	GRANT_REWARD_TYPE_CROPS_RACE = 27, // 帮派赛跑奖励
	GRANT_REWARD_TYPE_CORPS_CENTER = 28, // 帮派跨服战
	GRANT_REWARD_TYPE_H5_GAME = 29, // H5游戏奖励
	GRANT_REWARD_TYPE_CONTEST_MARRIAGE = 30, // 比武招亲
	GRANT_REWARD_TYPE_SOCIAL_SPACE = 31, // 空间奖励
	GRANT_REWARD_TYPE_COUPLE_BATTLE_GUESS = 32, // 情侣赛竞猜
	GRANT_REWARD_TYPE_LOTTERY = 33, // 彩票奖励
	GRANT_REWARD_TYPE_DROP = 34, // 怪物掉落
	GRANT_REWARD_TYPE_DOLL = 35, // 抓娃娃机
	GRANT_REWARD_TYPE_FACTION_PROFIT = 36, // 社团工资
	GRANT_REWARD_TYPE_ADVENTURE_TASK = 37, // 奇遇任务排名奖励
	GRANT_REWARD_TYPE_ROLL = 38, // ROLL点奖励
	GRANT_REWARD_TYPE_CG_REWARD = 39, // 完整观看CG奖励
	GRANT_REWARD_TYPE_CAREER = 40, // 身份奖励
	GRANT_REWARD_TYPE_GOLDEN_INTIMATE_ANNIVERSARY = 41, // 羁绊纪念日奖励
	GRANT_REWARD_TYPE_SLOT_MACHINE = 42, // 拉霸机奖励
	GRANT_REWARD_TYPE_ADVENTURE = 43, // 大冒险奖励
	GRANT_REWARD_TYPE_EGG_MACHINE = 44, // 扭蛋机奖励
	GRANT_REWARD_TYPE_TEAM_RAND_LEVEL = 45, // 队伍随机等级奖励
	GRANT_REWARD_TYPE_FIRST_RECHARGE = 46, // 首充
	GRANT_REWARD_TYPE_NIGHT_REWARD = 47, // 晚间发奖
	GRANT_REWARD_TYPE_TSS_GIFTS = 48, // 腾讯礼包
	GRANT_REWARD_TYPE_SECT = 49, // 新师徒系统
	GRANT_REWARD_TYPE_SHARE_FRIEND = 50, // 邀请未注册好友成功次数奖励
	GRANT_REWARD_TYPE_SHARE_COMMON = 51, // 分享成功次数奖励
	GRANT_REWARD_TYPE_MINIGAME_SCULPTURE = 52, // 雕塑小游戏
	GRANT_REWARD_TYPE_THANKS_GIVING = 53, // 温情计划
	GRANT_REWARD_TYPE_HOMETOWN_CLEANING = 54, // 家园清洁
	GRANT_REWARD_TYPE_HOMETOWN_PLANT = 55, // 家园种植
	GRANT_REWARD_TYPE_SHOP_PARTNER = 56, // 小店合伙人
	GRANT_REWARD_TYPE_OVERCOOK = 57, // 异界厨房
	GRANT_REWARD_TYPE_CHILD_TOUR = 58, // 继承者旅行
	GRANT_REWARD_TYPE_ELIMINATE_GUESS = 59, // 淘汰塞竞猜
	GRANT_REWARD_TYPE_MINIGAME_MORA = 60, // 小游戏猜拳
	GRANT_REWARD_TYPE_PDD_TEAM = 61, // 拼团
	GRANT_REWARD_TYPE_PRAY = 62, // 祈愿
	GRANT_REWARD_TYPE_INT = 0x7fffffff, // 

};

enum GRANT_REWARD_ENUM {
	GRANT_REWARD_ENUM_EXP = 0, // 经验
	GRANT_REWARD_ENUM_BOUNDMONEY = 1, // 绑定币
	GRANT_REWARD_ENUM_TRADEMONEY = 2, // 交易币
	GRANT_REWARD_ENUM_REPU1 = 3, // 声望1
	GRANT_REWARD_ENUM_REPU2 = 4, // 声望2
	GRANT_REWARD_ENUM_REPU3 = 5, // 声望3
	GRANT_REWARD_ENUM_REPU4 = 6, // 声望4
	GRANT_REWARD_ENUM_REPU5 = 7, // 声望5
	GRANT_REWARD_ENUM_REPU6 = 8, // 声望6
	GRANT_REWARD_ENUM_REPU7 = 9, // 声望7
	GRANT_REWARD_ENUM_REPU8 = 10, // 声望8
	GRANT_REWARD_ENUM_FACTION_CONTRIBUTION_RESERVED = 11, // 帮贡
	GRANT_REWARD_ENUM_CONSTRUCTION_RESERVED = 12, // 建设度
	GRANT_REWARD_ENUM_BID_VALUE_RESERVED = 13, // 竞标值
	GRANT_REWARD_ENUM_FACTION_FUNDS_RESERVED = 14, // 帮派资金
	GRANT_REWARD_ENUM_ITEM1 = 15, // 物品1
	GRANT_REWARD_ENUM_ITEM2 = 16, // 物品2
	GRANT_REWARD_ENUM_ITEM3 = 17, // 物品3
	GRANT_REWARD_ENUM_ITEM4 = 18, // 物品4
	GRANT_REWARD_ENUM_ITEM5 = 19, // 物品5
	GRANT_REWARD_ENUM_ITEM6 = 20, // 物品6
	GRANT_REWARD_ENUM_ITEM7 = 21, // 物品7
	GRANT_REWARD_ENUM_ITEM8 = 22, // 物品8
	GRANT_REWARD_ENUM_FULIEXP_RESERVED = 23, // 福利经验
	GRANT_REWARD_ENUM_PROEXP = 24, // 基础功力
	GRANT_REWARD_ENUM_BOUND_YUANBO = 25, // 基础绑定元宝
	GRANT_REWARD_ENUM_COUNTER1 = 26, // 计数器1
	GRANT_REWARD_ENUM_COUNTER2 = 27, // 计数器2
	GRANT_REWARD_ENUM_COUNTER3 = 28, // 计数器3
	GRANT_REWARD_ENUM_COUNTER4 = 29, // 计数器4
	GRANT_REWARD_ENUM_COUNTER5 = 30, // 计数器5
	GRANT_REWARD_ENUM_SWITCH1 = 31, // 开关1
	GRANT_REWARD_ENUM_SWITCH2 = 32, // 开关2
	GRANT_REWARD_ENUM_SWITCH3 = 33, // 开关3
	GRANT_REWARD_ENUM_SWITCH4 = 34, // 开关4
	GRANT_REWARD_ENUM_SWITCH5 = 35, // 开关5
	GRANT_REWARD_ENUM_YUANBAO = 36, // 元宝
	GRANT_REWARD_ENUM_RUNE_NEW = 37, // 新符文
	GRANT_REWARD_MASK_VALID_COUNT = 38, // 

};

enum GRAFFITI_IDEN_ENUM {
	GRAFFITI_ID_TIGUAN_ORGANIZER = 0, // 踢馆发起者
	GRAFFITI_ID_FACTION_MASTER = 1, // 帮主
	GRAFFITI_ID_FACTION_MASTER_SPOUSE = 2, // 帮主配偶
	GRAFFITI_ID_FACTION_VICEMASTER = 3, // 副帮主
	GRAFFITI_ID_FACTION_VICEMASTER_SPOUSE = 4, // 副帮主配偶、
	GRAFFITI_ID_FACTION_HUFA = 5, // 护法
	GRAFFITI_ID_FACTION_ZHANGLAO = 6, // 长老
	GRAFFITI_ID_RESERVED1 = 7, // GRAFFITI_ID_RESERVED1
	GRAFFITI_ID_RESERVED2 = 8, // GRAFFITI_ID_RESERVED2
	GRAFFITI_ID_RESERVED3 = 9, // GRAFFITI_ID_RESERVED3
	GRAFFITI_ID_RESERVED4 = 10, // GRAFFITI_ID_RESERVED4
	GRAFFITI_ID_RESERVED5 = 11, // GRAFFITI_ID_RESERVED5
	GRAFFITI_ID_NUM = 12, // 

};

enum GS_ERRMSG_TYPE {
	GS_ERRMSGTYPE_NORMAL = 0, // GS_ERRMSGTYPE_NORMAL
	GS_ERRMSGTYPE_GATHER = 1, // 挖矿出错
	GS_ERRMSGTYPE_USEITEM = 2, // 使用物品出错
	GS_ERRMSGTYPE_TEAMFOLLOW = 3, // 组队跟随出错
	GS_ERRMSGTYPE_PRODUCE = 4, // 制造出错

};

enum SHOWERRMSG_TYPE {
	SHOWERRMSG_MSGBOX = 0, // 弹框
	SHOWERRMSG_CHAT = 1, // 聊天栏

};

enum BATTLEFIELD_DEATH_PROC_TYPE {
	BATTLEFIELD_DEATH_PROC_NORMAL = 0, // 正常死亡复活处理
	BATTLEFIELD_DEATH_PROC_REVIVE_BY_TIME = 1, // 定时复活
	BATTLEFIELD_DEATH_PROC_1V1ARENA = 2, // 1V1擂台死亡处理
	BATTLEFIELD_DEATH_PROC_REVIVE_COIN = 3, // 复活币
	BATTLEFIELD_DEATH_PROC_5V5TYPE = 4, // 5V5战场死亡处理
	BATTLEFIELD_DEATH_PROC_TEAM = 5, // 组队副本模式
	BATTLEFIELD_DEATH_PROC_MELEE = 6, // 混战副本模式
	BATTLEFIELD_DEATH_PROC_END_INSTANCE = 7, // 死亡结束副本模式
	BATTLEFIELD_DEATH_PROC_ARENA = 8, // 竞技模模式
	BATTLEFIELD_DEATH_PROC_NUM = 9, // 

};

enum TRAP_TYPE {
	TRAP_TYPE_TRAP = 0, // 陷阱
	TRAP_TYPE_ADVENTURE1 = 1, // 奇遇1
	TRAP_TYPE_ADVENTURE2 = 2, // 奇遇2
	TRAP_TYPE_ADVENTURE3 = 3, // 奇遇3
	TRAP_TYPE_NUM = 4, // 
	TRAP_TYPE_INT = 0x7fffffff, // 

};

enum DART_CAR_TRAPPED_ACTION {
	DART_CAR_TRAP_0 = 0, // （地藏井）
	DART_CAR_TRAP_1 = 1, // （碎辘刺）
	DART_CAR_TRAP_2 = 2, // （铁荆棘）
	DART_CAR_TRAP_ACTION_NUM = 3, // 
	DART_CAR_TRAP_ACTION_INT = 0x7fffffff, // 

};

enum POISON_TYPE {
	POISON_TYPE_POISON1 = 0, // POISON_TYPE_POISON1
	POISON_TYPE_POISON2 = 1, // POISON_TYPE_POISON2
	POISON_TYPE_POISON3 = 2, // POISON_TYPE_POISON3
	POISON_TYPE_POISON4 = 3, // POISON_TYPE_POISON4
	POISON_TYPE_POISON5 = 4, // POISON_TYPE_POISON5
	POISON_TYPE_POISON6 = 5, // POISON_TYPE_POISON6
	POISON_TYPE_NUM = 6, // 
	POISON_TYPE_INT = 0x7fffffff, // 

};

enum DEL_ROB_DART_TYPE {
	DEL_ROB_DART_ANTIDOTE = 0, // 解药
	DEL_ROB_DART_DETECTOR = 1, // 探查器
	DEL_ROB_DART_REMOVE_TRAP = 2, // 解除陷阱道具
	DEL_ROB_DART_SET_LVL = 3, // 指定镖车等级道具
	DEL_ROB_DART_ADD_DARTS_NUM = 4, // 增加镖货数量道具
	DEL_ROB_DART_TYPE_NUM = 5, // 
	DEL_ROB_DART_TYPE_INT = 0x7fffffff, // 

};

enum DART_CAR_TRACE_TYPE {
	DART_CAR_TRACE_NOTIFY_MASK = 0x00000010, // 通知类掩码
	DART_CAR_TRACE_NOTIFY_BEGIN = 0x00000011, // 运镖镖车生成
	DART_CAR_TRACE_NOTIFY_ROB_BEGIN = 0x00000012, // 劫镖镖车生成
	DART_CAR_TRACE_NOTIFY_CHANGE_SCENE = 0x00000013, // 镖车换场景
	DART_CAR_TRACE_NOTIFY_LOGIN = 0x00000014, // 运镖玩家重上线
	DART_CAR_TRACE_NOTIFY_ROB_LOGIN = 0x00000015, // 劫镖玩家重上线
	DART_CAR_TRACE_NOTIFY_ALERT_BEGIN = 0x00000016, // 运镖脱离警告开始(60秒)
	DART_CAR_TRACE_NOTIFY_ALERT_END = 0x00000017, // 运镖脱离警告结束
	DART_CAR_TRACE_NOTIFY_10_MIN_LEFT = 0x00000018, // 总时间还剩10分钟
	DART_CAR_TRACE_NOTIFY_REWARD_ABSENCE = 0x00000019, // 运镖领奖时有人不在场
	DART_CAR_TRACE_NOTIFY_TIMES_FULL = 0x0000001a, // 有人次数已满
	DART_CAR_TRACE_NOTIFY_START_FAR_AWAY = 0x0000001b, // 开启时有人距离过远
	DART_CAR_TRACE_NOTIFY_GOODS_COUNT = 0x0000001c, // 剩余镖货数量
	DART_CAR_TRACE_NOTIFY_WAIT_FINISH = 0x0000001d, // 镖车到达目的地
	DART_CAR_TRACE_SUCCESS_MASK = 0x00000020, // 成功类掩码
	DART_CAR_TRACE_SUCCESS_AWARD = 0x00000021, // 成功-玩家已领取奖励
	DART_CAR_TRACE_SUCCESS_ROB_ALL = 0x00000022, // 成功-镖车被抢完(劫镖专用)
	DART_CAR_TRACE_FAIL_MASK = 0x00000040, // 失败类掩码
	DART_CAR_TRACE_FAIL_TEAM_LEAVE = 0x00000041, // 失败-自身离队
	DART_CAR_TRACE_FAIL_TEAM_DISBAND = 0x00000042, // 失败-队伍解散
	DART_CAR_TRACE_FAIL_ARRIVE = 0x00000043, // 失败-镖车已达目的地(劫镖专用)
	DART_CAR_TRACE_FAIL_ROB_ALL = 0x00000044, // 失败-镖车被抢完(运镖专用)
	DART_CAR_TRACE_FAIL_TOTAL_TIMEOUT = 0x00000045, // 失败-镖车总时间超时
	DART_CAR_TRACE_FAIL_ALONE_TIMEOUT = 0x00000046, // 失败-玩家脱离时间过长(运镖专用)
	DART_CAR_TRACE_FAIL_DISAPPEAR = 0x00000047, // 失败-其他原因导致镖车消失

};

enum TITLE_CLASS_LIMIT {
	TITLE_CLASS_LIMIT_PRINCE = 0, // 0(亲王爵位可用)
	TITLE_CLASS_LIMIT_BELOW_PRINCE = 1, // 1(亲王爵位以下可用)
	TITLE_CLASS_LIMIT_MARQUIS = 2, // 2(侯爵及以上爵位可用)
	TITLE_CLASS_LIMIT_PRINCE1 = 3, // 3(亲王1可用)
	TITLE_CLASS_LIMIT_PRINCE2 = 4, // 4(亲王2可用)
	TITLE_CLASS_LIMIT_INT = 0x7fffffff, // 

};

enum WOO_INDENTITY_LIMIT {
	WOO_IDENTITY_LIMIT_NONE = 0, // 无限制
	WOO_IDENTITY_LIMIT_TITLE = 1, // 爵位
	WOO_IDENTITY_LIMIT_FAMOUS = 2, // 名人
	WOO_IDENTITY_LIMIT_FORCE_INT = 0x7fffffff, // 

};

enum FAMOUS_MARK_TYPE {
	FAMOUS_MARK_TYPE_NONE = 0, // 无标示
	FAMOUS_MARK_TYPE_1ST_CHAPTER = 1, // 上卷明星
	FAMOUS_MARK_TYPE_2ST_CHAPTER = 2, // 中卷明星
	FAMOUS_MARK_TYPE_3ST_CHAPTER = 3, // 下卷明星
	FAMOUS_MARK_TYPE_FORCE_INT = 0x7fffffff, // 

};

enum TREASUREITEM_TYPE {
	TREASUREITEM_TYPE_WEAPON = 0, // 神兵
	TREASUREITEM_TYPE_ARMOR = 1, // 宝甲
	TREASUREITEM_TYPE_DART = 2, // 暗器
	TREASUREITEM_TYPE_POISON = 3, // 毒物
	TREASUREITEM_TYPE_BOOK_DODGE = 4, // 轻功秘籍
	TREASUREITEM_TYPE_BOOK_ATTACK = 5, // 内攻秘籍
	TREASUREITEM_TYPE_BOOK_DEFEND = 6, // 内防秘籍
	TREASUREITEM_TYPE_TOOL_PRACTICE = 7, // 练功器具
	TREASUREITEM_TYPE_DRAG = 8, // 药物
	TREASUREITEM_TYPE_INT = 0x7fffffff, // 

};

enum TITLE_SERV_TYPE {
	TITLE_SERV_TYPE_AUCTION = 0, // 开启拍卖
	TITLE_SERV_TYPE_DOUBLE = 1, // 开启双倍
	TITLE_SERV_TYPE_INT = 0x7fffffff, // 

};

enum CHUANGONG_FAIL_RESULT {
	CHUANGONE_FAIL_RESULT_KEEP = 0, // 不变
	CHUANGONE_FAIL_DEC_LEVEL = 1, // 降1级
	CHUANGONE_FAIL_DEC_TO_ZERO = 2, // 降到0级
	CHUANGONE_FAIL_DISAPPEAR = 3, // 消失
	CHUANGONE_FAIL_MAD = 4, // 走火入魔
	CHUANGONE_FAIL_RESULT_INT = 0x7fffffff, // 

};

enum SCENE_ITEM_TYPE {
	SCENE_ITEM_TYPE_IVTRITEM = 0, // SCENE_ITEM_TYPE_IVTRITEM
	SCENE_ITEM_TYPE_BOOK = 1, // SCENE_ITEM_TYPE_BOOK
	SCENE_ITEM_TYPE_INT = 0x7fffffff, // 

};

enum PET_APTITUDES_ENUM {
	PET_APTITUDES_PHYSIC = 0, // PET_APTITUDES_PHYSIC
	PET_APTITUDES_MAGIC = 1, // PET_APTITUDES_MAGIC
	PET_APTITUDES_DEFENCE = 2, // PET_APTITUDES_DEFENCE
	PET_APTITUDES_MEGAHP = 3, // PET_APTITUDES_MEGAHP
	PET_APTITUDES_INT = 0x7fffffff, // 

};

enum CLUE_TYPE_ENUM {
	CLUE_TYPE_MOTIVATION = 0, // 0(作案动机)、
	CLUE_TYPE_TOOL = 1, // 1(作案工具)、
	CLUE_TYPE_ANALYZE = 2, // 2(案情分析)
	CLUE_TYPE_ENUM_INT = 0x7fffffff, // 

};

enum CHUANGONG_TYPE {
	CHUANGONG_TYPE_NORMAL = 0, // (普通传功)
	CHUANGONG_TYPE_FRIENDS = 1, // (好友传功)
	CHUANGONG_TYPE_TEACHER_STUDENT = 2, // (师徒传功)
	CHUANGONG_TYPE_FAMILY = 3, // (结义传功)
	CHUANGONG_TYPE_OTHER = 4, // (其他传功)
	CHUANGONG_TYPE_INT = 0x7fffffff, // 

};

enum PROGRESS_TYPE_ENUM {
	PROGRESS_TYPE_NONE = 0, // 0(无进度)
	PROGRESS_TYPE_SPECIAL_ACT = 1, // 1(特色活动进度)
	PROGRESS_TYPE_FAMILY_KILL = 2, // 2(灭门进度)
	PROGRESS_TYPE_SEEK_TREASURE = 3, // 3(寻宝进度)
	PROGRESS_TYPE_NUM = 4, // 
	PROGRESS_TYPE_INT = 0x7fffffff, // 

};

enum TEMP_UNREACHABLE_TYPE {
	TEMP_UNREACHABLE_TYPE_NONE = 0, // 0(不生成)
	TEMP_UNREACHABLE_TYPE_SQUARE = 1, // 1(正方形)
	TEMP_UNREACHABLE_TYPE_CIRCLE = 2, // 2(圆形)
	TEMP_UNREACHABLE_TYPE_RECT_1 = 3, // 3(厚度为1的长方形)
	TEMP_UNREACHABLE_TYPE_NUM = 4, // 
	TEMP_UNREACHABLE_TYPE_INT = 0x7fffffff, // 

};

enum GRID_SHOW_CONDITION {
	GRID_SHOW_CONDITION_NONE = 0, // 0(无)
	GRID_SHOW_CONDITION_DEAD = 1, // 1(死亡)
	GRID_SHOW_CONDITION_JOINPROF = 2, // 2(加入门派)
	GRID_SHOW_CONDITION_JOINFACTION = 3, // 3(加入帮派)
	GRID_SHOW_CONDITION_INT = 0x7fffffff, // 

};

enum APTITUDE_CONDITION_JUDGE_ENUM {
	APTITUDE_CONDITION_JUDGE_ALONE = 0, // （单独计算）
	APTITUDE_CONDITION_JUDGE_SUM = 1, // （计算总和）
	APTITUDE_CONDITION_JUDGE_BOTH = 2, // （两种方式都有）
	APTITUDE_CONDITION_JUDGE_NUM = 3, // 

};

enum TALK_PROC_TYPE {
	TALK_PROC_TYPE_DEFAULT = 0, // TALK_PROC_TYPE_DEFAULT
	TALK_PROC_TYPE_PORTRAIT = 1, // TALK_PROC_TYPE_PORTRAIT
	TALK_PROC_TYPE_NUM = 2, // 

};

enum LOOT_TABLE_TYPE {
	LOOT_TABLE_NORMAL_LOOT = 0, // LOOT_TABLE_NORMAL_LOOT
	LOOT_TABLE_DROP_LOOT = 1, // LOOT_TABLE_DROP_LOOT

};

enum LOOT_OBJECT_REWARD_TYPE {
	LOOT_OBJECT_REWARD_BIND_MONEY = 0, // LOOT_OBJECT_REWARD_BIND_MONEY
	LOOT_OBJECT_REWARD_BUFF = 1, // LOOT_OBJECT_REWARD_BUFF
	LOOT_OBJECT_REWARD_ACCPOINTS = 2, // LOOT_OBJECT_REWARD_ACCPOINTS
	LOOT_OBJECT_REWARD_REWARD_BOX = 3, // LOOT_OBJECT_REWARD_REWARD_BOX
	LOOT_OBJECT_REWARD_REWARD_REPUTATION = 4, // LOOT_OBJECT_REWARD_REWARD_REPUTATION

};

enum CHEMISTRY_MEDICINE_ATTRIBUTE_TYPE {
	CHEMISTRY_MEDICINE_ATTRIBUTE_LIDAO = 0, // CHEMISTRY_MEDICINE_ATTRIBUTE_LIDAO
	CHEMISTRY_MEDICINE_ATTRIBUTE_JINGU = 1, // CHEMISTRY_MEDICINE_ATTRIBUTE_JINGU
	CHEMISTRY_MEDICINE_ATTRIBUTE_NEIJIN = 2, // CHEMISTRY_MEDICINE_ATTRIBUTE_NEIJIN
	CHEMISTRY_MEDICINE_ATTRIBUTE_QIHAI = 3, // CHEMISTRY_MEDICINE_ATTRIBUTE_QIHAI
	CHEMISTRY_MEDICINE_ATTRIBUTE_TIPO = 4, // CHEMISTRY_MEDICINE_ATTRIBUTE_TIPO

};

enum REQUISITE_INSTANCE_DIFFICULTY {
	REQUISITE_INSTANCE_DIFFICULTY_EASY = 0, // REQUISITE_INSTANCE_DIFFICULTY_EASY
	REQUISITE_INSTANCE_DIFFICULTY_NORMAL = 1, // REQUISITE_INSTANCE_DIFFICULTY_NORMAL
	REQUISITE_INSTANCE_DIFFICULTY_HARD = 2, // REQUISITE_INSTANCE_DIFFICULTY_HARD

};

enum CELEBRITY_TYPE {
	CELEBRITY_TYPE_MOUNT = 0, // 坐骑
	CELEBRITY_TYPE_FLYSWORD = 1, // 飞剑

};

enum INSTANCE_CONTENT_TYPE {
	INSTANCE_CONTENT_TYPE_STORY = 0, // INSTANCE_CONTENT_TYPE_STORY
	INSTANCE_CONTENT_TYPE_RACE = 1, // INSTANCE_CONTENT_TYPE_RACE
	INSTANCE_CONTENT_TYPE_WUSHUANG = 2, // INSTANCE_CONTENT_TYPE_WUSHUANG
	INSTANCE_CONTENT_TYPE_TECH = 3, // INSTANCE_CONTENT_TYPE_TECH

};

enum EQUIP_OBJECT_TYPE {
	EQUIP_OBJECT_TYPE_NONE = 0, // 无限制
	EQUIP_OBJECT_TYPE_PLAYER = 1, // 只能玩家
	EQUIP_OBJECT_TYPE_CELEBRITY = 2, // 只能名人

};

enum TOWER_CHALLENGE_LIMIT {
	TOWER_CHALLENGE_LIMIT_PER_DAY = 0, // 每天
	TOWER_CHALLENGE_LIMIT_PER_WEEK = 1, // 每周
	TOWER_CHALLENGE_LIMIT_PER_MONTH = 2, // 每月

};

enum ZHAOJILING_TARGET_TYPE {
	ZHAOJILING_TARGET_ALL = 0, // 所有人
	ZHAOJILING_TARGET_CORPS_MASTERS = 1, // 所有帮帮主
	ZHAOJILING_TARGET_SAME_CORPS = 2, // 本帮派成员

};

enum NPC_SOULVISION_TYPE {
	SOULVISION_NONE = 0, // 不受灵视影响
	SOULVISION_VISIBLE = 1, // 灵视可见
	SOULVISION_INVISIBLE = 2, // 灵视不可见

};

enum EXP_NATION_OFFICER_ENUM {
	EXP_NATION_OFFICER_NONE = 0, // NONE
	EXP_NATION_OFFICER_GUO_WANG = 1, // 
	EXP_NATION_OFFICER_WANG_FEI = 2, // 
	EXP_NATION_OFFICER_JIANG_JUN = 3, // 
	EXP_NATION_OFFICER_TAI_SHI = 4, // 
	EXP_NATION_OFFICER_TAI_WEI = 5, // 
	EXP_NATION_OFFICER_CHENG_XIANG = 6, // 
	EXP_NATION_OFFICER_WU_WEI_1 = 7, // 
	EXP_NATION_OFFICER_WU_WEI_2 = 8, // 
	EXP_NATION_OFFICER_YU_SHI_1 = 9, // 
	EXP_NATION_OFFICER_YU_SHI_2 = 10, // 
	EXP_NATION_OFFICER_COUNT = 10, // 

};

enum EXP_FACTION_POSITION_ENUM {
	EXP_FACTION_POSITION_NORMAL = 0, // 帮众
	EXP_FACTION_POSITION_MASTER = 1, // 帮主
	EXP_FACTION_POSITION_VICE_MASTER = 2, // 副帮主
	EXP_FACTION_POSITION_ZHANGLAO1 = 3, // EXP_FACTION_POSITION_ZHANGLAO1
	EXP_FACTION_POSITION_ZHANGLAO2 = 4, // EXP_FACTION_POSITION_ZHANGLAO2

};

enum EXP_QUALITY_ENUM {
	EXP_QUALITY_0 = 0, // 无
	EXP_QUALITY_1 = 1, // 白
	EXP_QUALITY_2 = 2, // 绿
	EXP_QUALITY_3 = 3, // 蓝
	EXP_QUALITY_4 = 4, // 紫
	EXP_QUALITY_5 = 5, // 橙
	EXP_QUALITY_6 = 6, // 金
	EXP_QUALITY_7 = 7, // 品质7
	EXP_QUALITY_8 = 8, // 品质8

};

enum EXP_MONSTER_SHOWNAME_ENUM {
	EXP_MONSTER_SHOWNAME_NONE = 0, // 不显示
	EXP_MONSTER_SHOWNAME_RED = 1, // 红色

};

enum EXP_AUCTION_CLASS1_ENUM {
	EXP_AUCTION_CLASS1_NONE = 0, // 无
	EXP_AUCTION_CLASS1_EQUIPMENT = 1, // 装备
	EXP_AUCTION_CLASS1_MATERIAL = 2, // 材料
	EXP_AUCTION_CLASS1_STONE = 3, // 宝石
	EXP_AUCTION_CLASS1_MOUNT = 4, // 坐骑
	EXP_AUCTION_CLASS1_BUILDUP = 5, // 成长
	EXP_AUCTION_CLASS1_MEDICINE = 6, // 药品
	EXP_AUCTION_CLASS1_OTHER = 7, // 其他
	EXP_AUCTION_CLASS1_COUNT = 8, // 

};

enum EXP_AUCTION_CLASS2_ENUM {
	EXP_AUCTION_CLASS2_NONE = 0, // 无
	EXP_AUCTION_CLASS2_EQUIP_WEAPON = 1, // 武器
	EXP_AUCTION_CLASS2_EQUIP_HEAD = 2, // 头部
	EXP_AUCTION_CLASS2_EQUIP_CHEST = 3, // 胸部
	EXP_AUCTION_CLASS2_EQUIP_WAIST = 4, // 腰部
	EXP_AUCTION_CLASS2_EQUIP_LEG = 5, // 腿部
	EXP_AUCTION_CLASS2_EQUIP_FOOT = 6, // 脚部
	EXP_AUCTION_CLASS2_EQUIP_WRIST = 7, // 手腕
	EXP_AUCTION_CLASS2_EQUIP_FINGER = 8, // 戒指
	EXP_AUCTION_CLASS2_EQUIP_NECK = 9, // 颈部
	EXP_AUCTION_CLASS2_EQUIP_AMULET = 10, // 符咒
	EXP_AUCTION_CLASS2_MEDICINE_RECOVER = 11, // 恢复
	EXP_AUCTION_CLASS2_MEDICINE_EXP = 12, // 经验
	EXP_AUCTION_CLASS2_STONE_1 = 13, // 珠宝
	EXP_AUCTION_CLASS2_STONE_2 = 14, // 翡翠
	EXP_AUCTION_CLASS2_STONE_3 = 15, // 玛瑙
	EXP_AUCTION_CLASS2_JUANZHOU = 16, // 卷轴
	EXP_AUCTION_CLASS2_REFRESH = 17, // 洗炼
	EXP_AUCTION_CLASS2_STRENGTHEN = 18, // 强化
	EXP_AUCTION_CLASS2_FENGMOTIE = 19, // 封魔帖
	EXP_AUCTION_CLASS2_JIU = 20, // 酒
	EXP_AUCTION_CLASS2_SWORDMAN_WEAPON = 21, // 刀
	EXP_AUCTION_CLASS2_PIKEMAN_WEAPON = 22, // 枪
	EXP_AUCTION_CLASS2_MAGICIAN_WEAPON = 23, // 杖
	EXP_AUCTION_CLASS2_ARCHER_WEAPON = 24, // 弓
	EXP_AUCTION_CLASS2_COUNT = 25, // 

};

enum EXP_AUCTION_CLASS3_ENUM {
	EXP_AUCTION_CLASS3_NONE = 0, // 无
	EXP_AUCTION_CLASS3_COUNT = 1, // 

};

enum EXP_MIX_SUITE_TYPE {
	EXP_MIX_SUITE_ENHANCE = 0, // 强化套装
	EXP_MIX_SUITE_GEM = 1, // 宝石套装
	EXP_MIX_SUITE_BABY = 2, // 孩子祝福套装
	EXP_MIX_SUITE_SPIRIT_GEM = 3, // 精神核心套装

};

enum EXP_ITEM_SORT_TYPE {
	EXP_ITEM_SORT_NONE = 0, // 普通(按物品属性确定分类)
	EXP_ITEM_SORT_VIP_CARD = 1, // Vip体验卡
	EXP_ITEM_SORT_LOOTERY_TICKET = 2, // 抽奖券
	EXP_ITEM_SORT_FENGMOTIE = 3, // 封魔帖
	EXP_ITEM_SORT_JIU = 4, // 酒
	EXP_ITEM_SORT_REFRESH_STONE = 5, // 洗练石

};

enum EXP_TALENT_TYPE {
	EXP_TALENT_ROLE = 0, // 角色天赋
	EXP_TALENT_CELEBRITY = 1, // 名人天赋

};

enum EXP_CARD_CAMP {
	EXP_CARD_CAMP_WEI = 0, // 魏
	EXP_CARD_CAMP_SHU = 1, // 蜀
	EXP_CARD_CAMP_WU = 2, // 吴
	EXP_CARD_CAMP_OTHERS = 3, // 群雄

};

enum EXP_BLACK_MARKET_MONEY_TYPE {
	EXP_BLACK_MARKET_BIND_MONEY = 0, // 绑定币
	EXP_BLACK_MARKET_TRADE_MONEY = 1, // 交易币
	EXP_BLACK_MARKET_REPUTATION = 2, // 声望
	EXP_BLACK_MARKET_ITEM = 3, // 物品

};

enum EXP_SERVER_UNLOCK_FUNCTION_ENUM {
	EXP_SERVER_UNLOCK_FUNCTION_JUEWEI = 0, // 爵位

};

enum EXP_USE_ITEM_NATION_LIMIT {
	EXP_USE_ITEM_NATION_ANY = 0, // 不限
	EXP_USE_ITEM_NATION_OWN = 1, // 本国
	EXP_USE_ITEM_NATION_OTHERS = 2, // 他国
	EXP_USE_ITEM_NATION_ENEMY = 3, // 敌国
	EXP_USE_ITEM_NATION_COUNTRY_START = 4, // 具体某国开始

};

enum EXP_HOLYBOSS_FEEDING_RANK_INDEX {
	EXP_HOLYBOSS_FEEDING_RANK_1ST = 0, // EXP_HOLYBOSS_FEEDING_RANK_1ST
	EXP_HOLYBOSS_FEEDING_RANK_2ND = 1, // EXP_HOLYBOSS_FEEDING_RANK_2ND
	EXP_HOLYBOSS_FEEDING_RANK_3RD = 2, // EXP_HOLYBOSS_FEEDING_RANK_3RD
	EXP_HOLYBOSS_FEEDING_RANK_4_TO_10 = 3, // EXP_HOLYBOSS_FEEDING_RANK_4_TO_10
	EXP_HOLYBOSS_FEEDING_RANK_11_TO_20 = 4, // EXP_HOLYBOSS_FEEDING_RANK_11_TO_20
	EXP_HOLYBOSS_FEEDING_RANK_COUNT = 5, // 

};

enum EXP_NORMALITEM_TYPE {
	EXP_NORMALITEM_NORMAL = 0, // 普通物品
	EXP_NORMALITEM_TITLE = 1, // 称号物品
	EXP_NORMALITEM_SPECIAL = 2, // 特殊道具
	EXP_NORMALITEM_TRAIN = 3, // 培养道具
	EXP_NORMALITEM_ACTIVITY = 4, // 活动道具
	EXP_NORMALITEM_UNDERWEAR_GRADEUP = 5, // 内装升级
	EXP_NORMALITEM_EXTERIOR_REFRESH = 6, // 神器洗练

};

enum EXP_TASKDICE_TYPE {
	EXP_TASKDICE_NORMAL = 0, // 普通
	EXP_TASKDICE_TITLE = 1, // 称号
	EXP_TASKDICE_ACTIVITY = 2, // 活动道具
	EXP_TASKDICE_LOTTERY = 3, // 彩票
	EXP_TASKDICE_GROW = 4, // 人物成长丹药

};

enum EXP_RECOVER_REWARD_LIMIT_TYPE {
	EXP_RECOVER_REWARD_LIMIT_TASK = 0, // 任务
	EXP_RECOVER_REWARD_LIMIT_TASK_STORAGE = 1, // 任务库
	EXP_RECOVER_REWARD_LIMIT_INSTANCE = 2, // 副本
	EXP_RECOVER_REWARD_LIMIT_CMN_USELIMIT = 3, // 公用使用限制模版
	EXP_RECOVER_REWARD_LIMIT_REPU = 4, // 声望

};

enum EXP_RECOVER_REWARD_SHOW_TYPE {
	EXP_RECOVER_REWARD_SHOW_EXP = 0, // 经验
	EXP_RECOVER_REWARD_SHOW_ITEM = 1, // 道具

};

enum EXP_RED_PACKET_TYPE {
	EXP_RED_PACKET_NATION = 0, // 国家红包（限王城该地图使用）
	EXP_RED_PACKET_FACTION = 1, // 帮会红包（限帮会地图使用）

};

enum EXP_FRIEND_BLESSING_GREETING_TYPE {
	EXP_FRIEND_BLESSING_GREETING_LEVELUP = 0, // 升级

};

enum EXP_FRIEND_BLESSING_INVEST_TYPE {
	EXP_FRIEND_BLESSING_INVEST_LEVELUP = 0, // 升级

};

enum EXP_CHARIOT_BUILD_COST_TYPE {
	EXP_CHARIOT_BUILD_COST_MONEY = 0, // 金钱
	EXP_CHARIOT_BUILD_COST_DIAMOND = 1, // 钻石
	EXP_CHARIOT_BUILD_COST_REPU = 2, // 声望
	EXP_CHARIOT_BUILD_COST_FACTION_FUND = 3, // 帮派资金

};

enum EXP_TRANSFORM_TYPE {
	EXP_TRANSFORM_NORMAL = 0, // 普通变身
	EXP_TRANSFORM_CHARIOT = 1, // 战车变身
	EXP_TRANSFORM_WEAPON = 2, // 武器变身
	EXP_TRANSFORM_MISSILE = 3, // 炮类变身
	EXP_TRANSFORM_PLAYER = 4, // 玩家形象变身

};

enum TEAM_PLATFORM_TYPE {
	TEAMPLATFORM_INSTANCE = 0, // 0副本活动
	TEAMPLATFORM_TASK = 1, // 1任务活动

};

enum PRODUCE_SKILL_TYPE {
	PRODUCE_SKILL_NOMAL = 0, // 普通生产
	PRODUCE_SKILL_PROF = 1, // 职业制符
	PRODUCE_SKILL_PROP = 2, // 属性技能

};

enum TASK_STATE_LIMIT {
	TASK_STATE_LIMIT_CANPICK = 0, // 可接取
	TASK_STATE_LIMIT_ACTIVED = 1, // 已接取
	TASK_STATE_LIMIT_FINISHED = 2, // 已完成
	TASK_STATE_LIMIT_COUNT = 3, // 

};

enum LEVEL_ADAPTED_TYPE {
	LEVEL_ADAPTED_NONE = 0, // 不自适应
	LEVEL_ADAPTED_PERSONAL = 1, // 个人
	LEVEL_ADAPTED_TEAM = 2, // 队伍
	LEVEL_ADAPTED_COUNT = 3, // 

};

enum CARD_INSIDE_TYPE {
	CARD_INSIDE_NORMALCARD = 0, // 普通卡牌
	CARD_INSIDE_RETINUE = 1, // 随从
	CARD_INSIDE_TALISMAN = 2, // 法宝
	CARD_INSIDE_COUNT = 3, // 

};

enum TASK_GRANT_REWARD_PROB_TYPE {
	TASK_GRANT_REWARD_PROB_TYPE_INDEPEND = 0, // 独立概率
	TASK_GRANT_REWARD_PROB_TYPE_NORMALIZATION = 1, // 归一化概率

};

enum CHAT_RED_PACKET_SEND_TYPE {
	CHAT_RED_PACKET_SEND_TYPE_PERSONAL = 1, // 个人红包
	CHAT_RED_PACKET_SEND_TYPE_FACTION = 2, // 帮派
	CHAT_RED_PACKET_SEND_TYPE_WORLD = 3, // 世界
	CHAT_RED_PACKET_SEND_TYPE_TEAM = 4, // 队伍

};

enum EXP_HOMEBUILDING_STYLE {
	EXP_HOMEBUILDING_STYLE_RURAL = 0, // 田园
	EXP_HOMEBUILDING_STYLE_RETRO = 1, // 复古
	EXP_HOMEBUILDING_STYLE_MODERN = 2, // 现代
	EXP_HOMEBUILDING_STYLE_SPIRITANIMAL = 3, // 灵禽异兽
	EXP_HOMEBUILDING_STYLE_RELAXATION = 4, // 休闲

};

enum EXP_HOMEBUILDING_TYPE {
	EXP_HOMEBUILDING_TYPE_DESK = 0, // 桌凳
	EXP_HOMEBUILDING_TYPE_FLOWER = 1, // 花草
	EXP_HOMEBUILDING_TYPE_DECORATION = 2, // 摆设
	EXP_HOMEBUILDING_TYPE_OTHERS = 3, // 杂物

};

enum WING_FIXVIEW_TYPE {
	WING_FIXVIEW_TYPE_FLYSWORD = 0, // 飞剑
	WING_FIXVIEW_TYPE_WING = 1, // 翅膀
	WING_FIXVIEW_TYPE_CARPET = 2, // 飞毯
	WING_FIXVIEW_TYPE_FLYMOUNT = 3, // 飞行坐骑

};

enum SURFACE_SPEED_LEVEL {
	LOW_SPEED = 0, // 低档速度
	MIDDLE_SPEED = 1, // 中档速度
	HIGH_SPEED = 2, // 高档速度
	SPEED_COUNT = 3, // 

};

enum WING_ACT_TYPE {
	WING_ACT_TYPE_NOT_DEFINE = 0, // 无特殊定义（程序默认姿势）
	WING_ACT_TYPE_STAND = 1, // 站姿
	WING_ACT_TYPE_SIT = 2, // 坐姿
	WING_ACT_TYPE_MOUNT = 3, // 骑姿
	WING_ACT_TYPE_CHAIR = 4, // 坐椅子
	WING_ACT_TYPE_KNEES = 5, // 跪坐

};

enum DUELSTATE_ENUM {
	DUEL_ST_NONE = 0, // 无
	DUEL_ST_PREPARE = 1, // 准备
	DUEL_ST_INDUEL = 2, // 决斗中
	DUEL_ST_STOPPING = 3, // 停止

};

enum DUELRES_ENUM {
	DUELRES_UNDEFINED = 0, // 无
	DUELRES_WIN = 1, // 胜
	DUELRES_LOSE = 2, // 负
	DUELRES_DRAW = 3, // 平

};

enum PRACTICE_SKILL_TYPE {
	PRACTICE_SKILL_TYPE_PLAYER = 0, // 角色
	PRACTICE_SKILL_TYPE_PET = 1, // 宠物

};

enum EXP_RECHARGE_FUND_TYPE {
	EXP_FUND_TYPE_NORMAL = 0, // 普通
	EXP_FUND_TYPE_DAILY = 1, // 每日成长基金
	EXP_FUND_TYPE_LEVEL = 2, // 等级成长基金
	EXP_FUND_TYPE_ADVANCED = 3, // 高阶成长基金

};

enum EXP_RECHARGE_TIME_LIMIT_TYPE {
	EXP_RECHARGE_TIME_LIMIT_TYPE_NORMAL = 0, // 不清空
	EXP_RECHARGE_TIME_LIMIT_TYPE_DAILY = 1, // 每日清空
	EXP_RECHARGE_TIME_LIMIT_TYPE_WEEK = 2, // 每周清空

};

enum RECHARGE_CONFIG_TYPE {
	EXP_RECHARGE_CONFIG_TYPE_NORMAL = 0, // 普通
	EXP_RECHARGE_CONFIG_TYPE_WEEK = 1, // 周卡
	EXP_RECHARGE_CONFIG_TYPE_MONTH = 2, // 月卡
	EXP_RECHARGE_CONFIG_TYPE_FESTIVAL1 = 3, // 节日1
	EXP_RECHARGE_CONFIG_TYPE_FESTIVAL2 = 4, // 节日2
	EXP_RECHARGE_CONFIG_TYPE_FESTIVAL3 = 5, // 节日3
	EXP_RECHARGE_CONFIG_TYPE_FESTIVAL4 = 6, // 节日4
	EXP_RECHARGE_CONFIG_TYPE_FESTIVAL5 = 7, // 节日5

};

enum TALISMAN_ATTR_TYPE {
	TALISMAN_ATTR_TYPE_NON = 0, // 无
	TALISMAN_ATTR_TYPE_COUNT = 8, // 

};

enum BODYSIZE_LIMIT {
	BODYSIZE_LIMIT_NONE = 0, // BODYSIZE_LIMIT_NONE
	BODYSIZE_LIMIT_NORMAL = 1, // BODYSIZE_LIMIT_NORMAL
	BODYSIZE_LIMIT_SMALL = 2, // BODYSIZE_LIMIT_SMALL
	BODYSIZE_LIMIT_INT = 0x7fffffff, // 

};

enum BABY_STONE_TYPE {
	BABY_STONE_COUNT = 15, // BABY_STONE_COUNT

};

enum BABY_SYNC_DEVICE_TYPE_MASK {
	BABY_SYNC_DEVICE_TYPE_1 = 0x00000001, // 红色
	BABY_SYNC_DEVICE_TYPE_2 = 0x00000002, // 黄色
	BABY_SYNC_DEVICE_TYPE_4 = 0x00000004, // 蓝色
	BABY_SYNC_DEVICE_TYPE_8 = 0x00000008, // 绿色

};

enum BABY_EQUIP_TYPE {
	BABY_EQUIP_TYPE_WEAPON = 0, // 武器
	BABY_EQUIP_TYPE_CLOTH = 1, // 衣服
	BABY_EQUIP_TYPE_HAT = 2, // 帽子
	BABY_EQUIP_TYPE_PANT = 3, // 裤子
	BABY_EQUIP_TYPE_GLOVES = 4, // 手套
	BABY_EQUIP_TYPE_SHOES = 5, // 鞋子

};

enum BABY_SKILL_COLOR_MASK {
	BABY_SKILL_COLOR_1 = 0x00000001, // 红色
	BABY_SKILL_COLOR_2 = 0x00000002, // 黄色
	BABY_SKILL_COLOR_4 = 0x00000004, // 蓝色
	BABY_SKILL_COLOR_5 = 0x00000005, // 紫色
	BABY_SKILL_COLOR_7 = 0x00000007, // 白色

};

enum HOMEBUILDING_SET_SCENE_TYPE {
	HOME = 0, // HOME
	SECOND_HOME = 1, // SECOND_HOME
	THIRD_HOME = 2, // THIRD_HOME

};

enum DICE_AWARD_TYPE {
	DICE_AWARD_TYPE_PARK = 0, // 乐园
	DICE_AWARD_TYPE_TREASURE = 1, // 珍宝阁
	DICE_AWARD_TYPE_CARD = 2, // 翻牌彩票
	DICE_AWARD_TYPE_CARD2 = 3, // 翻牌彩票二期
	DICE_AWARD_TYPE_VIDEO_GAME = 4, // 绘梨衣的游戏机
	DICE_AWARD_TYPE_WITCH_COTTAGE = 5, // 魔女小屋
	DICE_AWARD_TYPE_TOP_STAR = 6, // 明星应援
	DICE_AWARD_TYPE_DAILY_SALES = 7, // 每日促销
	DICE_AWARD_TYPE_OVERSEA = 8, // 海外简化版彩票
	DICE_AWARD_TYPE_HEAVEN = 9, // 夏弥的游乐场
	DICE_AWARD_TYPE_DRAGON_HOUSE = 10, // 巨龙宝库
	DICE_AWARD_TYPE_PROMOTE_SALES = 11, // 促销彩票
	DICE_AWARD_TYPE_PLEASURE = 12, // 趣味夺宝
	DICE_AWARD_TYPE_EASY = 13, // 简易彩票
	DICE_AWARD_TYPE_EASY2 = 14, // 简易彩票2
	DICE_AWARD_TYPE_SPRING = 15, // 秘钥宝箱
	DICE_AWARD_TYPE_KING_HOUSE = 16, // 王之宝库
	DICE_AWARD_TYPE_GOD_EXPLORE = 17, // 神迹探索
	DICE_AWARD_TYPE_GATHER_WORD = 18, // 集字
	DICE_AWARD_TYPE_LITTLE_CARD = 19, // 小彩票
	DICE_AWARD_TYPE_TREASURE_TAG = 20, // 宝签
	DICE_AWARD_TYPE_TEAM_RECHARGE = 21, // 组队充值彩票
	DICE_AWARD_TYPE_SEA = 22, // 星海秘藏
	DICE_AWARD_TYPE_SHIP = 23, // 冰海行动
	DICE_AWARD_TYPE_CARD3 = 24, // 翻牌彩票三期
	DICE_AWARD_TYPE_QXQY = 25, // 千寻奇遇
	DICE_AWARD_TYPE_MACHINE = 26, // 街头游戏机
	DICE_AWARD_TYPE_HOLY_GHOST = 27, // 英灵彩票
	DICE_AWARD_TYPE_BINGO = 28, // 宾果彩票
	DICE_AWARD_TYPE_TURNTABLE = 29, // 转盘彩票
	DICE_AWARD_TYPE_DRAW = 30, // 绘梦星空
	DICE_AWARD_TYPE_SHARE_BOX = 31, // 分享宝箱

};

enum HOLYBOSS_MATERIAL_FEED_TYPE {
	HOLYBOSS_MATERIAL_FEED_TYPE_COUNT = 4, // HOLYBOSS_MATERIAL_FEED_TYPE_COUNT

};

enum MONSTER_HP_TYPE {
	NORMAL_MONSTER = 0, // 普通怪
	ELITE_MONSTER = 1, // 精英怪
	BOSS_MONSTER_BROADCAST = 2, // BOSS广播血条
	BOSS_MONSTER_NOT_BROADCAST = 3, // BOSS不广播血条

};

enum MONSTER_MATERIAL_TYPE {
	TYPE_BODY = 0, // 肉体类
	TYPE_RIGID = 1, // 刚体类
	TYPE_FROZEN = 2, // 冰冻类
	TYPE_STONE = 3, // 石化类

};

enum TYPE_FASHION {
	FASHION_INVILID = 0, // 无效
	FASHION_HAIR = 1, // 发型
	FASHION_EAR = 2, // 耳朵
	FASHION_TAIL = 3, // 尾巴
	FASHION_TATTOO = 4, // 纹身
	FASHION_HEAD_DRESS = 5, // 头饰
	FASHION_FACIAL_ORNAMENT = 6, // 面饰
	FASHION_BACK_ORNAMENT = 7, // 背饰
	FASHION_WAIST_ORNAMENT = 8, // 腰饰
	FASHION_EARRING = 9, // 耳环
	FASHION_NECKLACE = 10, // 项链
	FASHION_HEAD_TATTOO = 11, // 纹面
	FASHION_CLOTH = 12, // 衣服
	FASHION_WEAPON = 13, // 时装武器
	FASHION_UMBRELLA = 14, // 雨伞
	FASHION_FOOTPRINT = 15, // 脚印
	FASHION_SURROUND_FX = 16, // 环身特效
	FASHION_BABY_CLOTH = 20, // 孩子衣服
	FASHION_BABY_HAIR = 21, // 孩子发型
	FASHION_BREED_CUTE_PET_NECKLACE = 30, // 育宠达人萌宠项链
	FASHION_BREED_CUTE_PET_HEAD_DRESS = 31, // 育宠达人萌宠头饰
	FASHION_BREED_CUTE_PET_BACK_ORNAMENT = 32, // 育宠达人萌宠背饰

};

enum TYPE_MAKE_UP {
	EYEBROW_SAMPLE = 1, // 眉毛样式
	EYEBROW_COLOR = 2, // 眉毛颜色
	EYE_SAMPLE = 3, // 眼睛样式
	EYE_COLOR = 4, // 眼睛颜色
	EYE_COLOR1 = 5, // 眼白颜色
	EYE_LASH_SAMPLE = 6, // 睫毛样式
	MAKE_UP_SAMPLE = 7, // 化妆样式
	EYE_LINE_COLOR = 8, // 眼线颜色
	EYE_SHADOW_COLOR = 9, // 眼影颜色
	BLUSHER_COLOR = 10, // 胭脂颜色
	LIP_SAMPLE = 11, // 唇膏样式
	LIP_COLOR = 12, // 唇膏颜色
	BEARD_SAMPLE = 13, // 胡须样式
	BEARD_COLOR = 14, // 胡须颜色
	TATTOO_SAMPLE = 15, // 纹身
	EYE_LASH_COLOR = 16, // 睫毛颜色

};

enum CHARM_ATTRIBUTE_TYPE1 {
	TYPE_GORGEOUS = 0, // 华丽
	TYPE_SIMPLE = 1, // 简约

};

enum CHARM_ATTRIBUTE_TYPE2 {
	TYPE_GRACE = 0, // 优雅
	TYPE_LIVELY = 1, // 活泼

};

enum CHARM_ATTRIBUTE_TYPE3 {
	TYPE_MATURE = 0, // 成熟
	TYPE_LOVELY = 1, // 可爱

};

enum CHARM_ATTRIBUTE_TYPE4 {
	TYPE_SEXY = 0, // 性感
	TYPE_PURE = 1, // 清纯

};

enum CHARM_ATTRIBUTE_TYPE5 {
	TYPE_COOL = 0, // 清凉
	TYPE_WARM = 1, // 保暖

};

enum FASHION_EARRING_TYPE {
	DOUBLE = 0, // 双边对称
	LEFT = 1, // 只有左边
	RIGHT = 2, // 只有右边

};

enum GENERAL_SUB_TYPE {
	GST_UNLOCK_FASHION = 0, // 时装
	GST_UNLOCK_PORTRAIT_IMAGE = 1, // 头像-图片
	GST_UNLOCK_PORTRAIT_FRAMGE = 2, // 头像-边框
	GST_UNLOCK_TITLE = 3, // 称号
	GST_UNLOCK_BUBBLE = 4, // 气泡
	GST_UNLOCK_WORDART = 5, // 艺术字
	GST_UNLOCK_CHATBACKGROUND = 6, // 聊天背景
	GST_UNLOCK_FLYSWORD_SURFACE = 7, // 解锁座驾
	GST_UNLOCK_RETINUE = 8, // 伙伴
	GST_UNLOCK_DICE = 9, // 色子
	GST_UNLOCK_COLLECT = 10, // 手办收藏
	GST_UNLOCK_RETINUE_GIFT = 11, // 伙伴礼物
	GST_UNLOCK_FASHION_LOGO = 12, // 时装图案
	GST_UNLOCK_HOMETOWN_OBJECT = 13, // 家园建筑、家具
	GST_UNLOCK_EMOJI = 14, // 表情包
	GST_UNLOCK_BREED_CUTE_PET = 15, // 育宠达人萌宠
	GST_ADD_DRAGONBORN = 16, // 生成龙裔
	GST_ADD_HONEYGARDEN = 17, // 增加花园物品
	GST_UNLOCK_LONGHUN = 18, // 解锁龙魂
	GST_UNLOCK_RECHARGE_ACT = 19, // 解锁付费动作
	GST_ADD_REPU = 20, // 增加声望
	GST_FISH_ROD = 21, // 鱼竿
	GST_NAME_FX = 22, // 姓名板特效
	GST_RUNE_STONE = 23, // 符石
	GST_LOTTERY_MACHINE_GET_MONEY = 24, // 街头大彩票兑换
	GST_UNLOCK_HERO = 25, // 英灵解锁
	GST_HERO_GIFT = 26, // 英灵礼物
	GST_UNLOCK_TOWNLET_SKIN = 27, // 解锁小镇皮肤
	GST_TOWNLET_VISIT = 28, // 小镇伙伴来访吸引道具
	GST_TOWNLET_TRAVEL = 29, // 小镇伙伴旅行事件道具
	GST_DRAGON_MYSTIQUE_ADD_INSCRIPTION = 30, // 龙凝秘法解锁刻印
	GST_PERSONAL_CARD = 31, // 解锁个人名片
	GST_TOWNLET_PET_FOOD = 32, // 小镇宠物食品药品
	GST_TOWNLET_PET_MATERIAL = 33, // 小镇宠物材料

};

enum PHOTO_TAG_TYPE {
	PHOTO_TAG_EMPTY = 0, // 空
	PHOTO_TAG_HOT = 1, // 热卖
	PHOTO_TAG_NORMAL = 2, // 普通
	PHOTO_TAG_ACTIVITY = 3, // 活动

};

enum TITLE_QUALITY_ENUM {
	TITLE_QUALITY_0 = 0, // 绿
	TITLE_QUALITY_1 = 1, // 蓝
	TITLE_QUALITY_2 = 2, // 紫
	TITLE_QUALITY_3 = 3, // 橙
	TITLE_QUALITY_4 = 4, // 黄
	TITLE_QUALITY_5 = 5, // 粉

};

enum ENHANCE_BASE_PROP_TYPE {
	EBRT_EQUIP_ENHANCE = 1, // 装备位强化
	EBRT_KOTODAMA = 2, // 言灵
	EBRT_GUARD_STAR = 2, // 宠物星阵
	EBRT_SEVEN_CRIME_SWORD = 3, // 七宗罪神威
	EBRT_HERO = 4, // 英灵
	EBRT_DRAON_MYSTIQUE = 5, // 龙凝秘法
	EBRT_TOWNLET = 6, // 小镇
	EBRT_PERSONAL_CARD = 7, // 名片
	EBRT_STAR_GHOST = 8, // 星魂
	EBRT_TETRAHEDRON_REFINE = 9, // 圣核精炼
	EBRT_FORCE_INT = 0x7fffffff, // EQUIPIVTR_FORCE_INT

};

enum KOTODAMA_TYPE {
	KOTODAMA_TYPE_COMBAT = 1, // 战斗言灵
	KOTODAMA_TYPE_ENHANCE = 2, // 辅助言灵
	KOTODAMA_TYPE_OUGI = 3, // 奥义言灵

};

enum RUNE_NEW_TYPE {
	RUNE_NEW_TYPE_COMMON = 0, // 通用符文
	RUNE_NEW_TYPE_LAND = 1, // 地符文
	RUNE_NEW_TYPE_FIRE = 2, // 火符文
	RUNE_NEW_TYPE_WATER = 3, // 水符文
	RUNE_NEW_TYPE_WIND = 4, // 风符文
	RUNE_NEW_TYPE_SPIRIT = 5, // 精神符文

};

enum RUNE_NEW_WORD_TYPE {
	RUNE_NEW_WORD_TYPE_NONE = 0, // 无效
	RUNE_NEW_WORD_TYPE_LEGEND = 1, // 传奇
	RUNE_NEW_WORD_TYPE_REGULAR = 2, // 稀有
	RUNE_NEW_WORD_TYPE_ELEMENT = 3, // 元素
	RUNE_NEW_WORD_TYPE_COUNT = 4, // 数量

};

enum WEAPON_MODEL_TYPE {
	ONE_MAIN = 0, // 一把主武器
	ONE_MAIN_ONE_SUB = 1, // 一把主一把副
	TWO_MAIN = 2, // 两把主武器
	TWO_MAIN_ONE_SUB = 3, // 两把主一把副
	ONE_MAIN_TWO_SUB = 4, // 一把主两把副
	ONE_MAIN_ONE_SUB_ONE_MINOR = 5, // 一把主一把副一把次
	TWO_MAIN_ONE_SUB_ONE_MINOR = 6, // 两把主一把副一把次
	ONE_MAIN_ONE_SUB_ONE_MINOR_ONE_RELAX = 7, // 一把主一把副一把次——把休闲
	TWO_MAIN_TWO_SUB_ONE_MINOR = 8, // 两把主两把副一把次

};

enum GUARD_ELEM_TYPE {
	GUARD_ELEM_LIGHT = 0, // 光明
	GUARD_ELEM_DARK = 1, // 黑暗
	GUARD_ELEM_NONE = 2, // 无元素属性
	GUARD_ELEM_COUNT = 3, // 

};

enum GUARD_FIGHT_TYPE {
	FIGHT_GENERAL = 0, // 通用类
	FIGHT_ATTACK = 1, // 攻击类
	FIGHT_ASSIST = 2, // 辅助类
	FIGHT_CURE = 3, // 治疗类

};

enum GUARD_SOURCE_ENUM {
	LEVEL_UNLOCK = 0, // 等级解锁
	TOOL_UNLOCK = 1, // 道具解锁

};

enum GUARD_SHAP_ENUM {
	GUARD_SHAP_YONG = 0, // 幼年体
	GUARD_SHAP_ADULT = 1, // 成年体
	GUARD_SHAP_FINAL = 2, // 完全体

};

enum RETINUE_PROP_ENUM {
	RETINUE_PROP_0 = 0, // 生命值
	RETINUE_PROP_1 = 1, // 物理攻击
	RETINUE_PROP_2 = 2, // 法术攻击
	RETINUE_PROP_3 = 3, // 物理防御
	RETINUE_PROP_4 = 4, // 法术防御
	RETINUE_PROP_5 = 5, // 暴击等级
	RETINUE_PROP_6 = 6, // 破甲等级
	RETINUE_PROP_7 = 7, // 冷却缩减
	RETINUE_PROP_8 = 8, // 多重打击
	RETINUE_PROP_COUNT = 9, // 

};

enum RETINUE_SKILL_ENUM {
	RETINUE_SKILL_0 = 0, // 技能品质1
	RETINUE_SKILL_1 = 1, // 技能品质2
	RETINUE_SKILL_2 = 2, // 技能品质3
	RETINUE_SKILL_3 = 3, // 技能品质4
	RETINUE_SKILL_4 = 4, // 技能品质5
	RETINUE_SKILL_COUNT = 9, // 

};

enum NPC_IMPACT_TYPE {
	NPC_IMPACT_TYPE_0 = 0, // 无碰撞
	NPC_IMPACT_TYPE_1 = 1, // 客户端碰撞
	NPC_IMPACT_TYPE_2 = 2, // 服务器碰撞

};

enum INTERACTION_TYPE {
	INTERACTION_TYPE_DEFAULT = 0, // 普通
	INTERACTION_TYPE_TRANSPORT = 1, // 起重机
	INTERACTION_TYPE_OPEN_ALBUM = 2, // 查看相册
	INTERACTION_TYPE_BASKETBALL = 3, // 篮球
	INTERACTION_TYPE_GUITAR = 4, // 吉他
	INTERACTION_TYPE_BOOK = 5, // 书籍
	INTERACTION_TYPE_MISSILE = 6, // 炮弹
	INTERACTION_TYPE_JIGSAW = 7, // 拼图
	INTERACTION_TYPE_FINDDIFFERENT = 8, // 找不同
	INTERACTION_TYPE_CROSSLIGHT = 9, // 十字灯
	INTERACTION_TYPE_RADIO = 10, // 收音机
	INTERACTION_TYPE_PIANO = 11, // 钢琴
	INTERACTION_TYPE_PHONE_BOOTH = 12, // 电话亭
	INTERACTION_TYPE_WISH = 13, // 许愿
	INTERACTION_TYPE_TRANSMIT = 14, // 传送
	INTERACTION_TYPE_TERRAIN_TOGGLE = 15, // 地形机关
	INTERACTION_TYPE_WATCH_TV = 16, // 看电视
	INTERACTION_TYPE_ONEPEN = 17, // 一笔画
	INTERACTION_TYPE_RECEIVE = 18, // 接道具
	INTERACTION_TYPE_FINDGOODS = 19, // 找道具
	INTERACTION_TYPE_FINDSAME = 20, // 记忆翻格子
	INTERACTION_TYPE_MAHJONG = 21, // 连连看
	INTERACTION_TYPE_2048 = 22, // 小游戏2048
	INTERACTION_TYPE_IPAD = 23, // ipad
	INTERACTION_TYPE_DOLL_MACHINE = 24, // 娃娃机
	INTERACTION_TYPE_RIDDLE_TOGGLE = 25, // 解谜机关
	INTERACTION_TYPE_ROLLER_COASTER = 26, // 过山车
	INTERACTION_TYPE_FERRIS_WHEEL = 27, // 摩天轮
	INTERACTION_TYPE_CARROUSEL = 28, // 旋转木马
	INTERACTION_TYPE_THING = 29, // 物件
	INTERACTION_TYPE_COMPUTER = 30, // 电脑
	INTERACTION_TYPE_DIARY = 31, // 日记本
	INTERACTION_TYPE_COOK = 32, // 做菜
	INTERACTION_TYPE_CHOICE = 33, // 选择
	INTERACTION_TYPE_STAR = 34, // 明星
	INTERACTION_TYPE_LIVE = 35, // 直播
	INTERACTION_TYPE_REFRIGERATOR = 36, // 冰箱(自研食谱)
	INTERACTION_TYPE_RESEARCH_OPTION = 37, // 自研操作(自研食谱)
	INTERACTION_TYPE_SLOT_MACHINE = 38, // 拉霸机
	INTERACTION_TYPE_EGG_MACHINE = 39, // 扭蛋机
	INTERACTION_TYPE_DRESS_UP = 40, // 换装
	INTERACTION_TYPE_WINERACK = 41, // 酒架(自研食谱)
	INTERACTION_TYPE_STAR_MAKE = 42, // 明星制造
	INTERACTION_TYPE_CHARACTER = 43, // 角色
	INTERACTION_TYPE_QUESTION = 44, // 智慧问答
	INTERACTION_TYPE_MINIGAME = 45, // 小游戏
	INTERACTION_TYPE_PHOTOGRAPH = 46, // 摄影
	INTERACTION_TYPE_SHELVE = 47, // 货架
	INTERACTION_TYPE_STORE_MAKE = 48, // 小店定制
	INTERACTION_TYPE_FACTIONFLOWER = 49, // 社团花圃
	INTERACTION_TYPE_FACTIONFLOWER_SIGN = 50, // 社团花圃牌子
	INTERACTION_TYPE_OPEN_PANEL = 51, // 打开界面
	INTERACTION_TYPE_FURNITURE_LIGHT = 52, // 家具灯
	INTERACTION_TYPE_FURNITURE_THING = 53, // 家具物件
	INTERACTION_TYPE_DANCE_TOGETHER = 54, // 共舞情缘
	INTERACTION_TYPE_MORA = 55, // 猜拳
	INTERACTION_TYPE_SOUL_TREE = 56, // 誓约树
	INTERACTION_TYPE_PARKING = 57, // 停车位
	INTERACTION_TYPE_HOME_GARDENING = 58, // 家园花圃
	INTERACTION_TYPE_PARTY_COOK = 59, // 派对料理
	INTERACTION_TYPE_BIRTHDAY_CAKE = 60, // 生日蛋糕
	INTERACTION_TYPE_MIRROR = 61, // 镜子
	INTERACTION_TYPE_OBJECT_FURNITURE = 62, // 孩子交互家具
	INTERACTION_TYPE_OVERCOOK = 63, // 胡闹厨房交互
	INTERACTION_TYPE_MUSIC_FLOOR = 64, // 音乐地板交互
	INTERACTION_TYPE_MINIGAME_MORA = 65, // 小游戏猜拳
	INTERACTION_TYPE_PET_FURNITURE = 66, // 宠物交互家具
	INTERACTION_TYPE_WINTER_PROJECT_BALLOT_BOX = 67, // 冬日计划投票箱
	INTERACTION_TYPE_WINTER_PROJECT_PRODUCT_STATION = 68, // 冬日计划制作台
	INTERACTION_TYPE_WINTER_PROJECT_PUBLIC_STORAGE_BOX = 69, // 冬日计划公共储物箱
	INTERACTION_TYPE_WINTER_PROJECT_STORAGE_BOX = 70, // 冬日计划普通补给箱
	INTERACTION_TYPE_WINTER_PROJECT_BAD_STORAGE_BOX = 71, // 冬日计划狼人补给箱
	INTERACTION_TYPE_WINTER_PROJECT_GOOD_STORAGE_BOX = 72, // 冬日计划好人补给箱
	INTERACTION_TYPE_WINTER_PROJECT_MATERIAL_TASK_BOX = 73, // 冬日计划材料任务箱
	INTERACTION_TYPE_WINTER_PROJECT_TREASURE_TASK_BOX = 74, // 冬日计划挖宝任务箱
	INTERACTION_TYPE_WINTER_PROJECT_PASSWORD_GET = 75, // 冬日计划获取密码箱
	INTERACTION_TYPE_WINTER_PROJECT_PASSWORD_SET = 76, // 冬日计划输入密码箱
	INTERACTION_TYPE_WINTER_PROJECT_BEACONS = 77, // 冬日计划信标
	INTERACTION_TYPE_WINTER_PROJECT_FAKE_STORAGE_BOX = 78, // 冬日计划假补给箱
	INTERACTION_TYPE_WINTER_PROJECT_SINGLE_ESCAPE = 79, // 冬日计划单人逃生舱
	INTERACTION_TYPE_WINTER_PROJECT_CALL_ESCAPE = 80, // 冬日计划呼叫逃生载具
	INTERACTION_TYPE_WINTER_PROJECT_ESCAPE = 81, // 冬日计划逃生载具
	INTERACTION_TYPE_WINTER_PROJECT_SHORTCUT = 82, // 冬日计划狼人通道
	INTERACTION_TYPE_WINTER_PROJECT_REBORN = 83, // 冬日计划复活装置
	INTERACTION_TYPE_WINTER_PROJECT_BUNKER_SWITCH = 84, // 冬日计划地堡开关
	INTERACTION_TYPE_WINTER_PROJECT_BUNKER_STORAGE_BOX = 85, // 冬日计划地堡补给箱
	INTERACTION_TYPE_WINTER_PROJECT_BUNKER_WEAPON_BOX = 86, // 冬日计划地堡武器库
	INTERACTION_TYPE_WINTER_PROJECT_BUNKER_WEAPON_SWITCH = 87, // 冬日计划武器地堡开关
	INTERACTION_TYPE_BLESS = 88, // 春节祝福墙
	INTERACTION_TYPE_HOLIDAYBLESS = 89, // 节日告白祝福墙
	INTERACTION_TYPE_SCRAWL_PASTED = 90, // 涂鸦玩法墙体贴片
	INTERACTION_TYPE_FISH = 91, // 钓鱼
	INTERACTION_TYPE_TOWNLET_RETINUE = 92, // 小镇伙伴玩法
	INTERACTION_TYPE_TOWNLET_POSTCARD_SHOW = 93, // 小镇明信片展示墙
	INTERACTION_TYPE_TOWNLET_MAILBOX = 94, // 小镇邮箱
	INTERACTION_TYPE_TOWNLET_PET_JOB = 95, // 小镇宠物打工

};

enum FAKE_INTERACTION_TYPE {
	FAKE_INTERACTION_TYPE_NONE = 0, // 无效
	FAKE_INTERACTION_TYPE_BABY = 1, // 继承者
	FAKE_INTERACTION_TYPE_PET = 2, // 宠物

};

enum BABY_INTERACTION_TYPE {
	INTERACTION_TYPE_EMOTION = 0, // 单人动作
	INTERACTION_TYPE_INTERACT_EMOTION = 1, // 双人交互动作
	INTERACTION_TYPE_FURNITUER = 2, // 家具交互

};

enum PET_INTERACTION_TYPE {
	PET_INTERACT_EMOTION = 0, // 双人交互动作
	PET_HOME_FURNITUER = 1, // 家园家具交互

};

enum BABY_INTERACT_EFFECT_TYPE {
	EFFECT_NONE = 0, // 无
	EFFECT_FOOD = 1, // 饱食
	EFFECT_CLEAN = 2, // 清洁
	EFFECT_INTIMATE = 3, // 亲密
	EFFECT_HAPPY = 4, // 愉悦
	EFFECT_EMPTY1 = 5, // 预留1
	EFFECT_EMPTY2 = 6, // 预留2
	EFFECT_EMPTY3 = 7, // 预留3
	EFFECT_EMPTY4 = 8, // 预留4
	EFFECT_EMPTY5 = 9, // 预留5
	EFFECT_EMPTY6 = 10, // 预留6
	EFFECT_HEAL = 11, // 恢复健康

};

enum MINI_GAME_TYPE {
	MINI_GAME_TYPE_NONE = 0, // 无
	MINI_GAME_TYPE_JIGSAW = 1, // 拼图
	MINI_GAME_TYPE_FINDDIFFERENT = 2, // 找不同
	MINI_GAME_TYPE_CROSSLIGHT = 3, // 十字灯
	MINI_GAME_TYPE_MAHJONG = 4, // 连连看
	MINI_GAME_TYPE_ONEPEN = 5, // 一笔画
	MINI_GAME_TYPE_CATCHITEM = 6, // 接道具
	MINI_GAME_TYPE_FINDGOODS = 7, // 找道具
	MINI_GAME_TYPE_FINDSAME = 8, // 记忆翻格子
	MINI_GAME_TYPE_GAME2048 = 9, // 2048
	MINI_GAME_TYPE_DOLL = 10, // 抓娃娃机
	MINI_GAME_TYPE_CUTTING = 11, // 刀工小试
	MINI_GAME_TYPE_ORIGINS = 12, // 美食溯源
	MINI_GAME_TYPE_DISASSEMBLE = 13, // 厨艺拆解
	MINI_GAME_TYPE_SLOT = 14, // 拉霸机
	MINI_GAME_TYPE_EGG_MACHINE = 15, // 扭蛋机
	MINI_GAME_TYPE_REDCARPET = 16, // 走红毯
	MINI_GAME_TYPE_RHYTHM = 17, // 节奏达人
	MINI_GAME_TYPE_DRESS = 18, // 抓礼服
	MINI_GAME_TYPE_DRUMBEAT = 19, // 跟上鼓点
	MINI_GAME_TYPE_QUESTION = 20, // 智慧问答
	MINI_GAME_TYPE_CROSSWALL = 21, // 动感过墙
	MINI_GAME_TYPE_SCULPTURE = 22, // 雕塑
	MINI_GAME_TYPE_PAINT = 23, // 喷绘
	MINI_GAME_TYPE_KLOTSKI = 24, // 华容道
	MINI_GAME_TYPE_DANCE_TOGETHER = 25, // 共舞寻缘
	MINI_GAME_TYPE_SCENE_JIGSAW = 26, // 实景拼图
	MINI_GAME_TYPE_MORA = 27, // NPC猜拳
	MINI_GAME_TYPE_SCRABBLE = 28, // 春联
	MINI_GAME_TYPE_PIANO = 29, // 钢琴
	MINI_GAME_TYPE_COUNT = 30, // 

};

enum MINI_GAME_BROADCAST_TYPE {
	MINI_GAME_BROADCAST_TYPE_NONE = 0, // 不广播
	MINI_GAME_BROADCAST_TYPE_TEAM = 1, // 队伍广播
	MINI_GAME_BROADCAST_TYPE_AROUND = 2, // 附近广播

};

enum SKILLMATTER_TYPE {
	SKILLMATTER_TYPE_DEFAULT = 0, // 普通
	SKILLMATTER_TYPE_COLD = 1, // 冷饮
	SKILLMATTER_TYPE_HOT = 2, // 热饮

};

enum INTERACTION_PRIOR {
	INTERACTION_PRIOR_0 = 0, // 使用动作
	INTERACTION_PRIOR_1 = 1, // 使用filter1
	INTERACTION_PRIOR_2 = 2, // 使用filter2
	INTERACTION_PRIOR_3 = 3, // 使用filter3
	INTERACTION_PRIOR_4 = 4, // 使用filter4
	INTERACTION_PRIOR_5 = 5, // 使用filter5
	INTERACTION_PRIOR_6 = 6, // 使用filter6
	INTERACTION_PRIOR_7 = 7, // 使用filter7
	INTERACTION_PRIOR_8 = 8, // 使用filter8
	INTERACTION_PRIOR_TOTAL = 9, // 

};

enum CAREER_TYPE {
	CAREER_TYPE_COOK = 1, // 料理身份
	CAREER_TYPE_STAR = 2, // 明星身份
	CAREER_TYPE_CAREER2 = 3, // 明星身份
	CAREER_TYPE_CAREER3 = 4, // 艺术家身份
	CAREER_TYPE_CAREER4 = 5, // 预留身份1
	CAREER_TYPE_CAREER5 = 6, // 预留身份2
	CAREER_TYPE_SHOP = 7, // 小店身份
	CAREER_TYPE_CUTE_PET = 8, // 萌宠培育

};

enum CAREER_ABILITY_TYPE {
	CAREER_ABILITY_COOK_MATERIAL = 1, // 食材
	CAREER_ABILITY_COOK_CUISINE = 2, // 烹饪
	CAREER_ABILITY_COOK_BOOK = 3, // 食谱
	CAREER_ABILITY_STAR_TALENT = 4, // 才艺
	CAREER_ABILITY_STAR_APPERANCE = 5, // 形象
	CAREER_ABILITY_STAR_SHOW = 6, // 演出
	CAREER_ABILITY_RESERVE_1 = 7, // 预留1
	CAREER_ABILITY_RESERVE_2 = 8, // 预留2
	CAREER_ABILITY_RESERVE_3 = 9, // 预留3
	CAREER_ABILITY_SHOP_PHOTO = 10, // 摄影
	CAREER_ABILITY_SHOP_CUT = 11, // 剪裁
	CAREER_ABILITY_SHOP_PAINT = 12, // 喷绘
	CAREER_ABILITY_STAR_CARVE = 13, // 雕刻

};

enum COOK_MATERIAL_TYPE {
	COOK_MATERIAL_TYPE_0 = 0, // 辅料
	COOK_MATERIAL_TYPE_1 = 1, // 谷物
	COOK_MATERIAL_TYPE_2 = 2, // 肉类
	COOK_MATERIAL_TYPE_3 = 3, // 蔬菜
	COOK_MATERIAL_TYPE_4 = 4, // 水果
	COOK_MATERIAL_TYPE_5 = 5, // 水产
	COOK_MATERIAL_TYPE_6 = 6, // 饮料
	COOK_MATERIAL_TYPE_7 = 7, // 伴侣
	COOK_MATERIAL_TYPE_8 = 8, // 调料

};

enum RECIPE_TYPE {
	RECIPE_TYPE_NORMAL = 0, // 普通
	RECIPE_TYPE_COOK = 1, // 料理
	RECIPE_TYPE_SHOP_COOK = 2, // 小店料理
	RECIPE_TYPE_WINTER = 2, // 冬日计划

};

enum COOKBOOK_LEARN_TYPE {
	COOKBOOK_LEARN_AUTO = 0, // 到等级自动学会
	COOKBOOK_LEARN_IPAD = 1, // IPAD
	COOKBOOK_LEARN_TV = 2, // 看电视
	COOKBOOK_LEARN_READ = 3, // 看书
	COOKBOOK_LEARN_SPECIAL = 4, // 自研(默认不显示)
	COOKBOOK_LEARN_TASK = 5, // 任务解锁(默认不显示)

};

enum COOKBOOK_TYPE {
	COOKBOOK_TYPE_0 = 0, // 主食
	COOKBOOK_TYPE_1 = 1, // 主菜
	COOKBOOK_TYPE_2 = 2, // 副菜
	COOKBOOK_TYPE_3 = 3, // 甜点
	COOKBOOK_TYPE_4 = 4, // 头盘
	COOKBOOK_TYPE_5 = 5, // 汤饮

};

enum COOKBOOK_STYLE {
	COOKBOOK_STYLE_0 = 0, // 中国菜
	COOKBOOK_STYLE_1 = 1, // 德国菜
	COOKBOOK_STYLE_2 = 2, // 法国菜
	COOKBOOK_STYLE_3 = 3, // 日本菜
	COOKBOOK_STYLE_4 = 4, // 东南亚菜
	COOKBOOK_STYLE_5 = 5, // 意大利菜
	COOKBOOK_STYLE_6 = 6, // 酒水饮料

};

enum ABILITY_LINK_TYPE {
	ABILITY_LINK_TYPE_0 = 0, // 类型1
	ABILITY_LINK_TYPE_1 = 1, // 类型2
	ABILITY_LINK_TYPE_2 = 2, // 类型3
	ABILITY_LINK_TYPE_3 = 3, // 类型4
	ABILITY_LINK_TYPE_4 = 4, // 类型5
	ABILITY_LINK_TYPE_5 = 5, // 类型6

};

enum PARTNER_CHAT_STATE {
	STATE_NONE = 0, // 无状态
	STATE_SELFINPUT = 1, // 自己说话
	STATE_TARGETINPUT = 2, // 对方说话
	STATE_END = 3, // 结束聊天

};

enum PARTNER_CHAT_STATE_START {
	STATE_SELFINPUT_START = 1, // 自己说话
	STATE_TARGETINPUT_START = 2, // 对方说话

};

enum GUARD_SKILL_TYPE {
	GUARD_SKILL_PASSIVE = 0, // 被动技能
	GUARD_SKILL_ASSIST = 1, // 辅助技能

};

enum GUARD_PLAYER_PROP_TYPE {
	GUARD_ATTACK_PHYSIC = 0, // 物理攻击
	GUARD_ATTACK_MAGIC = 1, // 法术攻击
	GUARD_ATTACK_MAIN = 2, // 物理和法术攻击的最大值

};

enum SPEAK_TYPE_DEFINE {
	SPEAK_TYPE_NONE = -1, // 空
	SPEAK_TYPE_CHAT = 0, // 普通喊话
	SPEAK_TYPE_TIP = 1, // 提示
	SPEAK_TYPE_STORY = 2, // 剧情
	SPEAK_TYPE_FLOWER = 3, // 送花
	SPEAK_TYPE_CALLBOARD = 4, // 跑马灯
	SPEAK_TYPE_FLASHTIP = 5, // flashtip
	SPEAK_TYPE_BROADCAST = 6, // 用类似广播的方式喊
	SPEAK_TYPE_STORY_TITLE = 7, // 章回剧情标题
	SPEAK_TYPE_MARRIAGE = 8, // 结婚
	SPEAK_TYPE_DUEL = 9, // 约战
	SPEAK_TYPE_BULLET = 10, // 弹幕
	SPEAK_TYPE_RED_POCKET = 11, // 主界面红包
	SPEAK_TYPE_REGION = 12, // 副本
	SPEAK_TYPE_TOPTIP = 13, // 通知
	SPEAK_TYPE_TEAMRANK = 14, // 组队排名称号

};

enum AI_SIMPLE_POLICY_CONDITION_TYPE {
	AI_SIMPLE_POLICY_CONDITION_NONE = 0, // 空
	AI_SIMPLE_POLICY_CONDITION_BORN = 1, // 出生
	AI_SIMPLE_POLICY_CONDITION_DIE = 2, // 死亡
	AI_SIMPLE_POLICY_CONDITION_START_ATTACK = 3, // 进入战斗
	AI_SIMPLE_POLICY_CONDITION_HP_PERCENT_LESS_THAN = 4, // 血量百分比少于

};

enum AI_SIMPLE_POLICY_TARGET_TYPE {
	AI_SIMPLE_POLICY_TARGET_NONE = 0, // 空
	AI_SIMPLE_POLICY_TARGET_SELF = 1, // 对自身执行
	AI_SIMPLE_POLICY_TARGET_FIRST_AGGRO = 2, // 对第一仇恨目标执行

};

enum AI_SIMPLE_POLICY_OPERATION_TYPE {
	AI_SIMPLE_POLICY_OPERATION_NONE = 0, // 空
	AI_SIMPLE_POLICY_OPERATION_CAST_SKILL = 1, // 对目标使用技能
	AI_SIMPLE_POLICY_OPERATION_CAST_TARGET_POS_SKILL = 2, // 对目标所在的位置使用技能
	AI_SIMPLE_POLICY_OPERATION_SPEAK = 3, // 喊话
	AI_SIMPLE_POLICY_OPERATION_ACTIVE_CONTROLLER = 4, // 开关控制器

};

enum BLOOD_EQUIPMENT_TYPE {
	BLOOD_EQUIPMENT_PVE = 0, // PVE
	BLOOD_EQUIPMENT_PVP = 1, // PVP

};

enum BLOOD_EQUIPMENT_SLOT_ENUM {
	BLOOD_EQUIPMENT_SLOT_1 = 0, // 部位1
	BLOOD_EQUIPMENT_SLOT_2 = 1, // 部位2
	BLOOD_EQUIPMENT_SLOT_3 = 2, // 部位3
	BLOOD_EQUIPMENT_SLOT_4 = 3, // 部位4
	BLOOD_EQUIPMENT_SLOT_5 = 4, // 部位5
	BLOOD_EQUIPMENT_SLOT_6 = 5, // 部位6

};

enum RECHARGE_SERVICE_CONFIG_TYPE {
	RECHARGE_SERVICE_CONFIG_NULL = 0, // 空
	RECHARGE_SERVICE_CONFIG_FIRST_RECHARGE = 1, // 首充服务
	RECHARGE_SERVICE_CONFIG_GIFTS = 2, // 礼包服务
	RECHARGE_SERVICE_CONFIG_LEVEL_FUND = 3, // 成长基金
	RECHARGE_SERVICE_CONFIG_QUARTER_CARD = 4, // 季卡
	RECHARGE_SERVICE_CONFIG_TRIGGER_GIFT = 5, // 触发奖励
	RECHARGE_SERVICE_CONFIG_ACHIEVEMENT_RECHARGE = 6, // 日服等级充值礼包
	RECHARGE_SERVICE_CONFIG_SPECIAL_CARD = 7, // 特殊周月卡

};

enum HOME_BUILD_CLASS {
	HOME_BUILD_NONE = 0, // 无
	HOME_BUILD_WALL = 1, // 墙壁
	HOME_BUILD_FLOOR = 2, // 地板
	HOME_BUILD_WALLPAPER = 3, // 墙纸
	HOME_BUILD_DOOR = 4, // 门
	HOME_BUILD_WINDOW = 5, // 窗
	HOME_BUILD_STAIR = 6, // 楼梯
	HOME_BUILD_ROOF = 7, // 屋顶
	HOME_BUILD_POOL = 8, // 泳池
	HOME_BUILD_ROOF_MAT = 9, // 天花板
	HOME_BUILD_TOP_MAT = 10, // 瓦
	HOME_BUILD_BARRIER = 11, // 栅栏

};

enum HOME_FURNITURE_FUNC_CLASS {
	HOME_FURNITURE_FUNC_OTHER = 0, // 杂项
	HOME_FURNITURE_FUNC_CHAIR = 1, // 桌椅
	HOME_FURNITURE_FUNC_REST = 2, // 休息
	HOME_FURNITURE_FUNC_DESK = 3, // 桌柜
	HOME_FURNITURE_FUNC_LIGHT = 4, // 照明
	HOME_FURNITURE_FUNC_CLEAN = 5, // 清洁
	HOME_FURNITURE_FUNC_COOKER = 6, // 餐饮
	HOME_FURNITURE_FUNC_DECORATE = 7, // 装饰
	HOME_FURNITURE_FUNC_ENTERTAINMENT = 8, // 娱乐
	HOME_FURNITURE_FUNC_SKILL = 9, // 技能
	HOME_FURNITURE_FUNC_SOUL_TREE = 10, // 誓约树
	HOME_FURNITURE_FUNC_PARKING = 11, // 停车位
	HOME_FURNITURE_FUNC_GARDENING = 12, // 花坛
	HOME_FURNITURE_FUNC_DIAOXIANG = 13, // 雕像

};

enum HOME_FURNITURE_ROOM_CLASS {
	HOME_FURNITURE_ROOM_LIVING = 0, // 起居室
	HOME_FURNITURE_ROOM_BED = 1, // 卧室
	HOME_FURNITURE_ROOM_KITCHEN = 2, // 厨房
	HOME_FURNITURE_ROOM_TOILET = 3, // 卫生间
	HOME_FURNITURE_ROOM_DINING = 4, // 餐厅
	HOME_FURNITURE_ROOM_STUDY = 5, // 书房

};

enum HOME_FURNITURE_PUT_TYPE {
	HOME_FURNITURE_PUT_GROUND = 0, // 落地
	HOME_FURNITURE_PUT_WALL = 1, // 贴墙
	HOME_FURNITURE_PUT_ROOF = 2, // 吊顶

};

enum HOME_FURNITURE_STACK_TYPE {
	HOME_FURNITURE_STACK_NO = 0, // 不可以叠放
	HOME_FURNITURE_STACK_CAN = 1, // 可以叠放
	HOME_FURNITURE_STACK_MUST = 2, // 必须叠放

};

enum HOME_BUILD_TOOL_TYPE {
	HOME_BUILD_TOOL_NONE = 0, // 无
	HOME_BUILD_TOOL_WALL = 1, // 建造单墙
	HOME_BUILD_TOOL_ROOM = 2, // 建造空房间
	HOME_BUILD_TOOL_CUSTOM = 3, // 生成自定义模板
	HOME_BUILD_TOOL_MODEL = 4, // 生成模型
	HOME_BUILD_TOOL_MAT = 5, // 替换材质

};

enum CHESS_PROF_ENUM {
	CHESS_PROF_TYPE_0 = 0, // 先锋
	CHESS_PROF_TYPE_1 = 1, // 火力
	CHESS_PROF_TYPE_2 = 2, // 能量
	CHESS_PROF_TYPE_3 = 3, // 刺杀
	CHESS_PROF_TYPE_4 = 4, // 掩护
	CHESS_PROF_TYPE_5 = 5, // 支援
	CHESS_PROF_TYPE_COUNT = 6, // 

};

enum CHESS_CAMP_ENUM {
	CHESS_CAMP_TYPE_0 = 0, // 小怪兽
	CHESS_CAMP_TYPE_1 = 1, // 小魔鬼
	CHESS_CAMP_TYPE_2 = 2, // 卡塞尔
	CHESS_CAMP_TYPE_3 = 3, // 蛇岐八家
	CHESS_CAMP_TYPE_4 = 4, // 猛鬼众
	CHESS_CAMP_TYPE_5 = 5, // 孤儿院
	CHESS_CAMP_TYPE_COUNT = 6, // 

};

enum BREED_PROFICIENCY_LEVEL {
	BREED_PROFICIENCY_LEVEL_NONE = 0, // 0级
	BREED_PROFICIENCY_LEVEL_1 = 1, // 1级
	BREED_PROFICIENCY_LEVEL_2 = 2, // 2级
	BREED_PROFICIENCY_LEVEL_3 = 3, // 3级
	BREED_PROFICIENCY_LEVEL_4 = 4, // 4级
	BREED_PROFICIENCY_LEVEL_5 = 5, // 5级
	BREED_PROFICIENCY_LEVEL_6 = 6, // 6级
	BREED_PROFICIENCY_LEVEL_7 = 7, // 7级
	BREED_PROFICIENCY_LEVEL_8 = 8, // 8级
	BREED_PROFICIENCY_LEVEL_9 = 9, // 9级
	BREED_PROFICIENCY_LEVEL_10 = 10, // 10级
	BREED_PROFICIENCY_LEVEL_11 = 11, // 11级
	BREED_PROFICIENCY_LEVEL_12 = 12, // 12级
	BREED_PROFICIENCY_LEVEL_13 = 13, // 13级
	BREED_PROFICIENCY_LEVEL_14 = 14, // 14级
	BREED_PROFICIENCY_LEVEL_15 = 15, // 15级

};

enum CUTE_PET_TYPE {
	CUTE_PET_TYPE_NONE = 0, // 无效类型
	CUTE_PET_TYPE_CAT = 1, // 猫
	CUTE_PET_TYPE_DOG = 2, // 狗
	CUTE_PET_TYPE_COUNT = 2, // 

};

enum CUTE_PET_TYPE_LIMIT {
	CUTE_PET_TYPE_LIMIT_ALL = 0, // 通用
	CUTE_PET_TYPE_LIMIT_CAT = 1, // 猫
	CUTE_PET_TYPE_LIMIT_DOG = 2, // 狗
	CUTE_PET_TYPE_LIMIT_COUNT = 2, // 

};

enum CUTE_PET_GENE_TYPE {
	CUTE_PET_GENE_TYPE_0 = 0, // 无效基因类型
	CUTE_PET_GENE_TYPE_1 = 101, // a
	CUTE_PET_GENE_TYPE_2 = 102, // A
	CUTE_PET_GENE_TYPE_3 = 201, // b
	CUTE_PET_GENE_TYPE_4 = 202, // B
	CUTE_PET_GENE_TYPE_5 = 301, // c
	CUTE_PET_GENE_TYPE_6 = 302, // C
	CUTE_PET_GENE_TYPE_7 = 401, // d
	CUTE_PET_GENE_TYPE_8 = 402, // D
	CUTE_PET_GENE_TYPE_9 = 501, // e
	CUTE_PET_GENE_TYPE_10 = 502, // E
	CUTE_PET_GENE_TYPE_11 = 601, // f
	CUTE_PET_GENE_TYPE_12 = 602, // F
	CUTE_PET_GENE_TYPE_13 = 701, // g
	CUTE_PET_GENE_TYPE_14 = 702, // G
	CUTE_PET_GENE_TYPE_15 = 801, // h
	CUTE_PET_GENE_TYPE_16 = 802, // H
	CUTE_PET_GENE_TYPE_17 = 901, // i
	CUTE_PET_GENE_TYPE_18 = 902, // I
	CUTE_PET_GENE_TYPE_COUNT = 18, // 

};

enum CUTE_PET_VARIETY_GENE_EXPRESS {
	CUTE_PET_VARIETY_GENE_EXPRESS_0 = 0, // 无表达
	CUTE_PET_VARIETY_GENE_EXPRESS_1 = 1, // Aa
	CUTE_PET_VARIETY_GENE_EXPRESS_2 = 2, // Bb
	CUTE_PET_VARIETY_GENE_EXPRESS_3 = 3, // Cc
	CUTE_PET_VARIETY_GENE_EXPRESS_4 = 4, // Dd
	CUTE_PET_VARIETY_GENE_EXPRESS_5 = 5, // Ee
	CUTE_PET_VARIETY_GENE_EXPRESS_6 = 6, // Ff
	CUTE_PET_VARIETY_GENE_EXPRESS_7 = 7, // Gg
	CUTE_PET_VARIETY_GENE_EXPRESS_8 = 8, // Hh
	CUTE_PET_VARIETY_GENE_EXPRESS_9 = 9, // Ii
	CUTE_PET_VARIETY_GENE_EXPRESS_COUNT = 9, // 

};

enum CUTE_PET_GENDER {
	CUTE_PET_GENDER_MALE = 0, // 雄
	CUTE_PET_GENDER_FEMALE = 1, // 雌

};

enum CUTE_PET_NATURE_QUALITY {
	CUTE_PET_NATURE_QUALITY_NONE = 0, // 无效性格品质
	CUTE_PET_NATURE_QUALITY_1 = 1, // 萌宠性格品质1
	CUTE_PET_NATURE_QUALITY_2 = 2, // 萌宠性格品质2
	CUTE_PET_NATURE_QUALITY_3 = 3, // 萌宠性格品质3
	CUTE_PET_NATURE_QUALITY_4 = 4, // 萌宠性格品质4
	CUTE_PET_NATURE_QUALITY_5 = 5, // 萌宠性格品质5
	CUTE_PET_NATURE_QUALITY_COUNT = 5, // 

};

enum CUTE_PET_GROWTH_STAGE {
	CUTE_PET_GROWTH_STAGE_NONE = 0, // 无效阶段
	CUTE_PET_GROWTH_STAGE_BABY = 1, // 幼崽期
	CUTE_PET_GROWTH_STAGE_YOUNGSTERS = 2, // 青少期
	CUTE_PET_GROWTH_STAGE_ADULT = 3, // 成年期
	CUTE_PET_GROWTH_STAGE_OLD = 4, // 老年期
	CUTE_PET_GROWTH_STAGE_COUNT = 4, // 

};

enum OVERCOOK_ITEM_PROCESS_TYPE {
	OVERCOOK_ITEM_PROCESS_TYPE_NONE = 0, // 无效操作
	OVERCOOK_ITEM_PROCESS_TYPE_1 = 1, // 切菜
	OVERCOOK_ITEM_PROCESS_TYPE_2 = 2, // 煮
	OVERCOOK_ITEM_PROCESS_TYPE_3 = 3, // 煎
	OVERCOOK_ITEM_PROCESS_TYPE_4 = 4, // 炒
	OVERCOOK_ITEM_PROCESS_TYPE_5 = 5, // 拼盘
	OVERCOOK_ITEM_PROCESS_TYPE_6 = 6, // 烤
	OVERCOOK_ITEM_PROCESS_TYPE_7 = 7, // 蒸
	OVERCOOK_ITEM_PROCESS_TYPE_8 = 8, // 榨汁机
	OVERCOOK_ITEM_PROCESS_TYPE_9 = 9, // 预留9
	OVERCOOK_ITEM_PROCESS_TYPE_10 = 10, // 预留10
	OVERCOOK_ITEM_PROCESS_TYPE_11 = 11, // 预留11
	OVERCOOK_ITEM_PROCESS_TYPE_COUNT = 11, // 

};

enum CHILD_COLLECTION_TYPE {
	CHILD_COLLECTION_TYPE_1 = 1, // 纪念品
	CHILD_COLLECTION_TYPE_2 = 2, // 明信片
	CHILD_COLLECTION_TYPE_3 = 3, // 龙族遗物
	CHILD_COLLECTION_TYPE_4 = 4, // 时光结晶
	CHILD_COLLECTION_TYPE_COUNT = 4, // 

};

enum DRAGONBORN_PROP_ENUM {
	DRAGONBORN_PROP_0 = 0, // 生命值
	DRAGONBORN_PROP_1 = 1, // 物理攻击
	DRAGONBORN_PROP_2 = 2, // 法术攻击
	DRAGONBORN_PROP_3 = 3, // 物理防御
	DRAGONBORN_PROP_4 = 4, // 法术防御
	DRAGONBORN_PROP_5 = 5, // 暴击抗性
	DRAGONBORN_PROP_6 = 6, // 破甲抗性
	DRAGONBORN_PROP_COUNT = 7, // 

};

enum LOTTERY_GOD_TYPE_SETTING {
	TYPE_SETTING_1 = 1, // 1*1
	TYPE_SETTING_2 = 2, // 2*2
	TYPE_SETTING_3 = 3, // 3*3
	TYPE_SETTING_4 = 4, // 4*4
	TYPE_SETTING_5 = 5, // 5*5
	TYPE_SETTING_6 = 6, // 6*6
	TYPE_SETTING_7 = 7, // 7*7
	TYPE_SETTING_8 = 8, // 8*8
	TYPE_SETTING_9 = 9, // 9*9
	TYPE_SETTING_10 = 10, // 10*10

};

enum HONEY_GARDEN_TOOL_TYPE_ENUM {
	TYPE_SEED = 1, // 种子
	TYPE_FLOWER = 2, // 花朵
	TYPE_FERTILIZE = 3, // 化肥
	TYPE_KETTLE = 4, // 水壶
	TYPE_INSECTICIDE = 5, // 杀虫剂

};

enum ALCHEMY_RUNESTONE_ADDON_PROP_TYPE {
	ATK = 1, // 攻击
	DEF = 2, // 防御(物理)
	HP = 3, // 生命
	CRIT = 4, // 暴击
	MUL_ATK = 5, // 多重
	CRIT_DEF = 6, // 暴抗
	PIERCE = 7, // 破甲
	CDR = 8, // 冷缩
	PIERCE_DEF = 9, // 破甲抗
	CRIT_RATIO = 10, // 暴伤
	MDEF = 11, // 防御(魔法)

};

enum PUT_ITEM_LOCATION {
	IL_EQUIPMENT_1 = 0, // 背包
	IL_BACKPACK = 1, // 背包
	IL_TASK_ITEM = 2, // 任务物品包
	IL_MATERIAL = 3, // 材料物品包(废弃)
	IL_DEPOSITORY = 4, // 仓库
	IL_MAFIA_STORE = 5, // 帮派仓库
	IL_RECYCLE_BIN = 6, // 回收站
	IL_TEMP_BACK = 7, // 临时包裹，过关时获得的奖励会先放在这里
	IL_CHILD_RECYCLE = 8, // 孩子临时包裹
	IL_EQUIPMENT_2 = 9, // 装备2
	IL_PET = 10, // 宠物栏
	IL_CHILD = 11, // 孩子栏
	IL_CHILD_DEPO = 12, // 孩子仓库
	IL_DRAGONBORN = 13, // 龙裔上阵栏
	IL_DRAGONBORN_DEPO = 14, // 龙裔背包
	IL_ALCHEMY_RUNESTONE_DEPOSITORY = 15, // 炼金矩阵符文石仓库
	IL_ALCHEMY_RUNESTONE_EQUIP_LIST = 16, // 炼金矩阵符文石装备槽位
	IL_TOWNLET_DEPO = 17, // 小镇伙伴背包
	IL_HOLY_GHOST_INSCRIPTION_DEPOSITORY = 18, // 英灵圣印仓库
	IL_HOLY_GHOST_INSCRIPTION_EQUIP_LIST = 19, // 英灵圣印装备槽位
	IL_TOWNLET_PET = 20, // 小镇宠物
	IL_GUARD_ASTROLABE_DEPOSITORY = 21, // 宠物星盘仓库
	IL_GUARD_ASTROLABE_EQUIP_LIST = 22, // 宠物星盘装备槽位

};

enum ZSPACE_ITEM_TYPE {
	ZSPACE_NONE = 0, // 默认
	ZSPACE_PHOTO_FRAME = 1, // 相框,壁挂物品
	ZSPACE_CARPET = 2, // 地毯
	ZSPACE_WALL = 3, // 墙壁
	ZSPACE_FLOOR = 4, // 地板
	ZSPACE_DOLLCABINET = 5, // 手办柜

};

enum ZSPACE_PHOTO_CUT_TYPE {
	ZSPACE_NORMAL = 0, // 矩形
	ZSPACE_CIRCLE = 1, // 圆形
	ZSPACE_ELLIPSE = 2, // 椭圆形
	ZSPACE_SQUARE = 3, // 正方形

};

enum TOWNLET_POSTCARD_LOCATION {
	TPL_NONE = 0, // 无
	TPL_BINGYUAN = 1, // 冰原
	TPL_DONGJINGWAIHUAN = 2, // 东京外环
	TPL_DONGJING = 3, // 东京
	TPL_GONGLU = 4, // 公路
	TPL_LUNDUN = 5, // 伦敦
	TPL_XUEYUAN = 6, // 学院
	TPL_YOULUN = 7, // 游轮
	TPL_YOULEYUAN = 8, // 游乐园
	TPL_QUESTION = 9, // ???

};

enum IVTRTYPE_MASK_ENUM {
	IVTRTYPE_MASK_EQUIPPACK = 0x00000001, // Equipment
	IVTRTYPE_MASK_BACKPACK = 0x00000002, // Equipment
	IVTRTYPE_MASK_PACK = 0x00000004, // Normalpack
	IVTRTYPE_MASK_PACK1 = 0x00000008, // Normalpack1
	IVTRTYPE_MASK_PACK2 = 0x00000010, // Normalpack2
	IVTRTYPE_MASK_TASKITEM = 0x00000020, // TaskItempack
	IVTRTYPE_MASK_MATERIAL = 0x00000040, // Materialpack
	IVTRTYPE_MASK_GIFT = 0x00000080, // Giftpack
	IVTRTYPE_MASK_PETPACK = 0x00000100, // Petpack
	IVTRTYPE_MASK_SCENEONLY = 0x00000200, // 场景专用
	IVTRTYPE_MASK_TRASHBOX = 0x00000400, // Trashbox
	IVTRTYPE_MASK_PETSTORAGE = 0x00000800, // Petstorage
	IVTRTYPE_MASK_FACTIONSTORE = 0x00001000, // 帮派包裹
	IVTRTYPE_MASK_RECYCLE = 0x00002000, // Recyclepack
	IVTRTYPE_MASK_ALL = 0xffffffff, // 

};

enum PROFTYPE_MASK {
	PROFTYPEMASK_STANDARD = 0x00000001, // 标准
	PROFTYPEMASK_BLADE = 2, // 刀
	PROFTYPEMASK_SPEAR = 4, // 枪
	PROFTYPEMASK_SWORD = 8, // 剑
	PROFTYPEMASK_RING = 16, // 环
	PROFTYPEMASK_UNBRELLA = 32, // 伞
	PROFTYPEMASK_BOW = 64, // 弓
	PROFTYPEMASK_YULING = 128, // 御灵
	PROFTYPEMASK_TIANSHA = 256, // 天煞
	PROFTYPEMASK_PLAYER_9 = 512, // 逆潮
	PROFTYPEMASK_10 = 1024, // 魔术师
	PROFTYPEMASK_11 = 2048, // 预留11
	PROFTYPEMASK_12 = 4096, // 预留12
	PROFTYPEMASK_13 = 8192, // 预留13
	PROFTYPEMASK_14 = 16384, // 预留14
	PROFTYPEMASK_15 = 32768, // 预留15
	PROFTYPEMASK_16 = 65536, // 预留16
	PROFTYPEMASK_17 = 131072, // 预留17
	PROFTYPEMASK_18 = 262144, // 预留18
	PROFTYPEMASK_19 = 524288, // 预留19
	PROFTYPEMASK_20 = 1048576, // 预留20
	PROFTYPEMASK_BIGWORLD_ELITE = 2097152, // 大地图精英怪
	PROFTYPEMASK_BIGWORLD_BOSS = 4194304, // 大地图BOSS
	PROFTYPEMASK_INSTANCE_NORMAL = 8388608, // 副本普通怪
	PROFTYPEMASK_INSTANCE_ELITE = 16777216, // 副本精英怪
	PROFTYPEMASK_INSTANCE_BOSS = 33554432, // 副本BOSS
	PROFTYPEMASK_WORLD_BOSS = 67108864, // 世界BOSS
	PROFTYPEMASK_MAGIC_DEF_ELITE = 134217728, // 法防精英怪
	PROFTYPEMASK_PHYSIC_DEF_ELITE = 268435456, // 物防精英怪
	PROFTYPEMASK_DOUBLE_DEF_ELITE = 536870912, // 双防精英怪
	PROFTYPEMASK_FORCE_INT = 0x7fffffff, // 

};

enum ACTION_MASK {
	ACTIONMASK_GETOFFRIDE = 0x00000001, // 下坐骑播放动画
	ACTIONMASK_02 = 2, // 预留1
	ACTIONMASK_03 = 4, // 预留2
	ACTIONMASK_04 = 8, // 预留3
	ACTIONMASK_05 = 16, // 预留4
	ACTIONMASK_06 = 32, // 预留5
	ACTIONMASK_07 = 64, // 预留6
	ACTIONMASK_08 = 128, // 预留7
	ACTIONMASK_09 = 256, // 预留8
	ACTIONMASK_10 = 512, // 预留9
	ACTIONMASK_11 = 1024, // 预留10
	ACTIONMASK_12 = 2048, // 预留11
	ACTIONMASK_13 = 4096, // 预留12
	ACTIONMASK_14 = 8192, // 预留13
	ACTIONMASK_15 = 16384, // 预留14
	ACTIONMASK_16 = 32768, // 预留15
	ACTIONMASK_17 = 65536, // 预留16
	ACTIONMASK_18 = 131072, // 预留17
	ACTIONMASK_19 = 262144, // 预留18
	ACTIONMASK_20 = 524288, // 预留19
	ACTIONMASK_21 = 1048576, // 预留20
	ACTIONMASK_FORCE_INT = 0x7fffffff, // 

};

enum ROLE_PHYSIC_MASK {
	ROLE_PHYSIC_MASK_NORMAL = 0x01, // 标准
	ROLE_PHYSIC_MASK_TINY = 0x02, // 小

};

enum EXP_CAMP_MASK {
	EXP_CAMPMASK_INVALID = 0, // EXP_CAMPMASK_INVALID
	EXP_CAMPMASK_PLAYER_1 = 0x00000001, // 并州玩家
	EXP_CAMPMASK_PLAYER_2 = 0x00000002, // 冀州玩家
	EXP_CAMPMASK_PLAYER_3 = 0x00000004, // 豫州玩家
	EXP_CAMPMASK_PLAYER_4 = 0x00000008, // 徐州玩家
	EXP_CAMPMASK_PLAYER_5 = 0x00000010, // 荆州玩家
	EXP_CAMPMASK_PLAYER_6 = 0x00000020, // 益州玩家
	EXP_CAMPMASK_NPC_1 = 0x00000040, // 并州NPC
	EXP_CAMPMASK_NPC_2 = 0x00000080, // 冀州NPC
	EXP_CAMPMASK_NPC_3 = 0x00000100, // 豫州NPC
	EXP_CAMPMASK_NPC_4 = 0x00000200, // 徐州NPC
	EXP_CAMPMASK_NPC_5 = 0x00000400, // 荆州NPC
	EXP_CAMPMASK_NPC_6 = 0x00000800, // 益州NPC
	EXP_CAMPMASK_NEUTRAL_NPC = 0x00001000, // 中立NPC
	EXP_CAMPMASK_FRIEND_NPC = 0x00002000, // 友善NPC
	EXP_CAMPMASK_GUARD = 0x00004000, // 卫兵NPC
	EXP_CAMPMASK_ENEMY_MON1 = 0x00008000, // 邪恶怪物1
	EXP_CAMPMASK_ENEMY_MON2 = 0x00010000, // 邪恶怪物2
	EXP_CAMPMASK_IN_PVP = 0x00020000, // PK状态玩家
	EXP_CAMPMASK_YELLOW_NAME = 0x00040000, // 黄名玩家
	EXP_CAMPMASK_RED_NAME = 0x00080000, // 红名玩家
	EXP_CAMPMASK_BATFIEL_A_PLAYER = 0x00100000, // 活动攻方玩家
	EXP_CAMPMASK_BATFIEL_A_NPC = 0x00200000, // 活动攻方NPC
	EXP_CAMPMASK_BATFIEL_B_PLAYER = 0x00400000, // 活动守方玩家
	EXP_CAMPMASK_BATFIEL_B_NPC = 0x00800000, // 活动守方NPC
	EXP_CAMPMASK_TASK_TRANSFORM_1 = 0x01000000, // 变身任务阵营1
	EXP_CAMPMASK_ESCORT_NPC = 0x02000000, // 可战斗的护送NPC
	EXP_CAMPMASK_BATFIEL_C_PLAYER = 0x04000000, // 活动攻方玩家2
	EXP_CAMPMASK_BATFIEL_D_PLAYER = 0x08000000, // 活动守方玩家2
	EXP_CAMPMASK_29 = 0x10000000, // 待定
	EXP_CAMPMASK_30 = 0x20000000, // 待定
	EXP_CAMPMASK_31 = 0x40000000, // 待定
	EXP_CAMPMASK_32 = 0x80000000, // 待定
	EXP_CAMPMASK_NUM = 32, // 
	EXP_CAMPMASK_FORCE_INT = 0x7fffffff, // 
	EXP_CAMP_MASK_PLAYER_NATION = 63, // EXP_CAMP_MASK_PLAYER_NATION
	EXP_CAMP_MASK_NPC_NATION = 4032, // EXP_CAMP_MASK_NPC_NATION
	EXP_CAMP_MASK_OBJECT_NATION = 4095, // EXP_CAMP_MASK_OBJECT_NATION

};

enum EXP_SECTION_MASK {
	EXP_SECTIONMASK_0 = 0x00000001, // MASK_0
	EXP_SECTIONMASK_1 = 0x00000002, // MASK_1
	EXP_SECTIONMASK_2 = 0x00000004, // MASK_2
	EXP_SECTIONMASK_3 = 0x00000008, // MASK_3
	EXP_SECTIONMASK_4 = 0x00000010, // MASK_4
	EXP_SECTIONMASK_5 = 0x00000020, // MASK_5
	EXP_SECTIONMASK_6 = 0x00000040, // MASK_6
	EXP_SECTIONMASK_7 = 0x00000080, // MASK_7
	EXP_SECTIONMASK_8 = 0x00000100, // MASK_8
	EXP_SECTIONMASK_9 = 0x00000200, // MASK_9
	EXP_SECTIONMASK_10 = 0x00000400, // MASK_10
	EXP_SECTIONMASK_11 = 0x00000800, // MASK_11
	EXP_SECTIONMASK_12 = 0x00001000, // MASK_12
	EXP_SECTIONMASK_13 = 0x00002000, // MASK_13
	EXP_SECTIONMASK_14 = 0x00004000, // MASK_14
	EXP_SECTIONMASK_15 = 0x00008000, // MASK_15
	EXP_SECTIONMASK_16 = 0x00010000, // MASK_16
	EXP_SECTIONMASK_17 = 0x00020000, // MASK_17
	EXP_SECTIONMASK_18 = 0x00040000, // MASK_18
	EXP_SECTIONMASK_19 = 0x00080000, // MASK_19
	EXP_SECTIONMASK_20 = 0x00100000, // MASK_20
	EXP_SECTIONMASK_21 = 0x00200000, // MASK_21
	EXP_SECTIONMASK_22 = 0x00400000, // MASK_22
	EXP_SECTIONMASK_23 = 0x00800000, // MASK_23
	EXP_SECTIONMASK_24 = 0x01000000, // MASK_24
	EXP_SECTIONMASK_25 = 0x02000000, // MASK_25
	EXP_SECTIONMASK_26 = 0x04000000, // MASK_26
	EXP_SECTIONMASK_27 = 0x08000000, // MASK_27
	EXP_SECTIONMASK_28 = 0x10000000, // MASK_28
	EXP_SECTIONMASK_29 = 0x20000000, // MASK_29
	EXP_SECTIONMASK_30 = 0x40000000, // MASK_30
	EXP_SECTIONMASK_31 = 0x80000000, // MASK_31
	EXP_SECTIONMASK_NUM = 32, // 
	EXP_SECTIONMASK_FORCE_INT = 0x7fffffff, // 

};

enum MONSTER_LIFE_MASK {
	MONSTER_LIFE_NORMAL = 0x00000000, // 普通
	MONSTER_LIFE_UNATTACKABLE = 0x00000001, // 不能被攻击
	MONSTER_LIFE_UNTARGETABLE = 0x00000002, // 不可被选中
	MONSTER_LIFE_STATUE = 0x00000004, // 雕像（特殊定义）
	MONSTER_LIFE_LIFENESS = 0x00000008, // 无生命（不自动转向，无表情回应等)
	MONSTER_LIFE_UNSHOWHINT = 0x00000010, // 不显示悬浮
	MONSTER_LIFE_UNSHOWNAME = 0x00000020, // 不显示名称
	MONSTER_LIFE_UNSHOWBARS = 0x00000040, // 不显示血条
	MONSTER_LIFE_UNSHOWDAMAGE = 0x00000080, // 不显示伤害冒字
	MONSTER_LIFE_NO_DMGQUIVER = 0x00000100, // 无受伤抖动
	MONSTER_LIFE_NOSHOW_IN_UIMAP = 0x00000200, // 界面地图上不显示
	MONSTER_LIFE_COLLIDE = 0x00000400, // 参与碰撞
	MONSTER_LIFE_UNCLICKABLE = 0x00000800, // 不可被点击
	MONSTER_LIFE_NOTPLAY_ACT_VOICE = 0x00001000, // 不播动作条上的语音(_Voice_关键字)
	MONSTER_LIFE_UNSHOWHEAD = 0x00002000, // 不显示头像和头像血条
	MONSTER_LIFE_UNSHOW_LEVEL = 0x00004000, // 不显示等级
	MONSTER_LIFE_FORCE_NOT_DYNOPT = 0x00008000, // 强制不动态优化掉模型
	MONSTER_LIFE_PRETEND_HOSTPLAYER = 0x00010000, // 伪装成主角模型
	MONSTER_LIFE_NO_DIEBACK = 0x00020000, // 无死亡击退
	MONSTER_LIFE_AS_BOSS = 0x00040000, // 作为boss（音效等）
	MONSTER_LIFE_DIE_RAGDOLL = 0x00080000, // 开启物理死亡ragdoll
	MONSTER_LIFE_BREAKABLE = 0x00100000, // 可破坏物
	MONSTER_LIFE_SKILLFX_PRIO_HIGH = 0x00200000, // 技能光效用最高等级(慎用！)
	MONSTER_LIFE_HITFX_PRIO_HIGH = 0x00400000, // 击中主角的光效用最高等级，击中别人的用中等级(慎用！）
	MONSTER_LIFE_NO_SEL_FX = 0x00800000, // 不显示选中光效（仅限巨型BOSS）
	MONSTER_LIFE_FORCE_SHOWBAR = 0x01000000, // 强制显示头顶血条
	MONSTER_LIFE_NO_HIT_MATERIAL = 0x02000000, // 无击中闪白

};

enum MATTER_MISC_MASK {
	MATTER_NONE = 0x00000000, // 无
	MATTER_FORCE_NOT_DYNOPT = 0x00000001, // 强制不动态优化掉模型
	MATTER_TOWNLET_OWNER_VISIBLE = 0x00000002, // 只有小镇拥有者才显示

};

enum NPC_MISC_MASK {
	NPCMISCMASK_NORMAL = 0x00000000, // 普通
	NPCMISCMASK_CUSTOMANIM = 0x00000001, // 有特殊动作（时间天气等）
	NPCMISCMASK_NO_TONEMAPPER = 0x00000002, // 对话时禁止开启tonemapper效果
	NPCMISCMASK_HIDE_LV = 0x00000004, // 选中不显示等级
	NPCMISCMASK_BLOCK_CAMERA = 0x00000008, // 镜头不可穿入
	NPCMISCMASK_SHOW_QUICKBTN = 0x00000010, // 显示快速交互按钮
	NPCMISCMASK_HIDE_SELECTED_FX = 0x00000020, // 隐藏选中的特效
	NPCMISCMASK_HIDE_PATE = 0x00000040, // 隐藏头顶Pate
	NPCMISCMASK_PHONE_TASK_NPC = 0x00000080, // 手机通讯任务专用NPC(镜头距离,对话方向,在手机通讯场景生效)
	NPCMISCMASK_TOWNLET_OWNER_VISIBLE = 0x00000100, // 只有小镇拥有者才显示
	NPCMISCMASK_SHOW_MIDMAP = 0x00000200, // 在小地图中显示NPC

};

enum ITEM_PROC_TYPE {
	IPT_NULL_1 = 0x00000001, // no use
	IPT_NODESTROY = 0x00000002, // 不允许摧毁
	IPT_NOSELLTONPC = 0x00000004, // 无法卖给NPC
	IPT_NULL_2 = 0x00000008, // no use
	IPT_NULL_3 = 0x00000010, // no use
	IPT_BROADCAST = 0x00000020, // 广播物品
	IPT_NULL_4 = 0x00000040, // no use
	IPT_NULL_5 = 0x00000080, // no use
	IPT_NULL_6 = 0x00000100, // no use
	IPT_NULL_7 = 0x00000200, // no use
	IPT_NULL_8 = 0x00000400, // no use
	IPT_NO_SPLIT = 0x00000800, // 不可拆分和堆叠
	IPT_AUTO_DEL = 0x00001000, // 离开区域自动删除且不存盘？
	IPT_NULL_9 = 0x00002000, // use
	IPT_NULL_10 = 0x00004000, // use
	IPT_NULL_11 = 0x00008000, // use
	IPT_TEMP = 0x00010000, // 临时道具标志
	IPT_NULL_12 = 0x00020000, // use
	IPT_LEAVE_SCENE_DROP = 0x00040000, // 离开场景掉落
	IPT_NULL_13 = 0x00080000, // use
	IPT_NULL_14 = 0x00100000, // use
	IPT_NULL_15 = 0x10000000, // use
	IPT_NULL_16 = 0x20000000, // use
	IPT_NULL_17 = 0x40000000, // use
	IPT_NULL_18 = 0x80000000, // use

};

enum COMBINESEV1_MASK {
	CMBSEV_CLEAR_PROFLEVEL = 0x00000001, // 清职业等级
	CMBSEV_SELECT_PROF = 0x00000002, // 选主、副职业服务
	CMBSEV_COOK = 0x00000004, // 料理服务
	CMBSEV_SWORN = 0x00000008, // 结义服务
	CMBSEV_FACTION = 0x00000010, // 帮派服务
	CMBSEV_STOREHOUSE = 0x00000020, // 仓库服务
	CMBSEV_MAIL = 0x00000040, // 邮寄服务
	CMBSEV_AUCTION = 0x00000080, // 拍卖服务
	CMBSEV_RECIPE_RESEARCH_INSTANCE = 0x00000100, // 自研食谱进入副本
	CMBSEV_BUDDY_LEARNSKILL = 0x00000200, // 宠物学技能服务
	CMBSEV_PET_ARTIFICE = 0x00000400, // 宠物换血服务
	CMBSEV_OVERCOOK = 0x00000800, // 分手厨房服务
	CMBSEV_LEAVEBATTLE = 0x00001000, // 离开战场服务
	CMBSEV_HUNT_DRAGON = 0x00002000, // 猎龙服务
	CMBSEV_FACTION_STORAGE = 0x00004000, // 帮派仓库服务
	CMBSEV_TEAM_PLATFORM = 0x00008000, // 组队平台前往匹配
	CMBSEV_EQUIP_UPGRADE = 0x00010000, // 装备成长服务
	CMBSEV_EQUIP_UPGRADECLEAR = 0x00020000, // 取消装备成长服务
	CMBSEV_EQUIP_HOLE = 0x00040000, // 打孔服务（已废弃）
	CMBSEV_EQUIP_ENCHASE = 0x00080000, // 镶嵌服务
	CMBSEV_EQUIP_TAKEOUT = 0x00100000, // 拆卸服务
	CMBSEV_EQUIP_ATTACH = 0x00200000, // 附加属性服务
	CMBSEV_EQUIP_ATTACHCLEAR = 0x00400000, // 拆除附加属性服务
	CMBSEV_EQUIP_ENCHANT = 0x00800000, // 附魔服务
	CMBSEV_EQUIP_SEPERATE = 0x01000000, // 拆分服务
	CMBSEV_BIND = 0x02000000, // 严格绑定服务
	CMBSEV_BINDCLEAR = 0x04000000, // 取消严格绑定服务
	CMBSEV_BINDRESTORE = 0x08000000, // 恢复严格绑定服务
	CMBSEV_BATTLEINFO = 0x10000000, // 剧本服务
	CMBSEV_EQUIP_REINFORCE = 0x20000000, // 强化服务
	CMBSEV_EQUIP_KAIGUANG = 0x40000000, // 开光服务
	CMBSEV_CHANGE_PROF = 0x80000000, // 转职服务
	CMBSEV_FORCE_INT = 0x7fffffff, // 

};

enum COMBINESEV2_MASK {
	CMBSEV2_EQUIPATTACHTRANSFER = 0x00000001, // 追加属性转移服务
	CMBSEV2_EQUIPTRANSFORM = 0x00000002, // 装备变形服务
	CMBSEV2_NATION_ESCROT_CHANGE_CARGO = 0x00000004, // 运镖换镖车
	CMBSEV2_HORSEEQUIP = 0x00000008, // 骑乘道具装配
	CMBSEV2_HORSETRANSFER = 0x00000010, // 骑乘道具转移
	CMBSEV2_NPCBUY_NORMALITEM = 0x00000020, // 回收普通道具
	CMBSEV2_NPCBUY_SPECITEM = 0x00000040, // 回收特殊道具
	CMBSEV2_NPCBUY_NORMALEQUIP = 0x00000080, // 回收普通装备
	CMBSEV2_NPCBUY_SPECEQUIP = 0x00000100, // 回收特殊装备
	CMBSEV2_NPC_REPAIR = 0x00000200, // 提供修理
	CMBSEV2_MARRIAGE = 0x00000400, // 结婚服务
	CMBSEV2_WORLDTRANSMIT = 0x00000800, // 大世界传送服务
	CMBSEV2_PET_RECYCLE = 0x00001000, // 宠物回收服务
	CMBSEV2_GET_BUDDY_IDCARD = 0x00002000, // 领取伙伴身份证
	CMBSEV2_FACELIFT = 0x00004000, // 重塑容貌服务
	CMBSEV2_SENDGIFT = 0x00008000, // 赠送礼物服务
	CMBSEV2_PET_DEPOSIT = 0x00010000, // 宠物寄存
	CMBSEV2_HOMELAND_BUY = 0x00020000, // 家园服务-->家园购买服务
	CMBSEV2_DOUBLEEXP = 0x00040000, // 领双倍经验服务
	CMBSEV2_PET_BLOOD_IDENTIFY = 0x00080000, // 宠物血统鉴定服务
	CMBSEV2_WEDDING_CELEBRATE_APPLY = 0x00100000, // 婚庆申请
	CMBSEV2_WEDDING_CELEBRATE_TRANSMIT = 0x00200000, // 婚庆传送
	CMBSEV2_SECT_HELP = 0x00400000, // 门派互助
	CMBSEV2_CHANGE_CUR_LIFEPROF = 0x00800000, // 更换当前差事
	CMBSEV2_FACTION_GRANT_BENIFIT = 0x01000000, // 帮派发放福利
	CMBSEV2_ALLIANCEWAR = 0x02000000, // 盟主战服务
	CMBSEV2_SUB_FACTION_APPLY = 0x04000000, // 分舵申请
	CMBSEV2_TRANSFER_TO_FACTION = 0x08000000, // 传送回帮
	CMBSEV2_TRANSFER_TO_THISMAP_BASE = 0x10000000, // 传送本地图基地
	CMBSEV2_FACTIONBASE_BUILD = 0x20000000, // 帮会基地建设
	CMBSEV2_FACTIONBASE_RECOVER = 0x40000000, // 帮会基地恢复
	CMBSEV2_HOMELAND_TRANSFER = 0x80000000, // 家园传送服务
	CMBSEV2_FORCE_INT = 0x7fffffff, // 

};

enum COMBINESEV3_MASK {
	COMBSEV3_GET_DART = 0x00000001, // 收回镖货
	COMBSEV3_DONGJING_CENTER_BATTLE = 0x00000002, // 东京跨服白月境
	COMBSEV3_QIANHE_CENTER_BATTLE = 0x00000004, // 千鹤町跨服白月境
	COMBSEV3_NORMAL_MARRIAGE_PARADE = 0x00000008, // 花轿巡游服务
	COMBSEV3_HOME_PLANT = 0x00000010, // 花园种菜
	COMBSEV3_WORLD_INFO_MANAGEMENT = 0x00000020, // 江湖消息管理
	COMBSEV3_REWARD_TASK_PUBLISH = 0x00000040, // 发布赏金任务
	COMBSEV3_REWARD_TASK_OPEN = 0x00000080, // 打开赏金任务榜
	COMBSEV3_REWARD_TASK_PAY = 0x00000100, // 结算奖励
	COMBSEV3_TREASUREHOUSE_MANAGE = 0x00000200, // 藏宝阁管理
	COMBSEV3_TREASUREHOUSE_TRAPSETTING = 0x00000400, // 藏宝阁陷阱设置
	COMBSEV3_TREASUREHOUSE_RECEIVE_GAIN = 0x00000800, // 藏宝阁增益领取
	COMBSEV3_TREASUREHOUSE_STEAL = 0x00001000, // 盗宝
	COMBSEV3_EQUIP_TREASURE_UPGRADE = 0x00002000, // 包裹内宝物盘养
	COMBSEV3_PET_XISUI = 0x00004000, // 宠物洗髓服务
	COMBSEV3_SELL_REWARD = 0x00008000, // 奖励出售
	COMBSEV3_INTIMATE = 0x00010000, // 情缘服务
	COMBSEV3_MASTER_DECIPLE = 0x00020000, // 师徒服务
	COMBSEV3_UPGRADE_DONATION = 0x00040000, // 升级物资捐赠
	COMBSEV3_TIGUAN = 0x00080000, // 踢馆
	COMBSEV3_INQUIRY_INTERSERVER_TIME = 0x00100000, // 剩余跨服时间查询
	COMBSEV3_WEDDING = 0x00200000, // 婚礼服务
	COMBSEV3_AUCTION = 0x00400000, // 拍卖
	COMBSEV3_FEED_HOLYBOSS = 0x00800000, // 喂养圣兽
	COMBSEV3_OPEN_CAMPFIRE = 0x01000000, // 开启篝火
	COMBSEV3_DAILY_INSTANCE = 0x02000000, // 每日副本
	COMBSEV3_DREAM_INSTANCE = 0x04000000, // 前尘旧梦
	COMBSEV3_GOLD_SHOP = 0x08000000, // 商铺服务
	COMBSEV3_TOWER = 0x10000000, // 爬塔服务
	COMBSEV3_MARRIAGE_TIME_REWARD = 0x20000000, // 结婚纪念日
	COMBSEV3_FACTION_TREE = 0x40000000, // 帮派种树活动
	COMBSEV3_REQUEST_PK = 0x80000000, // 约战服务
	COMBSEV3_FORCE_INT = 0x7fffffff, // 

};

enum COMBINESEV4_MASK {
	COMBSEV4_PREGNATE = 0x00000001, // 怀孕服务
	COMBSEV4_ADOPTION_BABY = 0x00000002, // 领养孩子
	COMBSEV4_BEAR_BABY = 0x00000004, // 生育孩子
	COMBSEV4_STORE_BABY = 0x00000008, // 寄存孩子
	COMBSEV4_BOAT_PARADE = 0x00000010, // 灵魂伴侣巡游
	COMBSEV4_FLYSOWRD_PARADE = 0x00000020, // 飞剑巡游
	COMBSEV4_WEDDING_VOW = 0x00000040, // 婚礼宣誓
	COMBSEV4_SECOND_HOME_TRANS = 0x00000080, // 第二家园传送
	COMBSEV4_FACTION_HERO_AWARD = 0x00000100, // 帮派勇者赛领奖
	COMBSEV4_BABY_MARRIAGE = 0x00000200, // 孩子结亲
	COMBSEV4_BABY_DIVORCE = 0x00000400, // 孩子离婚
	COMBSEV4_CHANGE_RACE = 0x00000800, // 转换种族服务
	COMBSEV4_DUOQI_BATTLE = 0x00001000, // 夺旗新战场
	COMBSEV4_CENTER_SERVER_BATTLE = 0x00002000, // 服务器城战服务（已废弃下次更新删除）
	COMBSEV4_FACTION_SHILIAN = 0x00004000, // 帮派试炼
	COMBSEV4_CENTER_SERVER_BATTLE_AWARD = 0x00008000, // 服务器城战领奖服务（已废弃下次更新删除）
	COMBSEV4_BABY_RETRIEVE = 0x00010000, // 找回宝宝
	COMBSEV4_HOME_SLAVE_WORK = 0x00020000, // 第二家园仆从打工
	COMBSEV4_HOME_SLAVE_CARE = 0x00040000, // 第二家园仆从好感
	COMBSEV4_EQUIP_UPGRADE = 0x00080000, // 装备升级
	COMBSEV4_THIRD_HOME_TRANS = 0x00100000, // 第三家园传送
	COMBSEV4_CONTEST_FOR_MARRIAGE = 0x00200000, // 比武招亲
	COMBSEV4_CENTER_SERVER_BATTLE_TRANS = 0x00400000, // 战场传送
	COMBSEV3_WEATHER = 0x00800000, // 天气
	COMBSEV4_LOVERS = 0x01000000, // 情侣
	COMBSEV4_SENIOR_INTIMATE = 0x02000000, // 金玉良缘
	COMBSEV4_ARENA_SOLO = 0x04000000, // 单人竞技场
	COMBSEV4_ARENA_MANYSOLO = 0x08000000, // 组队竞技场
	COMBSEV4_PUSH_CAR = 0x10000000, // 单人推车
	COMBSEV4_FACTION_RACE_TRANSPORT = 0x20000000, // 使用传送机器
	COMBSEV4_MANY_PUSH_CAR = 0x40000000, // 多人推车
	COMBSEV4_CARRACE = 0x80000000, // 赛车
	COMBSEV4_FORCE_INT = 0x7fffffff, // 

};

enum COMBINESEV5_MASK {
	COMBSEV5_CARRACE_PVP = 0x00000001, // 赛车PVP
	COMBSEV5_HOST_PARTY = 0x00000002, // 举办派对
	COMBSEV5_JOIN_PARTY = 0x00000004, // 参与派对
	COMBSEV5_OPEN_PARTY = 0x00000008, // 开启派对
	COMBSEV5_CHANGE_BODY = 0x00000010, // 性别体型转换
	COMBSEV5_CAREER_SHOP_EMPLOYEE = 0x00000020, // 小店店员
	COMBSEV5_BUY_HOMETOWN = 0x00000040, // 购买家园
	COMBSEV5_GEM_TRANSFER = 0x00000080, // 圣核转换
	COMBSEV5_DANCE_TOGETHER = 0x00000100, // 共舞寻缘
	COMBSEV5_SOUL_INTIMATE = 0x00000200, // 灵魂伴侣
	COMBSEV5_CARRACE_PVP2 = 0x00000400, // 赛车PVP2
	COMBSEV5_ACTIVITY_EFFIGY = 0x00000800, // 活动雕像
	COMBSEV5_ACTIVITY_INTRODUCE = 0x00001000, // 活动介绍
	COMBSEV5_GUARD_MARRAY = 0x00002000, // 宠物结伴
	COMBSEV5_MINIGAME_MATCH = 0x00004000, // 小游戏对战
	COMBSEV5_SOUL_INTIMATE_DISMISS = 0x00008000, // 灵魂伴侣解除
	COMBSEV5_FACTION_COLLECTION2 = 0x00010000, // 引龙行动
	COMBSEV5_SOUL_INTIMATE_VOW = 0x00020000, // 灵魂伴侣宣誓
	COMBSEV5_SOUL_INTIMATE_MESSAGE = 0x00040000, // 灵魂伴侣宣誓留言板
	COMBSEV5_BABY_TEST = 0x00080000, // 孩子体检
	COMBSEV5_RECAST_DIAOXIANG = 0x00100000, // 重铸雕像
	COMBSEV5_CORPS_BOSS = 0x00200000, // 社团BOSS
	COMBSEV5_MINIGAME_MORA = 0x00400000, // 小游戏猜拳
	COMBSEV5_CUTE_PET = 0x00800000, // 萌宠任务
	COMBSEV5_FACTION_DUEL = 0x01000000, // 社团约战
	COMBSEV5_WEDDING_CREATE = 0x02000000, // 举办婚礼
	COMBSEV5_WEDDING_JOIN = 0x04000000, // 参加婚礼
	COMBSEV5_BUY_CONTRACTHOMETOWN = 0x08000000, // 伴侣家园购买
	COMBSEV5_BDSG = 0x10000000, // 白帝神宫
	COMBSEV5_GO_TO_ROAM_NORMAL = 0x20000000, // 本服传送至PVE跨服
	COMBSEV5_BACK_FROM_ROAM_NORMAL = 0x40000000, // PVE跨服传送至本服
	COMBSEV5_CLUB_BOSS = 0x80000000, // 社团试炼
	COMBSEV5_FORCE_INT = 0x7fffffff, // 

};

enum COMBINESEV6_MASK {
	COMBSEV6_DISNEY_WISH = 0x00000001, // 迪士尼放灯心愿
	COMBSEV6_FINK_VILLAGE_BOARD = 0x00000002, // 芬克村告示板
	COMBSEV6_DISNEY_WISH_BOARD = 0x00000004, // 迪士尼心愿墙
	COMBSEV6_HONEY_GARDEN_OPEN = 0x00000008, // 开启甜蜜花园
	COMBSEV6_HONEY_GARDEN_EXTENT = 0x00000010, // 扩建甜蜜花园
	COMBSEV6_HONEY_GARDEN_BUTLER = 0x00000020, // 甜蜜花园特殊商品
	COMBSEV6_ROAM_INTIMATE = 0x00000040, // 跨服羁绊
	COMBSEV6_THUNDERDTRIKE_CHOICE_MAP = 0x00000080, // 天谴计划选图
	COMBSEV6_HONEY_GARDEN_BARGAIN = 0x00000100, // 甜蜜花园砍价
	COMBSEV6_RAINBOW = 0x00000200, // 彩虹航线
	COMBSEV6_TEAMTOWER = 0x00000400, // 组队爬塔
	COMBSEV6_PHOTO_MINIGAME = 0x00000800, // 极影空间小游戏
	COMBSEV6_PVE_MAZE = 0x00001000, // 永恒迷宫
	COMBSEV6_CENTER_ARENA_SOLO = 0x00002000, // 浩瀚实训竞技场
	COMBSEV6_TOWNLET = 0x00004000, // 小镇服务
	COMBSEV6_DANCE_PARTY = 0x00008000, // 双人共舞
	COMBSEV6_HOLY_GHOST_LOTTERY = 0x00010000, // 英灵彩票
	COMBSEV6_TOWNLET_CHANGE_SKIN = 0x00020000, // 小镇换皮肤
	COMBSEV6_LOTTERY_DRAW = 0x00040000, // 绘梦星空彩票
	COMBSEV6_ROUGE_INSTANCE = 0x00080000, // 新肉鸽玩法
	COMBSEV6_GREEN_PUZZLE = 0x00100000, // 包青团小游戏
	COMBSEV6_INSTANCE_TEN = 0x00200000, // 团队副本

};

enum FACTION_TYPE_RESTRICTION_MASK {
	FACTION_TYPE_RESTR_NO_FACTION = 0x00000001, // 无帮派
	FACTION_TYPE_RESTR_NO_BASE = 0x00000002, // 未开宗
	FACTION_TYPE_RESTR_ESCORT = 0x00000004, // 镖局
	FACTION_TYPE_RESTR_CARAVAN = 0x00000008, // 马帮
	FACTION_TYPE_RESTR_COTTAGE = 0x00000010, // 山寨
	FACTION_TYPE_RESTR_WORKSHOP = 0x00000020, // 工坊
	FACTION_TYPE_RESTR_INT = 0x7fffffff, // 

};

enum ACTION_ITEM_USE_LIMIT_MASK {
	USE_LIMIT_FIGHT = 0x00000001, // 战斗
	USE_LIMIT_VEHICLE = 0x00000002, // 骑乘
	USE_LIMIT_RUSH = 0x00000004, // 疾行
	USE_LIMIT_SWIM = 0x00000008, // 游泳
	USE_LIMIT_FLY = 0x00000010, // 飞行（潜水）
	USE_LIMIT_TRANSFORM = 0x00000020, // 变身
	USE_LIMIT_INT = 0x7fffffff, // 

};

enum HIDE_NAME_MASK {
	HIDE_NAME_NAME = 0x00000001, // 隐藏名字
	HIDE_NAME_HPBAR = 0x00000002, // 隐藏血条
	HIDE_NAME_MASK_INT = 0x7fffffff, // 

};

enum PLAYER_OPTIONS_ROUND_TIME_MASK {
	PLAYER_OPTION_TIME_99SEC = 0x00000001, // 实际是60秒
	PLAYER_OPTION_TIME_180SEC = 0x00000002, // 实际是99秒
	PLAYER_OPTION_TIME_INFINITY = 0x00000004, // 无限
	PLAYER_OPTION_TIME_90MIN = 0x00000008, // 90分钟
	PLAYER_OPTION_TIME_INT = 0x7fffffff, // 

};

enum PLAYER_OPTIONS_FORBIDDEN_MASK {
	PLAYER_OPTIONS_FORBIDDEN_MEDICINE = 0x00000001, // 玩家可开关禁止吃药
	PLAYER_OPTIONS_FORBIDDEN_ITEMS = 0x00000002, // 玩家可开关禁止使用道具
	PLAYER_OPTIONS_FORBIDDEN_PETS = 0x00000004, // 玩家可开关禁止招宠物
	PLAYER_OPTIONS_FORBIDDEN_RIDE = 0x00000008, // 玩家可开关禁止骑乘
	PLAYER_OPTIONS_FORBIDDEN_SWITCH_EQUIP = 0x00000010, // 玩家可开关禁止切换装备
	PLAYER_OPTIONS_FORBIDDEN_INT = 0x7fffffff, // 

};

enum PLAYER_FORBID_OPTIONS_MASK {
	PLAYER_FORBID_MEDICINE = 0x00000001, // 默认禁止吃药打开
	PLAYER_FORBID_ITEMS = 0x00000002, // 默认禁止使用道具打开
	PLAYER_FORBID_PET = 0x00000004, // 默认禁止招宠物打开
	PLAYER_FORBID_RIDE = 0x00000008, // 默认禁止骑乘打开
	PLAYER_FORBID_SWITCH_EQUIP = 0x00000010, // 默认禁止切换装备打开
	PLAYER_FORBID_OPTIONS_MASK_PASSWORD = 0x80000000, // 用于通知客户端房间的Lock状态
	PLAYER_FORBID_OPTION_INT = 0x7fffffff, // 

};

enum PLAYER_OPTIONS_BATTLE_UI_MASK {
	PLAYER_OPTION_UI_HP = 0x00000001, // PLAYER_OPTION_UI_HP
	PLAYER_OPTION_UI_MP = 0x00000002, // PLAYER_OPTION_UI_MP
	PLAYER_OPTION_UI_AP = 0x00000004, // 体力
	PLAYER_OPTION_UI_ANGRY = 0x00000008, // 怒气
	PLAYER_OPTION_UI_WEAKPOINT = 0x00000010, // 破绽
	PLAYER_OPTION_UI_INT = 0x7fffffff, // 

};

enum ORIG_BATTLE_UI_STATE_MASK {
	ORIG_BATTLE_UI_BOTH_SEE_HP = 0x00000001, // HP双方可见
	ORIG_BATTLE_UI_BOTH_SEE_MP = 0x00000002, // MP双方可见
	ORIG_BATTLE_UI_BOTH_SEE_AP = 0x00000004, // 体力双方可见
	ORIG_BATTLE_UI_BOTH_SEE_ANGRY = 0x00000008, // 怒气双方可见
	ORIG_BATTLE_UI_BOTH_SEE_WEAKPOINT = 0x00000010, // 破绽双方可见
	ORIG_BATTLE_UI_BOTH_SEE_INT = 0x7fffffff, // 

};

enum OPTIONAL_ROUND_NUM_MASK {
	OPTIONAL_ROUND_NUM_1 = 0x00000001, // OPTIONAL_ROUND_NUM_1
	OPTIONAL_ROUND_NUM_3 = 0x00000002, // OPTIONAL_ROUND_NUM_3
	OPTIONAL_ROUND_NUM_5 = 0x00000004, // OPTIONAL_ROUND_NUM_5
	OPTIONAL_ROUND_NUM_7 = 0x00000008, // OPTIONAL_ROUND_NUM_7
	OPTIONAL_ROUND_NUM_9 = 0x00000010, // OPTIONAL_ROUND_NUM_9
	OPTIONAL_ROUND_5V5 = 0x00000020, // 5对5标准
	OPTIONAL_ROUND_NUM_INT = 0x7fffffff, // 

};

enum INSTANCE_MISC_MASK {
	INSTANCE_MISC_MASK_NONE = 0x00000000, // 无
	INSTANCE_MISC_MASK_EXTRA_SYNCPOS = 0x00000001, // 主角高频同步位置给服务器

};

enum INST_SPECMODE_MASK {
	INSTSPECMODEMASK_NONE = 0, // INSTSPECMODEMASK_NONE
	INSTSPECMODEMASK_NEWBIE = 0x00000001, // 新手模式
	INSTSPECMODEMASK_MASTERDECIPLE = 0x00000002, // 师徒模式
	INSTSPECMODEMASK_FORCE_INT = 0x7fffffff, // 

};

enum BATTLE_INFO_MASK {
	BATTLE_WITH_PASSWORD = 0x00000001, // 需要密码进入
	BATTLE_PLAYING = 0x00000002, // 游戏已经开始
	BATTLE_CAN_SPECTE = 0x00000004, // 可以观战

};

enum EXP_BATFLD_RECORDDATA_MASK {
	EXP_BATFLD_RECORDDATA_MASK_FACTIONS = 0x00000001, // 阵营ABCD
	EXP_BATFLD_RECORDDATA_MASK_REPU_EXP = 0x00000002, // 声望105(经验)
	EXP_BATFLD_RECORDDATA_MASK_REPU_MONEY = 0x00000004, // 声望106(钱)
	EXP_BATFLD_RECORDDATA_MASK_TRANSFORM = 0x00000008, // 变身状态(是否变身变成谁)
	EXP_BATFLD_RECORDDATA_MASK_SCENE_PACK = 0x00000010, // 现有的场景包裹
	EXP_BATFLD_RECORDDATA_MASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_YEAR_MASK {
	EXP_YEAR_MASK_2009 = 0x00000001, // 2009
	EXP_YEAR_MASK_2010 = 0x00000002, // 2010
	EXP_YEAR_MASK_2011 = 0x00000004, // 2011
	EXP_YEAR_MASK_2012 = 0x00000008, // 2012
	EXP_YEAR_MASK_2013 = 0x00000010, // 2013
	EXP_YEAR_MASK_2014 = 0x00000020, // 2014
	EXP_YEAR_MASK_2015 = 0x00000040, // 2015
	EXP_YEAR_MASK_2016 = 0x00000080, // 2016
	EXP_YEAR_MASK_2017 = 0x00000100, // 2017
	EXP_YEAR_MASK_2018 = 0x00000200, // 2018
	EXP_YEAR_MASK_2019 = 0x00000400, // 2019
	EXP_YEAR_MASK_2020 = 0x00000800, // 2020
	EXP_YEAR_MASK_ALL = 0x00000fff, // EXP_YEAR_MASK_ALL
	EXP_YEAR_MASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_MONTH_MASK {
	EXP_MONTH_MASK_JANUARY = 0x00000001, // 一月
	EXP_MONTH_MASK_FEBRUARY = 0x00000002, // 二月
	EXP_MONTH_MASK_MARCH = 0x00000004, // 三月
	EXP_MONTH_MASK_APRIL = 0x00000008, // 四月
	EXP_MONTH_MASK_MAY = 0x00000010, // 五月
	EXP_MONTH_MASK_JUNE = 0x00000020, // 六月
	EXP_MONTH_MASK_JULY = 0x00000040, // 七月
	EXP_MONTH_MASK_AUGUST = 0x00000080, // 八月
	EXP_MONTH_MASK_SEPTEMBER = 0x00000100, // 九月
	EXP_MONTH_MASK_OCTOBER = 0x00000200, // 十月
	EXP_MONTH_MASK_NOVEMBER = 0x00000400, // 十一月
	EXP_MONTH_MASK_DECEMBER = 0x00000800, // 十二月
	EXP_MONTH_MASK_ALL = 0x00000fff, // EXP_MONTH_MASK_ALL
	EXP_MONTH_MASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_DAY_MASK {
	EXP_DAY_MASK_1 = 0x00000001, // 1号
	EXP_DAY_MASK_2 = 0x00000002, // 2号
	EXP_DAY_MASK_3 = 0x00000004, // 3号
	EXP_DAY_MASK_4 = 0x00000008, // 4号
	EXP_DAY_MASK_5 = 0x00000010, // 5号
	EXP_DAY_MASK_6 = 0x00000020, // 6号
	EXP_DAY_MASK_7 = 0x00000040, // 7号
	EXP_DAY_MASK_8 = 0x00000080, // 8号
	EXP_DAY_MASK_9 = 0x00000100, // 9号
	EXP_DAY_MASK_10 = 0x00000200, // 10号
	EXP_DAY_MASK_11 = 0x00000400, // 11号
	EXP_DAY_MASK_12 = 0x00000800, // 12号
	EXP_DAY_MASK_13 = 0x00001000, // 13号
	EXP_DAY_MASK_14 = 0x00002000, // 14号
	EXP_DAY_MASK_15 = 0x00004000, // 15号
	EXP_DAY_MASK_16 = 0x00008000, // 16号
	EXP_DAY_MASK_17 = 0x00010000, // 17号
	EXP_DAY_MASK_18 = 0x00020000, // 18号
	EXP_DAY_MASK_19 = 0x00040000, // 19号
	EXP_DAY_MASK_20 = 0x00080000, // 20号
	EXP_DAY_MASK_21 = 0x00100000, // 21号
	EXP_DAY_MASK_22 = 0x00200000, // 22号
	EXP_DAY_MASK_23 = 0x00400000, // 23号
	EXP_DAY_MASK_24 = 0x00800000, // 24号
	EXP_DAY_MASK_25 = 0x01000000, // 25号
	EXP_DAY_MASK_26 = 0x02000000, // 26号
	EXP_DAY_MASK_27 = 0x04000000, // 27号
	EXP_DAY_MASK_28 = 0x08000000, // 28号
	EXP_DAY_MASK_29 = 0x10000000, // 29号
	EXP_DAY_MASK_30 = 0x20000000, // 30号
	EXP_DAY_MASK_31 = 0x40000000, // 31号
	EXP_DAY_MASK_ALL = 0x7fffffff, // EXP_DAY_MASK_ALL
	EXP_DAY_MASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_WEEKDAY_MASK {
	EXP_WEEKDAY_MASK_MONDAY = 0x00000001, // 周一
	EXP_WEEKDAY_MASK_TUESDAY = 0x00000002, // 周二
	EXP_WEEKDAY_MASK_WEDNESDAY = 0x00000004, // 周三
	EXP_WEEKDAY_MASK_THURSDAY = 0x00000008, // 周四
	EXP_WEEKDAY_MASK_FRIDAY = 0x00000010, // 周五
	EXP_WEEKDAY_MASK_SATURDAY = 0x00000020, // 周六
	EXP_WEEKDAY_MASK_SUNDAY = 0x00000040, // 周日
	EXP_WEEKDAY_MASK_ALL = 0x0000007f, // EXP_WEEKDAY_MASK_ALL
	EXP_WEEKDAY_MASK_FORCE_INT = 0x7fffffff, // 

};

enum PET_PERSONALITY_MASK {
	PETPERSONALITY_PHYSIC = 0x00000001, // 内功
	PETPERSONALITY_MAGIC = 0x00000002, // 外功
	PETPERSONALITY_FORCE_INT = 0x7fffffff, // 

};

enum PET_SKILLBOOK_MASK {
	PET_SKILLBOOK_EXTERNAL_PET = 0x00000001, // 外攻宠
	PET_SKILLBOOK_INTERNAL_PET = 0x00000002, // 内攻宠
	PET_SKILLBOOK_DEFENCE_PET = 0x00000004, // 防御宠
	PET_SKILLBOOK_EXTERNAL_MAN = 0x00000008, // 外攻人
	PET_SKILLBOOK_INTERNAL_MAN = 0x00000010, // 内攻人
	PET_SKILLBOOK_DEFENCE_MAN = 0x00000020, // 防御人
	PET_SKILLBOOK_EXTERNAL_FRD = 0x00000040, // 外攻友
	PET_SKILLBOOK_INTERNAL_FRD = 0x00000080, // 内攻友
	PET_SKILLBOOK_DEFENCE_FRD = 0x00000100, // 防御友

};

enum EQUIPSLOT_MASK {
	EQUIP_MASK_WEAPON = 0x0001, // 武器
	EQUIP_MASK_HEAD = 0x0002, // 头部
	EQUIP_MASK_ARMOUR = 0x0004, // 铠甲
	EQUIP_MASK_WAIST = 0x0008, // 手套
	EQUIP_MASK_LEG = 0x0010, // 腿部
	EQUIP_MASK_FOOT = 0x0020, // 脚部
	EQUIP_MASK_NECK = 0x0040, // 颈部
	EQUIP_MASK_FINGER = 0x0080, // 戒指
	EQUIP_MASK_WRIST = 0x0100, // 手环
	EQUIP_MASK_WAISTT = 0x0200, // 腰带
	EQUIP_MASK_PUBG_MAIN_WEAPON = 0x010000, // 大逃杀主武器
	EQUIP_MASK_PUBG_SUB_WEAPON = 0x020000, // 大逃杀副武器
	EQUIP_MASK_PUBG_HEAD = 0x040000, // 大逃杀头盔
	EQUIP_MASK_PUBG_ARMOUR = 0x080000, // 大逃杀护甲
	EQUIP_MASK_PUBG_NECK = 0x100000, // 大逃杀护符
	EQUIP_MASK_ALL = 0x00ffffff, // EQUIP_MASK_ALL
	EQUIP_MASK_FORCE_INT = 0x7fffffff, // 

};

enum BEHAVLIMIT_MASK_MAIN {
	BEHAVLIMIT_JUMP = 0x00000001, // 跳跃
	BEHAVLIMIT_RUN = 0x00000002, // 奔跑
	BEHAVLIMIT_WALK = 0x00000004, // 行走
	BEHAVLIMIT_TRANSFER = 0x00000008, // 传送
	BEHAVLIMIT_TEAMFOLLOW = 0x00000010, // 组队跟随
	BEHAVLIMIT_SERVER_MOVE = 0x00000020, // 移动
	BEHAVLIMIT_FLY = 0x00000040, // 御剑飞行
	BEHAVLIMIT_OFFLINE_COMBAT = 0x00000080, // 离线自动战斗
	BEHAVLIMITMAIN_FORCE_INT = 0x7fffffff, // 

};

enum BEHAVLIMIT_MASK_ALL {
	BEHAVLIMIT_BOOTH = 0x00000001, // 摆摊
	BEHAVLIMIT_SKILL = 0x00000002, // 技能
	BEHAVLIMIT_TRADE = 0x00000004, // 交易
	BEHAVLIMIT_MANUAL_CANCEL = 0x00000008, // 手动脱离
	BEHAVLIMIT_NPC_SERVICE = 0x00000010, // NPC服务
	BEHAVLIMIT_SUMMON_PET = 0x00000020, // 召唤宠物
	BEHAVLIMITALL_FORCE_INT = 0x7fffffff, // 

};

enum EXP_RELIEVE_MASK {
	EXP_RELIEVEMASK_LEAVE_SCENE = 0x00000001, // 离开当前地图解除
	EXP_RELIEVEMASK_OFFLINE = 0x00000002, // 下线解除
	EXP_RELIEVEMASK_FIGHT = 0x00000004, // 进入战斗解除
	EXP_RELIEVEMASK_MANUAL = 0x00000008, // 手动解除
	EXP_RELIEVEMASK_FORCE_INT = 0x7fffffff, // 

};

enum SUBOBJ_JUDGECOLLIDE_MASK {
	SUBOBJJUDGECOLLMASK_ATTACKABLE = 0x00000001, // 可攻击角色
	SUBOBJJUDGECOLLMASK_UNATTACKBLE = 0x00000002, // 不可攻击角色
	SUBOBJJUDGECOLLMASK_ORNAMENT = 0x00000004, // 阻碍建筑
	SUBOBJJUDGECOLLMASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_RELATIONREQ_MASK {
	EXPRELREQMASK_NONE = 0, // 无
	EXPRELREQMASK_FRIEND = 0x00000001, // 好友
	EXPRELREQMASK_MASTERDISCIPLE = 0x00000002, // 师徒
	EXPRELREQMASK_MARRIAGE = 0x00000004, // 夫妻
	EXPRELREQMASK_SWORN = 0x00000008, // 结义
	EXPRELREQMASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_TRANSFORCE_DAMAGE_MASK {
	EXPTRANSFORCEDMG_NONE = 0, // 无
	EXPTRANSFORCEDMG_INSANE = 0x00000001, // 走火入魔
	EXPTRANSFORCEDMG_DIE = 0x00000002, // 死亡
	EXPTRANSFORCEDMG_POISON = 0x00000004, // 中毒
	EXPTRANSFORCEDMG_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ACHIEVESECTION_MASK {
	EXPACHIEVESECT_DEVELOPING = 0x00000001, // 开发中成就
	EXPACHIEVESECT_EXPERIENCE = 0x00000002, // 体验服专有成就
	EXPACHIEVESECT_PUBLIC_1 = 0x00000004, // 公开版本1通用成就
	EXPACHIEVESECT_PUBLIC_2 = 0x00000008, // 公开版本2通用成就
	EXPACHIEVESECT_PUBLIC_3 = 0x00000010, // 公开版本3通用成就
	EXPACHIEVESECT_PUBLIC_4 = 0x00000020, // 公开版本4通用成就
	EXPACHIEVESECT_PUBLIC_5 = 0x00000040, // 公开版本5通用成就
	EXPACHIEVESECT_PUBLIC_6 = 0x00000080, // 公开版本6通用成就
	EXPACHIEVESECT_PUBLIC_7 = 0x00000100, // 公开版本7通用成就
	EXPACHIEVESECT_PUBLIC_8 = 0x00000200, // 公开版本8通用成就
	EXPACHIEVESECT_PUBLIC_9 = 0x00000400, // 公开版本9通用成就
	EXPACHIEVESECT_PUBLIC_10 = 0x00000800, // 公开版本10通用成就
	EXPACHIEVESECT_PUBLIC_11 = 0x00001000, // 公开版本11通用成就
	EXPACHIEVESECT_PUBLIC_12 = 0x00002000, // 公开版本12通用成就
	EXPACHIEVESECT_PUBLIC_13 = 0x00004000, // 公开版本13通用成就
	EXPACHIEVESECT_PUBLIC_14 = 0x00008000, // 公开版本14通用成就
	EXPACHIEVESECT_MAINLAND = 0x00010000, // 大陆版本专用成就
	EXPACHIEVESECT_TAIWAN = 0x00020000, // 台湾版本专用成就
	EXPACHIEVESECT_FORCE_INT = 0x7fffffff, // 

};

enum EXP_EQUIPSTONE_SHAPE_MASK {
	EXPEQUIPSTONESHAPE_MASK_ROUND = 0x00000001, // 圆形
	EXPEQUIPSTONESHAPE_MASK_TRIANGLE = 0x00000002, // 三角形
	EXPEQUIPSTONESHAPE_MASK_SQUARE = 0x00000004, // 方形
	EXPEQUIPSTONESHAPE_MASK_HALFMOON = 0x00000008, // 半月形
	EXPEQUIPSTONESHAPE_MASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_ESTONE_TYPE_MASK {
	EXPEQUIPSTONETYPE_MASK_EARTH = 0x00000001, // 地
	EXPEQUIPSTONETYPE_MASK_FIRE = 0x00000002, // 火
	EXPEQUIPSTONETYPE_MASK_WATER = 0x00000004, // 水
	EXPEQUIPSTONETYPE_MASK_WIND = 0x00000008, // 风
	EXPEQUIPSTONETYPE_MASK_SPIRIT = 0x00000010, // 精神
	EXPEQUIPSTONETYPE_MASK_FORCE_INT = 0x7fffffff, // 

};

enum POLICY_OBJSTATE_MASK {
	POLICYSTATE_DEFENCE = 0x00000001, // 防御
	POLICYSTATE_DODGE = 0x00000002, // 闪避
	POLICYSTATE_TUMBLE = 0x00000004, // 被击倒
	POLICYSTATE_SUSPEND = 0x00000008, // 被击飞
	POLICYSTATE_COLLAPSE = 0x00000010, // 被击溃
	POLICYSTATE_FLEE = 0x00000020, // 逃跑
	POLICYSTATE_WANDER = 0x00000040, // 徘徊
	POLICYSTATE_FORCE_INT = 0x7fffffff, // 

};

enum TRANSMITFLAG_TYPE_MASK {
	TRANSFLAGMASK_NONE = 0, // TRANSFLAGMASK_NONE
	TRANSFLAGMASK_POSTHOUSE = 0x00000001, // 驿站标记
	TRANSFLAGMASK_REVIVE = 0x00000002, // 复活标记
	TRANSFLAGMASK_OUTSIDE = 0x00000004, // 大地图野外标记
	TRANSFLAGMASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_LIFEPROF_MASK {
	EXPLIFEPROFMASK_HEAL = 0x00000001, // 治疗
	EXPLIFEPROFMASK_SOCIAL = 0x00000002, // 社交
	EXPLIFEPROFMASK_HANDWORK = 0x00000004, // 手工
	EXPLIFEPROFMASK_FELLOW = 0x00000008, // 门徒
	EXPLIFEPROFMASK_WARRIOR = 0x00000010, // 武者
	EXPLIFEPROFMASK_HOME = 0x00000020, // 家园
	EXPLIFEPROFMASK_FORCE_INT = 0x7fffffff, // 

};

enum EXP_FACTION_IDENTITY_MASK {
	EXP_FACTION_IDENTITY_MASK_BANGZHU = 0x00000001, // 帮主
	EXP_FACTION_IDENTITY_MASK_VICE_BANGZHU = 0x00000002, // 副帮主
	EXP_FACTION_IDENTITY_MASK_ZHANGLAO = 0x00000004, // 长老
	EXP_FACTION_IDENTITY_MASK_HUFA = 0x00000008, // 护法
	EXP_FACTION_IDENTITY_MASK_MENGZHU = 0x00000010, // 盟主
	EXP_FACTION_IDENTITY_MASK_FORCE_INT = 0x7fffffff, // 

};

enum GRANT_REWARD_MASK {
	GRANT_REWARD_MASK_EXP = 0x00000001, // 经验
	GRANT_REWARD_MASK_BOUNDMONEY = 0x00000002, // 绑定比
	GRANT_REWARD_MASK_TRADEMONEY = 0x00000004, // 交易币
	GRANT_REWARD_MASK_REPU1 = 0x00000008, // 声望1
	GRANT_REWARD_MASK_REPU2 = 0x00000010, // 声望2
	GRANT_REWARD_MASK_REPU3 = 0x00000020, // 声望3
	GRANT_REWARD_MASK_REPU4 = 0x00000040, // 声望4
	GRANT_REWARD_MASK_REPU5 = 0x00000080, // 声望5
	GRANT_REWARD_MASK_REPU6 = 0x00000100, // 声望6
	GRANT_REWARD_MASK_REPU7 = 0x00000200, // 声望7
	GRANT_REWARD_MASK_REPU8 = 0x00000400, // 声望8
	GRANT_REWARD_MASK_FACTION_CONTRIBUTION_RESERVED = 0x00000800, // 帮贡
	GRANT_REWARD_MASK_CONSTRUCTION_RESERVED = 0x00001000, // 建设度
	GRANT_REWARD_MASK_BID_VALUE_RESERVED = 0x00002000, // 竞标值
	GRANT_REWARD_MASK_FACTION_FUNDS_RESERVED = 0x00004000, // 帮派资金
	GRANT_REWARD_MASK_ITEM1 = 0x00008000, // 物品1
	GRANT_REWARD_MASK_ITEM2 = 0x00010000, // 物品2
	GRANT_REWARD_MASK_ITEM3 = 0x00020000, // 物品3
	GRANT_REWARD_MASK_ITEM4 = 0x00040000, // 物品4
	GRANT_REWARD_MASK_ITEM5 = 0x00080000, // 物品5
	GRANT_REWARD_MASK_ITEM6 = 0x00100000, // 物品6
	GRANT_REWARD_MASK_ITEM7 = 0x00200000, // 物品7
	GRANT_REWARD_MASK_ITEM8 = 0x00400000, // 物品8
	GRANT_REWARD_MASK_FULIEXP_RESERVED = 0x00800000, // 福利经验
	GRANT_REWARD_MASK_PROEXP = 0x01000000, // 基础功力
	GRANT_REWARD_MASK_BOUND_YUANBO = 0x02000000, // 基础绑定元宝
	GRANT_REWARD_MASK_HAS_COUNTER = 0x04000000, // 有奖励计数器
	GRANT_REWARD_MASK_HAS_SWITCH = 0x08000000, // 有奖励开关
	GRANT_REWARD_MASK_YUANBAO = 0x10000000, // 元宝
	GRANT_REWARD_MASK_RUNE_NEW = 0x20000000, // 新符文
	GRANT_REWARD_MASK_RESERVED8 = 0x40000000, // 

};

enum GRAFFITI_IDENTITY_MASK {
	GRAFFITI_MASK_TIGUAN_ORGANIZER = 1, // GRAFFITI_MASK_TIGUAN_ORGANIZER
	GRAFFITI_MASK_FACTION_MASTER_MASK = 2, // GRAFFITI_MASK_FACTION_MASTER_MASK
	GRAFFITI_MASK_FACTION_MASTER_SPOUSE = 4, // GRAFFITI_MASK_FACTION_MASTER_SPOUSE
	GRAFFITI_MASK_FACTION_VICEMASTER = 8, // GRAFFITI_MASK_FACTION_VICEMASTER
	GRAFFITI_MASK_FACTION_VICEMASTER_SPOUSE = 16, // GRAFFITI_MASK_FACTION_VICEMASTER_SPOUSE
	GRAFFITI_MASK_FACTION_HUFA = 32, // GRAFFITI_MASK_FACTION_HUFA
	GRAFFITI_MASK_FACTION_ZHANGLAO = 64, // GRAFFITI_MASK_FACTION_ZHANGLAO
	GRAFFITI_MASK_RESERVED1 = 128, // GRAFFITI_MASK_RESERVED1
	GRAFFITI_MASK_RESERVED2 = 256, // GRAFFITI_MASK_RESERVED2
	GRAFFITI_MASK_RESERVED3 = 512, // GRAFFITI_MASK_RESERVED3
	GRAFFITI_MASK_RESERVED4 = 1024, // GRAFFITI_MASK_RESERVED4
	GRAFFITI_MASK_RESERVED5 = 2048, // GRAFFITI_MASK_RESERVED5
	GRAFFITI_MASK_INT = 0x7fffffff, // 

};

enum CAN_HIT_MASK {
	CAN_HIT_NORNAM_MONSTER = 0, // 普通怪物
	CAN_HIT_PLAYER = 1, // 等同玩家角色
	CAN_HIT_BUILDING = 2, // 建筑单位
	CAN_HIT_MASK_INT = 0x7fffffff, // 

};

enum PROGRESS_MASK_ENUM {
	PROGRESS_MASK_SPECIAL_ACT = 1, // 1(特色活动进度)
	PROGRESS_MASK_FAMILY_KILL = 2, // 2(灭门进度)
	PROGRESS_MASK_SEEK_TREASURE = 4, // 3(寻宝进度)
	PROGRESS_MASK_INT = 0x7fffffff, // 

};

enum MONSTER_CASTSKILLSEQ_MASK {
	MONSTER_CASTSKILLSEQ_NONE = 0x0, // NONE
	MONSTER_CASTSKILLSEQ_INIT = 0x00000001, // 0-初始套路
	MONSTER_CASTSKILLSEQ_NORMAL = 0x00000002, // 1-普攻套路，
	MONSTER_CASTSKILLSEQ_OTHERS1 = 0x00000004, // 2~31待定

};

enum MONSTER_PARAM_MASK {
	MONSTER_PARAM_NONE = 0x0, // NONE
	MONSTER_PARAM_1 = 0x00000001, // MONSTER_PARAM_1
	MONSTER_PARAM_2 = 0x00000002, // MONSTER_PARAM_2
	MONSTER_PARAM_3 = 0x00000004, // MONSTER_PARAM_3
	MONSTER_PARAM_4 = 0x00000008, // MONSTER_PARAM_4
	MONSTER_PARAM_5 = 0x00000010, // MONSTER_PARAM_5
	MONSTER_PARAM_MASK_INT = 0x7fffffff, // 

};

enum ZHAOJILING_USER_LIMIT_MASK {
	ZHAOJILINE_USER_LIMIT_ALL = 0x00000001, // 所有人
	ZHAOJILINE_USER_LIMIT_NATION_KING = 0x00000002, // 国家国王
	ZHAOJILINE_USER_LIMIT_NATION_JIANGJUN = 0x00000004, // 国家将军
	ZHAOJILINE_USER_LIMIT_NATION_TAISHI = 0x00000008, // 国家太师
	ZHAOJILINE_USER_LIMIT_CORPS_MASTER = 0x00000010, // 帮派帮主
	ZHAOJILINE_USER_LIMIT_CORPS_VICEMASTER = 0x00000020, // 帮派副帮主

};

enum NPC_SERVICE_LIMIT_MASK {
	NPC_SERVICE_LIMIT_ALL = 0x00000001, // 所有人
	NPC_SERVICE_LIMIT_SAME_NATION = 0x00000002, // 本国
	NPC_SERVICE_LIMIT_ALLIANCE = 0x00000004, // 盟国(不包括本国)
	NPC_SERVICE_LIMIT_ENEMY_NATION = 0x00000008, // 敌国

};

enum NATION_OFFICER_MASK {
	NATION_OFFICER_MASK_NONE = 0x00000001, // NATION_OFFICER_MASK_NONE
	NATION_OFFICER_MASK_GUO_WANG = 0x00000002, // NATION_OFFICER_MASK_GUO_WANG
	NATION_OFFICER_MASK_WANG_FEI = 0x00000004, // NATION_OFFICER_MASK_WANG_FEI
	NATION_OFFICER_MASK_JIANG_JUN = 0x00000008, // NATION_OFFICER_MASK_JIANG_JUN
	NATION_OFFICER_MASK_TAI_SHI = 0x00000010, // NATION_OFFICER_MASK_TAI_SHI
	NATION_OFFICER_MASK_TAI_WEI = 0x00000020, // NATION_OFFICER_MASK_TAI_WEI
	NATION_OFFICER_MASK_CHENG_XIANG = 0x00000040, // NATION_OFFICER_MASK_CHENG_XIANG
	NATION_OFFICER_MASK_WU_WEI_1 = 0x00000080, // NATION_OFFICER_MASK_WU_WEI_1
	NATION_OFFICER_MASK_WU_WEI_2 = 0x00000100, // NATION_OFFICER_MASK_WU_WEI_1
	NATION_OFFICER_MASK_YU_SHI_1 = 0x00000200, // NATION_OFFICER_MASK_YU_SHI_1
	NATION_OFFICER_MASK_YU_SHI_2 = 0x00000400, // 

};

enum EXP_FACTION_POSITION_MASK {
	EXP_FACTION_POSITION_MASK_NORMAL = 0x00000001, // 帮众
	EXP_FACTION_POSITION_MASK_MASTER = 0x00000002, // 帮主
	EXP_FACTION_POSITION_MASK_VICE_MASTER = 0x00000004, // 副帮主
	EXP_FACTION_POSITION_MASK_ZHANGLAO1 = 0x00000008, // EXP_FACTION_POSITION_MASK_ZHANGLAO1
	EXP_FACTION_POSITION_MASK_ZHANGLAO2 = 0x00000010, // EXP_FACTION_POSITION_MASK_ZHANGLAO2

};

enum RECOVER_REWARD_MASK {
	RECOVER_REWARD_MASK_COMMON = 0, // 普通
	RECOVER_REWARD_MASK_CORPS = 0x00000001, // 社团
	RECOVER_REWARD_MASK_CORPS_BATTLE = 0x00000002, // 社团竞赛

};

enum MONSTER_DIE_EFFECT_MASK {
	MONSTER_DIE_EFFECT_NONE = 0x0, // NONE
	MONSTER_DIE_EFFECT_SLOW_SCREEN = 0x00000001, // 慢屏
	MONSTER_DIE_EFFECT_DISSOLUTION = 0x00000002, // 电子光效
	MONSTER_DIE_EFFECT_BURN = 0x00000004, // 燃烧溶解
	MONSTER_DIE_EFFECT_FROZEN = 0x00000008, // 冰冻溶解
	MONSTER_DIE_EFFECT_DARKEN = 0x00000010, // 暗黑溶解
	MONSTER_DIE_EFFECT_MAGIC = 0x00000020, // 魔法溶解
	MONSTER_DIE_EFFECT_FORCE_INT = 0x7fffffff, // 

};

enum IMMUNE_TYPE {
	IMMUNE_Type_1 = 0x00000001, // 硬直
	IMMUNE_Type_2 = 0x00000002, // 浮空
	IMMUNE_Type_4 = 0x00000004, // 倒地
	IMMUNE_Type_8 = 0x00000008, // 技能段-眩晕
	IMMUNE_Type_16 = 0x00000010, // 冰冻
	IMMUNE_Type_32 = 0x00000020, // 石化
	IMMUNE_Type_64 = 0x00000040, // 时空锁定
	IMMUNE_Type_128 = 0x00000080, // 掩码128预留
	IMMUNE_Type_256 = 0x00000100, // 定身
	IMMUNE_Type_512 = 0x00000200, // 减速
	IMMUNE_Type_1024 = 0x00000400, // 恐惧
	IMMUNE_Type_2048 = 0x00000800, // 恐惧，可递减
	IMMUNE_Type_4096 = 0x00001000, // 束缚
	IMMUNE_Type_8192 = 0x00002000, // 魅惑，可递减
	IMMUNE_Type_16384 = 0x00004000, // 沉默
	IMMUNE_Type_262144 = 0x00040000, // 拖拽
	IMMUNE_Type_2097152 = 0x00200000, // 睡眠
	IMMUNE_Type_8388608 = 0x00800000, // 技能效果-眩晕

};

enum IMMUNE_TYPE2 {
	IMMUNE_Type2_16 = 0x00000010, // 绝对沉默
	IMMUNE_Type2_128 = 0x00000080, // 缠绕
	IMMUNE_Type2_256 = 0x00000100, // 牢笼
	IMMUNE_Type2_512 = 0x00000200, // 混乱
	IMMUNE_Type2_1024 = 0x00000400, // 混乱2
	IMMUNE_Type2_4096 = 0x00001000, // 入画
	IMMUNE_Type2_8192 = 0x00002000, // 卷协

};

enum FASHION_MISC_MASK {
	FMM_NO_STOIC_NONE = 0x00000000, // NONE
	FMM_NO_STOIC_MATERIAL = 0x00000001, // 霸体材质无效

};

enum BABY_STAGE_MASK {
	STAGE_FIRST = 0x00000001, // 阶段1
	STAGE_SECOND = 0x00000002, // 阶段2
	STAGE_THIRD = 0x00000004, // 阶段3
	STAGE_FOURTH = 0x00000008, // 阶段4

};

enum WEATHER_MASK {
	WEATHER_NONE = 0x0, // NONE
	WEATHER_SUNNY = 0x00000001, // 晴天
	WEATHER_BLIZZARD = 0x00000002, // 暴风雪
	WEATHER_SNOW = 0x00000004, // 雪
	WEATHER_THUNDERSTORM = 0x00000008, // 雷雨
	WEATHER_HEAVY_RAIN = 0x00000010, // 大雨
	WEATHER_LIGHT_RAIN = 0x00000020, // 小雨
	WEATHER_CLOUDY = 0x00000040, // 多云
	WEATHER_AURORA = 0x00000080, // 极光

};

enum HUMIDITY_MASK {
	HUMIDITY_NONE = 0x0, // NONE
	HUMIDITY_DROUGHT = 0x00000001, // 干旱
	HUMIDITY_DRY = 0x00000002, // 干燥
	HUMIDITY_COMFORTABLE = 0x00000004, // 适宜
	HUMIDITY_MOIST = 0x00000008, // 湿润
	HUMIDITY_DAMP = 0x00000010, // 潮湿

};

enum INTERACTION_EXIT_MASK {
	INTERACTION_EXIT_NONE = 0x0, // NONE
	INTERACTION_EXIT_CLICK_BUTTON = 0x00000001, // 点击屏幕中央退出按钮
	INTERACTION_EXIT_JOYSTICK = 0x00000002, // 拉动摇杆
	INTERACTION_EXIT_MOVE = 0x00000004, // 点击地面移动
	INTERACTION_EXIT_CUSTOM = 0x00000008, // 自定义

};

enum INTERACTION_SELECT_MASK {
	INTERACTION_SELECT_NONE = 0x0, // NONE
	INTERACTION_SELECT_CLICK = 0x00000001, // 点击选择
	INTERACTION_SELECT_RANDOM = 0x00000002, // 随机选择

};

enum INTERACTION_SUB_OPERATION_SCRIPT_MASK {
	INTERACTION_SUB_OPERATION_SCRIPT_NONE = 0x0, // NONE
	INTERACTION_SUB_OPERATION_SCRIPT_BEGIN = 0x00000001, // 子操作开始
	INTERACTION_SUB_OPERATION_SCRIPT_DATA = 0x00000002, // 子操作数据
	INTERACTION_SUB_OPERATION_SCRIPT_END = 0x00000004, // 子操作结束

};

enum INTERACTION_PRIOR_CONCOMITANT_MASK {
	INTERACTION_PRIOR_MASK_NONE = 0x0, // NONE
	INTERACTION_PRIOR_MASK_0 = 0x00000001, // 使用动作
	INTERACTION_PRIOR_MASK_1 = 0x00000002, // 使用filter1
	INTERACTION_PRIOR_MASK_2 = 0x00000004, // 使用filter2
	INTERACTION_PRIOR_MASK_3 = 0x00000008, // 使用filter3
	INTERACTION_PRIOR_MASK_4 = 0x00000010, // 使用filter4
	INTERACTION_PRIOR_MASK_5 = 0x00000020, // 使用filter5
	INTERACTION_PRIOR_MASK_6 = 0x00000040, // 使用filter6
	INTERACTION_PRIOR_MASK_7 = 0x00000080, // 使用filter7
	INTERACTION_PRIOR_MASK_8 = 0x00000100, // 使用filter8
	INTERACTION_PRIOR_MASK_ALL = 0x000001FF, // 

};

enum INTERACTION_CAREER_MASK {
	INTERACTION_CAREER_MASK_ALL = 0x0, // ALL
	INTERACTION_CAREER_MASK_COOK = 0x00000001, // 料理身份
	INTERACTION_CAREER_MASK_STAR = 0x00000002, // 明星身份

};

enum PARTNER_MOOD_MASK {
	PARTNER_MOOD_MASK_0 = 0x0, // NONE
	PARTNER_MOOD_MASK_1 = 0x00000001, // 心情1
	PARTNER_MOOD_MASK_2 = 0x00000002, // 心情2
	PARTNER_MOOD_MASK_3 = 0x00000004, // 心情3
	PARTNER_MOOD_MASK_4 = 0x00000008, // 心情4
	PARTNER_MOOD_MASK_5 = 0x00000010, // 心情5

};

enum COOK_MATERIAL_RESEARCH_MASK {
	COOK_MATERIAL_RESEARCH_NO_USE = 0x0, // 不可用
	COOK_MATERIAL_RESEARCH_COOK = 0x00000001, // 料理
	COOK_MATERIAL_RESEARCH_WINE = 0x00000002, // 调酒

};

enum SKILL_STATE_TYPEMASK_1 {
	SS_TYPEMASK_STUN = 0x00000001, // 硬直
	SS_TYPEMASK_AIR = 0x00000002, // 浮空
	SS_TYPEMASK_GROUND = 0x00000004, // 倒地
	SS_TYPEMASK_DIZZ = 0x00000008, // 技能段-眩晕
	SS_TYPEMASK_FROZEN = 0x00000010, // 冰冻
	SS_TYPEMASK_STONE = 0x00000020, // 石化
	SS_TYPEMASK_TIME = 0x00000040, // 时空锁定
	SS_TYPEMASK_CHANGE_MODEL = 0x00000080, // 变形
	SS_TYPEMASK_ROOT = 0x00000100, // 定身
	SS_TYPEMASK_SLOW = 0x00000200, // 减速
	SS_TYPEMASK_FRAR = 0x00000400, // 恐惧
	SS_TYPEMASK_FEAR_CAN_REDU = 0x00000800, // 恐惧，可递减
	SS_TYPEMASK_CHAIN = 0x00001000, // 束缚
	SS_TYPEMASK_DELUDED_CAN_REDU = 0x00002000, // 魅惑，可递减
	SS_TYPEMASK_SILENT = 0x00004000, // 沉默
	SS_TYPEMASK_SUPPER = 0x00010000, // 霸体
	SS_TYPEMASK_KOTODAMA = 0x00020000, // 伙伴言灵cd互斥
	SS_TYPEMASK_DRAG = 0x00040000, // 拖拽
	SS_TYPEMASK_FAKE_REVIVE = 0x00080000, // 死后续航
	SS_TYPEMASK_INVISIBLE_NO_CHOOSE = 0x00100000, // 隐身
	SS_TYPEMASK_SLEEP = 0x00200000, // 睡眠
	SS_TYPEMASK_STANCE = 0x00400000, // 姿态
	SS_TYPEMASK_DIZZ_STATE = 0x00800000, // 技能效果-眩晕
	SS_TYPEMASK_DISPEL_SUPPER = 0x01000000, // 驱散霸体, 且一段时间内不可加上霸体(dispel_immune)
	SS_TYPEMASK_OVERWHELMING = 0x04000000, // 无敌

};

enum SKILL_STATE_TYPEMASK_2 {
	SS_TYPEMASK_TEMPERATURE = 0x00000001, // 温度和湿度
	SS_TYPEMASK_WEATHER = 0x00000002, // 天气
	SS_TYPEMASK_BX = 0x00000004, // 暴血
	SS_TYPEMASK_ATTACK_1 = 0x00000008, // 特殊普攻限制
	SS_TYPEMASK_SILENT_ABSOLUTE = 0x00000010, // 绝对沉默
	SS_TYPEMASK_HAS_MECH = 0x00000020, // 有机甲
	SS_TYPEMASK_FORBID_MP_1_4 = 0x00000040, // 禁止获取mp1 mp4
	SS_TYPEMASK_TWINING = 0x00000080, // 缠绕
	SS_TYPEMASK_CAGE = 0x00000100, // 牢笼
	TYPEMASK_CHAOTIC = 0x00000200, // 混乱
	TYPEMASK_CHAOTIC2 = 0x00000400, // 混乱2
	TYPEMASK_SEVEN_CRIME_DEBUFF = 0x00000800, // 七戒技能的Debuff
	TYPEMASK_FALL_INTO_PICTURE = 0x00001000, // 入画
	TYPEMASK_PICTURE_TRANSFER = 0x00002000, // 卷协

};

enum OVERCOOK_CONTAINER_MASK {
	OVERCOOK_CONTAINER_TYPE_PLATE = 0x00000001, // 盘子
	OVERCOOK_CONTAINER_TYPE_BOIL_POT = 2, // 煮锅
	OVERCOOK_CONTAINER_TYPE_FRYING_PAN = 4, // 煎锅
	OVERCOOK_CONTAINER_TYPE_FRIED_PAN = 8, // 炒锅
	OVERCOOK_CONTAINER_TYPE_JUICER = 16, // 榨汁机
	OVERCOOK_CONTAINER_TYPE_OVEN = 32, // 烤箱
	OVERCOOK_CONTAINER_TYPE_STEAMER = 64, // 蒸锅

};

enum ALCHEMY_PLACE_MASK {
	place1 = 0x00000001, // 位置1
	place2 = 0x00000002, // 位置2
	place3 = 0x00000004, // 位置3
	place4 = 0x00000008, // 位置4
	place5 = 0x00000010, // 位置5
	place6 = 0x00000020, // 位置6

};


#endif
