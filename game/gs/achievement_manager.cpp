#include <base64.h>
#include "achievement_manager.h"
#include "player.h"
#include "player_badge.h"
#include "csvread.h"
#include "player_talent.h"
#include "item/item_child_bedge.h"
#include "longyu_config_man.h"
#include "activity_manager.h"
//#include "bilog.h"

bool UpdateInProgressAchievement(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
                                 int achievement_tid, size_t cond_count, size_t cond_index, int delta, bool force = false)
{
	in_progress_achievement_t *pAchievement = NULL;
	IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = in_progress_map.find(achievement_tid);
	if (it != in_progress_map.end())
	{
		pAchievement = &(it->second);
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(achievement_tid);
		if (NULL == pTemplate)
		{
			return false;
		}
		if (pTemplate->CheckExpiration(imp, pAchievement->timestamp))
		{
			//清空过期成就
			pAchievement->Reset();
		}
	}
	else if (force)
	{
		pAchievement = &(in_progress_map[achievement_tid]);
		if (cond_count > COMPLETE_EVENT_CONDITIOND_COUNT)
		{
			cond_count = COMPLETE_EVENT_CONDITIOND_COUNT;
		}
		if (cond_count > 0)
		{
			pAchievement->cond_count = cond_count;
		}

		imp->Runner()->achievement_active(achievement_tid); //通知客户端激活成就


	}
	if (NULL == pAchievement)
	{
		return false;
	}
	pAchievement->ChangeCondData(cond_index, delta);
	return true;
}

/* 状态条件: 角色等级达到 */
class condition_template_level : public condition_template
{
public:
	condition_template_level() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_LEVEL) {}
	/* 参数说明:
	 * <1>角色等级: (0, 200]
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->GetLevel() >= params[0];
	}
};

/* 状态条件: 钱数达到 */
class condition_template_money : public condition_template
{
public:
	condition_template_money() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_MONEY) {}
	/* 参数说明:
	 * <1>钱币类型: 0表示交易币和绑定币；1表示绑定币；2表示交易币
	 * <2>钱币数量
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = false;
		if (0 == params[0])
		{
			flag = (imp->GetMoney(MT_BIND) + imp->GetMoney(MT_TRADE)) >= (money_t)params[1];
		}
		else if (1 == params[0])
		{
			flag = (imp->GetMoney(MT_BIND) >= (money_t)params[1]);
		}
		else if (2 == params[0])
		{
			flag = (imp->GetMoney(MT_TRADE) >= (money_t)params[1]);
		}
		return flag;
	}
};

/* 状态条件: PK值达到 */
class condition_template_pk_value : public condition_template
{
public:
	condition_template_pk_value() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_PK_VALUE) {}
	/* 参数说明:
	 * <1>PK值
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = ((params[0] >= 0 && imp->GetPKMan().GetPKValue() >= params[0])
		             || (params[0] < 0 && imp->GetPKMan().GetPKValue() <= params[0]));
		return flag;
	}
};

/* 状态条件: 完成普通任务 */
class condition_template_normal_task : public condition_template
{
public:
	condition_template_normal_task() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_NORMAL_TASK) {}
	/* 参数说明:
	 * <1>任务id
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!(*imp->GetTaskGuard())->HaveFinishedTask(params[0]))
		{
			return false;
		}
		return true;
	}
};
/* 状态条件: 完成限次任务 */
class condition_template_limit_num_task : public condition_template
{
public:
	condition_template_limit_num_task() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_LIMITNUM_TASK) {}
	/* 参数说明:
	 * <1>任务id
	 * <2>次数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params[0] > 0 && (*imp->GetTaskGuard())->GetTaskFinishCount(params[0]) >= (size_t)params[1]);
	}
};

/* 状态条件: 学习技能 */
class condition_template_learn_skill : public condition_template
{
public:
	condition_template_learn_skill() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_LEARN_SKILL) {}
	/* 参数说明:
	 * <1>技能id
	 * <2>技能等级
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params[0] > 0)
		{
			return imp->GetSkillLevel(params[0]) >= params[1];
		}
		else
		{
			return imp->GetMinSkillLevel() >= params[1];
		}
	}
};

/* 状态条件: 龙威平均等级达到 */
class condition_template_seven_crime_sword_level_average : public condition_template
{
public:
	condition_template_seven_crime_sword_level_average() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SWORD_LEVEL_AVERAGE) {}
	/* 参数说明:
	 * <1>等级
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->GetSevenCrime().GetSwordLevelAverage() >= params[0];
	}
};

/* 状态条件: 罪责平均品质达到 */
class condition_template_seven_crime_sacrifice_quality_average : public condition_template
{
public:
	condition_template_seven_crime_sacrifice_quality_average() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SACRIFICE_QUALITY_AVERAGE) {}
	/* 参数说明:
	 * <1>品质
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->GetSevenCrime().GetSacrificeQualityAverage() >= params[0];
	}
};

/* 状态条件: 上阵龙裔进化突破消耗物品达到 */
class condition_template_dragonborn_evo_break_cost : public condition_template
{
public:
	condition_template_dragonborn_evo_break_cost() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_DRAGONBORN_EVO_BREAK_COST) {}
	/* 参数说明:
	 * <1>品质
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->GetDragonborn().GetAttachEvoBreakCost(imp) >= params[0];
	}
};

/* 状态条件: 上阵龙裔平均资质达到 */
class condition_template_dragonborn_aptitude_average : public condition_template
{
public:
	condition_template_dragonborn_aptitude_average() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_DRAGONBORN_APTITUDE) {}
	/* 参数说明:
	 * <1>品质
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->GetDragonborn().GetAttachAverageAptitude() >= params[0];
	}
};

/* 状态条件: 增加好友 */
class condition_template_make_friend : public condition_template
{
public:
	condition_template_make_friend() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_MAKE_FRIEND) {}
	/* 参数说明:
	 * <1>好友数量
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params[0] >= 0 && imp->GetFriendCount() >= params[0]);
	}
};

/* 状态条件: 加入帮派 */
class condition_template_join_mafia : public condition_template
{
public:
	condition_template_join_mafia() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_JOIN_MAFIA) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->GetParent()->mafia_id != 0;
	}
};


/* 状态条件: 成就评分 */
class condition_template_achievement_grade : public condition_template
{
public:
	condition_template_achievement_grade() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ACHIEVEMENT_GRADE) {}
	/* 参数说明:
	 * <1>分数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params[0] >= 0 && imp->GetAchievement().GetAchievementGrade() >= params[0]);
	}
};

/* 状态条件: 装备某品阶等级的装备达到某数量 */
class condition_template_equip_quality_count : public condition_template
{
public:
	condition_template_equip_quality_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_EQUIP_QUALITY_COUNT) {}
	/* 参数说明:
	 * <1>品阶等级
	 * <2>数量
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0 && params[1] > 0)
		{
			return imp->GetEquipForgeCount(params[0]) >= params[1];
		}
		return false;
	}
};

/* 状态条件: 装备某炼星等级的装备达到某数量 */
class condition_template_equip_star : public condition_template
{
public:
	condition_template_equip_star() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_EQUIP_START_COUNT) {}
	/* 参数说明:
	 * <1>炼星等级
	 * <2>数量
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0 && params[1] > 0)
		{
			return imp->GetEquipStarCount(params[0]) >= params[1];

		}
		return false;
	}
};

/* 状态条件: 背包格数 */
class condition_template_inventory_size : public condition_template
{
public:
	condition_template_inventory_size() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_INVENTORY_CAPACITY) {}
	/* 参数说明:
	 * <1>背包类型
	 * <2>格数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] >= 0 && params[0] < GNET::IL_COUNT)
		{
			return imp->GetInventory().GetInventory(params[0]).GetEffectSize() >= (size_t)params[1];
		}
		return false;
	}
};

/* 状态条件: 背包扩展格数 */
class condition_template_inventory_extra_size : public condition_template
{
public:
	condition_template_inventory_extra_size() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_INVENTORY_EXTRA_CAPACITY) {}
	/* 参数说明:
	 * <1>背包类型
	 * <2>格数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] >= 0 && params[0] < GNET::IL_COUNT)
		{
			size_t effect_size = imp->GetInventory().GetInventory(params[0]).GetEffectSize();
			size_t extra_size = imp->GetInventory().GetInventoryExtraSize(params[0], effect_size);
			return extra_size >= (size_t)params[1];
		}
		return false;
	}
};

/* 状态条件: 战斗力数值 */
class condition_template_fight_capacity : public condition_template
{
public:
	condition_template_fight_capacity() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_FIGHTING) {}
	/* 参数说明:
	 * <1>战斗力值
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] >= 0)
		{
			return imp->GetFightingCapactiy() >= params[0];
		}
		return false;
	}
};

/* 状态条件: 每日登陆 */
class condition_template_daily_login : public condition_template
{
public:
	condition_template_daily_login() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_LOGIN) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return true;
	}
};

/* 状态条件: 完成成就 */
class condition_template_accomplish_achievement : public condition_template
{
public:
	condition_template_accomplish_achievement() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ACCOMP_ACHIEVE) {}
	/* 参数说明:
	 ** <1>成就id
	 **/
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params[0] > 0 && imp->GetAchievement().IsCompletedAchievement(params[0]));
	}
};

/*状态条件：某个位置的修炼是否已达成*/
class condition_template_upgrade_practice : public condition_template
{
public:
	condition_template_upgrade_practice() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_UPGRADE_RRACTICE) {}
	/*参数说明:
	 * <1>修炼位置
	 * */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		//检查某个位置的修炼等级是否已达要求
		if (params != NULL && params[0] >= 0 && params[1] >= 0)
		{
			if (imp->GetPraticeLevel(params[0]) >= params[1])
			{
				return true;
			}
		}
		return false;
	}
};

/*状态条件:宠物最高战力*/
class condition_template_pet_fight : public condition_template
{
public:
	condition_template_pet_fight(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_PET_FIGHTING) {}
	/*参数说明:
	 *<1>战力要求
	 * */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0)
		{
			//获取宠物战力
			const pet_fight_capacity_t& pet_info = imp->GetPetMan().GetPetFightCapacity();
			if (pet_info.value >= params[0])
			{
				return true;
			}
		}
		return false;
	}
};

/*状态条件:宝石镶嵌的最高等级 或者品质
 * <1>宝石等级
 * <2>数量
 * */
class condition_template_equip_stone : public condition_template
{
public:
	condition_template_equip_stone(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_EQUIP_STONE) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0 && params[1] > 0)
		{
			return imp->GetTetrahedron().GetGemLevelCount(params[0]) >= params[1];
		}
		return false;
	}
};

/*状态条件:坐骑升级
 * <1> 等级 0表示不限等级
 * <2> 数量
 * */
class condition_template_upgrade_surface : public condition_template
{
public:
	condition_template_upgrade_surface(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_UPGRADE_SURFACE) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] >= 0 && params[1] > 0)
		{
			return imp->GetSurfaceLevelCount(params[0]) >= params[1];
		}
		return false;
	}
};

/*状态条件:改装部位数量
 * <1> 数量
 * */
class condition_template_surface_fashion : public condition_template
{
public:
	condition_template_surface_fashion(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_SURFACE_FASHION) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0)
		{
			return imp->GetSurfaceFashionCount() >= params[0];
		}
		return false;
	}
};

/*状态条件:坐骑解锁色块
 * <1> 数量
 * */
class condition_template_surface_color : public condition_template
{
public:
	condition_template_surface_color(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_SURFACE_COLOR) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0)
		{
			return imp->GetSurfaceActiveColorCount() >= params[0];
		}
		return false;
	}
};

/*状态条件:言灵学习
 * <1> 数量
 * <2> index
 * */
class condition_template_kotodama_learn : public condition_template
{
public:
	condition_template_kotodama_learn(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_KOTODAMA_LEARN) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0)
		{
			return imp->GetKotodama().GetCount() >= params[0];
		}
		else if (params != NULL && params[0] == 0 && params[1] > 0)
		{
			return !!imp->GetKotodama().GetKotodamaByIndex(params[1]);
		}
		return false;
	}
};

/*状态条件:言灵升级
 * <1> 数量
 * */
class condition_template_kotodama_levelup : public condition_template
{
public:
	condition_template_kotodama_levelup(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_KOTODAMA_LEVELUP) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0 && params[1] > 0)
		{
			return imp->GetKotodama().GetCountByLevel(params[0]) >= params[1];
		}
		return false;
	}
};

/*状态条件:修炼等级
 * <1> 0：金币修炼 1：帮贡修炼
 * <2> 等级
 * <3> 数量
 * */
class condition_template_practice_levelup : public condition_template
{
public:
	condition_template_practice_levelup(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_PRACTICE_LEVELUP) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[1] > 0 && params[2] > 0)
		{
			return imp->GetPracticeInfo().GetPraticeCountByLevel(params[0] > 0, params[1]) >= params[2];
		}
		return false;
	}
};

/*状态条件:激活龙语
 * <1> 数量
 * <2> 龙语颜色
 * */
class condition_template_active_longyu : public condition_template
{
public:
	condition_template_active_longyu(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ACTIVE_LONGYU) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0 && params[1] > 0)
		{
			return imp->GetEnhance().GetLongYuCountByQuality(params[1]) >= params[0];
		}
		return false;
	}
};


/* 事件条件: 双人共乘
 *  * 参数说明:
 *   * <1>共乘次数
 *    */
class condition_template_xyxw_invite : public condition_template
{
public:
	condition_template_xyxw_invite() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_XYXW) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 座驾进化
 *  * 参数说明:
 *   * <1>进化次数
 *    */
class condition_template_surface_levelup : public condition_template
{
public:
	condition_template_surface_levelup() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UPGRADE_SURFACE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 激活龙语
 *  * 参数说明:
 *   * <1>次数
 *   * <2>品质
 *    */
class condition_template_active_longyu_quality : public condition_template
{
public:
	condition_template_active_longyu_quality() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGYU) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 1)
		{
			return;
		}

		if (input[0] < params[1])
		{
			return;
		}

		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

class condition_template_enhance_upgrade : public condition_template
{
public:
	condition_template_enhance_upgrade() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ENHANCE_COUNT) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};
class condition_template_enhance_upgrade2 : public condition_template
{
public:
	condition_template_enhance_upgrade2(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ENHANCE_LEVEL) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0)
		{
			return imp->GetEnhance().GetMaxUpgradeLevel() >= params[0];
		}
		return false;
	}
};
class condition_template_guard_star_slot : public condition_template
{
public:
	condition_template_guard_star_slot(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_GUARD_STAR_SLOT_LEVEL) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		if (params != NULL && params[0] > 0)
		{
			return imp->GetGuard().GetMinStarSlotLevel() >= params[0];
		}
		return false;
	}
};

/* 状态条件: 拥有xx阶孩子数量达到多少 */
class condition_template_stage_child_count : public condition_template
{
public:
	condition_template_stage_child_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_CHILD_COUNT) {}
	/* 参数说明:
	 *<1>孩子数量
	 *<2>阶段 0 为不判断
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0)
		{
			int stage = params[1];

			auto filter = [stage](const item_child_bedge * item)->bool
			{
				return (stage == 0) || (item->GetStage() >= stage);
			};

			if (imp->GetChildMan().GetChildCount(imp, filter) >= params[0])
			{
				return true;
			}
		}
		return false;
	}
};

/* 状态条件: 拥有xx层天命孩子数量达到多少 */
class condition_template_destiny_child_count : public condition_template
{
public:
	condition_template_destiny_child_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_DESTINY_CHILD_COUNT) {}

	/* 参数说明:
	 *<1>孩子数量
	 *<2>天命层数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0)
		{
			int destiny_level = params[1];

			auto filter = [destiny_level](const item_child_bedge * item)->bool
			{
				return item->GetDestinyLevel() >= destiny_level;
			};

			if (imp->GetChildMan().GetChildCount(imp, filter) >= params[0])
			{
				return true;
			}
		}
		return false;
	}
};

/* 状态条件: 拥有xx个共鸣晶片孩子数量达到多少 */
class condition_template_device_child_count : public condition_template
{
public:
	condition_template_device_child_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_DEVICE_CHILD_COUNT) {}

	/* 参数说明:
	 *<1>孩子数量
	 *<2>共鸣晶片个数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0)
		{
			int device_count = params[1];

			auto filter = [device_count](const item_child_bedge * item)->bool
			{
				return item->GetDeviceCount() >= device_count;
			};

			if (imp->GetChildMan().GetChildCount(imp, filter) >= params[0])
			{
				return true;
			}
		}
		return false;
	}
};

/* 状态条件: 拥有xx个技能孩子数量达到多少 */
class condition_template_skill_child_count : public condition_template
{
public:
	condition_template_skill_child_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_SKILL_CHILD_COUNT) {}

	/* 参数说明:
	 *<1>孩子数量
	 *<2>技能个数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0)
		{
			int skill_count = params[1];

			auto filter = [skill_count](const item_child_bedge * item)->bool
			{
				return item->GetSkillCount() >= skill_count;
			};

			if (imp->GetChildMan().GetChildCount(imp, filter) >= params[0])
			{
				return true;
			}
		}
		return false;
	}
};

/* 状态条件: 拥有全部专长达到xx的孩子数量达到多少 */
class condition_template_talent_child_count : public condition_template
{
public:
	condition_template_talent_child_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_TALENT_CHILD_COUNT) {}

	/* 参数说明:
	 *<1>孩子数量
	 *<2>全部专长点数
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0)
		{
			int talent_point = params[1];

			auto filter = [talent_point](const item_child_bedge * item)->bool
			{
				return item->GetTalentGeneralPoint() >= talent_point;
			};

			if (imp->GetChildMan().GetChildCount(imp, filter) >= params[0])
			{
				return true;
			}
		}
		return false;
	}
};

/* 状态条件: 拥有xx件xx品质孩子数量达到多少 */
class condition_template_equipment_child_count : public condition_template
{
public:
	condition_template_equipment_child_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_EQUIP_CHILD_COUNT) {}

	/* 参数说明:
	 *<1>孩子数量
	 *<2>装备件数
	 *<2>装备品质
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params != NULL && params[0] > 0)
		{
			int equip_count = params[1];
			int equip_quality = params[2];

			auto filter = [equip_count, equip_quality](const item_child_bedge * item)->bool
			{
				return item->GetGemCountByQuality(equip_quality) >= equip_count;
			};

			if (imp->GetChildMan().GetChildCount(imp, filter) >= params[0])
			{
				return true;
			}
		}
		return false;
	}
};

/* 事件条件: 分解特定等级和品质的装备
 *  * 参数说明:
 *   * <1>次数
 *   * <2>品质
 *   * <3>等级
 *    */
class condition_template_equip_chaijie : public condition_template
{
public:
	condition_template_equip_chaijie() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_CHAIJIE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 2)
		{
			return;
		}

		if (input[0] < params[1])
		{
			return;
		}

		if (input[1] < params[2])
		{
			return;
		}

		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 制作特定座驾
 *  * 参数说明:
 *  * <1> 次数
 *  * <2> 座驾id
 *    */
class condition_template_produce_surface : public condition_template
{
public:
	condition_template_produce_surface() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_PRODUCE_SURFACE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 1)
		{
			return;
		}

		if (input[0] != params[1])
		{
			return;
		}

		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};


/* 事件条件: 激活身份
 *  * 参数说明:
 *  * <1> 次数
 *    */
class condition_template_active_career : public condition_template
{
public:
	condition_template_active_career() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ACTIVE_CAREER) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 店铺上架
 *  * 参数说明:
 *  * <1> 次数
 *    */
class condition_template_auction_open : public condition_template
{
public:
	condition_template_auction_open() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_AUCTION_OPEN) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 获取物品
 * 成就完成条件参数含义：
 * <1>获取途径：0不限
 * <2>次数
 * <3>收集方案：0按物品收集，1按类型收集，2按品质收集，3按品质收集，类型固定为装备
 * <4>参数值：收集方案为0，为物品ID；收集方案为1，为物品类型；收集方案为2，为物品品质3，为物品品质
 */
class condition_template_get_item : public condition_template
{
private:
	enum
	{
		BY_ITEM_TID     = 0,
		BY_ITEM_TYPE    = 1,
		BY_ITEM_QUALITY = 2,
		BY_ITEM_QUALITY_EQUIP = 3,
	};
public:
	condition_template_get_item() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GET_ITEM) {}

	// input: 物品ID，物品类型，物品品质，获取途径, 物品数量
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 5)
		{
			return;
		}
		// 检查获取途径
		if (params[0] != 0 && params[0] != input[3])
		{
			return;
		}
		// 检查收集方案
		switch (params[2])
		{
		case BY_ITEM_TID:
		{
			if (params[3] > 0 && input[0] != params[3])
			{
				return;
			}
		}
		break;
		case BY_ITEM_TYPE:
		{
			if (params[3] > 0 && input[1] != params[3])
			{
				return;
			}
		}
		break;
		case BY_ITEM_QUALITY:
		{
			if (params[3] > 0 && input[2] < params[3])
			{
				return;
			}
		}
		break;
		case BY_ITEM_QUALITY_EQUIP:
		{
			if (input[1] != ITT_EQUIPMENT)
			{
				return;
			}
			if (params[3] > 0 && input[2] < params[3])
			{
				return;
			}
		}
		break;
		default:
		{
			return;
		}
		break;
		}

		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[4], true);
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != data && NULL != params && *data >= params[1]);
		return flag;
	}
};

/* 事件条件: 完成重复任务
 * 成就完成条件参数含义：
 * <1>任务id：仅限不记录完成结果的重复任务
 * <2>次数：
 * <3>任务库id：即任务库id
 * 注：第一、三个条件是或的关系，满足其一就记次
 */
class condition_template_finish_redo_task : public condition_template
{
public:
	condition_template_finish_redo_task() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_FINISH_CANREDO_TASK) {}

	//input: 任务ID，任务库ID
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 2 || input[0] < 0 || input[1] < 0)
		{
			return;
		}
		if ((0 != params[0] && params[0] == input[0]) || (0 != params[2] && params[2] == input[1]) || (0 == params[0] && 0 == params[2]))
		{
			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != data && NULL != params && *data >= params[1]);
		return flag;
	}
};

/* 事件条件: 完成重复任务
 * 成就完成条件参数含义：
 * <1>任务id：仅限不记录完成结果的重复任务
 * <2>次数：
 * <3>任务库id：即任务库id
 * 注：第一、三个条件是或的关系，满足其一就记次
 */
class condition_template_finish_task : public condition_template
{
public:
	condition_template_finish_task() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_FINISH_TASK) {}

	//input: 任务ID，任务库ID
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 2 || input[0] < 0 || input[1] < 0)
		{
			return;
		}
		if ((0 != params[0] && params[0] == input[0]) || (0 != params[2] && params[2] == input[1]) || (0 == params[0] && 0 == params[2]))
		{
			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != data && NULL != params && *data >= params[1]);
		return flag;
	}
};

/*状态条件:获得随从
 * <1> 宠物ID
 * <2> 阶限制
 * <3> 数量
 * */
class condition_template_get_retinue : public condition_template
{
public:
	condition_template_get_retinue(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RETINUE) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params)
		{
			return false;
		}
		if (params[0] != 0)
		{
			auto retinue = imp->GetRetinues().GetRetinue(params[0]);
			return retinue != NULL;
		}
		else if (params[1] != 0)
		{
			return imp->GetRetinues().GetStarCounter(params[1]) >= params[2];
		}
		else
		{
			return imp->GetRetinues().GetSize() >= params[2];
		}
	}
};

/*状态条件:指定伙伴品质
 * <1> 伙伴ID，0表示任意伙伴
 * <2> 品质
 * */
class condition_template_retinue_quality : public condition_template
{
public:
	condition_template_retinue_quality(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RETINUE_QUALITY) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params)
		{
			return false;
		}
		if (params[0] == 0)
		{
			return imp->GetRetinues().GetMaxQuality() >= params[1];
		}
		else
		{
			auto retinue = imp->GetRetinues().GetRetinue(params[0]);
			return retinue && retinue->GetQuality() >= params[1];
		}
	}
};

/*状态条件:指定伙伴等级
 * <1> 伙伴ID
 * <2> 等级
 * */
class condition_template_retinue_level : public condition_template
{
public:
	condition_template_retinue_level(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RETINUE_LEVEL) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0)
		{
			return false;
		}
		auto retinue = imp->GetRetinues().GetRetinue(params[0]);
		return retinue && retinue->GetLevel() >= params[1];
	}
};

/*状态条件:指定伙伴好友度
 * <1> 伙伴ID
 * <2> 好友度
 * */
class condition_template_retinue_amity : public condition_template
{
public:
	condition_template_retinue_amity(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RETINUE_AMITY) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0 || params[1] == 0)
		{
			return false;
		}
		return imp->GetRetinues().GetRetinueAmity(params[0]) >= params[1];
	}
};

/*事件条件:伙伴赠送礼物
 * <1> 伙伴ID
 * <2> 礼物ID
 * <3> 数量
 * */
class condition_template_retinue_gift : public condition_template
{
public:
	condition_template_retinue_gift(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_RETINUE_GIFT) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 2)
		{
			return;
		}
		if (params[0] == input[0] && params[1] == input[1])
		{
			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[2]);
		return flag;
	}
};

/*数量条件:伙伴完成悬赏任务
 * <1> 数量
 * */
class condition_template_retinue_finish_task : public condition_template
{
public:
	condition_template_retinue_finish_task(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_RETINUE_FINISH_TASK) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[0]);
		return flag;
	}
};

/*状态条件:指定伙伴有用私有物
 * <1> 伙伴ID
 * <2> 好友度
 * */
class condition_template_retinue_private_item : public condition_template
{
public:
	condition_template_retinue_private_item(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RETINUE_PRIVATE_ITEM) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0 || params[1] == 0)
		{
			return false;
		}
		auto retinue = imp->GetRetinues().GetRetinue(params[0]);
		return retinue && retinue->HasPrivateItem(params[1]);
	}
};

/*状态条件:指定伙伴解锁时装配色
 * <1> 伙伴ID
 * <2> 数量
 * */
class condition_template_retinue_fashion_color : public condition_template
{
public:
	condition_template_retinue_fashion_color(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RETINUE_FASHION_COLOR) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0 || params[1] == 0)
		{
			return false;
		}
		auto retinue = imp->GetRetinues().GetRetinue(params[0]);
		return retinue && retinue->GetFashionColorCount() >= params[1];
	}
};

/*状态条件:伙伴组合
 * <1> 数量
 * */
class condition_template_retinue_group : public condition_template
{
public:
	condition_template_retinue_group(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RETINUE_COMPOSE_GROUP) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0)
		{
			return false;
		}
		return imp->GetRetinueGroupInfo().GetGroupCount() >= params[0];
	}
};

/*状态条件:获得守护灵
 * <1> 守护灵ID
 * <2> 守护灵数量
 * */
class condition_template_get_guard : public condition_template
{
public:
	condition_template_get_guard(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_GUARD_COUNT) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params)
		{
			return false;
		}
		if (params[0] != 0)
		{
			auto guard = imp->GetGuard().GetGuard(params[0]);
			return guard != NULL;
		}
		else
		{
			return imp->GetGuard().GetGuardCount() >= params[1];
		}
	}
};

/*状态条件:守护灵进阶
 * <1> 守护灵ID
 * <2> 阶段
 * <2> 数量
 * */
class condition_template_guard_phase : public condition_template
{
public:
	condition_template_guard_phase(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_GUARD_PHASE) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params)
		{
			return false;
		}
		if (params[0] == 0)
		{
			return imp->GetGuard().GetPhaseCount(params[1]) >= params[2];
		}
		else
		{
			auto guard = imp->GetGuard().GetGuard(params[0]);
			return guard && guard->GetPhase() >= params[1];
		}
	}
};

/*状态条件:守护灵上阵
 * <1> 数量
 * */
class condition_template_guard_slot : public condition_template
{
public:
	condition_template_guard_slot(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_GUARD_SLOT) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0)
		{
			return false;
		}
		return imp->GetGuard().GetInSlotCount() >= params[0];
	}
};

/*事件条件:守护灵学习技能
 * <1> 数量
 * */
class condition_template_guard_learn_skill : public condition_template
{
public:
	condition_template_guard_learn_skill(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GUARD_LEARN_SKILL) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[0]);
		return flag;
	}
};

/*事件条件:守护灵培养
 * <1> 守护灵ID，0表示任意守护灵
 * <2> 数量
 * */
class condition_template_guard_train: public condition_template
{
public:
	condition_template_guard_train(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GUARD_TRAIN) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count < 2)
		{
			return;
		}
		if (params[0] != 0 && params[0] != input[0])
		{
			return;
		}
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[1], true);
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[1]);
		return flag;
	}
};

/*事件条件:词缀转移
 * <1> 转移次数
 * */
class condition_template_equip_affix_transfer : public condition_template
{
public:
	condition_template_equip_affix_transfer(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_AFFIX_TRANSFER) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[0]);
		return flag;
	}
};

/*事件条件:好友送花票
 * <1> 次数
 * */
class condition_template_friend_bless: public condition_template
{
public:
	condition_template_friend_bless(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_FRIEND_BLESS) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[0]);
		return flag;
	}
};

/*事件条件:坐骑解锁色块
 * <1> 次数
 * */
class condition_template_surface_active_color: public condition_template
{
public:
	condition_template_surface_active_color(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SURFACE_COLOR) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[0]);
		return flag;
	}
};


/*状态条件:拥有特定称号
 * <1> 称号ID
 * */
class condition_template_get_title : public condition_template
{
public:
	condition_template_get_title(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_HAS_TITLE) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0)
		{
			return false;
		}
		return imp->GetTitle().HasTitle(params[0]);
	}
};

/*状态条件:排行榜历史最高排名
 * <1> 头像ID
 * */
class condition_template_get_photo : public condition_template
{
public:
	condition_template_get_photo(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_PHOTO) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0)
		{
			return false;
		}
		return imp->GetPhotoIDInfo().HasPhotoId(imp, PMD_ID, params[0]);
	}
};


/*状态条件:排行榜历史最高排名
 * <1> 排行榜ID
 * <2> 排名
 * */
class condition_template_toplist : public condition_template
{
public:
	condition_template_toplist(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_TOP_RANK) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0 || params[1] == 0)
		{
			return false;
		}
		int high_rank = imp->GetHighRank(params[0]);
		if (high_rank <= 0)
		{
			return false;
		}
		return high_rank <= params[1];
	}
};

/*状态条件:词缀套装
 * <1> 套装数量
 * */
class condition_template_equip_affix_quality : public condition_template
{
public:
	condition_template_equip_affix_quality(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_EQUIP_AFFIX_QUALITY) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0)
		{
			return false;
		}
		return imp->GetEquipAffixQualityCount() >= params[0];
	}
};

/*状态条件:解锁龙语
 * <1> 数量
 * */
class condition_template_get_longyu : public condition_template
{
public:
	condition_template_get_longyu(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_LONGYU) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!params || params[0] == 0)
		{
			return false;
		}
		return imp->GetEnhance().GetLongyuCount() >= params[0];
	}
};

/*状态条件:获得法宝
 * */
class condition_template_get_talisman : public condition_template
{
public:
	condition_template_get_talisman(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_TALISMAN) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		//获得法宝时各种条件检查
		if (params != NULL)
		{
			if (params[0] != 0)
			{
				//当参数0不为0 时检查玩家是否获得指定法宝
				if (imp->HasTalisman(params[0]))
				{
					return true;
				}
			}
			if ((params[0] == 0) && (params[1] > 0))
			{
				//当参数0为0时检查玩家获得法宝的个数是否达到指定值
				if (imp->GetTalismanNums() >= params[1])
				{
					return true;
				}
			}
		}
		return false;
	}
};

/* 状态条件: 结婚条件 */
class condition_template_marry : public condition_template
{
public:
	condition_template_marry() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_MARRY) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->IsMarried();
	}
};

/* 事件条件: 杀怪
 * 成就完成条件参数含义：
 * <1>怪物ID
 * <2>次数
 * 注：杀怪只统计玩家本人杀死或他具有分配权的怪物
 */
class condition_template_kill_monster : public condition_template
{
public:
	condition_template_kill_monster() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_KILL_MONSTER) {}

	// input: 怪物ID
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                                  const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 1 || input[0] <= 0)
		{
			return;
		}
		if (0 == params[0] || params[0] == input[0])
		{
			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		bool flag = (NULL != params && NULL != data && *data >= params[1]);
		return flag;
	}
};

/* 事件条件: 使用物品
 * 参数说明:
 * <1>物品id,0无效
 * <2>使用次数
 */
class condition_template_use_item : public condition_template
{
public:
	condition_template_use_item() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_USE_ITEM) {}
	// input: item_tid, item_count
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 2)
		{
			return;
		}
		if (params[0] > 0 && params[0] == input[0])
		{
			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[1], true);
		}
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[1]);
	}
};
/*事件条件：获得奖励
 *参数说明：
 *<1>奖励模板id
 *<2>领取次数
 */
class condition_template_get_reward: public condition_template
{
public:
	condition_template_get_reward() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GET_GRANT_REWARD) {}

	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 2)
		{
			return;
		}
		if (params[0] > 0 && params[0] == input[0])
		{
			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[1], true);
		}
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data)const
	{
		return (params != NULL && data != NULL && *data >= params[1]);
	}
};


/* 事件条件: 消费钱数
 * 参数说明:
 * <1>类型，0/不限 1/绑定 2/非绑定
 * <2>数量
 */
class condition_template_spend_money : public condition_template
{
public:
	condition_template_spend_money() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SPEND_MONEY) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		int add_count = 0;
		if (NULL == params || NULL == input || input_count != 2)
		{
			return;
		}
		if (params[0] == 0)
		{
			add_count = input[0] + input[1];
		}
		else if (params[0] == 1)
		{
			add_count = input[0];
		}
		else if (params[0] == 2)
		{
			add_count = input[1];
		}
		if (add_count <= 0)
		{
			return;
		}

		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, add_count, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[1]);
	}
};

/**
 * 事件条件：激活阵法
 * 参数说明:
 * <1>阵法ID
 * <2>激活次数
 */
class condition_template_active_formation: public condition_template
{
public:
	condition_template_active_formation() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ACTIVE_FORMATION) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 2 || input[0] <= 0 || input[1] <= 0)
		{
			return;
		}

		if (params[0] > 0 && params[0] == input[0])
		{
			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[1], true);
		}
	}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[1]);
	}
};


/* 事件条件: 装备锻造
 * 参数说明:
 * <1>锻造次数
 */
class condition_template_equip_forge : public condition_template
{
public:
	condition_template_equip_forge() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_FORCE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 装备炼星
 * 参数说明:
 * <1>炼星次数
 */
class condition_template_equip_inc_star : public condition_template
{
public:
	condition_template_equip_inc_star() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_INC_STAR) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 装备炼星继承
 * 参数说明:
 * <1>次数
 */
class condition_template_equip_inherit : public condition_template
{
public:
	condition_template_equip_inherit() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_INHERIT) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 装备镶嵌
 * 参数说明:
 * <1>次数
 */
class condition_template_equip_embed : public condition_template
{
public:
	condition_template_equip_embed() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_ATTACH) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		//if (NULL == params || NULL == input || input_count != 1) return;
		//UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[0], true);
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 装备洗练
 * 参数说明:
 * <1>次数
 */
class condition_template_equip_refine : public condition_template
{
public:
	condition_template_equip_refine() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_REFINE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 物品合成
 * 参数说明:
 * <1>合成方式 0/物品id 1/物品类型 2/物品品阶
 * <2>参数值
 */
class condition_template_item_merge : public condition_template
{
public:
	condition_template_item_merge() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ITEM_MERGE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 3)
		{
			return;
		}
		int inc_count = 0;
		if (params[0] == 0)
		{
			if (params[1] == input[0])
			{
				inc_count = 1;
			}
		}
		else if (params[0] == 1)
		{
			if ((params[1] == 1 && input[1] == ITT_POTION)
			        || (params[1] == 2 && input[1] == ITT_ESTONE)
			        || (params[1] == 3 && input[1] == ITT_LSTONE)
			        || (params[1] == 4 && input[1] == ITT_TASK_DICE))
			{
				inc_count = 1;
			}
		}
		else if (params[0] == 2)
		{
			if (params[1] == input[2])
			{
				inc_count = 1;
			}
		}
		if (inc_count <= 0)
		{
			return;
		}

		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, inc_count, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[1]);
	}
};

/* 事件条件: 国战杀人
 * 参数说明:
 * <1>杀人数
 */
class condition_template_nation_war_kill : public condition_template
{
public:
	condition_template_nation_war_kill() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_NATION_WAR_KILL) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 国战胜利
 * 参数说明:
 * <1>胜负，0表示失败，1表示胜利
 * <2>次数
 */
class condition_template_nation_war_result : public condition_template
{
public:
	condition_template_nation_war_result() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_NATION_WAR_WIN) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		int add_count = 0;
		if (NULL == params || NULL == input || input_count != 1)
		{
			return;
		}
		if (params[0] == 0 && input[0] == 0)
		{
			add_count = 1;
		}
		else if (params[0] == 1 && input[0] == 1)
		{
			add_count = 1;
		}

		if (add_count <= 0)
		{
			return;
		}
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[1]);
	}
};

/* 事件条件: 国战复活
 * 参数说明:
 * <1>次数
 */
class condition_template_nation_war_revive : public condition_template
{
public:
	condition_template_nation_war_revive() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_NATION_WAR_REVIVE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 天赋升级
 * 参数说明:
 * <1>合成方式
 */
class condition_template_talent_upgrade : public condition_template
{
public:
	condition_template_talent_upgrade() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_TALENT_UPGRADE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		if (NULL == params || NULL == input || input_count != 1)
		{
			return;
		}
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[0], true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[0]);
	}
};

/* 事件条件: 进入副本
 * 参数说明:
 * <1>副本
 * <2>次数
 */
class condition_template_enter_instance : public condition_template
{
public:
	condition_template_enter_instance() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ENTER_INSTANCE) {}
	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count,
	                                  size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
	{
		int inc_count = 0;
		if (NULL == params || NULL == input || input_count != 1)
		{
			return;
		}
		if (params[0] != input[0])
		{
			return;
		}

		inc_count = 1;
		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, inc_count, true);
	}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && data != NULL && *data >= params[1]);
	}
};

/* 状态条件: 声望达到 */
class condition_template_reputation : public condition_template
{
public:
	condition_template_reputation() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_REPUTATION) {}
	/* 参数说明:
	 * <1>声望id
	 * <2>声望数值
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params[0] > 0 && imp->GetReputation(params[0]) >= params[1]);
	}
};

/* 状态条件: 激活套牌 */
class condition_template_card_suit : public condition_template
{
public:
	condition_template_card_suit() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO) {}
	/* 参数说明:
	 * <1>套牌id
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params[0] > 0 && imp->GetCardInfo().IsSuitActivated(params[0]));
	}
};
/* 状态条件: 激活套牌 */
class condition_template_card_suit_group : public condition_template
{
public:
	condition_template_card_suit_group() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO_GROUP) {}
	/* 参数说明:
	 * <1>套牌组合id
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params[0] > 0 && imp->GetCardInfo().IsSuitGroupFinish(params[0]));
	}
};
/* 事件条件: 飞剑变化
 * 参数说明:
 * <1> 飞剑数量
 * <2> 飞剑品质: 1绿2蓝3紫4橙5金
 */
class condition_template_flysword_change : public condition_template
{
public:
	condition_template_flysword_change(): condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_INVOKE_WING_SOUL) {}

	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params == NULL || params[0] <= 0 || params[1] <= 0 || params[1] > 5)
		{
			return false;
		}
		return imp->CheckFlyswordByCondition(params[0], params[1]);
	}
};

/* 状态条件: 解锁时装
 * 参数说明:
 * <1> 时装ID
 * <2> 时装类型（所有，衣服，发型，其他）
 * <3> 解锁数量
 */
class condition_template_unlock_fashion : public condition_template
{
public:
	condition_template_unlock_fashion() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_UNLOCK_FASHION) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (params[0] != 0)
		{
			return imp->GetFashion().IsFashionUnlock(params[0]);
		}
		else if (params[1] == 0)
		{
			return imp->GetFashion().GetTotalCount() >= params[2];
		}
		else
		{
			return imp->GetFashion().GetCategoryCount(params[1] - 1) >= params[2];
		}
	}
};

/* 状态条件: 解锁时装色块
 */
class condition_template_fashion_color : public condition_template
{
public:
	condition_template_fashion_color() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_FASHION_COLOR) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return (params != NULL && imp->GetFashion().GetColorCount() >= params[0]);
	}
};

/* 事件条件: 赛车PVP地
 * 参数说明:
 * <1> 名次
 * <2> 次数
 */
class condition_template_car_race_pvp_rank_times: public condition_template
{
public:
	condition_template_car_race_pvp_rank_times():
		condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_CAR_RACE_PVP_RANK_TIMES)
	{
	}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}
		if (params[0] != input[0])
		{
			return;
		}

		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 状态条件: 育宠达人熟练度等级达到 */
class condition_template_breed_proficiency_level : public condition_template
{
public:
	condition_template_breed_proficiency_level() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_BREED_PROFICIENCY_LEVEL) {}
	/* 参数说明:
	 * <1>育宠达人熟练度等级
	 */
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->GetBreed().GetProficiencyLevel() >= params[0];
	}
};

/* 事件条件: 育宠达人获取指定类型基因表达萌宠数量
 * 参数说明:
 * <1> 萌宠类型
 * <2> 萌宠基因表达
 */
class condition_template_breed_cute_pet_gene_express : public condition_template
{
public:
	condition_template_breed_cute_pet_gene_express() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_GENE_EXPRESS)
	{
	}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 2)
		{
			return;
		}

		if (params[0] != 0)
		{
			if (input[0] != params[0])
			{
				return;
			}

			if (params[1] != 0)
			{
				if (input[1] + 1 != params[1])
				{
					return;
				}
			}
		}

		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[2];
	}
};

/* 事件条件: 育宠达人获取指定萌宠毛色数量
 * 参数说明:
 * <1> 萌宠毛色id
 */
class condition_template_breed_cute_pet_hair_colour_id : public condition_template
{
public:
	condition_template_breed_cute_pet_hair_colour_id() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_HAIR_COLOUR_ID)
	{
	}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		if (input[0] != params[0])
		{
			return;
		}

		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 育宠达人获取指定萌宠性格数量
 * 参数说明:
 * <1> 萌宠类型
 * <2> 萌宠基因表达
 * <3> 萌宠性格id
 */
class condition_template_breed_cute_pet_nature_quality_id : public condition_template
{
public:
	condition_template_breed_cute_pet_nature_quality_id() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_NATURE_QUALITY_ID)
	{
	}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 3)
		{
			return;
		}

		if (params[0] != 0)
		{
			if (input[0] != params[0])
			{
				return;
			}

			if (params[1] != 0)
			{
				if (input[1] != params[1])
				{
					return;
				}

				if (input[2] != params[2])
				{
					return;
				}
			}
		}

		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[3];
	}
};

/* 事件条件: 炼金槽位洗练n次
 * 参数说明:
 * <1> 洗练次数
 */
class condition_template_alchemy_refresh_slot_times : public condition_template
{
public:
	condition_template_alchemy_refresh_slot_times() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ALCHEMY_REFRESH_SLOT_TIMES)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, input[0], true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 炼金获取n个x级石头
 * 参数说明:
 * <1> 石头等级
 * <2> 石头数量
 */
class condition_template_alchemy_get_stone : public condition_template
{
public:
	condition_template_alchemy_get_stone() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ALCHEMY_GET_STONE)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 2)
		{
			return;
		}
		if (params[0] != 0)
		{
			if (params[0] != input[0])
			{
				return ;
			}
		}
		if (input[1] == 0)
		{
			return ;
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, input[1], true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 炼金n个符文石升级到x级
 * 参数说明:
 * <1> 符文石等级
 * <2> 数量
 */
class condition_template_alchemy_runestone_level : public condition_template
{
public:
	condition_template_alchemy_runestone_level() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ALCHEMY_RUNESTONE_LEVEL)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}
		if (params[0] != 0)
		{
			if (params[0] != input[0])
			{
				return ;
			}
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 炼金n个槽位升级到x级
 * 参数说明:
 * <1> 槽位等级
 * <2> 数量
 */
class condition_template_alchemy_slot_level : public condition_template
{
public:
	condition_template_alchemy_slot_level() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ALCHEMY_SLOT_LEVEL)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}
		if (params[0] != 0)
		{
			if (params[0] != input[0])
			{
				return ;
			}
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 通用系统战斗力
 *
 * 参数说明:
 * <1> 系统类型: 1座驾 2伙伴 3龙裔 4继承者 5龙茧
 * <2> 战斗力限制
 */
class condition_template_common_fighting_capacity : public condition_template
{
public:
	condition_template_common_fighting_capacity() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SYSTEM_SCORE)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 2)
		{
			return;
		}
		if (params[0] != 0 && params[1] != 0)
		{
			if (params[0] != input[0])
			{
				return ;
			}
			if (params[1] > input[1])
			{
				return ;
			}
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= 1;
	}
};

/* 事件条件: 龙茧槽位洗练等级
 *
 * 参数说明:
 * <1> 数量
 * <2> 等级要求
 */
class condition_template_dragon_cocoon_refresh_slot : public condition_template
{
public:
	condition_template_dragon_cocoon_refresh_slot() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_DRAGON_COCOON_REFRESH_SLOT)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 2)
		{
			return;
		}
		if (params[1] != 0)
		{
			if (params[1] > input[0])
			{
				return ;
			}
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 装备龙茧等级
 *
 * 参数说明:
 * <1> 数量
 * <2> 等级要求
 */
class condition_template_dragon_cocoon_equiped_level : public condition_template
{
public:
	condition_template_dragon_cocoon_equiped_level() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_DRAGON_COCOON_EQUIPED_LEVEL)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, int achievement_tid, size_t cond_count, size_t cond_index, const int *params, const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 2)
		{
			return;
		}
		if (params[1] != 0)
		{
			if (params[1] > input[0])
			{
				return ;
			}
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 状态条件: 炼金激活任意一个套装
 * 参数说明:
 * <1> 套装件数
 */
class condition_template_alchemy_active_any_suit : public condition_template
{
public:
	condition_template_alchemy_active_any_suit() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_ANY_SUIT) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetAlchemyRunestone().CheckHasAnySuit(params[0]);
	}
};

/* 状态条件: 炼金激活指定套装
 * 参数说明:
 * <1> 技能id
 */
class condition_template_alchemy_active_suit : public condition_template
{
public:
	condition_template_alchemy_active_suit() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetAlchemyRunestone().CheckHasSuitBySkill(params[0]);
	}
};

/* 状态条件: 炼金任意套装升到n级
 * 参数说明:
 * <1> 套装件数2 4 6
 * <2> 技能等级
 */
class condition_template_alchemy_active_suit_level : public condition_template
{
public:
	condition_template_alchemy_active_suit_level() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT_LEVEL) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetAlchemyRunestone().CheckHasAnySuitBySkillLevel(params[0], params[1]);
	}
};

/* 状态条件: 炼金总计n个槽位洗练出x以上的数值
 * 参数说明:
 * <1> 属性值x
 * <2> 数量n
 */
class condition_template_alchemy_refresh_slot : public condition_template
{
public:
	condition_template_alchemy_refresh_slot() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ALCHEMY_REFRESH_SLOT) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetAlchemyRunestone().CheckSlotVar(params[0], params[1]);
	}
};

/* 状态条件: 指定序号x龙纹等级达到n级
 * 参数说明:
 * <1> 龙纹序号x
 * <2> 等级n
 */
class condition_template_longwen_level : public condition_template
{
public:
	condition_template_longwen_level() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ALCHEMY_LEVEL) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetLongwen().GetLevel(imp, params[0]) >= params[1];
	}
};

/* 状态条件: 获得n个x品质符石
 * 参数说明:
 * <1> 品质x
 * <2> 数量n
 */
class condition_template_alchemy_rune_count : public condition_template
{
public:
	condition_template_alchemy_rune_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RUNE_STONE_GET) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetAlchemyCircle().GetRuneCount(params[0]) >= params[1];
	}
};

/* 状态条件: 指定遗阵x升级到n级
 * 参数说明:
 * <1> 遗阵序号x
 * <2> 等级n
 */
class condition_template_alchemy_circle_level : public condition_template
{
public:
	condition_template_alchemy_circle_level() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_RUNE_STONE_ARRAY) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetAlchemyCircle().GetCircleLevel(params[0]) >= params[1];
	}
};

/* 状态条件: 英灵X好友度达到N
 * 参数说明:
 * <1> 英灵id X
 * <2> 好友度 N
 */
class condition_template_holy_ghost_amity: public condition_template
{
public:
	condition_template_holy_ghost_amity() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_HOLY_GHOST_AMITY) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetHolyGhost().GetHolyGhostAmity(params[0]) >= params[1];
	}
};

/* 状态条件: 英灵X等级达到N级
 * 参数说明:
 * <1> 英灵id X
 * <2> 等级 N
 */
class condition_template_holy_ghost_level: public condition_template
{
public:
	condition_template_holy_ghost_level() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_HOLY_GHOST_LEVEL) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetHolyGhost().GetHolyGhostLevel(params[0]) >= params[1];
	}
};

/* 状态条件: 解锁英灵N个
 * 参数说明:
 * <1> 英灵个数N
 */
class condition_template_holy_ghost_unlock_count: public condition_template
{
public:
	condition_template_holy_ghost_unlock_count() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_HOLY_GHOST_UNLOCK_COUNT) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetHolyGhost().GetHolyGhostCount() >= params[0];
	}
};

/* 状态条件: 灵树高度N米
 * 参数说明:
 * <1> 灵树高度N
 */
class condition_template_holy_ghost_world_tree_height: public condition_template
{
public:
	condition_template_holy_ghost_world_tree_height() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_HOLY_GHOST_TREE_HEIGHT) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->GetHolyGhost().GetWorldTreeHeight() >= params[0];
	}
};

/* 状态条件: 解锁伙伴
 * 参数说明:
 * <1> 伙伴id
 */
class condition_template_partner_unlock: public condition_template
{
public:
	condition_template_partner_unlock() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_PARTNER_UNLOCK) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		return imp->HasRetinue(params[0]);
	}
};

/* 状态条件: 解锁全部伙伴
 * 参数说明:
 * <1> 伙伴类型
 */
class condition_template_partner_group_unlock: public condition_template
{
public:
	condition_template_partner_group_unlock() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_PARTNER_GROUP_UNLOCK) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		if (!imp || !params)
		{
			return false;
		}
		auto it = GLOBAL_CONFIG.retinue_family_cfg.find(params[0]);
		if (it == GLOBAL_CONFIG.retinue_family_cfg.end())
		{
			return false;
		}
		for (auto v: it->second)
		{
			if (!imp->HasRetinue(v))
			{
				return false;
			}
		}
		return true;
	}
};

/* 事件条件: 座驾焕纹升级总计n次
 * 参数说明:
 * input: use default as nullptr
 * <0> use default as 1
 * params:
 * <0> n
 */
class condition_template_surface_effect_upgrade: public condition_template
{
public:
	condition_template_surface_effect_upgrade(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_VEHICLE_LEVEL) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 座驾焕纹累计n个x阶座驾
 * 参数说明:
 * input:
 * <0> 座驾阶级
 * params:
 * <0> x
 * <1> n
 */
class condition_template_surface_effect_break : public condition_template
{
public:
	condition_template_surface_effect_break() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_VEHICLE_BREAK)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}
		if (params[0] != 0)
		{
			if (params[0] != input[0])
			{
				return;
			}
		}

		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 座驾焕纹累计n个解锁部位x次
 * 参数说明:
 * input:
 * <0> 解锁次数
 * params:
 * <0> x
 * <1> n
 */
class condition_template_surface_effect_unlock : public condition_template
{
public:
	condition_template_surface_effect_unlock() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_VEHICLE_EFFECT)
	{
	}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}
		if (params[0] != 0)
		{
			if (params[0] != input[0])
			{
				return;
			}
		}

		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 熔炼获得Q品质的N个宠物芯片
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> quality
 * params: 策划配置参数
 * <0> Q
 * <1> N
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_smelt_pet_chip: public condition_template
{
public:
	condition_template_smelt_pet_chip(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SMELT_PET_CHIP) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 熔炼获得Q品质的N个宠物技能
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> quality
 * params: 策划配置参数
 * <0> Q
 * <1> N
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_smelt_pet_skill: public condition_template
{
public:
	condition_template_smelt_pet_skill(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SMELT_PET_SKILL) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 熔炼获得Q品质的N个继承者技能
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> quality
 * params: 策划配置参数
 * <0> Q
 * <1> N
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_smelt_heres_skill: public condition_template
{
public:
	condition_template_smelt_heres_skill(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SMELT_HERES_SKILL) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 赠送甜蜜花园花束N次
 * 参数说明:
 * params: 策划配置参数
 * <0> N
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_send_honey_garden_bouquet: public condition_template
{
public:
	condition_template_send_honey_garden_bouquet(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SEND_FLOWERS) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 获得指定守护灵
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> guard_id
 * params: 策划配置参数
 * <0> guard_id
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_get_guard_event: public condition_template
{
public:
	condition_template_get_guard_event(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UNLOCK_PET) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data > 0;
	}
};

/* 事件条件: 指定声望达到多少
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> 声望id
 * <1> 声望值
 * params: 策划配置参数
 * <0> 声望id
 * <1> 声望值
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_reputation_reach_target: public condition_template
{
public:
	condition_template_reputation_reach_target(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_REPUTATION_REACH_TARGET) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 2)
		{
			return;
		}

		if (input[0] == params[0])
		{
			int delta = input[1];
			IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = in_progress_map.find(achievement_tid);
			if (it != in_progress_map.end())
			{
				in_progress_achievement_t *pAchievement = &(it->second);
				if (NULL == pAchievement)
				{
					return;
				}
				const int *data_ptr = pAchievement->GetCondData(cond_index);
				if (!data_ptr)
				{
					return;
				}
				delta = input[1] - *data_ptr;
			}

			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, delta, true);
		}
	}

	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 获得指定类型指定数量的伙伴
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> retinue_type
 * params: 策划配置参数
 * <0> retinue_type
 * <1> count
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_get_all_retinue_event: public condition_template
{
public:
	condition_template_get_all_retinue_event(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UNLOCK_PARTNER_GROUP) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}

	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 获得指定伙伴
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> retinue_id
 * params: 策划配置参数
 * <0> retinue_id
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_get_retinue_event: public condition_template
{
public:
	condition_template_get_retinue_event(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UNLOCK_PARTNER) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data > 0;
	}
};

/* 事件条件: 赋能连续x(成功或失败)n次，相反结果会重置data
 * 参数说明:
 * input:
 * <0> 是否成功
 * params:
 * <0> x 1成功，0失败
 * <1> n 连续次数
 */
class condition_template_enhance_continuous_result: public condition_template
{
public:
	condition_template_enhance_continuous_result() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ENHANCE_CONTINUOUS_RESULT) {}
	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (!pimp || !params || !input || input_count != 1)
		{
			return;
		}

		int delta = 1;
		if (input[0] != params[0])
		{
			in_progress_achievement_t *pAchievement = NULL;
			IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = in_progress_map.find(achievement_tid);
			if (it == in_progress_map.end())
			{
				return;
			}

			pAchievement = &(it->second);
			if (NULL == pAchievement)
			{
				return;
			}
			const int *data_ptr = pAchievement->GetCondData(cond_index);
			if (!data_ptr)
			{
				return;
			}

			delta = -1 * (*data_ptr);
		}
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, delta, true);
	}
	bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};



/* 事件条件: 累计放生鱼n次
 * 参数说明:
 * input: 事件调用函数传入参数
 * params: 策划配置参数
 * <0> n
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_release_fish: public condition_template
{
public:
	condition_template_release_fish(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_RELEASE_FISH) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 累计调一定重量的鱼n次
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> 重量
 * params: 策划配置参数
 * <0> 重量
 * <1> n
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_fish: public condition_template
{
public:
	condition_template_fish(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_FISH) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] >= params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

/* 事件条件: 解锁指定符咒
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> rune_id
 * params: 策划配置参数
 * <0> rune_id
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_unlock_alchemy_circle_rune: public condition_template
{
public:
	condition_template_unlock_alchemy_circle_rune(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UNLOCK_RUNE) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data > 0;
	}
};

/* 事件条件: 指定符咒升星到n星
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> rune_id
 * <1> stage
 * params: 策划配置参数
 * <0> rune_id
 * <1> stage
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_upstage_alchemy_circle_rune: public condition_template
{
public:
	condition_template_upstage_alchemy_circle_rune(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UPGRADE_RUNE) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] == params[0] && input[1] > *in_progress_map[achievement_tid].GetCondData(cond_index))
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, input[1] - *in_progress_map[achievement_tid].GetCondData(cond_index), true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return (*data >= params[1]);
	}
};

/* 事件条件: 解锁指定龙魂
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> longhun_id
 * params: 策划配置参数
 * <0> longhun_id
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_unlock_longhun: public condition_template
{
public:
	condition_template_unlock_longhun(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGHUI_NEW) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data > 0;
	}
};

/* 事件条件: 解锁指定龙语
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> longyu_id
 * params: 策划配置参数
 * <0> longyu_id
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_unlock_longyu: public condition_template
{
public:
	condition_template_unlock_longyu(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGYU_NEW) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data > 0;
	}
};

/* 事件条件: 解锁指定套装
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> equip_suit_id
 * params: 策划配置参数
 * <0> equip_suit_id
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_unlock_fashion_suit: public condition_template
{
public:
	condition_template_unlock_fashion_suit(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION_NEW) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data > 0;
	}
};

/* 事件条件: 解锁指定时装
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> fashion_index
 * params: 策划配置参数
 * <0> fashion_index
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_unlock_fashion_new: public condition_template
{
public:
	condition_template_unlock_fashion_new(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION_SPECIAL) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data > 0;
	}
};

/* 事件条件: 解锁指定座驾幻化
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> surface_tid
 * params: 策划配置参数
 * <0> surface_tid
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_unlock_surface: public condition_template
{
public:
	condition_template_unlock_surface(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_UNLOCK_VEHICLE) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		if (input[0] == params[0])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= 0;
	}
};

/* 事件条件: 合成n个橙色龙语
 * 参数说明:
 * input: 事件调用函数传入参数
 * params: 策划配置参数
 * <0> n
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_compose_suboptimal_longyu: public condition_template
{
public:
	condition_template_compose_suboptimal_longyu(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_ORANGE) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 合成n个金色龙语
 * 参数说明:
 * input: 事件调用函数传入参数
 * params: 策划配置参数
 * <0> n
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_compose_optimal_longyu: public condition_template
{
public:
	condition_template_compose_optimal_longyu(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_TOTALL) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 合成龙语失败n次
 * 参数说明:
 * input: 事件调用函数传入参数
 * params: 策划配置参数
 * <0> n
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_compose_longyu_failed: public condition_template
{
public:
	condition_template_compose_longyu_failed(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_FAILURE) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[0];
	}
};

/* 事件条件: 合成龙语连续成功/失败n次
 * 参数说明:
 * input: 事件调用函数传入参数
 * <0> true/false
 * <1> quality
 * params: 策划配置参数
 * <0> true/false
 * <1> n
 * <2> quality
 * 变量说明:
 * in_progress_map: 所有成就计数存储结构
 * data: 成就计数
 */
class condition_template_compose_longyu_continuously: public condition_template
{
public:
	condition_template_compose_longyu_continuously(): condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_CONTINUOUS) {}

	void UpdateInProgressData(gplayer_imp *pimp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
	                          int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
	                          const int *input = nullptr, int input_count = 0) const
	{
		int delta = 1;
		if (input[0] != params[0])
		{
			in_progress_achievement_t *pAchievement = nullptr;
			IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = in_progress_map.find(achievement_tid);
			if (it == in_progress_map.end())
			{
				return;
			}

			pAchievement = &(it->second);
			if (pAchievement == nullptr)
			{
				return;
			}
			const int *data_ptr = pAchievement->GetCondData(cond_index);
			if (!data_ptr)
			{
				return;
			}

			delta = -1 * (*data_ptr);
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, delta, true);
		}
		else if (input[1] >= params[2])
		{
			UpdateInProgressAchievement(pimp, in_progress_map, achievement_tid, cond_count, cond_index, delta, true);
		}
	}
	virtual bool Check(gplayer_imp *pimp, const int *params, const int *data) const
	{
		if (!pimp || !params || !data)
		{
			return false;
		}
		return *data >= params[1];
	}
};

achievement_template::achievement_template(const ACHIEVEMENT_ESSENCE& ess) : _valid(false), _tid(0), _unlock_state_condition_mask(0), _complete_state_condition_mask(0), _complete_event_condition_mask(0),
	_unlock_match_num(EXP_ACHIEVE_MATCH_ALL), _complete_match_num(EXP_ACHIEVE_MATCH_ALL)
{
	_tid = ess.id;
	_type = ess.type;
	_prizeScore = ess.achieve_score;
	_section_mask = ess.achieve_section;
	_need_broadcast = ess.broadcast;
	_broadcast_channel = ess.broadcast_channel;
	_lib_type = ess.lib_type;

	_server_open_time_begin = ess.server_open_time_begin;
	_server_open_time_end = ess.server_open_time_end;
	_player_create_time_begin = ess.player_create_time_begin;
	_player_create_time_end = ess.player_create_time_end;
	_activity_id = ess.activity_id;

	//_unlock_match_num = ess.unlock_match_num; TODO
	_complete_match_num = ess.complete_match_num;
	_title_id = ess.id_title_out;
	_award_id = ess.id_grant_award;
	_task_id = ess.id_complete_task;
	//__PRINTF("加载成就模板 %d：%s（type=%d）\n", ess.id, temp_name, _type);
	//解锁状态条件
	//__PRINTF("\t解锁条件（状态）：");
	for (size_t i = 0; i < UNLOCK_STATE_CONDITIOND_COUNT; ++i)
	{
#ifdef USE_CONVEX
		const ACHIEVEMENT_ESSENCE_UNLOCK_STATE_CONDITION& usc = ess.unlock_state_condition[i];
#else
		const ACHIEVEMENT_ESSENCE::UNLOCK_STATE_COND& usc = ess.unlock_state_condition[i];
#endif
		if (EXP_ACHIEVE_STATECOND_NONE == usc.condition)
		{
			continue;
		}
		//__PRINTF("{%d [%d,%d,%d,%d]} ", usc.condition, usc.param[0], usc.param[1], usc.param[2], usc.param[3]);
		const condition_template *pTemplate = achievement_manager::GetInstance().GetConditionTemplate(CT_STATE, usc.condition);
#if 1
		ASSERT(pTemplate);
#else
		if (!pTemplate)
		{
			continue;
		}
#endif
		//_unlock_state_condition_mask |= pTemplate->GetMask();
		_unlock_state_condition_mask.set(pTemplate->GetID(), true);
		condition_t temp(pTemplate);
		temp.SetParam(usc.param);
		_unlock_state_condition_vec.push_back(temp);
	}
	//完成状态条件
	//__PRINTF("\n\t完成条件（状态）：");
	for (size_t i = 0; i < COMPLETE_STATE_CONDITIOND_COUNT; ++i)
	{
#ifdef USE_CONVEX
		const ACHIEVEMENT_ESSENCE_ACHIEVE_STATE_CONDITION& usc = ess.achieve_state_condition[i];
#else
		const ACHIEVEMENT_ESSENCE::ACHIEVE_STATE_COND& usc = ess.achieve_state_condition[i];
#endif
		if (EXP_ACHIEVE_STATECOND_NONE == usc.condition)
		{
			continue;
		}
		//__PRINTF("{%d [%d,%d,%d,%d]} ", usc.condition, usc.param[0], usc.param[1], usc.param[2], usc.param[3]);
		const condition_template *pTemplate = achievement_manager::GetInstance().GetConditionTemplate(CT_STATE, usc.condition);
#if 1
		ASSERT(pTemplate);
#else
		if (!pTemplate)
		{
			continue;
		}
#endif
		//_complete_state_condition_mask |= pTemplate->GetMask();
		_complete_state_condition_mask.set(pTemplate->GetID(), true);
		condition_t temp(pTemplate);
		temp.SetParam(usc.param);
		_complete_state_condition_vec.push_back(temp);
	}
	//完成事件条件
	//__PRINTF("\n\t完成条件（事件）：");
	for (size_t i = 0; i < COMPLETE_EVENT_CONDITIOND_COUNT; ++i)
	{
#ifdef USE_CONVEX
		const ACHIEVEMENT_ESSENCE_ACHIEVE_EVENT_CONDITION& usc = ess.achieve_event_condition[i];
#else
		const ACHIEVEMENT_ESSENCE::ACHIEVE_EVENT_COND& usc = ess.achieve_event_condition[i];
#endif
		if (EXP_ACHIEVE_EVENTCOND_NONE == usc.condition)
		{
			break;    //不允许夹杂无效条件
		}
		//__PRINTF("{%d [%d,%d,%d,%d]} ", usc.condition, usc.param[0], usc.param[1], usc.param[2], usc.param[3]);
		const condition_template *pTemplate = achievement_manager::GetInstance().GetConditionTemplate(CT_EVENT, usc.condition);
#if 1
		ASSERT(pTemplate);
#else
		if (!pTemplate)
		{
			continue;
		}
#endif
		//_complete_event_condition_mask |= pTemplate->GetMask();
		_complete_event_condition_mask.set(pTemplate->GetID(), true);
		condition_t temp(pTemplate);
		temp.SetParam(usc.param);
		_complete_event_condition_vec.push_back(temp);
	}
	//__PRINTF("\n");
	if (_unlock_state_condition_vec.size() <= 0 && _complete_state_condition_vec.size() <= 0 && _complete_event_condition_vec.size() <= 0)
	{
		__PRINTF("发现不正确的成就模板 %d：所有条件都为空\n", ess.id);
		return; //3个条件都为空认为无效
	}
	_valid = true;
}

bool achievement_template::CheckExpiration(gplayer_imp *imp, time_t timestamp) const
{
	time_t cur_local_timestamp = gmatrix::GetInstance().GetSysTime() + gmatrix::GetInstance().GetGmtOff();
	if (EXPACHIEVETYPE_DAILY == _type || EXPACHIEVETYPE_NATION_WAR == _type || _lib_type == EXP_ACHIEVEMENT_LIB_ACTIVITY)
	{
		time_t last_local_timestamp = timestamp + gmatrix::GetInstance().GetGmtOff();
		if (last_local_timestamp / SEC_IN_DAY < cur_local_timestamp / SEC_IN_DAY)
		{
			return true;
		}
	}
	else if (EXPACHIEVETYPE_DAY_14 == _type)
	{
		time_t last_local_timestamp = timestamp + gmatrix::GetInstance().GetGmtOff();
		if (cur_local_timestamp / SEC_IN_DAY - last_local_timestamp / SEC_IN_DAY >= 14)
		{
			__PRINTF("检查14天过期成就：%d, last_timestamp=%ld, local_timestamp=%ld, 过期！\n", _tid, last_local_timestamp, cur_local_timestamp);
			return true;
		}
	}
	else if (EXPACHIEVETYPE_ACTIVITY_LIMIT == _type)
	{
		//活动限制成就，判断活动是否开启，开服天数以及注册时间
		if (_activity_id != 0 && !activity_manager::GetInstance().IsActivityOpen(_activity_id))
		{
			//活动已经关闭，认为过期
			return true;
		}
		if (_server_open_time_end != 0)
		{
			//开服多少天之后过期
			int gmt_offset = gmatrix::GetInstance().GetGmtOff();
			time_t server_open_timestamp = gmatrix::GetInstance().GetServerOpenTime() + gmt_offset;
			if (GNET::g_center != 0)
			{
				LOG_TRACE("achievement_template checkexpiration: center server not check expiration, roleid=%ld,level=%d,achievementid=%d,zoneid=%d,server_open_time=%ld",
				          imp->Parent()->ID.id, imp->GetLevel(), _tid, MERGE_ZONE(imp->Parent()->ID.id), server_open_timestamp);
				return false;

				//server_open_timestamp = gmatrix::GetInstance().GetRoamServerOpenTime(MERGE_ZONE(imp->Parent()->ID.id));
				//if (server_open_timestamp <= 0)
				//{
				//	LOG_TRACE("achievement_template checkexpiration: role server_open_timestamp is 0, roleid=%ld,level=%d,achievementid=%d,zoneid=%d,server_open_time=%ld", imp->Parent()->ID.id, imp->GetLevel(), _tid, MERGE_ZONE(imp->Parent()->ID.id), server_open_timestamp);
				//	return false;
				//}
				//server_open_timestamp += gmt_offset;
			}
			if ((server_open_timestamp / SEC_IN_DAY + _server_open_time_end) <= cur_local_timestamp / SEC_IN_DAY)
			{
				LOG_TRACE("achievement_template checkexpiration: expiration, roleid=%ld,level=%d,achievementid=%d,zoneid=%d,server_open_time=%ld,open_time_end=%d,cur_local_time=%ld,gmt_offset=%d", imp->Parent()->ID.id, imp->GetLevel(), _tid, MERGE_ZONE(imp->Parent()->ID.id), server_open_timestamp, _server_open_time_end, cur_local_timestamp, gmt_offset);
				return true;
			}
		}
		if (_player_create_time_end != 0)
		{
			//注册多少天之后过期
			time_t player_create_timestamp = imp->GetParent()->create_time + gmatrix::GetInstance().GetGmtOff();
			if ((player_create_timestamp / SEC_IN_DAY + _player_create_time_end) <= cur_local_timestamp / SEC_IN_DAY)
			{
				return true;
			}
		}
	}
	return false;
}

bool achievement_template::CheckCanUnlock(gplayer_imp *imp) const
{
	if (!_valid)
	{
		return false;
	}
	if (!(_section_mask & achievement_manager::GetInstance().GetSectionMask()))
	{
		return false;
	}
	if (EXPACHIEVETYPE_ACTIVITY_LIMIT == _type)
	{
		//活动限制成就，判断活动是否开启，开服天数以及注册时间
		if (_activity_id != 0 && !activity_manager::GetInstance().IsActivityOpen(_activity_id))
		{
			//活动没有开启，不能解锁
			return false;
		}
		time_t cur_local_timestamp = gmatrix::GetInstance().GetSysTime() + gmatrix::GetInstance().GetGmtOff();
		time_t server_open_timestamp = gmatrix::GetInstance().GetServerOpenTime() + gmatrix::GetInstance().GetGmtOff();
		time_t player_create_timestamp = imp->GetParent()->create_time + gmatrix::GetInstance().GetGmtOff();
		if (_server_open_time_begin != 0)
		{
			if ((server_open_timestamp / SEC_IN_DAY + _server_open_time_begin) > (cur_local_timestamp / SEC_IN_DAY + 1))
			{
				return false;
			}
		}
		if (_server_open_time_end != 0)
		{
			int gmt_offset = gmatrix::GetInstance().GetGmtOff();
			if (GNET::g_center != 0)
			{
				server_open_timestamp = gmatrix::GetInstance().GetRoamServerOpenTime(MERGE_ZONE(imp->Parent()->ID.id));
				if (server_open_timestamp <= 0)
				{
					LOG_TRACE("achievement_template checkcanunlock: role server_open_timestamp is 0, roleid=%ld,level=%d,achievementid=%d,zoneid=%d,server_open_time=%ld", imp->Parent()->ID.id, imp->GetLevel(), _tid, MERGE_ZONE(imp->Parent()->ID.id), server_open_timestamp);
					return false;
				}
				server_open_timestamp += gmt_offset;
			}
			if ((server_open_timestamp / SEC_IN_DAY + _server_open_time_end) <= cur_local_timestamp / SEC_IN_DAY)
			{
				return false;
			}
		}
		if (_player_create_time_begin != 0)
		{
			if ((player_create_timestamp / SEC_IN_DAY + _player_create_time_begin) > (cur_local_timestamp / SEC_IN_DAY + 1))
			{
				return false;
			}
		}
		if (_player_create_time_end != 0)
		{
			if ((player_create_timestamp / SEC_IN_DAY + _player_create_time_end) <= cur_local_timestamp / SEC_IN_DAY)
			{
				return false;
			}
		}
	}
	if (_unlock_match_num == EXP_ACHIEVE_MATCH_ALL)
	{
		for (CONDITION_VECTOR::const_iterator it = _unlock_state_condition_vec.begin(); it != _unlock_state_condition_vec.end(); ++it)
		{
			if (!it->Check(imp))
			{
				return false;
			}
		}
		return true;
	}
	else if (_unlock_match_num == EXP_ACHIEVE_MATCH_ONE)
	{
		for (CONDITION_VECTOR::const_iterator it = _unlock_state_condition_vec.begin(); it != _unlock_state_condition_vec.end(); ++it)
		{
			if (it->Check(imp))
			{
				return true;
			}
		}
		return false;
	}
	else
	{
		ASSERT(false);
	}
	return true;
}

bool achievement_template::CheckComplete(gplayer_imp *imp, const IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map) const
{
	if (!_valid)
	{
		return false;
	}
	if (!(_section_mask & achievement_manager::GetInstance().GetSectionMask()))
	{
		return false;
	}
	if (EXPACHIEVETYPE_ACTIVITY_LIMIT == _type)
	{
		//活动限制成就，判断活动是否开启，开服天数以及注册时间
		if (_activity_id != 0 && !activity_manager::GetInstance().IsActivityOpen(_activity_id))
		{
			//活动没有开启，不能完成
			return false;
		}
		time_t cur_local_timestamp = gmatrix::GetInstance().GetSysTime() + gmatrix::GetInstance().GetGmtOff();
		time_t server_open_timestamp = gmatrix::GetInstance().GetServerOpenTime() + gmatrix::GetInstance().GetGmtOff();
		time_t player_create_timestamp = imp->GetParent()->create_time + gmatrix::GetInstance().GetGmtOff();
		if (_server_open_time_begin != 0)
		{
			if ((server_open_timestamp / SEC_IN_DAY + _server_open_time_begin) > (cur_local_timestamp / SEC_IN_DAY + 1))
			{
				return false;
			}
		}
		if (_server_open_time_end != 0)
		{
			int gmt_offset = gmatrix::GetInstance().GetGmtOff();
			if (GNET::g_center != 0)
			{
				server_open_timestamp = gmatrix::GetInstance().GetRoamServerOpenTime(MERGE_ZONE(imp->Parent()->ID.id));
				if (server_open_timestamp <= 0)
				{
					LOG_TRACE("achievement_template checkcomplete: role server_open_timestamp is 0, roleid=%ld,level=%d,achievementid=%d,zoneid=%d,server_open_time=%ld", imp->Parent()->ID.id, imp->GetLevel(), _tid, MERGE_ZONE(imp->Parent()->ID.id), server_open_timestamp);
					return false;
				}
				server_open_timestamp += gmt_offset;
			}
			if ((server_open_timestamp / SEC_IN_DAY + _server_open_time_end) <= cur_local_timestamp / SEC_IN_DAY)
			{
				return false;
			}
		}
		if (_player_create_time_begin != 0)
		{
			if ((player_create_timestamp / SEC_IN_DAY + _player_create_time_begin) > (cur_local_timestamp / SEC_IN_DAY + 1))
			{
				return false;
			}
		}
		if (_player_create_time_end != 0)
		{
			if ((player_create_timestamp / SEC_IN_DAY + _player_create_time_end) <= cur_local_timestamp / SEC_IN_DAY)
			{
				return false;
			}
		}
	}
	if (_complete_match_num == EXP_ACHIEVE_MATCH_ALL)
	{
		// Check state condition if any
		for (CONDITION_VECTOR::const_iterator it = _complete_state_condition_vec.begin(); it != _complete_state_condition_vec.end(); ++it)
		{
			if (!it->Check(imp))
			{
				return false;
			}
		}
		// Check event condition if any
		if (_complete_event_condition_vec.size() > 0)
		{
			IN_PROGRESS_ACHIEVEMENT_MAP::const_iterator it = in_progress_map.find(_tid);
			if (it == in_progress_map.end())
			{
				return false;
			}
			for (size_t index = 0; index < _complete_event_condition_vec.size(); ++index)
			{
				const int *cond_data = it->second.GetCondData(index);
				if (NULL == cond_data)
				{
					return false;
				}
				if (!_complete_event_condition_vec[index].Check(imp, cond_data))
				{
					return false;
				}
			}
		}
		return true;
	}
	else if (_complete_match_num == EXP_ACHIEVE_MATCH_ONE)
	{
		for (CONDITION_VECTOR::const_iterator it = _complete_state_condition_vec.begin(); it != _complete_state_condition_vec.end(); ++it)
		{
			if (it->Check(imp))
			{
				return true;
			}
		}
		// Check event condition if any
		if (_complete_event_condition_vec.size() > 0)
		{
			IN_PROGRESS_ACHIEVEMENT_MAP::const_iterator it = in_progress_map.find(_tid);
			if (it != in_progress_map.end())
			{
				for (size_t index = 0; index < _complete_event_condition_vec.size(); ++index)
				{
					const int *cond_data = it->second.GetCondData(index);
					if (NULL == cond_data)
					{
						continue;
					}
					if (_complete_event_condition_vec[index].Check(imp, cond_data))
					{
						return true;
					}
				}
			}
		}
		return false;
	}
	else
	{
		ASSERT(false);
	}

	return true;
}

bool achievement_template::UpdateInProgress(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, EXP_ACHIEVE_EVENT_CONDITION target_event_cond, const int *input, int input_count) const
{
	if (!_valid)
	{
		return false;
	}
	if (!(_section_mask & achievement_manager::GetInstance().GetSectionMask()))
	{
		return false;
	}
	for (size_t index = 0; index < _complete_event_condition_vec.size(); ++index)
	{
		const condition_t& cond = _complete_event_condition_vec[index];
		ASSERT(NULL != cond.GetTemplate());
		if (CT_EVENT == cond.GetTemplate()->GetType() && (target_event_cond == cond.GetTemplate()->GetID()))
		{
			cond.UpdateInProgressData(imp, in_progress_map, _tid, _complete_event_condition_vec.size(), index, input, input_count);
		}
	}
	return true;
}

achievement_manager::achievement_manager()
{
}

achievement_manager::~achievement_manager()
{
	for (CONDITION_TEMPLATE_MAP::iterator it = _condition_template_map.begin(); it != _condition_template_map.end(); ++it)
	{
		if (it->second)
		{
			delete it->second;
		}
	}
	for (ACHIEVEMENT_TEMPLATE_MAP::iterator it = _achievement_template_map.begin(); it != _achievement_template_map.end(); ++it)
	{
		if (it->second)
		{
			delete it->second;
		}
	}
}

bool achievement_manager::Load(elementdataman& data_man)
{
	//前提数据
#define INIT_CONDITION_TEMPLATE(name) \
	name* p##name = new name; \
	if (_condition_template_map.find(abase::pair<int,int>(p##name->GetType(),p##name->GetID())) !=_condition_template_map.end())\
	{\
		__PRINTF("MISTAKE %d-%d\n", p##name->GetType(), p##name->GetID());\
	}\
	_condition_template_map [abase::pair<int,int>(p##name->GetType(),p##name->GetID())] = p##name;

	INIT_CONDITION_TEMPLATE(condition_template_level)
	INIT_CONDITION_TEMPLATE(condition_template_money)
	INIT_CONDITION_TEMPLATE(condition_template_pk_value)
	INIT_CONDITION_TEMPLATE(condition_template_normal_task)
	INIT_CONDITION_TEMPLATE(condition_template_learn_skill)
	INIT_CONDITION_TEMPLATE(condition_template_seven_crime_sword_level_average)
	INIT_CONDITION_TEMPLATE(condition_template_seven_crime_sacrifice_quality_average)
	INIT_CONDITION_TEMPLATE(condition_template_make_friend)
	INIT_CONDITION_TEMPLATE(condition_template_join_mafia)
	INIT_CONDITION_TEMPLATE(condition_template_achievement_grade)
	INIT_CONDITION_TEMPLATE(condition_template_equip_quality_count)
	INIT_CONDITION_TEMPLATE(condition_template_equip_star)
	INIT_CONDITION_TEMPLATE(condition_template_inventory_size)
	INIT_CONDITION_TEMPLATE(condition_template_fight_capacity)
	INIT_CONDITION_TEMPLATE(condition_template_daily_login)
	INIT_CONDITION_TEMPLATE(condition_template_inventory_extra_size)
	INIT_CONDITION_TEMPLATE(condition_template_unlock_fashion)
	INIT_CONDITION_TEMPLATE(condition_template_fashion_color)
	INIT_CONDITION_TEMPLATE(condition_template_get_retinue)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_quality)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_level)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_amity)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_private_item)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_fashion_color)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_group)
	INIT_CONDITION_TEMPLATE(condition_template_get_guard)
	INIT_CONDITION_TEMPLATE(condition_template_guard_phase)
	INIT_CONDITION_TEMPLATE(condition_template_guard_slot)
	INIT_CONDITION_TEMPLATE(condition_template_get_title)
	INIT_CONDITION_TEMPLATE(condition_template_get_photo)
	INIT_CONDITION_TEMPLATE(condition_template_toplist)
	INIT_CONDITION_TEMPLATE(condition_template_equip_affix_quality)
	INIT_CONDITION_TEMPLATE(condition_template_get_longyu)
	INIT_CONDITION_TEMPLATE(condition_template_upgrade_surface)
	INIT_CONDITION_TEMPLATE(condition_template_surface_fashion)
	INIT_CONDITION_TEMPLATE(condition_template_surface_color)
	INIT_CONDITION_TEMPLATE(condition_template_kotodama_learn)
	INIT_CONDITION_TEMPLATE(condition_template_kotodama_levelup)
	INIT_CONDITION_TEMPLATE(condition_template_practice_levelup)
	INIT_CONDITION_TEMPLATE(condition_template_active_longyu)
	INIT_CONDITION_TEMPLATE(condition_template_dragonborn_aptitude_average)
	INIT_CONDITION_TEMPLATE(condition_template_dragonborn_evo_break_cost)
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_active_any_suit)
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_active_suit)
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_active_suit_level)
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_refresh_slot)
	INIT_CONDITION_TEMPLATE(condition_template_surface_effect_upgrade)
	INIT_CONDITION_TEMPLATE(condition_template_surface_effect_break)
	INIT_CONDITION_TEMPLATE(condition_template_surface_effect_unlock)
	INIT_CONDITION_TEMPLATE(condition_template_smelt_pet_chip)
	INIT_CONDITION_TEMPLATE(condition_template_smelt_pet_skill)
	INIT_CONDITION_TEMPLATE(condition_template_smelt_heres_skill)
	INIT_CONDITION_TEMPLATE(condition_template_send_honey_garden_bouquet)
	INIT_CONDITION_TEMPLATE(condition_template_get_all_retinue_event)
	INIT_CONDITION_TEMPLATE(condition_template_get_retinue_event)
	INIT_CONDITION_TEMPLATE(condition_template_enhance_continuous_result)
	INIT_CONDITION_TEMPLATE(condition_template_release_fish)
	INIT_CONDITION_TEMPLATE(condition_template_unlock_alchemy_circle_rune)
	INIT_CONDITION_TEMPLATE(condition_template_upstage_alchemy_circle_rune)
	INIT_CONDITION_TEMPLATE(condition_template_unlock_longhun)
	INIT_CONDITION_TEMPLATE(condition_template_unlock_longyu)
	INIT_CONDITION_TEMPLATE(condition_template_unlock_fashion_suit)
	INIT_CONDITION_TEMPLATE(condition_template_unlock_fashion_new)
	INIT_CONDITION_TEMPLATE(condition_template_unlock_surface)
	INIT_CONDITION_TEMPLATE(condition_template_compose_suboptimal_longyu)
	INIT_CONDITION_TEMPLATE(condition_template_compose_optimal_longyu)
	INIT_CONDITION_TEMPLATE(condition_template_compose_longyu_failed)
	INIT_CONDITION_TEMPLATE(condition_template_compose_longyu_continuously)
	INIT_CONDITION_TEMPLATE(condition_template_reputation_reach_target)
	INIT_CONDITION_TEMPLATE(condition_template_fish)
	INIT_CONDITION_TEMPLATE(condition_template_longwen_level)
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_rune_count)
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_circle_level)
	INIT_CONDITION_TEMPLATE(condition_template_holy_ghost_amity)
	INIT_CONDITION_TEMPLATE(condition_template_holy_ghost_level)
	INIT_CONDITION_TEMPLATE(condition_template_holy_ghost_unlock_count)
	INIT_CONDITION_TEMPLATE(condition_template_holy_ghost_world_tree_height)
	INIT_CONDITION_TEMPLATE(condition_template_partner_unlock)
	INIT_CONDITION_TEMPLATE(condition_template_partner_group_unlock)

	INIT_CONDITION_TEMPLATE(condition_template_get_item)
	INIT_CONDITION_TEMPLATE(condition_template_finish_task)
	INIT_CONDITION_TEMPLATE(condition_template_finish_redo_task)
	INIT_CONDITION_TEMPLATE(condition_template_kill_monster)
	INIT_CONDITION_TEMPLATE(condition_template_use_item)
	INIT_CONDITION_TEMPLATE(condition_template_spend_money)
	INIT_CONDITION_TEMPLATE(condition_template_equip_forge)
	INIT_CONDITION_TEMPLATE(condition_template_equip_inc_star)
	INIT_CONDITION_TEMPLATE(condition_template_equip_inherit)
	INIT_CONDITION_TEMPLATE(condition_template_equip_embed)
	INIT_CONDITION_TEMPLATE(condition_template_equip_refine)
	INIT_CONDITION_TEMPLATE(condition_template_item_merge)
	INIT_CONDITION_TEMPLATE(condition_template_nation_war_kill)
	INIT_CONDITION_TEMPLATE(condition_template_nation_war_result)
	INIT_CONDITION_TEMPLATE(condition_template_nation_war_revive)
	INIT_CONDITION_TEMPLATE(condition_template_talent_upgrade)
	INIT_CONDITION_TEMPLATE(condition_template_limit_num_task)
	INIT_CONDITION_TEMPLATE(condition_template_reputation)
	INIT_CONDITION_TEMPLATE(condition_template_card_suit)
	INIT_CONDITION_TEMPLATE(condition_template_card_suit_group)
	INIT_CONDITION_TEMPLATE(condition_template_enter_instance)
	INIT_CONDITION_TEMPLATE(condition_template_accomplish_achievement)
	INIT_CONDITION_TEMPLATE(condition_template_upgrade_practice)
	INIT_CONDITION_TEMPLATE(condition_template_pet_fight)
	INIT_CONDITION_TEMPLATE(condition_template_equip_stone)
	INIT_CONDITION_TEMPLATE(condition_template_get_talisman)
	INIT_CONDITION_TEMPLATE(condition_template_active_formation)
	INIT_CONDITION_TEMPLATE(condition_template_marry)
	INIT_CONDITION_TEMPLATE(condition_template_get_reward)
	INIT_CONDITION_TEMPLATE(condition_template_flysword_change)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_gift)
	INIT_CONDITION_TEMPLATE(condition_template_retinue_finish_task)
	INIT_CONDITION_TEMPLATE(condition_template_guard_learn_skill)
	INIT_CONDITION_TEMPLATE(condition_template_guard_train)
	INIT_CONDITION_TEMPLATE(condition_template_equip_affix_transfer)
	INIT_CONDITION_TEMPLATE(condition_template_friend_bless)
	INIT_CONDITION_TEMPLATE(condition_template_surface_active_color)
	INIT_CONDITION_TEMPLATE(condition_template_xyxw_invite)
	INIT_CONDITION_TEMPLATE(condition_template_surface_levelup)
	INIT_CONDITION_TEMPLATE(condition_template_active_longyu_quality)
	INIT_CONDITION_TEMPLATE(condition_template_equip_chaijie)
	INIT_CONDITION_TEMPLATE(condition_template_produce_surface)
	INIT_CONDITION_TEMPLATE(condition_template_active_career)
	INIT_CONDITION_TEMPLATE(condition_template_auction_open)
	INIT_CONDITION_TEMPLATE(condition_template_car_race_pvp_rank_times);
	INIT_CONDITION_TEMPLATE(condition_template_enhance_upgrade);
	INIT_CONDITION_TEMPLATE(condition_template_enhance_upgrade2);
	INIT_CONDITION_TEMPLATE(condition_template_guard_star_slot);
	INIT_CONDITION_TEMPLATE(condition_template_stage_child_count);
	INIT_CONDITION_TEMPLATE(condition_template_destiny_child_count);
	INIT_CONDITION_TEMPLATE(condition_template_device_child_count);
	INIT_CONDITION_TEMPLATE(condition_template_skill_child_count);
	INIT_CONDITION_TEMPLATE(condition_template_talent_child_count);
	INIT_CONDITION_TEMPLATE(condition_template_equipment_child_count);
	INIT_CONDITION_TEMPLATE(condition_template_breed_proficiency_level);
	INIT_CONDITION_TEMPLATE(condition_template_breed_cute_pet_gene_express);
	INIT_CONDITION_TEMPLATE(condition_template_breed_cute_pet_hair_colour_id);
	INIT_CONDITION_TEMPLATE(condition_template_breed_cute_pet_nature_quality_id);
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_refresh_slot_times);
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_get_stone);
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_runestone_level);
	INIT_CONDITION_TEMPLATE(condition_template_alchemy_slot_level);
	INIT_CONDITION_TEMPLATE(condition_template_common_fighting_capacity);
	INIT_CONDITION_TEMPLATE(condition_template_dragon_cocoon_refresh_slot);
	INIT_CONDITION_TEMPLATE(condition_template_dragon_cocoon_equiped_level);
	//INIT_CONDITION_TEMPLATE(condition_template_scene_task)
	//INIT_CONDITION_TEMPLATE(condition_template_learn_produce_skill)
	//INIT_CONDITION_TEMPLATE(condition_template_learn_recipe)
	//INIT_CONDITION_TEMPLATE(condition_template_battle_count)
	//INIT_CONDITION_TEMPLATE(condition_template_wulin_level)
	//INIT_CONDITION_TEMPLATE(condition_template_winning_steak)
	//INIT_CONDITION_TEMPLATE(condition_template_apprentice)
	//INIT_CONDITION_TEMPLATE(condition_template_accept_disciple)
	//INIT_CONDITION_TEMPLATE(condition_template_join_family)
	//INIT_CONDITION_TEMPLATE(condition_template_upgrade_soul)
	//INIT_CONDITION_TEMPLATE(condition_template_active_aptitude)

	//INIT_CONDITION_TEMPLATE(condition_template_equip_item)
	//INIT_CONDITION_TEMPLATE(condition_template_get_pet)
	//INIT_CONDITION_TEMPLATE(condition_template_produce)
	//INIT_CONDITION_TEMPLATE(condition_template_finish_instance)
	//INIT_CONDITION_TEMPLATE(condition_template_finish_inst_mode)
	//INIT_CONDITION_TEMPLATE(condition_template_pk)
	//INIT_CONDITION_TEMPLATE(condition_template_dead)
	//INIT_CONDITION_TEMPLATE(condition_template_got_bindmoney)
	//INIT_CONDITION_TEMPLATE(condition_template_complete_task)
	//INIT_CONDITION_TEMPLATE(condition_template_answer_question)
	//INIT_CONDITION_TEMPLATE(condition_template_plant)
	//INIT_CONDITION_TEMPLATE(condition_template_battle_mode)
	//INIT_CONDITION_TEMPLATE(condition_template_battle_perfect)
	//INIT_CONDITION_TEMPLATE(condition_template_battle_mvp)
	//INIT_CONDITION_TEMPLATE(condition_template_roam_count)
	//INIT_CONDITION_TEMPLATE(condition_template_use_service)
	//INIT_CONDITION_TEMPLATE(condition_template_synthetize_stone)
	//INIT_CONDITION_TEMPLATE(condition_template_equip_make_hole)
	//INIT_CONDITION_TEMPLATE(condition_template_equip_embed_stone)
	//INIT_CONDITION_TEMPLATE(condition_template_equip_unembed_stone)
	//INIT_CONDITION_TEMPLATE(condition_template_synthetize_equip)
	//INIT_CONDITION_TEMPLATE(condition_template_revive_at_once)
	//INIT_CONDITION_TEMPLATE(condition_template_produce_equip)
	//INIT_CONDITION_TEMPLATE(condition_template_kill_player)
	//INIT_CONDITION_TEMPLATE(condition_template_tizi)
	//INIT_CONDITION_TEMPLATE(condition_template_erase_tizi)
	//INIT_CONDITION_TEMPLATE(condition_template_parading)
	//INIT_CONDITION_TEMPLATE(condition_template_reset_soul)

#undef INIT_CONDITION_TEMPLATE

	_section_mask = EXPACHIEVESECT_FORCE_INT;

	//成就模板 数据
	LIST_DATA_BEGIN(ID_SPACE_ACHIEVEMENT, ACHIEVEMENT_ESSENCE, ess)
	{
		if (!(_section_mask & ess.achieve_section))
		{
			continue;
		}
		achievement_template *pTemplate = new achievement_template(ess);
		if (!pTemplate->Valid())
		{
			delete pTemplate;
			continue;
		}
		if (_achievement_template_map.find(pTemplate->GetTID()) != _achievement_template_map.end())
		{
			delete pTemplate;
			return false;
		}
		//加入到总表中
		_achievement_template_map[pTemplate->GetTID()] = pTemplate;
		//组织解锁状态条件分组
		for (int i = EXP_ACHIEVE_STATECOND_NONE + 1; i < EXP_ACHIEVE_STATECOND_COUNT; ++i)
		{
			//unsigned int mask = 1 << (i - 1);
			if (pTemplate->GetUnlockStateConditionMask().test(i))
			{
				_achievement_template_unlock_state_condition_group[i][pTemplate->GetTID()] = pTemplate;
				//__PRINTF("加载解锁成就矩阵:条件=%d:成就=%d\n", i, pTemplate->GetTID());
			}
		}
		//组织完成状态条件分组
		for (int i = EXP_ACHIEVE_STATECOND_NONE + 1; i < EXP_ACHIEVE_STATECOND_COUNT; ++i)
		{
			//unsigned int mask = 1 << (i - 1);
			if (pTemplate->GetCompleteStateConditionMask().test(i))
			{
				_achievement_template_complete_state_condition_group[i][pTemplate->GetTID()] = pTemplate;
			}
		}
		//组织完成事件条件分组
		for (int i = EXP_ACHIEVE_EVENTCOND_NONE + 1; i < EXP_ACHIEVE_EVENTCOND_COUNT; ++i)
		{
			//unsigned int mask = 1 << (i - 1);
			if (pTemplate->GetCompleteEventConditionMask().test(i))
			{
				_achievement_template_complete_event_condition_group[i][pTemplate->GetTID()] = pTemplate;
			}
		}
	}
	LIST_DATA_END

	GNET::Conf *conf = GNET::Conf::GetInstance();
	std::string root = conf->find("Template", "Root");
	std::string file_path = root + "server/dailycampaign.txt";

	CSVTable table;
	if (!table.LoadFromFile(XOpenFile(file_path.c_str())))
	{
		return false;
	}
	if (table.GetRowCount() < 1)
	{
		return false;
	}
	for (unsigned int i = 1 ; i < table.GetRowCount(); i ++)
	{
		int newbie_days = 0;
		int achievement_tid = 0;
		int level_limit = 0;
		int random_weight = 0;
		if (!table.GetDataRow(i, 0, &newbie_days, &achievement_tid, &level_limit, &random_weight))
		{
			__PRINTF("每日成就库出错，第%d行\n", i + 1);
			return false;
		}
		ACHIEVEMENT_TEMPLATE_MAP::iterator it = _achievement_template_map.find(achievement_tid);
		if (it == _achievement_template_map.end())
		{
			__PRINTF("每日成就库出错，成就 %d 不存在\n", achievement_tid);
			return false;
		}
		if (it->second->GetLibType() != EXP_ACHIEVEMENT_LIB_ACTIVITY)
		{
			__PRINTF("每日成就库出错，成就 %d 库类型 %d 错误\n", achievement_tid, it->second->GetLibType());
			return false;
		}
		struct daily_extra_config e_config;
		e_config.aid = achievement_tid;
		e_config.level = level_limit;
		e_config.weight = random_weight;
		e_config.p_template = it->second;
		e_config.newbie_days = newbie_days;
		_extra_achievements.push_back(e_config);
	}

	return true;
}

void achievement_manager::RebuildUnlockSet(gplayer_imp *imp, const COMPLETE_ACHIEVEMENT_MAP& complete_map, std::set<int>& unlock_set, EXP_ACHIEVE_STATE_CONDITION target_cond)
{
	if (target_cond <= EXP_ACHIEVE_STATECOND_NONE || target_cond >= EXP_ACHIEVE_STATECOND_COUNT)
	{
		return;
	}
	std::stringstream ss;
	bool accept = false;
	for (ACHIEVEMENT_TEMPLATE_MAP::iterator it = _achievement_template_unlock_state_condition_group[target_cond].begin(); it != _achievement_template_unlock_state_condition_group[target_cond].end(); ++it)
	{
		if (complete_map.find(it->first) != complete_map.end())
		{
			continue;    //已经完成的
		}
		if (unlock_set.find(it->first) != unlock_set.end())
		{
			continue;    //已经解锁
		}
		if (!it->second->CheckCanUnlock(imp))
		{
			continue;    //不满足解锁条件
		}
		if (it->second->GetLibType() == EXP_ACHIEVEMENT_LIB_ACTIVITY)
		{
			continue;
		}
		if (!accept)
		{
			accept = true;
		}
		else
		{
			ss << ":";
		}
		ss << it->first;
		__PRINTF("玩家: "   FMT_I64" 解锁成就: %d 类型：%d\n", imp->Parent()->ID.id, it->first, it->second->GetType());
		unlock_set.insert(it->first);
	}
	if (accept)
	{
		GLog::formatlog("achievemen_accept", "roleid="   FMT_I64":level=%d:achievementid=%s", imp->Parent()->ID.id, imp->GetLevel(), ss.str().c_str());
	}
}
void achievement_manager::GiveExtraAchievement(gplayer_imp *imp, std::set<int>& extra)
{
	int role_create_day = (imp->GetParent()->create_time + gmatrix::GetInstance().GetGmtOff()) / SEC_IN_DAY;
	int cur_day = (gmatrix::GetInstance().GetSysTime() + gmatrix::GetInstance().GetGmtOff()) / SEC_IN_DAY;
	int create_days = cur_day - role_create_day + 1;

	std::map<int, int> valid_choice;
	int m_level = imp->GetLevel();
	int total_weight = 0;

	if (create_days <= 7)
	{
		for (EXTRA_ACHIEVEMENT_VEC::iterator it = _extra_achievements.begin(); it != _extra_achievements.end(); ++it)
		{
			if (it->newbie_days != create_days)
			{
				continue;
			}

			if (it->level <= m_level && it->p_template->CheckCanUnlock(imp))
			{
				if (valid_choice.insert(std::make_pair(it->aid, it->weight)).second)
				{
					total_weight += it->weight;
				}
			}
		}
	}
	else
	{
		for (EXTRA_ACHIEVEMENT_VEC::iterator it = _extra_achievements.begin(); it != _extra_achievements.end(); ++it)
		{
			if (it->newbie_days != 0)
			{
				continue;
			}

			if (it->level <= m_level && it->p_template->CheckCanUnlock(imp))
			{
				if (valid_choice.insert(std::make_pair(it->aid, it->weight)).second)
				{
					total_weight += it->weight;
				}
			}
		}
	}

	//每天，按概率，随机选择三个库成就
	for (int i = 0; i < 3; ++i)
	{
		int ran = abase::RandUniform() * total_weight;
		for (std::map<int, int>::iterator it = valid_choice.begin(); it != valid_choice.end(); ++it)
		{
			if (ran < it->second)
			{
				//添加一个库成就
				GLog::log(LOG_INFO, "玩家: " FMT_I64" 获得了一个库成就: %d\n", imp->Parent()->ID.id, it->first);
				extra.insert(it->first);
				total_weight -= it->second;
				valid_choice.erase(it);
				break;
			}
			else
			{
				ran -= it->second;
			}
		}
	}
}

void achievement_manager::RebuildUnlockSetForAll(gplayer_imp *imp, const COMPLETE_ACHIEVEMENT_MAP& complete_map, std::set<int>& unlock_set)
{
	for (ACHIEVEMENT_TEMPLATE_MAP::iterator it = _achievement_template_map.begin(); it != _achievement_template_map.end(); ++it)
	{
		if (complete_map.find(it->first) != complete_map.end())
		{
			continue;    //已经完成的
		}
		if (!it->second->CheckCanUnlock(imp))
		{
			continue;    //不满足解锁条件
		}
		if (it->second->GetLibType() == EXP_ACHIEVEMENT_LIB_ACTIVITY)
		{
			continue;
		}
		//GLog::formatlog("achievemen_accept", "roleid="   FMT_I64":level=%d:achievementid=%d", imp->Parent()->ID.id, imp->GetLevel(), it->first);
		__PRINTF("玩家: "   FMT_I64" 解锁成就: %d 类型：%d\n", imp->Parent()->ID.id, it->first, it->second->GetType());
		unlock_set.insert(it->first);
	}
}

void achievement_manager::InnerCompleteAchievement(gplayer_imp *imp, int tid, COMPLETE_ACHIEVEMENT_MAP& complete_map, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, std::set<int>& unlock_set)
{
	const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(tid);
	if (NULL == pTemplate)
	{
		return;
	}
	//完成了
	complete_achievement_t temp;
	temp.timestamp = gmatrix::GetInstance().GetSysTime();
	temp.award_tid = pTemplate->GetAwardID();

	complete_map[tid] = temp;
	in_progress_map.erase(tid);
	unlock_set.erase(tid);
	GLog::log(LOG_INFO, " " FMT_I64" 完成成就 %d", imp->Parent()->ID.id, tid);

	//BI_LOG_BEGIN(imp->GetParent()->account.ToStr())
	//header.serverid = gmatrix::GetInstance().GetZoneID();
	//WRITE_BILOG(achievement, &header, bi.platform, bi.account, imp->GetParent()->ID.id, imp->GetLevel(), tid);
	//BI_LOG_END

	if (pTemplate->NeedBroadcast())
	{
		gplayer_imp::SPEAK_PARAM_MAP map;

		raw_wrapper& h1 = map[SYS_SPEAK::ACHIEVEMENT_NAME];
		SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::ACHIEVEMENT_NAME, tid);

		imp->SystemSpeak2(SERVER_CONFIG.id_get_achievement_speak, &map, XID(), pTemplate->GetBroadcastChannel());
	}

	imp->Runner()->achievement_complete(tid, temp.timestamp, temp.award_tid);

	//刷新特殊成就
	imp->GetAchievement().OnCompleteAchievement(imp, tid);
}

void achievement_manager::UpdateComplete(gplayer_imp *imp, COMPLETE_ACHIEVEMENT_MAP& complete_map, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, std::set<int>& unlock_set, EXP_ACHIEVE_STATE_CONDITION target_state_cond, EXP_ACHIEVE_EVENT_CONDITION target_event_cond, const int *input, int input_count)
{
	if (target_state_cond > EXP_ACHIEVE_STATECOND_NONE && target_state_cond < EXP_ACHIEVE_STATECOND_COUNT)
	{
		for (ACHIEVEMENT_TEMPLATE_MAP::iterator it = _achievement_template_complete_state_condition_group[target_state_cond].begin(); it != _achievement_template_complete_state_condition_group[target_state_cond].end(); ++it)
		{
			int tid = it->first;
			const achievement_template *pTemplate = it->second;
			if (complete_map.find(tid) != complete_map.end())
			{
				continue;    //已经完成的
			}
			if (unlock_set.find(tid) == unlock_set.end())
			{
				continue;
			}
			if (!pTemplate->CheckComplete(imp, in_progress_map))
			{
				continue;
			}
			//完成了
			InnerCompleteAchievement(imp, tid, complete_map, in_progress_map, unlock_set);
		}
	}
	if (target_event_cond > EXP_ACHIEVE_EVENTCOND_NONE && target_event_cond < EXP_ACHIEVE_EVENTCOND_COUNT)
	{
		for (ACHIEVEMENT_TEMPLATE_MAP::iterator it = _achievement_template_complete_event_condition_group[target_event_cond].begin(); it != _achievement_template_complete_event_condition_group[target_event_cond].end(); ++it)
		{
			int tid = it->first;
			const achievement_template *pTemplate = it->second;
			if (complete_map.find(tid) != complete_map.end())
			{
				continue;    //已经完成的
			}
			// TODO:这里先UpdateInProgress，后判unlock_set，会发生“未解锁先计数”的情况，有些成就可能“解锁时有进度”或“解锁即完成”
			pTemplate->UpdateInProgress(imp, in_progress_map, target_event_cond, input, input_count); //更新进行中成就数据
			if (unlock_set.find(tid) == unlock_set.end())
			{
				continue;
			}
			if (!pTemplate->CheckComplete(imp, in_progress_map))
			{
				continue;
			}
			//完成了
			InnerCompleteAchievement(imp, tid, complete_map, in_progress_map, unlock_set);
		}
	}
}

void achievement_manager::UpdateCompleteForAll(gplayer_imp *imp, COMPLETE_ACHIEVEMENT_MAP& complete_map, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map, std::set<int>& unlock_set)
{
	std::set<int> temp_unlock_set = unlock_set;
	for (std::set<int>::iterator it = temp_unlock_set.begin(); it != temp_unlock_set.end(); ++it)
	{
		int tid = *it;
		const achievement_template *pTemplate = GetAchievementTemplate(tid);
		ASSERT(pTemplate); //不会是不存在的
		if (complete_map.find(tid) != complete_map.end())
		{
			continue;    //已经完成的
		}
		if (!pTemplate->CheckComplete(imp, in_progress_map))
		{
			continue;
		}
		InnerCompleteAchievement(imp, tid, complete_map, in_progress_map, unlock_set);
	}
}

void player_achieve_man::Load(gplayer_imp *pImp, archive& ar)
{
	if (ar.size() == 0)
	{
		return;
	}
	unsigned char version;
	ar >> version;
	switch (version)
	{
	case 0x02:
	{
		unsigned short complete_count;
		ar >> complete_count;
		size_t i, j;
		for (i = 0; i < complete_count; ++i)
		{
			int tid;
			complete_achievement_t temp;
			ar >> tid;
			ar >> temp.timestamp;
			ar >> temp.award_tid;
			_complete_map[tid] = temp;
		}
		unsigned short in_progress_count;
		ar >> in_progress_count;
		for (i = 0; i < in_progress_count; ++i)
		{
			int tid;
			ar >> tid;
			in_progress_achievement_t& data = _in_progress_map[tid];
			ar >> data.timestamp;
			ar >> data.cond_count;
			for (j = 0; j < data.cond_count; ++j)
			{
				if (j < COMPLETE_EVENT_CONDITIOND_COUNT)
				{
					ar >> data.cond_data[j];
				}
			}
		}
		unsigned short extra_count;
		try
		{
			int last_update = 0;
			ar >> last_update;
			_last_update_timestamp = last_update;
			ar >> extra_count;
			for (i = 0; i < extra_count; i++)
			{
				int extra;
				ar >> extra;
				_extra_achieve.insert(extra);
				_unlock_set.insert(extra);
			}
		}
		catch (...)
		{

		}
	}
	break;

	default:
	{
		//Unsupported achievement version, then ignore data
		GLog::log(LOG_ERR, "Player "   FMT_I64" achievement data will be ignored due to invalid version %d", pImp->GetRoleID(), version);
		return;
	}
	}

	std::set<int> to_del;
	for (COMPLETE_ACHIEVEMENT_MAP::iterator it = _complete_map.begin(); it != _complete_map.end(); ++it)
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it->first);
		if (NULL == pTemplate)
		{
			to_del.insert(it->first);
			continue;
		}
		if (pTemplate->GetPrizeScore() > 0) // grade related
		{
			_achievement_grade += pTemplate->GetPrizeScore();
		}
	}
	for (IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = _in_progress_map.begin(); it != _in_progress_map.end(); ++it)
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it->first);
		if (NULL == pTemplate)
		{
			to_del.insert(it->first);
			continue;
		}
	}
	for (std::set<int>::iterator it = _extra_achieve.begin(); it != _extra_achieve.end(); ++it)
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(*it);
		if (NULL == pTemplate)
		{
			to_del.insert(*it);
			continue;
		}
	}
	for (std::set<int>::iterator it = to_del.begin(); it != to_del.end(); ++it)
	{
		_complete_map.erase(*it);
		_in_progress_map.erase(*it);
		_extra_achieve.erase(*it);
		_unlock_set.erase(*it);
	}
}

void player_achieve_man::Save(archive& ar)
{
	ar << (unsigned char)GS_ACHIEVEMENT_VERSION;
	//已完成成就保存
	unsigned short complete_count = _complete_map.size();
	ar << complete_count;
	for (COMPLETE_ACHIEVEMENT_MAP::iterator it = _complete_map.begin(); it != _complete_map.end(); ++it)
	{
		ar << it->first;
		ar << it->second.timestamp;
		ar << it->second.award_tid;
	}
	//进行中的成就保存
	unsigned short in_progress_count = _in_progress_map.size();
	ar << in_progress_count;
	for (IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = _in_progress_map.begin(); it != _in_progress_map.end(); ++it)
	{
		ar << it->first;
		ar << (int)it->second.timestamp;
		ar << (unsigned char)it->second.cond_count;
		for (size_t i = 0; i < it->second.cond_count; ++i)
		{
			ar << it->second.cond_data[i];
		}
	}
	//随机库成就
	ar << (int)_last_update_timestamp;
	unsigned short extra_achieve_count = _extra_achieve.size();
	ar << extra_achieve_count;
	std::stringstream eachieves;
	for (std::set<int>::iterator it = _extra_achieve.begin(); it != _extra_achieve.end(); ++it)
	{
		eachieves << (int)(*it);
		ar << (int)(*it);
	}
	//GLog::log(LOG_INFO, "player_achieve_man::Save extra_achieve %s", eachieves.str().c_str() );

}

void player_achieve_man::SaveForClient(archive& ar)
{
	raw_wrapper tmp_rw;

	unsigned short complete_count = 0;
	for (COMPLETE_ACHIEVEMENT_MAP::iterator it = _complete_map.begin(); it != _complete_map.end(); ++it)
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it->first);
		if (NULL == pTemplate)
		{
			continue;
		}
		++complete_count;
		tmp_rw << it->first;
		tmp_rw << it->second.timestamp;
		tmp_rw << it->second.award_tid;
	}
	ar << complete_count;
	ar.push_back(tmp_rw.data(), tmp_rw.size());

	tmp_rw.clear();
	unsigned short in_progress_count = 0;
	for (IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = _in_progress_map.begin(); it != _in_progress_map.end(); ++it)
	{
		//const achievement_template* pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it->first);
		//进行中的非永久成就不必发给客户端
		//if (NULL == pTemplate || pTemplate->GetType() != EXPACHIEVETYPE_PERMANENT) continue;
		++in_progress_count;
		tmp_rw << it->first;
	}
	ar << in_progress_count;
	ar.push_back(tmp_rw.data(), tmp_rw.size());

	tmp_rw.clear();
	unsigned short extra_count = _extra_achieve.size();
	for (std::set<int>::iterator it = _extra_achieve.begin(); it != _extra_achieve.end(); ++it)
	{
		GLog::log(LOG_INFO, "player_achieve_man::SaveForClient extra_achieve %d", (*it));
		tmp_rw << *it;
	}
	ar << extra_count;
	ar.push_back(tmp_rw.data(), tmp_rw.size());
}


void player_achieve_man::DetailSaveForClient(archive& ar, bool include_locked, bool include_daily, int *tids, unsigned short tid_count) const
{
	bool is_save_all = true;
	std::set<int> tid_set;
	if (NULL != tids && tid_count > 0)
	{
		is_save_all = false;
		tid_set.insert(tids, tids + tid_count);
	}

	raw_wrapper tmp_rw;
	unsigned short count = 0;
	COMPLETE_ACHIEVEMENT_MAP::const_iterator it_complete;
	for (it_complete = _complete_map.begin(); it_complete != _complete_map.end(); ++it_complete)
	{
		if (!is_save_all && tid_set.empty())
		{
			break;
		}
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it_complete->first);
		if (!pTemplate)
		{
			continue;    //不存在的成就
		}
		if (!is_save_all && tid_set.find(it_complete->first) == tid_set.end())
		{
			continue;
		}
		if (!include_daily && EXPACHIEVETYPE_DAILY == pTemplate->GetType())
		{
			continue;    //排除每日成就
		}
		// Refer to S2C::CMD::complete_achievement_data_t
		tmp_rw << (int)it_complete->first;
		tmp_rw << (int)it_complete->second.timestamp;
		tmp_rw << (int)it_complete->second.award_tid;
		++count;
		if (tid_set.size() > 0)
		{
			tid_set.erase(it_complete->first);
		}
	}
	ar << count;
	if (count > 0)
	{
		ar.push_back(tmp_rw.data(), tmp_rw.size());
	}

	tmp_rw.clear();
	count = 0;
	IN_PROGRESS_ACHIEVEMENT_MAP::const_iterator it_in_progress;
	for (it_in_progress = _in_progress_map.begin(); it_in_progress != _in_progress_map.end(); ++it_in_progress)
	{
		if (!is_save_all && tid_set.empty())
		{
			break;
		}
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it_in_progress->first);
		if (!pTemplate)
		{
			continue;    //不存在的成就
		}
		if (tid_set.size() > 0 && tid_set.find(it_in_progress->first) == tid_set.end())
		{
			continue;
		}
		if (!include_locked && !IsUnlockedAchievement(it_in_progress->first))
		{
			continue;    //排除未解锁成就
		}
		if (!include_daily && EXPACHIEVETYPE_DAILY == pTemplate->GetType())
		{
			continue;    //排除每日成就
		}
		// Refer to S2C::CMD::active_achievement_data_t
		tmp_rw << (int)it_in_progress->first;
		tmp_rw << (unsigned char)it_in_progress->second.cond_count;
		for (size_t i = 0; i < it_in_progress->second.cond_count; ++i)
		{
			tmp_rw << (int)it_in_progress->second.cond_data[i];
		}
		++count;
		if (tid_set.size() > 0)
		{
			tid_set.erase(it_in_progress->first);
		}
	}
	ar << count;
	if (count > 0)
	{
		ar.push_back(tmp_rw.data(), tmp_rw.size());
	}

	if (!is_save_all)
	{
		ar << (unsigned short)tid_set.size();
		for (std::set<int>::iterator it_other = tid_set.begin(); it_other != tid_set.end(); ++it_other)
		{
			ar << *it_other;
		}
	}
}

void player_achieve_man::QueryInProgressAchievementData(gplayer_imp *imp, int *achievement_tid_list, unsigned short count) const
{
	unsigned short tmp_count = 0;
	raw_wrapper rw;
	for (size_t i = 0; i < count; ++i)
	{
		int tid = achievement_tid_list[i];
		IN_PROGRESS_ACHIEVEMENT_MAP::const_iterator it = _in_progress_map.find(tid);
		if (it != _in_progress_map.end())
		{
			++tmp_count;
			rw << it->first;
			//rw << (int)it->second.timestamp;
			rw << (unsigned char)it->second.cond_count;
			for (size_t j = 0; j < it->second.cond_count; ++j)
			{
				rw << it->second.cond_data[j];
			}
		}
	}
	imp->Runner()->active_achievement_data(tmp_count, rw.data(), rw.size());
}

bool player_achieve_man::IsUnlockedAchievement(int achievement_id) const
{
	return (_unlock_set.find(achievement_id) != _unlock_set.end());
}

bool player_achieve_man::IsCompletedAchievement(int achievement_id) const
{
	return (_complete_map.find(achievement_id) != _complete_map.end());
}

void player_achieve_man::AwardNationWarAchievements(gplayer_imp *imp)
{
	std::set<int> to_del;
	COMPLETE_ACHIEVEMENT_MAP::iterator it_complete;
	for (it_complete = _complete_map.begin(); it_complete != _complete_map.end(); ++it_complete)
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it_complete->first);
		if (NULL == pTemplate)
		{
			continue;
		}

		if (pTemplate->GetType() == EXPACHIEVETYPE_NATION_WAR)
		{
			if (it_complete->second.award_tid != 0)
			{
				imp->DeliverGeneralReward({kFuncCodeAchievement, pTemplate->GetTID()}, GRANT_REWARD_TYPE_ACHIEVEMENT, it_complete->second.award_tid, NULL, 0);
				it_complete->second.award_tid = 0;
				to_del.insert(it_complete->first);
			}
		}
	}
	IN_PROGRESS_ACHIEVEMENT_MAP::iterator it_in_progress;
	for (it_in_progress = _in_progress_map.begin(); it_in_progress != _in_progress_map.end(); ++it_in_progress)
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it_in_progress->first);
		if (NULL == pTemplate)
		{
			continue;
		}

		if (pTemplate->GetType() == EXPACHIEVETYPE_NATION_WAR)
		{
			to_del.insert(it_in_progress->first);
		}
	}
	for (std::set<int>::iterator it = to_del.begin(); it != to_del.end(); ++it)
	{
		_complete_map.erase(*it);
		_in_progress_map.erase(*it);
	}
}
void player_achieve_man::RemoveTemporaryAchievements(gplayer_imp *imp)
{
	time_t cur_timestamp = gmatrix::GetInstance().GetSysTime();
	time_t cur_local_timestamp = cur_timestamp + gmatrix::GetInstance().GetGmtOff();
	time_t last_update_local_timestamp = _last_update_timestamp + gmatrix::GetInstance().GetGmtOff();

	//GLog::log(LOG_INFO, "玩家: " FMT_I64" player_achieve_man::RemoveTemporaryAchievements._last_update_timestamp=%d\n",
	//imp->Parent()->ID.id, (int)_last_update_timestamp);

	// 保证每天只清一次
	if (_last_update_timestamp > 0 && last_update_local_timestamp / SEC_IN_DAY >= cur_local_timestamp / SEC_IN_DAY)
	{
		return;
	}

	COMPLETE_ACHIEVEMENT_MAP::iterator it_complete;
	for (it_complete = _complete_map.begin(); it_complete != _complete_map.end(); )
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it_complete->first);
		if (NULL == pTemplate)
		{
			++it_complete;
			continue;
		}
		if (pTemplate->CheckExpiration(imp, it_complete->second.timestamp))
		{
			if (pTemplate->GetType() == EXPACHIEVETYPE_NATION_WAR || pTemplate->GetLibType() == EXP_ACHIEVEMENT_LIB_ACTIVITY)
			{
				if (it_complete->second.award_tid != 0)
				{
					imp->DeliverGeneralReward({kFuncCodeAchievement, pTemplate->GetTID()}, GRANT_REWARD_TYPE_ACHIEVEMENT, it_complete->second.award_tid, NULL, 0);
				}
			}
			COMPLETE_ACHIEVEMENT_MAP::iterator it_temp = it_complete;
			++it_complete;
			_complete_map.erase(it_temp);
		}
		else
		{
			++it_complete;
		}
	}

	IN_PROGRESS_ACHIEVEMENT_MAP::iterator it_in_progress;
	for (it_in_progress = _in_progress_map.begin(); it_in_progress != _in_progress_map.end(); )
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(it_in_progress->first);
		if (NULL == pTemplate)
		{
			++it_in_progress;
			continue;
		}
		if (pTemplate->CheckExpiration(imp, it_in_progress->second.timestamp))
		{
			IN_PROGRESS_ACHIEVEMENT_MAP::iterator it_temp = it_in_progress;
			++it_in_progress;
			_in_progress_map.erase(it_temp);
		}
		else
		{
			++it_in_progress;
		}
	}

	for (std::set<int>::iterator it = _extra_achieve.begin(); it != _extra_achieve.end(); ++it)
	{
		GLog::log(LOG_INFO, "玩家: " FMT_I64" 过期了一个库成就: %d\n", imp->Parent()->ID.id, *it);
		_unlock_set.erase(*it);
	}
	_extra_achieve.clear();
	achievement_manager::GetInstance().GiveExtraAchievement(imp, _extra_achieve);
	for (std::set<int>::iterator it = _extra_achieve.begin(); it != _extra_achieve.end(); ++it)
	{
		GLog::log(LOG_INFO, "玩家: " FMT_I64" 解锁了一个库成就: %d\n", imp->Parent()->ID.id, *it);
		_unlock_set.insert(*it);
	}

	_last_update_timestamp = cur_timestamp;
}

void player_achieve_man::DailyUpdate(gplayer_imp *imp)
{
	GLog::log(LOG_INFO, "玩家: " FMT_I64" player_achieve_man::DailyUpdate\n", imp->Parent()->ID.id);

	RemoveTemporaryAchievements(imp);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_LOGIN);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_LOGIN, EXP_ACHIEVE_EVENTCOND_NONE);

	raw_wrapper rw;
	SaveForClient(rw);
	imp->Runner()->simple_achievement_data(rw.data(), rw.size());
}

void player_achieve_man::OnLogin(gplayer_imp *imp)
{
	//GLog::log(LOG_INFO, "玩家: " FMT_I64" player_achieve_man::OnLogin\n",imp->Parent()->ID.id);
	//清除临时成就
	RemoveTemporaryAchievements(imp);
	/*
	if (_extra_achieve.empty())
	{
		achievement_manager::GetInstance().GiveExtraAchievement(imp, _extra_achieve);
		for (std::set<int>::iterator it = _extra_achieve.begin(); it != _extra_achieve.end(); ++it)
		{
			GLog::log(LOG_INFO, "玩家: " FMT_I64" 解锁了一个库成就: %d\n",imp->Parent()->ID.id, *it);
			_unlock_set.insert(*it);
		}
	}
	*/

	//首先发送保存的成就数据,原封不动的发给客户端
	raw_wrapper rw;
	SaveForClient(rw);
	imp->Runner()->simple_achievement_data(rw.data(), rw.size());

	achievement_manager::GetInstance().RebuildUnlockSetForAll(imp, _complete_map, _unlock_set);
	achievement_manager::GetInstance().UpdateCompleteForAll(imp, _complete_map, _in_progress_map, _unlock_set);

	//TODO
	//这里需要发放满足状态条件的新成就(这种成就只有状态条件)
	//只有状态条件的成就需要标记优化
	//对于前提为完成成就的那种需要特殊处理

	//__PRINTF("已完成的成就: ");
	//for(COMPLETE_ACHIEVEMENT_MAP::iterator it = _complete_map.begin();it != _complete_map.end();++it)
	//{
	//	__PRINTF("%d ",it->first);
	//}
	//__PRINTF("\n");
	//__PRINTF("进行中的成就: ");
	//for(IN_PROGRESS_ACHIEVEMENT_MAP::iterator it = _in_progress_map.begin();it != _in_progress_map.end();++it)
	//{
	//	__PRINTF("%d ",it->first);
	//}
	//__PRINTF("\n");
	//__PRINTF("解锁状态下的成就: ");
	//for(std::set<int>::iterator it = _unlock_set.begin();it != _unlock_set.end();++it)
	//{
	//	__PRINTF("%d ",*it);
	//}
	//__PRINTF("\n");
}

void player_achieve_man::AddAchievement(gplayer_imp *imp, int tid)
{
	const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(tid);
	if (!pTemplate)
	{
		return;    //不存在的成就
	}
	if (_complete_map.find(tid) != _complete_map.end())
	{
		return;    //已经完成了
	}
	complete_achievement_t temp;
	temp.timestamp = gmatrix::GetInstance().GetSysTime();
	temp.award_tid = pTemplate->GetAwardID();
	_complete_map[tid] = temp;
	imp->Runner()->achievement_complete(tid, temp.timestamp, temp.award_tid);
}

void player_achieve_man::SubAchievement(gplayer_imp *imp, int tid)
{
	if (tid > 0)
	{
		const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(tid);
		if (!pTemplate)
		{
			return;    //不存在的成就
		}
		if (_complete_map.find(tid) == _complete_map.end())
		{
			return;    //不存在
		}
		_complete_map.erase(tid);
	}
	else
	{
		_complete_map.clear();
	}
}

bool player_achieve_man::GetAchievementAward(gplayer_imp *imp, int tid)
{
	std::set<int> get_tid;
	if (tid == 0)
	{
		for (COMPLETE_ACHIEVEMENT_MAP::iterator it = _complete_map.begin(); it != _complete_map.end(); ++it)
		{
			get_tid.insert(it->first);
		}
	}
	else
	{
		get_tid.insert(tid);
	}
	for (std::set<int>::iterator g_it = get_tid.begin(); g_it != get_tid.end(); ++g_it)
	{
		tid = *g_it;
		COMPLETE_ACHIEVEMENT_MAP::iterator it = _complete_map.find(tid);
		if (it != _complete_map.end())
		{
			const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(tid);
			if (NULL == pTemplate)
			{
				continue;
			}
			if (pTemplate->GetType() == EXPACHIEVETYPE_NATION_WAR)
			{
				continue;
			}

			// pve中心服不能领该类型的成就奖励
			if (pTemplate->GetType() == EXPACHIEVETYPE_ACTIVITY_LIMIT && GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
			{
				continue;
			}

			if (it->second.award_tid != 0)
			{
				imp->DeliverGeneralReward({kFuncCodeAchievement, pTemplate->GetTID()}, GRANT_REWARD_TYPE_ACHIEVEMENT, it->second.award_tid, NULL, 0);
				it->second.award_tid = 0;
				imp->Runner()->achievement_complete(tid, it->second.timestamp, 0);
			}
		}
	}
	return true;
}


void player_achieve_man::OnLevelUp(gplayer_imp *imp, unsigned char level)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_LEVEL);
		achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
	}

	void player_achieve_man::OnMoneyChanged(gplayer_imp *imp, int code, MONEY_TYPE money_type, money_t inc)
	{
		achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_MONEY);
		achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_MONEY, EXP_ACHIEVE_EVENTCOND_NONE);
	}

	void player_achieve_man::OnPKValueChanged(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_PK_VALUE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_PK_VALUE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnTaskFinished(gplayer_imp *imp, int task_id)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_NORMAL_TASK);
	//achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NORMAL_TASK,EXP_ACHIEVE_EVENTCOND_NONE);

	//{ 任务id， 库id}
	int input[] = { task_id, 0};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NORMAL_TASK, EXP_ACHIEVE_EVENTCOND_FINISH_TASK, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnLearnSkill(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_LEARN_SKILL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_LEARN_SKILL, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnSevenCrimeSwordLevelChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SWORD_LEVEL_AVERAGE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SWORD_LEVEL_AVERAGE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnSevenCrimeSacrificeQualityChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SACRIFICE_QUALITY_AVERAGE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_SEVEN_CRIME_SACRIFICE_QUALITY_AVERAGE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnMakeFriend(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_MAKE_FRIEND);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_MAKE_FRIEND, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnJoinMafia(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_JOIN_MAFIA);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_JOIN_MAFIA, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnCompleteAchievement(gplayer_imp *imp, int achievement_id)
{
	const achievement_template *pTemplate = achievement_manager::GetInstance().GetAchievementTemplate(achievement_id);
	if (pTemplate)
	{
		// 增加成就评分
		if (pTemplate->GetPrizeScore() > 0)
		{
			_achievement_grade += pTemplate->GetPrizeScore();
		}

		// 发放称号
		if (pTemplate->GetTitleID() > 0)
		{
			imp->GetTitle().AddTitle(imp, pTemplate->GetTitleID());
			__PRINTF("玩家："   FMT_I64" 获取一个成就对应的称号 achievement_id:%d title_id:%d.\n", imp->Parent()->ID.id, achievement_id, pTemplate->GetTitleID());
		}

		// 发放任务
		if (pTemplate->GetTaskID() > 0)
		{
			imp->GetTaskGuard()->GetTaskIf()->OnTaskCheckDeliver(pTemplate->GetTaskID(), 0);
			__PRINTF("玩家："   FMT_I64" 获取一个成就对应的任务:achievement_id=%d:task_id=%d.\n", imp->Parent()->ID.id, achievement_id, pTemplate->GetTaskID());
		}
		imp->OnCompleteAchievement(achievement_id);
	}

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACCOMP_ACHIEVE);
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACHIEVEMENT_GRADE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACCOMP_ACHIEVE, EXP_ACHIEVE_EVENTCOND_NONE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACHIEVEMENT_GRADE, EXP_ACHIEVE_EVENTCOND_NONE);

	imp->GetAutoReward().OnAchievementComplete(imp, achievement_id);

	if (TriggerRewardManager::GetInstance().IsTriggerAchievement(achievement_id))
	{
		imp->GetAutoReward().OnTriggerGift(imp);
	}
}

void player_achieve_man::OnUpgradePractive(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_UPGRADE_RRACTICE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_UPGRADE_RRACTICE, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_PRACTICE_LEVELUP);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_PRACTICE_LEVELUP, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnChangePetFight(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_PET_FIGHTING);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_PET_FIGHTING, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnEuipStone(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_STONE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_STONE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnEquipmentEquip(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_QUALITY_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_QUALITY_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_START_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_START_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);

	OnEquipAffixQualityChange(imp);
}


void player_achieve_man::OnGetItem(gplayer_imp *imp, int item_tid, int item_type, int item_quality, const FuncInfo& fi, int item_count)
{
	if (fi.txn_id > 0 && !fi.txn_success)
	{
		return;
	}
	if (fi.code == kFuncCodeMail)
	{
		unsigned char category = fi.arg2;
		if (CMP_CATEGORY(category, GNET::MAIL_CATEGORY_AUCTION_TIMEOUT)
		        || CMP_CATEGORY(category, GNET::MAIL_CATEGORY_AUCTION_CANCEL)
		        || CMP_CATEGORY(category, GNET::MAIL_CATEGORY_AUCTION_VERIFY_FAIL_SELL))
		{
			return;
		}
	}
	else if (fi.code == kFuncCodeStoreAuctionClose)
	{
		return;
	}
	int input[] = {item_tid, item_type, item_quality, fi.code, item_count};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GET_ITEM, input, sizeof(input) / sizeof(int));

	if (fi.code == kFuncCodeBookworm2 || fi.code == kFuncCodeBookworm)
	{
		EXP_ACHIEVE_EVENT_CONDITION event_cond = EXP_ACHIEVE_EVENTCOND_NONE;
		if (fi.arg3 == 1)
		{
			event_cond = EXP_ACHIEVE_EVENTCOND_SMELT_HERES_SKILL;
		}
		else if (fi.arg3 == 2)
		{
			event_cond = EXP_ACHIEVE_EVENTCOND_SMELT_PET_SKILL;
		}
		else if (fi.arg3 == 3)
		{
			event_cond = EXP_ACHIEVE_EVENTCOND_SMELT_PET_CHIP;
		}

		if (event_cond > EXP_ACHIEVE_EVENTCOND_NONE)
		{
			int input2[] = { item_quality };
			achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, event_cond, input2, sizeof(input2) / sizeof(int));
		}
	}
}

void player_achieve_man::OnCanRedoNoRecordTaskFinished(gplayer_imp *imp, unsigned int task_id, unsigned int task_set_id)
{
	int input[] = { (int)task_id, (int)task_set_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_FINISH_CANREDO_TASK, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnKillMonster(gplayer_imp *imp, int monster_tid)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_KILL_MONSTER, &monster_tid, 1);
}

void player_achieve_man::OnUseItem(gplayer_imp *imp, int item_tid, int item_count)
{
	int input[] = {item_tid, item_count};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_USE_ITEM, input, 2);
}
void player_achieve_man::OnSpendMoney(gplayer_imp *imp, MONEY_TYPE money_type, int money_count)
{
	int bind_money = (money_type == MT_BIND) ? money_count : 0;
	int trade_money = (money_type == MT_TRADE) ? money_count : 0;
	int input[] = {bind_money, trade_money};

	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_SPEND_MONEY, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnRetinueGift(gplayer_imp *imp, int retinue_id, int gift_id)
{
	int input[] = {retinue_id, gift_id};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_RETINUE_GIFT, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnRetinueFinishTask(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_RETINUE_FINISH_TASK);
}

void player_achieve_man::OnActiveFormation(gplayer_imp *imp, int formation_tid)
{
	int input[] = {formation_tid, 1};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ACTIVE_FORMATION, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnGetReward(gplayer_imp *imp, int reward_tid)
{
	int input[] = {reward_tid, 1};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GET_GRANT_REWARD, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnEquipmentForge(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_FORCE);
}

void player_achieve_man::OnEquipmentLianxing(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_INC_STAR);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_START_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_START_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnEquipmentInherit(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_INHERIT);
}

void player_achieve_man::OnEquipmentEmbed(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_ATTACH);
}

void player_achieve_man::OnEquipmentRefine(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_REFINE);
}

void player_achieve_man::OnItemMerge(gplayer_imp *imp, int new_tid, int new_type, int new_quality)
{
	int input[] = { new_tid, new_type, new_quality};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ITEM_MERGE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnNationWarKill(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_NATION_WAR_KILL);
}

void player_achieve_man::OnNationWarFinish(gplayer_imp *imp, int win_lose)
{
	int input[] = {win_lose};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_NATION_WAR_WIN, input, sizeof(input) / sizeof(int));
}
void player_achieve_man::OnNationWarRevive(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_NATION_WAR_REVIVE);
}

/*
void player_achieve_man::OnNationWarBegin(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSetForAll(imp,_complete_map,_unlock_set);
}
*/

void player_achieve_man::OnTalentUpgrade(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_TALENT_UPGRADE);
}

void player_achieve_man::OnEnterInstance(gplayer_imp *imp, int instance_tid)
{
	int input[] = {instance_tid};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ENTER_INSTANCE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnGetRetinue(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_QUALITY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_QUALITY, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnRetinueQualityChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_QUALITY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_QUALITY, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnRetinueLevelChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_LEVEL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnRetinueAmityChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_AMITY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_AMITY, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnRetinuePrivateItem(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_PRIVATE_ITEM);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_PRIVATE_ITEM, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnRetinueFashionColor(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_FASHION_COLOR);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_FASHION_COLOR, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnRetinueComposeGroup(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_COMPOSE_GROUP);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RETINUE_COMPOSE_GROUP, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnPropUpdate(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_FIGHTING);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_FIGHTING, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnReputationInc(gplayer_imp *imp, int reputation_id, int value)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_REPUTATION);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_REPUTATION, EXP_ACHIEVE_EVENTCOND_NONE);
	int input[] = { reputation_id, value };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_REPUTATION_REACH_TARGET, input, sizeof(input) / sizeof(int));
}


void player_achieve_man::OnGetTalisman(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_TALISMAN);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_TALISMAN, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnActiveCardSuit(gplayer_imp *imp, int suit_id)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO_GROUP);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_COLLECT_CARD_COMBO_GROUP, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnChangeProf(gplayer_imp *imp, unsigned char new_prof)
{
	/*
		achievement_manager::GetInstance().RebuildUnlockSet(imp,_complete_map,_unlock_set,EXP_ACHIEVE_STATECOND_PROF);
		achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_PROF,EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnSceneTaskFinished(gplayer_imp *imp, unsigned short scene_tag)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp,_complete_map,_unlock_set,EXP_ACHIEVE_STATECOND_SCENE_TASK);
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_SCENE_TASK,EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnLimitNumTaskFinished(gplayer_imp *imp, int task_id)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_LIMITNUM_TASK);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_LIMITNUM_TASK, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnUnlockFashion(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_UNLOCK_FASHION);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_UNLOCK_FASHION, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnUnlockFashionColor(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_FASHION_COLOR);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_FASHION_COLOR, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGetGuard(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGuardBreakPhase(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_PHASE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_PHASE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGuardPutInSlot(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_SLOT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_SLOT, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGuardStarSlotLevelUp(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_STAR_SLOT_LEVEL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_GUARD_STAR_SLOT_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGuardLearnSkill(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GUARD_LEARN_SKILL);
}

void player_achieve_man::OnGuardTrain(gplayer_imp *imp, int guard_id, int train_count)
{
	int input[] = {guard_id, train_count};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GUARD_TRAIN, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnEquipmentAffixesTransfer(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_AFFIX_TRANSFER);
}

void player_achieve_man::OnEquipDragonborn(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_DRAGONBORN_APTITUDE, EXP_ACHIEVE_EVENTCOND_NONE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_DRAGONBORN_EVO_BREAK_COST, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnFriendBless(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_FRIEND_BLESS);
}

void player_achieve_man::OnXYXWInvite(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_XYXW);
}

void player_achieve_man::OnSurfaceLevelUp(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UPGRADE_SURFACE);
}

void player_achieve_man::OnActiveLongYuByQuality(gplayer_imp *imp, int quality)
{
	int input[] = {quality};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGYU, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnEquipmentChaiJie(gplayer_imp *imp, int quality, int level)
{
	int input[] = { quality, level };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_CHAIJIE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnProduceSurfaceById(gplayer_imp *imp, int tid)
{
	int input[] = { tid };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_PRODUCE_SURFACE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnActiveCarrer(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ACTIVE_CAREER);
}

void player_achieve_man::OnAuctionOpen(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_AUCTION_OPEN);
}

void player_achieve_man::OnBreedCutePetGeneExpress(gplayer_imp *imp, int type, int gene_express)
{
	int input[] = { type, gene_express };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_GENE_EXPRESS, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnBreedCutePetHairColourId(gplayer_imp *imp, int hair_colour_id)
{
	int input[] = { hair_colour_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_HAIR_COLOUR_ID, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnBreedCutePetNatureQualityId(gplayer_imp *imp, int type, int gene_express, int nature_quality_id)
{
	int input[] = { type, gene_express, nature_quality_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_BREED_CUTE_PET_NATURE_QUALITY_ID, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnCarRacePVPFinished(gplayer_imp *pimp, int rank)
{
	achievement_manager::GetInstance()
	.UpdateComplete(pimp, _complete_map, _in_progress_map, _unlock_set,
	                EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_CAR_RACE_PVP_RANK_TIMES,
	                &rank, 1);
}

void player_achieve_man::OnGetTitle(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_HAS_TITLE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_HAS_TITLE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGetPhoto(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_PHOTO);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_PHOTO, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnTopListRankChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_TOP_RANK);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_TOP_RANK, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnEquipAffixQualityChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_AFFIX_QUALITY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_AFFIX_QUALITY, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGetLongyu(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_LONGYU);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_LONGYU, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnUpgradeSurface(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_UPGRADE_SURFACE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_UPGRADE_SURFACE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnSurfaceActiveFashion(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_SURFACE_FASHION);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_SURFACE_FASHION, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnSurfaceActiveColor(gplayer_imp *imp, bool by_system)
{
	EXP_ACHIEVE_EVENT_CONDITION target_event_cond = by_system ? EXP_ACHIEVE_EVENTCOND_NONE : EXP_ACHIEVE_EVENTCOND_SURFACE_COLOR;
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_SURFACE_COLOR);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_SURFACE_COLOR, target_event_cond);
}

void player_achieve_man::OnKotodamaLearn(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_KOTODAMA_LEARN);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_KOTODAMA_LEARN, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnKotodamaLevelUp(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_KOTODAMA_LEVELUP);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_KOTODAMA_LEVELUP, EXP_ACHIEVE_EVENTCOND_NONE);
}


void player_achieve_man::OnActiveLongYu(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACTIVE_LONGYU);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACTIVE_LONGYU, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGetChild(gplayer_imp *imp)
{
	OnChildUpgrade(imp);
	OnChildDestinyChange(imp);
	OnChildDeviceChange(imp);
	OnChildSkillChange(imp);
	OnChildTalentChange(imp);
	OnChildEquipChange(imp);
}
void player_achieve_man::OnChildUpgrade(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_CHILD_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_CHILD_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}
void player_achieve_man::OnChildDestinyChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_DESTINY_CHILD_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_DESTINY_CHILD_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}
void player_achieve_man::OnChildDeviceChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_DEVICE_CHILD_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_DEVICE_CHILD_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}
void player_achieve_man::OnChildSkillChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_SKILL_CHILD_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_SKILL_CHILD_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}
void player_achieve_man::OnChildTalentChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_TALENT_CHILD_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_TALENT_CHILD_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}
void player_achieve_man::OnChildEquipChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_CHILD_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_EQUIP_CHILD_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnBreedProficiencyLevelUp(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_BREED_PROFICIENCY_LEVEL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_BREED_PROFICIENCY_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnLongwenLevelup(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_LEVEL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
}

/*void player_achieve_man::OnAlchemyRuneLevelup(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RUNE_STONE_GET);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RUNE_STONE_GET, EXP_ACHIEVE_EVENTCOND_NONE);
}*/

void player_achieve_man::OnAlchemyCircleLevelup(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RUNE_STONE_ARRAY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RUNE_STONE_ARRAY, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnHolyGhostAddAmity(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_AMITY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_AMITY, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnHolyGhostLevelUp(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_LEVEL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnHolyGhostUnlock(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_UNLOCK_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_UNLOCK_COUNT, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnHolyGhostAddWorldTreeHeight(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_TREE_HEIGHT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_HOLY_GHOST_TREE_HEIGHT, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnFlySwordChange(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_INVOKE_WING_SOUL, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnEnhanceUpgrade(gplayer_imp *imp, bool success)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ENHANCE_COUNT);
	if (success)
	{
		achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ENHANCE_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
	}

	int input[] = { success };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ENHANCE_CONTINUOUS_RESULT, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnAlchemyRunestoneGet(gplayer_imp *imp, int level, int count)
{
	int input[] = {level, count};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ALCHEMY_GET_STONE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnAlchemyRunestoneLevelUp(gplayer_imp *imp, int level )
{
	OnAlchemyRunestoneGet(imp, level, 1);
	OnAlchemyRuneStonePutIn(imp);
	int input[] = {level};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ALCHEMY_RUNESTONE_LEVEL, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnAlchemyRuneStonePutIn(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_ANY_SUIT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_ANY_SUIT, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT_LEVEL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_ACTIVE_SUIT_LEVEL, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnAlchemySlotLevelUp(gplayer_imp *imp, int level)
{
	int input[] = {level};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ALCHEMY_SLOT_LEVEL, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnAlchemySlotRefresh(gplayer_imp *imp, int level)
{
	int input[] = {level};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ALCHEMY_REFRESH_SLOT_TIMES);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_REFRESH_SLOT);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ALCHEMY_REFRESH_SLOT, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_DRAGON_COCOON_REFRESH_SLOT, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnCommonFightingCapacityUpdate(gplayer_imp *imp, int type, int fighting_capacity)
{
	int input[] = { type, fighting_capacity };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_SYSTEM_SCORE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnAlchemyRuneStoneEquiped(gplayer_imp *imp, int level)
{
	int input[] = { level };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_DRAGON_COCOON_EQUIPED_LEVEL, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnSurfaceEffectUpgrade(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_VEHICLE_LEVEL);
}

void player_achieve_man::OnSurfaceEffectBreak(gplayer_imp *imp, int stage)
{
	int input[] = { stage };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_VEHICLE_BREAK, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnSurfaceEffectUnlock(gplayer_imp *imp, int count)
{
	int input[] = { count };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_VEHICLE_EFFECT, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnReleaseFish(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_RELEASE_FISH);
}

void player_achieve_man::OnCatchFish(gplayer_imp *imp, int weight)
{
	int input[] = { weight };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_FISH, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnUnlockAlchemyCircleRune(gplayer_imp *imp, int rune_id)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_RUNE_STONE_GET);

	int input[] = { rune_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_RUNE, input, sizeof(input) / sizeof(int));
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_RUNE_STONE_GET, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnUpstageAlchemyCircleRune(gplayer_imp *imp, int rune_id, int stage)
{
	int input[] = { rune_id, stage };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UPGRADE_RUNE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnUnlockLonghun(gplayer_imp *imp, int longhun_id)
{
	int input[] = { longhun_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGHUI_NEW, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnUnlockLongyu(gplayer_imp *imp, int longyu_id)
{
	int input[] = { longyu_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ACTIVE_LONGYU_NEW, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnUnlockEquipSuit(gplayer_imp *imp, int equip_suit_id)
{
	//int input[] = { equip_suit_id };
	//achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION_NEW, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnUnlockFashionSuit(gplayer_imp *imp, int fashion_suit_id)
{
	int input[] = { fashion_suit_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION_NEW, input, sizeof(input) / sizeof(int));
}
void player_achieve_man::OnUnlockFashionNew(gplayer_imp *imp, int fashion_index)
{
	int input[] = { fashion_index };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_FASHION_SPECIAL, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnUnlockSurface(gplayer_imp *imp, int surface_tid)
{
	int input[] = { surface_tid };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_VEHICLE, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnGetRetinueEvent(gplayer_imp *imp, int retinue_id)
{
	int input[] = { retinue_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_PARTNER, input, sizeof(input) / sizeof(int));

	auto iter = GLOBAL_CONFIG.retinue_2_type.find(retinue_id);
	if (iter != GLOBAL_CONFIG.retinue_2_type.end())
	{
		int input_type[] = { iter->second };
		achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_PARTNER_GROUP, input_type, sizeof(input_type) / sizeof(int));
	}

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_PARTNER_UNLOCK);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_PARTNER_UNLOCK, EXP_ACHIEVE_EVENTCOND_NONE);

	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_PARTNER_GROUP_UNLOCK);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_PARTNER_GROUP_UNLOCK, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGetGuardEvent(gplayer_imp *imp, int guard_id)
{
	int input[] = { guard_id };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_UNLOCK_PET, input, sizeof(input) / sizeof(int));
}

void player_achieve_man::OnSendHoneyGardenBouquet(gplayer_imp *imp)
{
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_SEND_FLOWERS);
}

void player_achieve_man::OnComposeLongyu(gplayer_imp *imp, bool success, int quality)
{
	int input[] = { success, quality };
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_CONTINUOUS, input, sizeof(input) / sizeof(int));
	if (success)
	{
		if (quality == LONGYU_QUALITY_GOLDEN)
		{
			achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_TOTALL, input, sizeof(input) / sizeof(int));
		}
		else if (quality == LONGYU_QUALITY_GOLDEN - 1)
		{
			achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_ORANGE, input, sizeof(input) / sizeof(int));
		}
	}
	else
	{
		achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_GEN_LONGYU_FAILURE, input, sizeof(input) / sizeof(int));
	}
}

void player_achieve_man::OnLearnProduceSkill(gplayer_imp *imp, int skill_id, int skill_level)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp,_complete_map,_unlock_set,EXP_ACHIEVE_STATECOND_LEARN_PROD_SKILL);
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_LEARN_PROD_SKILL,EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnLearnRecipe(gplayer_imp *imp, int recipe_id, int recipe_level)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp,_complete_map,_unlock_set,EXP_ACHIEVE_STATECOND_LEARN_RECIPE);
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_LEARN_RECIPE,EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnBattleRecordChanged(gplayer_imp *imp, int battle_class, bool victory)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp,_complete_map,_unlock_set,EXP_ACHIEVE_STATECOND_ARENA_COUNT);
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_ARENA_COUNT,EXP_ACHIEVE_EVENTCOND_NONE);
	if(victory)
	{
		achievement_manager::GetInstance().RebuildUnlockSet(imp,_complete_map,_unlock_set,EXP_ACHIEVE_STATECOND_ARENA_CONTINOUS_WIN);
		achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_ARENA_CONTINOUS_WIN,EXP_ACHIEVE_EVENTCOND_NONE);
	}
	*/
}

void player_achieve_man::OnWuLinLevelChanged(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp,_complete_map,_unlock_set,EXP_ACHIEVE_STATECOND_ARENA_GRADE);
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_ARENA_GRADE,EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnMarry(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_MARRY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_MARRY, EXP_ACHIEVE_EVENTCOND_NONE);
}


void player_achieve_man::OnApprentice(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_APPRENTICE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_APPRENTICE, EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnAcceptDisciple(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACCEPT_DISCIPLE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACCEPT_DISCIPLE, EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnJoinFamily(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_JOIN_FAMILY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_JOIN_FAMILY, EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnUpgradeSoul(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_UPGRADE_SOUL);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_UPGRADE_SOUL, EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnActiveAptitude(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACTIVE_APTITUDE);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_ACTIVE_APTITUDE, EXP_ACHIEVE_EVENTCOND_NONE);
	*/
}

void player_achieve_man::OnInventorySizeChanged(gplayer_imp *imp)
{
//$$$$$$$$
//	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_INVENTORY_SIZE);
//	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_INVENTORY_SIZE, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnInventoryExtraSizeChanged(gplayer_imp *imp)
{
	achievement_manager::GetInstance().RebuildUnlockSet(imp, _complete_map, _unlock_set, EXP_ACHIEVE_STATECOND_INVENTORY_EXTRA_CAPACITY);
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_INVENTORY_EXTRA_CAPACITY, EXP_ACHIEVE_EVENTCOND_NONE);
}

void player_achieve_man::OnGetPet(gplayer_imp *imp, int pet_tid, int pet_quality)
{
	/*
	int input[] = { pet_tid, pet_quality };
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_GET_PET,input,sizeof(input)/sizeof(int));
	*/
}


void player_achieve_man::OnProduceItem(gplayer_imp *imp, int recipe_tid)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_PRODUCE, &recipe_tid, 1);
	*/
}

void player_achieve_man::OnInstanceBoardFinished(gplayer_imp *imp, int instance_tid, int board_id)
{
	/*
	int input[] = { instance_tid, board_id };
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_FINISH_INSTANCE,input,sizeof(input)/sizeof(int));
	*/
}

void player_achieve_man::OnInstanceFinished(gplayer_imp *imp, int instance_tid, int mode, int level)
{
	/*
	int input[] = { instance_tid, mode, level};
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_FINISH_INST_MODE,input,sizeof(input)/sizeof(int));
	*/
}

void player_achieve_man::OnDuel(gplayer_imp *imp, int duel_result)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_PK,&duel_result,1);
	*/
}

void player_achieve_man::OnDead(gplayer_imp *imp)
{
	/*
	int scene_id = imp->GetParent()->scene_tag;
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_DEAD, &scene_id, 1);
	*/
}

void player_achieve_man::OnSpecificTypeTaskFinished(gplayer_imp *imp, int task_type, int task_id)
{
	//int input[] = { task_type, task_id };
	/*
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_COMPLETE_TASK, &task_type, 1);
	*/
}

void player_achieve_man::OnAnswerQuestion(gplayer_imp *imp, bool is_on_list)
{
	/*
	int input = (is_on_list ? 1 : 0);
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_ANSWER_QUEST,&input,1);
	*/
}

void player_achieve_man::OnFinishOneBattle(gplayer_imp *imp, int battle_class, bool victory)
{
	/*
	int input[] = { battle_class, victory };
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_ARENA_MODE_COUNT,input,sizeof(input)/sizeof(int));
	*/
}

void player_achieve_man::OnPerfectBattle(gplayer_imp *imp, int battle_mode)
{
	/*
	int input = battle_mode;
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_ARENA_PERFECT_COUNT,&input,1);
	*/
}

void player_achieve_man::OnBattleMVP(gplayer_imp *imp, int mvp_type)
{
	/*
	int input = mvp_type;
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE,EXP_ACHIEVE_EVENTCOND_ARENA_MVP_COUNT,&input,1);
	*/
}

void player_achieve_man::OnRoamLogin(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp,_complete_map,_in_progress_map,_unlock_set,EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_CROSS_SERVER_COUNT);
	*/
}

void player_achieve_man::OnUseService(gplayer_imp *imp, int service_id)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_USE_SERVICE, &service_id, 1);
	*/
}

void player_achieve_man::OnSynthetizeStone(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_SYNTHETIZE_STONE);
	*/
}

void player_achieve_man::OnEquipMakeHole(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_MAKE_HOLE);
	*/
}

void player_achieve_man::OnEquipEmbedStone(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_EMBED_STONE);
	*/
}

void player_achieve_man::OnEquipUnembedStone(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_EQUIP_UNEMBED_STONE);
	*/
}

void player_achieve_man::OnSynthetizeEquip(gplayer_imp *imp, int prev_level, int post_level)
{
	/*
	int input[] = {prev_level, post_level};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_SYNTHETIZE_EQUIP, input, 2);
	*/
}

void player_achieve_man::OnReviveAtOnce(gplayer_imp *imp)
{
	/*
	int world_tid = imp->GetParent()->world_tid;
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_REVIVE_AT_ONCE, &world_tid, 1);
	*/
}

void player_achieve_man::OnProduceEquip(gplayer_imp *imp, int equip_count, int equip_quality)
{
	/*
	int input[] = {equip_count, equip_quality};
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_PRODUCE_EQUIP, input, 2);
	*/
}

void player_achieve_man::OnKillPlayer(gplayer_imp *imp)
{
	/*
	int world_tid = imp->GetParent()->world_tid;
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_KILL_PLAYER, &world_tid, 1);
	*/
}

void player_achieve_man::OnTizi(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_TIZI);
	*/
}

void player_achieve_man::OnEraseTizi(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_ERASE_TIZI);
	*/
}

void player_achieve_man::OnParading(gplayer_imp *imp, int level)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_PARADING, &level, 1);
	*/
}

void player_achieve_man::OnResetSoul(gplayer_imp *imp)
{
	/*
	achievement_manager::GetInstance().UpdateComplete(imp, _complete_map, _in_progress_map, _unlock_set, EXP_ACHIEVE_STATECOND_NONE, EXP_ACHIEVE_EVENTCOND_RESET_SOUL);
	*/
}

///* 状态条件: 职业满足 */
//class condition_template_prof : public condition_template
//{
//public:
//	condition_template_prof() : condition_template(CT_STATE,EXP_ACHIEVE_STATECOND_PROF) {}
//	/* 参数说明:
//	 * <1>职业序号: 0表示新手，1-10表示少林-峨眉，11及以上无意义
//	 */
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const { return imp->GetProf() == params[0]; }
//};



///* 状态条件: 完成场景任务 */
//class condition_template_scene_task : public condition_template
//{
//public:
//	condition_template_scene_task() : condition_template(CT_STATE,EXP_ACHIEVE_STATECOND_SCENE_TASK) {}
//	/* 参数说明:
//	 * <1>场景id
//	 * <2>任务个数
//	 */
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		if((*imp->GetTaskGuard())->GetFnshSceneTaskNum(params[0]) < (unsigned int)params[1]) return false;
//		return true;
//	}
//};


///* 状态条件: 学习生产技能 */
//class condition_template_learn_produce_skill : public condition_template
//{
//public:
//	condition_template_learn_produce_skill() : condition_template(CT_STATE,EXP_ACHIEVE_STATECOND_LEARN_PROD_SKILL) {}
//	/* 参数说明:
//	 * <1>生产技能id
//	 * <2>生产技能等级
//	 */
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		return false;
//	}
//};
//
///* 状态条件: 学会配方数目 */
//class condition_template_learn_recipe : public condition_template
//{
//public:
//	condition_template_learn_recipe() : condition_template(CT_STATE,EXP_ACHIEVE_STATECOND_LEARN_RECIPE) {}
//	/* 参数说明:
//	 * <1>生产配方id
//	 * <2>配方数量
//	 * <3>生产配方等级
//	 */
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		return false;
//	}
//};
//
///* 状态条件: 竞技场总次数 */
//class condition_template_battle_count : public condition_template
//{
//public:
//	condition_template_battle_count() : condition_template(CT_STATE,EXP_ACHIEVE_STATECOND_ARENA_COUNT) {}
//	/* 参数说明:
//	 * <1>竞技类型
//	 * <2>竞技次数
//	 * <3>竞技结果类型
//	 */
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		return false;
////		return (params[0] >= 0 && params[0] < BATTLEFIELDCLASS_COUNT) &&
////			(imp->GetRecord().GetCount(params[0], params[2]) >= params[1]);
//	}
//};
//
///* 状态条件: 竞技段位达到 */
//class condition_template_wulin_level : public condition_template
//{
//public:
//	condition_template_wulin_level() : condition_template(CT_STATE,EXP_ACHIEVE_STATECOND_ARENA_GRADE) {}
//	/* 参数说明:
//	 * <1>段位
//	 */
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		//武林已经废弃
//		return false;
//	}
//};
//
///* 状态条件: 连胜次数达到 */
//class condition_template_winning_steak : public condition_template
//{
//public:
//	condition_template_winning_steak() : condition_template(CT_STATE,EXP_ACHIEVE_STATECOND_ARENA_CONTINOUS_WIN) {}
//	/* 参数说明:
//	 * <1>竞技类型
//	 * <2>次数
//	 */
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		return false;
//	}
//};
//
/* 状态条件: 结婚条件 */
/*class condition_template_marry : public condition_template
{
public:
	condition_template_marry() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_MARRY) {}
	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
	{
		return imp->IsMarried();
	}
};*/

///* 状态条件: 拜师 */
//class condition_template_apprentice : public condition_template
//{
//public:
//	condition_template_apprentice() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_APPRENTICE) {}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return false;
//	}
//};
//
///* 状态条件: 收徒 */
//class condition_template_accept_disciple : public condition_template
//{
//public:
//	condition_template_accept_disciple() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ACCEPT_DISCIPLE) {}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return false;
//	}
//};
//
///* 状态条件: 结义 */
//class condition_template_join_family : public condition_template
//{
//public:
//	condition_template_join_family() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_JOIN_FAMILY) {}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return false;
//	}
//};

///* 状态条件: 升级心法 */
//class condition_template_upgrade_soul : public condition_template
//{
//public:
//	condition_template_upgrade_soul() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_UPGRADE_SOUL) {}
//	/* 参数说明:
//	 * <1>心法等级
//	 */
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		//心法已经废弃
//		return false;
//	}
//};
//
///* 状态条件: 激活穴位 */
//class condition_template_active_aptitude : public condition_template
//{
//public:
//	condition_template_active_aptitude() : condition_template(CT_STATE, EXP_ACHIEVE_STATECOND_ACTIVE_APTITUDE) {}
//	/* 参数说明:
//	 * <1>激活数量
//	 */
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		//心法已经废弃
//		return false;
//	}
//};

///* 事件条件: 装备物品
// * 参数说明:
// * <1>装备id
// */
//class condition_template_equip_item : public condition_template
//{
//public:
//	condition_template_equip_item() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_EQUIP_ITEM) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid,
//					  size_t cond_count, size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//	}
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		return false;
//	}
//};


///* 事件条件: 获取宠物
// * 成就完成条件参数含义：
// * <1>宠物id：宠物牌id，如为0则是“任意宠物牌”
// * <2>次数：
// * <3>宠物品质，为1、2、3等则限制特定品质的宠物，如为0则不限
// * 注：第一、三个条件是与的关系，都满足才记次
// */
//class condition_template_get_pet : public condition_template
//{
//public:
//	condition_template_get_pet() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_GET_PET) {}
//
//	//input: 宠物ID，宠物品质
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 2 || input[0] < 0 || input[1] < 0) return;
//		if ((0 != params[0] && params[0] != input[0]) || (0 != params[2] && params[2] != input[1])) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};


///* 事件条件: 生产
// * 参数说明:
// * <1>配方id，0表示任意配方均计数
// * <2>次数
// */
//class condition_template_produce : public condition_template
//{
//public:
//	condition_template_produce() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_PRODUCE) {}
//	// input: 配方ID
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map,
//					  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
//					  const int *input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 1 || input[0] < 0) return;
//		if (0 == params[0] || params[0] == input[0])
//			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		return (NULL != params && NULL != data && *data >= params[1]);
//	}
//};
//
///* 事件条件: 完成副本
// * 三项参数分别为
// * <1>副本ID：config　ID中副本模板的ID
// * <2>次数：副本成功完成并退出的次数
// * <3>难度：改成“版面ID”，当指定副本ID的指定版面完成计次
// */
//class condition_template_finish_instance : public condition_template
//{
//public:
//	condition_template_finish_instance() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_FINISH_INSTANCE) {}
//
//	//input: instance template id, board id
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 2 || input[0] <= 0 || input[1] <= 0) return;
//		if (params[0] != input[0] || params[2] != input[1]) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
///* 事件条件: 完成副本（限难度评价）
// * 四项参数分别为
// * <1>参数1：副本ID
// * <2>参数2：完成副本次数
// * <3>参数3：副本难度模式，当副本完成时检查副本难度，需要大于等于填写值。
// * <4>参数4：评价等级，当副本完成时检查副本评价，需要大于等于填写值。（评价在脚本ectype_score.lua填写，每个模式的Award ={}里上往下为1、2、3、4档）
// */
//class condition_template_finish_inst_mode: public condition_template
//{
//public:
//	condition_template_finish_inst_mode() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_FINISH_INST_MODE) {}
//
//	//input: instance template id, instance mode, score level
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 3 || input[0] <= 0 || input[1] <= 0 || input[2] <= 0) return;
//		if (params[0] != input[0] || (params[2] != 0 && input[1] < params[2]) || (params[3] != 0 && input[2] < params[3])) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
///* 事件条件: 进行决斗
// * 三项参数分别为
// * <1>决斗结果：1胜利，2失败，0其它结束（脱离）
// * <2>次数：结束切的次数
// * <3>判断方式：0大于等于，1小于等于
// */
//class condition_template_pk : public condition_template
//{
//public:
//	condition_template_pk() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_PK) {}
//
//	//input: 决斗结果，refer to PK_RESULT_xxx in achievement_manager.h
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		//if (NULL == params || NULL == input || input_count != 1) return;
//		//if (0 == params[0] ||
//		//    (1 == params[0] && player_duel::DUEL_RESULT_WIN == input[0]) ||
//		//    (2 == params[0] && player_duel::DUEL_RESULT_LOSE == input[0]))
//		//{
//		//	UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//		//}
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = false;
//		if (NULL != data && NULL != params)
//		{
//			switch (params[2])
//			{
//				case 0:
//					flag = (*data >= params[1]);
//					break;
//				case 1:
//					flag = (*data <= params[1]);
//					break;
//				default:
//					break;
//			}
//		}
//		return flag;
//	}
//};
//
///* 事件条件: 死亡
// * 参数说明
// * <1>场景id
// * <2>次数
// */
//class condition_template_dead : public condition_template
//{
//public:
//	condition_template_dead() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_DEAD) {}
//	// input: 场景id
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map,
//					  int achievement_tid, size_t cond_count, size_t cond_index, const int *params,
//					  const int *input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 1 || input[0] < 0) return;
//		if (0 == params[0] || params[0] == input[0])
//			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		return (NULL != params && NULL != data && *data >= params[1]);
//       	}
//};
//
///* 事件条件: 获取绑定币
// * 添加两个参数
// * <1>获取方式：0所有获取绑定币，1通过任务获得绑定币，2通过怪物掉落获得绑定币
// * <2>总量：绑定币获得数量
// */
//class condition_template_got_bindmoney : public condition_template
//{
//public:
//	condition_template_got_bindmoney() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_GOT_BINDMONEY) {}
//
//	// input: 总增加值，任务增加值，杀怪增加值
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 3 || input[0] <= 0 || input[1] < 0 || input[2] < 0) return;
//		int delta = 0;
//		switch (params[0])
//		{
//			case 0:
//				delta = input[0];
//				break;
//			case 1:
//				delta = input[1];
//				break;
//			case 2:
//				delta = input[2];
//				break;
//			default:
//				break;
//		}
//		if (0 != delta)
//		{
//			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, delta, true);
//		}
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
///* 事件条件: 完成某类任务
// * 成就完成条件参数含义：
// * <1>任务类型编号
// * <2>次数
// */
//class condition_template_complete_task: public condition_template
//{
//public:
//	condition_template_complete_task() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_COMPLETE_TASK) {}
//
//	// input: 任务类型编号
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || params[0] <= 0) return;
//		if (NULL == input || input_count != 1 || input[0] <= 0) return;
//		if (params[0] != input[0]) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
///* 事件条件: 答题
// * 成就完成条件参数含义：
// * <1>是否上榜
// * <2>次数
// */
//class condition_template_answer_question : public condition_template
//{
//public:
//	condition_template_answer_question() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_ANSWER_QUEST) {}
//
//	// input: 是否上榜
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 1) return;
//		if (static_cast<bool>(params[0]) != static_cast<bool>(input[0])) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
//
///* 事件条件: 竞技模式次数
// * 三项参数分别为
// * <1>竞技模式: 擂台模式：int，1－5代表战场“局数选择”的mask
// * <2>次数：
// * <3>结果:int，0代表总次数，1代表胜利次数
// */
//class condition_template_battle_mode : public condition_template
//{
//public:
//	condition_template_battle_mode() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_ARENA_MODE_COUNT) {}
//
//	//input: 模式 结果
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 2 || input[0] <= 0 || input[1] < 0) return;
//		if (params[0] != input[0] || params[2] != input[1]) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
///* 事件条件: 竞技完美次数
// * 二项参数分别为
// * <1>竞技模式: 擂台模式：int，1－5代表战场“局数选择”的mask
// * <2>次数
// */
//class condition_template_battle_perfect : public condition_template
//{
//public:
//	condition_template_battle_perfect() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_ARENA_PERFECT_COUNT) {}
//
//	//input: 模式
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 1 || input[0] <= 0) return;
//		if (params[0] != input[0]) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
///* 事件条件: 竞技MVP次数
// * 二项参数分别为
// * <1>MVP类型:0类擂台竞技，车轮战（目前有） 1类竞技（功能待补充）
// * <2>次数：
// */
//class condition_template_battle_mvp : public condition_template
//{
//public:
//	condition_template_battle_mvp() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_ARENA_MVP_COUNT) {}
//
//	//input: 模式
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		if (NULL == params || NULL == input || input_count != 1 || input[0] < 0) return;
//		if (params[0] != input[0]) return;
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[1]);
//		return flag;
//	}
//};
//
///* 事件条件: 跨服次数
// * 一个参数
// * <1>次数
// */
//class condition_template_roam_count : public condition_template
//{
//public:
//	condition_template_roam_count() : condition_template(CT_EVENT,EXP_ACHIEVE_EVENTCOND_CROSS_SERVER_COUNT) {}
//
//	//input: 模式
//	virtual void UpdateInProgressData(gplayer_imp* imp, IN_PROGRESS_ACHIEVEMENT_MAP& in_progress_map,
//	                                  int achievement_tid, size_t cond_count, size_t cond_index, const int* params,
//	                                  const int* input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//
//	virtual bool Check(gplayer_imp* imp,const int* params,const int* data) const
//	{
//		bool flag = (NULL != data && NULL != params && *data >= params[0]);
//		return flag;
//	}
//};
//
///* 事件条件: 使用服务
// * 参数说明
// * <1>使用次数
// * <2>服务ID
// */
//class condition_template_use_service : public condition_template
//{
//public:
//	condition_template_use_service() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_USE_SERVICE) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		if (params == NULL || input == NULL || input_count != 1)
//			return;
//		if (params[1] == input[0])
//			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 合成宝石
// * 参数说明
// * <1>次数
// */
//class condition_template_synthetize_stone : public condition_template
//{
//public:
//	condition_template_synthetize_stone() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SYNTHETIZE_STONE) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data > params[0]);
//	}
//};
//
///* 事件条件: 装备打孔
// * 参数说明
// * <1>次数
// */
//class condition_template_equip_make_hole : public condition_template
//{
//public:
//	condition_template_equip_make_hole() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_MAKE_HOLE) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 镶嵌宝石
// * 参数说明
// * <1>镶嵌次数
// */
//class condition_template_equip_embed_stone : public condition_template
//{
//public:
//	condition_template_equip_embed_stone() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_EMBED_STONE) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 取出宝石
// * 参数说明
// * <1>取出次数
// */
//class condition_template_equip_unembed_stone : public condition_template
//{
//public:
//	condition_template_equip_unembed_stone() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_EQUIP_UNEMBED_STONE) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 装备淬炼
// * 参数说明
// * <1>数量
// * <2>淬炼等级
// */
//class condition_template_synthetize_equip : public condition_template
//{
//public:
//	condition_template_synthetize_equip() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_SYNTHETIZE_EQUIP) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		if (params == NULL || input == NULL || input_count != 2)
//			return;
//		int prev_level = input[0];
//		int post_level = input[1];
//		if (prev_level < params[1] && post_level >= params[1])
//			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 原地复活
// * 参数说明
// * <1>场景id
// * <2>复活次数
// */
//class condition_template_revive_at_once : public condition_template
//{
//public:
//	condition_template_revive_at_once() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_REVIVE_AT_ONCE) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		if (params == NULL || input == NULL || input_count != 1)
//			return;
//		if (params[0] == 0 || params[0] == input[0])
//			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[1]);
//	}
//};
//
///* 事件条件: 生产装备
// * 参数说明
// * <1>装备数量
// * <2>装备品质
// */
//class condition_template_produce_equip : public condition_template
//{
//public:
//	condition_template_produce_equip() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_PRODUCE_EQUIP) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		if (params == NULL || input == NULL || input_count != 2)
//			return;
//		if (input[1] >= params[1])
//			UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, input[0], true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 击杀玩家
// * 参数说明
// * <1>场景类型
// * <2>击杀数量
// */
//class condition_template_kill_player : public condition_template
//{
//public:
//	condition_template_kill_player() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_KILL_PLAYER) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		if (params == NULL || input == NULL || input_count != 1)
//			return;
//		if (params[0] == 0 || params[0] == (input[0] + 1))
//		       UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[1]);
//	}
//};
//
///* 事件条件: 涂鸦
// * 参数说明
// * <1>次数
// */
//class condition_template_tizi : public condition_template
//{
//public:
//	condition_template_tizi() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_TIZI) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 擦除涂鸦
// * 参数说明
// * <1>次数
// */
//class condition_template_erase_tizi : public condition_template
//{
//public:
//	condition_template_erase_tizi() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_ERASE_TIZI) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 游街
// * 参数说明
// * <1>游街次数
// * <2> 游街类型: 0-任意游街类型，1-普通游街类型，2-中级游街类型，3-高级游街类型
// */
//class condition_template_parading : public condition_template
//{
//public:
//	condition_template_parading() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_PARADING) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		if (params == NULL || input == NULL || input_count != 1)
//			return;
//		if (input[0] >= 0 && input[0] < EXP_PARADING_SERVICE_CONFIG_NUM)
//		{
//			if (params[1] == 0 || (input[0] + 1) == params[1])
//				UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//		}
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};
//
///* 事件条件: 重置穴位
// * 参数说明
// * <1>重置次数
// */
//class condition_template_reset_soul : public condition_template
//{
//public:
//	condition_template_reset_soul() : condition_template(CT_EVENT, EXP_ACHIEVE_EVENTCOND_RESET_SOUL) {}
//	virtual void UpdateInProgressData(gplayer_imp *imp, IN_PROGRESS_ACHIEVEMENT_MAP &in_progress_map, int achievement_tid, size_t cond_count,
//					size_t cond_index, const int *params, const int *input = NULL, int input_count = 0) const
//	{
//		UpdateInProgressAchievement(imp, in_progress_map, achievement_tid, cond_count, cond_index, 1, true);
//	}
//	virtual bool Check(gplayer_imp *imp, const int *params, const int *data) const
//	{
//		return (params != NULL && data != NULL && *data >= params[0]);
//	}
//};


