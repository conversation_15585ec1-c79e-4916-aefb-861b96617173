#ifndef __GS_ATTACK_H__
#define __GS_ATTACK_H__

#include <a3dvector.h>

#include <common/types.h>

#include "config.h"
#include "property.h"
const int  SKILL_TALENT_COUNT = 25;
//为了方便拷贝，不要出现任何指针和复杂结构比如octets
//攻击者信息
struct attacker_info_t
{
	enum 
	{
		AM_PVP_ENABLE		= 0x0001,	//激活PVP状态（非安全区才有效且不在低级保护时）
		AM_PVP_FREE			= 0x0002,	//自由PK
		AM_DRAGONBORN		= 0x0004,	//龙裔攻击，复用(原来是AM_PVP_DURATION已经在和玩家PK)
		AM_PVP_DUEL			= 0x0008,	//此次是决斗攻击
		AM_PVP_MAFIA_DUEL	= 0x0010,	//此次是帮派决斗
		AM_PVP_SANCTUARY	= 0x0020,	//安全区
		AM_PLAYER_HEIR		= 0x0040,	//player_heir
		AM_PVP_INVADER		= 0x0080,	//是否卑劣攻击
		AM_PVP_BATTLE		= 0x0100,	//战场战斗
		AM_PVP_BATTLE_1V1	= 0x0200,	//战场1v1
		AM_GUARD			= 0x0400,	//守护灵攻击
		AM_RETINUE			= 0x0800,	//伙伴攻击
		AM_SKILLFX_PRIO		= 0x1000,	//使用最高等级技能光效
		AM_PLAYER_NPC		= 0x2000,	//player_npc
		AM_PLAYER_TWIN		= 0x4000,	//player_twin
		AM_PLAYER_MECH		= 0x8000,	//player_mech
		AM_PLAYER_REPLISOME		= 0x10000,	//player_replisome
		AM_PLAYER_DOG         = 0x20000, // prof12 dog
	};//attacker_mode使用
	//XID 有默认的构造 
	XID attacker;			//攻击者的ID 对于宠物,此ID与消息ID不一致
	XID duel_target;		//决斗者id
	level_t level = 0;			//攻击者级别
	level_t prof_level = 0;		//攻击者战斗等级
	level_t eff_level = 0;		//有效级别 为组队做准备的
	link_id_t lid;
	//A3DVECTOR3 不管
	A3DVECTOR3 pos ;			//位置
	XID team;			//攻击者队伍
	//int family_id;			//家族ID,用于攻击判定
	int64_t mafia_id = 0;			//帮派ID
	unsigned int faction = 0;		//攻击者阵营
	unsigned int enemy_faction = 0;	//攻击者的敌人阵营(自己的阵营只有符合这个阵营才能被非强制攻击伤害)
	unsigned int friend_faction =0;	//攻击者的友好阵营(自己的阵营只有符合这个阵营才能被祝福)
	unsigned int attacker_mode = 0;	//攻击者的状态(定义见AM系列枚举)
	unsigned short native_zoneid =0;	//攻击者的原生服务器
	unsigned char pk_setting_a = 0;	//pk模式
	short pk_value_a = 0;
	bool married = false;			//是否结婚
	bool is_male = false;			//
	unsigned char name_len = 0;		//名字长度
	char name[MAX_PLAYER_NAME_LENGTH] = {0};	//名字
	union
	{
		int wallow_level = 0;	//如果是player这个表示沉迷等级
		int npc_tid ;		//如果是npc这个是npc的tid
	};
	float body_size = 0.f;		//(直接)攻击者自身半径
	ruid_t spouse_id = 0;		//配偶id
	ruid_t mentor_id = 0;		//师父ID
	A3DVECTOR3 dir;			//(直接)攻击者的朝向
	unsigned int extend_faction = 0;	//特殊阵营处理
	XID pet_master;			//主人
	XID cur_target;			//当前目标
	uint64_t fashion_gfx_modify_mask  = 0;
	ruid_t pve_spouse_id = 0;   
	int64_t roam_communityid = 0;
	bool is_skill_npc = false;
};

#define AM_SEQUENCE_XID_COUNT 5
#define FEEDBACK_SKILL_COUNT 3
#define ATTACK_TYPEMASK_COUNT 5

typedef uint64_t filter_typemask_t;
const int FILTER_TYPEMASK_COUNT = sizeof(filter_typemask_t) * 8;

typedef uint64_t filter_eventmask_t;

//攻击消息
struct attack_msg
{
	//Creature::FillAttackMsg 填充 
	attacker_info_t attacker_info;	//攻击者的信息

	int		pvp_level = 0;		//PVP伤害等级

	int 	attack_physic = 0;	//物理攻击
	int 	attack_magic = 0;	//法术攻击

	int 	base_attack_physic = 0;	//计算原伤的物理攻击
	int 	base_attack_magic = 0;	//计算原伤的法术攻击

	int 	attack_element_damage[4] = {0};	//元素伤害
	int		attack_element_type = 0;	//元素属性

	int 	attack_crit = 0;	//暴击率
	int 	attack_crit_ratio = 0;//暴击伤害
	int		attack_pierce = 0;	//真破防，破甲
	int		attack_pierce_inc = 0;//破甲增幅
	int		attack_stun = 0;	//硬直值
	int		attack_hit = 0;		//命中等级
	int		attack_hit_add = 0; //额外命中率
	int		attack_seven_crime_add = 0;//罪责强度

	//对具有typemask的目标增伤
	filter_typemask_t attack_typemask[ATTACK_TYPEMASK_COUNT] = {0};
	short attack_typemask_damage_add[ATTACK_TYPEMASK_COUNT] = {0};

	int 	attack_physic_damage_add = {0};	//物理增伤
	int 	attack_magic_damage_add = 0;	//法术增伤
	int		attack_element_damage_add[4] = {0};	//元素增伤
	int 	attack_npc_damage_add = 0;	//对npc的增伤
	int 	attack_deadly_damage_add = 0;//对生命值低于25%的目标增伤
	int		attack_damage_add = 0;		//总增伤
	int		attack_damage_add2 = 0;		//总增伤2
	int		attack_damage_add3 = 0;		//总增伤3
	int		attack_damage_add4 = 0;		//总增伤4
	int		attack_damage_add5 = 0;		//总增伤5
	int		attack_damage_add6 = 0;		//总增伤6
	int		attack_damage_add7 = 0;		//总增伤7
	int		attack_damage_add8 = 0;		//总增伤8
	int		attack_damage_add9 = 0;		//总增伤9
	int		attack_damage_add10 =0;		//总增伤10
	int		attack_damage_add11 =0;		//总增伤10
	int		attack_damage_add13 =0;		//总增伤13
	int		attack_damage_add12 =0;		//总增伤10
	int		attack_damage_add14 =0;		//总增伤14
	int		attack_damage_add15 =0;		//总增伤15
	int		attack_damage_add16 =0;		//总增伤16
	int		attack_damage_add17 =0;		//总增伤17
	int		attack_damage_add18 =0;		//总增伤18
	int		attack_damage_add19 =0;		//总增伤19
	int		attack_damage_add20 =0;		//总增伤20
	int		attack_damage_rate  = 0;		//最终伤害倍率, 目前只有npc使用
	int		attack_opposite_sex_damage_add = 0;//对异性增伤
	int		attack_same_sex_damage_add = 0;//对同性增伤
	int		heir_dam_add = 0;//对继承者伤害增加千分比
	int		pvp_dam_add = 0;//对玩家伤害增加千分比
	int		pve_dam_fix = 0;//pve伤害修正		 
	int     pvp_dam_fix = 0;//pvp伤害修正
	int		pvp_dam_add2 = 0;//对玩家伤害增加千分比
	int 		attack_versatility_rating = 0; //全能属性(用于增伤/减伤)
	//Creature::FillAttackMsg 填充 结束

	//Skill::FillAttackMsg 填充
	unsigned short 	skill_id = 0;	//技能攻击对应的技能号
	unsigned char 	skill_level = 0;	//技能等级
	unsigned char 	attack_stage = 0;	//连击阶段计数
	bool 		helpful = 0;	//是否善意法术
	bool 		is_xp_skill = 0;	//是否XP技能
	bool		only_npc = 0;	//是否只溅射npc
	unsigned int 	hit_mask = 0;	//会击中的方位集合
	int 		hit_mask_type = 0;	//命中类型

	char 		skill_damage_type = 0;	//攻击类型
	int64_t 	skill_physic_damage = 0;	//技能物理伤害
	int 		skill_physic_ratio = 0;	//技能物理伤害倍率
	int64_t 	skill_magic_damage = 0;	//技能法术伤害
	int 		skill_magic_ratio = 0;	//技能法术伤害倍率
	float		skill_damage_rate = 0;	//技能最终伤害比率
	int			skill_element_damage[4] ={0};	//技能元素伤害 new
	int			skill_element_ratio[4] = {0};	//技能元素伤害倍率 new
	int 		skill_crit = 0; 	//技能附加爆击率
	int			skill_hit = 0;		//技能附加命中率 new
	int			skill_aggro =0;	//技能附加仇恨倍率
	int         replisome_dam_ratio= 0; //职业10分身伤害比例调整
	////Skill::FillAttackMsg 填充 结束
	
	//特殊填充
	unsigned short 	subobj_pid =0;	//子物体过程编号

	char 		control_type = 0;	//控制类型(站立/浮空/倒地)
	int 		control_time = 0;	//控制时间
	char 		control_move_type = 0;	//控制位移类型(无/击退/拉回)
	char 		control_move_level = 0;	//控制位移等级(0最大，CHAR_MAX最小）
	char 		control_dir = 0;		//是否改变被控制者朝向(不/面朝攻击者/背对攻击者)
	float 		control_move_distance  =0.f;	//控制位移距离
	int 		control_move_dir_type = 0;	//控制位移的方向类型
	bool 		is_zhuatou_control = false;	//抓投控制?
	bool 		is_force_control = false;	//强制控制?
	bool 		can_add_in_immue_control = 0;	//受免控影响?
	//反馈相关
	int 		action_id = 0;	//攻击action id
	unsigned int 	seq = 0;		//攻击序列号
	int 		hit_add = 0;	//攻击命中时自身增加连击数
	int 		force_add = 0;	//攻击命中时自身增加蓝
	unsigned short feedback_type[FEEDBACK_SKILL_COUNT] = {0};//反馈类型
	unsigned short feedback_arg[FEEDBACK_SKILL_COUNT] = {0};//反馈参数
	//技能可引用关键字
	float 		distance = 0.f;
	float 		charge_ratio = 0.f;
	float 		last_moved_distance = 0.f;
	bool 		hit_someone = false;//本技能是否命中过
	int 		skill_modify[SKILL_TALENT_COUNT] = {0};
	//依次被影响(闪电链)
	bool 		is_sequence = false;
	short 		sequence_tick = 0;
	char 		sequence_idx = 0;
	XID 		sequence_xid[AM_SEQUENCE_XID_COUNT]; 
	//自己造成的伤害与挨打者的距离相关
	float 		distance_dmg_rate = 0.f;
	short 		distance_dmg_first = 0;
	//自己造成的伤害与挨打者的距离相关
	float 		distance_dmg_rate2 = 0.f;
	short 		distance_dmg_first2 = 0;

	int 	special_attack_damage_add = 0;	//特殊增伤
	int 	master_attack_damage_add = 0;	//主人增伤
	/*enum	//attack_mask的定义
	{
		AM_MASK_MELEE		= 0x01,		//表示是近战武器技能
	};*/
	//unsigned char attack_class;	//攻击类型
	//float phy_attack_low;		//外功攻击下限
	//float phy_attack_high;		//外功攻击上限
	//float phy_attack_power;		//外功攻击强度
	//float phy_hit;		//外功命中
	//float phy_crit_rate;		//外功暴击率
	//float phy_crit_damage_scale;	//外功暴击附加伤害比率
	//float phy_crit_damage_point;	//外功暴击附加伤害点数
	//float mag_attack_low;		//内功攻击下限
	//float mag_attack_high;		//内功攻击上限
	//float mag_attack_power;		//内功攻击强度
	//float mag_hit;		//内功命中
	//float mag_crit_rate;		//内功暴击率
	//float mag_crit_damage_scale;	//内功暴击附加伤害比率
	//float mag_crit_damage_point;	//内功暴击附加伤害点数
	//float attr_damage[ATT_COUNT];		//属性伤害
	//float attr_resist_ignore[ATT_COUNT];	//属性伤害抗性无视
	//float attr_pierce[ATT_COUNT];		//属性伤害穿透
	//float crit;			//会心
	//float crit_ratio;		//会心伤害比率
	//float mastery;			//精通
	//float phy_skill_ratio;		//技能外功倍率
	//float phy_skill_damage;		//技能外功伤害
	//float mag_skill_ratio;		//技能内功倍率
	//float mag_skill_damage;		//技能内功伤害
	//unsigned char judge_type;	//技能判定类型
	//unsigned char attack_mask;	//攻击消息的mask 
	//bool can_hit_air;		//是否能击中高空目标
	//int sutra_id;			//心法ID
	//int sutra_level;		//心法级别
	//int aim_mask;			//当前过程的判定针对组合条件
	//float normal_attack_sight;	//正常攻击视距
	//float valid_attack_sight;	//合法攻击视距
	//int inhibited_effect;           //克制效果类型
	//float inhibited_param;          //克制效果类型参数
	//float min_range;                //最小距离（非控制击退）
	//int combo;			//连招数(>1表示连招中)
	//int combo_mask;			//起承转合
	//bool combo_invalid;		//重复的连招数或特殊状态下发出的连招
	//bool target_point;		//目标点影响
	//bool first_damage;		//技能的第一次输出
	//uint64_t vtick;
	//float phy_attack_damage_scale;	//伤害公式: m
	//float mag_attack_damage_scale;	//伤害公式: n
	//float charge_time;		//蓄力多久
	//unsigned int target_mask;
	//unsigned char feedback_mask;	//0x01 是否需要反馈buff触发消息
	//uint64_t tick;			//消息产生的时间
	//float rage_add;			//攻击命中时自身增加怒气值
};

struct hurt_msg
{
	attacker_info_t attacker_info;	//攻击者的信息
	int64_t 	damage 	= 0.f; //伤害值
};

const char FEEDBACK_SOURCE_TWIN = 1;//双生体的反馈
const char FEEDBACK_SOURCE_MECH = 2;//机甲的反馈
const char FEEDBACK_SOURCE_REPLISOME = 3;//职业10分身的反馈

//(攻击引起的)反馈消息
struct feedback_msg
{
	int mask[FEEDBACK_SKILL_COUNT];//FEEDBACK_HIT/FEEDBACK_DAMAGE/...
	int action_id;			//攻击action id
	unsigned int seq;		//攻击序列号
	int hit_add;			//攻击命中时自身增加连击数
	int force_add;			//攻击命中时自身增加蓝
	int control_type;		//是否造成控制以及类型
	int control_time;		//造成控制时间
	XID id;					//反馈来源
	char source_type;		//双生体或机甲的反馈
	//uint64_t vtick;
	//float rage_add;			//攻击命中时自身增加怒气值
};

//检查收集到的反馈消息的消息
struct checkfeedback_msg
{
	unsigned int seq;		//攻击序列号
	int type[FEEDBACK_SKILL_COUNT];
	unsigned short skill[FEEDBACK_SKILL_COUNT];
	unsigned char target[FEEDBACK_SKILL_COUNT];
	char source_type;		//双生体或机甲的反馈
	//int skill_type;			//攻击技能类型
	//int xp_add;			//怒气增量
	//int weak_add_by_miss;		//攻击MISS时自身破绽额外增加值
	//int weak_add_by_parry;		//攻击被格挡时时自身破绽额外增加值
	//float rage_add;			//攻击命中时自身增加怒气值(基数)

	//uint64_t vtick;
	//bool grab1;			//抓投逻辑第1段, 对这种段不处理闪避破绽
};

//子物体计算环境(因为对子物体, 消息的填充和发送是异步的)
struct subobj_env
{
	XID prior_target;		//优先目标
	A3DVECTOR3 target_pos;		//选目标点技能的目标

	int max_coverage;
	float max_range;
	float affect_radius;
	float affect_radius2;
	float affect_length;
	float affect_angle;
	char skill_type;
	unsigned short feedback_type[FEEDBACK_SKILL_COUNT];//反馈类型
	unsigned short feedback_arg[FEEDBACK_SKILL_COUNT];//反馈参数
	unsigned short feedback_skill[FEEDBACK_SKILL_COUNT];//反馈技能
	unsigned short feedback_rate[FEEDBACK_SKILL_COUNT];//反馈概率
	unsigned char feedback_target[FEEDBACK_SKILL_COUNT];
	unsigned int seq;
	unsigned int seq_max;
};

//延时创建子物体需要的数据
struct create_subobj_delay_t
{
	tid_t tid;
	A3DVECTOR3 pos;
	A3DVECTOR3 direction;
	A3DVECTOR3 client_subobj_pos;//客户端想要生成子物体的位置
	float speed;
	subobj_env env;
	attack_msg attack;
	int life_mstime;
	bool has_pos;
	int subobj_ctype;
	int subobj_angle;
	float subobj_distance;
};

const char GUARD_CAST_SKILL = 1;//宠物释放技能
const char RETINUE_CAST_SKILL = 2;//伙伴释放技能
const char DRAGONBORN_CAST_SKILL = 3;//龙裔释放技能
const char SPIRIT_CAST_SKILL = 4;//精灵释放技能

const unsigned char MSG_HURT_MASK_IGNORE_PROP_DAM3 = 0x01;
const unsigned char MSG_HURT_MASK_IGNORE_INVINCIBLE = 0x02;

#endif

