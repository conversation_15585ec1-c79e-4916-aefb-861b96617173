#include <glog.h>
#include "printformat.h"
#include "gprotoc/gp_common_operation_re.pb.h"
#include "gprotoc/gp_common_operation.pb.h"
#include "gprotoc/data_RegionNotify.pb.h"
#include "gprotoc/data_ClientData.pb.h"
#include "gprotoc/data_ChessRegionNotify.pb.h"
#include "gprotoc/data_ParentHPMP.pb.h"


#include "creature.h"
#include "subobject.h"
#include <common/protocol_imp.h>
#include "skillcommon.h"
#include "action/action_customove.h"
#include "player.h"
#include "npc.h"
#include "combat_checker.h"
#include "property_manager.h"
#include "skill_config_man.h"
#include "pwsl_extend.h"
#include "conv_charset.h"
#include "global_config.h"

gcreature_imp::gcreature_imp() :  _cooldown_man(this), _action_man(this), _prop(this)/*, _es_man(this)*/, _heartbeat_counter(0),
	_can_enter_dying_state(false),/*_dying(false),*/_invinceble_except(false), _clock(NULL), _in_pre_levelup(false), _in_post_levelup(false), _in_levelup(false)
{
	memset(_visual_effect, 0, sizeof(_visual_effect));
	_timeout.SetOwner(this);
	_expire_item_date = 0;
	_ignore_max_hp2 = false;
	_init_can_be_hit_mask = HIT_MASK_BODY | HIT_MASK_FOOT;
	_notify_action = 0;
	_cg_block_action = false;
	_no_dot_counter = 0;
	_whoCastSkill = 0;
	_drag_skill_add_speed = .0;
	_speed_min = .0;
	_speed_max = .0;
	_hp_min_locked = 0;
}

gcreature_imp::~gcreature_imp()
{
	if (_clock)
	{
		delete _clock;
		_clock = NULL;
	}
	if (_p_summon_npc_map)
	{
		delete _p_summon_npc_map;
		_p_summon_npc_map = nullptr;
	}
}

void gcreature_imp::Init(gobject *parent, float long_dis_sight)
{
	gobject_imp::Init(parent, long_dis_sight);
	_filter_man.Init(this);
}

bool gcreature_imp::DoHeartbeat()
{
	_timeout.HeartbeatStep1();
	if (!OnHeartbeat())
	{
		return false;
	}
	if (_clock)
	{
		_clock->CheckEvent(gmatrix::GetInstance().GetSysTime());
	}
	_timeout.CheckAllTimeout(); //最后让超时器检查所有的超时对象
	_commander->OnHeartbeat();
	_buff.Update(this);		//更新Buff状态
	object_interface oif(this);
	_skill.OnHeartbeat(oif);
	++_heartbeat_counter;
	return true;
}

bool gcreature_imp::OnHeartbeat()
{
	/*
	#ifdef _TEST_TASK_LOAD_
	#ifndef _LOAD_DEBUG
		cpu_load_statistics::GetInstance().State(THT_TASK_LIST);
	#endif
	#endif
	*/
	_filter_man.Heartbeat(1);
	return true;
}

void gcreature_imp::PropertyUpdateAndNotify()
{
	//可以继续进行 如果需要，可以再增加时戳和其他信息
	//比如NPC的npc_state是 SPAWING的时候，就不应该计算了

	//这里还需要进行状态检查，可以引入一个状态，作为可以更新数ude标志                       $$$$$$$$$$$$$$$$$

	if (_prop.Update())
	{
		_prop.Constraint();

		if (GetProf6P())
		{
			//mp4恢复速度变化，更新mp4
			if (_prop.IsPropChanged(GPROP_INDEX(MP4Recover)))
			{
				GetProf6P()->OnMP4RecoverChange();
			}
		}
		if (GetProf6())
		{
			//生命变化后需要记录
			if (_prop.IsPropChanged(GPROP_INDEX(HP)))
			{
				GetProf6()->OnHPChange();
			}
		}

		//这里可以自主计算一些数值，比如广播数据等。但发给玩家的数据，则必须由玩家处理了。
		OnPropUpdate();

		GetParent()->base_info.fighting_capacity = GetFightingCapactiy();
		//之前的UpdateToParent逻辑
		property_template::data_ParentHPMP *pHPMP = & GetParent()->base_info.prop_hpmp;
		int rst = _prop.CompareAndUpdate(pHPMP);	//这里是不是直接BuildFrom就好了？
		if (rst)
		{
			if (_parent->object_state2 & gobject::STATE2_PLAYER_CHESS)
			{
				PB::data_ChessRegionNotify pb;
				property_template::data_ChessRegionNotify::Property2PB(*_prop.GetGProperty(), pb, [](int property_idx)
				{
					return true;
				});
				Runner()->object_notify_prop_delta_chess(GetParent()->ID, pb);
			}
			else
			{
				//如果有变化才继续 判断是否有需要广播的数据变化
				property_template::data_RegionNotify *pNotify = & GetParent()->base_info.prop_notify;
				char change_list[sizeof(*pNotify) / sizeof(int) + 1];
				memset(change_list, 0, sizeof(change_list));
				int cnt = _prop.CompareAndUpdate(pNotify, change_list);	//这里会收集变化的数据，并且如果数据变化（会比对是否和原来的值相同），如果的确发生了修改，则更新且设置change_list
				if (cnt > 0)
				{
					//存在着变化的数据, 发送广播协议
					PB::data_RegionNotify pb;
					property_template::data_RegionNotify::Property2PB(*_prop.GetGProperty(), pb, [change_list](int property_idx)
					{
						return change_list[property_template::data_RegionNotify::GetIndex(property_idx)];
					});
					Runner()->object_notify_prop_delta(GetParent()->ID, pb);

					//生命比率变化, 由于该变化十分频繁，不能立刻更新buff, 设置标记即可
					if (change_list[0] > 0)
					{
						HPPercentChange();
					}
				}
			}
		}

		//冷却缩减值变化, 更新cd
		if (_prop.IsPropChanged(GPROP_INDEX(curCDRedu)))
		{
			UpdateCDRedu();
		}

		//念力值变化, 更新多重打击概率
		if (_prop.IsPropChanged(GPROP_INDEX(curPsychokinesis)))
		{
			UpdateMultiAttack();
		}

		//清凉度或保暖度变化，重置温湿buff
		if (_prop.IsPropChanged(GPROP_INDEX(warm)) || _prop.IsPropChanged(GPROP_INDEX(cool)))
		{
			SendTo(GM_MSG_UPDATE_TEMP_HUMIDITY, GetParent()->ID, 0);
		}

		//饱食度变化，检查buff
		if (_prop.IsPropChanged(GPROP_INDEX(hungry)))
		{
			SendTo(GM_MSG_PROP_HUNGRY_CHANGED, GetParent()->ID, 0);
		}

		//愉悦度变化，检查buff
		if (_prop.IsPropChanged(GPROP_INDEX(happy)))
		{
			SendTo(GM_MSG_PROP_HAPPY_CHANGED, GetParent()->ID, 0);
		}

		// 冷却缩减增加属性
		if (_prop.IsPropChanged(GPROP_INDEX(curCDReduLevel)))
		{
			AddPropertyByCdReduLevel();
		}

		_prop.ClearChangedSet();
	}
}

void gcreature_imp::PropertyUpdateWithoutNotify()
{
	//本接口只应该由技能效果使用，
	//当技能效果修改某个属性后，实时修改其关联属性，不做其它额外操作。
	//不调用 _prop.ClearChangedSet()，
	//好在消息处理后执行完整的 PropertyUpdateAndNotify。
	if (_prop.Update())
	{
		_prop.Constraint();
	}
}

void gcreature_imp::DispatchMessage(const MSG& msg)
{
	gcreature *pCreature = GetParent();
	gobject_imp *imp = this;

	OnDispatchMessage(msg);

	//在OnDispatchMessage里imp可能被析构掉，所以必须在那个函数之前先保存，然后判断有效性
	if (pCreature->IsActive() && pCreature->imp == imp)
	{
		PropertyUpdateAndNotify();
	}
}

void gcreature_imp::MessageHandler(const MSG& msg)
{
	switch (msg.message)
	{
	case GM_MSG_ENCHANT:
	{
		ASSERT(msg.content_length >= sizeof(attack_msg));
		attack_msg& atk_msg = *(attack_msg *)msg.content;
		HandlerEnchantMsg(msg, atk_msg);
	}
	return;
	case GM_MSG_ENCHANT_SEQUENCE:
	{
		ASSERT(msg.content_length >= sizeof(attack_msg));
		attack_msg& atk_msg = *(attack_msg *)msg.content;

		//串行发送的消息，要把它传递给下一个目标
		int next_idx = atk_msg.sequence_idx + 1;
		bool send_next = next_idx < AM_SEQUENCE_XID_COUNT && atk_msg.sequence_xid[next_idx].IsValid();
		MSG *next_msg = NULL;
		if (send_next)
		{
			next_msg = DupeMessage(msg);//msg可能被HandlerEnchantMsg修改，先备份
		}
		int ret = HandlerEnchantMsg(msg, atk_msg);
		if (next_msg)
		{
			attack_msg& next_atk_msg = *(attack_msg *)next_msg->content;
			if (ret != 0)
			{
				//由于移动，死亡等等原因，未承受技能，把我从列表里抹去
				next_atk_msg.sequence_xid[next_idx - 1].id = INVALID_OBJECT_ID;
			}
			next_msg->target = next_atk_msg.sequence_xid[next_idx];
			next_atk_msg.sequence_idx = next_idx;
			gmatrix::GetInstance().SendMessage(*next_msg, next_atk_msg.sequence_tick);
			FreeMessage(next_msg);
		}
	}
	return;
	case GM_MSG_DIZZY_SELF:
	{
		BreakAction(true);
	}
	return ;
	case GM_MSG_ENCHANT_ZOMBIE:
	{
		if (_parent->CheckObjectState(gobject::STATE_INSTANCE_SPECTATE))
		{
			return;
		}
		if (!_parent->IsZombie())
		{
			return;
		}
		ASSERT(msg.content_length >= sizeof(attack_msg));
		//这里由于都要进行复制操作，有一定的耗费存在
		attack_msg& atk_msg = *(attack_msg *)msg.content;
		const XID& attacker = atk_msg.attacker_info.attacker;
		if (_invinceble_except && _invinceble_except_id != attacker)
		{
			return;
		}
		//处理一下到来的攻击消息
		_filter_man.EF_TranslateRecvAttack(msg.source, atk_msg);
		if (!TestEnchantRule(msg, atk_msg))
		{
			return;
		}
		HandleAttackMsg(msg, atk_msg);
	}
	return;

	case GM_MSG_OBJ_ZOMBIE_ACTION_END:
		if (!_parent->IsZombie())
		{
			return;
		}

	case GM_MSG_OBJ_ACTION_REPEAT:
	{
#ifdef GS_DEBUG
		if (debug::debug_ruid == Parent()->ID.id)
		{
			__PRINTF("DEBUG, GM_MSG_OBJ_ACTION_REPEAT, tick=" FMT_U64", action_id=%d\n", g_timer.get_tick(), msg.param);
		}
#endif
		if (_action_man.ActionValid(msg.param))
		{
			_action_man.ContinueAction();
		}
	}
	return;
	case GM_MSG_OBJ_ACTION_CHARGE_FINISH:
	{

#ifdef GS_DEBUG
		if (debug::debug_ruid == Parent()->ID.id)
		{
			__PRINTF("DEBUG, GM_MSG_OBJ_ACTION_CHARGE_FINISH, tick=" FMT_U64", action_id=%d\n", g_timer.get_tick(), msg.param);
		}
#endif
		if (_action_man.ActionValid(msg.param))
		{
			_action_man.ActionChargeFinish();
		}

	}
	return;

	/*case GM_MSG_ATTACK_LOOP:
	{
		if (_attack._attack_loop._timestamp == msg.param)
		{
			_attack._attack_loop.Continue(this);
		}
	}
	return;*/

	case GM_MSG_HEARTBEAT:
	{
		if (!DoHeartbeat())
		{
			return;
		}
	}
	break;

	case GM_MSG_SUBSCIBE_TARGET:
	{
		ASSERT(msg.source.IsPlayer());
		_buff.InsertSubscibe(this, msg.source, *(link_id_t *)msg.content);
		SendTo(GM_MSG_SUBSCIBE_CONFIRM, msg.source, 0);
	}
	return;

	case GM_MSG_UNSUBSCIBE_TARGET:
	{
		ASSERT(msg.source.IsPlayer());
		_buff.RemoveSubscibe(msg.source);
	}
	return;

	case GM_MSG_EXTERN_HEAL:
	{
		if (_parent->CheckObjectState(gobject::STATE_INSTANCE_SPECTATE))
		{
			return;
		}
		if (!_parent->IsZombie() && msg.param > 0)
		{
			IncHP(msg.param);
		}
	}
	return;

	case GM_MSG_QUERY_DEFINITE_INFO:
	{
		link_id_t lid;
		lid.index = msg.param;
		lid.sid = msg.param2;
		_runner->other_get_definite_info(lid);
	}
	return;

	case GM_MSG_HURT:
	{
		if (_parent->CheckObjectState(gobject::STATE_INSTANCE_SPECTATE))
		{
			return;
		}
		if (!_parent->IsZombie() && msg.content_length == sizeof(hurt_msg) )
		{
			int skill_id = msg.param;
			bool ignore_prop_dam3 = msg.param2 & MSG_HURT_MASK_IGNORE_PROP_DAM3;
			bool ignore_invincible = msg.param2 & MSG_HURT_MASK_IGNORE_INVINCIBLE;
			const hurt_msg& h_msg = *(const hurt_msg *)msg.content;
			BeHurt(msg.source, h_msg.attacker_info, h_msg.damage, skill_id, ignore_invincible, ignore_prop_dam3);
		}
	}
	return;

	case GM_MSG_DUEL_HURT:
	{
		if (_parent->CheckObjectState(gobject::STATE_INSTANCE_SPECTATE))
		{
			return;
		}
		if (!_parent->IsZombie())
		{
			object_state_t state = ((gcreature *)_parent)->object_state;
			if (state & gobject::STATE_IN_DUEL_MODE)
			{
				int damage = msg.param;
				DoDamage(damage, NULL, false);
				attacker_info_t info;
				memset(&info, 0, sizeof(info));
				info.attacker_mode = attacker_info_t::AM_PVP_DUEL;
				if (_prop.GetHP() <= 0)
				{
					Die(msg.source, &info);
				}
			}
		}
	}
	return;

	case GM_MSG_DEATH:
	{
		if (_parent->CheckObjectState(gobject::STATE_INSTANCE_SPECTATE))
		{
			return;
		}
		if (_parent->IsZombie())
		{
			return;
		}
		_prop.DecHP(0, _prop.GetHP(), true);
		Die(msg.source, NULL, 0, 0, msg.param);
	}
	return;

	case GM_MSG_ADD_BUFF:
	{
		int skill_id = msg.param;
		int skill_level = msg.param2;
		if (skill_id && skill_level)
		{
			object_interface oif(this);
			_skill.CastRune(skill_id, skill_level, -1, oif);
		}
	}
	return;

	case GM_MSG_ADD_MP:
	{
		if (msg.param > 0)
		{
			IncMP(msg.param);
		}
	}
	return;

	case GM_MSG_SYMBIOTIC_CHECK:
	{
		if (msg.param == 1)//检查a
		{
			if (!_skill.CheckSymbioticA(msg.source.newtype))
			{
				SendTo2(GM_MSG_SYMBIOTIC_CHECK, msg.source, 3, msg.param2);
			}
		}
		else if (msg.param == 2)//检查b
		{
			if (!_skill.CheckSymbioticB(msg.source.newtype))
			{
				SendTo2(GM_MSG_SYMBIOTIC_CHECK, msg.source, 3, msg.param2);
			}
		}
		else if (msg.param == 3)//返回检查失败
		{
			//玩家身上的buff可能有多个，该buff必须是个人buff, 才能正确驱掉(不把别人加的目标buff驱掉)
			_filter_man.RemoveFilter(msg.param2, msg.source.id, filter::ER_COEXIST);
		}
	}
	return;

	case GM_MSG_ATTACK_FEED_BACK:
	{
		OnAttackFeedBack(msg.source);
		ASSERT(msg.content_length >= sizeof(feedback_msg));
		const feedback_msg *p = (const feedback_msg *)msg.content;
		if (p->mask[0] & WMSKILL::FEEDBACK_HIT)
		{
			_filter_man.EF_AttackEnemy(msg.source);
			if (p->mask[0] & WMSKILL::FEEDBACK_TAR_HIT)
			{
				_filter_man.EF_AttackTarget(msg.source);
			}
			if (p->mask[0] & WMSKILL::FEEDBACK_CRIT)
			{
				_filter_man.EF_CritEnemy(msg.source);
			}
		}
		if (p->mask[0] & WMSKILL::FEEDBACK_DAMAGE)
		{
			_filter_man.EF_DamageEnemy(msg.source);
			OnDamageEnemy();
		}
		if (p->mask[0] & WMSKILL::FEEDBACK_KILL_PLAYER)
		{
			_filter_man.EF_KillEnemy(msg.source, msg.param, false);    //杀死npc
		}
		if (p->mask[0] & WMSKILL::FEEDBACK_KILL_NPC)
		{
			_filter_man.EF_KillEnemy(msg.source, msg.param, true);
		}
		if (p->mask[0] & WMSKILL::FEEDBACK_MISS)
		{
			_filter_man.EF_EnemyDodge(msg.source);
		}
		if (p->mask[0] & WMSKILL::FEEDBACK_ZHUATOU)
		{
			GetSkill().GrabEnemy(msg.source);
		}
		object_interface oif(this);
		GetSkill().NewAttackFeedback(oif, msg.source, *p);
	}
	return;

	case GM_MSG_CHECK_FEEDBACK:
	{
		ASSERT(msg.content_length >= sizeof(checkfeedback_msg));
		const checkfeedback_msg *p = (const checkfeedback_msg *)msg.content;
		object_interface oif(this);
		GetSkill().CheckAttackFeedback(oif, *p);
	}
	return;

	case GM_MSG_MOVE_ATTACKER:
	{
		struct data
		{
			A3DVECTOR3 pos;
			uint64_t tick;
		};
		ASSERT(msg.content_length >= sizeof(data));
		const data *p = (const data *)msg.content;
		_skill.MoveAttacker(p->pos, p->tick);
	}
	return;

	case GM_MSG_GROUP_MAN_OPERATION:
	case GM_MSG_GROUP_OBJECT_OPERATION:
		return;

	case GM_MSG_DELAY_REMOVE_FILTER:
	{
		_filter_man.RemoveFilter(msg.param);
	}
	return;

	case GM_MSG_DISPEL_FILTER_TYPEMASK:
	{
		_filter_man.ClearSpecFilter2((filter_typemask_t)msg.param, msg.param2);
	}
	return;

	case GM_MSG_FILTER_ON_TIMER:
	{
		_filter_man.FilterOnTimer(msg.param, msg.param2, msg.param3);
	}
	return;

	case GM_MSG_SUBOBJECT_DISAPPEAR:
	{
		if (_subobject_map.find(msg.param) == _subobject_map.end())
		{
			return;
		}
		std::list<xid_time_t>& list = _subobject_map[msg.param];
		std::list<xid_time_t>::iterator it = list.begin();
		for (; it != list.end(); ++it)
		{
			if (it->xid == msg.source)
			{
				list.erase(it);
				const subobject_template *pTemplate = subobject_template_manager::GetInstance().Get(msg.param);
				ASSERT(pTemplate);
				if (SUBOBJEFFCTTYPE_REMOTECTRL == pTemplate->judge_effectmode_type)
				{
					object_interface oif(this);
					_skill.RemoteCtrlSubobjectDisappeared(oif, msg.param2);
				}
				return;
			}
		}
	}
	return;

	case GM_MSG_CREATE_SUBOBJECT:
	{
		ASSERT(msg.content_length >= sizeof(create_subobj_delay_t));
		const create_subobj_delay_t *pd = (const create_subobj_delay_t *)msg.content;
		if (pd->has_pos)
		{
			CreateSubObject(pd->tid, pd->pos, pd->direction, pd->speed, pd->env, pd->attack, pd->life_mstime);
		}
		else
		{
			WMSKILL::calc_subobj_create_pos_t args(Parent()->pos, Parent()->direction, pd->client_subobj_pos);
			args.ctype = pd->subobj_ctype;
			args.angle = pd->subobj_angle;
			args.distance = pd->subobj_distance;
			WMSKILL::get_pos_fun_t get_grabbed_pos = [](A3DVECTOR3 & pos)->bool
			{
				return false;
			};
			const XID& target_id = pd->env.prior_target;
			WMSKILL::get_pos_fun_t get_target_pos = [this, &target_id](A3DVECTOR3 & pos)->bool
			{
				object_info info;
				if (!gmatrix::GetInstance().QueryObject(target_id, info))
				{
					return false;
				}
				if (info.CheckRange(this->Parent(), 2500))//50的平方，目标离我50米内才能生成子物体
				{
					pos = info.pos;
					return true;
				}
				return false;
			};

			A3DVECTOR3 pos, dir;
			if (WMSKILL::CalcSubobjCreatePosDir(args, get_grabbed_pos, get_target_pos, pos, dir))
			{
				CreateSubObject(pd->tid, pos, dir, pd->speed, pd->env, pd->attack, pd->life_mstime);
			}
		}
	}
	return;

	case GM_MSG_EXACT_STATUS_TIMEOUT:
	{
		//_es_man.RemoveStatus(msg.param);
		object_interface oif(this);
		//if (msg.param == ESI_COMBO)
		//	_skill.ComboTimerTimeout(oif);
		//if (msg.param == ESI_HIT)
		//	_skill.HitTimerTimeout(oif);
		//else if (msg.param == ESI_SUSPEND)
		//	_skill.SuspendTimerTimeout(oif);
		/*if (msg.param == ESI_CHUPOZHAN)
		{
			_skill.OnChuPozhanTimeout(oif);
		}*/
	}
	return;

	case GM_MSG_BE_STARED_BY_AI:
	{
		//if (msg.param)
		//	_skill.BeStaredByAI(msg.source);
		//else
		//	_skill.StopStare(msg.source);
	}
	return;

	case GM_MSG_SYSTEM_SPEAK:
	{
		//param:speak_id, param2:next_delay 因为每次delay的tick必须小于MAX_MESSAGE_LATENCY，所以有时需要接力
		//__PRINTF("GM_MSG_SYSTEM_SPEAK param=%d,param2=%d\n",msg.param,msg.param2);
		if (msg.param > 0)
		{
			if (msg.param2)
			{
				int delay_tick = msg.param2;
				int next_delay = 0;
				if (delay_tick >= MAX_MESSAGE_LATENCY_TICK)
				{
					next_delay = delay_tick - MAX_MESSAGE_LATENCY_TICK + 1;
					delay_tick = MAX_MESSAGE_LATENCY_TICK - 1;
				}
				gmatrix::GetInstance().LazySendTo2(GM_MSG_SYSTEM_SPEAK, msg.source, _parent->ID, msg.pos, msg.param, next_delay, delay_tick);
			}
			else
			{
				SystemSpeak(msg.param, msg.source);
			}
		}
	}
	return;

	case GM_MSG_STRENGTH_EXHAUST:
	{
		object_interface oif(this);
		_skill.OnStaminaExhaust(oif);
	}
	return;

	case GM_MSG_GET_EXTEND_DATA:
	{
		link_id_t lid;
		lid.index = msg.param;
		lid.sid = msg.param2;
		unsigned short flag = msg.param3;
		unsigned char mask = msg.param4;
		if (horizontal_distance(msg.pos, _parent->pos) <= GET_EQUIP_INFO_DIS * GET_EQUIP_INFO_DIS)
		{
			_runner->player_extend_data(flag, mask, lid, _equip_info.mask, _equip_info.data.begin(), _equip_info.data.size());
		}
	}
	return;

	case GM_MSG_FAST_REGEN_HP:
	{
		//快速回复血量至“气血快速回复上限”的逻辑，每秒回复 20%， 每秒回复10次（暂定，这些参数都应参数化）
		//如果当前血量没有回满，则进行回复逻辑。 如果当前存在判断快速回复的定时器，表示又进入受伤状态，则不继续回复了
		//
		if (GetRealSceneImp()->IsAllowFastHPGen())
		{
			if (!_parent->IsZombie() && !IsCombatState() && GetHP() < _prop.GetHPMax() /*&& !_timeout.CheckExist(TM_IDX_FAST_REGEN_HP)*/)
			{
				float regen_period = HP_MP_REGEN_CONFIG.hp_regen_ticktime;	//多少秒回复一次
				int64_t hp_inc = _prop.GetHPMax();
				if (IsPubg())
				{
					hp_inc *= pubg_config::GetInstance().GetHPReservedGenRate() * regen_period;
				}
				else
				{
					hp_inc *= HP_MP_REGEN_CONFIG.hp_regen_rate * regen_period;
				}

				int64_t cur_hp = GetHP();
				if (hp_inc + cur_hp > _prop.GetHPMax())
				{
					hp_inc = _prop.GetHPMax() - cur_hp;
				}
				hp_inc = (hp_inc > _prop.GetHPReserved()) ? _prop.GetHPReserved() : hp_inc;

				if (hp_inc <= 0)
				{
					GetParent()->ClrObjectServerState(gobject::SERVER_STATE_FAST_REGEN_HP);
					return;
				}
				_prop.SetHP(cur_hp + hp_inc);
				_prop.MODIFY_PROP_BY_NAME(HPReserved, -hp_inc, creature_prop::CPM_CUR);

				attacker_info_t info;
				info.attacker.Clear();
				BeCure(info.attacker, info, hp_inc, 0);

				if (_prop.GetHP() < _prop.GetHPMax() && _prop.GetHPReserved())
				{
					LazySendTo(GM_MSG_FAST_REGEN_HP, _parent->ID, 0, SECOND_TO_TICK(regen_period));
					return;
				}
			}
			GetParent()->ClrObjectServerState(gobject::SERVER_STATE_FAST_REGEN_HP);
		}
	}
	return;


	case GM_MSG_SKILL_PERSIST_MOVE:
	{
		//__PRINTF("GM_MSG_SKILL_PERSIST_MOVE\n");
		bool cont = false;
		bool brk_act = false;
		if (_action_man.OnPersistMove(cont, brk_act))
		{
			if (cont)
			{
				LazySendTo(GM_MSG_SKILL_PERSIST_MOVE, Parent()->ID, msg.param, msg.param);
			}
			if (brk_act)
			{
				BreakAction(false);
			}
		}
		else if (_filter_man.OnPersistMove(cont))
		{
			if (cont)
			{
				LazySendTo(GM_MSG_SKILL_PERSIST_MOVE, Parent()->ID, msg.param, msg.param);
			}
		}
	}
	return;

	case GM_MSG_PLAYER_CUSTOM_MOVE:
	{
		//场景脚本让对象执行自主移动，目前应该用于玩家和名人，暂时忽略了控制逻辑
		//$$$$$$$$ TODO 死亡怎么办？ 是否应该处理？
		if (msg.content_length == 0 || msg.content_length % sizeof(ai_action_custom::ACTION) != 0)
		{
			break;
		}

		bool is_offset = msg.param;
		bool is_overwhelming = msg.param2;
		int count = msg.content_length / sizeof(ai_action_custom::ACTION);
		ai_action_custom::ACTION   *pList = (ai_action_custom::ACTION * )msg.content;

		ClearAction();
		ai_action_custom *pAction = new ai_action_custom(this);
		pAction->SetOffsetMode(is_offset);
		pAction->SetOverwhelming(is_overwhelming);
		for (int i = 0; i < count; i ++)
		{
			pAction->AddAction(pList[i]);
		}
		StartAction(pAction);
	}
	return;

	case GM_MSG_SUBSCRIBE_ACTION:
	{
		_notify_action = 0;
		if (msg.param)
		{
			_notify_action |= 1;
		}
		if (msg.param2)
		{
			_notify_action |= 2;
		}
	}
	return;

	case GM_MSG_BATTLE_LOCK_PLAYER:
	{
		__PRINTF("creature:%ld , GM_MSG_BATTLE_LOCK_PLAYER, time_lock:%d only_overwhelming:%d\n",
		         Parent()->ID.id, msg.param, msg.param2);
		if (GetWorldImp()->Parent()->xid != msg.source)
		{
			return;
		}
		//停止攻击
		if (HasAction())
		{
			CancelAction(true, 0);
		}
		BattleLock(msg.param, (msg.param2 != 0) ? true : false);
	}
	return;

	case GM_MSG_SET_OBJECT_DIR:
	{
		__PRINTF("creature:%ld , GM_MSG_SET_OBJECT_DIR dir=%d\n", Parent()->ID.id, msg.param);

		_runner->object_turn(GP_TURN_STOP, msg.param);
		_runner->self_turn(GP_TURN_STOP, msg.param);	//object_turn不给自己发，这里补一个
		GetParent()->SetDirection(msg.param);
	}
	return;

	case GM_MSG_PROP_SUBSCRIBE_TARGET:
	{
		ASSERT(msg.source.IsPlayer());
		_buff.InsertPropSubscribe(this, msg.source, *(link_id_t *)msg.content);
		SendTo(GM_MSG_PROP_SUBSCRIBE_CONFIRM, msg.source, 0);
	}
	return;

	case GM_MSG_PROP_UNSUBSCRIBE_TARGET:
	{
		ASSERT(msg.source.IsPlayer());
		_buff.RemovePropSubscribe(msg.source);
	}
	return;

	case GM_MSG_SILENCE_TARGET:
	{
		SetSilentAbsoluteSeal((bool)msg.param);
	}
	return;

	case GM_MSG_JUMP_FARRHEST_SUBOBJECT_POS:
	{
		Jump2FarthestSubobjPos(msg.param, (bool)msg.param2);
	}
	return;

	case GM_MSG_SUMMON_MONSTER_EXHAUST:
	{
		XID summon_help_monster = msg.source;
		if (!_p_summon_npc_map)
		{
			return;
		}
		auto& summon_npc_map = *_p_summon_npc_map;
		auto it = summon_npc_map.find(summon_help_monster);
		if (it != summon_npc_map.end())
		{
			summon_npc_map.erase(it);
		}
	}
	return;

	case GM_MSG_CHALLENGE_TARGET_ADD:
	{
		GetSkill().AddChallengeTarget(msg.source.id, msg.param, msg.param2);
		//LOG_TRACE("GM_MSG_CHALLENGE_TARGET_ADD:roleid=%ld,targetid=%ld", GetRoleID(), msg.source.id);
	}
	return;

	case GM_MSG_CHALLENGE_TARGET_REMOVE:
	{
		GetSkill().RemoveChallengeTarget(msg.source.id);
		//LOG_TRACE("GM_MSG_CHALLENGE_TARGET_REMOVE:roleid=%ld,targetid=%ld", GetRoleID(), msg.source.id);

	}
	return;

	case GM_MSG_CHALLENGE_TARGET_HEARTBEAT:
	{
		GetSkill().RemoveChallengeTarget(msg.source.id);
		GetSkill().AddChallengeTarget(msg.source.id, msg.param, msg.param2);
		//LOG_TRACE("GM_MSG_CHALLENGE_TARGET_HEARTBEAT:roleid=%ld,targetid=%ld", GetRoleID(), msg.source.id);
	}
	return;

	default:
		break;
	}
	gobject_imp::MessageHandler(msg);
}

void gcreature_imp::FillAttackMsg(const XID& target, attack_msg& attack)
{
	memset(&attack, 0, sizeof(attack));

	attack.attacker_info.attacker = _parent->ID;
	attack.attacker_info.level = _prop.GetLevel();
	attack.attacker_info.prof_level = _prop.GetProfLevel();
	attack.attacker_info.eff_level = _prop.GetLevel();
	attack.attacker_info.team.Clear();
	attack.attacker_info.faction = GetFaction();
	attack.attacker_info.native_zoneid = ((gcreature *)Parent())->native_zoneid;
	attack.attacker_info.enemy_faction = GetEnemyFaction();
	attack.attacker_info.friend_faction = GetFriendFaction();
	attack.attacker_info.pos = Parent()->pos;
	attack.attacker_info.body_size = Parent()->body_size;
	attack.attacker_info.dir = Parent()->direction;
	attack.attacker_info.pet_master.Clear();
	attack.attacker_info.cur_target = _cur_target;
	attack.attacker_info.extend_faction = Parent()->extend_faction;
	attack.attacker_info.is_male = !GetParent()->IsFemale();
	GetWorldImp()->SetAttackMode(this, target, attack);

	attack.attack_physic = _prop.GetGProperty()->PROP_NAME_GET(curPhyAtk);
	attack.attack_magic = _prop.GetGProperty()->PROP_NAME_GET(curMagAtk);
	attack.attack_crit = _prop.GetGProperty()->PROP_NAME_GET(curCrit);
	attack.attack_crit_ratio = _prop.GetGProperty()->PROP_NAME_GET(curCritRatio);
	attack.attack_pierce = _prop.GetGProperty()->PROP_NAME_GET(curPierce);
	attack.attack_pierce_inc = _prop.GetGProperty()->PROP_NAME_GET(curPierceInc);
	attack.attack_stun = _prop.GetGProperty()->PROP_NAME_GET(curStun);
	attack.attack_hit = _prop.GetGProperty()->PROP_NAME_GET(curHit);
	attack.attack_hit_add = _prop.GetGProperty()->PROP_NAME_GET(curHitExtraRatio);
	attack.pvp_level = _prop.GetGProperty()->PROP_NAME_GET(curPVPLevel);
	attack.attack_npc_damage_add = _prop.GetGProperty()->PROP_NAME_GET(curNpcDamAdd);
	attack.attack_deadly_damage_add = _prop.GetGProperty()->PROP_NAME_GET(curDeadlyAtk);
	attack.heir_dam_add = _prop.GetGProperty()->PROP_NAME_GET(heirDamAdd);
	attack.pvp_dam_add = _prop.GetGProperty()->PROP_NAME_GET(pvpDamAdd);
	attack.pvp_dam_add2 = _prop.GetGProperty()->PROP_NAME_GET(pvpDamAdd2);
	attack.attack_seven_crime_add = _prop.GetGProperty()->PROP_NAME_GET(curSevenCrimeAdd);
	attack.attack_damage_add3 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd3);
	attack.pve_dam_fix = _prop.GetGProperty()->PROP_NAME_GET(curPveDamageFix);
	attack.pvp_dam_fix = _prop.GetGProperty()->PROP_NAME_GET(curPvpDamageFix);
	attack.attack_versatility_rating = _prop.GetGProperty()->PROP_NAME_GET(curVersatilityRating);

	float _ignore_damage_prop_ratio   =  GetSkill().GetIgnoreAddDamagePropRatio();

	attack.attack_damage_add = _prop.GetGProperty()->PROP_NAME_GET(DamAdd) * _ignore_damage_prop_ratio;
	attack.attack_damage_add2 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd2) * _ignore_damage_prop_ratio;
	attack.attack_damage_add4 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd4) * _ignore_damage_prop_ratio;
	attack.attack_damage_add5 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd5) * _ignore_damage_prop_ratio;
	attack.attack_damage_add6 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd6) * _ignore_damage_prop_ratio;
	attack.attack_damage_add7 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd7) * _ignore_damage_prop_ratio;
	attack.attack_damage_add8 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd8) * _ignore_damage_prop_ratio;
	attack.attack_damage_add9 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd9) * _ignore_damage_prop_ratio;
	attack.attack_damage_add10 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd10) * _ignore_damage_prop_ratio;
	attack.attack_damage_add11 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd11) * _ignore_damage_prop_ratio;
	attack.attack_damage_add13 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd13) * _ignore_damage_prop_ratio;
	attack.attack_damage_add12 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd12) * _ignore_damage_prop_ratio;
	attack.attack_damage_add14 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd14) * _ignore_damage_prop_ratio;
	attack.attack_damage_add15 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd15) * _ignore_damage_prop_ratio;
	attack.attack_damage_add16 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd16) * _ignore_damage_prop_ratio;
	attack.attack_damage_add17 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd17) * _ignore_damage_prop_ratio;
	attack.attack_damage_add18 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd18) * _ignore_damage_prop_ratio;
	attack.attack_damage_add19 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd19) * _ignore_damage_prop_ratio;
	attack.attack_damage_add20 = _prop.GetGProperty()->PROP_NAME_GET(DamAdd20) * _ignore_damage_prop_ratio;
	attack.attack_physic_damage_add = _prop.GetGProperty()->PROP_NAME_GET(PhyDamAdd) * _ignore_damage_prop_ratio;
	attack.attack_magic_damage_add = _prop.GetGProperty()->PROP_NAME_GET(MagDamAdd) * _ignore_damage_prop_ratio;
	attack.attack_opposite_sex_damage_add = _prop.GetGProperty()->PROP_NAME_GET(curOppositeSexDamAdd) * _ignore_damage_prop_ratio;
	attack.attack_same_sex_damage_add = _prop.GetGProperty()->PROP_NAME_GET(curSameSexDamAdd) * _ignore_damage_prop_ratio;

	attack.special_attack_damage_add = _prop.GetGProperty()->PROP_NAME_GET(SpecialDamAdd);

	if (_whoCastSkill > 0 
		&& _whoCastSkill != SPIRIT_CAST_SKILL //精灵不走FillPartnerAttackMsg，而是继续走后续逻辑
		)
	{

		//填充召唤物属性
		FillPartnerAttackMsg(_whoCastSkill, target, attack);
		return;
	}

	attack.base_attack_physic = attack.attack_physic;
	attack.base_attack_magic = attack.attack_magic;
	//宠物&伙伴没有元素伤害
	attack.attack_element_type = _prop.GetGProperty()->PROP_NAME_GET(mainElementary);
	attack.attack_element_damage[0] = _prop.GetGProperty()->PROP_NAME_GET(curElementalDmg1);
	attack.attack_element_damage[1] = _prop.GetGProperty()->PROP_NAME_GET(curElementalDmg2);
	attack.attack_element_damage[2] = _prop.GetGProperty()->PROP_NAME_GET(curElementalDmg3);
	attack.attack_element_damage[3] = _prop.GetGProperty()->PROP_NAME_GET(curElementalDmg4);

	attack.attack_element_damage_add[0] = _prop.GetGProperty()->PROP_NAME_GET(ElementalDamAdd1) * _ignore_damage_prop_ratio;
	attack.attack_element_damage_add[1] = _prop.GetGProperty()->PROP_NAME_GET(ElementalDamAdd2) * _ignore_damage_prop_ratio;
	attack.attack_element_damage_add[2] = _prop.GetGProperty()->PROP_NAME_GET(ElementalDamAdd3) * _ignore_damage_prop_ratio;
	attack.attack_element_damage_add[3] = _prop.GetGProperty()->PROP_NAME_GET(ElementalDamAdd4) * _ignore_damage_prop_ratio;

	attack.replisome_dam_ratio = GetSkill().GetReplisomeDamageRatio();

	attack.attacker_info.roam_communityid = GetParent()->roam_communityid;
	//LOG_TRACE("gcreature_imp::FillAttackMsg roleid=%ld, rc_id=%ld", Parent()->ID.id, GetParent()->roam_communityid);
}

bool gcreature_imp::TestCoolDown(unsigned short cd_id) const
{
	return _cooldown_man.TestCoolDown(cd_id);
}

int gcreature_imp::TestCoolDownCount(unsigned short cd_id) const
{
	return _cooldown_man.TestCoolDown(cd_id);
}

bool gcreature_imp::SetCoolDown(unsigned short cd_id, size_t cd_time)
{
	if (cd_id >= COOLDOWN_SKILL_START && SkillConfig::GetInstance().IsInCdAddList(cd_id))
	{
		cd_time += GetSkillCDAdd();
	}
	return _cooldown_man.SetCoolDown(cd_id, cd_time);
}

bool gcreature_imp::SetCoolDownByCount(unsigned short cd_id, size_t cd_time)
{
	return _cooldown_man.SetCoolDown(cd_id, cd_time);
}

bool gcreature_imp::CostCoolDown(unsigned short cd_id, size_t cd_time)
{
	return _cooldown_man.CostCoolDown(cd_id, cd_time);
}

bool gcreature_imp::AddCoolDown(unsigned short cd_id, size_t cd_time)
{
	return _cooldown_man.AddCoolDown(cd_id, cd_time);
}

void gcreature_imp::SetDelayBoomProess(int process)
{
	GetParent()->delay_boom_process = process;
	PB::gp_common_operation_re pb;
	pb.set_rid(GetParent()->ID.id);
	pb.set_error_code(S2C::ERR_SUCCESS);
	pb.set_op(PB::gp_common_operation::DELAY_BOOM);
	pb.set_param(process);
	Runner()->RegionSend<S2C::CMD::PBS2C>(pb);
}

void gcreature_imp::SetDelayAttackProess(int process)
{
	GetParent()->delay_attack_tm = process;
	PB::gp_common_operation_re pb;
	pb.set_rid(GetParent()->ID.id);
	pb.set_error_code(S2C::ERR_SUCCESS);
	pb.set_op(PB::gp_common_operation::DELAY_ATTACK);
	pb.set_param(process);
	Runner()->RegionSend<S2C::CMD::PBS2C>(pb);
}

bool gcreature_imp::ClrCoolDown(unsigned short cd_id)
{
	return _cooldown_man.ClrCoolDown(cd_id);
}

void gcreature_imp::ClearAllCoolDown()
{
	_cooldown_man.ClearExpiredCoolDown();
	_cooldown_man.ClearAllCoolDown(this);
}

bool gcreature_imp::CreateSubObject(tid_t tid, const A3DVECTOR3& pos, const A3DVECTOR3& direction, float speed, const subobj_env& env, const attack_msg& attack, int life_mstime)
{
	const subobject_template *pTemplate = subobject_template_manager::GetInstance().Get(tid);
	if (!pTemplate)
	{
		return false;
	}
	switch (pTemplate->limit_type)
	{
	case SUBOBJLIMITTYPE_NONE:
	default:
		break;

	case SUBOBJLIMITTYPE_REACHMAX_NOTGEN:
	{
		SUBOBJECT_MAP::iterator it = _subobject_map.find(tid);
		if (it == _subobject_map.end())
		{
			break;
		}
		std::list<xid_time_t>& list = it->second;
		if (list.size() <= 0)
		{
			break;
		}
		if (list.size() < pTemplate->limit_count)
		{
			break;
		}
		//已经满了,校验下时间
		if ((gmatrix::GetInstance().GetSysTime() - list.begin()->timestamp) * 1000 < (int)MAX_SUBOBJECT_LIVING_MSTIME)
		{
			return false;
		}
		//去掉第一个
		list.erase(list.begin());
	}
	break;

	case SUBOBJLIMITTYPE_REACHMAX_REMOVEFIRST:
	{
		SUBOBJECT_MAP::iterator it = _subobject_map.find(tid);
		if (it == _subobject_map.end())
		{
			break;
		}
		std::list<xid_time_t>& list = it->second;
		if (list.size() <= 0)
		{
			break;
		}
		if (list.size() < pTemplate->limit_count)
		{
			break;
		}
		//已经满了,通知消失
		SendTo(GM_MSG_DISAPPEAR, list.begin()->xid, 0);
		//去掉第一个
		list.erase(list.begin());
	}
	break;
	}
	int weapon_tid;
	int weapon_mid;
	unsigned char star;
	GetWeapon(weapon_tid, weapon_mid, star);
	gsubobject *pSubobject = GetSceneImp()->CreateSubobject(tid, pos, direction, speed, env, attack, life_mstime, weapon_tid, weapon_mid, star);
	__PRINTF("创建子物体%p\n", pSubobject);
	if (!pSubobject)
	{
		GLog::log(LOG_DEBUG, "创建子物体失败,tid=%d\n", tid);
		return false;
	}
	xid_time_t xtt;
	xtt.xid = pSubobject->ID;
	xtt.timestamp = gmatrix::GetInstance().GetSysTime();
	_subobject_map[tid].push_back(xtt);
	return true;
}

void gcreature_imp::ClearRemoteCtrlSubobject(int param)
{
	for (SUBOBJECT_MAP::iterator it = _subobject_map.begin(); it != _subobject_map.end(); ++it)
	{
		int tid = it->first;
		const subobject_template *pTemplate = subobject_template_manager::GetInstance().Get(tid);
		ASSERT(pTemplate);
		if (SUBOBJEFFCTTYPE_REMOTECTRL != pTemplate->judge_effectmode_type)
		{
			continue;
		}
		std::list<xid_time_t>& list = it->second;
		for (std::list<xid_time_t>::iterator lit = list.begin(); lit != list.end(); ++ lit)
		{
			SendTo(GM_MSG_DISAPPEAR, lit->xid, param);
		}
	}
}

void gcreature_imp::TriggerRemoteCtrlSubObject(const subobj_env& env, const attack_msg& attack)
{
	for (SUBOBJECT_MAP::iterator it = _subobject_map.begin(); it != _subobject_map.end(); ++it)
	{
		int tid = it->first;
		const subobject_template *pTemplate = subobject_template_manager::GetInstance().Get(tid);
		ASSERT(pTemplate);
		if (SUBOBJEFFCTTYPE_REMOTECTRL != pTemplate->judge_effectmode_type)
		{
			continue;
		}
		std::list<xid_time_t>& list = it->second;
		for (std::list<xid_time_t>::iterator lit = list.begin(); lit != list.end(); ++ lit)
		{
			struct
			{
				subobj_env env;
				attack_msg attack;
				//XID target;
				//A3DVECTOR3 target_pos;
			} data;
			data.env = env;
			data.attack = attack;
			//data.target = target;
			//data.target_pos = target_pos;
			SendTo(GM_MSG_TRIGGER_SUBOBJECT, lit->xid, 0, &data, sizeof(data));
		}
	}
}

XID gcreature_imp::GetRemoteCtrlSubObjectID()
{
	for (SUBOBJECT_MAP::iterator it = _subobject_map.begin(); it != _subobject_map.end(); ++it)
	{
		int tid = it->first;
		const subobject_template *pTemplate = subobject_template_manager::GetInstance().Get(tid);
		ASSERT(pTemplate);
		if (SUBOBJEFFCTTYPE_REMOTECTRL != pTemplate->judge_effectmode_type)
		{
			continue;
		}
		std::list<xid_time_t>& list = it->second;
		if (list.size() > 0)
		{
			return list.begin()->xid;    //返回第一个id, 策划保证同时只能有一个遥控子物体
		}
	}
	return XID();
}

void gcreature_imp::SelectTarget(const XID& target, bool is_client_cmd)
{
	if (_cur_target == target)
	{
		return;
	}
	_cur_target = target;
	gcreature *pObj = (gcreature *)_parent;
	pObj->cur_target = _cur_target;
	Runner()->object_target_change(target);
}

void gcreature_imp::UnselectTarget()
{
	if (!_cur_target.IsValid())
	{
		return;
	}
	_cur_target.Clear();
	gcreature *pObj = (gcreature *)_parent;
	pObj->cur_target.Clear();
	Runner()->object_target_change(_cur_target);
}

void gcreature_imp::TrySelect(const XID& target)
{
	if (!_cur_target.IsValid())
	{
		//当前没有目标直接选中
		SelectTarget(target);
		return;
	}
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(_cur_target, info))
	{
		SelectTarget(target);
		return;
	}
	if (info.zombie)
	{
		//当前有目标如果目标是尸体也选中
		SelectTarget(target);
	}
}

void gcreature_imp::SetEnterDyingState(bool flag)
{
	_can_enter_dying_state = flag;
	//if(_dying && !flag)
	if (GetParent()->dying && !flag)
	{
		int lastHit = _dying_attacker_info.attacker_info.attacker.GetNewObjID();
		if ((_notify_action & 0x02) && !GetSkill().CanFakeRevive())
		{
			//通知给场景
			SendTo2(GM_MSG_LEVEL_CREATURE_ACTION, GetSceneImp()->Parent()->xid, -1, lastHit);
		}

		_filter_man.EF_BeforeDeath();
		//真死
		ClearAction();
		//死亡，调用死亡函数，进行处理
		OnDeath(_dying_attacker_info.msg_attacker, &_dying_attacker_info.attacker_info, _dying_attacker_info.kill_skill_id, _dying_attacker_info.kill_attack_stage);
		//_dying = false;
		GetParent()->dying = false;
	}
}

void gcreature_imp::Die(const XID& attacker, const attacker_info_t *pInfo, skill_id_t kill_skill_id, unsigned char kill_attack_stage, bool ignore_statistics)
{
	//if(_dying) return;
	if (GetParent()->dying)
	{
		return;
	}
	OnDying();
	int lastHit = 0;
	if (pInfo)
	{
		lastHit = pInfo->attacker.GetNewObjID();
	}

	if (_can_enter_dying_state)
	{
		_dying_attacker_info.msg_attacker = attacker;
		if (pInfo)
		{
			_dying_attacker_info.attacker_info = *pInfo;
		}
		else
		{
			memset(&_dying_attacker_info.attacker_info, 0, sizeof(_dying_attacker_info.attacker_info));
		}
		_dying_attacker_info.kill_skill_id = kill_skill_id;
		_dying_attacker_info.kill_attack_stage = kill_attack_stage;
		//_dying = true;
		GetParent()->dying = true;
		Runner()->enter_dying();
		return;
	}
	if (_parent->IsZombie())
	{
		return;
	}
	if ((_notify_action & 0x02) && !GetSkill().CanFakeRevive())
	{
		//通知给场景
		// 之所以延时通知 是因为 希望ginstance_imp::OnPlayerDeath先执行完 统计玩家死亡次数和判断幽灵状态
		LazySendTo2(GM_MSG_LEVEL_CREATURE_ACTION, GetSceneImp()->Parent()->xid, -1, lastHit, 2);
	}
	_filter_man.EF_BeforeDeath();
	//清除必要的标志
	//_attack.Clear();
	//清除当前Action
	ClearAction();
	//死亡，调用死亡函数，进行处理
	const XID& real_attacker = (pInfo && pInfo->pet_master.IsValid()) ? pInfo->pet_master : ((attacker.IsSubobject() && pInfo) ? pInfo->attacker : attacker); //问题太多 需要在这里统一修改避免子物体影响
	OnDeath(real_attacker, pInfo, kill_skill_id, kill_attack_stage, ignore_statistics);
	//死亡时通知召唤npc
	if (_p_summon_npc_map)
	{
		for (auto& summon_npc : *_p_summon_npc_map)
		{
			if (!summon_npc.second.die_with_owner)
			{
				continue;
			}
			MSG msg;
			BuildMessage(msg, GM_MSG_MASTER_DEAD, summon_npc.first, XID(), A3DVECTOR3(), 0);
			gmatrix::GetInstance().SendMessage(msg);
		}
	}
}

int gcreature_imp::HandlerEnchantMsg(const MSG& msg, attack_msg& atk_msg)
{
	if (_parent->CheckObjectState(gobject::STATE_INSTANCE_SPECTATE))
	{
		return 1;
	}
	if (_parent->IsZombie())
	{
		return 1;    //这里直接过滤了尸体接受技能，如果需要以尸体接受技能，则需要修改
	}

	//有害的并且无敌
	if (!atk_msg.helpful && GetOverwhelmingMode())
	{
		if (WMSKILL::SkillWrapper::GetType(atk_msg.skill_id) != WMSKILL::TYPE_CURSE_INVINCIBLE)
		{
			//策划要求补发无敌消息
			_runner->object_be_attacked(atk_msg.attacker_info.attacker, 0, atk_msg.skill_id, GetHPPercent(), WMSKILL::ATTACK_FLAG_IMMUNE, atk_msg.attack_stage, 0, A3DVECTOR3(0, 0, 0), Parent()->dir, 0, 0, 0, GetMoveStamp(), XID());
			return 1;
		}
	}

	//是否只接受某ID攻击，如果_invinceble_except_id无效,_invinceble_except的话就是无敌了。
	const XID& attacker = atk_msg.attacker_info.attacker;
	if (_invinceble_except && _invinceble_except_id != attacker)
	{
		return 1;
	}
	//处理一下到来的攻击消息
	_filter_man.EF_TranslateRecvAttack(msg.source, atk_msg);
	if (!TestEnchantRule(msg, atk_msg))
	{
		return 1;
	}
	return HandleAttackMsg(msg, atk_msg);
}

int gcreature_imp::HandleAttackMsg(const MSG& msg, attack_msg& atk_msg)
{
	object_interface oif(this);
	_skill.BeEnchanted(oif, msg.source, msg.pos, msg.param, atk_msg);
	return 0;
}

bool gcreature_imp::StartAction(base_action *pAction)
{
	return _action_man.StartAction(pAction);
}

void gcreature_imp::EnterScene(gscene_imp *pSceneImp)
{
	DoDamage(0, NULL, false);	//做一次假的伤害，更新血量HP定时器
	DecMP(0);		//同上
	gobject_imp::EnterScene(pSceneImp);
}

void gcreature_imp::LeaveScene()
{
	if (_p_summon_npc_map)
	{
		for (auto& summon_npc : *_p_summon_npc_map)
		{
			MSG msg;
			BuildMessage(msg, GM_MSG_MASTER_DEAD, summon_npc.first, XID(), A3DVECTOR3(), 0);
			gmatrix::GetInstance().SendMessage(msg);
		}
	}
	gobject_imp::LeaveScene();
}

void gcreature_imp::Say(const abase::octets& msg)
{
	//这个好像只有debug才用
	if (!_parent->pSlice)
	{
		return;
	}
	SightRegionChatMsg(GetParent(), GetSceneImp(), _parent->pSlice, CHAT_CHANNEL_LOCAL, 0, 0, _parent->ID, NULL, &msg);
}

void gcreature_imp::DebugUTF8Say(const abase::octets& msg)
{
	if (SERVER_CONFIG.debug_command_mode)
	{
		if (!_parent->pSlice)
		{
			return;
		}
		GNET::Octets os_to;
		GNET::CharsetConverter::conv_charset_t2u(GNET::Octets(msg.begin(), msg.size()), os_to);
		abase::octets msg_ucs2(os_to.begin(), os_to.size());
		SightRegionChatMsg(GetParent(), GetSceneImp(), _parent->pSlice, CHAT_CHANNEL_LOCAL, 0, 0, _parent->ID, NULL, &msg_ucs2);
	}
}

void gcreature_imp::DebugUTF8Say(const char *msg)
{
	if (SERVER_CONFIG.debug_command_mode)
	{
		if (!_parent->pSlice)
		{
			return;
		}
		GNET::Octets os_to;
		GNET::CharsetConverter::conv_charset_t2u(GNET::Octets(msg, strlen(msg)), os_to);
		abase::octets msg_ucs2(os_to.begin(), os_to.size());
		SightRegionChatMsg(GetParent(), GetSceneImp(), _parent->pSlice, CHAT_CHANNEL_LOCAL, 0, 0, _parent->ID, NULL, &msg_ucs2);
	}
}

int gcreature_imp::GetSceneParamValue(int key)
{
	gscene_imp *pSceneImp = GetRealSceneImp();
	if (!pSceneImp)
	{
		return 0;
	}
	return pSceneImp->GetCommonDataValue(key);
}

void gcreature_imp::SetSceneParamValue(int key, int value)
{
	gscene_imp *pSceneImp = GetRealSceneImp();
	if (!pSceneImp)
	{
		return;
	}
	pSceneImp->SetCommonDataValue(key, value);
}

int gcreature_imp::ModifySceneParamValue(int key, int offset)
{
	gscene_imp *pSceneImp = GetRealSceneImp();
	if (!pSceneImp)
	{
		return 0;
	}
	return pSceneImp->ModifyCommonDataValue(key, offset);
}

void gcreature_imp::BeCure(const XID& who, const attacker_info_t& info, float value, int skill_id)
{
	OnCure(who, info, value, skill_id);
	if (value > 0.5f)
	{
		_runner->be_healed(info.attacker, info.lid, value);
	}
}


void gcreature_imp::BeHurt(const XID& who, const attacker_info_t& info, float damage, int skill_id, bool ignore_invincible, bool ignore_prop_dam3)
{
	if (!ignore_invincible && GetOverwhelmingMode())
	{
		return;
	}
	if (_invinceble_except && _invinceble_except_id != info.attacker)
	{
		return;
	}
	if (GetNoDot() > 0)
	{
		return;
	}

	//在这里处理第三组增减伤属性，以便技能过程扣血和buff扣血都生效
	if (!ignore_prop_dam3)
	{
		damage *= 1 - _prop.GetGProperty()->PROP_NAME_GET(DamRedu3) * 0.001f;
		damage = std::max(1.0f, damage);
	}

	//发个消息给客户端
	float damage_final = DoDamage(damage, &info, ignore_invincible);
	OnHurt(who, info, damage_final, skill_id);
	_runner->be_hurt(info.attacker.IsValid() ? info.attacker : who, info.lid, damage);
	if (_prop.GetHP() <= 0)
	{
		Die(who, &info, skill_id);
	}
}

float gcreature_imp::BeAttacked(const XID& who, const attack_msg& msg, float damage, unsigned int flags, const A3DVECTOR3& pos, char prev_control_type, char control_type, int control_time)
{
	float damage_final = 0;
	//有害的
	//if(GetOverwhelmingMode()) return; //FIXME: 不能在这处理
	if (HasAction())
	{
		_action_man.EventBeAttacked();
	}

	//在这里处理第三组增减伤属性，以便技能过程扣血和buff扣血都生效
	damage *= 1 + msg.attack_damage_add3 * 0.001f - _prop.GetGProperty()->PROP_NAME_GET(DamRedu3) * 0.001f;
	damage = std::max(1.0f, damage);

	_filter_man.EF_BeAttacked(who, msg.attacker_info, damage, flags);
	if (damage > 0)
	{
		damage_final = DoDamage(damage, &(msg.attacker_info), false);
	}
	OnAttacked(who, msg, damage_final); //用实际伤害
	int fix_damage = GetFixedDamage();
	float damage_show = 0;
	if (fix_damage > 0)
	{
		damage_show = fix_damage;
	}
	else
	{
		damage_show = std::max(1.0f, damage);
	}
	//只通知，实际移动不在这里做
	//A3DVECTOR3 temp = Parent()->pos;
	//if(flags & WMSKILL::ATTACK_FLAG_CTRL_MOVE)
	//{
	//	GetSceneImp()->GetLastReachablePos(Parent()->pos,pos,temp,true); //以固定的步长直线测试每个点, 直到遇到不可达, 并修正最后可达点的y坐标
	//	float _x = temp.x-Parent()->pos.x;
	//	float _z = temp.z-Parent()->pos.z;
	//	if(_x*_x+_z*_z < 0.001)
	//	{
	//		flags &= ~WMSKILL::ATTACK_FLAG_CTRL_MOVE;
	//	}
	//	else
	//	{
	//		A3DVECTOR3 offset = temp;
	//		offset -= Parent()->pos;
	//		if(!StepMove(offset, false))
	//		{
	//			flags &= ~WMSKILL::ATTACK_FLAG_CTRL_MOVE;
	//			OnForceStepMove();
	//		}
	//		else
	//		{
	//			//flags &= ~WMSKILL::ATTACK_FLAG_CTRL_MOVE;
	//		}
	//	}
	//}
	//_runner->object_be_attacked(who,damage_show,msg.skill_id,/*msg.skill_level*/ GetHPPercent(),flags,msg.attack_stage,0,temp,Parent()->dir,prev_control_type,control_type,control_time, GetMoveStamp());

	//对于串行的消息，追溯上一个人作为 attacker 发给客户端
	XID front_xid;
	if (msg.is_sequence)
	{
		for (int i = msg.sequence_idx - 1; i >= 0; i --)
		{
			if (msg.sequence_xid[i].IsValid())
			{
				front_xid = msg.sequence_xid[i];
				flags |= WMSKILL::ATTACK_FLAG_SEQUENCE;
				break;
			}
		}
		if (!front_xid.IsValid())
		{
			front_xid = who;
			flags |= WMSKILL::ATTACK_FLAG_SEQUENCE;
		}
	}
	//__PRINTF("debug_control: send object_be_attacked skill=%d stage=%d flags=0x%X pre_control=%d control_type=%d control_time=%d dir=%d\n",
	//         msg.skill_id, msg.attack_stage, flags, prev_control_type, control_type, control_time, Parent()->dir);
	if (msg.attacker_info.attacker_mode & (attacker_info_t::AM_PLAYER_MECH | attacker_info_t::AM_PLAYER_HEIR))
	{
		flags |= WMSKILL::ATTACK_FLAG_PET;
	}
	_runner->object_be_attacked(msg.attacker_info.attacker, damage_show, msg.skill_id,/*msg.skill_level*/ GetHPPercent(), flags, msg.attack_stage, 0, pos, Parent()->dir, prev_control_type, control_type, control_time, GetMoveStamp(), front_xid);

//	gcreature* pCreature = GetParent();
//	pCreature->base_info.cur_hp = _prop.GetCurHP();
	if (GetHP() <= 0)
	{
		Die(who, &msg.attacker_info, msg.skill_id, msg.attack_stage);
	}
	return damage_final;
}

float gcreature_imp::BeTreated(const XID& who, const attack_msg& msg, float value, unsigned int flags, const A3DVECTOR3& pos, char prev_control_type, char control_type, int control_time)
{
	value = IncHP(value);
	if (value > 0)
	{
		OnCure(who, msg.attacker_info, value, msg.skill_id);
		flags |= WMSKILL::ATTACK_FLAG_TREAT;
		_runner->object_be_attacked(msg.attacker_info.attacker, value, msg.skill_id, GetHPPercent(), flags, msg.attack_stage, 0, pos, Parent()->dir, prev_control_type, control_type, control_time, GetMoveStamp(), XID());
	}
	return value;
}

void gcreature_imp::SendControlInfo(const attack_msg& msg, char prev_control_type, char control_type, int control_time)
{
	XID front_xid;
	_runner->object_be_attacked(msg.attacker_info.attacker, 0, msg.skill_id, GetHPPercent(), WMSKILL::ATTACK_FLAG_CTRL, msg.attack_stage, 0, A3DVECTOR3(0, 0, 0), Parent()->dir, prev_control_type, control_type, control_time, GetMoveStamp(), front_xid);
}

void gcreature_imp::BeEnchanted(const XID& who, const attack_msg& msg, int arg, unsigned int flags)
{
	//有害的并且无敌
	if (!msg.helpful && GetOverwhelmingMode())
	{
		return;
	}
	_runner->be_enchanted(who, arg, msg.skill_id, msg.skill_level, flags, msg.attack_stage, 0);
	OnEnchanted(who, msg, arg);
}

float gcreature_imp::DoDamage(float damage, const attacker_info_t *atk_info, bool ignore_invincible)
{
	/*
	class ReGenTimer : public timeout_ctrl::callback
	{
		virtual int OnTimeout(gcreature_imp* pImp, int index, time_t timeout, int usec)
		{
			timeval tv;
			gettimeofday(&tv, NULL);
			int64_t offset = (timeout - tv.tv_sec)*1000000 - tv.tv_usec + usec;
			if(offset < 0) offset = 0;
			if(offset > 1000000 * 2) offset = 1000000;

			//计算发生时间触发的确切时间，转换为延迟消息
			if(pImp->_prop.GetHP() < pImp->_prop.GetHPMax()) pImp->LazySendTo(GM_MSG_FAST_REGEN_HP,pImp->_parent->ID, 0, MICROSEC_TO_TICK(offset));
			return 0;
		}
		virtual bool NeedRelease() {return false;}
	};
	//	BeforeDoDamage(damage,atk_info);  目前不调用了
	if(_ignore_max_hp2)
	{
		//如果存在每下必扣真血的选项，则修正damage
		damage_hard += damage;
		damage = 0;
	}

	static ReGenTimer ccb;
	if(!IsPlayerClass()) _timeout.HRInsert(TM_IDX_FAST_REGEN_HP, HP_MP_REGEN_CONFIG.hp_regen_wait_sec , &ccb, true);		//加入高精度定时器 在5秒后触发快速回血操 现在只有NPC走这个逻辑了
	*/
	int64_t i_damage = damage;
	int shield = _prop.GetShield();
	if (shield > 0)
	{
		if (shield > i_damage)
		{
			_prop.SetShield(shield - i_damage);
		}
		else
		{
			_prop.SetShield(0);
			if (!GetMechP() || GetMechP()->TryBreakShield())
			{
				_filter_man.EF_ShieldBreak();
			}
		}
		return i_damage;
	}
	if (!ignore_invincible && atk_info)
	{
		float f_damage = i_damage;
		_filter_man.EF_FinalAdjustDamage(atk_info->attacker, f_damage);
		i_damage = f_damage;
	}
	BeforeDoDamage(i_damage, atk_info, ignore_invincible); //添加npc锁血功能
	if (i_damage > 0)
	{
		return _prop.DecHP(i_damage, 0, GetSealIdleMode() || GetSealRootMode());
	}
	else
	{
		return 0.f;    //即使没有减血，也要更新ＨＰ回复
	}
}
unsigned int gcreature_imp::GetFaction() const
{
	unsigned int faction = gobject_imp::GetFaction();

	if (IsPlayerClass())
	{
		faction |= NATION_TO_FACTION_MASK_PLAYER(GetNation());
	}

	faction = (faction & ~_prop._exclude_faction) | _prop._include_faction;
	return faction;
}

void gcreature_imp::ChangeNation(int nation_id)
{
	__PRINTF("creature:%ld change nation from:%d to:%d",
	         Parent()->ID.id, FACTION_MASK_TO_NATION_ID(gobject_imp::GetFaction()), nation_id);
	if (IsNPCClass())
	{
		//TODO 直接修改原有配置 ? 还是用 Include和 Exclude?
		//初始化时在_faction上作的
		unsigned int faction = gobject_imp::GetFaction();
		if (FACTION_MASK_TO_NATION_ID(faction) == nation_id)
		{
			return;
		}
		faction &= ~NATION_TO_FACTION_MASK_NPC(FACTION_MASK_TO_NATION_ID(faction));
		faction |= NATION_TO_FACTION_MASK_NPC(nation_id);
		gnpc_imp *pimp = (gnpc_imp *)this;
		pimp->ChangeFaction(faction);
	}
}

bool gcreature_imp::ActiveCombatState(bool combat_state, unsigned char reason, bool notify)
{
	bool rst = _attack.SetCombat(combat_state);
	if (rst)
	{
		__PRINTF("creature:%ld change combat_state from:%d to:%d\n",
		         Parent()->ID.id, GetParent()->combat_state, combat_state);
		GetParent()->combat_state = combat_state;
	}
	if (rst || notify)
	{
		//rst保证了有变化才会通知，但有些时候需要专门提醒客户端停止战斗状态
		Runner()->combat_state(combat_state, reason);
	}
	return rst;
}

void gcreature_imp::UpdateDataToParent()
{
	//这个函数的调用，不需要太频繁了，只有少量数据会产生变化 $$$$$$$$$$$$$$
	//这个函数可以修改，将容易改变的数据做统一更新，不容易改变的数据做手动更新，单独的数据做特殊处理
	gcreature *pCreature = (gcreature *)_parent;
	ASSERT(pCreature);

	pCreature->base_info.level = _prop.GetLevel();
	pCreature->base_info.prof = _prop.GetProf();
	pCreature->base_info.prof_level = _prop.GetProfLevel();      //以后可以考虑将部分不易变的内容减少更新次数
	pCreature->faction = GetFaction();   //阵营可能变化
	pCreature->enemy_faction = GetEnemyFaction();
	pCreature->combat_state = IsCombatState();
	pCreature->cur_target = _cur_target;
}

void gcreature_imp::OnDuelStart(const XID& target)
{
	GetParent()->duel_target = target;
	GetParent()->SetObjectState(gobject::STATE_IN_DUEL_MODE);
}

void gcreature_imp::OnDuelStop()
{
	GetParent()->duel_target.Clear();
	GetParent()->ClrObjectState(gobject::STATE_IN_DUEL_MODE);
}

//void gcreature_imp::SkillMove(bool check_path,bool redir,A3DVECTOR3& target_pos,unsigned short& dir,bool check_dir_info,bool check_ydiff)
//{
//	//if(GetSealRootMode()) return; //击退对手后的跟进，是否需要受定身限制？需要的话则加上
//	A3DVECTOR3 stop_pos = target_pos;
//	if(check_path)
//	{
//		//只有check_path有效, check_dir_info/check_ydiff才有意义
//		GetSceneImp()->GetLastReachablePos(Parent()->pos,target_pos,stop_pos,check_dir_info,check_ydiff);
//		float _x = stop_pos.x-Parent()->pos.x;
//		float _z = stop_pos.z-Parent()->pos.z;
//		if(_x*_x+_z*_z < 0.001)
//		{
//			target_pos = Parent()->pos;
//			dir = Parent()->dir;
//			return;
//		}
//	}
//	else
//	{
//		//逆向trace获得最后一个可达点, 既然无视不可达自然无视单向通过
//		GetSceneImp()->GetLastReachableTargetPos(Parent()->pos,target_pos,stop_pos);
//	}
///*
//	if(!GetSceneImp()->GetValidPos(stop_pos))
//	{
//		//stop_pos不可达
//		target_pos = Parent()->pos;
//		dir = Parent()->dir;
//		return;
//	}
//*/
//	A3DVECTOR3 offset = stop_pos;
//	offset -= Parent()->pos;
//	if(!StepMove(offset,redir))
//	{
//		if(redir)
//		{
//			A3DVECTOR3 temp = target_pos;
//			temp -= Parent()->pos;
//			temp.y = 0;
//			temp.Normalize();
//			if(temp.SquaredMagnitude() > 1e-3)
//			{
//				//需要设置方向并且具有横向位移,调整方向
//				Parent()->SetDirection(temp);
//			}
//		}
//	}
//	target_pos = Parent()->pos;
//	dir = Parent()->dir;
//}
//
////如果路径可达的话, 直接移动到目标点, 不要修正y坐标, 否则不移动
//bool gcreature_imp::DirectMove(bool redir,A3DVECTOR3& target_pos,unsigned short& dir,bool nocheck,bool trace_y)
//{
//	//if(GetSealRootMode()) return; //击退对手后的跟进，是否需要受定身限制？需要的话则加上
//	A3DVECTOR3 stop_pos = target_pos;
//	if (!nocheck)
//	{
//		GetSceneImp()->GetLastReachablePos(Parent()->pos,target_pos,stop_pos,false,IsPlayerClass() || IsPlayerNPCClass());
//		float _x = stop_pos.x-target_pos.x;
//		float _z = stop_pos.z-target_pos.z;
//		if(_x*_x+_z*_z > 0.001) //不能到目标点
//		{
//			target_pos = Parent()->pos;
//			dir = Parent()->dir;
//			return false;
//		}
//	}
//	if(!trace_y) stop_pos.y = target_pos.y;		//如果标记了不trace_y, 那么就用传入的数值，否则使用真的值
//	A3DVECTOR3 offset = stop_pos;
//	offset -= Parent()->pos;
//	if(!StepMove(offset,redir))
//	{
//		if(redir)
//		{
//			A3DVECTOR3 temp = target_pos;
//			temp -= Parent()->pos;
//			temp.y = 0;
//			temp.Normalize();
//			if(temp.SquaredMagnitude() > 1e-3)
//			{
//				//需要设置方向并且具有横向位移,调整方向
//				Parent()->SetDirection(temp);
//			}
//		}
//	}
//	target_pos = Parent()->pos;
//	dir = Parent()->dir;
//	return true;
//}

bool gcreature_imp::SkillMove(A3DVECTOR3& target_pos, unsigned short& dir, bool redir, bool check_path, bool reverse_check, int check_y_diff)
{
	//if(GetSealRootMode()) return; //击退对手后的跟进，是否需要受定身限制？需要的话则加上
	A3DVECTOR3 stop_pos = target_pos;
	if (check_path)
	{
		if (reverse_check)
		{
			//逆向trace获得最后一个可达点, 既然无视不可达自然无视单向通过
			GetSceneImp()->GetLastReachableTargetPos(Parent()->pos, target_pos, stop_pos);
		}
		else
		{
			//只有check_path有效, check_dir_info/check_ydiff才有意义
			GetSceneImp()->GetLastReachablePos(Parent()->pos, target_pos, stop_pos, check_y_diff);
			float _x = stop_pos.x - Parent()->pos.x;
			float _z = stop_pos.z - Parent()->pos.z;
			if (_x * _x + _z * _z < 0.001)
			{
				target_pos = Parent()->pos;
				dir = Parent()->dir;
				return false;
			}
		}
	}
	/*
		if(!GetSceneImp()->GetValidPos(stop_pos))
		{
			//stop_pos不可达
			target_pos = Parent()->pos;
			dir = Parent()->dir;
			return;
		}
	*/
	A3DVECTOR3 offset = stop_pos;
	offset -= Parent()->pos;
	if (!StepMove(offset, redir, 0, false, CanFly()))
	{
		if (redir)
		{
			A3DVECTOR3 temp = target_pos;
			temp -= Parent()->pos;
			temp.y = 0;
			temp.Normalize();
			if (temp.SquaredMagnitude() > 1e-3)
			{
				//需要设置方向并且具有横向位移,调整方向
				Parent()->SetDirection(temp);
			}
		}
	}
	target_pos = Parent()->pos;
	dir = Parent()->dir;
	return true;
}

void gcreature_imp::GetLastReachablePos(const A3DVECTOR3& start, const A3DVECTOR3& end, A3DVECTOR3& stop_pos, int check_y_diff, bool can_fall)
{
	GetSceneImp()->GetLastReachablePos(start, end, stop_pos, check_y_diff, CanFly(), can_fall);
}

bool gcreature_imp::RawStepMove(const A3DVECTOR3& offset)
{
	return gobject_imp::StepMove(offset, false, 0, true, 0, 0, 0, 0, 0, 0, 0, 0, false, false);
}

void gcreature_imp::BeKnockback(unsigned short skill_id, float speed, const A3DVECTOR3& direction, unsigned short ms_time, unsigned char type)
{
	ASSERT(false && "击退逻辑需要另外制作");
	/*
	__PRINTF(""   FMT_I64" 被击退了 skill_id: %d,speed: %f,time: %d,direction(%f,%f,%f)\n",GetParent()->ID.id,skill_id,speed,ms_time,direction.x,direction.y,direction.z);
	CancelAction(); //强制打断所有action进行击退动作
	StartAction(new action_knockback(this,skill_id,speed,direction,ms_time,type));
	*/
}

void gcreature_imp::SetHP(int64_t hp)
{
	if (_parent->IsZombie())
	{
		return;
	}
	_prop.SetHP(hp);
}

float gcreature_imp::AbsIncHP(int64_t hp)
{
	if (_parent->IsZombie() || _parent->CheckObjectServerState(gobject::SERVER_STATE_BAN_ADD_HP))
	{
		return 0.0f;
	}
	if (hp < 0)
	{
		hp = 0;
	}
	float inc_hp = _prop.IncHP(hp);
	//if(inc_hp > 0) _runner->be_healed(inc_hp);
	return inc_hp;
}

float gcreature_imp::IncHP(int64_t hp)
{
	int heal_add = _prop.GetGProperty()->PROP_NAME_GET(curHealAdd);
	hp = (int64_t)(hp * (1 + _prop._heal_effect_addon + heal_add / 1000.f));
	if (hp <= 0)
	{
		return 0;
	}
	return AbsIncHP(hp);
}

void gcreature_imp::DecHP(int64_t hp)
{
	DoDamage(hp, NULL, false);
	int64_t cur_hp = _prop.GetHP();
	if (cur_hp <= 0)
	{
		Die(XID());
	}
}

void gcreature_imp::DelayRemoveFilter(int id, size_t delay_ms_time)
{
	if (!delay_ms_time)
	{
		return;
	}
	MSG msg;
	BuildMessage(msg, GM_MSG_DELAY_REMOVE_FILTER, Parent()->ID, Parent()->ID, Parent()->pos, id);
	gmatrix::GetInstance().SendMessage(msg, MILLISEC_TO_TICK(delay_ms_time));
}

void gcreature_imp::EnterKnockback(size_t ms_time, const A3DVECTOR3& end_pos, unsigned char type)
{
//	__PRINTF("void gcreature_imp::EnterKnockback(size_t ms_time,const A3DVECTOR3& end_pos) tick: "   FMT_I64",ms_time: %d,end_pos(%f,%f,%f)\n",g_timer.get_tick(),ms_time,end_pos.x,end_pos.y,end_pos.z);
	Runner()->enter_server_move_control_mode(ms_time, end_pos, type);
}

void gcreature_imp::LeaveKnockback()
{
	Runner()->leave_server_move_control_mode();
}

bool gcreature_imp::IsNPCNear(ruid_t npc_id, int tid, float range)
{
	//查询指定id 的npc是否符合tid并且在附近,这个接口如果使用太多是不行的
	float squared_range = range * range;
	XID npc;
	MAKE_XID(npc, npc_id);
	if (!npc.IsNPC())
	{
		return false;
	}
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(npc, info))
	{
		return false;
	}
	if (info.tid != tid)
	{
		return false;
	}
	if (info.zombie)
	{
		return false;
	}
	const gobject *pObj = Parent();
	return info.CheckRange(pObj, squared_range);
}
/*
bool gcreature_imp::CheckCanAttack(const skill_judgement_user_t& sju,const skill_judgement_target_t& sjt,PK_TYPE pk_type)
{
	if(!sju.xid.IsValid()) return false;
	if(!sjt.xid.IsValid()) return false;
	if(sju.xid == sjt.xid) return false;
	switch(pk_type)
	{
		default:
		case PT_MAFIA://暂时把帮派基地规则和大世界一样
		case PT_BIG_WORLD:
		{
			// 决斗>队伍>阵营>结义>夫妻 (这里服务器不判新手保护,只在伤害判定时处理)
			if(sju.duel_target == sjt.xid) return true; //决斗判断 将来可能还需要帮战,优先级高
			if(sju.team.IsValid() && sjt.team.IsValid())
			{
				//有队伍判断队伍
				if(sju.team == sjt.team) return false;
			}
			if(sju.family_id > 0 && sju.family_id == sjt.family_id) return false;//不能兄弟相残
			if(sju.spouse_id > 0 && sju.spouse_id == sjt.xid.id)  return false;//不能打老婆(丈夫)

			//PVP保护状态的玩家无法攻击他国NPC
			if(sju.xid.IsPlayer() && sjt.xid.IsNPC() && sju.pvp_protected)
			{
				int sjt_nation = FACTION_MASK_TO_NATION_ID(sjt.faction);
				int sju_nation = FACTION_MASK_TO_NATION_ID(sju.faction);
				if(sjt_nation && sju_nation != sjt_nation)
					return false;
			}
			//仅对敌对战车和个别怪物
			if(sju.extend_faction & gobject::EXTEND_FACTION_CHARIOT)
			{
				if(sjt.extend_faction & gobject::EXTEND_FACTION_CHARIOT_ENEMY)
					return true;
				else
					return false;
			}

			if(sju.enemy_faction & sjt.faction) return true; //阵营符合

			//如果是还击对象 可以直接打
			if(sju.can_fight_back(sjt.xid)) return true;

			//pk规则
			if(sju.pk_setting == PS_PEACE || sju.pk_setting == PS_ALLIANCE)
			{
				return false;//和平模式，不能打人
			}
			else if(sju.pk_setting == PS_KILL || sju.pk_setting == PS_TEAM)
			{
				return true;//杀戮模式，见人就杀
			}
			else if(sju.pk_setting == PS_MAFIA)
			{
				if(sjt.mafia_id > 0 && sjt.mafia_id == sju.mafia_id) return false;//不能同门相残
				return true;
			}
			else if(sju.pk_setting == PS_CRIME)
			{
				if(sjt.faction & EXP_CAMPMASK_RED_NAME || sjt.faction & EXP_CAMPMASK_YELLOW_NAME)
				{
					return true;
				}
				return false;
			}
			else if(sju.pk_setting == PS_COUNTRY)
			{
				if(FACTION_MASK_TO_NATION_ID(sjt.faction) != FACTION_MASK_TO_NATION_ID(sju.faction))
					return true;
				return false;
			}
			else
			{
				//TODO:模式不正确时，返回true还是false？true似乎合理一些
				return false;
			}
		}
		break;

		case PT_BATTLE:
		{
			//安全区虽然生效但是无法在搜索的时候使用，不会产生真正的伤害
			if(sju.enemy_faction & sjt.faction) return true; //阵营符合则能打
			return false;
		}
		break;

		case PT_UNDERGROUND:
		{
			if(sju.team.IsValid() && sjt.team.IsValid())
			{
				//有队伍判断队伍
				if(sju.team == sjt.team) return false;
			}
			return true;
		}
		break;

		case PT_DEAD:
		{
			return true;
		}
		break;
	}
	return false;
}
*/
void gcreature_imp::GatherPlayer(float radius, std::vector<XID>& player_list)
{
	search_player<slice> worker(this, radius, player_list);
	GetSceneImp()->GetGrid().ForEachSlice(_parent->pos, radius, worker);
}

bool gcreature_imp::CheckCanAttack(const XID& target, std::set<int> *ignore_state) const
{
	if (!target.IsValid())
	{
		return false;
	}
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;    //对象是否存在
	}
	if (info.zombie)
	{
		return false;    //死亡
	}
	if (Parent()->ID.IsPlayer() && GetPvPProtectFlag())
	{
		//保护状态的玩家应该无法攻击其他玩家；以及其他玩家的机甲
		if (target.IsPlayer() && target != GetDuelTarget())
		{
			return false;
		}
		if (info.object_state & gobject::STATE_PLAYER_MECH && info.pet_master != GetDuelTarget())
		{
			return false;
		}
		if (info.object_state2 & gobject::STATE2_PLAYER_FAKER && info.pet_master != GetDuelTarget())
		{
			return false;
		}
		if (info.object_state2 & gobject::STATE2_PLAYER_HEIR && info.pet_master != GetDuelTarget())
		{
			return false;
		}
	}
	const gcreature *pObj = GetParent();
	if (!info.Check(pObj))
	{
		return false;    //世界场景位置
	}
	if (info.can_not_be_attack)
	{
		return false;    //不可攻击属性
	}
	if (ignore_state == NULL || ignore_state->find(WMSKILL::REASON_IN_SEAL_MODE) == ignore_state->end())
	{
		if (GetSealSilentMode())
		{
			return false;    //对象自身属性
		}
	}
	if (!pObj->pSceneImp)
	{
		return false;    //不在场景里，打个P啊
	}
	attacker_info_t agent;
	agent.attacker = pObj->ID;
	agent.faction = pObj->faction;
	agent.enemy_faction = GetEnemyFaction();
	agent.friend_faction = GetFriendFaction();
	agent.pk_setting_a = GetPKSetting();
	agent.pk_value_a = GetPKValue();
	agent.duel_target = GetDuelTarget();
	agent.mafia_id = pObj->mafia_id;
	agent.team = pObj->team;
	agent.spouse_id = pObj->spouse_id;
	agent.mentor_id = pObj->mentor_id;
	agent.pet_master = pObj->master;
	agent.pve_spouse_id = pObj->pve_spouse_id;
	agent.attacker_mode = GetPvPProtectFlag() ? 0 : attacker_info_t::AM_PVP_ENABLE;
	if (GetRegionSetting() & RS_SANCTUARY)
	{
		agent.attacker_mode |= attacker_info_t::AM_PVP_SANCTUARY;
	}

	CombatChecker::target_info_t target_info;
	target_info.ID = target;
	target_info.info = info;
	target_info.pk_type = GetScenePKType();
	GetFightBack(target_info.fight_back);
	GetIntimateList(target_info.intimate_list);
	target_info.is_valid = true;
	return CombatChecker::CanAttack(agent, target_info, ignore_state);
	/*
	skill_judgement_user_t sju;
	sju.xid = pObj->ID;
	sju.faction = pObj->faction;
	sju.enemy_faction = pObj->enemy_faction;
	sju.pk_setting = GetPKSetting();
	sju.duel_target = GetDuelTarget();
	sju.mafia_id = pObj->mafia_id;
	sju.family_id = pObj->family_id;
	sju.team = pObj->team;
	sju.married = pObj->married;
	sju.spouse_id = pObj->spouse_id;
	sju.native_zoneid = pObj->native_zoneid;
	sju.fight_back_num = CollectFightBackMember(sju.fight_back,MAX_PLAYER_FIGHT_BACK);
	sju.pvp_protected = GetPvPProtectFlag();
	sju.extend_faction = pObj->extend_faction;
	//被攻击方
	skill_judgement_target_t sjt;
	sjt.xid = target;
	sjt.faction = info.faction;
	sjt.mafia_id = info.mafia_id;
	sjt.family_id = info.family_id;
	sjt.team = info.team;
	sjt.married = info.married;
	sjt.spouse_id = info.spouse_id;
	sjt.scene = info.scene_tag;
	sjt.world = info.world_tid;
	sjt.native_zoneid = info.native_zoneid;
	sjt.pvp_protected = info.object_state & gobject::STATE_PVP_NOPROTECTED;
	sjt.extend_faction = info.extend_faction;
	return CheckCanAttack(sju,sjt,GetScenePKType());
	*/
}

bool gcreature_imp::CheckCanBless(const XID& target) const
{
	if (!target.IsValid())
	{
		return false;
	}
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;    //对象是否存在
	}
	if (info.zombie)
	{
		return false;    //死亡
	}
	const gcreature *pObj = GetParent();
	if (!info.Check(pObj))
	{
		return false;    //世界场景位置
	}
	if (!pObj->pSceneImp)
	{
		return false;    //不在场景里，打个P啊
	}
	attacker_info_t agent;
	agent.attacker = pObj->ID;
	agent.faction = pObj->faction;
	agent.enemy_faction = GetEnemyFaction();
	agent.friend_faction = GetFriendFaction();
	agent.pk_setting_a = GetPKSetting();
	agent.pk_value_a = GetPKValue();
	agent.duel_target = GetDuelTarget();
	agent.mafia_id = pObj->mafia_id;
	agent.team = pObj->team;
	agent.spouse_id = pObj->spouse_id;
	agent.pet_master = pObj->master;
	agent.attacker_mode = GetPvPProtectFlag() ? 0 : attacker_info_t::AM_PVP_ENABLE;
	if (GetRegionSetting() & RS_SANCTUARY)
	{
		agent.attacker_mode |= attacker_info_t::AM_PVP_SANCTUARY;
	}

	CombatChecker::target_info_t target_info;
	target_info.ID = target;
	target_info.info = info;
	target_info.pk_type = GetScenePKType();
	GetFightBack(target_info.fight_back);
	GetIntimateList(target_info.intimate_list);
	target_info.is_valid = true;
	return CombatChecker::CanBless(agent, target_info);
}

bool gcreature_imp::SkillCheckCanAttack(const XID& target, unsigned int& flag, float& distance, A3DVECTOR3& pos, skill_id_t skill_id)
{
	enum
	{
		CANNOT_ATTACK   = 1,
		TARGET_INVALID  = 2,
		OUT_OF_RANGE    = 4,
		IN_SEAL_MODE    = 4,
	};
	flag = 0;
	object_info info;
	memset(&info, 0, sizeof(info));
	bool rst = gmatrix::GetInstance().QueryObject(target, info);
	if (!rst)
	{
		flag |= TARGET_INVALID;
		return false;
	}
	else
	{
		if (info.zombie)
		{
			flag |= TARGET_INVALID;
			return false;
		}
	}
	const gobject *pObj = Parent();
	if (!info.Check(pObj))
	{
		return false;
	}
	pos = info.pos;
	float attack_range = GetSkillRange(skill_id) + info.body_size + GetParent()->body_size;
	float dis = squared_distance(info.pos, _parent->pos);
	if (dis > attack_range * attack_range)
	{
		//无法攻击到敌人，距离过远
		flag |= OUT_OF_RANGE;
		return false;
	}
	distance = dis;
	if (IsSilentWithSkill(skill_id) || IsDragWithSkill(skill_id) || IsChainWithSkill(skill_id))
	{
		flag |= IN_SEAL_MODE;
		return false;
	}
	return true;
}

void gcreature_imp::StopQinggong(bool rush)
{
}

void gcreature_imp::GetToGround()
{
	if (!GetSceneImp())
	{
		return;
	}
	A3DVECTOR3 pos = Parent()->pos;
	if (!GetSceneImp()->GetValidPos(pos))
	{
		return;
	}
	Parent()->pos.y = pos.y;
}

bool gcreature_imp::MafiaAttackCheck(const XID& self_id, const int& self_mafia_id, const XID& target_id, const int& target_mafia_id)
{
	// 调用此函数，必须保证已经在帮派基地里面了
	// 在帮派基地里面的两个实体A,B，只要A,B中有一个是NPC(怪物)且它们的帮派id不一样，则可以打
	// 这里的规则涉及到宠物，需要对一下宠物的规则 TODO
	if (!self_id.IsNPC() && !target_id.IsNPC())
	{
		return false;
	}
	if (self_mafia_id == target_mafia_id)
	{
		return false;
	}
	return true;
}

bool gcreature_imp::TestEnchantRule(const MSG& msg, attack_msg& ack_msg)
{
	if (InvalidPos(ack_msg.attacker_info.pos))
	{
		return false;
	}
	CombatChecker::target_info_t info(Parent());
	//FIXME:这里原本是传入的msg.source 但是我觉得子物体会导致BUG，改成attacker了
	info.is_victim = IsVictim(ack_msg.attacker_info.attacker);
	info.is_intimate = IsIntimate(ack_msg.attacker_info.attacker);
	if (ack_msg.helpful)
	{
		if (IsRed() && ack_msg.attacker_info.attacker != Parent()->ID)
		{
			return false;
		}
		return CombatChecker::CanBless(ack_msg.attacker_info, info);
	}
	else
	{
		return CombatChecker::CanAttack(ack_msg.attacker_info, info);
	}

	/*
	const XID& attacker = ack_msg.attacker_info.attacker;
	const attacker_info_t& ainfo = ack_msg.attacker_info;
	PK_TYPE pk_type = GetScenePKType();
	if (!ack_msg.helpful)
	{
		//针对玩家以及其宠物进行判断
		if (!IsPlayerClass())
		{
			return true;
		}

		switch (pk_type)
		{
		default:
		case PT_MAFIA://暂时把帮派基地规则和大世界一样
		case PT_BIG_WORLD:
		case PT_BIG_WORLD_PK:
		{
			if (ack_msg.attacker_info.attacker_mode & (attacker_info_t::AM_PVP_DUEL))
			{
				//决斗状态一律有效
				ack_msg.attacker_info.enemy_faction = 0xFFFFFFFF;
			}
			else
			{
				if (attacker.IsPlayer())
				{
					if (attacker.id == Parent()->ID.id)
					{
						return false;    // 如果是自己直接返回失败
					}
					// 攻击者为玩家
					if (GetPvPProtectFlag())
					{
						return false;    //如果处于PK保护状态则不被攻击
					}

					if (ainfo.team.id > 0 && ainfo.team == Parent()->team)
					{
						return false;    //不能打队友
					}

					//不是AM_PVP_ENABLE状态,安全区
					if (!(ainfo.attacker_mode & attacker_info_t::AM_PVP_ENABLE))
					{
						return false;
					}
					if (GetRegionSetting() & RS_SANCTUARY || ainfo.attacker_mode & attacker_info_t::AM_PVP_SANCTUARY)
					{
						return false;
					}

					if (ainfo.family_id > 0 && ainfo.family_id == GetParent()->family_id)
					{
						return false;    //不能打兄弟
					}
					if (ainfo.spouse_id > 0 && ainfo.spouse_id == GetParent()->spouse_id)
					{
						return false;    ////不能打家属
					}

					if (ainfo.extend_faction & gobject::EXTEND_FACTION_CHARIOT)
					{
						if (!(Parent()->extend_faction & gobject::EXTEND_FACTION_CHARIOT_ENEMY))
						{
							return false;
						}
					}

					//阵营相符,阵营对打
					if (GetFaction() & ainfo.enemy_faction)
					{
						ack_msg.attacker_info.enemy_faction = 0xFFFFFFFF;
						//TrySelect(msg.source);
						return true;
					}
					//玩家可以直接攻击反击的人
					if (IsVictim(msg.source))
					{
						ack_msg.attacker_info.enemy_faction = 0xFFFFFFFF;
						//TrySelect(msg.source);
						return true;
					}
					//以下依照个人设置
					if (ainfo.pk_setting_a == PS_PEACE || ainfo.pk_setting_a == PS_ALLIANCE)
					{
						return false;//和平模式，不能打人
					}
					else if (ainfo.pk_setting_a == PS_MAFIA)
					{
						if (ainfo.mafia_id > 0 && ainfo.mafia_id == GetParent()->mafia_id)
						{
							return false;//不能同门相残
						}
					}
					else if (ainfo.pk_setting_a == PS_CRIME)
					{
						if (GetFaction() & EXP_CAMPMASK_RED_NAME || GetFaction() & EXP_CAMPMASK_YELLOW_NAME)
						{
							//TrySelect(msg.source);
							return true;//为名除害,只可以杀红名和黄名玩家
						}
						else
						{
							return false;
						}
					}
					else if (ainfo.pk_setting_a == PS_KILL || ainfo.pk_setting_a == PS_TEAM)
					{
						//杀戮模式，想杀就杀
					}
					else if (ainfo.pk_setting_a == PS_COUNTRY)
					{
						if (FACTION_MASK_TO_NATION_ID(ainfo.faction) == FACTION_MASK_TO_NATION_ID(GetFaction()))
						{
							return false;
						}
					}
					else
					{
						//非法模式
						return false;
					}
					//非安全区，可以pk，但是要加pk值
					ack_msg.attacker_info.attacker_mode |= attacker_info_t::AM_PVP_INVADER;
				}
				else if (attacker.IsNPC())
				{
					// 阵营敌对，可以攻击
					if ((GetFaction() & ainfo.enemy_faction))
					{
						ack_msg.attacker_info.enemy_faction = 0xFFFFFFFF;
					}
					else
					{
						return false;
					}
				}
			}
		}
		break;

		case PT_BATTLE:
		{
			//可以在战场中做安全区
			if (GetRegionSetting() & RS_SANCTUARY || ainfo.attacker_mode & attacker_info_t::AM_PVP_SANCTUARY)
			{
				return false;
			}

			if ((GetFaction() & ainfo.enemy_faction))
			{
				ack_msg.attacker_info.enemy_faction = 0xFFFFFFFF;
			}
			else
			{
				return false;
			}
		}
		break;

		case PT_UNDERGROUND:
		{
			if (ainfo.team.id > 0 && ainfo.team == Parent()->team)
			{
				return false;    //不能打队友
			}
		}
		break;

		case PT_DEAD:
		{
			return true;
		}
		break;
		}
		//试着选择对象
		//TrySelect(msg.source);
	}
	else
	{
		// 增益技能
		if (attacker.id == Parent()->ID.id)
		{
			return true;    //如果是自己则直接返回成功
		}
		if (attacker.IsPlayer())
		{
			//处于决斗状态谁也不能加,这个要在里面判断,因为>有宠物问题，上面已经返回了?
			if (IsDuelMode())
			{
				return false;
			}
			if (!(ack_msg.attacker_info.attacker_mode & attacker_info_t::AM_PVP_ENABLE) &&
			        GetSpecialPkState())
			{
				//如果自己处于PVP状态 并且施放者处于PK保护状态，则不能使用技能
				////防止安全区或者小号不停给pk中的人加加 FIXME: 为啥不允许这个?
				return false;
			}
		}
		//笑傲的pk规则,不会把任何人拉下水了
		if (!(ainfo.friend_faction & GetFaction()))
		{
			return false;
		}
	}
	return true;
	*/
}

void gcreature_imp::ResetComboTimer()
{
	//_es_man.UpdateStatus(ESI_COMBO, SECOND_TO_TICK(NORMAL_COMBO_LAST_TIME));
}

void gcreature_imp::ResetHitTimer(int tick)
{
	/*if (!tick)
	{
		_es_man.RemoveStatus(ESI_HIT);
		return;
	}
	_es_man.UpdateStatus(ESI_HIT, tick);*/
}

void gcreature_imp::ResetSuspendTimer(int tick)
{
	//_es_man.UpdateStatus(ESI_SUSPEND, tick);
}

void gcreature_imp::SetInvincebleExcept(bool invinceble, const XID& except)
{
	_invinceble_except = invinceble;
	_invinceble_except_id = except;
}

void gcreature_imp::OnPropChanged(int idx)
{
	//属性变化后被同步调用
	//这里一定不能做导致属性变化的同步操作, 如果有需要, 可以用消息异步实现
	/*
	if (idx == GPROP_INDEX(Pozhan))
	{
		if (_prop.GetMPPozhan() == _prop.GetMPMaxPozhan())
		{
			object_interface oif(this);
			_skill.OnPozhanFull(oif);
		}
	}
	*/
}

void gcreature_imp::ResetChuPozhanTimer(int tick)
{
	//_es_man.UpdateStatus(ESI_CHUPOZHAN, tick);
}

void gcreature_imp::SetCanBeHitMask(unsigned int m)
{
	_parent->can_be_hit_mask = m;
}

void gcreature_imp::ResetCanBeHitMask()
{
	_parent->can_be_hit_mask = _init_can_be_hit_mask;
}

void gcreature_imp::OnControlEnd(int from_type)
{
	_action_man.OnControlEnd();
}

void gcreature_imp::OnControlChange(int old_type, int new_type)
{
	if (!old_type && new_type)
	{
		OnControlStart(new_type);
	}
	if (old_type && !new_type)
	{
		OnControlEnd(old_type);
	}
	GetParent()->control_state = new_type;
}

void gcreature_imp::SetInWeak(bool b)
{
	//GetParent()->in_weak = b;
}

bool gcreature_imp::IsInWeak() const
{
	return false;
	//return GetParent()->in_weak;
}

float gcreature_imp::GetSpeed(bool is_run) const
{
	if (IsPelt())
	{
		return _prop.GetPeltSpeed();
	}
	if (Parent()->CheckObjectState(gobject::STATE_GHOST))
	{
		if (GetWinterProjectP() && GetWinterProjectP()->IsActive(this))
		{
			return winter_project_config::GetInstance().ghost_speed;
		}
		return GLOBAL_CONFIG.player_ghost_run_speed;
	}
	return is_run ? _prop.GetRunSpeed() : _prop.GetWalkSpeed();
}

float gcreature_imp::GetWanderSpeed() const
{
	return _prop.GetWanderSpeed();
}
void gcreature_imp::GroupSpeak(int group_id)
{
	const system_speak_group *cfg = gmatrix::GetInstance().GetSpeakGroup(group_id);
	if (!cfg || cfg->group_id != group_id)
	{
		return;
	}
	int delay = 0;
	for (abase::vector<system_speak_group_entry>::const_iterator it = cfg->speak_list.begin(); it != cfg->speak_list.end(); ++it)
	{
		delay += it->delay;
		if (delay == 0)
		{
			SystemSpeak(it->speak_id);
		}
		else
		{
			int delay_tick = MILLISEC_TO_TICK(delay);
			int next_delay = 0;
			if (delay_tick >= MAX_MESSAGE_LATENCY_TICK)
			{
				next_delay = delay_tick - MAX_MESSAGE_LATENCY_TICK + 1;
				delay_tick = MAX_MESSAGE_LATENCY_TICK - 1;
			}
			gmatrix::GetInstance().LazySendTo2(GM_MSG_SYSTEM_SPEAK, _parent, _parent->ID, it->speak_id, next_delay, delay_tick);
		}
	}
}

void gcreature_imp::SetOverwhelmingMode(bool flag)
{
	_attack.SetOverwhelmingMode(flag);
	if (GetOverwhelmingMode() != Parent()->CheckObjectState(gobject::STATE_OVERWHELMING2))
	{
		if (GetOverwhelmingMode())
		{
			Parent()->SetObjectState(gobject::STATE_OVERWHELMING2);
		}
		else
		{
			Parent()->ClrObjectState(gobject::STATE_OVERWHELMING2);
		}
		Runner()->update_object_state();
	}
}

void gcreature_imp::AddPropertyByCdReduLevel()
{
	int64_t limit_value = gmatrix::GetInstance().GetCdRedulevelLimit(GetProf());
	if (limit_value >= 0)
	{
		int cdredulevel = GetCDReduLevel();
		int radio = 0;
		if (cdredulevel >= limit_value)
		{
			int cur = (cdredulevel - limit_value) / 12;
			radio = cur - _last_cdredulevel_radio;
			if (radio == 0)
			{
				return;
			}
			_last_cdredulevel_radio = cur;
		}
		else
		{
			radio = -_last_cdredulevel_radio;
			_last_cdredulevel_radio = 0;
		}

		if (radio == 0)
		{
			return;
		}

		SLOG(DEBUG, "AddPropertyByCdReduLevel").P("role_id", Parent()->ID.id).P("last_radio", _last_cdredulevel_radio).P("radio", radio).P("prof", GetProf()).P("limit_value", limit_value).P("cdredulevel", cdredulevel);

		// 生命
		int val = gmatrix::GetInstance().GetCdReduLevelAddHp();
		GetProperty().MODIFY_PROP_BY_NAME(pointHP, radio * val, creature_prop::CPM_CUR);
		// 物攻
		val = gmatrix::GetInstance().GetCdReduLevelAddPhyAtk();
		GetProperty().MODIFY_PROP_BY_NAME(pointPhyAtk, radio * val, creature_prop::CPM_CUR);
		// 魔攻
		val = gmatrix::GetInstance().GetCdReduLevelAddMagAtk();
		GetProperty().MODIFY_PROP_BY_NAME(pointMagAtk, radio * val, creature_prop::CPM_CUR);
	}
}

int gcreature_imp::GetCDReduLevelLimitByProf() const
{
	return gmatrix::GetInstance().GetCdRedulevelLimit(GetProf());
}

int gcreature_imp::CalcProperty(unsigned char prof, int type, int p1, int p2, int p3, int p4, int p5, int p6) const
{
	return property_manager::GetInstance().GetProperty(prof, type, p1, p2, p3, p4, p5, p6);
}

void gcreature_imp::FormPetMasterData(pet_master_prop& data)
{
	data.pvp_protect_flag = false;//_pvp_protect_flag;
	//data.family_id = GetFamilyID();
	data.mafia_id = GetMafiaID();
	//data.native_zoneid = GetNativeZoneID();
	data.level = GetLevel();
	//data.wallow_level = _wallow_level;
	data.team = GetParent()->team;
	//data.lid = GetLinkID();
	//data.duel_target = _duel_target;
	data.pk_setting = GetPKSetting();
	data.invader_counter = GetPKValue();
	data.faction = GetFaction();
	data.speed = GetSpeed(true);
	//data.married = IsMarried();
	data.spouse_id = GetSpouseID();
}

void gcreature_imp::PartnerCastSkill(int partner_type, int partner_id, skill_id_t skill_id, const XID& target)
{
	int level = _skill.GetSkillLevel(skill_id);
	if (level <= 0)
	{
		return;
	}
	if (!WMSKILL::SkillWrapper::IsInstant(skill_id))
	{
		return;
	}

	_whoCastSkill = partner_type;
	object_interface oif(this);
	_skill.InstantSkill(oif, skill_id, level, 0, WMSKILL::CAST_PRIOR_ID, target, Parent()->pos/*target_pos*/, 0);
	_whoCastSkill = 0;
}

void gcreature_imp::PartnerCastSkill_AutoDelay(int partner_type, int partner_id, const std::vector<skill_id_t>& skill_arr, const XID& target,
        std::function<void(int, bool)>&& callback)
{
	if (skill_arr.empty())
	{
		return;
	}
	//内部调用，这里就不检查合法性了
	_whoCastSkill = partner_type;
	object_interface oif(this);
	int id = skill_arr[0];
	int level = _skill.GetSkillLevel(id);
	char ret = _skill.InstantSkill(oif, id, level, 0, WMSKILL::CAST_PRIOR_ID, target, Parent()->pos/*target_pos*/, 0);
	callback(0, ret == 0);
	if (ret == 0)
	{
		int msg_delay_tm = _skill.GetMsgDelayTm();
		for (size_t i = 1; i < skill_arr.size(); ++i)
		{
			int id = skill_arr[i];
			level = _skill.GetSkillLevel(id);
			ret = _skill.InstantSkill(oif, id, level, 0, WMSKILL::CAST_PRIOR_ID, target, Parent()->pos/*target_pos*/, 0, &msg_delay_tm);
			callback(i, ret == 0);
		}
	}
	_whoCastSkill = 0;
}

void gcreature_imp::BattleLock(int seconds, bool only_overwhelming)
{
	class BattleLockCB : public timeout_ctrl::callback
	{
		virtual int OnTimeout(gcreature_imp *pImp, int index)
		{
			if (pImp->IsPlayerClass())
			{
				if (((gplayer_imp *)pImp)->GetParent()->login_state == gplayer::PLS_LOGIN_OK)
				{
					gplayer_imp *_imp = (gplayer_imp *)pImp;
					_imp->BattleLock(0);
				}
			}
			else
			{
				pImp->BattleLock(0);
			}
			return -1;
		}
		virtual bool NeedRelease()
		{
			return false;
		}
	};
	static BattleLockCB blcb;
	object_state_t temp = Parent()->object_state;
	__PRINTF("battle lock :%ld with %d seconds, state:%ld\n", Parent()->ID.id, seconds, temp & gobject::STATE_ROOT);

	if (seconds <= 0)   //解锁
	{
		if (!GetTimeout().IsChecking())
		{
			GetTimeout().Remove(TM_IDX_BATTLE_LOCK_PLAYER);
		}

		Parent()->ClearRootMode();
		SetOverwhelmingMode(false);
	}
	else if (seconds > 0)   //锁定
	{
		if (only_overwhelming)
		{
			SetOverwhelmingMode(true);
		}
		else
		{
			//设置技能状态，客户端显示
			//BreakAction();
			SetOverwhelmingMode(true);
			Parent()->SetRootMode();
		}
		GetTimeout().Insert(TM_IDX_BATTLE_LOCK_PLAYER, seconds, &blcb, true);
	}
	_runner->update_object_state();
}

void gcreature_imp::OnAIPerformSkill(int skill_id, int perform_idx, int tick, const A3DVECTOR3& target_pos, float target_body_size)
{
	float move_dist = SkillConfig::GetInstance().GetSkillMove(skill_id, perform_idx);
	move_dist *= GetSceneImp()->GetTemplate()->rootmotion_scale;
	if (move_dist < 0.01 && move_dist > -0.01)
	{
		return;
	}
	A3DVECTOR3 dir = Parent()->GetDirection();

	if (target_body_size > 0)
	{
		//避免穿过目标
		A3DVECTOR3 v1 = target_pos - Parent()->pos;
		float l1_2 = v1.SquaredMagnitude(); //和目标的连线，三角形最长边的平方
		float l2 = DotProduct(v1, dir); //l1在位移方向上的投影，直角边
		if (l2 > 0)//目标和位移方向相同，可能碰撞
		{
			float l3_2 = l1_2 - l2 * l2; //另外一条直角边的平方
			float min = Parent()->body_size + target_body_size + 0.1;
			if (l3_2 < min * min)//目标和位移线路距离较近，可能碰撞
			{
				float r = sqrt(min * min - l3_2);
				if (move_dist > l2 - r)
				{
					move_dist = l2 - r;
				}
			}
		}
	}
	A3DVECTOR3 p = Parent()->pos + dir * move_dist;
	unsigned short _;
	SkillMove(p, _, false, true, true, CHECK_Y_DIFF_STRICT);
	int tm = TICK_TO_MILLISEC(tick);
	if (tm > 0)
	{
		unsigned short client_speed = move_dist * 1000 / tm * 256.0f + 0.5f;
		dir_t ndir = a3dvector_to_dir(dir);
		Runner()->object_move(p, GP_MOVE_ROOTMOTION | GP_MOVE_DIR_DIFF, ndir, ndir, 0, client_speed, 0, 0, 0, 0);
	}
}

void gcreature_imp::OnAddonActivate(const addon_data_spec *spec)
{
	//在升级过程中，不允许激活附加属性，否则影响与等级相关附加属性计算
	ASSERT(false == _in_levelup);
	ASSERT(false == _in_pre_levelup);

	if (_in_post_levelup)
	{
		return;
	}

	_addon_spec[spec]++;
}
void gcreature_imp::OnAddonDeactivate(const addon_data_spec *spec)
{
	//在升级过程中，不允许激活附加属性，否则影响与等级相关附加属性计算
	ASSERT(false == _in_levelup);
	ASSERT(false == _in_post_levelup);

	if (_in_pre_levelup)
	{
		return;
	}

	auto it = _addon_spec.find(spec);
	if (it != _addon_spec.end())
	{
		it->second --;
		if (it->second <= 0)
		{
			_addon_spec.erase(it);
		}
	}
}
void gcreature_imp::BeforeLevelUp()
{
	ASSERT(false == _in_pre_levelup);
	ASSERT(false == _in_levelup);
	ASSERT(false == _in_post_levelup);

	_in_pre_levelup = true;

	creature_enhance_if cei(this);
	for (auto& pair : _addon_spec)
	{
		int count = pair.second;
		while (count--)
		{
			pair.first->handler->Deactivate(pair.first, cei, pair.first->data, 0, 0);
		}
	}

	_in_pre_levelup = false;
	_in_levelup = true;
}
void gcreature_imp::AfterLevelUp()
{
	ASSERT(false == _in_post_levelup);
	ASSERT(true  == _in_levelup);

	_in_levelup = false;
	_in_post_levelup = true;

	creature_enhance_if cei(this);
	for (auto& pair : _addon_spec)
	{
		int count = pair.second;
		while (count--)
		{
			pair.first->handler->Activate(pair.first, cei, pair.first->data, 0, 0);
		}
	}
	_in_post_levelup = false;
}

void gcreature_imp::TryFastReGenHP()
{
	if (GetRealSceneImp()->IsAllowFastHPGen() && GetHP() < GetProperty().GetHPMax() &&
	        GetProperty().GetHPReserved() && !GetParent()->CheckObjectServerState(gobject::SERVER_STATE_FAST_REGEN_HP)
	        && !GetParent()->IsNearDeath())
	{
		GetParent()->SetObjectServerState(gobject::SERVER_STATE_FAST_REGEN_HP);
		LazySendTo(GM_MSG_FAST_REGEN_HP, Parent()->ID, 0, MICROSEC_TO_TICK(10));
	}
}

bool gcreature_imp::HasExtendState(unsigned int buff_id)
{
	return GetParent()->HasExtendState(buff_id);
}

void gcreature_imp::Jump2SubobjPos(int tid, bool remove)
{
	auto it = _subobject_map.find(tid);
	if (it == _subobject_map.end())
	{
		return;
	}
	std::list<xid_time_t>& list = it->second;
	if (list.empty())
	{
		return;
	}
	XID& xid = list.begin()->xid;
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(xid, info))
	{
		return;
	}
	if (!info.Check(this->Parent()))
	{
		return;
	}
	info.pos.y = fmax(GetSceneImp()->GetTerrainHeightAt(info.pos.x, info.pos.z), info.pos.y);//可以比地形高
	if (!GetSceneImp()->IsPosReachable(info.pos, true))
	{
		return;
	}
	CancelAction();
	if (StepMove(info.pos - _parent->pos, false, 0, true))
	{
		//Runner()->notify_pos(_parent->scene_tag, _parent->pos, _parent->dir, _move_ctrl.GetNextStamp(), 0);
		Runner()->object_move(_parent->pos, GP_MOVE_POS_NOTIFY | GP_MOVE_DIR_DIFF | GP_MOVE_STOP | GP_MOVE_SEND_SELF, 0, _parent->dir, 0, 0xFFFF, 0, 0, 0, 0);
		if (remove)
		{
			SendTo(GM_MSG_SUBOBJ_DISAPEAR, xid, 0);
		}
	}
}

bool gcreature_imp::GetSubobjPos(int tid, A3DVECTOR3& pos) const
{
	auto it = _subobject_map.find(tid);
	if (it == _subobject_map.end())
	{
		return false;
	}
	const std::list<xid_time_t>& list = it->second;
	if (list.empty())
	{
		return false;
	}
	const XID& xid = list.begin()->xid;
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(xid, info))
	{
		return false;
	}
	if (!info.Check(this->Parent()))
	{
		return false;
	}
	pos = info.pos;
	return true;
}

void gcreature_imp::GetSubobjXidAndPos(int tid, std::map<XID, A3DVECTOR3>& m, float& body_size) const
{
	auto it = _subobject_map.find(tid);
	if (it == _subobject_map.end())
	{
		return;
	}
	const std::list<xid_time_t>& list = it->second;
	for (auto a : list)
	{
		const XID& xid = a.xid;
		object_info info;
		if (gmatrix::GetInstance().QueryObject(xid, info))
		{
			if (info.Check(Parent()))
			{
				m[xid] = info.pos;
				body_size = info.body_size;
			}
		}
	}
}

void gcreature_imp::SendSubobjPKSetting(int pk_setting)
{
	for (auto& a : _subobject_map)
	{
		for (auto& b : a.second)
		{
			SendTo(GM_MSG_PK_SETTING_CHANGE, b.xid, pk_setting);
		}
	}
}

bool gcreature_imp::HasEnemyTarget() const
{
	return CheckCanAttack(_cur_target);
}

void gcreature_imp::SetSilentAbsoluteSeal(bool isSeal)
{
	if (isSeal)
	{
		IncActionSeal(SEAL_SILENT_ABSOLUTE);
	}
	else
	{
		DecActionSeal(SEAL_SILENT_ABSOLUTE);
	}
}

void gcreature_imp::ChangeModel(int tid, int attacker_newid)
{
	ResetModel();
	ClearAction();
	GetParent()->transform_tid = tid;
	GetParent()->transform_attacker_newid = attacker_newid;
	_parent->SetObjectState(gobject::STATE_CHANGE_MODE);
	_runner->transform_state(tid, 1, attacker_newid);
	_skill.ModifyImmuneMask(WMSKILL::TYPEMASK_CHANGE_MODEL, true);
}

void gcreature_imp::ResetModel()
{
	if (GetParent()->transform_tid == 0)
	{
		return;
	}
	ClearAction();
	GetParent()->transform_tid = 0;
	GetParent()->transform_attacker_newid = 0;
	_parent->ClrObjectState(gobject::STATE_CHANGE_MODE);
	_runner->transform_state(0);
	_skill.ModifyImmuneMask(WMSKILL::TYPEMASK_CHANGE_MODEL, false);
}

void gcreature_imp::ClearPropSystemAndSkill()
{
	if (IsPlayerClass() && !IsRoamIn())
	{
		return;//要清数据，必须在跨服
	}

	//重置cd转化血量缓存
	_last_cdredulevel_radio = 0;

	//收回宠物
	player_guard *guard = GetGuardP();
	if (guard)
	{
		guard->SummonGuard(this, 0, 0);
		for (int i = 0; i < GUARD_SLOT_COUNT; ++i)
		{
			guard->TakeOutGuard(this, i, false);
		}
	}

	//收回伙伴
	player_retinue_manager *retinue = GetRetinuesP();
	if (retinue)
	{
		for (int i = 0; i <= ASSIST_COMBAT_RETINUE_NUM; ++i)
		{
			retinue->RemoveCombatRetinue(i);
		}
	}

	//收回双生体和机甲
	if (GetTwinP())
	{
		GetTwinP()->TryDelete();
	}
	if (GetMechP())
	{
		GetMechP()->OnLeaveScene();
	}


	//收回继承者
	if (GetHeirControlP())
	{
		std::vector<uint64_t> _;
		GetHeirControlP()->SetBattleList(_);
	}

	//七宗罪属性和技能
	if (GetSevenCrimeP())
	{
		GetSevenCrimeP()->ClearSkillAndProp();
	}
	//回收分身
	for (int i = 0 ; i < PLAYER_REPLISOME_MAX; ++i)
	{
		if (GetReplisomeP(i))
		{
			GetReplisomeP(i)->TryDelete();
		}
	}
	if (GetProf12P())
	{
		GetProf12P()->TryDelete();
	}

	gplayer_imp *pPlayer = dynamic_cast<gplayer_imp *>(this);
	if (pPlayer)//player_npc没有child_manager
	{
		//删除已收集的龙语
		pPlayer->GetEnhance().ClearLongyuCollection();
		pPlayer->GetEnhance().SendLongyuCollection(pPlayer);

		//pPlayer->GetEnhance().ClearLonghunCollection();
		pPlayer->GetEnhance().SendLonghunCollection(pPlayer);
		pPlayer->GetEnhance().SendClient(pPlayer);

		//收回孩子
		pPlayer->GetChildMan().DetachAll(pPlayer);
		int summon_idx = pPlayer->GetChildMan().GetSummonIndex();
		pPlayer->GetChildMan().Recall(pPlayer, summon_idx);

		//收回龙裔
		pPlayer->GetDragonborn().DetachAll(pPlayer);
		pPlayer->GetDragonborn().SetParentData(pPlayer, 0, 0);
	}


	//清除赋能和武器光效
	GetParent()->enhance_gfx_suit_id = 0;
	GetParent()->gem_gfx_suit_id = 0;
	GetParent()->ClrObjectState(gplayer::STATE_STAR_SUIT);
	Runner()->send_suit_info(GetParent()->enhance_gfx_suit_id, GetParent()->gem_gfx_suit_id, false);

	//删除所有技能
	object_interface oif(this);
	GetSkill().RemoveAllSkill(oif);

	//清除所有buff
	GetFilterMan().ClearAllFilterExcept(0);

}

void gcreature_imp::NormalizePropAndSkill(int max_equip_slot_size, int base_backpack_size, int prop_tab_id, bool keep_prof)
{
	if (IsPlayerClass() && !IsRoamIn())
	{
		return;//要清数据，必须在跨服
	}

	ClearPropSystemAndSkill();

	gplayer_imp *pPlayer = dynamic_cast<gplayer_imp *>(this);
	if (pPlayer)
	{
		//复活
		if (pPlayer->Parent()->IsZombie())
		{
			pPlayer->ReviveStandFree(true);
		}
	}

	//清空身上的装备和背包
	player_inventory *inventory = GetInventoryP();
	if (inventory)
	{
		if (IsPlayerClass())
		{
			inventory->Clear();//保留player_npc的装备
		}
		int equip_slot_add = max_equip_slot_size - inventory->GetInventory(GNET::IL_EQUIPMENT_1).GetBaseSize() + 1;
		if (equip_slot_add > 0)
		{
			inventory->GetInventory(GNET::IL_EQUIPMENT_1).SetExtraSize(equip_slot_add);//新的装备位
		}
		inventory->GetBackpack().SetBaseSize(base_backpack_size);
		for (int location = GNET::IL_EQUIPMENT_1; location < GNET::IL_COUNT; ++location)
		{
			SendClientInvData(location);
		}
	}

	//重建所有属性
	creature_prop new_prop(this);
	if (keep_prof)
	{
		new_prop.SetProf(GetProf()); //职业保持
		new_prop.SetLevel(GetProperty().GetLevel());//等级保持
	}
	else
	{
		new_prop.SetProf(PROFTYPE_BLADE);
		new_prop.IncLevel();//等级为1
	}
	GetProperty() = new_prop;
	prof_template_manager::GetInstance().SetProfBaseProperty(0, 1, GetProperty(), prop_tab_id);
	GetProperty().Update();
	GetProperty().RenewAll();
	SetHP(GetHPMax());
	GetProperty().SET_PROP_BY_NAME(HPReserved, 0, creature_prop::CPM_CUR);

	//同步属性给客户端
	packet_tla_wrapper ar(2048);
	if (GetProperty().WRAPPER_CHANGED_PROP(data_ClientData, ar))
	{
		Runner()->property_update(ar);
	}
	GetProperty().ClearChangedSet();

	if (pPlayer)
	{
		if (pPlayer->GetParent()->weapon_hide)
		{
			pPlayer->GetParent()->weapon_hide = false;
			PB::gp_common_operation_re pb;
			pb.set_rid(pPlayer->GetParent()->ID.id);
			pb.set_error_code(S2C::ERR_SUCCESS);
			pb.set_op(PB::gp_common_operation::WEAPON_SHOW);
			pPlayer->Runner()->RegionSend<S2C::CMD::PBS2C>(pb);
		}

		pPlayer->GetParent()->fashion_gfx_modify_mask = 0;
		pPlayer->Runner()->fashion_gfx_modify_mask(0);

		pPlayer->Runner()->equip_info_change(0, 0xffffffff, NULL);
	}
	SLOG(FORMAT, "NormalizePropAndSkill").P("rid", Parent()->ID.id);
}

void gcreature_imp::Jump2FarthestSubobjPos(int tid, bool remove)
{
	auto it = _subobject_map.find(tid);
	if (it == _subobject_map.end())
	{
		return;
	}
	std::list<xid_time_t>& list = it->second;
	if (list.empty())
	{
		return;
	}
	A3DVECTOR3 _farthest_pos(_parent->pos);
	XID _farthest_xid;
	float _farthest_distance = 0 ;
	for_each(list.begin(), list.end(), [ =, &_farthest_pos, &_farthest_xid, &_farthest_distance](const xid_time_t& xid_time)
	{
		const XID& xid = xid_time.xid;
		object_info info;
		if (!gmatrix::GetInstance().QueryObject(xid, info))
		{
			return;
		}
		if (!info.Check(Parent()))
		{
			return;
		}
		A3DVECTOR3 pos = info.pos;//不改变子物体的坐标
		pos.y = fmax(GetSceneImp()->GetTerrainHeightAt(pos.x, pos.z), pos.y);
		if (!GetSceneImp()->IsPosReachable(pos, true))
		{
			return;
		}
		A3DVECTOR3 target_pos = pos;
		GetSceneImp()->GetLastReachablePos(Parent()->pos, target_pos, pos, CHECK_Y_DIFF_LOOSE);
		if (std::abs(target_pos.MagnitudeH() - pos.MagnitudeH()) > 1 + 1e-6)
		{
			return;
		}
		float distance = horizontal_distance(pos, _parent->pos);
		if (distance > _farthest_distance)
		{
			_farthest_distance = distance;
			_farthest_xid = xid;
			_farthest_pos = pos;
		}
	});
	if (!_farthest_xid.IsValid())
	{
		return;
	}
	CancelAction();
	if (StepMove(_farthest_pos - _parent->pos, false, 0, false))
	{
		Runner()->object_move(_parent->pos, GP_MOVE_POS_NOTIFY | GP_MOVE_DIR_DIFF | GP_MOVE_STOP | GP_MOVE_SEND_SELF, 0, _parent->dir, 0, 0xFFFF, 0, 0, 0, 0);
		if (remove)
		{
			SendTo(GM_MSG_SUBOBJ_DISAPEAR, _farthest_xid, 0);
		}
	}
}
size_t gcreature_imp::GetSubObjectCount(int tid) const
{
	auto it = _subobject_map.find(tid);
	if (it != _subobject_map.end())
	{
		return  it->second.size();
	}
	return 0;
}

int gcreature_imp::CalcMasterDmgAdd()
{
	float ratio = (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd2) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd4) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd5) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd6) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd7) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd8) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd12) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd16) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd19) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(DamAdd20) * 0.001f)
	              * (1 + _prop.GetGProperty()->PROP_NAME_GET(SpecialDamAdd) * 0.001f) - 1;
	return (ratio > 1e-6) ? (1000 * ratio) : 0;
}

void gcreature_imp::OnMasterSkillCast(int skill_id, bool can_attack)
{
	if (!_p_summon_npc_map || _p_summon_npc_map->empty())
	{
		return;
	}
	property_template::data_SummonNpcMasterProp tmp_prop;
	memset(&tmp_prop, 0, sizeof(tmp_prop));
	_prop.ExportTo(&tmp_prop);
	int master_dam_add = CalcMasterDmgAdd();
	for (auto& summon_npc : *_p_summon_npc_map)
	{
		auto *p_npc_templ = summon_npc.second.p_templ;
		if (!p_npc_templ)
		{
			continue;
		}
		auto& summon_npc_listen_skill_list = p_npc_templ->listen_skill_list;
		if (summon_npc_listen_skill_list.find(skill_id) != summon_npc_listen_skill_list.end())
		{
			MSG msg;
			if (can_attack)
			{
				BuildMessage2(msg, GM_MSG_MASTER_CAST_SKILL, summon_npc.first, _cur_target, A3DVECTOR3(), skill_id, master_dam_add, &tmp_prop, sizeof(property_template::data_SummonNpcMasterProp));
			}
			else
			{
				BuildMessage2(msg, GM_MSG_MASTER_CAST_SKILL, summon_npc.first, XID(), A3DVECTOR3(), skill_id, master_dam_add, &tmp_prop, sizeof(property_template::data_SummonNpcMasterProp));
			}
			gmatrix::GetInstance().SendMessage(msg);
		}
	}
}

void gcreature_imp::SyncSummonNpcMasterInfo()
{
	if (!_p_summon_npc_map || _p_summon_npc_map->empty())
	{
		return;
	}
	pet_master_prop data;
	FormPetMasterData(data);
	for (auto& kv : *_p_summon_npc_map)
	{
		SendTo(GM_MSG_PET_MASTER_INFO, kv.first, 0, &data, sizeof(data));
	}
}

int gcreature_imp::ChangeBodyFakeScale(int body_fake_scale_new)
{
	const int body_fake_scale_old = GetParent()->body_fake_scale;
	if (body_fake_scale_old != body_fake_scale_new)
	{
		GetParent()->body_fake_scale = body_fake_scale_new;
		Runner()->notify_body_fake_scale_change();
	}
	return body_fake_scale_old;
}