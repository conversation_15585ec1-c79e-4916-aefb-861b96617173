
#include <common/protocol_s2c.h>
#include "gprotoc/HONEY_GARDEN_OP.pb.h"
#include "gprotoc/gp_object_buff.pb.h"
#include "gprotoc/player_mech.pb.h"
#include "gprotoc/gp_npc_bubble_say.pb.h"
#include "gprotoc/gp_garden_npc_update.pb.h"
#include "gprotoc/gp_npc_enter_scene.pb.h"
#include "gprotoc/player_heir.pb.h"
#include "gprotoc/DRI_TYPE.pb.h"
#include "gprotoc/npc_definite_info_common.pb.h"
#include "gprotoc/fashion_detail.pb.h"
#include "gprotoc/player_twin.pb.h"
#include "gprotoc/gp_damage_rank_info.pb.h"
#include "gprotoc/gp_npc_info.pb.h"
#include "gprotoc/reputation.pb.h"
#include "gprotoc/gp_runinto_npc.pb.h"
#include "gprotoc/gp_runinto_npc_res.pb.h"
#include "gprotoc/gp_transform_state.pb.h"
#include "gprotoc/npc_extend_state.pb.h"
#include "gprotoc/gp_scene_special_object_info.pb.h"
#include "gprotoc/gp_npc_affix_data_chg.pb.h"
#include "gprotoc/gp_npc_definite_info.pb.h"
#include "gprotoc/data_MonsterEss.pb.h"
#include "gprotoc/ipt_remote_call.pb.h"
#include "gprotoc/gp_object_buff_personal.pb.h"
#include "gprotoc/gp_npc_enter_slice.pb.h"
#include "gprotoc/player_replisome.pb.h"
#include "gprotoc/buff_t.pb.h"
#include "gprotoc/gp_matter_interact_info.pb.h"

#include <common/protocol_imp.h>
#include <common/chat_data.h>
#include <iconv.h>
#include "npc.h"
#include "spawner/npc_spawner.h"
#include "class_table.h"
#include "service_manager.h"
#include "item_manager.h"
#include "net_message.h"
#include "mine_matter.h"
#include "task/TaskTemplMan.h"
//#include "tizi_manager.h"
#include "object_grouping.h"
#include "ai/ai_mission.h"
#include "player.h"
#include "player_bot.h"
#include "npc_tasks.h"
#include "remote_call.h"
#include "interact_manager.h"
#include "idip_data_manager.h"
#include "career/career_template.h"
#include "ai/ai_policy.h"
#include "global_config.h"
#include "player_talent.h"
#include "player_pureblooded.h"
#include "combat_checker.h"

int NIGHT_SLEEP_GAME_DAY_BEGIN_HOUR	= 6;	//夜晚睡眠，游戏时间白天开始小时>=
int NIGHT_SLEEP_GAME_DAY_END_HOUR	= 22;	//夜晚睡眠，游戏时间白天结束小时<
enum END_NIGHT_SLEEP_REASON
{
	ENSR_NONE					= 0,
	ENSR_BY_DAY					= 1,		// 到白天了
	ENSR_ENEMY_ENTER_SIGHT		= 2,		// 主动怪，有人进入视野
	ENSR_BE_ATTACKED			= 3,		// 被攻击
	ENSR_DEATH					= 4,		// 死亡
};

int CORPS_BOSS_SCORE_DAMAGE_PLAYER_INFO_CLIENT_SIZE = 10;	// 社团boss积分伤害个人排行榜客户端显示
int CORPS_BOSS_SCORE_DAMAGE_CORPS_INFO_CLIENT_SIZE = 10;	// 社团boss积分伤害社团排行榜客户端显示


DEFINE_SUBSTANCE(gnpc_controller, controller, CLS_NPC_CONTROLLER)
DEFINE_SUBSTANCE(gnpc_dispatcher, dispatcher, CLS_NPC_DISPATCHER)
DEFINE_SUBSTANCE(gnpc_imp, gobject_imp, CLS_NPC_IMP)


void npc_template::LoadFromMonsterEssence(const MONSTER_ESSENCE& ess)
{
	memset(this, 0, sizeof(*this));
	tid = monster_tid = ess.id;

	star = ess.star_level;
	//ASSERT(ess.monster_prof >= 0 && ess.monster_prof < PROFTYPE_COUNT);
	prof = ess.monster_prof;
	if (ess.monster_prof >= PROFTYPE_COUNT)
	{
		prof = PROFTYPE_BIGWORLD_ELITE;    // 大地图精英怪
	}
	id_monster_base_prop = ess.id_monster_base_prop >= 0 ? ess.id_monster_base_prop : 0;
	level = ess.level;
	show_level = ess.show_level;
	body_size = ess.size;
	if (body_size < 0.5)
	{
		body_size = 0.5;
	}

	property_template::data_MonsterEss data_ess;
	memset(&data_ess, 0, sizeof(data_ess));

	data_ess.scalePhyAtk 		= (int)ess.external_dmg_min;
	data_ess.scalePhyDef		= (int)ess.external_defense;
//	data_ess.scalePhyCrit		= (int)ess.external_crit_rate;
//	data_ess.scalePhyCritDmg	= (int)ess.external_crit_addon_ratio;

	data_ess.scaleMagAtk		= (int)ess.internal_dmg_min;
	data_ess.scaleMagDef		= (int)ess.internal_defense;
//	data_ess.scaleMagCrit		= (int)ess.internal_crit_rate;
//	data_ess.scaleMagCritDmg	= (int)ess.internal_crit_addon_ratio;
//
//	data_ess.scaleIceDmg		= (int)ess.attrib_dmg[0];
//	data_ess.scaleFireDmg		= (int)ess.attrib_dmg[1];
//	data_ess.scalePoisonDmg		= (int)ess.attrib_dmg[2];

//	data_ess.scaleIceRes		= (int)ess.attrib_dmg_anti[0];
//	data_ess.scaleFireRes		= (int)ess.attrib_dmg_anti[1];
//	data_ess.scalePoisonRes		= (int)ess.attrib_dmg_anti[2];

	data_ess.scaleHP		= (int)ess.hp;
	data_ess.scaleMP		= (int)ess.mp[0];
//	data_ess.scaleRage		= (int)ess.mp[1];
//	data_ess.scalePozhan		= (int)ess.mp[2];
//	data_ess.scaleStamina		= (int)ess.mp[3];
	data_ess.scaleRunSpeed		= (int)ess.run_speed;
//	data_ess.scaleWalkSpeed		= (int)ess.walk_speed;

//	data_ess.state1Res		= (int)ess.attrib_state_anti[0];
//	data_ess.state2Res		= (int)ess.attrib_state_anti[1];
//	data_ess.state3Res		= (int)ess.attrib_state_anti[2];
//	data_ess.state4Res		= (int)ess.attrib_state_anti[3];
//	data_ess.state5Res		= (int)ess.attrib_state_anti[4];
//	data_ess.state6Res		= (int)ess.attrib_state_anti[5];
//	data_ess.state7Res		= (int)ess.attrib_state_anti[6];
//	data_ess.state8Res		= (int)ess.attrib_state_anti[7];

	prop_ess = data_ess;


	ASSERT(ess.ak_type >= 0 && ess.ak_type < EXPATKTYPE_COUNT);
	ak_type = (EXP_ATTACK_TYPE)ess.ak_type;
	ASSERT(ess.df_type >= 0 && ess.df_type < EXPDEFTYPE_COUNT);
	df_type = (EXP_DEFENCE_TYPE)ess.df_type;
	corpse_disapear_time = ess.disapear_time_ms / 1000;
	refresh_speed = ess.refresh_speed;
	monster_born_protect_time = ess.born_protect_time;

	faction = ess.faction;
	monster_faction = ess.monster_faction;
	monster_faction_ask_help = ess.monster_faction_ask_help;
	monster_faction_can_help = ess.monster_faction_can_help;

	wander_speed = ess.wander_speed;
	if (wander_speed < 0)
	{
		wander_speed = 0.5f;
	}
	if (wander_speed > 10)
	{
		wander_speed = 5.0f;
	}
	run_speed = ess.run_speed;	//只用于特定配置

	strategy = ess.id_strategy;
	id_monster_prop = ess.id_monster_prop;
	odds_monster_prop = ess.odds_monster_prop;

	aggressive_mode = ess.aggressive_mode;
	positive_value = ess.positive_value;
	if (positive_value < 0)
	{
		positive_value = 0;
	}
	if (positive_value > 100)
	{
		positive_value = 100;
	}
	positive_value /= 100;
	wander_time = ess.wander_time_ms / 1000;
	if (wander_time < WANDER_MIN_TIME)
	{
		wander_time = WANDER_MIN_TIME;
	}
	if (wander_time > WANDER_MAX_TIME)
	{
		wander_time = WANDER_MAX_TIME;
	}
	wander_range_min = ess.wander_range_min;
	if (wander_range_min < WANDER_MIN_RANGE)
	{
		wander_range_min = WANDER_MIN_RANGE;
	}
	wander_range_max = ess.wander_range_max;
	if (wander_range_max > WANDER_MAX_RANGE)
	{
		wander_range_max = WANDER_MAX_RANGE;
	}
	if (wander_range_min > wander_range_max)
	{
		wander_range_max = wander_range_min;
	}

	sight_range = ess.sight_range;
	if (sight_range >= MAX_NPC_SIGHT_RANGE)
	{
		sight_range = MAX_NPC_SIGHT_RANGE;
	}
	if (sight_range <= 1e-3)
	{
		sight_range = 30.f;    //如果视野范围为0，则设置为一个默认值
	}
	sight_angle = ess.sight_angle;
	if (sight_angle > 360)
	{
		sight_angle = 360;
	}
	can_night_sleep = ess.can_night_sleep;
	if (can_night_sleep)
	{
		squared_night_sleep_sight_range = ess.night_sleep_sight_range * ess.night_sleep_sight_range;
	}

	aggro_type = ess.aggro_type;
	aggro_gen_per_sec = ess.aggro_inc_persec;
	attack_nearest_one = ess.attack_nearest_one;
	aggro_gen_min_atk = ess.min_aggro_inc_default;
	aggro_gen_max_atk = ess.max_aggro_inc_default;

	patrol_mode = ess.patroll_mode;
	ai_policy_id = ess.common_strategy; //怪物策略ID  目前怪物没有策略逻辑了


	attack_player_strategy = ess.not_attack_player;
	random_walk_dist = ess.random_walk_dist;
	if (random_walk_dist < 1)
	{
		random_walk_dist = 1;
	}
	if (random_walk_dist > 10)
	{
		random_walk_dist = 10;
	}
	random_walk_interval = ess.random_walk_interval;
	if (random_walk_interval < 5)
	{
		random_walk_interval = 5;
	}
	if (random_walk_interval > 60)
	{
		random_walk_interval = 60;
	}
	max_chase_distance = ess.chase_max_distance;

	prob_battle_chase1 = ess.follow_attack_chance;
	prob_battle_cruise1 = ess.fight_wander_chance;
	time_battle_cruise1 = ess.fight_wander_period * 0.001f;
	delta_battle_cruise1 = ess.fight_wander_period_fluctuation * 0.001f;
	angle_cruise1 = ess.wander_angle;

	speak_id_on_death = ess.speak_id_on_death;
	speak_id_on_team_kill = ess.speak_id_on_team_kill;

	gen_auto_fit = ess.level_adapted_type;
	is_center_battle_monster = ess.is_center_server_monster;
	//技能相关
	//被动技能
	passive_skill.id = ess.id_pass_skill;
	passive_skill.level = ess.pass_skill_level;

	//怪物所会技能包
	int weight_adjust[EXP_MONSTER_CAST_SKILL_COUNT];
	memset(skill_seq_list, 0, sizeof(skill_seq_list));
	for (size_t i = 0; i < EXP_MONSTER_CAST_SKILL_COUNT; ++i)
	{
#ifdef USE_CONVEX
		if (ess.monsterskillseq.size() > i)
		{
			skill_seq_list[i].skillseq_id   = ess.monsterskillseq[i].skillseq_id;
			skill_seq_list[i].mask          = ess.monsterskillseq[i].mask;
			skill_seq_list[i].is_auto       = ess.monsterskillseq[i].is_auto;
			skill_seq_list[i].weight        = ess.monsterskillseq[i].weight;
		}
#else
		skill_seq_list[i].skillseq_id	= ess.monsterskillseq[i].skillseq_id;
		skill_seq_list[i].mask		= ess.monsterskillseq[i].mask;
		skill_seq_list[i].is_auto	= ess.monsterskillseq[i].is_auto;
		skill_seq_list[i].weight	= ess.monsterskillseq[i].weight;
#endif

		weight_adjust[i] = -1;	//负数代表不进行权重调整
	}


	//生成默认权重数据
	BuildSkillWeight(weight_adjust, _skill_map);

	score_point = ess.score_point;

	exp = ess.exp;
	if (exp < 0)
	{
		exp = 0;
	}
	new_exp = ess.loot_exp;

	money_low = ess.loot_min_money;
	money_high = ess.loot_max_money;

	id_acti_expfactor_config = ess.id_acti_expfactor_config;
	id_acti_exp_ratio = ess.id_acti_exp_ratio;
	special_drop_table = ess.id_drop_table;
	special_drop_times = ess.drop_table_times;
	not_npc_loot = ess.not_npc_loot; //这个为true表示 npc不会进入仇恨列表

	monster_usage = ess.monster_usage;

	cannot_attack = ess.cannot_attack;
	bodysize_type = ess.bodysize_type;

	specdmg_reduce_type = ess.specdmg_reduce_type;
	specdmg_reduce_param = ess.specdmg_reduce_param;
	repu_count = 0;
	for (size_t i = 0; i < EXP_MON_ATTACH_REPU_COUNT; ++i)
	{
		VECTOR_CHECK(ess.repus, i);
		if (ess.repus[i].id > 0 && ess.repus[i].value != 0)
		{
			npc_template::repu_t repu;
			repu.id = ess.repus[i].id;
			repu.value = ess.repus[i].value;
			repu.effect_type = ess.repus[i].effect_type;
			repu.param = ess.repus[i].param;
			repus[repu_count] = repu;
			++repu_count;
		}
	}
	can_be_pushed = ess.can_be_pushed;
	if (can_be_pushed > CHAR_MAX)
	{
		can_be_pushed = CHAR_MAX;
	}
	is_lifeness = ess.is_lifeness;

	temp_unreachable_type = ess.temp_unreachable_type;
	temp_unreachable_range = ess.temp_unreachable_range;
	if (temp_unreachable_type < TEMP_UNREACHABLE_TYPE_NONE || temp_unreachable_type >= TEMP_UNREACHABLE_TYPE_NUM)
	{
		__PRINTF("怪物: %d 的临时不可达类型: %d 不合法\n", tid, temp_unreachable_type);
		temp_unreachable_type = TEMP_UNREACHABLE_TYPE_NONE;
	}
	if (temp_unreachable_range <= 1e-6)
	{
		temp_unreachable_range = 0.0f;
	}
	if (TEMP_UNREACHABLE_TYPE_NONE != temp_unreachable_type && temp_unreachable_range <= 1e-6)
	{
		__PRINTF("怪物: %d 临时不可达类型: %d 的范围：%f 不合法\n", tid, temp_unreachable_type, temp_unreachable_range);
	}
	exp_cost_activity_point = 0; //ess.energy_cost_for_exp; $$$$$$ 使用了旧版本的Exptypes.h

	ignore_max_hp2 = ess.attacked_affect_hp;
	can_be_attacked_by_chariot = ess.can_be_attacked_by_chariot;
	file_minimap_icon = ess.file_minimap_icon;
	player_damage_list_size = ess.player_damage_list_size;
	corps_damage_list_size = ess.corps_damage_list_size;
	can_not_be_bless = ess.can_not_be_bless;
	sync_damage_player_to_instance = ess.sync_damage_player_to_instance;
	player_damage_region_send = ess.player_damage_region_send;
	corps_damage_region_send = ess.corps_damage_region_send;

	is_center_server_monster = ess.is_center_server_monster; // 是否是跨服战场怪bool
	hp_type = ess.hp_type; // 血条类型
	broadcast_radius = ess.broadcast_radius; // 广播半径

	// 硬直条
	has_stiff_bar = ess.has_stiff_bar;
	sp_restore_tm = ess.stiff_bar_recovery_time;
	sp_out_skill_id = ess.stiff_bar_bite_id;
	immune_mask1 = (uint64_t)ess.immune_type2 << 32 | (uint64_t)ess.immune_type;
	immune_mask2 = (uint64_t)ess.boss_special_immune_type2 << 32 | (uint64_t)ess.boss_special_immune_type;

	control_move_dis_ratio = ess.additional_repel_rate;
	if (control_move_dis_ratio <= 0.f)
	{
		control_move_dis_ratio = 1.f;
	}
	else if (control_move_dis_ratio > 10.f)
	{
		control_move_dis_ratio = 10.f;
	}
	control_move_dis_extra = ess.additional_repel_distance;
	if (control_move_dis_extra < -10.f)
	{
		control_move_dis_extra = -10.f;
	}
	else if (control_move_dis_extra > 10.f)
	{
		control_move_dis_extra = 10.f;
	}

	//状态盒子相关
	drop_buff_box_odds = ess.drop_buff_box_odds;
	memset(drop_buff_box_ids, 0, sizeof(drop_buff_box_ids));
	for (size_t i = 0; i < 4 && i < ess.drop_buff_box_ids.size(); ++ i)
	{
		drop_buff_box_ids[i] = ess.drop_buff_box_ids[i];
	}
	matter_template_id = ess.matter_id;

	turn_speed = ess.turn_speed;
	skillfx_prio = ess.life_state & MONSTER_LIFE_HITFX_PRIO_HIGH;
	height = ess.height;
	life_state = ess.life_state;

	assert(ess.ai_simple_policy.size() >= 10);
	for (size_t i = 0; i < 10; ++ i)
	{
		memcpy(&ai_simple_policy[i], &(ess.ai_simple_policy[i]), sizeof(MONSTER_ESSENCE_AI_SIMPLE_POLICY));
	}

	damage_rate = ess.damage_rate;
	diaoxiang_id = 0;//ess.diaoxiang_id;
	listen_skill_list.clear();
	for (size_t i = 0; i < ess.listen_skill_list.size(); ++i)
	{
		listen_skill_list.insert(ess.listen_skill_list[i]);
	}
}

void npc_template::BuildSkillWeight(int weight_adjust[EXP_MONSTER_CAST_SKILL_COUNT], npc_template::SKILL_MAP& smap) const
{
	//将技能包以MASK的形式整理起来
	for (size_t i = 0 ; i < EXP_MONSTER_CAST_SKILL_COUNT; ++i)
	{
		if (!skill_seq_list[i].skillseq_id)
		{
			continue;
		}

		//取得权重
		skill_sq_t  sst = skill_seq_list[i];
		int weight = sst.weight;
		if (weight_adjust[i] >= 0)
		{
			weight = weight_adjust[i];
			sst.weight = weight;
		}
		if (weight <= 0)
		{
			continue;
		}

		//放入技能表内
		for (int j = 0; j < 31; j ++)
		{
			if (sst.mask & ( 1 << j))
			{
				smap.skill_seq_map[j].push_back(sst);
				smap.skill_seq_weight[j] += weight;
			}
		}
	}
}

void npc_sell_service_template::LoadFromEssence(const NPC_SELL_SERVICE& ess)
{
	tid = ess.id;
	count = 0;
	for (int i = 0; i < EXP_NPC_SELL_PAGE_COUNT; ++i)
	{
		VECTOR_CHECK(ess.pages, i);
		for (int j = 0; j < EXP_NPC_SELL_PAGE_GOODS_COUNT; ++j)
		{
			VECTOR_CHECK(ess.pages[i].goods, j);
			if (ess.pages[i].goods[j].id_item == 0)
			{
				continue;
			}

			good_t good;
			good.id_item 		= ess.pages[i].goods[j].id_item;
			good.id_genitem_cfg 	= ess.pages[i].goods[j].id_genitem_cfg;
			good.activity_id	 	= ess.pages[i].activity_id;
			good.npcsell_cost_mode 	= ess.pages[i].cost_mode;
			good.index		= EXP_NPC_SELL_PAGE_GOODS_COUNT * i + j;

			goods.push_back(good);
			++count;
		}
	}
}

void npc_buy_service_template::LoadFromEssence(const NPC_BUY_SERVICE& ess)
{
	tid = ess.id;
	for (int i = 0; i < EXP_NPCBUY_SERVICE_ITEM_NUM; ++i)
	{
		VECTOR_CHECK(ess.items, i);
		if (!(ess.items[i].id && ess.items[i].count))
		{
			continue;
		}
		if (!item_manager::GetInstance().IsItem(ess.items[i].id))
		{
			continue;
		}
		buy_items_vec.push_back(ess.items[i]);
	}
}

void npc_task_in_service_template::LoadFromEssence(const NPC_TASK_IN_SERVICE& ess)
{
	tid = ess.id;
	count = 0;
	for (int i = 0; i < EXP_NPC_IN_TASK_COUNT; ++i)
	{
		VECTOR_CHECK(ess.id_tasks, i);
		if (!ess.id_tasks[i])
		{
			continue;
		}
		task_in_list[count] = ess.id_tasks[i];
		++count;
	}
}

void npc_task_out_service_template::LoadFromEssence(const NPC_TASK_OUT_SERVICE& ess)
{
	tid = ess.id;
	count = 4;
	task_out_list[0] = ess.id_task_set;
	task_out_list[1] = ess.id_refresh_matter;
	task_out_list[2] = ess.id_matter;
	task_out_list[3] = ess.disapear_reach_max_tasks;

	for (int i = 0; i < EXP_NPC_OUT_TASK_COUNT; ++i)
	{
		VECTOR_CHECK(ess.id_tasks, i);
		if (!ess.id_tasks[i])
		{
			continue;
		}
		task_out_list[count] = ess.id_tasks[i];
		++count;
	}
	if (count == 4 && ess.id_task_set == 0)
	{
		//空任务则忽略
		//count = 0;
		__PRINTF("发现空任务taskid:%d\n", tid);
	}
}

void npc_easy_lottery_service::LoadFromEssence(const NPC_LOTTERY_SERVICE& ess)
{
	memset(this, 0, sizeof(*this));
	tid = ess.id;
	cost_money_type = ess.cost_money_type;
	cost_money = ess.cost_money;
	cost_reputation_id = ess.cost_reputation_id;
	cost_reputation = ess.cost_reputation;
	discount_act_id = ess.discount_act_id;				//活动id
	ASSERT(discount_act_id == 0 || (discount_act_id != 0 && ess.discount >= 1 && ess.discount <= 100));
	discount = (float)ess.discount / 100.0;		//活动折扣比例
#ifdef USE_CONVEX
	assert(sizeof(cost_item) / sizeof(cost_item[0]) >= ess.cost_item.size());
#else
	static_assert(sizeof(cost_item) == sizeof(ess.cost_item), "size unmatch");
#endif
	for (size_t i = 0; i < sizeof(cost_item) / sizeof(cost_item[0]); i ++)
	{
		VECTOR_CHECK(ess.cost_item, i);
		cost_item[i].item_id = ess.cost_item[i].item_id;
		cost_item[i].item_count = ess.cost_item[i].item_count;
	}
	free_time_uselimit_config = ess.free_common_uselimit_id;
	free_cool_down_id = ess.free_cool_down_id;
	free_cool_down_second = ess.free_cool_down_time;
	need_inventory_slot = ess.need_inventory_slot;
	id_cmn_uselimit_config = ess.id_cmn_uselimit_config;
	lottery_begin_time = ess.lottery_begin_time;
	lottery_end_time = ess.lottery_end_time;
	ASSERT(lottery_begin_time <= lottery_end_time);

}

void npc_template::LoadFromNPCEssence(const NPC_ESSENCE& ess)
{
#ifdef USE_CONVEX
#define GET_SERVICE_DATA(id_space, name, id) \
        const name *ptr = name::get(id).get(); \
        bool suc = false; \
	const name& service = *ptr; \
        if(ptr) \
        { \
                suc = true; \
        }
#else
#define GET_SERVICE_DATA(id_space, name, id) \
	DATA_TYPE dt2; \
	const name &service = *(const name*)gmatrix::GetInstance().GetDataMan().get_data_ptr(id,id_space,dt2); \
	bool suc = (dt2 == DT_##name);
#endif

	memset(this, 0, sizeof(*this));
	//使用特殊的模板
	tid_t monster_tid = ess.id_src_monster;
	const npc_template *pTemplate = npc_template_manager::GetInstance().Get(monster_tid);
	if (!pTemplate)
	{
		__PRINTF("错误的NPC模板: %u,没有对应的怪物模板ID %d\n", ess.id, monster_tid);
		return;
	}
	*this = *pTemplate;
	is_npc = true;
	tid = ess.id;

	//可交互NPC相关
	interact_template_id = ess.interaction_id;
	if (player_replace_unit && !ess.player_replace_unit)
	{
		__PRINTF("错误的NPC模板: %u,对应的怪物模板不是 玩家替代怪物 %d\n", ess.id, monster_tid);
	}
	player_replace_unit = ess.player_replace_unit;
	if (player_replace_unit)
	{
		if (player_id == 0)
		{
			__PRINTF("错误的NPC模板: %u,对应的怪物模板玩家替代 id = 0 %d\n", ess.id, monster_tid);
			player_replace_unit = false;
		}
	}
	if (ess.file_minimap_icon)
	{
		file_minimap_icon = ess.file_minimap_icon;
	}
	refresh_speed = (int)(ess.refresh_time + 0.5f);
	if (!cannot_attack && CANBEATTACK_NO == ess.attack_rule)
	{
		cannot_attack = true;
	}
	npc_data.tax_rate = 0;
	DATACOPY(npc_data.mirror_npc_ids, ess.mirror_npc_ids);

	DATACOPY(npc_data.carrier_mins, ess.carrier_mins);
	DATACOPY(npc_data.carrier_maxs, ess.carrier_maxs);

	npc_data.id_instance = ess.id_instance;
	npc_data.can_multi_open = (bool)ess.can_multi_open;
	npc_data.max_open_num = ess.max_open_num;
	npc_data.disapear_reach_max_open = (bool)ess.disapear_reach_max_open;
	player_damage_list_size = 0;
	corps_damage_list_size = 0;
	can_not_be_bless = false;
	sync_damage_player_to_instance = false;
	player_damage_region_send = false;
	corps_damage_region_send = false;

	id_graffiti_box = ess.id_graffiti_box;

	has_service = true;
	//处理各种服务
	for (int i = 0; i < EXP_NPC_SELL_SERV_NUM; ++i)
	{
		VECTOR_CHECK(ess.id_sell_service, i);
		if (ess.id_sell_service[i])
		{
			npc_data.sell_service_template[i] = npc_template_manager::GetInstance().GetNPCSellServiceTemplate(ess.id_sell_service[i]);
			if (!npc_data.sell_service_template[i])
			{
				__PRINTF("发现了错误的sell service %u 在NPC %d\n", ess.id_sell_service[i], tid);
				break;
			}
			GNET::InsertIdipForbidShopItem(npc_data.sell_service_template[i]->tid);
		}
	}

	for (int i = 0; i < EXP_NPC_LOTTERY_SERVICE_NUM; i ++)
	{
		VECTOR_CHECK(ess.id_npc_lottery_service, i);
		if (!ess.id_npc_lottery_service[i])
		{
			continue;
		}
		npc_data.easy_lottery_service[i] = npc_template_manager::GetInstance().GetNPCEasyLotteryServiceTemplate(ess.id_npc_lottery_service[i]);
		if (!npc_data.easy_lottery_service[i])
		{
			__PRINTF("发现了错误的id_npc_lottery_service service %u 在NPC %d\n", ess.id_npc_lottery_service[i], tid);
			break;
		}
	}

	if (ess.id_buy_service)
	{
		npc_data.buy_service_template = npc_template_manager::GetInstance().GetNPCBuyServiceTemplate(ess.id_buy_service);
		if (!npc_data.buy_service_template)
		{
			__PRINTF("发现了错误的buy service %u 在NPC %d\n", ess.id_buy_service, tid);
		}
	}
	if (ess.id_transmit_service)
	{
		GET_SERVICE_DATA(ID_SPACE_ESSENCE, NPC_TRANSMIT_SERVICE, ess.id_transmit_service)
		if (!suc)
		{
			__PRINTF("发现了错误的transmit service %u 在NPC %d\n", ess.id_transmit_service, tid);
		}
		else
		{
			int num = 0;
			for (int i = 0; i < 8; ++i)
			{
				VECTOR_CHECK(service.targets, i);
				//if(service.targets[i].id_instance <= 0) continue;
				npc_data.transmit_entry[num].gs_id = service.targets[i].gs_id;
				npc_data.transmit_entry[num].trans_condition = service.targets[i].trans_condition;
				npc_data.transmit_entry[num].id_instance = service.targets[i].id_instance;
				npc_data.transmit_entry[num].id_scene = service.targets[i].id_scene;
				float x, y, z;
				x = service.targets[i].x;
				y = service.targets[i].y;
				z = service.targets[i].z;
				npc_data.transmit_entry[num].target_pos = A3DVECTOR3(x, y, z);
				npc_data.transmit_entry[num].require_level = service.targets[i].required_level;
				npc_data.transmit_entry[num].fee = service.targets[i].fee;
				npc_data.transmit_entry[num].radius = service.targets[i].radius;
				++num;
			}
			npc_data.service_transmit_target_num = num;
		}
	}
	/*
	if(ess.id_task_in_service)
	{
		DATA_TYPE dt2;
		const NPC_TASK_IN_SERVICE &service = *(const NPC_TASK_IN_SERVICE*)gmatrix::GetInstance().GetDataMan().get_data_ptr(ess.id_task_in_service,ID_SPACE_ESSENCE,dt2);
		if(!(dt2 == DT_NPC_TASK_IN_SERVICE))
		{
			__PRINTF("发现了错误的task in service %d 在NPC %d\n",ess.id_task_in_service,tid);
		}
		else
		{
			int num = 0;
			for(int i = 0;i < EXP_NPC_IN_TASK_COUNT;++i)
			{
				if(!service.id_tasks[i]) continue;
				npc_data.service_task_in_list[num] = service.id_tasks[i];
				num ++;
			}
			npc_data.service_task_in_num = num;
		}
	}
	if(ess.id_task_out_service)
	{
		DATA_TYPE dt2;
		const NPC_TASK_OUT_SERVICE &service = *(const NPC_TASK_OUT_SERVICE*)gmatrix::GetInstance().GetDataMan().get_data_ptr(ess.id_task_out_service,ID_SPACE_ESSENCE,dt2);
		if(!(dt2 == DT_NPC_TASK_OUT_SERVICE))
		{
			__PRINTF("发现了错误的task out service %d 在NPC %d\n",ess.id_task_out_service,tid);
		}
		else
		{
			int num = 3;
			npc_data.service_task_out_list[0] = service.id_task_set;
			npc_data.service_task_out_list[1] = service.id_refresh_matter;
			npc_data.service_task_out_list[2] = service.id_matter;
			for(int i = 0; i < EXP_NPC_OUT_TASK_COUNT; ++i)
			{
				if(!service.id_tasks[i]) continue;
				npc_data.service_task_out_list[num] = service.id_tasks[i];
				num ++;
			}
			npc_data.service_task_out_num = num;
			if(num == 2 && service.id_task_set == 0)
			{
				//空任务则忽略
				npc_data.service_task_out_num = 0;
				__PRINTF("发现空任务npcid:%d  taskid:%d\n", ess.id, ess.id_task_out_service);
			}
		}
	}
	*/
	if (ess.id_task_in_service)
	{
		npc_data.task_in_service_template = npc_template_manager::GetInstance().GetNPCTaskInServiceTemplate(ess.id_task_in_service);
		if (!npc_data.task_in_service_template)
		{
			__PRINTF("发现了错误的task_in service %u 在NPC %d\n", ess.id_task_in_service, tid);
		}
	}
	if (ess.id_task_out_service)
	{
		npc_data.task_out_service_template = npc_template_manager::GetInstance().GetNPCTaskOutServiceTemplate(ess.id_task_out_service);
		if (!npc_data.task_out_service_template)
		{
			__PRINTF("发现了错误的task_out service %u 在NPC %d\n", ess.id_task_out_service, tid);
		}
	}

	if (ess.id_instance_service)
	{
		//副本
		GET_SERVICE_DATA(ID_SPACE_ESSENCE, NPC_INSTANCE_SERVICE, ess.id_instance_service)
		if (!suc)
		{
			__PRINTF("发现了错误的instance service %u 在NPC %d\n", ess.id_instance_service, tid);
		}
		else
		{
			npc_data.service_enter_instance_tid = service.id;
			int num = 0;
			for (int i = 0; i < EXP_NPC_INSTANCE_CONFIG_COUNT; ++i)
			{
				VECTOR_CHECK(service.config, i);
				if (service.config[i].inst_id <= 0)
				{
					continue;
				}
				//TODO 时间控制还没做
				npc_data.service_enter_instance_list[num].inst_tid = service.config[i].inst_id;
				npc_data.service_enter_instance_list[num].npc_use_count = service.config[i].npc_use_count;
				++num;
			}
			npc_data.service_enter_instance_num = num;
		}
	}


	npc_data.combined_services = ess.combined_services;
	npc_data.combined_services2 = ess.combined_services2;
	npc_data.combined_services3 = ess.combined_services3;
	npc_data.combined_services4 = ess.combined_services4;
	npc_data.combined_services5 = ess.combined_services5;
	npc_data.combined_services6 = ess.combined_services6;

	if (ess.has_pkvalue_service)
	{
		npc_data.service_reset_pkvalue.has_service = 1;
		npc_data.service_reset_pkvalue.fee_per_unit  = ess.fee_per_pkvalue;
	}

	if (ess.id_task_event_service)
	{
		GET_SERVICE_DATA(ID_SPACE_ESSENCE, NPC_TASK_EVENT_SERVICE, ess.id_task_event_service)
		if (!suc)
		{
			__PRINTF("发现了错误的task_event service %u 在NPC %d\n", ess.id_task_event_service, tid);
		}
		else
		{
			size_t t = 0;
			for (size_t i = 0; i < EXP_NPC_TASK_EVENT_COUNT; i ++)
			{
				VECTOR_CHECK(service.tasks, i);
				if (service.tasks[i].id <= 0)
				{
					continue;
				}
				npc_data.service_task_event_list[t].id = service.tasks[i].id;
				npc_data.service_task_event_list[t].count = service.tasks[i].count;
				npc_data.service_task_event_list[t].cooltime_sec = service.tasks[i].cooltime_sec;
				t++;
			}
			npc_data.service_task_event_num = t;
		}
	}

	if (ess.id_reputation_sell_service)
	{
		GET_SERVICE_DATA(ID_SPACE_ESSENCE, REPUTATION_SELL_SERVICE, ess.id_reputation_sell_service)
		if (!suc)
		{
			__PRINTF("发现了错误的REPUTATION_SELL_SERVICE %u 在NPC %d\n", ess.id_reputation_sell_service, tid);
		}
		else
		{
			npc_data.reputation_sell_service.npcsell_mode = service.npcsell_mode;
			npc_data.reputation_sell_service.repu_id = service.repu_id;
			npc_data.reputation_sell_service.repu_num = service.repu_num;
			npc_data.reputation_sell_service.cost_money = service.cost_money;
			npc_data.reputation_sell_service.cost_repu_id = service.cost_repu_id;
			npc_data.reputation_sell_service.cost_repu_num = service.cost_repu_num;
		}
	}
	if (ess.id_start_minigame_service)
	{
		GET_SERVICE_DATA(ID_SPACE_ESSENCE, START_MINIGAME_SERVICE, ess.id_start_minigame_service)
		//const START_MINIGAME_SERVICE &service = *(const START_MINIGAME_SERVICE*)
		if (!suc)
		{
			__PRINTF("发现了错误的START_MINIGAME_SERVICE %u 在NPC %d\n", ess.id_start_minigame_service, tid);
		}
		//npc_data.minigame_service = ess.id_start_minigame_service;
		npc_data.minigame_service = service.id;
	}



	if (ess.id_playerdiy_activity_service)
	{
		GET_SERVICE_DATA(ID_SPACE_ESSENCE, PLAYERDIY_ACTIVITY_SERVICE, ess.id_playerdiy_activity_service)
		if (!suc)
		{
			__PRINTF("发现了错误的playerdiy_acitivity service %u 在NPC %d\n", ess.id_playerdiy_activity_service, tid);
		}
		else
		{
			size_t t = 0;
			for (size_t i = 0; i < EXP_PLAYERDIY_ACT_SERVICE_CFG_COUNT; i ++)
			{
				VECTOR_CHECK(service.id_playerdiy_cfgs, i);
				if (service.id_playerdiy_cfgs[i] <= 0)
				{
					continue;
				}
				npc_data.service_playerdiy_activity_list[t] = service.id_playerdiy_cfgs[i];
				t++;
			}
			npc_data.service_playerdiy_activity_num = t;
		}
	}


	npc_data.is_for_faction_only = ess.factionspec_type;
	npc_data.service_nation_limit = ess.service_limit;
	npc_data.type_faction_dyn_service = ess.type_faction_dyn_service;
	npc_data.need_domain = ess.domain_related;
	npc_data.id_pre_required_task = ess.id_pre_required_task;
	is_guard = ess.guard_npc;
	impact_type = ess.impact_type;

	diaoxiang_id = ess.diaoxiang_id;
#undef GET_SERVICE_DATA
}

bool npc_template::GetIsInteract() const
{
	if (interact_template_id)
	{
		const interact_template *temp = InteractManager::GetInstance().Get(interact_template_id);
		return temp != NULL;
	}
	return false;
}


int  npc_template_manager::ReLoadNPC(int tid)
{

	NPC_ESSENCE::HMAP_NPC_ESSENCE tmpNpcConfigMap;
	std::set<unsigned int> tmpTid;
	tmpTid.emplace(tid);
	const std::string filepath = gmatrix::GetInstance().GetTemplateFilePath() + GetConfName(169) + ".xml";
	NPC_ESSENCE::loadFromXml(tmpNpcConfigMap, tmpTid, filepath);

	auto it = tmpNpcConfigMap.find(tid);
	if ( it == tmpNpcConfigMap.end())
	{
		return -1;
	}


	npc_template *pTemplate = Get(tid);
	if (!pTemplate)
	{
		return -1;
	}

	if (it->second->id_src_monster && 0 != ReLoadMonster(it->second->id_src_monster))
	{
		return -1;
	}
	pTemplate->LoadFromNPCEssence(*it->second);

	return 0;

}

int npc_template_manager::ReLoadMonster(int tid)
{
	MONSTER_ESSENCE::HMAP_MONSTER_ESSENCE tmpMonsterConfigMap;
	std::set<unsigned int> tmpTid;
	tmpTid.emplace(tid);
	const std::string filepath = gmatrix::GetInstance().GetTemplateFilePath() + GetConfName(203) + ".xml";
	MONSTER_ESSENCE::loadFromXml(tmpMonsterConfigMap, tmpTid, filepath);

	auto it = tmpMonsterConfigMap.find(tid);
	if ( it == tmpMonsterConfigMap.end())
	{
		return -1;
	}

	npc_template *pTemplate = Get(tid);
	if (!pTemplate)
	{
		return -1;
	}

	pTemplate->LoadFromMonsterEssence(*it->second);

	return 0;
}

bool npc_template_manager::GetSellServiceGoods(const npc_template& temp, GOOD_TRANF_T_VEC& goods_vec)
{
	if (temp.npc_data.sell_service_template[0])
	{
		for (int i = 0; i < EXP_NPC_SELL_SERV_NUM; ++i)
		{
			if (temp.npc_data.sell_service_template[i])
			{
				for (int j = 0; j < temp.npc_data.sell_service_template[i]->count; ++j)
				{
					const npc_sell_service_template::good_t& goods = temp.npc_data.sell_service_template[i]->goods[j];
					goods_vec.push_back(npc_sell_service_template::good_tranf_t(temp.npc_data.sell_service_template[i]->tid, goods.id_item, goods.id_genitem_cfg, goods.index, (unsigned char)i, goods.npcsell_cost_mode, goods.activity_id));
				}
			}
		}
		//统计各种药品信息
		if (temp.tid == SERVER_CONFIG.self_service_npc_tid)
		{
			player_bot::UpdateSelfServiceSellItems(goods_vec);
		}
	}

	return (!goods_vec.empty());
}

bool npc_template_manager::CheckGoods(const npc_sell_service_template::good_tranf_t& gtt, std::map<int, price_t> *price_list)
{
	if (INVALID_ITEM_TID == gtt.id_item)
	{
		__PRINTF("npc_template_manager::CheckGoods fail for invalid item:item_id=%d\n", gtt.id_item);
		return false;
	}

	const item *pItem = item_manager::GetInstance().GetItem(gtt.id_item);
	if (!pItem)
	{
		__PRINTF("npc_template_manager::CheckGoods fail for item:item_id=%d\n", gtt.id_item);
		return false;
	}
	money_t shop_price;
	int shop_repu_type;
	size_t shop_repu_value;
	int shop_item_id;
	size_t shop_item_num;
	if (item_manager::GetInstance().GetItemShopPrice(gtt.id_item, shop_price, shop_repu_type, shop_repu_value, shop_item_id, shop_item_num))
	{
		price_t p;
		if (p.CheckAndInit(gtt.service_tid, gtt.npcsell_cost_mode, gtt.activity_id, pItem, shop_price, shop_repu_type, shop_repu_value, shop_item_id, shop_item_num, gtt.id_genitem_cfg))
		{
			if (price_list)
			{
				p._service_tid = gtt.service_tid;
				int local_index = gtt.service_offset * EXP_NPC_SELL_PAGE_COUNT * EXP_NPC_SELL_PAGE_GOODS_COUNT + gtt.index;
				price_list->insert(std::make_pair(local_index, p));
			}
		}
		else
		{
			__PRINTF("npc_template_manager::CheckGoods fail for CheckAndInit:item_id=%d\n", gtt.id_item);
			return false;
		}
	}
	else
	{
		__PRINTF("npc_template_manager::CheckGoods fail for GetItemShopPrice:item_id=%d\n", gtt.id_item);
		return false;
	}

	return true;
}

bool npc_template_manager::Load(elementdataman& data_man, bool test)
{
#ifdef USE_CONVEX
	for (auto& tmp : CONVEX_MAP(MONSTER_ESSENCE))
	{
		const MONSTER_ESSENCE *ess = tmp.second.get();
		ASSERT(ess);
		npc_template temp;
		temp.LoadFromMonsterEssence(*ess);
		Insert(temp);
	}
	for (auto& tmp : CONVEX_MAP(NPC_SELL_SERVICE))
	{
		const NPC_SELL_SERVICE *ess = tmp.second.get();
		ASSERT(ess);
		npc_sell_service_template temp;
		temp.LoadFromEssence(*ess);
		InsertNPCSellServiceTemplate(temp);
	}
	for (auto& tmp : CONVEX_MAP(NPC_BUY_SERVICE))
	{
		const NPC_BUY_SERVICE *ess = tmp.second.get();
		ASSERT(ess);
		npc_buy_service_template temp;
		temp.LoadFromEssence(*ess);
		InsertNPCBuyServiceTemplate(temp);
	}
	for (auto& tmp : CONVEX_MAP(NPC_TASK_IN_SERVICE))
	{
		const NPC_TASK_IN_SERVICE *ess = tmp.second.get();
		ASSERT(ess);
		npc_task_in_service_template temp;
		temp.LoadFromEssence(*ess);
		InsertNPCTaskInServiceTemplate(temp);
	}
	for (auto& tmp : CONVEX_MAP(NPC_TASK_OUT_SERVICE))
	{
		const NPC_TASK_OUT_SERVICE *ess = tmp.second.get();
		ASSERT(ess);
		npc_task_out_service_template temp;
		temp.LoadFromEssence(*ess);
		InsertNPCTaskOutServiceTemplate(temp);
	}
	for (auto& tmp : CONVEX_MAP(NPC_LOTTERY_SERVICE))
	{
		const NPC_LOTTERY_SERVICE *ess = tmp.second.get();
		ASSERT(ess);
		npc_easy_lottery_service temp;
		temp.LoadFromEssence(*ess);
		InsertNPCEasyLotteryServiceTemplate(temp);
	}

	//由于NPC内部的怪物ID,所以先读入全部的怪物数据才能进行NPC数据的读取
	//	//由于NPC需要使用服务数据,所以先读取服务数据才能进行NPC数据读取
	for (auto& tmp : CONVEX_MAP(NPC_ESSENCE))
	{
		const NPC_ESSENCE *ess = tmp.second.get();
		ASSERT(ess);
		npc_template temp;
		temp.LoadFromNPCEssence(*ess);
		if (temp.tid == 0)
		{
			continue;
		}

		GOOD_TRANF_T_VEC goods_vec;
		if (GetSellServiceGoods(temp, goods_vec))
		{
			_npc_sell_service_good_tranf_t_map.insert(std::make_pair(temp.tid, goods_vec));
			if (test)
			{
				for (auto it = goods_vec.begin(); it != goods_vec.end(); ++it)
				{
					if (!CheckGoods(*it, nullptr))
					{
						ASSERT(false);
						continue;
					}
				}
			}
		}

		Insert(temp);
	}
#else
	DATA_TYPE dt;
	unsigned int datasize;
	unsigned int id;
	const void *ptr = data_man.get_first_data(ID_SPACE_ESSENCE, id, dt, datasize);
	for (; ptr; ptr = data_man.get_next_data(ID_SPACE_ESSENCE, id, dt, datasize))
	{
		if (DT_MONSTER_ESSENCE != dt)
		{
			continue;
		}
		const MONSTER_ESSENCE& ess = *(const MONSTER_ESSENCE *)ptr;
		ASSERT(DT_MONSTER_ESSENCE == dt);
		npc_template temp;
		temp.LoadFromMonsterEssence(ess);
		Insert(temp);
	}
	ptr = data_man.get_first_data(ID_SPACE_ESSENCE, id, dt, datasize);
	for (; ptr; ptr = data_man.get_next_data(ID_SPACE_ESSENCE, id, dt, datasize))
	{
		if (DT_NPC_SELL_SERVICE != dt)
		{
			continue;
		}
		const NPC_SELL_SERVICE& ess = *(const NPC_SELL_SERVICE *)ptr;
		ASSERT(DT_NPC_SELL_SERVICE == dt);
		npc_sell_service_template temp;
		temp.LoadFromEssence(ess);
		InsertNPCSellServiceTemplate(temp);
	}
	ptr = data_man.get_first_data(ID_SPACE_ESSENCE, id, dt, datasize);
	for (; ptr; ptr = data_man.get_next_data(ID_SPACE_ESSENCE, id, dt, datasize))
	{
		if (DT_NPC_BUY_SERVICE != dt)
		{
			continue;
		}
		const NPC_BUY_SERVICE& ess = *(const NPC_BUY_SERVICE *)ptr;
		ASSERT(DT_NPC_BUY_SERVICE == dt);
		npc_buy_service_template temp;
		temp.LoadFromEssence(ess);
		InsertNPCBuyServiceTemplate(temp);
	}
	ptr = data_man.get_first_data(ID_SPACE_ESSENCE, id, dt, datasize);
	for (; ptr; ptr = data_man.get_next_data(ID_SPACE_ESSENCE, id, dt, datasize))
	{
		if (DT_NPC_TASK_IN_SERVICE != dt)
		{
			continue;
		}
		const NPC_TASK_IN_SERVICE& ess = *(const NPC_TASK_IN_SERVICE *)ptr;
		ASSERT(DT_NPC_TASK_IN_SERVICE == dt);
		npc_task_in_service_template temp;
		temp.LoadFromEssence(ess);
		InsertNPCTaskInServiceTemplate(temp);
	}
	ptr = data_man.get_first_data(ID_SPACE_ESSENCE, id, dt, datasize);
	for (; ptr; ptr = data_man.get_next_data(ID_SPACE_ESSENCE, id, dt, datasize))
	{
		if (DT_NPC_TASK_OUT_SERVICE != dt)
		{
			continue;
		}
		const NPC_TASK_OUT_SERVICE& ess = *(const NPC_TASK_OUT_SERVICE *)ptr;
		ASSERT(DT_NPC_TASK_OUT_SERVICE == dt);
		npc_task_out_service_template temp;
		temp.LoadFromEssence(ess);
		InsertNPCTaskOutServiceTemplate(temp);
	}
	ptr = data_man.get_first_data(ID_SPACE_ESSENCE, id, dt, datasize);
	for (; ptr; ptr = data_man.get_next_data(ID_SPACE_ESSENCE, id, dt, datasize))
	{
		if (DT_NPC_LOTTERY_SERVICE != dt)
		{
			continue;
		}
		const NPC_LOTTERY_SERVICE& ess = *(const NPC_LOTTERY_SERVICE *)ptr;
		ASSERT(DT_NPC_LOTTERY_SERVICE == dt);
		npc_easy_lottery_service temp;
		temp.LoadFromEssence(ess);
		InsertNPCEasyLotteryServiceTemplate(temp);
	}

	//由于NPC内部的怪物ID,所以先读入全部的怪物数据才能进行NPC数据的读取
	//由于NPC需要使用服务数据,所以先读取服务数据才能进行NPC数据读取
	ptr = data_man.get_first_data(ID_SPACE_ESSENCE, id, dt, datasize);
	for (; ptr; ptr = data_man.get_next_data(ID_SPACE_ESSENCE, id, dt, datasize))
	{
		if (DT_NPC_ESSENCE != dt)
		{
			continue;
		}
		const NPC_ESSENCE& ess = *(const NPC_ESSENCE *)ptr;
		ASSERT(DT_NPC_ESSENCE == dt);
		npc_template temp;
		temp.LoadFromNPCEssence(ess);
		if (temp.tid == 0)
		{
			continue;
		}
		Insert(temp);
	}
#endif
	return true;
}


void gnpc_controller::Release(bool unmapped)
{
	gnpc *pNPC = (gnpc *)_imp->Parent();
	dispatcher *runner = _imp->Runner();
	CF_Release(_imp, this, runner);
	gmatrix::GetInstance().FreeNPC(pNPC);
}

void gnpc_dispatcher::object_enter_scene()
{
	gnpc_imp *pImp = (gnpc_imp *)_imp;
	PB::gp_npc_enter_scene proto;
	pImp->GetParent()->MakeNPCEnterScene(proto);
	RegionSend<S2C::CMD::PBS2C>(proto);
}

void
gnpc_dispatcher::CommitTransfer(slice_info_collector& collector)
{
	//第一步，发送 enter_slice 消息
	//do nothing
	packet_tla_wrapper tbuf;
	using namespace S2C;

	tbuf.reserve(8192);

	//发送自己离开别人视野的消息
	if (!collector.leave_player_list.empty())
	{
		CMD::Make<CMD::object_leave_slice>::From(tbuf, (gnpc *)_imp->Parent());
		std::sort(collector.leave_player_list.begin(), collector.leave_player_list.end());//, link_entity::SimpleCompare);
		multi_send_ls_msg(collector.leave_player_list, tbuf.data(), tbuf.size());
		tbuf.clear();
	}

	//发送自己进入别人视野的消息
	if (!collector.enter_player_list.empty())
	{
		gnpc *pNpc = (gnpc *) _imp->Parent();
		//TODO:改发PB
		PB::gp_npc_enter_slice proto;
		pNpc->MakeNPCEnterSlice(proto, pNpc->pos);
		std::sort(collector.enter_player_list.begin(), collector.enter_player_list.end());
		//, link_entity::SimpleCompare);//收集出来是无序的，排序后才能提交给multi_send_ls_msg
		multisend_ls_command<S2C::CMD::PBS2C>(collector.enter_player_list, proto);
		tbuf.clear();
	}
}

void gnpc_dispatcher::EnterSlice(slice *pSlice, slice_info_collector& collector)
{
	AUTO_LOCK(pSlice->spinlock, &debug::slice_lcs);
	gather_slice_linkid(pSlice, collector.enter_player_list);
}

void gnpc_dispatcher::LeaveSlice(slice *pSlice, slice_info_collector& collector)
{
	AUTO_LOCK(pSlice->spinlock, &debug::slice_lcs);
	gather_slice_linkid(pSlice, collector.leave_player_list);
}

void gnpc_dispatcher::EnterSliceAOI(slice *pSlice, slice_info_collector& collector)
{
	if (_imp->IsLongDisObject())
	{
		return;    //远距对象不需要操作,npc不需要收集aoi信息
	}
	EnterSlice(pSlice, collector);
}

void gnpc_dispatcher::LeaveSliceAOI(slice *pSlice, slice_info_collector& collector)
{
	if (_imp->IsLongDisObject())
	{
		return;
	}
	LeaveSlice(pSlice, collector);
}

void gnpc_dispatcher::EnterSliceLongDis(slice *pSlice, slice_info_collector& collector)
{
	EnterSlice(pSlice, collector);
}

void gnpc_dispatcher::LeaveSliceLongDis(slice *pSlice, slice_info_collector& collector)
{
	LeaveSlice(pSlice, collector);
}

void gnpc_dispatcher::other_get_definite_info(const link_id_t& lid)
{
	using namespace S2C;
	gnpc_imp *pImp = (gnpc_imp *)_imp;
	PB::gp_npc_definite_info proto;
	pImp->GetParent()->MakeNPCDefiniteInfo(proto);
	send_ls_command<S2C::CMD::PBS2C>(lid, proto);
}

void gnpc_dispatcher::on_death(const XID& attacker, unsigned char corpse_delay, skill_id_t kill_skill_id, unsigned char kill_attack_stage, unsigned char state)
{
	//__PRINTF("npc "   FMT_I64" die\n",_imp->Parent()->ID.id);
	RegionSend<S2C::CMD::npc_die>(_imp->Parent(), attacker, g_timer.get_tick(), corpse_delay, kill_skill_id, kill_attack_stage);
}

void gnpc_dispatcher::be_hurt(const XID& attacker, const link_id_t& lid, float damage)
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	if (!attacker.IsValid())
	{
		return;
	}
	send_ls_command<S2C::CMD::be_hurt>(lid, attacker, pNPC->ID, damage);
}

void gnpc_dispatcher::npc_ascription_change()
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	RegionSend<S2C::CMD::npc_ascription_change>(pNPC->ID, pNPC->ascription_id);
	//__PRINTF("npc: "   FMT_I64",群发所有权改变: "   FMT_I64"\n",pNPC->ID.id,pNPC->ascription_id);
}

void gnpc_dispatcher::npc_change_shape()
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	RegionSend<S2C::CMD::npc_change_shape>(pNPC->ID, pNPC->shape);
}

void gnpc_dispatcher::npc_change_name()
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	RegionSend<S2C::CMD::npc_change_name>(pNPC->ID, pNPC->npc_name, pNPC->name_size);
}

void gnpc_dispatcher::npc_fight_policy(unsigned char policy)
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	RegionSend<S2C::CMD::npc_fight_policy>(pNPC->ID, policy);
}

void gnpc_dispatcher::limit_purchase_end()
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	RegionSend<S2C::CMD::limit_purchase_end>(pNPC->ID);
}

void gnpc_dispatcher::object_beattack_changed()
{
	PB::gp_npc_info pb;
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	pb.set_newtype(pNPC->ID.GetNewObjID());
	pb.set_can_be_attacked(pNPC->can_not_be_attack);
	RegionSend<S2C::CMD::PBS2C>(pb);
	//RegionSend<S2C::CMD::object_beattack_changed>(pNPC->ID.id,(char)(pNPC->can_not_be_attack));
}

void gnpc_dispatcher::npc_affix_data_chg(unsigned int affix_data)
{
	PB::gp_npc_affix_data_chg pb;
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	pb.set_newtype(pNPC->ID.GetNewObjID());
	pb.set_affix_data(affix_data);
	RegionSend<S2C::CMD::PBS2C>(pb);
}

void gnpc_dispatcher::escort_trap_info(unsigned char is_active, tid_t trap_tid)
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	RegionSend<S2C::CMD::escort_trap_info>(pNPC->ID, is_active, trap_tid);
}

/*void gnpc_dispatcher::npc_tizi_info_change()
{
	gnpc *pNPC = (gnpc *)(_imp->Parent());
	RegionSend<S2C::CMD::npc_tizi_info_change>(pNPC->ID.id, pNPC->tizi_data, pNPC->tizi_size, pNPC->pen_id, pNPC->tizi_style);
}*/

void gnpc_dispatcher::transform_state(tid_t tid, int use_transform_template, int attacker_newid)
{
	PB::gp_transform_state protoc;
	protoc.set_roleid(_imp->Parent()->ID.id);
	protoc.set_tid(tid);
	protoc.set_use_transform_template(use_transform_template);
	protoc.set_attacker_newid(attacker_newid);
	RegionSend<S2C::CMD::PBS2C>(protoc);
}

void gnpc_dispatcher::update_interact_info(PB::gp_matter_interact_info& msg, const XID& ex_id)
{
	RegionSendEx<S2C::CMD::PBS2C>(ex_id, msg);
}

void gnpc_dispatcher::npc_bubble_say(int id)
{
	PB::gp_npc_bubble_say proto;
	proto.set_id(id);
	RegionSend<S2C::CMD::PBS2C>(proto);
}

void gnpc_dispatcher::npc_bubble_say2(int id)
{
	PB::gp_npc_bubble_say proto;
	proto.set_id(id);
	proto.set_npc_id(_imp->Parent()->ID.id);
	RegionSend<S2C::CMD::PBS2C>(proto);
}

//--------------------------------------------
gnpc_imp::gnpc_imp() : _template(NULL), _master_type(0), _ai_ctrl(NULL), _master_data(NULL), _interact_data_ptr(NULL), _beckon_die_with_host(false), _cannot_find_host(false), _beckon_fly_with_host(false), _beckon_task_id(0),
	_use_sight_angle(false), _sight_angle(0.0f), _faction_ask_help(0), _faction_accept_help(0), _npc_state(NPC_STATE_NORMAL),
	_birth_pos(0, 0, 0), _pos_offset(0, 0, 0), _birth_dir(0), _idle_time_counter(NPC_DEFAULT_IDLE_TIME), _is_fly(false),
	_player_npc_last_die_timestamp(0), _player_npc_death_times(0), _player_npc_revive_delay_time(0), _spawner(NULL), _corpse_delay(0), _dead_time_count(0), _max_attacker_level(0), _shape(0),
	_issetorig(false), _service(NULL), _aggro_mode(0), _fight_back(true), _no_ai_skill(false), _pause_path(false),
	_exist_duration_after_combat(0), _exist_counter_after_combat(0), _leave_combat_time(0), _last_peep_time(0), _attach_sleep_buff_time(0)
{
	_pet = XID();
	_event_region = NULL;
	_leave_scene_type = 0;
	//rune_skill = NULL;
	_use_skill_cooldown = false;
	_fixed_damage = 0;
	_last_move.tick = 0;
}

gnpc_imp::~gnpc_imp()
{
	if (_event_region)
	{
		_event_region->OnLeaveMap(this);
		delete _event_region;
	}
	if (_ai_ctrl)
	{
		delete _ai_ctrl;
		_ai_ctrl = NULL;
	}
	if (_master_data)
	{
		delete _master_data;
		_master_data = NULL;
	}
	if (_service)
	{
		delete _service;
		_service = NULL;
	}
	/*if (rune_skill)
	{
		delete rune_skill;
		rune_skill = NULL;
	}*/

	if (_interact_data_ptr)
	{
		delete _interact_data_ptr;
		_interact_data_ptr = NULL;
	}
}

void gnpc_imp::ReInit(float init_hp)
{
	//npc创建及以后每次重生都需要重新初始化的操作
	_attack.Clear();
	SetOverwhelmingMode(false);
	ResetCruiseTimer(true);

	//加入被动技能
	if (_template->passive_skill.id > 0)
	{
		object_interface oif(this);
		GetSkill().NpcPassiveSkill(_template->passive_skill.id, oif, _template->passive_skill.level);
	}

	if (_cnt.is_stage_npc)
	{
		_template->BuildSkillWeight(_cnt.skill_weight_adjust, _cnt._skill_map);
	}
	else
	{
		_cnt._skill_map = _template->_skill_map;
	}

	if (_cnt.prob_battle_chase < 0)
	{
		_cnt.prob_battle_chase = _template->prob_battle_chase1;
	}
	if (_cnt.prob_battle_cruise < 0)
	{
		_cnt.prob_battle_cruise = _template->prob_battle_cruise1;
	}
	if (_cnt.time_battle_cruise < 0)
	{
		_cnt.time_battle_cruise = _template->time_battle_cruise1;
	}
	if (_cnt.delta_battle_cruise < 0)
	{
		_cnt.delta_battle_cruise = _template->delta_battle_cruise1;
	}
	if (_cnt.max_range_cruise < 0)
	{
		_cnt.max_range_cruise = _template->wander_range_max;
	}
	_cnt.angle_cruise = _template->angle_cruise1;

	/*
	// 执行追击技能套的几率(0~1)
	_cnt.prob_battle_chase = 0;
	_cnt.prob_battle_cruise = 0.2;	//徘徊概率
	_cnt.time_battle_cruise = 2;	//徘徊时间
	_cnt.delta_battle_cruise = 1;	// 徘徊随机范围
	*/

	//限制属性修正不能小于-100%
	if (_cnt.hp_ratio < -1)
	{
		_cnt.hp_ratio = -1;
	}
	if (_cnt.dfs_ratio < -1)
	{
		_cnt.dfs_ratio = -1;
	}
	if (_cnt.atk_ratio < -1)
	{
		_cnt.atk_ratio = -1;
	}

	_prop.MODIFY_PROP_BY_NAME(scaleHP, (int)(_cnt.hp_ratio * 1000), creature_prop::CPT_BASE);
	_prop.MODIFY_PROP_BY_NAME(scalePhyAtk, (int)(_cnt.atk_ratio * 1000), creature_prop::CPT_BASE);
	_prop.MODIFY_PROP_BY_NAME(scaleMagAtk, (int)(_cnt.atk_ratio * 1000), creature_prop::CPT_BASE);
	_prop.MODIFY_PROP_BY_NAME(scalePhyDef, (int)(_cnt.dfs_ratio * 1000), creature_prop::CPT_BASE);
	_prop.MODIFY_PROP_BY_NAME(scaleMagDef, (int)(_cnt.dfs_ratio * 1000), creature_prop::CPT_BASE);

	int tmp_level = (_cnt.level) ? _cnt.level : _template->level;
	GenerateProperty(tmp_level, _template->prof, tmp_level, _template->exp);
	if (init_hp > 1e-6)
	{
		_prop.SetHP((int)init_hp);
	}
	_prop.SetSP(_prop.GetSPmax());
	_prop.SetIncSP(0);
	GetSkill().ModifyImmuneMask(GetTemplate()->immune_mask2, false);
	GetSkill().ModifyImmuneMask(GetTemplate()->immune_mask1, true);

	if (_cnt.init_overwhelming)
	{
		_parent->SetObjectState(gobject::STATE_NPC_INVINC);
		SetOverwhelmingMode(true);		//初始无敌状态
	}
}

void gnpc_imp::InitNPC(gobject *parent, const npc_template *pTemplate, const create_npc_t& cnt, unsigned int data_mask, const void *buf, size_t size)
{
	//npc创建后之需要初始化一次的操作
	//不需要每次重生都设置的属性
	float long_dis_sight = cnt.long_dis_sight;
	if (long_dis_sight < 1e-5 && pTemplate->broadcast_radius > 1e-5)
	{
		long_dis_sight = pTemplate->broadcast_radius;
		__PRINTF("gnpc_imp::InitNPC set long_dis_sight=%d tid=%d\n", pTemplate->broadcast_radius, cnt.tid);
	}
	gcreature_imp::Init(parent, long_dis_sight);
	_action_man.Init(action_list::MODE_NPC);
	ASSERT(pTemplate);
	override_addon_group_t oag;
	GetWorldImp()->OnNPCInit(pTemplate, &oag);
	_template = pTemplate;
	_cnt = cnt;
	gnpc *pNPC = GetParent();
	pNPC->heartbeat_latancy = cnt.spec_latancy ? cnt.heartbeat_latancy : abase::Rand(0, TICK_PER_SECOND);
	pNPC->origin_pos = cnt.origin_pos;
	pNPC->pos = cnt.pos;
	pNPC->pos += cnt.pos_offset;
	pNPC->SetDirection(cnt.dir);
	pNPC->tid = cnt.tid;
	pNPC->vis_tid = cnt.tid;
	pNPC->body_size = _template->body_size;

	//pNPC->body_height = NPC_DEFAULT_BODY_HEIGHT;
	if (_template->height > 0.1)
	{
		pNPC->body_height = _template->height;
	}
	else
	{
		pNPC->body_height = _template->body_size * SERVER_CONFIG.monster_size_height_rate;
	}

	ResetCanBeHitMask();
	pNPC->spawner_index = cnt.spawner_index;
	if (cnt.corpse_delay)
	{
		_corpse_delay = cnt.corpse_delay;
	}
	else
	{
		_corpse_delay = _template->corpse_disapear_time;
	}
	pNPC->idle_timer = 0;   //怪物创建出来默认是休眠的
	pNPC->msg_mask = 0;
	pNPC->msg_mask &= cnt.msg_mask_and;
	pNPC->msg_mask |= cnt.msg_mask_or;
//	pNPC->nation_id = GetSceneImp()->Nation();
	int npc_faction = (_cnt.faction ? _cnt.faction : _template->faction);
	/*if (npc_faction == 0 || _template->has_service || _template->cannot_attack)
	{
		if (FACTION_MASK_TO_NATION_ID(npc_faction) == 0)
		{
			npc_faction |= NATION_TO_FACTION_MASK_NPC(GetSceneImp()->Nation());
		}
	}*/
	/*
	if (FACTION_MASK_TO_NATION_ID(npc_faction) == 0)
	{
		npc_faction |= NATION_TO_FACTION_MASK_NPC(GetSceneImp()->Nation());
	}
	*/
//	__PRINTF("inti npc:%d cnt fac:0x%x, tempalte:0x%x, npc_fac:0x%x\n",
//		cnt.tid, _cnt.faction, _template->faction, npc_faction);
	InitFaction(npc_faction);
	_npc_state = cnt.state;
	_birth_pos = cnt.pos;
	_pos_offset = cnt.pos_offset;
	_birth_pos += cnt.pos_offset;
	_birth_dir = cnt.dir;
	gettimeofday(&pNPC->birth_time, NULL);
	pNPC->monster_born_protect_time = _template->monster_born_protect_time;
	//__PRINTF("npc init pos(%f,%f,%f),dir(%d)\n",_birth_pos.x,_birth_pos.y,_birth_pos.z,_birth_dir);
	_idle_time_counter = NPC_DEFAULT_IDLE_TIME;
	_is_fly = cnt.is_fly;
	/*
	__PRINTF("npc:%ld first born pos(%f,%f,%f),dir(%d),cnt pos(%.f,%f), offset(%.2f,%.2f)\n",
		Parent()->ID.id, _birth_pos.x,_birth_pos.y,_birth_pos.z,_birth_dir, _cnt.pos.x, _cnt.pos.z,
		_cnt.pos_offset.x, _cnt.pos_offset.z);
	*/
	//根据创建类型指定不同种类的AI控制器
	_ai_ctrl = ai_control::CreateAI(this, _cnt.create_type);
	/*
		//创建AI之后才能设定寿命
		GetAiMan()->SetLife(cnt.life); 寿命另说吧 $$$$$$$
		*/
	//寿命用脱战后若干秒消失的逻辑吧
	if (cnt.life)
	{
		_exist_duration_after_combat = cnt.life;
	}

	if (cnt.alias_hash)
	{
		pNPC->alias_hash = cnt.alias_hash;
		pNPC->SetObjectState(gcreature::STATE_ALIAS);
	}

	_sight_angle = _template->sight_angle;
	_sight_angle = float(_template->sight_angle) * 3.1415926 / 360; //转换成弧度的一半
	_use_sight_angle = (0 == _template->sight_angle ? false : true);
	_faction_ask_help = cnt.ask_for_help ? cnt.monster_faction_ask_help : _template->monster_faction_ask_help;
	_faction_accept_help = cnt.accept_ask_for_help ? cnt.monster_faction_accept_for_help : _template->monster_faction_can_help;
	if (_faction_accept_help)
	{
		pNPC->msg_mask |= gobject::MSG_MASK_ASK_FOR_HELP;
	}

	if (cnt.alias_hash)
	{
		pNPC->alias_hash = cnt.alias_hash;
		pNPC->SetObjectState(gcreature::STATE_ALIAS);
	}
	//加入各种服务
	pNPC->can_not_be_attack = _template->cannot_attack;
	if (MONAGGRE_INITIATIVE == _template->aggressive_mode)
	{
		++_aggro_mode;
		_fight_back = true;
	}
	else if (MONAGGRE_PASSIVE == _template->aggressive_mode)
	{
		_fight_back = true;
	}
	else if (MONAGGRE_NOFIGHTBACK_PASSIVE == _template->aggressive_mode)
	{
		_fight_back = false;
	}
	if (_template->has_service)
	{
		InitAllService();
	}
	_prop.attack_class = _template->ak_type;
	_prop.defense_class = _template->df_type;
	//设置主动和被动
	pNPC->msg_mask &= ~gobject::MSG_MASK_PLAYER_PEEP;
	if (_aggro_mode > 0)
	{
		pNPC->msg_mask |= gobject::MSG_MASK_PLAYER_PEEP;
	}

	/*
	 * 	没有特性组了
		//特性组选择
		bool has_specialty_group = false;
		if (oag.addon_group > 0 && oag.succ_rate > 0.0f)
		{
			if (abase::RandUniform() <= oag.succ_rate)
			{
				_specialty_group = oag.addon_group;
				_specialty_group_odds = 1.0f;
				has_specialty_group = true;
			}
		}
	*/
	//传送NPC时附带的额外信息
	float hp = 0.0f;
	if (data_mask)
	{
		raw_wrapper h1(buf, size);
		if (data_mask & TNDM_HP)
		{
			h1 >> hp;
		}
		if (data_mask & TNDM_BUFF)
		{
			//TODO
		}
		if (data_mask & TNDM_OWNER)
		{
			ruid_t owner_id, team_id;
			XID owner, team;
			h1 >> owner_id >> team_id;
			MAKE_XID(owner, owner_id);
			team = XID(GM_TYPE_TEAM, team_id);
			if (owner.IsValid() || team.IsValid())
			{
				SetOwner(owner, team);
			}
		}
		if (data_mask & TNDM_BECKON)
		{
			ruid_t master_id;
			h1 >> master_id >> (char&)_beckon_die_with_host >> _beckon_task_id;
			XID master;
			MAKE_XID(master, master_id);
			SetMaster(gnpc_imp::NPC_MASTER_TYPE_FOLLOW, master);
			pNPC->SetObjectState(gobject::STATE_ESCORT_CONVOY_NPC);
			pNPC->escort_convoy_player = master;
			pNPC->group_type = NPCGROUPTYPE_BECKON;
			pNPC->need_heartbeat |= NHM_TEMPLATE;
			//_group_type = NPCGROUPTYPE_BECKON;
			_cannot_find_host = true;   //让npc向玩家注册一下
			_beckon_fly_with_host = true;
		}
	}

	//设置NPC基础属性组 只需在这里设置一次
	_prop.IncByStruct(pTemplate->prop_ess, creature_prop::CPM_BASE);

	_ignore_max_hp2 = pTemplate->ignore_max_hp2;

	interact_template *temp = InteractManager::GetInstance().Get(pTemplate->interact_template_id);
	if (temp)
	{
		if (temp->interaction_num_max < MATTER_MAX_INTERACTION_SIZE)
		{
			GetParent()->interact_list_size = temp->interaction_num_max;
		}
		else
		{
			GetParent()->interact_list_size = MATTER_MAX_INTERACTION_SIZE;
		}
		GetParent()->interact_tid = pTemplate->interact_template_id;
		_interact_data_ptr = new interact_data<gnpc_imp>(this, temp);
		pNPC->SetObjectState(gobject::STATE_NPC_INTERACT);
	}

	if (_template->specdmg_reduce_type == MON_SPECDMG_REDUCE_FIXED)
	{
		_fixed_damage = _template->specdmg_reduce_param;
		if (_fixed_damage < 1)
		{
			_fixed_damage = 1;
		}
	}
	_leave_combat_time = 0;
	_last_peep_time = gmatrix::GetInstance().GetSysTime();
	_attach_sleep_buff_time = 0;

	if (_template->can_not_be_bless)
	{
		pNPC->SetObjectServerState(gobject::SERVER_STATE_CAN_NOT_BE_BLESS);
	}

	//生成属性
	ReInit(hp);
	_sp_restoring = false;
	_sp_restoring_tick = 0;
	_ai_ctrl->Init((bool)_template->patrol_mode, _template, &_cnt);
	LazySendTo(GM_MSG_INIT_AI, _parent->ID, 0, 1);

}

void gnpc_imp::InitHero(gobject *parent, const npc_template *pTemplate, const create_npc_t& cnt, creature_prop& prop)
{
	//npc创建后之需要初始化一次的操作
	//不需要每次重生都设置的属性
	gcreature_imp::Init(parent, cnt.long_dis_sight);
	ASSERT(pTemplate);
	_action_man.Init(action_list::MODE_NPC);
	override_addon_group_t oag;
	GetWorldImp()->OnNPCInit(pTemplate, &oag);
	_template = pTemplate;
	_cnt = cnt;
	/*if(_cnt.is_stage_npc)
	{
		_template->BuildSkillWeight(_cnt.skill_weight_adjust, _cnt._skill_map);
	}
	else*/
	{
		_cnt._skill_map = _template->_skill_map;
	}
	gnpc *pNPC = GetParent();
	pNPC->heartbeat_latancy = cnt.spec_latancy ? cnt.heartbeat_latancy : abase::Rand(0, TICK_PER_SECOND);
	pNPC->pos = cnt.pos;
	pNPC->pos += cnt.pos_offset;
	pNPC->SetDirection(cnt.dir);
	pNPC->tid = cnt.tid;
	pNPC->vis_tid = cnt.tid;
	pNPC->body_size = _template->body_size;
	pNPC->body_height = NPC_DEFAULT_BODY_HEIGHT;
	ResetCanBeHitMask();
	pNPC->spawner_index = cnt.spawner_index;
	pNPC->idle_timer = 0;   //怪物创建出来默认是休眠的
	pNPC->msg_mask = 0;
	pNPC->msg_mask &= cnt.msg_mask_and;
	pNPC->msg_mask |= cnt.msg_mask_or;
	InitFaction(_cnt.faction ? _cnt.faction : _template->faction);
	_npc_state = cnt.state;
	_birth_pos = cnt.pos;
	_pos_offset = cnt.pos_offset;
	_birth_pos += cnt.pos_offset;
	_birth_dir = cnt.dir;
	gettimeofday(&pNPC->birth_time, NULL);
	_idle_time_counter = NPC_DEFAULT_IDLE_TIME;
	_is_fly = cnt.is_fly;
	if (cnt.corpse_delay)
	{
		_corpse_delay = cnt.corpse_delay;
	}
	else
	{
		_corpse_delay = _template->corpse_disapear_time;
	}

	if (_template->has_service)
	{
		__PRINTF("gnpc_imp::InitHero::InitAllService::npc_tid=%d\n", _template->tid);
		InitAllService();
	}

	//根据创建类型指定不同种类的AI控制器
	_ai_ctrl = ai_control::CreateAI(this, _cnt.create_type);

	_sight_angle = _template->sight_angle;
	_sight_angle = float(_template->sight_angle) * 3.1415926 / 360; //转换成弧度的一半
	_use_sight_angle = (0 == _template->sight_angle ? false : true);
	_faction_ask_help = cnt.ask_for_help ? cnt.monster_faction_ask_help : _template->monster_faction_ask_help;
	_faction_accept_help = cnt.accept_ask_for_help ? cnt.monster_faction_accept_for_help : _template->monster_faction_can_help;
	if (_faction_accept_help)
	{
		pNPC->msg_mask |= gobject::MSG_MASK_ASK_FOR_HELP;
	}

	//设置主动和被动
	pNPC->msg_mask &= ~gobject::MSG_MASK_PLAYER_PEEP;
	if (_aggro_mode > 0)
	{
		pNPC->msg_mask |= gobject::MSG_MASK_PLAYER_PEEP;
	}

	//npc创建及以后每次重生都需要重新初始化的操作
	SetOverwhelmingMode(false);
	ResetCruiseTimer(true);
	_ai_ctrl->Init(true, _template, &_cnt);
	SendTo(GM_MSG_INIT_AI, _parent->ID, 0);		//这里的InitAI主要用于初始行为

	_prop = std::move(prop);
	_prop.SetImp(this);
	//_prop.SetHP(_prop.GetHPMax());
	//_prop.Constraint();
}

void gnpc_imp::InitTwin(gobject *parent, const npc_template *pTemplate, const create_npc_t& cnt)
{
	//npc创建后之需要初始化一次的操作
	//不需要每次重生都设置的属性
	gcreature_imp::Init(parent, cnt.long_dis_sight);
	ASSERT(pTemplate);
	_action_man.Init(action_list::MODE_NPC);
	override_addon_group_t oag;
	GetWorldImp()->OnNPCInit(pTemplate, &oag);
	_template = pTemplate;
	_cnt = cnt;
	gnpc *pNPC = GetParent();
	pNPC->heartbeat_latancy = cnt.spec_latancy ? cnt.heartbeat_latancy : abase::Rand(0, TICK_PER_SECOND);
	pNPC->pos = cnt.pos;
	pNPC->pos += cnt.pos_offset;
	pNPC->SetDirection(cnt.dir);
	pNPC->tid = cnt.tid;
	pNPC->vis_tid = cnt.tid;
	pNPC->body_size = _template->body_size;
	pNPC->body_height = NPC_DEFAULT_BODY_HEIGHT;
	ResetCanBeHitMask();
	InitFaction(_cnt.faction ? _cnt.faction : _template->faction);
	_birth_pos = cnt.pos;
	_birth_dir = cnt.dir;
	gettimeofday(&pNPC->birth_time, NULL);
	_idle_time_counter = NPC_DEFAULT_IDLE_TIME;
	_corpse_delay = _template->corpse_disapear_time;
	SetOverwhelmingMode(true);
}
void gnpc_imp::InitDog(gobject *parent, const npc_template *pTemplate, const create_npc_t& cnt)
{
	//npc创建后之需要初始化一次的操作
	//不需要每次重生都设置的属性
	gcreature_imp::Init(parent, cnt.long_dis_sight);
	ASSERT(pTemplate);
	_action_man.Init(action_list::MODE_NPC);
	override_addon_group_t oag;
	GetWorldImp()->OnNPCInit(pTemplate, &oag);
	_template = pTemplate;
	_cnt = cnt;
	gnpc *pNPC = GetParent();
	pNPC->heartbeat_latancy = cnt.spec_latancy ? cnt.heartbeat_latancy : abase::Rand(0, TICK_PER_SECOND);
	pNPC->pos = cnt.pos;
	pNPC->pos += cnt.pos_offset;
	pNPC->SetDirection(cnt.dir);
	pNPC->tid = cnt.tid;
	pNPC->vis_tid = cnt.tid;
	pNPC->body_size = _template->body_size;
	pNPC->body_height = NPC_DEFAULT_BODY_HEIGHT;
	ResetCanBeHitMask();
	InitFaction(_cnt.faction ? _cnt.faction : _template->faction);
	_birth_pos = cnt.pos;
	_birth_dir = cnt.dir;
	gettimeofday(&pNPC->birth_time, NULL);
	_idle_time_counter = NPC_DEFAULT_IDLE_TIME;
	_corpse_delay = _template->corpse_disapear_time;
	SetOverwhelmingMode(true);
}
void gnpc_imp::InitReplisome(gobject *parent, const npc_template *pTemplate, const create_npc_t& cnt)
{
	//npc创建后之需要初始化一次的操作
	//不需要每次重生都设置的属性
	gcreature_imp::Init(parent, cnt.long_dis_sight);
	ASSERT(pTemplate);
	_action_man.Init(action_list::MODE_NPC);
	override_addon_group_t oag;
	GetWorldImp()->OnNPCInit(pTemplate, &oag);
	_template = pTemplate;
	_cnt = cnt;
	gnpc *pNPC = GetParent();
	pNPC->heartbeat_latancy = cnt.spec_latancy ? cnt.heartbeat_latancy : abase::Rand(0, TICK_PER_SECOND);
	pNPC->pos = cnt.pos;
	pNPC->pos += cnt.pos_offset;
	pNPC->SetDirection(cnt.dir);
	pNPC->tid = cnt.tid;
	pNPC->vis_tid = cnt.tid;
	pNPC->body_size = _template->body_size;
	pNPC->body_height = NPC_DEFAULT_BODY_HEIGHT;
	ResetCanBeHitMask();
	InitFaction(_cnt.faction ? _cnt.faction : _template->faction);
	_birth_pos = cnt.pos;
	_birth_dir = cnt.dir;
	gettimeofday(&pNPC->birth_time, NULL);
	_idle_time_counter = NPC_DEFAULT_IDLE_TIME;
	_corpse_delay = _template->corpse_disapear_time;
	SetOverwhelmingMode(true);
}

void gnpc_imp::InitMech(gobject *parent, const npc_template *pTemplate, const create_npc_t& cnt)
{
	//npc创建后之需要初始化一次的操作
	//不需要每次重生都设置的属性
	gcreature_imp::Init(parent, cnt.long_dis_sight);
	ASSERT(pTemplate);
	_action_man.Init(action_list::MODE_NPC);
	override_addon_group_t oag;
	GetWorldImp()->OnNPCInit(pTemplate, &oag);
	_template = pTemplate;
	_cnt = cnt;
	gnpc *pNPC = GetParent();
	pNPC->heartbeat_latancy = cnt.spec_latancy ? cnt.heartbeat_latancy : abase::Rand(0, PLAYER_DOG_HEARTBEAT_INTERVAL_TICK);
	pNPC->pos = cnt.pos;
	pNPC->pos += cnt.pos_offset;
	pNPC->SetDirection(cnt.dir);
	pNPC->tid = cnt.tid;
	pNPC->vis_tid = cnt.tid;
	pNPC->body_size = _template->body_size;
	pNPC->body_height = NPC_DEFAULT_BODY_HEIGHT;
	ResetCanBeHitMask();
	InitFaction(_cnt.faction ? _cnt.faction : _template->faction);
	_birth_pos = cnt.pos;
	_birth_dir = cnt.dir;
	gettimeofday(&pNPC->birth_time, NULL);
	_idle_time_counter = NPC_DEFAULT_IDLE_TIME;
	_corpse_delay = _template->corpse_disapear_time;
}

bool gnpc_imp::OnHeartbeat()
{
	if (!_parent->IsZombie())
	{
		if (GetParent() && GNET::IsIdipForbidNpc(GetParent()->tid))
		{
			Die(XID());
		}

		if (_idle_time_counter > 0)
		{
			--_idle_time_counter;
		}

		if (_cnt.real_life)
		{
			if (++_exist_counter >= _cnt.real_life)
			{
				SendTo(GM_MSG_DISAPPEAR, GetParent()->ID, 0);
				return true;
			}
		}
		if (_exist_duration_after_combat)
		{
			//脱战后若干秒就要消失
			if (GetParent()->combat_state)
			{
				// 注释掉置0这一行表示总脱战时间
				//_exist_counter_after_combat = 0;
			}
			else if (++_exist_counter_after_combat == _exist_duration_after_combat)
			{
				//LifeExhaust();
				SendTo(GM_MSG_DISAPPEAR, GetParent()->ID, 0);
				return true;
			}
		}

		if (!GetParent()->combat_state && _template->can_night_sleep)
		{
			bool is_in_night_sleep_time = IsInNightSleepTime();
			if (_parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP))
			{
				if (!is_in_night_sleep_time)
				{
					EndNightSleep(ENSR_BY_DAY, 0);
				}
			}
			else
			{
				time_t now_time = gmatrix::GetInstance().GetSysTime();
				if (is_in_night_sleep_time && _leave_combat_time >= 0 && now_time - _leave_combat_time >= LEAVE_COMBAT_NIGHT_SLEEP_TIMEOUT && now_time - _last_peep_time >= PEEP_NIGHT_SLEEP_TIMEOUT)
				{
					BeginNightSleep();
				}
			}
		}
	}
	else if (Parent()->CheckObjectState(gobject::STATE_NPC_PLAYER) &&
	         ((GetWorldImp()->GetType() == WT_CENTER_BATTLE && (GetWorldImp()->GetCategory() == GNET::INSTANCE_CENTER_TEAM_ARENA_BATTLE || GetWorldImp()->GetCategory() == GNET::INSTANCE_CENTER_TEAM_ARENA_BATTLE_NEW)) ||
	          (((gplayer_npc *)Parent())->fake_roleid != 0 && ((gplayer_npc *)Parent())->fake_roleid == GetWorldImp()->GetTeamMemberNpcRoleid())))
	{
		++_dead_time_count;
		if (_dead_time_count >= _player_npc_revive_delay_time)
		{
			//中心战场的玩家NPC死亡会复活，不会消失
			_npc_state = NPC_STATE_SPAWNING;
			LeaveScene();
			Reborn(GetSceneImp());
		}
	}
	else if (_corpse_delay > 0)
	{
		++_dead_time_count;
		if (_dead_time_count >= _corpse_delay)
		{
			if (Parent()->CheckObjectState(gobject::STATE_NPC_PLAYER) &&
			        ((GetWorldImp()->GetType() == WT_CENTER_BATTLE && GetWorldImp()->GetCategory() != GNET::INSTANCE_CENTER_SINGLE_PUBG_BATTLE && GetWorldImp()->GetCategory() != GNET::INSTANCE_CENTER_TEAM_PUBG_BATTLE) ||
			         (((gplayer_npc *)Parent())->fake_roleid != 0 && ((gplayer_npc *)Parent())->fake_roleid == GetWorldImp()->GetTeamMemberNpcRoleid())))
			{
				//中心战场(除吃鸡战场)的玩家NPC死亡会复活，不会消失
				_npc_state = NPC_STATE_SPAWNING;
				LeaveScene();
				Reborn(GetSceneImp());
			}
			else
			{
				//这里让npc消失
				//只有尸体残留的才需要发这个，没有尸体的在npc_dead中就消失掉
				SendTo(GM_MSG_OBJ_ZOMBIE_END, _parent->ID, 0);
			}
			_dead_time_count = 0;
			return true;
		}
	}
	if (_template)
	{
		/*
		if(_template->is_guard && IsAggressive())
		{
			//卫兵逻辑  这个逻辑应该放在AI里更好 $$$$AI
			if(!IsCombatState() && 0 == _aggro_man.Size())
			{
				GatherTarget();
			}
		}
		//XOM 去除卫兵逻辑了
		*/

		//检查并重置求爱NPC归属 -> DS负责重置归属
		//if(NULL != _template->npc_data.woo_service_template && GetParent()->HaveWooOwner())
		//{
		//	unsigned int curtime = TaskInterface::GetCurTime();
		//	const ATaskTempl* pTaskTempl = GetTaskTemplMan()->GetTaskTemplByID(_template->npc_data.woo_service_template->id_task_lib_belong);
		//	if(NULL != pTaskTempl
		//	   && TASK_SUCCESS == pTaskTempl->CheckTimetable(curtime)
		//	   && !pTaskTempl->CheckTimetableEx(GetParent()->woo_update_time, curtime))
		//	{
		//		woo_manager::GetInstance().ResetNPCWooOwner(GetParent()->tid);
		//	}
		//}
		GetParent()->can_not_be_selected = IsInBornProtectTime();

		if (GetParent()->combat_state)
		{
			SyncDamagePlayerMapByNpcTid();
		}
		SyncPlayerDamageToSceneByNpcTid();
		SyncCorpsDamageToSceneByNpcTid();
		DamageCorpsRegionSend();
		DamagePlayerRegionSend();
	}

	//把npc功能的心跳函数加上，提供一些限时的功能
	if (_service)
	{
		_service->OnHeartbeat();
	}

	if (!_parent->IsZombie() && _ai_ctrl)
	{
		_ai_ctrl->Heartbeat();
	}
	if (_sp_restoring)
	{
		++_sp_restoring_tick;
		if (!(_sp_restoring_tick % 2))
		{
			// 恢复硬直条
			if (GetTemplate()->sp_restore_tm)
			{
				IncSP(GetProperty().GetSPmax() * 2 / GetTemplate()->sp_restore_tm);
			}
		}
	}

	if (_event_region)
	{
		_event_region->Update(this);
	}

	return gcreature_imp::OnHeartbeat();
}

void gnpc_imp::OnDispatchMessage(const MSG& msg)
{
	//$$$$$$ 注意NPCde状态和属性更新的冲突
	MessageHandler(msg);
}

void gnpc_imp::MessageHandler(const MSG& msg)
{
	if (NPC_STATE_SPAWNING == _npc_state)
	{
		return;
	}

	switch (msg.message)
	{
	case GM_MSG_SERVICE_HELLO:
	{
		if (GetSceneImp()->IsTimeStill())
		{
			return;
		}
		//这里告诉npc有人要跟他说话了，徐杰要求没有服务也需要停下
		WantCommunicateWithSomeOne(XID(), 0);
		if (_template)
		{
			if (!_template->has_service)
			{
				return;
			}
		}
		if (!_service)
		{
			const link_id_t& lid = *(const link_id_t *)msg.content;
			send_ls_command<S2C::CMD::npc_greeting>(lid, _parent->ID);
			return;
		}
		if (msg.param2 == GetSceneImp()->GetTag() && squared_distance(msg.pos, _parent->pos) < SQUARED_MAX_SERVICE_RANGE)  //距离限制
		{
			//TODO yuzhitao，阵营关系还没调整好，暂时先不检查阵营了
			//if(!(msg.param & GetEnemyFaction()))
			{
				//为了特殊处理邮件服务打的补丁(邮件不走service_serve了，所以在这里提前搞一下)
				char pet_index = 0;
				int service_type = 0;
				service_provider *provider = _service->GetProvider(SID_MAIL);
				if (provider)
				{
					pet_index = provider->GetPetIndex();
					service_type = SID_MAIL;
				}
				SendTo3(GM_MSG_SERVICE_GREETING, msg.source, 0, service_type, pet_index);
			}
		}
	}
	return;

	case GM_MSG_SERVICE_REQUEST:
	{
		if (GetSceneImp()->IsTimeStill())
		{
			return;
		}
		if (_template)
		{
			if (!_template->has_service)
			{
				return;
			}
		}

		//随身服务npc就不做距离检查了
		if (_parent->ID != GetSceneImp()->GetSelfServiceNPC())
		{
			if (squared_distance(msg.pos, _parent->pos) > SQUARED_MAX_SERVICE_RANGE) //距离限制
			{
				SendTo(GM_MSG_ERROR_MESSAGE, msg.source, S2C::ERR_SERVICE_UNAVILABLE);
				return ;
			}
		}
		//对服务的请求到来(要求服务)
		if (!_service)
		{
			return;
		}

		service_provider *provider = _service->GetProvider(msg.param);
		if (provider)
		{
			provider->PayService(msg.source, msg.content, msg.content_length);
		}
		else
		{
			SendTo(GM_MSG_ERROR_MESSAGE, msg.source, S2C::ERR_SERVICE_UNAVILABLE);
		}
	}
	return;

	case GM_MSG_SERVICE_GET_CONTENT:
	{
		ASSERT(sizeof(link_id_t) == msg.content_length);
		const link_id_t& lid = *(const link_id_t *)msg.content;
		if (!_service)
		{
			return;
		}
		service_provider *provider = _service->GetProvider(msg.param);
		if (!provider)
		{
			return;
		}
		provider->GetServiceContent(msg.source, lid, msg.param2 == 1);
	}
	return;

	case GM_MSG_SERVICE_RESULT:
	{
		if (_template)
		{
			if (!_template->has_service)
			{
				return;
			}
		}
		if (!_service)
		{
			return;
		}
		service_provider *provider = _service->GetProvider(msg.param);
		if (provider)
		{
			provider->EndService((char)msg.param2, msg.content, msg.content_length);
		}
	}
	return;

	case GM_MSG_OBJ_ZOMBIE_END:
	{
		if (!_parent->IsZombie())
		{
			return;
		}
		LifeExhaust();
	}
	return;

	case GM_MSG_WATCHING_YOU:
	{
		//能收到这个消息的已经是主动怪了，不用再判断了
		gnpc *pNPC = GetParent();
		ASSERT(sizeof(msg_watching_t) == msg.content_length);
		if (_parent->IsZombie())
		{
			return;
		}
		//初始一段时间怪物是傻的
		if (_idle_time_counter)
		{
			return;
		}
		if (GetOverwhelmingMode())
		{
			return;
		}
		msg_watching_t *pMsg = (msg_watching_t *)msg.content;
		if (!(pMsg->faction & GetEnemyFaction()))
		{
			// 帮派基地逻辑
			if (!(GetWorldImp()->GetMafiaID() && MafiaAttackCheck(Parent()->ID, GetParent()->mafia_id, msg.source, pMsg->mafia_id)))
			{
				return; //阵营不匹配
			}
		}
		//位置问题
		//A3DVECTOR3 npc_pos = _parent->pos;
		if (squared_distance(msg.pos, _parent->pos) > GetSquaredSightRange())
		{
			return; //太远了
		}
		_last_peep_time = gmatrix::GetInstance().GetSysTime();

		bool is_night_sleep = _parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP);
		bool has_sleep_buff = GetSkill().CheckTypeMask(WMSKILL::TYPEMASK_SLEEP);
		if (is_night_sleep || has_sleep_buff)
		{
			// 睡眠视野
			if (squared_distance(msg.pos, _parent->pos) > GetSquaredNightSightRange())
			{
				return;
			}
			if (is_night_sleep)
			{
				EndNightSleep(ENSR_ENEMY_ENTER_SIGHT, msg.source.id);
			}
			if (has_sleep_buff)
			{
				time_t now_time = gmatrix::GetInstance().GetSysTime();
				if (_attach_sleep_buff_time > 0 && now_time - _attach_sleep_buff_time >= SLEEP_BUFF_LOCK_TIME)
				{
					GetFilterMan().ClearSpecFilter2(WMSKILL::TYPEMASK_SLEEP, 0);
				}
			}
		}
		else if (_use_sight_angle)
		{
			//绝大多数怪是没这个属性的
			//角度方向问题
			A3DVECTOR3 offset = msg.pos;
			offset -= _parent->pos;
			offset.Normalize();
			if (offset.SquaredMagnitude() > 1e-3)
			{
				//在位置不重合的前提下
				//位置重合直接引怪
				A3DVECTOR3 dir = pNPC->GetDirection();
				dir.Normalize();
				float angle = acos(dot_product(offset, dir));
				//__PRINTF("%f,%f\n",angle,_aggro_man.GetSightAngle());
				if (angle > _sight_angle)
				{
					return;
				}
			}
		}
		if (pNPC->owner.IsValid() || pNPC->owner_team.IsValid())
		{
			if (msg.source != pNPC->owner && ( !pNPC->owner_team.IsValid() || pMsg->team != pNPC->owner_team.id) && pMsg->master != pNPC->owner)
			{
				return; //专属怪填成主动怪的话，只主动攻击主人吧
			}
		}

		if (!CheckCanAttack(msg.source))
		{
			return;
		}

		//pMsg->level 怎么用的问题,问策划,不用了!!!
		//其他判断
		//不要判断通路图问题,需要弓箭手隔河射这类东西
		//仇恨接口
		if (_ai_ctrl->GetAggroMan().AddAggroForAG(msg.source))
		{
			TriggerAggro(msg.source);
		}
	}
	return;

	case GM_MSG_GEN_AGGRO:
	{
		ASSERT(sizeof(msg_aggro_info_t) == msg.content_length);
//			const msg_aggro_info_t& mai = *(const msg_aggro_info_t*)msg.content;
		//	AddRage(mai.source,mai.aggro);
	}
	return;

	case GM_MSG_MINE_ALARM:
	{
		ASSERT(sizeof(msg_mine_alarm_t) == msg.content_length);
		const msg_mine_alarm_t& mma = *(const msg_mine_alarm_t *)msg.content;
		if (!(mma.faction & GetFaction()))
		{
			return;
		}
		if (!CheckCanAttack(mma.target))
		{
			return;
		}
		if (!(GetEnemyFaction() & mma.target_faction))
		{
			return;
		}
		_ai_ctrl->GetAggroMan().AddAggroForHelp(mma.target);
		TriggerAggro(mma.target);
	}
	return;

	case GM_MSG_PLAYER_KILLED_BY_NPC:
	{
		ASSERT(0 == msg.content_length);
		_ai_ctrl->EventKillTarget(msg.source);
	}
	return;

	case GM_MSG_ENTER_INSTANCE:
	{
		__PRINTF("玩家: "   FMT_I64" 申请进入副本tid: %d id:%d\n", msg.source.id, msg.param, msg.param2);
		//if(_template->npc_data.id_instance != msg.param) return;
		SendTo3(GM_MSG_ENTER_INSTANCE, msg.source, msg.param, msg.param2, msg.param3, msg.content, msg.content_length);
		__PRINTF("ENTER_INSTANCE: id support:%d, wanted:%d.\n", _template->npc_data.id_instance, msg.param);
	}
	return;

	case GM_MSG_INIT_AI:
	{
		//由于心跳可能先来，这个消息只是一个推荐
		_ai_ctrl->InitBehavior();
	}
	return ;

	/*$$$$$$$AI
			case GM_MSG_AI_EVENT_NOTIFY:
			{
				if (msg.param == 1) //调试用
				{
					if(!(msg.param2 & GetEnemyFaction()))
					{
						return; //阵营不匹配
					}
					if(!CheckCanAttack(msg.source))
					{
						return;
					}
					_aggro_man.AddAggroForAG(msg.source);
					TriggerAggro(msg.source);
				}
				else
					_ai_man->RecvScriptEvent(msg.source, msg.param, msg.param2);
			}
			return;
			*/

	/*$$$$$AI
			case GM_MSG_PLAYER_DEATH:
			{
				_ai_man->OnPlayerDeath(msg.source);
			}
			return;
			*/

	case GM_MSG_NPC_USE_SKILL:
	{
		//这个指令只会来自策略，有时候策略被清掉了，这条异步信息才到，导致多放一次技能，这里拦一下
		//param: skill id, param2: skill level, param2: policy id
		if (!GetAiCtrl() || !GetAiCtrl()->GetPolicy() || GetAiCtrl()->GetPolicy()->GetID() != msg.param3)
		{
			return;
		}
		if (_parent->IsZombie())
		{
			return;
		}
		if (_no_ai_skill)
		{
			return;
		}
		ASSERT(sizeof(XID) == msg.content_length);
		const XID& target = *(const XID *)msg.content;

		int skill_id = msg.param;
		int skill_level = msg.param2;
		ai_control::SKILL_SET skill_set;
		skill_set.push_back(ai_control::skill_t( skill_id, skill_level ));

		if (_ai_ctrl)
		{
			//加入任务
			_ai_ctrl->ClearMission();
			ClearAction();
			ai_mission_skill_set *pMission = new ai_mission_skill_set(target, skill_set, true, A3DVECTOR3());
			_ai_ctrl->StartMission(pMission);
		}
	}
	return;

	/*$$$$$$AI  这里的逻辑需要修改，因此也先注释掉了
			case GM_MSG_NPC_SPEC_SKILL_MOVE:
			{
				_ai_man->AddStartTask(new ai_task_spec_skill_move(_ai_man,msg.param,msg.param2,msg.param3));
			}
			return;
			*/

	case GM_MSG_EMOTE:
	{
		//发来什么发回去就好了,其他的客户端去模拟选择
		_runner->do_emote(msg.source, msg.param);
	}
	return;

	//目前只有调试命令使用这个： 其他地方不要用
	case GM_MSG_MONSTER_SKILL:
	{
		int skill_id = msg.param;
		int skill_level = msg.param2;
		ai_control::SKILL_SET skill_set;
		skill_set.push_back(ai_control::skill_t( skill_id, skill_level ));

		if (_ai_ctrl)
		{
			//加入任务
			_ai_ctrl->ClearMission();
			ClearAction();
			ai_mission_skill_set *pMission = new ai_mission_skill_set(msg.source, skill_set, true, msg.pos);
			_ai_ctrl->StartMission(pMission);
		}
	}
	return;
	/*

	宠物逻辑，考虑删除。 或进行大修改
		case GM_MSG_MASTER_MOVE:
		{
			if(!_master.IsValid()) return;
			if(msg.source != _master) return;
			ASSERT(sizeof(msg_master_move_t) == msg.content_length);
			const msg_master_move_t& mmm = *(const msg_master_move_t*)msg.content;
			_ai_man->OnMasterMove(mmm.pos,mmm.face_dir,mmm.move_dir,mmm.stop_flag,mmm.body_size);
		}
		return;
		*/

	case GM_MSG_MASTER_DEAD:
	{
		LifeExhaust();
	}
	return;

	case GM_MSG_OWNER_TELL_DIE:
	{
		gnpc *pNPC = GetParent();
		if (!pNPC)
		{
			return;
		}
		if (msg.source != pNPC->owner)
		{
			return;
		}
		if (msg.param > 0)
		{
			//校验模板id
			if (msg.param != pNPC->tid)
			{
				return;
			}
		}
		LifeExhaust();
	}
	return;

	case GM_MSG_VANISH:
	{
		gnpc *pNPC = (gnpc *)_parent;
		if (pNPC->pGrouping)
		{
			//这是关卡逻辑，处理NPC死亡的地方，关卡掉落应该在这里完成比较好
			pNPC->pGrouping->imp->OnNPCDeath(pNPC, _cnt.stage_id, _cnt.wave_id, _cnt.origin_pos, false, true, _template);
		}
		if (_ai_ctrl)
		{
			_ai_ctrl->EventOnDeath(XID());
		}
		_leave_scene_type = 2;	//告诉客户端让NPC直接消失，不渐隐
		LifeExhaust();
	}
	return;

	case GM_MSG_MASTER_ROLLBACK:
	{
		if (_cnt.bClearWhenNotFight)
		{
			LifeExhaust();
		}
	}
	return;

	case GM_MSG_NPC_PARAM:
	{
		int key = msg.param2;
		int value = msg.param3;
		char buf[1024];
		char buf2[2048];
		memset(buf, 0, 1024);
		memset(buf2, 0, sizeof(buf2));
		raw_wrapper ar;
		switch (msg.param)
		{
		case 0:
		{
			//设置
			_self_param.SetValue(key, value);
			sprintf(buf, "set npc param:Var[%d] + (%d) ----> Result:%d", key, value, _self_param.GetValue(key));
			for (size_t i = 0; i < strlen(buf); ++i)
			{
				buf2[i * 2] = buf[i];
			}
			ar.push_back(buf2, 2 * strlen(buf));
		}
		break;

		case 1:
		{
			//修改
			_self_param.ModifyValue(key, value);
			sprintf(buf, "modify npc param:Var[%d] + (%d) ----> Result:%d", key, value, _self_param.GetValue(key));
			for (size_t i = 0; i < strlen(buf); ++i)
			{
				buf2[i * 2] = buf[i];
			}
			ar.push_back(buf2, 2 * strlen(buf));
		}
		break;

		default:
		case 2:
		{
			//察看
			if (msg.param4)
			{
				//察看所有
				sprintf(buf, "npc param total:%d", (int)_self_param.GetMap().size());
				for (size_t i = 0; i < strlen(buf); ++i)
				{
					buf2[i * 2] = buf[i];
				}
				ar.push_back(buf2, strlen(buf) * 2);
				for (std::map<int, int>::iterator it = _self_param.GetMap().begin(); it != _self_param.GetMap().end(); ++it)
				{
					memset(buf, 0, sizeof(buf));
					memset(buf2, 0, sizeof(buf2));
					sprintf(buf, "var[%d]=%d", it->first, _self_param.GetValue(it->first));
					for (size_t i = 0; i < strlen(buf); ++i)
					{
						buf2[i * 2] = buf[i];
					}
					ar.push_back(buf2, strlen(buf) * 2);
				}
			}
			else
			{
				sprintf(buf, "npc param var[%d]=%d", key, _self_param.GetValue(key));
				for (size_t i = 0; i < strlen(buf); ++i)
				{
					buf2[i * 2] = buf[i];
				}
				ar.push_back(buf2, 2 * strlen(buf));
			}
		}
		break;
		}
		SendTo(GM_MSG_NPC_PARAM, msg.source, 0, ar.data(), ar.size());
	}
	return;
	case GM_MSG_MAFIA_UPDATE:
	{
		if (!_template || !_template->npc_data.is_for_faction_only)
		{
			return;
		}
	}
	return;
	case GM_MSG_SYS_SPEAK_GET_INFO:
	{
		//系统喊话异步获取所需信息的请求从玩家返回
		msg_sys_speak_get_info_t& get_info = *(msg_sys_speak_get_info_t *)msg.content;
		raw_wrapper h1(get_info.data, get_info.data_size);
		if (get_info.mask == 0)
		{
			//处理完了
			SendSystemSpeak(get_info.speak_id, get_info.content_index, get_info.channel, XID(), h1);
		}
		else
		{
			if (ParseAsyncSpeak(get_info.speak_id, get_info.content_index, get_info.mask, get_info.channel, h1))
			{
				//还需要向其他player获取
				break;
			}
			//来到这里，说明有不应出现的通配符
			gnpc *parent = GetParent();
			GLog::log(GLOG_ERR, "speak_id=%d,invalid mask:"   FMT_I64",npc_tid=%d,pos=%d(%f,%f,%f)\n", get_info.speak_id, get_info.mask, parent->tid, GetRealSceneImp()->GetTag(), parent->pos.x, parent->pos.y, parent->pos.z);
		}
	}
	return;
	case GM_MSG_GUIDE_READY:
	{
		_guide = msg.source;
	}
	return;
	/*$$$$$$AI
	case GM_MSG_GUIDE_MOVE:
	{
		object_interface oif(this);
		int skill_id = msg.param;
		int skill_level = msg.param2;
		A3DVECTOR3 target_pos = *(A3DVECTOR3*)msg.content;
		//如果target_pos与当前位置小于1.5米，以msg.pos2的方向为准
		//__PRINTF("distance = %f\n",sqrt(squared_distance(Parent()->pos,target_pos)));
		if(squared_distance(Parent()->pos,target_pos) < 2.25f)
		{
			target_pos = Parent()->pos + msg.pos2;
		}
		if(!_ai_man->AddStartTask(new ai_task_test_sprint(_ai_man,msg.source,skill_id,skill_level,target_pos)))
		{
			SendTo(GM_MSG_GUIDE_MOVE,msg.source,-1);
		}
	}
	return;
	case GM_MSG_GUIDE_TARGET:
	{
		//询问仇恨对象
		XID player, team;
		_aggro_man.GetFirstPlayer(player,team);
		if(player.IsValid())
		{
			SendTo(GM_MSG_GUIDE_TARGET,msg.source,0,&(player),sizeof(player));
		}
	}
	return;
	*/
	case GM_MSG_TRANSFER_NPC:
	{
		// msg.param: 目标场景scene_tag, msg.param2:场景镜像id，msg.param3: 数据掩码, msg.pos: 目标坐标
		int mask = msg.param3;
		if (!(mask & (TNDM_TID | TNDM_CREATE)))
		{
			__PRINTF("NPC传送请求未传递模板id\n");
			return;
		}
		raw_wrapper h1(768);
		h1 << mask;
		if (mask & TNDM_TID)
		{
			h1 << _cnt.tid;
		}
		if (mask & TNDM_CREATE)
		{
			_cnt.Save(h1);
		}
		if (mask & TNDM_HP)
		{
			h1 << _prop.GetHP();
		}
		if (mask & TNDM_BUFF)
		{
			//TODO
		}
		if (mask & TNDM_OWNER)
		{
			gnpc *pNPC = GetParent();
			h1 << pNPC->owner.id << pNPC->owner_team.id;
		}
		if (mask & TNDM_BECKON)
		{
			h1 << _master.id << (char)_beckon_die_with_host << _beckon_task_id;
		}

		ruid_t new_id;
		int result = transfer_npc(msg.param, msg.param2, msg.pos, h1.data(), h1.size(), new_id); //参数很冗，因为也要给gamed用
		if (result == 0)
		{
			//目标场景在同线，直接创建成功
			npc_on_transfer(_parent->ID.id, 0, new_id, msg.param, msg.param2, msg.pos, mask);
		}
		else if (result == -2)
		{
			//目标场景在其他线
			GSP::GetInstance().SendTransferNPC(_parent->ID.id, msg.param, msg.param2, msg.pos, mask, h1.data(), h1.size());
		}

	}
	return;
	case GM_MSG_NPC_TRANSFERRED:
	{
		if (msg.param == 0)
		{
			LifeExhaust();
		}
		else
		{
			//20秒后重试
			int delay_tick = SECOND_TO_TICK(20);
			if (delay_tick >= MAX_MESSAGE_LATENCY_TICK)
			{
				delay_tick = MAX_MESSAGE_LATENCY_TICK - 1;
			}
			gmatrix::GetInstance().LazySendTo3(GM_MSG_TRANSFER_NPC, _parent, msg.pos, _parent->ID, msg.param2, msg.param3, msg.param4, delay_tick);

		}
	}
	return;

	case GM_MSG_QUERY_NPC_PARAM:
	{
		ASSERT(sizeof(link_id_t) == msg.content_length);
		const link_id_t& lid = *(const link_id_t *)msg.content;
		int value = 0;
		bool in_use = _self_param.GetValue(msg.param, value);
		send_ls_command<S2C::CMD::gs_debug_query_npc_param_re>(lid, _parent->ID.id, msg.param, in_use, value);
	}
	return;

	case GM_MSG_SET_NPC_HP_PERCENT:
	{
		int hp_percent = msg.param;
		if (hp_percent < 0)
		{
			hp_percent = 0;
		}
		if (hp_percent > 100)
		{
			hp_percent = 100;
		}
		int64_t hp = GetHPMax() * (float)hp_percent * 0.01f;
		_prop.SetHP(hp);
	}
	return;

	/*
	case GM_MSG_GATHER_INFO_NOTIFY:
	{
		if(msg.param != GATHER_INFO_TORTURE && msg.param != GATHER_INFO_DINNER) return;
		__PRINTF("怪物："   FMT_I64" tortue_count=%d.\n",GetParent()->ID.id,_torture_count);
		if(msg.param2) _torture_count--;
		_torture_request_time = 0;
		if(_torture_count <= 0)
		{
			SendTo(GM_MSG_DEATH,GetParent()->ID,0); // 需要掉OnDeath函数
			__PRINTF("怪物："   FMT_I64" 被刑讯至死了.\n",GetParent()->ID.id);
		}
	}
	return;
	case GM_MSG_CAST_SKILL:
	{
		//param: id,level is_buff
		//__PRINTF("msg_cast_skill,id=%d,level=%d\n",msg.param,msg.param2);
		_ai_man->AddStartTask(new ai_task_skill(_ai_man,XID(),msg.param,msg.param2,msg.param3));
	}
	return;
	case GM_MSG_AI_BEHAVIOR:
	{
		_ai_man->AddBehavior(msg.source,msg.param,msg.param2,msg.param3);
	}
	return;
	case GM_MSG_AI_STAY_AROUND_BEGIN:
	{
		ASSERT(msg.content_length == sizeof(float));
		float max_distance = *(float*)msg.content;
		_ai_man->StayAround(msg.pos,msg.param,msg.param2,max_distance);
	}
	return;
	case GM_MSG_AI_STAY_AROUND_MOVE:
	{
		ASSERT(msg.content_length == sizeof(float));
		float max_distance = *(float*)msg.content;
		if(msg.param == 0)
		{
			//与固守逻辑相匹配的移动，需要维护_no_ai_skill
			if(max_distance >= 0.0f)
			{
				ai_task* pTask = new ai_task_move_around_pos(_ai_man,msg.pos,max_distance);
				if(!pTask) return;
				if(!_ai_man->AddStartTask(pTask))
				{
					_no_ai_skill = true;	//暂时不要加新的ai skill指令的，先给移动让路
				}
			}
			else
			{
				//npc以为自己偏离了安全位置，但其实仍处在安全区域中
				_no_ai_skill = false;
			}
		}
		else
		{
			//独立的单次移动
			ai_task* pTask = new ai_task_move_around_pos(_ai_man,msg.pos,max_distance);
			if(!pTask) return;
			_ai_man->AddStartTask(pTask);
		}
	}
	return;
	*/
	case GM_MSG_REFRESH_ENEMY_FACTION:
	{
		//param: 敌对关系表id
		ChangeFaction(GetFaction(), msg.param);
	}
	return;
	case GM_MSG_ALLIANCE_UPDATE:
	{
		ChangeFaction(GetFaction(), 0);
	}
	return;


	/*case GM_MSG_TIZI_INFO_SYNC:
	{
		if (msg.param <= 0 || _template->id_graffiti_box != msg.param)
		{
			return;
		}
		if (msg.content_length > MAX_TIZI_LENGTH)
		{
			return;
		}
		memcpy(GetParent()->tizi_data, msg.content, msg.content_length);
		GetParent()->tizi_size = msg.content_length;
		GetParent()->pen_id = msg.param2;
		GetParent()->tizi_style = (unsigned char)msg.param3;
		_runner->npc_tizi_info_change();
		__PRINTF("npc: tiziupdate id:"   FMT_I64" board_id:%d pen_id:%d style:%d content_length:%d mafia_id:%ld scene_id:%d.\n"
		         , Parent()->ID.id, msg.param, msg.param2, msg.param3, (int)msg.content_length, Parent()->mafia_id, GetRealSceneImp()->GetTag());
	}
	return;
	*/

	case GM_MSG_PAUSE_PATH:
	{
		//param: true为暂停，false为恢复
		//param2: 是否设置原点
		_pause_path = msg.param;
		if (msg.param2)
		{
			SetOrigPosition(_parent->pos, _parent->dir);
		}
	}
	return;

	/*$$$$$$AI 这个事件可能也是需要调用的， 但是目前的需求的场景很小，完全不需要用事件完成
			case GM_MSG_CHANGE_PATH:
			{
				//param: path_id param2: patrol_type, param3: is_run
				if(_ai_man)
				{
					_ai_man->ChangePath(msg.param,msg.param2,msg.param3,true);
				}
			}
			return;
			*/

	case GM_MSG_NPC_ACTION_END_EVENT:
	{
		if (_ai_ctrl)
		{
			_ai_ctrl->EventActionEnd(msg.param, msg.param2, msg.param3);
		}
	}
	return ;

	case GM_MSG_NPC_ACTION_FAILED_EVENT:
	{
		if (_ai_ctrl)
		{
			_ai_ctrl->EventActionStartFailed(msg.param, msg.param2, msg.param3);
		}
	}
	return ;

	case GM_MSG_NPC_TRIGGER_AI_PROCESS:
	{
		if (!_parent->IsZombie() && _ai_ctrl)
		{
			switch (msg.param)
			{
			case 0:
				_ai_ctrl->EventAINextProcess();
				break;
			case 1:
			{
				const double *pValue = (const double *)msg.content;
				int size = msg.content_length / sizeof(double);
				std::vector<double> list(pValue, pValue + size);
				_ai_ctrl->EventAIMsg(list);
			}
			break;
			default:
				break;
			}
		}
	}
	return;

	case GM_MSG_LEVEL_SET_BLOCK_MODE:
	{
		_cg_block_action = msg.param;
		_ai_ctrl->EventLevelBlockAction(msg.param);
	}
	return;

	case GM_MSG_PLAYER_INIT_STAGE:
	{
		int is_first_stage = msg.param;
		ASSERT(msg.content_length == sizeof(msg_player_init_stage));
		const msg_player_init_stage& data = *(const msg_player_init_stage *)msg.content;
		bool set_pos = is_first_stage;

		if (_event_region)	//检查是否在限制区域中
		{
			_event_region->Update(this);
			if ( !_event_region->InRetrictArea())
			{
				set_pos = true;
			}
		}

		if (set_pos)
		{
			//如果是第一个Stage，或者不在限制区域内，就需要被传送进去，传送的坐标点由定位点确定，这里由策划保证定位点应该在限制区域内
			ClearAction();
			A3DVECTOR3 pos(msg.pos);
			pos -= _parent->pos;
			StepMove(pos);
			GetParent()->SetDirection(data.dir);
			_runner->object_move(_parent->pos, GP_MOVE_POS_NOTIFY | GP_MOVE_DIR_DIFF | GP_MOVE_STOP, 0, _parent->dir, 0, 0xFFFF, 0, 0, 0, 0);
		}

	}
	return;

	case GM_MSG_DELAY_LONG_JUMP:
	{
		ClearAction();
		A3DVECTOR3 pos(msg.pos);
		pos -= _parent->pos;
		_cnt.pos = msg.pos;
		StepMove(pos);
		_runner->object_move(_parent->pos, GP_MOVE_POS_NOTIFY | GP_MOVE_DIR_DIFF | GP_MOVE_STOP, 0, _parent->dir, 0, 0xFFFF, 0, 0, 0, 0);

		__PRINTF("npc: GM_MSG_DELAY_LONG_JUMP id=%ld pos(%.2f,%.2f,%.2f)\n", _parent->ID.id, msg.pos.x, msg.pos.y, msg.pos.z);
	}
	return;

	case GM_MSG_CHANGE_FACTION:
	{
		ChangeFaction(msg.param2, 0);

		__PRINTF("npc: GM_MSG_CHANGE_FACTION id=%ld faction=%d\n", _parent->ID.id, msg.param2);
	}
	return;

	case GM_MSG_RUNINTO_NPC:
	{
		if (GetSceneImp()->IsTimeStill())
		{
			return;
		}
		if (IsMonster())
		{
			return;
		}
		if (!_template || _template->impact_type != 2)
		{
			return;
		}
		WantCommunicateWithSomeOne(XID(), 0);

		//客户端不再显示npc被别人碰撞，不需要广播了
		/*
		PB::gp_runinto_npc_res proto;
		proto.set_npc_id(_parent->ID.id);
		proto.set_effect(msg.param);
		Runner()->RegionSendEx<S2C::CMD::PBS2C>(msg.source, proto);
		*/
	}
	return;

	case GM_MSG_GET_BROADCAST_BUFF:
	{
		if (IsBroadcastBoss())
		{
			link_id_t lid;
			lid.index = msg.param;
			lid.sid = msg.param2;
			GetBuff().SendIconStateToOther(this, lid);
		}
	}
	return;

	case GM_MSG_NPC_CHANGE_AI_TRIGGER:
	{
		if (GetParent()->ID != msg.source)
		{
			return;
		}
		RealChangeAIPolicy(msg.param);
	}
	return;

	case GM_MSG_INTERACT_MATTER_REQUEST:
	case GM_MSG_INTERACT_MATTER_CANCEL:
	case GM_MSG_INTERACT_SPEEDUP:
	case GM_MSG_INTERACT_TRANSPORT_REQUEST:
	case GM_MSG_INTERACT_ACTIVE:
	case GM_MSG_INTERACT_MINI_GAME_ACTION:
	case GM_MSG_INTERACT_CANNON_TURN:
	case GM_MSG_INTERACT_MINI_GAME_RESET:
	case GM_MSG_INTERACT_SUB_OPERATION:
	case GM_MSG_INTERACT_INVITE:
	{
		if (_interact_data_ptr)
		{
			_interact_data_ptr->MessageHandler(msg);
		}
	}
	return;

	case GM_MSG_BATTLE_ON_DEATH:
	{
		ASSERT(msg.content_length == sizeof(action_content));
		action_content content_info = *((action_content *)msg.content);
		GetWorldImp()->OnNPCDeath(_template, 0, content_info, (bool)msg.param, (bool)msg.param2, this);
	}
	break;

	case GM_MSG_HEARTBEAT:
		break;

	case GM_MSG_CORPS_GOIAF_SET_MANIULATOR:
	{
		_parent->SetObjectState(gcreature::STATE_CORPS_GOIAF_MANIPULATOR);
		if (!msg.source.IsPlayer())
		{
			break;
		}
		object_info info;
		if (!gmatrix::GetInstance().QueryObject(msg.source, info))
		{
			break;
		}
		GetParent()->player_name_size = info.name._size;
		memcpy(GetParent()->player_name_buf, info.name._buf, info.name._size);
	}
	return;

	case GM_MSG_GARDEN_NPC_UPDATE:
	{
		gnpc *pNPC = GetParent();
		pNPC->garden_flower_id = msg.param;
		pNPC->garden_state = msg.param2;
		pNPC->garden_state_end = msg.param3 > 0;
		pNPC->garden_breed_end = msg.param4 > 0;

		PB::gp_garden_npc_update proto;
		proto.set_npc_id(_parent->ID.id);
		auto garden_attr = proto.mutable_npc_info();
		garden_attr->set_region(pNPC->garden_region);
		garden_attr->set_field(pNPC->garden_field);
		garden_attr->set_garden_flower_id(pNPC->garden_flower_id);
		garden_attr->set_garden_state(pNPC->garden_state);
		garden_attr->set_state_end(pNPC->garden_state_end);
		garden_attr->set_breed_end(pNPC->garden_breed_end);
		_runner->RegionSend<S2C::CMD::PBS2C>(proto);
	}
	return;

	case GM_MSG_CHESS_SPX_MOVE:
	{
		const A3DVECTOR3& pos = msg.pos;
		int dir = msg.param;
		Runner()->object_move(pos, GP_MOVE_STOP | GP_MOVE_POS_NOTIFY | GP_MOVE_DIR_DIFF, 0, dir, 0, 65535, 0, 0, 0, 0);
		A3DVECTOR3 temp_pos = pos;
		temp_pos -= GetParent()->pos;
		RawStepMove(temp_pos);
		SetDir(dir);
		__PRINTF("npc: GM_MSG_CHESS_SPX_MOVE id=%ld x= %f, z= %f, dir=%d\n", _parent->ID.id, msg.pos.x, msg.pos.z, msg.param);
	}
	return;

	case GM_MSG_MASTER_CAST_SKILL:
	{
		int skill_id = msg.param;
		XID target = msg.source;
		if (_master_data)
		{
			_master_data->_master_dmg_add = msg.param2;
			if (msg.content_length == sizeof(_master_data->_skill_prop))
			{
				_master_data->_skill_prop = *((property_template::data_SummonNpcMasterProp *)msg.content);
			}
		}
		if (target.IsValid())
		{
			_ai_ctrl->EventMasterCastSkill(skill_id, target.id);
		}
		else
		{
			_ai_ctrl->EventMasterCastSkill(skill_id, 0);
		}
	}
	return;

	case GM_MSG_PET_MASTER_INFO:
	{
		if (msg.content_length != sizeof(pet_master_prop))
		{
			return;
		}
		if (msg.source != _master)
		{
			return;
		}
		if (!_master_data)
		{
			return;
		}
		_master_data->_master_prop = *(pet_master_prop *)msg.content;
		//UpdateExtentInfo();
	}
	return;

	case GM_MSG_CAST_SKILL_2_ME:
	{
		object_interface oif(this);
		GetSkill().InstantSkill(oif, msg.param, msg.param2, 0, WMSKILL::CAST_PRIOR_ID, msg.source, msg.pos, false, NULL, false);
	}
	return;

	default:
		break;
	}
	//其他消息这里处理,不想处理的给creature
	gcreature_imp::MessageHandler(msg);
}

void gnpc_imp::FillAttackMsg(const XID& target, attack_msg& atk_msg)
{
	gcreature_imp::FillAttackMsg(target, atk_msg);
	gnpc *pNPC = GetParent();
	atk_msg.attacker_info.npc_tid = pNPC->tid;
	atk_msg.attacker_info.mafia_id = pNPC->mafia_id;
	if (_template)
	{
		if (_template->skillfx_prio)
		{
			atk_msg.attacker_info.attacker_mode |= attacker_info_t::AM_SKILLFX_PRIO;
		}
		atk_msg.attack_damage_rate = _template->damage_rate;
	}

	if (!_master_data)
	{
		return;
	}
	//战斗公式相关
	atk_msg.master_attack_damage_add = _master_data->_master_dmg_add;
	atk_msg.attack_crit = _master_data->_skill_prop.curCrit;
	atk_msg.attack_pierce = _master_data->_skill_prop.curPierce;

	//其他（阵营等）
	atk_msg.attacker_info.pet_master = _master;
	atk_msg.attacker_info.lid = _master_data->_master_prop.lid;
	atk_msg.attacker_info.team = _master_data->_master_prop.team;
	atk_msg.attacker_info.native_zoneid = _master_data->_master_prop.native_zoneid;
	//atk_msg.attacker_info.family_id = _master_data->_master_prop.family_id;
	atk_msg.attacker_info.mafia_id = _master_data->_master_prop.mafia_id;
	atk_msg.attacker_info.wallow_level = _master_data->_master_prop.wallow_level;
	atk_msg.attacker_info.faction = _master_data->_master_prop.faction;
	atk_msg.attacker_info.native_zoneid = _master_data->_master_prop.native_zoneid;
	//atk_msg.attacker_info.is_invader = _master_data->_master_prop.is_invader;
	atk_msg.attacker_info.enemy_faction = player_template::GetEnemyFaction(_master_data->_master_prop.faction);
	//atk_msg.attacker_info.level = _master_prop.level;
	//特意用主人等级，为了计算掉落，暂时去掉吧，最好把主人信息和宠物信息彻底分开
	atk_msg.attacker_info.married = _master_data->_master_prop.married;
	atk_msg.attacker_info.spouse_id = _master_data->_master_prop.spouse_id;
	//atk_msg.attacker_info.union_id = _master_data->_master_prop.union_id;
	atk_msg.attacker_info.duel_target = _master_data->_master_prop.duel_target;
	if (_master_data->_master_prop.pvp_protect_flag)
	{
		atk_msg.attacker_info.pk_setting_a = 0;
	}
	else
	{
		atk_msg.attacker_info.pk_setting_a = _master_data->_master_prop.pk_setting;
	}
	atk_msg.attacker_info.attacker_mode = _master_data->_master_prop.pvp_protect_flag ? 0 : attacker_info_t::AM_PVP_ENABLE;
	if (_master_data->_master_prop._region_setting & RS_SANCTUARY) //是安全区，覆盖掉AM_PVP_ENABLE//cancel
	{
		atk_msg.attacker_info.attacker_mode |= attacker_info_t::AM_PVP_SANCTUARY;
	}
	atk_msg.attacker_info.is_skill_npc = true;
}

void gnpc_imp::SendBuffData(const subscibe_id *begin, const subscibe_id *end, const PB::buff_t *buff/*=NULL*/)
{
	if (!IsBroadcastBoss())
	{
		return;
	}
	PB::gp_object_buff proto;
	proto.set_id(GetParent()->ID.id);
	if (buff)
	{
		proto.set_mode(PB::gp_object_buff::ONE);
		PB::buff_t *b = proto.add_buff_list();
		b->CopyFrom(*buff);
	}
	else
	{
		proto.set_mode(PB::gp_object_buff::ALL);
		_buff.GetBuff(proto);
	}
	_runner->RegionSend<S2C::CMD::PBS2C>(proto);
}

void gnpc_imp::SendBuffDataPersonal(const PB::buff_t& buff, const link_id_t& lid)
{
	PB::gp_object_buff_personal proto;
	proto.set_id(GetParent()->ID.id);
	proto.set_mode(PB::gp_object_buff_personal::ADD);
	PB::buff_t *b = proto.add_buff_list();
	b->CopyFrom(buff);

	packet_tla_wrapper h1(256);
	S2C::CMD::Make<S2C::CMD::PBS2C>::From(h1, proto);
	send_ls_msg(lid, h1);
}

void gnpc_imp::SendBuffDataPersonal(int buff_id, ruid_t rid, const link_id_t& lid)
{
	PB::gp_object_buff_personal proto;
	proto.set_id(GetParent()->ID.id);
	proto.set_mode(PB::gp_object_buff_personal::REMOVE);
	PB::buff_t *b = proto.add_buff_list();
	b->set_buff_id(buff_id);
	b->set_from_rid(rid);

	packet_tla_wrapper h1(256);
	S2C::CMD::Make<S2C::CMD::PBS2C>::From(h1, proto);
	send_ls_msg(lid, h1);
}

bool gnpc_imp::RebuildProp()
{
	object_level_t level = _prop.GetBasic().level;
	prof_t prof = _prop.GetBasic().prof;
	if (!_prop.Rebuild(level, prof, level, _template->id_monster_base_prop))
	{
		return false;
	}
//	EffectSpecialtys() ;$$$$$$$$$$$$$$ 这个附加属性如果要加上，其实可以早就加上的，不需要在这里加
	_prop.Constraint();
	return true;
}

void gnpc_imp::AskForHelp(float range, bool force)
{
	/*
		求救删除了
	*/
}


void gnpc_imp::ResetCruiseTimer(bool from_zero)
{
	ASSERT(_template);
	gnpc *pNPC = GetParent();
	if (from_zero)
	{
		pNPC->cruise_timer = abase::Rand(0, (int)_template->random_walk_interval);
	}
	else
	{
		pNPC->cruise_timer = (int)_template->random_walk_interval;
	}
}

bool gnpc_imp::CanRest()
{
	gnpc *pNPC = GetParent();
	if (!pNPC->pSlice->HasPlayerNearby())
	{
		if (!IsLongDisObject())
		{
			return false;    //周围没有人也不时远距离对象的话就别走了
		}
	}
	--pNPC->cruise_timer;
	if (pNPC->cruise_timer <= 0)
	{
		ResetCruiseTimer();
		return true;
	}
	return false;
}

void gnpc_imp::ServerSay(const abase::octets& msg, unsigned char channel, bool hide_speaker)
{
	AllServerBroadcastChatMsg(channel, 0, hide_speaker ? XID() : GetParent()->ID, &msg);
}

void gnpc_imp::WorldSay(const abase::octets& msg, bool hide_speaker)
{
//	GetWorldImp()->BroadcastChatMessage(hide_speaker ? 0 : GetParent()->ID.id,abase::octets(),msg,abase::octets(),abase::octets());
}

void gnpc_imp::SightSay(const abase::octets& msg, unsigned char channel, bool hide_speaker, unsigned char customize)
{
	gnpc *pNPC = GetParent();
	if (!pNPC->pSlice)
	{
		return;
	}
	SightRegionChatMsg(GetParent(), GetSceneImp(), GetParent()->pSlice, channel, 0, 0, hide_speaker ? XID() : GetParent()->ID, NULL, &msg, customize);
}

void gnpc_imp::GroupSay(const abase::octets& msg, unsigned char channel, bool hide_speaker)
{
	//护卫，废弃了，
}

void gnpc_imp::SystemSpeak(int speak_id, const XID& target)
{
	const system_speak *cfg = gmatrix::GetInstance().GetSpeakConfig(speak_id);
	if (!cfg || cfg->speak_id != speak_id)
	{
		return;
	}
	if (!GetSceneImp())
	{
		return;
	}
	//先随机选一个content_index
	int content_index = abase::Rand(0, cfg->content_num - 1);
	uint64_t mask = cfg->mask_list[content_index];
	gnpc *parent = GetParent();
	__PRINTF("gnpc_imp::SystemSpeak:speak_id=%d,content_index=%d,mask="   FMT_I64",npc_tid=%d,pos=%d(%f,%f,%f):target="   FMT_I64":cur_target="   FMT_I64"\n", speak_id, content_index, mask, parent->tid, GetRealSceneImp()->GetTag(), parent->pos.x, parent->pos.y, parent->pos.z, target.id, _cur_target.id);
	raw_wrapper h1(768);
	if (mask & SYS_SPEAK::MASK_PLAYER_NAME)
	{
		__PRINTF("gnpc_imp::SystemSpeak:speak_id=%d:mask:SYS_SPEAK::MASK_PLAYER_NAME\n", speak_id);
		if (GetSceneImp()->IsWedding())
		{
			ruid_t groom_id = GetSceneImp()->GetGroomID();
			abase::octets groom_name = GetSceneImp()->GetGroomName();
			SYS_SPEAK::MakeRoleName(h1, SYS_SPEAK::PLAYER_NAME, groom_id, groom_name.size(), groom_name.begin());
		}
		else
		{
			XID cur_target;
			if (target.IsPlayer())
			{
				cur_target = target;
			}
			else if (_cur_target.IsPlayer())
			{
				cur_target = _cur_target;
			}
			if (cur_target.IsValid())
			{
				object_info info;
				if (gmatrix::GetInstance().QueryObject(cur_target, info))
				{
					SYS_SPEAK::MakeRoleName(h1, SYS_SPEAK::PLAYER_NAME, cur_target.id, info.name.size(), info.name.begin());
				}
				else
				{
					return ;
				}
			}
		}
		mask &= ~SYS_SPEAK::MASK_PLAYER_NAME;
	}
	if (mask & SYS_SPEAK::MASK_PLAYER_NAME_2)
	{
		__PRINTF("gnpc_imp::SystemSpeak:speak_id=%d:mask:SYS_SPEAK::MASK_PLAYER_NAME_2\n", speak_id);
		if (GetSceneImp()->IsWedding())
		{
			ruid_t bride_id = GetSceneImp()->GetBrideID();
			abase::octets bride_name = GetSceneImp()->GetBrideName();
			SYS_SPEAK::MakeRoleName(h1, SYS_SPEAK::PLAYER_NAME_2, bride_id, bride_name.size(), bride_name.begin());
		}
		mask &= ~SYS_SPEAK::MASK_PLAYER_NAME_2;
	}
	if (mask & SYS_SPEAK::MASK_NPC_NAME)
	{
		gnpc *parent = GetParent();
		SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::NPC_NAME, parent->tid);
		mask &= ~SYS_SPEAK::MASK_NPC_NAME;
	}
	if (mask & SYS_SPEAK::MASK_NPC_NAME_WITH_TITLE)
	{
		gnpc *parent = GetParent();
		SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::NPC_NAME_WITH_TITLE, parent->tid);
		mask &= ~SYS_SPEAK::MASK_NPC_NAME_WITH_TITLE;
	}

	if (ParseAsyncSpeak(speak_id, content_index, mask, cfg->channel, h1))
	{
		return;
	}
	if (mask)
	{
		GLog::log(GLOG_ERR, "gnpc_imp::SystemSpeak:speak_id=%d,invalid mask="   FMT_I64",npc_tid=%d,pos=%d(%f,%f,%f)\n", speak_id, mask, parent->tid, GetRealSceneImp()->GetTag(), parent->pos.x, parent->pos.y, parent->pos.z);
	}
	SendSystemSpeak(speak_id, content_index, cfg->channel, target, h1);
}

void gnpc_imp::SystemSpeakWithAttacker(int speak_id, const XID& attacker, const XID& target)
{
	const system_speak *cfg = gmatrix::GetInstance().GetSpeakConfig(speak_id);
	if (!cfg || cfg->speak_id != speak_id)
	{
		return;
	}
	if (!GetSceneImp())
	{
		return;
	}
	//先随机选一个content_index
	int content_index = abase::Rand(0, cfg->content_num - 1);
	uint64_t mask = cfg->mask_list[content_index];
	gnpc *parent = GetParent();
	__PRINTF("gnpc_imp::SystemSpeakWithAttacker:speak_id=%d,content_index=%d,mask="   FMT_I64",npc_tid=%d,pos=%d(%f,%f,%f):target="   FMT_I64":cur_target="   FMT_I64", attacker=%ld\n", speak_id, content_index, mask, parent->tid, GetRealSceneImp()->GetTag(), parent->pos.x, parent->pos.y, parent->pos.z, target.id, _cur_target.id, attacker.id);
	raw_wrapper h1(768);
	if (mask & SYS_SPEAK::MASK_PLAYER_NAME)
	{
		__PRINTF("gnpc_imp::SystemSpeakWithAttacker:speak_id=%d:mask:SYS_SPEAK::MASK_PLAYER_NAME\n", speak_id);
		if (GetSceneImp()->IsWedding())
		{
			ruid_t groom_id = GetSceneImp()->GetGroomID();
			abase::octets groom_name = GetSceneImp()->GetGroomName();
			SYS_SPEAK::MakeRoleName(h1, SYS_SPEAK::PLAYER_NAME, groom_id, groom_name.size(), groom_name.begin());
		}
		else
		{
			XID cur_target;
			if (target.IsPlayer())
			{
				cur_target = target;
			}
			else if (_cur_target.IsPlayer())
			{
				cur_target = _cur_target;
			}
			if (cur_target.IsValid())
			{
				object_info info;
				if (gmatrix::GetInstance().QueryObject(cur_target, info))
				{
					SYS_SPEAK::MakeRoleName(h1, SYS_SPEAK::PLAYER_NAME, cur_target.id, info.name.size(), info.name.begin());
				}
				else
				{
					return ;
				}
			}
		}
		mask &= ~SYS_SPEAK::MASK_PLAYER_NAME;
	}
	if (mask & SYS_SPEAK::MASK_PLAYER_NAME_2)
	{
		__PRINTF("gnpc_imp::SystemSpeakWithAttacker:speak_id=%d:mask:SYS_SPEAK::MASK_PLAYER_NAME_2\n", speak_id);
		if (GetSceneImp()->IsWedding())
		{
			ruid_t bride_id = GetSceneImp()->GetBrideID();
			abase::octets bride_name = GetSceneImp()->GetBrideName();
			SYS_SPEAK::MakeRoleName(h1, SYS_SPEAK::PLAYER_NAME_2, bride_id, bride_name.size(), bride_name.begin());
		}
		mask &= ~SYS_SPEAK::MASK_PLAYER_NAME_2;
	}
	if (mask & SYS_SPEAK::MASK_NPC_NAME)
	{
		gnpc *parent = GetParent();
		SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::NPC_NAME, parent->tid);
		mask &= ~SYS_SPEAK::MASK_NPC_NAME;
	}
	if (mask & SYS_SPEAK::MASK_NPC_NAME_WITH_TITLE)
	{
		gnpc *parent = GetParent();
		SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::NPC_NAME_WITH_TITLE, parent->tid);
		mask &= ~SYS_SPEAK::MASK_NPC_NAME_WITH_TITLE;
	}
	if (IsPlayerNPCClass() && mask & SYS_SPEAK::MASK_CLONE_NAME)
	{
		SYS_SPEAK::MakeTypeIDName(h1, SYS_SPEAK::CLONE_NAME, _parent->ID.id, GetParent()->player_name_size, GetParent()->player_name_buf);
		mask &= ~SYS_SPEAK::MASK_CLONE_NAME;
	}
	if (attacker.IsValid() && mask & SYS_SPEAK::MASK_ATTACKER_NAME)
	{
		object_info attacker_info;
		if (gmatrix::GetInstance().QueryObject(attacker, attacker_info))
		{
			SYS_SPEAK::MakeTypeIDName(h1, SYS_SPEAK::ATTACKER_NAME, attacker.id, attacker_info.name.size(), attacker_info.name.begin());
		}
		mask &= ~SYS_SPEAK::MASK_PLAYER_NAME;
	}
	if (mask & SYS_SPEAK::MASK_FACTION_NAME)
	{
		if (IsPlayerNPCClass())
		{
			const abase::octets *mafia_name = GetMafiaName();
			if (mafia_name)
			{
				SYS_SPEAK::MakeTypeIDName(h1, SYS_SPEAK::FACTION_NAME, GetParent()->mafia_id, mafia_name->size(), mafia_name->begin());
			}
		}
		mask &= ~SYS_SPEAK::MASK_FACTION_NAME;
	}

	if (ParseAsyncSpeak(speak_id, content_index, mask, cfg->channel, h1))
	{
		return;
	}
	if (mask)
	{
		GLog::log(GLOG_ERR, "gnpc_imp::SystemSpeakWithAttacker:speak_id=%d,invalid mask="   FMT_I64",npc_tid=%d,pos=%d(%f,%f,%f)\n", speak_id, mask, parent->tid, GetRealSceneImp()->GetTag(), parent->pos.x, parent->pos.y, parent->pos.z);
	}
	SendSystemSpeak(speak_id, content_index, cfg->channel, target, h1);
}

//处理帮派信息等需要异步处理的系统喊话关键字，向玩家异步获取，等GM_MSG_SYS_SPEAK_GET_INFO返回后再处理
bool gnpc_imp::ParseAsyncSpeak(int speak_id, int content_index, uint64_t mask, unsigned char channel, raw_wrapper& h1)
{
	//存到副本信息里也不是不可以，主要是考虑再增加其他复杂关键字时的可扩展性
	/*
	if(mask & SYS_SPEAK::MASK_ECTYPE_CREATOR_FACTION)
	{
		XID xid;
		GetWorldImp()->GetCreator(xid);
		SystemSpeakGetInfo(speak_id,content_index,channel,SYS_SPEAK::ECTYPE_CREATOR_FACTION,
				mask & ~SYS_SPEAK::MASK_ECTYPE_CREATOR_FACTION,h1,xid);
		//流程中断，等msg从玩家返回后再继续
		return true;
	}
	if(mask & SYS_SPEAK::MASK_ECTYPE_OWNER_FACTION)
	{
		XID xid;
		GetWorldImp()->GetOwner(xid);
		SystemSpeakGetInfo(speak_id,content_index,channel,SYS_SPEAK::ECTYPE_OWNER_FACTION,
				mask & ~SYS_SPEAK::MASK_ECTYPE_OWNER_FACTION,h1,xid);
		//流程中断，等msg从玩家返回后再继续
		return true;
	}
	*/
	return false;
}

void gnpc_imp::SendSystemSpeak(int speak_id, int content_index, char channel, const XID& target, const raw_wrapper& h1)
{
	int nation = 0;
	gnpc *parent = GetParent();
	switch (channel)
	{
	case CHAT_CHANNEL_LOCAL:
	{
		slice *pSlice = parent->pSlice;
		gscene_imp *pSceneImp = GetRealSceneImp();
		if (!pSlice || !pSceneImp)
		{
			return;
		}
		GNET::Octets temp;
		GSP::GetInstance().SendSystemSpeak(parent->ID.id, speak_id, content_index, 0, h1.data(), h1.size(), &temp);
		SightRegionSendProtocol(pSceneImp, pSlice, temp);
	}
	break;
	case CHAT_CHANNEL_MAP:
	{
		GNET::Octets temp;
		GSP::GetInstance().SendSystemSpeak(parent->ID.id, speak_id, content_index, 0, h1.data(), h1.size(), &temp);
		GetRealSceneImp()->BroadcastProtocol(temp);
	}
	break;
	case CHAT_CHANNEL_TEAM:
	{
		abase::vector<link_id_t> vec;
		GetTeamLinkID(vec);
		if (vec.size())
		{
			GNET::Octets temp;
			GSP::GetInstance().SendSystemSpeak(parent->ID.id, speak_id, content_index, 0, h1.data(), h1.size(), &temp);
			multi_send_protocol(&vec[0], vec.size(), temp);
		}
	}
	break;
	case CHAT_CHANNEL_SYSTEM:
	{
		abase::vector<link_id_t> vec;
		GetOwnerLinkID(vec);
		if (target.IsPlayer())
		{
			//发起人能接受私聊频道
			link_id_t lid;
			if (gmatrix::GetInstance().GetPlayerLinkID(target, lid))
			{
				vec.push_back(lid);
			}
		}
		if (vec.size())
		{
			GNET::Octets temp;
			GSP::GetInstance().SendSystemSpeak(parent->ID.id, speak_id, content_index, 0, h1.data(), h1.size(), &temp);
			multi_send_protocol(&vec[0], vec.size(), temp);
		}
	}
	break;
	//以下两个频道完全由DS处理
	case CHAT_CHANNEL_WORLD:
	case CHAT_CHANNEL_BROADCAST:
	{
		GSP::GetInstance().SendSystemSpeak(parent->ID.id, speak_id, content_index, channel, h1.data(), h1.size(), NULL);
	}
	break;

	case CHAT_CHANNEL_COMMANDER:
	case CHAT_CHANNEL_NATION:
	case CHAT_CHANNEL_INTERACT_FISH:
		nation = FACTION_MASK_TO_NATION_ID(GetFaction());
		if (nation == 0)
		{
			int npc_faction = (_cnt.faction ? _cnt.faction : _template->faction);
			if (npc_faction == 0 || _template->has_service || _template->cannot_attack)
			{
				nation = GetSceneImp()->Nation();
			}
		}
		if (nation != 0)
		{
			GSP::GetInstance().SendSystemSpeak(nation, speak_id, content_index, channel, h1.data(), h1.size(), NULL, GNET::SIT_NATION);
			break;
		}
	//否则打印

	default:
	{
		GLog::log(GLOG_ERR, "NPC SystemSpeak:speak_id=%d,invalid channel=%d,nation=%d\n", speak_id, channel, nation);
	}
	}
	return;
}

void gnpc_imp::SystemSpeakGetInfo(int speak_id, int content_index, char channel, int type, uint64_t mask, const raw_wrapper& h1, XID& target)
{
	//__PRINTF("gnpc_imp::SystemSpeakGetInfo:speak_id=%d,type=%d,target="   FMT_I64"\n",speak_id,type,target.id);
	char buffer[sizeof(msg_sys_speak_get_info_t) + h1.size()];
	msg_sys_speak_get_info_t& get_info = *(msg_sys_speak_get_info_t *)buffer;
	get_info.speak_id = speak_id;
	get_info.content_index = content_index;
	get_info.channel = channel;
	get_info.type = type;
	get_info.mask = mask;
	get_info.data_size = h1.size();
	memcpy(get_info.data, h1.data(), h1.size());
	MSG msg;
	BuildMessage(msg, GM_MSG_SYS_SPEAK_GET_INFO, target, _parent->ID, A3DVECTOR3(), 0, &get_info, sizeof(buffer));
	gmatrix::GetInstance().SendMessage(msg);
}

void gnpc_imp::GetTeamLinkID(abase::vector<link_id_t>& vec)
{
	//npc的team频道发给护送玩家(召唤的没有group)
	abase::vector<XID> id_list;
	gnpc *parent = GetParent();
	if (parent->owner_team.IsValid())
	{
		player_team::GetTeamMember(parent->owner_team, id_list);
	}
	else if (parent->owner.IsValid())
	{
		id_list.push_back(parent->owner);
	}
	for (size_t i = 0; i < id_list.size(); ++i)
	{
		link_id_t lid;
		if (!gmatrix::GetInstance().GetPlayerLinkID(id_list[i], lid))
		{
			continue;
		}
		vec.push_back(lid);
	}
}

void gnpc_imp::GetOwnerLinkID(abase::vector<link_id_t>& vec)
{
	//专属npc的密语频道发给主人
	gnpc *parent = GetParent();
	if (parent->owner.IsValid())
	{
		link_id_t lid;
		if (gmatrix::GetInstance().GetPlayerLinkID(parent->owner, lid))
		{
			vec.push_back(lid);
		}
	}
}

void gnpc_imp::DebugSaySkillError(int skill_id, tid_t tid, ruid_t roleid)
{
	if (!SERVER_CONFIG.debug_command_mode)
	{
		return;
	}
	if (!gcreature_imp::TestCoolDown(CI_DEBUG_NPC_SKILL_ERR_SPEAK))
	{
		return;
	}
	char buf[1024];
	char buf2[2048];
	memset(buf, 0, sizeof(buf));
	memset(buf2, 0, sizeof(buf2));
	if (tid != 0)
	{
		sprintf(buf, "Cannot cast skill %d , target npc=%d", skill_id, tid);
	}
	else
	{
		sprintf(buf, "Cannot cast skill %d , target player="   FMT_I64"", skill_id, roleid);
	}
	for (size_t i = 0; i < strlen(buf); ++i)
	{
		buf2[i * 2] = buf[i];
	}
	Say(abase::octets(buf2, 2 * strlen(buf)));
	gcreature_imp::SetCoolDown(CI_DEBUG_NPC_SKILL_ERR_SPEAK, CT_DEBUG_NPC_SKILL_ERR_SPEAK);
}

struct object_collector
{
	abase::vector<link_id_t> *_lid_list;
	ObjList *_player_list;
	float _squared_radius;

	object_collector(abase::vector<link_id_t> *lid_list, ObjList *player_list, float radius)
		: _lid_list(lid_list), _player_list(player_list), _squared_radius(radius * radius) {}

	inline void operator()(slice *pSlice, const A3DVECTOR3& pos)
	{
		AUTO_LOCK(pSlice->spinlock, &debug::slice_lcs);
		gplayer *pObj = (gplayer *)pSlice->player_list;
		while (pObj)
		{
			if (squared_distance(pos, pObj->pos) <= _squared_radius)
			{
				if (NULL != _lid_list)
				{
					_lid_list->push_back(pObj->lid);
				}
				if (NULL != _player_list)
				{
					_player_list->push_back(pObj);
				}
			}
			pObj = pObj->get_next();
		}
	}
};

void gnpc_imp::RangeSay(float range, const abase::octets& msg, unsigned char channel, bool hide_speaker)
{
	if (!GetSceneImp())
	{
		return;
	}
	abase::vector<link_id_t> list;
	object_collector worker(&list, NULL, range);
	GetSceneImp()->GetGrid().ForEachSlice(GetParent()->pos, range, worker);
	if (list.size())
	{
		multi_send_chat_msg(hide_speaker ? NULL : GetParent(), &list[0], list.size(), channel, 0, 0, hide_speaker ? XID() : GetParent()->ID, (unsigned char)0, NULL, &msg);
	}
}

void gnpc_imp::SceneSay(const abase::octets& msg, unsigned char channel, bool hide_speaker)
{
	GetSceneImp()->BroadcastChatMessage(hide_speaker ? NULL : GetParent(), hide_speaker ? XID() : GetParent()->ID, channel, 0, 0, NULL, &msg);
}

void gnpc_imp::GenerateProperty(int level, int prof, int prof_level, int exp)
{
	_prop.GetBasic().level = level;
	_prop.GetBasic().exp = exp;
	_prop.GetBasic().prof = prof;
	_prop.GetBasic().prof_level = prof_level;

	//计算新的对象参数
	RebuildProp();
	_prop.RenewAll();
}

void gnpc_imp::GetRollbackPosDir(A3DVECTOR3& pos, dir_t& dir) const
{
	/*$$$$$$AI  Rollback逻辑完全不需要在imp实现吧， 找时间统一删除
	ASSERT(_template);
	if(_ai_man->GetPatrolPath())
	{
		if(!_ai_man->IsInPatrolPath() && _issetorig)
		{
			//已经走完路径，且设了原点的，走到原点
			pos = _orig_pos;
			dir = _orig_dir;
		}
		else
		{
			//正在巡逻的，以及没设过原点的，回到进入战斗的位置
			if(!GetCombatStartPosDir(pos,dir))
			{
				//取不到进入战斗位置的，留在当地吧
				pos = Parent()->pos;
				dir = Parent()->dir;
			}
		}
	}
	else if(GetMaster().IsValid() || _ctrl_unit_xid.IsValid())	//跟随主人的，或者团组怪，回到进入战斗的位置
	{
		if(!GetCombatStartPosDir(pos,dir))
		{
			pos = Parent()->pos;
			dir = Parent()->dir;

		}
	}
	else if(_issetorig)
	{
		pos = _orig_pos;
		dir = _orig_dir;
	}
	else
	{
		GetBirthPosDir(pos,dir);
	}
	*/
}

void gnpc_imp::SetAggressive(bool isactive)
{
	//这里是否要设置aggro_mode,这个值影响卫兵策略
	if (isactive)
	{
		Parent()->msg_mask |= gobject::MSG_MASK_PLAYER_PEEP;
	}
	else
	{
		Parent()->msg_mask &= ~gobject::MSG_MASK_PLAYER_PEEP;
	}
}

void gnpc_imp::SetCanBeAttack(bool active)
{
	GetParent()->can_not_be_attack = !active;
	_runner->object_beattack_changed();
}

void gnpc_imp::SetAffixData(unsigned int affix_data)
{
	gnpc *pNPC = GetParent();
	pNPC->affix_data = affix_data;
	if (affix_data)
	{
		pNPC->SetObjectState(gobject::STATE_NPC_AFFIX);
	}
	else
	{
		pNPC->ClrObjectState(gobject::STATE_NPC_AFFIX);
	}
	Runner()->npc_affix_data_chg(affix_data);
}

//怪物出生保护时间内，怪物无法被选择中，无法被攻击，不会主动攻击，不接收呼救，不作为技能群攻对象计算
bool gnpc_imp::IsInBornProtectTime() const
{
	if (!IsMonster() )
	{
		return false;
	}
	const gnpc *pNPC = GetParent();
	return pNPC->IsInBornProtectTime();
}

void gnpc_imp::Reborn(gscene_imp *pSceneImp)
{
	ASSERT(_npc_state == NPC_STATE_SPAWNING);
	_wait_release = false;
	_npc_state = NPC_STATE_NORMAL;
	_hp_min_locked = 0;
	GetParent()->can_not_be_attack = _template->cannot_attack;
	_dm_map.Clear();
	_dm_map.ClearPlayerDamage();
	_dm_map.ClearCorpsDamage();
	_attack.Clear();
	SetOverwhelmingMode(false);
	ClearAction();
	CancelTransform();
	_filter_man.ClearAllFilterExcept(0); //npc的Reborn并非npc复活, 而是软件设计上为了复用空间, 对玩家来说完全是新的npc, 所以filter完全不该有继承
	RebuildProp();
	_prop.RenewAll();
	_parent->pos = _cnt.pos;		//TODO weylan:不使用spaw的随机pos,在原出生点复活
	_parent->pos += _cnt.pos_offset;
	_birth_pos = _parent->pos;
	_birth_dir = _parent->dir;
	gnpc *pNPC = GetParent();
	if (!_cnt.spec_latancy)
	{
		pNPC->heartbeat_latancy = abase::Rand(0, TICK_PER_SECOND);
	}
	gettimeofday(&pNPC->birth_time, NULL);
	pNPC->monster_born_protect_time = _template->monster_born_protect_time;
	_issetorig = false;
	/*
	if(horizontal_distance(Parent()->pos, _cnt.pos) > 5 * 5)
		__PRINTF("npc:%ld reborn pos(%f,%f,%f),dir(%d),cnt pos(%.f,%f), offset(%.2f,%.2f)\n",
			Parent()->ID.id, _birth_pos.x,_birth_pos.y,_birth_pos.z,_birth_dir, _cnt.pos.x, _cnt.pos.z,
			_cnt.pos_offset.x, _cnt.pos_offset.z);
	*/
	_idle_time_counter = NPC_DEFAULT_IDLE_TIME;
	_parent->ClrObjectState(gobject::STATE_ZOMBIE);
	//_parent->ClrObjectState(gobject::STATE_IN_CHUPOZHAN);
	_max_attacker_level = 0;
	//_dying = false;
	GetParent()->dying = false;
	_can_enter_dying_state = false;
	_commander->Reborn();
	_exist_duration_after_combat = 0;
	_exist_counter_after_combat = 0;
	_leave_combat_time = 0;
	_last_peep_time = gmatrix::GetInstance().GetSysTime();
	_attach_sleep_buff_time = 0;
	if (_ai_ctrl)
	{
		delete _ai_ctrl;
		_ai_ctrl = NULL;
	}

	//重新初始化AI
	_ai_ctrl = ai_control::CreateAI(this, _cnt.create_type);
	_ai_ctrl->Init((bool)_template->patrol_mode, _template, &_cnt);
	_ai_ctrl->InitSkill();
	LazySendTo(GM_MSG_INIT_AI, _parent->ID, 0, 1);		//这里的InitAI主要用于初始行为

	if (_service)
	{
		_service->Reborn();
	}
	_self_param.Clear();

	if (_cnt.init_overwhelming)
	{
		_parent->SetObjectState(gobject::STATE_NPC_INVINC);
		SetOverwhelmingMode(true);              //初始无敌状态
	}

	EnterScene(pSceneImp);
	//__PRINTF( "NPC "   FMT_I64" 重新站起来啦!! _aggro_mode=%d\n", _parent->ID.id, _aggro_mode);

	//试着关联场景的group，创建NPC时，即 gscene_imp::CreateNPC里也会关联，NPC死亡时解除了关联
	if (_cnt.stage_id == 0 && _cnt.wave_id > 0 )
	{
		//大地图群组怪
		//使用wave_id作为group标记吧
		gobject_grouping *pGroup = pSceneImp->GetNormalGroup(_cnt.wave_id);
		if (pGroup)
		{
			pGroup->imp->_list.push_back(pNPC);
			pNPC->pGrouping = pGroup;
			pGroup->Unlock();
			//这之后啥也不要做了
		}
	}
}

void gnpc_imp::EnterScene(gscene_imp *pSceneImp)
{
	gnpc *pNPC = GetParent();
	if (_template)
	{
		if (_template->file_minimap_icon)
		{
			pNPC->SetObjectState(gobject::STATE_SCENE_SPECIAL_OBJECT);
			FormSpecialContent(_special_content);
		}
		if (IsBroadcastBoss())
		{
			pNPC->SetObjectServerState(gobject::SERVER_STATE_BOSS_BROADCAST);
		}
	}
	if (!pSceneImp)
	{
		pSceneImp = pNPC->pSceneImp;
		ASSERT(pSceneImp);
	}
	pNPC->pSceneImp = pSceneImp;
	pNPC->world_id = pSceneImp->GetWorldImp()->Parent()->xid;
	pNPC->world_tid = pSceneImp->GetWorldImp()->GetTID();
	pNPC->scene_tag = pSceneImp->GetTag();
	pNPC->mid = pSceneImp->GetMirrorID();
	if (!pNPC->master.IsPlayer())
	{
		pNPC->mafia_id = GetWorldImp()->GetMafiaID();
		if (pNPC->mafia_id)
		{
			pNPC->object_state |= gobject::STATE_MAFIA;
		}
	}
	if (_template && _template->npc_data.is_for_faction_only &&
	        !GNET::IsMafiaBase(pSceneImp->GetWorldImp()->GetInstanceTID()))
	{
		__PRINTF("ERROR: npc_tid=%d faction only in scene_tag %d world_tid %d with no faction\n",
		         _template->tid, pSceneImp->GetTag(), pNPC->world_tid);
		_commander->Release();
		return;
	}
	/*if (NULL != _template && _template->id_graffiti_box > 0)
	{
		int64_t id = (pNPC->mafia_id == 0) ? GS_SCENEID_TO_TIZIID(pSceneImp->GetTag()) : GS_FACTIONID_TO_TIZIID(pNPC->mafia_id);
		tizi_manager::GetInstance().OnNpcEnterScene(id, _template->id_graffiti_box, pNPC);
	}*/
	UpdateDataToParent();
	pSceneImp->InsertNPC(GetParent());
	GetWorldImp()->OnNPCBorn(_template, this);

	gcreature_imp::EnterScene(pSceneImp);

	if (_ai_ctrl)
	{
		_ai_ctrl->EventOnBorn();
	}

	_runner->object_enter_scene();
}

void gnpc_imp::LeaveScene()
{
	if (!Parent()->IsActive())
	{
		return;
	}

	/*if (NULL != _template && _template->id_graffiti_box > 0)
	{
		int64_t id = (Parent()->mafia_id == 0) ? GS_SCENEID_TO_TIZIID(GetSceneImp()->GetTag()) : GS_FACTIONID_TO_TIZIID(Parent()->mafia_id);
		tizi_manager::GetInstance().OnNpcLeaveScene(id, _template->id_graffiti_box, Parent()->ID);
	}*/
//	_ai_man->ClearTask(); $$$$$$$AI
	ClearAction();
	_runner->object_leave_scene(_leave_scene_type);
	GetSceneImp()->RemoveNPC(GetParent());

	gcreature_imp::LeaveScene();
}

bool gnpc_imp::CanService(const XID& target, tid_t id)
{
	if (!_template)
	{
		return true;
	}
	if (_template->npc_data.service_nation_limit == 0)
	{
		return false;
	}
	if (_template->npc_data.service_nation_limit & NPC_SERVICE_LIMIT_ALL)
	{
		return true;
	}

	object_info info;
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;
	}
	if (_template->npc_data.service_nation_limit & NPC_SERVICE_LIMIT_SAME_NATION)
	{
		if ((NATION_TO_FACTION_MASK_NPC(info.nation_id) & GetFaction()) != 0)
		{
			return true;
		}
	}
	/*
	if (_template->npc_data.service_nation_limit & NPC_SERVICE_LIMIT_ALLIANCE)
	{
		if ((NATION_TO_FACTION_MASK_NPC(info.nation_alliance) & GetFaction()) != 0)
		{
			return true;
		}
	}
	if (_template->npc_data.service_nation_limit & NPC_SERVICE_LIMIT_ENEMY_NATION)
	{
		if ((NATION_TO_FACTION_MASK_NPC(info.nation_id) & GetFaction()) == 0 &&
		        (NATION_TO_FACTION_MASK_NPC(info.nation_alliance) & GetFaction()) == 0)
		{
			return true;
		}
	}
	*/
	return false;
	//是否帮派专属
	/*
	if(!_template->npc_data.is_for_faction_only)  return true;
	if(Parent()->mafia_id == 0) return false;
	object_info info;
	if(!gmatrix::GetInstance().QueryObject(target, info)) return false;
	if(info.mafia_id == 0) return false;
	return mafia_manager::GetInstance().CanService(Parent()->mafia_id, info.mafia_id,
		_template->npc_data.is_for_faction_only, _template->npc_data.type_faction_dyn_service, id);
	*/
}

void gnpc_imp::ChangeFaction(faction_t faction, unsigned int relation_id)
{
	GetProperty().SetIncludeFaction(faction);
	CalcFaction(relation_id);
	faction_t new_faction = GetFaction();
	UpdateExtentInfo();
	UpdateDataToParent();
	_runner->object_change_faction(new_faction);
}

void gnpc_imp::ChangeFaction(faction_t add_mask, faction_t remove_mask, unsigned int relation_id)
{
	faction_t faction = GetFaction();
	if (add_mask > 0)
	{
		faction |= add_mask;
	}
	if (remove_mask > 0)
	{
		faction &= ~remove_mask;
	}
	InitFaction(faction);
	UpdateExtentInfo();
	UpdateDataToParent();
	_runner->object_change_faction(GetFaction());
}

void gnpc_imp::SetOrigPosition(A3DVECTOR3 pos, dir_t dir)
{
	if (!GetSceneImp()->PosInScene(pos) || !GetSceneImp()->IsPosReachable(pos))
	{
		return;
	}
	_issetorig = true;
	_orig_pos = pos;
	_orig_dir = dir;
}

void gnpc_imp::MoveBetweenSlice(slice *src, slice *dst, slice_info_collector&)
{
	src->MoveNPC(GetParent(), dst);
}

bool gnpc_imp::ActiveCombatState(bool combat_state, unsigned char reason, bool notify)
{
	if (!_fight_back && combat_state)
	{
		return false;
	}
	bool rst = gcreature_imp::ActiveCombatState(combat_state, reason, notify);
	gnpc *pNPC = (gnpc *)_parent;
	if (!rst)
	{
		return false;
	}

	if (combat_state)
	{
		_leave_combat_time = -1;

		//进入战斗状态
		pNPC->need_heartbeat |= NHM_COMBAT;
		AskForHelp(GetSightRange());
		_combat_start_pos = _parent->pos;
		_combat_start_dir = _parent->dir;
		_combat_start_flag = true;
		//__PRINTF("npc enter combat state pos(%f,%f,%f),dir(%d)\n",_combat_start_pos.x,_combat_start_pos.y,_combat_start_pos.z,_combat_start_dir);
		if (_pet.IsValid())
		{
			XID target;
			if (_ai_ctrl->GetAggroMan().GetFirst(target))
			{
				SendTo2(GM_MSG_PET_AUTO_ATTACK, _pet, 0, true, &target, sizeof(target));
			}
		}
	}
	else
	{
		_leave_combat_time = gmatrix::GetInstance().GetSysTime();

		_combat_start_flag = false;
		//脱离战斗状态
		pNPC->need_heartbeat &= ~NHM_COMBAT;
		//__PRINTF("npc leave combat state\n");
		if (_pet.IsValid())
		{
			SendTo2(GM_MSG_MASTER_COMBAT_STATE, _pet, 0, 0);
		}
		UnselectTarget();
	}
	return true;
}

bool gnpc_imp::GetTargetDistance(float& distance)
{
	//获取与当前目标的距离
	XID target;
	if (!_ai_ctrl->GetAggroMan().GetFirst(target))
	{
		return false;
	}
	object_info obj_info;
	if (!gmatrix::GetInstance().QueryObject(target, obj_info))
	{
		return false;
	}
	distance = sqrt(squared_distance(obj_info.pos, _parent->pos));
	return true;
}
/*
bool gnpc_imp::TestEnchantRule(const MSG& msg,attack_msg& ack_msg)
{
	if(!ack_msg.helpful && !CanBeAttack()) return false;
	gnpc* pNPC = GetParent();
	bool allow_attack = false;
	//专属npc只有主人能攻击
	if(!pNPC->CheckObjectState(gobject::STATE_NPC_HAVE_OWNER))
		allow_attack = true;
	else if(pNPC->owner.IsValid() && pNPC->owner == ack_msg.attacker_info.attacker)
		allow_attack = true;
	else if(pNPC->owner_team.IsValid() && pNPC->owner_team == ack_msg.attacker_info.team)
		allow_attack = true;

	if(!allow_attack) return false;

	//非本国的的PVP保护状态的玩家无法攻击本国NPC
	if(ack_msg.attacker_info.attacker.IsPlayer())
	{
		int npc_nation = FACTION_MASK_TO_NATION_ID(GetFaction());
		if(npc_nation && npc_nation != FACTION_MASK_TO_NATION_ID(ack_msg.attacker_info.faction) &&
			!(ack_msg.attacker_info.attacker_mode & attacker_info_t::AM_PVP_ENABLE))
			return false;
	}

	if(!ack_msg.helpful)
	{
		// 阵营敌对可以打
		if(GetFaction() & ack_msg.attacker_info.enemy_faction) return true;
		if(GetWorldImp()->GetMafiaID() && MafiaAttackCheck(Parent()->ID,Parent()->mafia_id,ack_msg.attacker_info.attacker,ack_msg.attacker_info.mafia_id)) return true;
		return false;
	}
	return true;
}
*/
void gnpc_imp::TriggerAggro(const XID& who)
{
	if (IsStub())
	{
		return;
	}
	if (!IsAggressive() && !_fight_back)
	{
		return;
	}
	if (GetHP() <= 0)
	{
		return;
	}
	_ai_ctrl->TriggerAggro();
}

bool gnpc_imp::CheckCanAddAggro(const XID&, const attacker_info_t& info)
{
	if (info.attacker.id == _parent->ID.id)
	{
		return false;
	}

	// 是否产生仇恨,无仇恨则无掉落
	// 策划：韩之昱  12.12.17
	// 1.NPC模板“不攻击玩家”选项符合，则不产生仇恨，不掉落
	switch (_template->attack_player_strategy)
	{
	default:
	case NO_EFFECT:
	{
	}
	break;

	case NO_ATTACK_PLAYER:
	{
		if (info.attacker.IsPlayer())
		{
			return false;
		}
	}
	break;

	case ONLY_ATTACK_PLAYER:
	{
		if (!info.attacker.IsPlayer())
		{
			return false;
		}
	}
	break;

	case ONLY_ATTACK_DIFF_FACTION_PLAYER:
	{
		if (info.attacker.IsPlayer() && !(GetWorldImp()->GetMafiaID() && MafiaAttackCheck(Parent()->ID, GetParent()->mafia_id, info.attacker, info.mafia_id)))
		{
			return false;
		}
	}
	break;
	}
	/*
	// 2.阵营不匹配，则不产生仇恨，不掉落
	if(!(info.faction & GetEnemyFaction()))
	{
		if(!(GetWorldImp()->GetMafiaID() && MafiaAttackCheck(Parent()->ID,Parent()->mafia_id,who,info.mafia_id)))
			return false;
	}
	*/
	// 3.不反击也不产生仇恨
	if (!_fight_back)
	{
		return false;
	}
	return true;
}
void gnpc_imp::RefreshAscription()
{
	XID who;
	_dm_map.GetFirst(who);
	if (who.IsValid())
	{
		ruid_t 	ascription_id = who.id;
		if (who.IsTeam())
		{
			ascription_id = -ascription_id;
		}
		GetParent()->SetObjectState(gobject::STATE_NPC_HAVE_ASCR);
		if (ascription_id == GetParent()->ascription_id)
		{
			return;
		}
		GetParent()->ascription_id = ascription_id;
		Runner()->npc_ascription_change();
	}
	else
	{

		GetParent()->ClrObjectState(gobject::STATE_NPC_HAVE_ASCR);
		if (0 == GetParent()->ascription_id)
		{
			return;
		}
		GetParent()->ascription_id = 0;
		Runner()->npc_ascription_change();

	}

}

void gnpc_imp::OnHarmfulExec(const XID&, const attacker_info_t& info, float damage, int aggro_rate/*=0*/)
{
	if (GetOverwhelmingMode())
	{
		return;
	}
	if (_parent->IsZombie())
	{
		return;
	}
	if (_parent->object_state & gobject::STATE_PLAYER_MECH)
	{
		return;
	}
	if (_parent->object_state2 & (gobject::STATE2_PLAYER_FAKER | gobject::STATE2_PLAYER_HEIR | gobject::STATE2_PLAYER_CHESS))
	{
		return;
	}
	bool is_stub = IsStub();	//树桩也可能需要掉落，特殊处理一下
	const XID& attacker = info.attacker;
	if (info.attacker_mode & attacker_info_t::AM_PVP_INVADER && attacker.IsPlayer())
	{
		SendTo2(GM_MSG_PLAYER_BECOME_INVADER, attacker, false, info.attacker_mode & attacker_info_t::AM_PVP_INVADER);
	}
	bool not_npc_loot = false;
	if (_template)
	{
		not_npc_loot = _template->not_npc_loot;
		if (info.attacker.IsPlayer())
		{
			_dm_map.RecordPlayerDamage(info, damage, _template->player_damage_list_size);

			if (_template->corps_damage_list_size > 0 && info.mafia_id > 0)
			{
				_dm_map.RecordCorpsDamage(info.mafia_id, damage, _template->corps_damage_list_size);
			}
		}
	}

	if (!_cnt.is_stage_npc)
	{
		_dm_map.DoDamage(damage, info.attacker, info.team);
		RefreshAscription();
	}

	bool first_damage = false;
	if (!CheckCanAddAggro(XID(), info))
	{
		return;
	}

	if (aggro_rate == 0)//策划未填写该字段，默认按照1倍处理
	{
		aggro_rate = 1;
	}
	else if (aggro_rate < 0)
	{
		aggro_rate = 0;
	}
	int64_t aggro_eq = damage * aggro_rate * ( 10000.0 / GetHPMax());//NPC的全部血量被换算为10000点仇恨值
	if (aggro_eq <= 0)
	{
		aggro_eq = 1;
	}
	if (!info.is_skill_npc)
	{
		_ai_ctrl->GetAggroMan().AddAggroForAttack(attacker, info.pet_master, aggro_eq, info.team, not_npc_loot, first_damage);
	}
	else if (info.pet_master.IsValid()) //技能召唤npc增加主人仇恨
	{
		_ai_ctrl->GetAggroMan().AddAggroForAttack(info.pet_master, XID(), aggro_eq, info.team, not_npc_loot, first_damage);
	}
	if (!CheckCanAttack(info.attacker))
	{
		return;
	}
	if (is_stub)
	{
		return;
	}
	TriggerAggro(info.attacker);
	if (GetHP() > 0)
	{
		//被没死掉, 触发被攻击逻辑
		_ai_ctrl->EventBeAttacked(info.attacker);
	}
	if (attacker.IsPlayer())
	{
		if (info.level > _max_attacker_level)
		{
			_max_attacker_level = info.level;
		}
		__PRINTF("player"   FMT_I64" new:%d, attack npc"   FMT_I64" newtype:%d, cur_hp:%ld\n",
		         attacker.id, attacker.GetNewObjID(), _parent->ID.id, _parent->ID.GetNewObjID(), GetHP());
	}
}

//自走棋的棋子npc处于同一个group中，它们的逻辑是串行的，可以互相修改数据
void gnpc_imp::ChessSyncDamage2Attacker(const XID& who, int damage)
{
	gnpc *p_npc = gmatrix::GetInstance().GetNPCByXID(who.id);
	if (p_npc && p_npc->pGrouping && p_npc->pGrouping == GetParent()->pGrouping && p_npc->CheckObjectState2(gcreature::STATE2_PLAYER_CHESS))
	{
		gplayer_chess *p_chess = (gplayer_chess *)p_npc;
		p_chess->damage_make += damage;
	}
}

void gnpc_imp::OnHurt(const XID& who, const attacker_info_t& info, float damage, int skill_id)
{
	const XID& real_who = info.pet_master.IsPlayer() ? info.pet_master : info.attacker;
	if (_parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP))
	{
		EndNightSleep(ENSR_BE_ATTACKED, real_who.id);
	}
	if (damage > 0)
	{
		time_t now_time = gmatrix::GetInstance().GetSysTime();
		if (_attach_sleep_buff_time > 0 && now_time - _attach_sleep_buff_time >= SLEEP_BUFF_LOCK_TIME)
		{
			GetFilterMan().ClearSpecFilter2(WMSKILL::TYPEMASK_SLEEP, 0);
		}
	}

	OnHarmfulExec(who, info, damage);

	if (GetWorldImp()->NeedStatPanel())
	{
		action_content content_info;
		content_info.real_attacker = real_who.id;
		content_info.skill_id = skill_id;
		content_info.npc_tid = real_who.IsNPC() ? info.npc_tid : 0;
		content_info.npc_faction = real_who.IsNPC() ? info.faction : 0;
		content_info.be_attack_npc_tid = GetParent()->tid;
		content_info.value = (int64_t)damage;
		MSG be_msg;
		BuildMessage2(be_msg, GM_MSG_CREATURE_BE_ACTION, GetWorldImp()->Parent()->xid, _parent->ID, A3DVECTOR3(0), BAT_HURT, 0, &content_info, sizeof(content_info));
		gmatrix::GetInstance().SendMessage(be_msg);
	}
	else if (GetSceneImp()->GetPKType() == PT_CHESS)
	{
		ChessSyncDamage2Attacker(real_who, damage);
	}

	__PRINTF("SKILL_STAT: hurt src=%ld, tar=%ld, skill=%d, value=%d\n", real_who.id, _parent->ID.id, skill_id, (int)damage);
}

void gnpc_imp::OnAttacked(const XID& who, const attack_msg& atk_msg, float damage)
{
	const XID& real_who = atk_msg.attacker_info.pet_master.IsPlayer() ? atk_msg.attacker_info.pet_master : atk_msg.attacker_info.attacker;
	if (_parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP))
	{
		EndNightSleep(ENSR_BE_ATTACKED, real_who.id);
	}
	if (damage > 0)
	{
		time_t now_time = gmatrix::GetInstance().GetSysTime();
		if (_attach_sleep_buff_time > 0 && now_time - _attach_sleep_buff_time >= SLEEP_BUFF_LOCK_TIME)
		{
			GetFilterMan().ClearSpecFilter2(WMSKILL::TYPEMASK_SLEEP, 0);
		}
	}

	OnHarmfulExec(who, atk_msg.attacker_info, damage, atk_msg.skill_aggro);

	if (GetWorldImp()->NeedStatPanel())
	{
		action_content content_info;
		content_info.real_attacker = real_who.id;
		content_info.skill_id = atk_msg.skill_id;
		content_info.npc_tid = real_who.IsNPC() ? atk_msg.attacker_info.npc_tid : 0;
		content_info.npc_faction = real_who.IsNPC() ? atk_msg.attacker_info.faction : 0;
		content_info.be_attack_npc_tid = GetParent()->tid;
		content_info.value = (int64_t)damage;
		MSG be_msg;
		BuildMessage2(be_msg, GM_MSG_CREATURE_BE_ACTION, GetWorldImp()->Parent()->xid, _parent->ID, A3DVECTOR3(0), BAT_ATTACK, 0, &content_info, sizeof(content_info));
		gmatrix::GetInstance().SendMessage(be_msg);
	}
	else if (GetSceneImp()->GetPKType() == PT_CHESS)
	{
		ChessSyncDamage2Attacker(real_who, damage);
	}

	__PRINTF("SKILL_STAT: attacked src=%ld, tar=%ld, skill=%d, value=%d\n", real_who.id, _parent->ID.id, atk_msg.skill_id, (int)damage);
}

void gnpc_imp::OnEnchanted(const XID& who, const attack_msg& msg, int arg)
{
	const XID& real_who = msg.attacker_info.pet_master.IsPlayer() ? msg.attacker_info.pet_master : msg.attacker_info.attacker;
	if (_parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP))
	{
		EndNightSleep(ENSR_BE_ATTACKED, real_who.id);
	}

	if (GetWorldImp()->NeedStatPanel())
	{
		action_content content_info;
		content_info.real_attacker = real_who.id;
		content_info.skill_id = msg.skill_id;
		content_info.npc_tid = real_who.IsNPC() ? msg.attacker_info.npc_tid : 0;
		content_info.npc_faction = real_who.IsNPC() ? msg.attacker_info.faction : 0;
		content_info.value = (int64_t)arg;
		MSG be_msg;
		BuildMessage2(be_msg, GM_MSG_CREATURE_BE_ACTION, GetWorldImp()->Parent()->xid, _parent->ID, A3DVECTOR3(0), BAT_ENCHANT, (int)msg.helpful, &content_info, sizeof(content_info));
		gmatrix::GetInstance().SendMessage(be_msg);
	}

	__PRINTF("SKILL_STAT: enchanted src=%ld, tar=%ld, skill=%d\n", real_who.id, _parent->ID.id, msg.skill_id);
}

void gnpc_imp::OnCure(const XID& who, const attacker_info_t& msg, float cure, int skill_id)
{
	const XID& real_who = msg.pet_master.IsPlayer() ? msg.pet_master : msg.attacker;
	if (_parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP))
	{
		EndNightSleep(ENSR_BE_ATTACKED, real_who.id);
	}

	if (GetWorldImp()->NeedStatPanel())
	{
		action_content content_info;
		content_info.real_attacker = real_who.id;
		content_info.skill_id = skill_id;
		content_info.npc_tid = real_who.IsNPC() ? msg.npc_tid : 0;
		content_info.npc_faction = real_who.IsNPC() ? msg.faction : 0;
		content_info.value = (int64_t)cure;
		MSG be_msg;
		BuildMessage2(be_msg, GM_MSG_CREATURE_BE_ACTION, GetWorldImp()->Parent()->xid, _parent->ID, A3DVECTOR3(0), BAT_CURE, 1, &content_info, sizeof(content_info));
		gmatrix::GetInstance().SendMessage(be_msg);
	}

	__PRINTF("SKILL_STAT: cure src=%ld, tar=%ld, skill=%d, value=%d\n", real_who.id, _parent->ID.id, skill_id, (int)cure);
}

void gnpc_imp::OnDying()
{
	if (_ai_ctrl)
	{
		_ai_ctrl->EventOnDying();
	}
}

void gnpc_imp::OnSPOut()
{
	if (_ai_ctrl)
	{
		_ai_ctrl->EventOnSPOut();
	}
}


void gnpc_imp::OnDeath(const XID& last_attacker, const attacker_info_t *pInfo, skill_id_t kill_skill_id, unsigned char kill_attack_stage, bool ignore_statistics)
{
	gnpc *pNPC = (gnpc *)_parent;
#ifdef GS_DEBUG
	if (debug::debug_ruid == pNPC->ID.id)
	{
		__PRINTF("void gnpc_imp::OnDeath(const XID& last_attacker,const attacker_info_t* pInfo,skill_id_t kill_skill_id,unsigned char kill_attack_stage)\n");
	}
#endif
	ASSERT(_template);
	if (_parent->IsZombie())
	{
		return;
	}
	SyncDamagePlayerMapByNpcTid();
	SyncPlayerDamageToSceneByNpcTid();
	SyncCorpsDamageToSceneByNpcTid();
	DamageCorpsRegionSend();
	DamagePlayerRegionSend();

	if (pNPC->pGrouping)
	{
		pNPC->pGrouping->imp->OnNPCBeforeDeath(pNPC);
	}

	XID attacker = last_attacker;
	if (pInfo)
	{
		attacker = pInfo->pet_master.IsPlayer() ? pInfo->pet_master : pInfo->attacker;
	}

	EndNightSleep(ENSR_DEATH, attacker.id);
	_parent->SetObjectState(gobject::STATE_ZOMBIE);
	//目前不需要保留filter，都清了吧
	_filter_man.ClearAllFilterExcept(0);
	/*if (_corpse_delay)
	{
		((gnpc *)_parent)->object_state |= gobject::STATE_NPC_CORPSE;
	}
	else
	{
		((gnpc *)_parent)->object_state &= ~gobject::STATE_NPC_CORPSE;
	}*/

	tid_t npc_tid = 0;
	unsigned int npc_faction = 0;
	if (pInfo && attacker.IsNPC())
	{
		npc_tid = pInfo->npc_tid;
		npc_faction = pInfo->faction;
	}

	action_content content_info(attacker.id, kill_skill_id, npc_tid, npc_faction);
	MSG battle_on_death_msg;
	BuildMessage(battle_on_death_msg, GM_MSG_BATTLE_ON_DEATH, _parent->ID, XID(), A3DVECTOR3(0), (int)ignore_statistics, &content_info, sizeof(content_info));
	gmatrix::GetInstance().SendMessage(battle_on_death_msg, 1);
	GetSceneImp()->OnNPCDeath(_template, pInfo);

	if (_template->is_center_battle_monster)
	{
		GetWorldImp()->CheckNpcDeath(attacker.id);
	}

	_notify_action = 0;
	ActiveCombatState(false, 0);	//清空战斗状态

	//只有非自杀的npc才有奖励 ，名人死亡无奖励
	XID who;
	bool has_award = (attacker != pNPC->ID) && _cnt.create_type == create_npc_t::CREATE_NORMAL && _template;
	//npc_death_generate_reward_t drop_data;
	//只有非自杀的npc才有奖励 ，名人死亡无奖励
	//非关卡NPC，非名人，对手，玩家走大世界掉落逻辑
	if (has_award)
	{
		WorldReward(attacker, pInfo, who);
		if (!who.IsValid() && attacker.IsPlayer())
		{
			who = attacker;
		}
	}
	if ( _template->speak_id_on_death )
	{
		BroadcastAfterDeath(who);
	}

	ClearAction();
	XID maxDamager;
	_dm_map.GetFirst(maxDamager);
	_dm_map.Clear();

	//策略里可能获取仇恨名单，把清仇恨放在后面
	if (_ai_ctrl)
	{
		_ai_ctrl->EventOnDeath(attacker);
	}
	_runner->on_death(attacker, _corpse_delay, kill_skill_id, kill_attack_stage);
	if (!_corpse_delay)
	{
		//如果没有死亡延迟就立刻发送消失代码否则等待心跳
		SendTo(GM_MSG_OBJ_ZOMBIE_END, _parent->ID, 0);
	}
	_dead_time_count = 0;
	if (Parent()->CheckObjectState(gobject::STATE_NPC_PLAYER))
	{
		auto *pPlayerNPC = (gplayer_npc *)Parent();
		if ((GetWorldImp()->GetType() == WT_CENTER_BATTLE && (GetWorldImp()->GetCategory() == GNET::INSTANCE_CENTER_TEAM_ARENA_BATTLE || GetWorldImp()->GetCategory() == GNET::INSTANCE_CENTER_TEAM_ARENA_BATTLE_NEW)) ||
		        (pPlayerNPC->fake_roleid != 0 && pPlayerNPC->fake_roleid == GetWorldImp()->GetTeamMemberNpcRoleid()))
		{
			_player_npc_death_times += 1;
			time_t now_time = gmatrix::GetInstance().GetSysTime();
			if (now_time - _player_npc_last_die_timestamp > GetWorldImp()->GetLocalReviveResetTime(this))
			{
				_player_npc_death_times = 1;
			}
			int local_revive_count_max = GetWorldImp()->GetLocalReviveCountMax(this);
			if (_player_npc_death_times > local_revive_count_max)
			{
				_player_npc_death_times = local_revive_count_max;
			}
			_player_npc_revive_delay_time = GetWorldImp()->GetResurrectDealyTime(this, _player_npc_death_times);
		}
	}
	if (_guide.IsValid())
	{
		SendTo(GM_MSG_DEATH, _guide, 0);
	}
	/*
	   关卡NPC死亡发放奖励的逻辑：
	   对于通版方式而言，一切发放都由版面完成

	  注意，处理GM_MSG_VANISH时，也用了和下面一样的代码
	 */
	if (pNPC->pGrouping)
	{
		//这是关卡逻辑，处理NPC死亡的地方，关卡掉落应该在这里完成比较好
		//大地图也可能到这里（因为NPC分组，但是这个没有关系，分组里面不会产生任何掉落）
		ASSERT(pNPC->pGrouping);
		int lastHit = attacker.GetNewObjID();
		pNPC->pGrouping->imp->OnNPCDeath(pNPC, _cnt.stage_id, _cnt.wave_id, _cnt.origin_pos, has_award, false,  _template, who.GetNewObjID(), lastHit);
	}
	else
	{
		if (_cnt.is_stage_npc)
		{
			//使用消息发送死亡消息 这个死亡消息主要是让场景产生掉落 这个掉落适用于关卡和副本，不用于大地图
			MSG msg;
			BuildMessage3(msg, GM_MSG_SCENE_LEVEL_COMMON, GetSceneImp()->Parent()->xid, pNPC->ID, pNPC->pos, 1,
			              _template->tid, has_award ? 1 : 0);
			gmatrix::GetInstance().SendMessage(msg);
		}
	}

	if (has_award)
	{
		if (_template->drop_buff_box_odds > 0)
		{
			int count = (int)_template->drop_buff_box_odds;
			float extra = _template->drop_buff_box_odds - (float)count;
			float rand = abase::RandUniform();
			if (rand < extra)
			{
				++ count;
			}
			if (count > 0)
			{
				abase::vector<tid_t> buff_box_ids;
				for (int i = 0; i < count; ++ i)
				{
					for (int j = 0; j < 4; ++ j)
					{
						if (_template->drop_buff_box_ids[j] > 0)
						{
							buff_box_ids.push_back(_template->drop_buff_box_ids[j]);
						}
					}
				}
				if (!buff_box_ids.empty())
				{
					GetSceneImp()->CreateBuffBoxMatter(_parent->pos, who, 3, buff_box_ids);
				}
			}
		}
	}

//	if (_template->is_boss)
	{
		struct timeval tv;
		gettimeofday(&tv, 0);
		if (GetSceneImp())
		{
			std::stringstream os;
			os << tv.tv_sec << ":" << "boss_die:" << GetParent()->tid << ":" << attacker.id << ":" << (tv.tv_sec - GetParent()->birth_time.tv_sec) << ":" << GetSkill().FeedBackSize() << ":";
			GetSceneImp()->AddSceneLog(os.str());
		}
	}

	if (_pet.IsValid())
	{
		SendTo(GM_MSG_DISAPPEAR, _pet, 0);
	}
}


void gnpc_imp::WorldReward(const XID& last_attacker, const attacker_info_t *pInfo, XID& who)
{
	gnpc *pNPC = (gnpc *)_parent;
	XID from_who;
	XID playerTemp;
	IDList list, drop_list;
	if (_template->aggro_type == MONSTERAGGRO_LIST)
	{
		XID team;
		_ai_ctrl->GetAggroMan().GetFirstPlayer(who, team);
		if (team.IsTeam())
		{
			playerTemp = who;
			who = team;
		}
	}
	else if (_template->aggro_type == MONSTERAGGRO_FIRST_DAMAGE)
	{
		XID team;
		_ai_ctrl->GetAggroMan().GetFirstAttacker(who, team);
		if (team.IsTeam())
		{
			playerTemp = who;
			who = team;
		}
	}
	else if (_template->aggro_type == MONSTERAGGRO_OWNER)
	{
		who = GetParent()->owner;
		if (GetParent()->owner_team.IsTeam())
		{
			playerTemp = who;
			who = GetParent()->owner_team;
		}
	}
	else if (_template->aggro_type == MONSTERAGGRO_MAX_DAMAGE)
	{
		_dm_map.GetFirst(who);
	}
	else if (_template->aggro_type == MONSTERAGGRO_FINAL_DAMAGE)
	{
		who = last_attacker;
		if (pInfo)
		{
			if (pInfo->team.IsValid())
			{
				playerTemp = who;
				who = pInfo->team;
			}
		}
	}
	else if (_template->aggro_type == MONSTERAGGRO_INSTANCE)
	{
		//TODO
		return;
	}
	else if (_template->aggro_type == MONSTERAGGRO_TASK_BOSS)
	{
		//任务掉落物品归属于伤害最高， 经验归属于所有仇恨者或者其队伍，目的:玩家都能完成任务
		_dm_map.GetFirst(who);
		_ai_ctrl->GetAggroMan().GetAllPlayer(list);
		player_team::DeliverTaskFinish(pNPC->ID, _template, GetParent()->pos, GetSceneImp(), list, who, GetLevel());
		list.clear();
	}
	else if (_template->aggro_type == MONSTERAGGRO_LIST_TEAM)
	{
		_dm_map.GetFirst(who);
		_ai_ctrl->GetAggroMan().GetAllPlayer(list);

		IDList tmp_list;
		std::set<XID> uniq_player_xid;

		auto collect_team_members_f = [](const XID & team_xid, std::vector<ruid_t>& members)
		{
			members.clear();

			auto *ipd = GetInterProcessData();
			if (!ipd)
			{
				return;
			}

			auto team_info = ipd->teammanager.get_team(team_xid.id);
			if (!team_info)
			{
				return;
			}

			int tmp_capacity = GetTeamCapacity(team_info->teamid);
			for (int i = 0; i < tmp_capacity; ++i)
			{
				if (team_info->members[i].roleid <= 0)
				{
					continue;
				}
				members.push_back(team_info->members[i].roleid);
			}
		};

		for (auto& xid : list)
		{
			if (!xid.IsPlayer()) // 队伍在list中 则角色一定在list中
			{
				continue;
			}
			if (uniq_player_xid.find(xid) != uniq_player_xid.end()) // 该玩家所在队伍已经被处理过
			{
				continue;
			}
			tmp_list.push_back(xid);
			uniq_player_xid.insert(xid);

			object_info player_info;
			if (!gmatrix::GetInstance().QueryObject(xid, player_info) || !player_info.team.IsValid())
			{
				continue;
			}

			std::vector<ruid_t> members;
			collect_team_members_f(player_info.team, members);

			for (ruid_t member_rid : members)
			{
				if (member_rid == xid.id)
				{
					continue;
				}

				XID member_xid;
				MAKE_XID(member_xid, member_rid);
				if (uniq_player_xid.find(member_xid) != uniq_player_xid.end())
				{
					continue;
				}
				uniq_player_xid.insert(member_xid); // 放在这里可减少后面两步检查的次数

				object_info info;
				if (!gmatrix::GetInstance().QueryObject(member_xid, info))
				{
					continue;
				}
				if (!info.CheckRange2(pNPC->world_id, pNPC->world_tid, pNPC->scene_tag, pNPC->mid, pNPC->pos,
				                      TEAM_KILL_MONSTER_FRIEND_AMITY_SQUARE_RANGE))
				{
					continue;
				}

				tmp_list.push_back(member_xid);
			}
		}
		list.swap(tmp_list);

		player_team::DeliverTaskFinish(pNPC->ID, _template, GetParent()->pos, GetSceneImp(), list, who, GetLevel());
		list.clear();
	}
	else
	{
		//TODO:
		return;
	}

	from_who = Parent()->ID;
	if ( _cnt.is_stage_npc )
	{
		GLog::log(GLOG_INFO, "npc %d(%ld) killed by %ld, list:%zu, team:%d\n", _template->tid, pNPC->ID.id, who.id, list.size(), who.IsTeam() ? (int)who.id : 0);
	}
	__PRINTF("大地图怪物死亡了%d(" FMT_I64"), 杀手是%ld, " FMT_I64", list:%zu, team:%d, pos(%.2f,%.2f)\n",
	         _template->tid, pNPC->ID.id, who.id, last_attacker.id, list.size(), who.IsTeam() ? (int)who.id : 0,
	         Parent()->pos.x, Parent()->pos.z);
	if (who.IsTeam())
	{
		player_team::TeamKillMonster(who.id, pNPC);
		//npc_death_generate_reward_t data;
		//data.team_task_id = pNPC->team_task_id;
		//data.pSceneImp = GetSceneImp();
		//data.npc_level = _cnt.level != 0 ? _cnt.level : _template->level;
		//XID teamman_xid(GM_TYPE_TEAM, INVALID_OBJECT_ID);
		//SendTo(GM_MSG_NPC_DEATH_GENERATE_REWARD, teamman_xid, ((gnpc *)_parent)->tid, &data, sizeof(data));
	}
	//如果属于队伍给队伍发经验，然后掉落
	if (who.IsTeam() && list.size() == 0)
	{
		//队伍，发给队伍经验，且只发经验
		player_team::DeliverDropReward(who, _template, GetParent()->pos, pNPC->ID, GetSceneImp(), &list, &drop_list);
		if ( !list.size() && !drop_list.size() ) //team状态不正常
		{
			GLog::log(GLOG_INFO, "team" FMT_I64 "of player" FMT_I64 " find nobody", who.id, playerTemp.id);
			if ( playerTemp.IsPlayer() )
			{
				who = playerTemp;
			}
		}
	}

	if (list.size() && who.IsTeam())
	{
		//直接掉落物品
		XID team;
		team = who;
		ItemVector items;
		abase::vector<matter_info,  abase::tla_alloc> matters;
		matters.reserve(items.size());
		item_manager::GetInstance().GetItemFromSpecialDropTables(_template->special_drop_table, _template->special_drop_times, items);
		if (items.size())
		{
			abase::vector<size_t> bind_money;
			abase::vector<size_t> trade_money;
			abase::vector<tid_t> souls;

			GetSceneImp()->CreateNPCDropMatter(Parent()->pos, XID(), team, NULL, 0, 0, 0, 4, 0, 0,
			                                   _template->tid, from_who, items, bind_money, trade_money, souls, &matters);
		}
		matter_info _matters[matters.size()];
		for (size_t i = 0; i < matters.size(); _matters[i] = matters[i], i ++);

		MSG msg;
		BuildMessage4(msg, GM_MSG_NPC_DROP_REWARD, who, pNPC->ID, pNPC->pos, _template->tid, 0, list.size(), _cnt.level, _matters, sizeof(_matters));
		gmatrix::GetInstance().SendMessage(list.begin(), list.end(), msg);
	}
	else if (who.IsPlayer())
	{
		//个人，发给个人
		MSG msg;
		BuildMessage4(msg, GM_MSG_NPC_DROP_REWARD, who, pNPC->ID, pNPC->pos, _template->tid, 1, 0, _cnt.level);
		gmatrix::GetInstance().SendMessage(msg);
	}

	if (drop_list.size() && who.IsTeam())
	{
		//新掉落，使用奖励模板发放掉落
		for (auto& xid : drop_list)
		{
			RemoteCall::CallObjectRemoteFunc("PlayerGetDrop", xid.id, PB::ipt_remote_call::OBJECT_TYPE_GS_GPLAYER_IMP, NULL,
			                                 GetParent()->world_tid, _template->tid, 0, 0,
			                                 pNPC->pos.x, pNPC->pos.y, pNPC->pos.z, Parent()->world_id.id,
			                                 Parent()->scene_tag, Parent()->mid);
		}
	}
	else if (who.IsPlayer())
	{
		//新掉落，使用奖励模板发放掉落
		RemoteCall::CallObjectRemoteFunc("PlayerGetDrop", who.id, PB::ipt_remote_call::OBJECT_TYPE_GS_GPLAYER_IMP, NULL,
		                                 GetParent()->world_tid, _template->tid, 0, 0,
		                                 pNPC->pos.x, pNPC->pos.y, pNPC->pos.z, Parent()->world_id.id,
		                                 Parent()->scene_tag, Parent()->mid);
	}
	SendTo(GM_MSG_CREATE_ROLL, _parent->world_id, _template->tid, &who, sizeof(who));
}

void gnpc_imp::LifeExhaust()
{
	if (_wait_release)
	{
		return;
	}
	_wait_release = true;
	__PRINTF(""   FMT_I64":%d LifeExhaust\n", _parent->ID.id, GetParent()->tid);
	if (_summon_help_npc)
	{
		MSG msg;
		BuildMessage(msg, GM_MSG_SUMMON_MONSTER_EXHAUST, GetMaster(), GetParent()->ID, A3DVECTOR3());
		gmatrix::GetInstance().SendMessage(msg);
	}

	//如果有交互绑定，通知交互玩家交互结束
	if (_interact_data_ptr)
	{
		_interact_data_ptr->OnMasterDie();
	}

	gnpc *pNPC = GetParent();
	pNPC->SetObjectState(gobject::STATE_ZOMBIE);
	gscene_imp *pSceneImp = GetSceneImp();
	if (_pet.IsValid())
	{
		SendTo(GM_MSG_DISAPPEAR, _pet, 0);
	}
	LeaveScene();
	if (_spawner && gmatrix::GetInstance().GetServerStatus() <= SS_RUNNING)
	{
		_buff.ClearSubscibeList(this);
		if (pNPC->pGrouping)
		{
			//和Group 脱开 这里的group多半是场景的动态group
			pNPC->pGrouping->imp->OnNPCRelease(pNPC, _ai_ctrl->IsHero());
			pNPC->pGrouping = NULL;
		}

		//释放到spawner中
		if (_spawner->Reclaim(pSceneImp, pNPC, this))
		{
			//若返回false 则本对象应当已经被释放，这时不应当再修改npc_state
			_npc_state = NPC_STATE_SPAWNING;
		}
	}
	else
	{
		//注，如果没有spawner，又有 pGrouping的话，那么应该是关卡的逻辑
		if (pNPC->pGrouping)
		{
			pNPC->pGrouping->imp->OnNPCRelease(pNPC, _ai_ctrl->IsHero());
		}
		_commander->Release();
	}
}

void gnpc_imp::ServerShutDown()
{
	gnpc *pNPC = GetParent();
	pNPC->SetObjectState(gobject::STATE_ZOMBIE);
	if (pNPC->pGrouping)
	{
		pNPC->pGrouping->imp->OnNPCRelease(pNPC, _ai_ctrl->IsHero());
		pNPC->pGrouping = NULL;
	}
	gobject_imp::ServerShutDown();
}

void gnpc_imp::OnStayAroundFail(const A3DVECTOR3& pos)
{
	GetWorldImp()->OnNPCStayAroundFail(this, pos);
}
void gnpc_imp::FormSpecialContent(raw_wrapper& rw)
{
	using namespace S2C;
	PB::gp_scene_special_object_info proto;
	proto.set_info_type(PB::gp_scene_special_object_info::INFO_NPC);
	auto pInfo = proto.mutable_npc_info();
	GetParent()->MakeNPCDefiniteInfo(*pInfo);
	CMD::Make<S2C::CMD::PBS2C>::From(rw, proto);
}
void gnpc_imp::ChangeShape(unsigned char shape)
{
	if (_shape == shape)
	{
		return;
	}
	//更换一下使用的索引
	if (shape > npc_service_t::SERVICE_NPC_SHAPE_COUNT)
	{
		return;
	}
	gnpc *pNPC = GetParent();
	pNPC->shape = shape;
	_shape = shape;
	Runner()->npc_change_shape();
	if (_service)
	{
		_service->SetCurIndex(shape);
	}
}

bool gnpc_imp::AdjustDirection(const object_info& info, float dir_dot_product)
{
	//npc想攻击的时候方向一定对
	if (!CanTurn())
	{
		return false;
	}
	Ctrl()->TryFaceTarget(info.pos, GetParent()->body_size, true);
	return true;
}

void gnpc_imp::WantCommunicateWithSomeOne(const XID& who, int faction)
{
	_ai_ctrl->CommunicateWithSomeOne();
}

bool gnpc_imp::CheckCanAttack(const XID& target, std::set<int> *ignore_state) const
{
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;    //对象是否存在
	}
	if (info.IsInBornProtectTime())
	{
		return false;    //处于保护时间
	}
	if (_master_data)
	{
		//有主人信息用主人信息检查
		const gobject *pObj = Parent();
		if (!pObj)
		{
			return false;
		}
		attacker_info_t agent;
		agent.attacker = pObj->ID;
		agent.faction = pObj->faction;
		agent.enemy_faction = GetEnemyFaction();
		agent.friend_faction = GetFriendFaction();
		agent.pk_setting_a = _master_data->_master_prop.pk_setting;
		agent.duel_target = _master_data->_master_prop.duel_target;
		agent.mafia_id = _master_data->_master_prop.mafia_id;
		agent.team = _master_data->_master_prop.team;
		agent.spouse_id = _master_data->_master_prop.spouse_id;
		agent.pet_master = GetMaster();
		agent.attacker_mode = _master_data->_master_prop.pvp_protect_flag ? 0 : attacker_info_t::AM_PVP_ENABLE;
		if (GetRegionSetting() & RS_SANCTUARY) //是安全区，覆盖掉AM_PVP_ENABLE//cancel
		{
			agent.attacker_mode &= attacker_info_t::AM_PVP_SANCTUARY;
		}

		CombatChecker::target_info_t target_info;
		target_info.ID = target;
		target_info.info = info;
		target_info.pk_type = pObj->pSceneImp->GetPKType();
		target_info.is_valid = true;
		if (!CombatChecker::CanAttack(agent, target_info))
		{
			return false;
		}
	}
	else
	{
		if (!gcreature_imp::CheckCanAttack(target, ignore_state))
		{
			return false;
		}
	}

	if (!_template)
	{
		return true;
	}
	ASSERT(_template);
	if (IsInBornProtectTime())
	{
		return false;    //出生保护时间内，不会主动攻击
	}
	switch (_template->attack_player_strategy)
	{
	default:
	case NO_EFFECT:
	{
	}
	break;

	case NO_ATTACK_PLAYER:
	{
		if (target.IsPlayer())
		{
			return false;
		}
	}
	break;

	case ONLY_ATTACK_PLAYER:
	{
		if (!target.IsPlayer())
		{
			return false;
		}
		return true;
	}
	break;

	case ONLY_ATTACK_DIFF_FACTION_PLAYER:
	{
		if (target.IsPlayer() && !(GetWorldImp()->GetMafiaID() && MafiaAttackCheck(Parent()->ID, GetParent()->mafia_id, target, info.mafia_id)))
		{
			return false;
		}
	}
	break;
	}
	return true;
}

void gnpc_imp::OnActionStart(int action_id, int ret_code, int mission_id, int type, int param2)
{
	if ((_notify_action & 0x01) && type != AT2_NULL && type != AT2_MOVE)
	{
		//通知给场景
		SendTo2(GM_MSG_LEVEL_CREATURE_ACTION, GetSceneImp()->Parent()->xid, type, param2);
	}
	_ai_ctrl->EventActionStart(mission_id, action_id);
}

void gnpc_imp::OnActionStartFailed(int action_id, int ret_code, int mission_id)
{
	//避免嵌套逻辑，用消息处理
	SendTo3(GM_MSG_NPC_ACTION_FAILED_EVENT, Parent()->ID, mission_id, action_id, ret_code);
}
void gnpc_imp::OnActionEnd(int action_id, int ret_code, int mission_id, int param2)
{
	//避免嵌套逻辑，用消息处理
	SendTo3(GM_MSG_NPC_ACTION_END_EVENT, Parent()->ID, mission_id, action_id, ret_code);
}

float gnpc_imp::BeAttacked(const XID& who, const attack_msg& msg, float damage, unsigned int flags, const A3DVECTOR3& pos, char prev_control_type, char control_type, int control_time)
{
	return gcreature_imp::BeAttacked(who, msg, damage, flags, pos, prev_control_type, control_type, control_time);
}

void gnpc_imp::TurnDirection(int angle)
{
	//npc转向相关内容
	Parent()->SetDirection(angle_to_dir(angle));
	Runner()->object_turn(GP_TURN_STOP, Parent()->dir);
}

void gnpc_imp::TurnToDir(dir_t dir)
{
	Parent()->SetDirection(dir);
	Runner()->object_turn(GP_TURN_STOP, Parent()->dir);
}

bool gnpc_imp::PlayCG(int id)
{
	//TODO
	return false;
}

void gnpc_imp::InitOneService(size_t index, tid_t tid)
{
	const npc_template *pTemplate = npc_template_manager::GetInstance().Get(tid);
	if (!pTemplate)
	{
		return;
	}

	if (pTemplate->npc_data.sell_service_template[0])
	{
		const npc_template_manager::GOOD_TRANF_T_VEC& goods_vec = npc_template_manager::GetInstance().GetNPCSellServiceGoodTranfT(tid);
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_SELL);
		AddProvider(index, provider, 0, 0, goods_vec.size() ? &goods_vec[0] : NULL, sizeof(npc_sell_service_template::good_tranf_t) * goods_vec.size());
	}
	//添加便捷服务
	vector<npc_easy_lottery_service> easy_vec;
	for (int i = 0; i < EXP_NPC_LOTTERY_SERVICE_NUM; i ++)
	{
		if (!pTemplate->npc_data.easy_lottery_service[i])
		{
			continue;
		}
		easy_vec.push_back(*pTemplate->npc_data.easy_lottery_service[i]);
	}
	if (easy_vec.size())
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_EASY_MALL);
		AddProvider(index, provider, 0, 0, easy_vec.size() ? &easy_vec[0] : NULL, sizeof(npc_easy_lottery_service) * easy_vec.size());
	}
	if (pTemplate->npc_data.buy_service_template)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PURCHASE);
#ifdef USE_CONVEX
		AddProvider(index, provider, pTemplate->npc_data.buy_service_template->tid, 0, &pTemplate->npc_data.buy_service_template->buy_items_vec[0], sizeof(NPC_BUY_SERVICE_ITEMS) * pTemplate->npc_data.buy_service_template->buy_items_vec.size());
#else
		AddProvider(index, provider, pTemplate->npc_data.buy_service_template->tid, 0, &pTemplate->npc_data.buy_service_template->buy_items_vec[0], sizeof(NPC_BUY_SERVICE::item_t) * pTemplate->npc_data.buy_service_template->buy_items_vec.size());
#endif
	}
	if (pTemplate->npc_data.task_in_service_template)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TASK_IN);
		AddProvider(index, provider, 0, tid, pTemplate->npc_data.task_in_service_template->task_in_list, pTemplate->npc_data.task_in_service_template->count * sizeof(int));
	}
	else if (NPCTasksManager::GetInstance().CanNPCAwardTask(tid))
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TASK_IN);
		AddProvider(index, provider, 0, tid);
	}
	if (pTemplate->npc_data.task_out_service_template)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TASK_OUT);
		AddProvider(index, provider, 0, 0, pTemplate->npc_data.task_out_service_template->task_out_list, pTemplate->npc_data.task_out_service_template->count * sizeof(int));
	}
	else if (NPCTasksManager::GetInstance().CanNPCDeliverTask(tid))
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TASK_OUT);
		AddProvider(index, provider, 0, tid);
	}

	if (int num = pTemplate->npc_data.service_transmit_target_num)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TRANSMIT);
		AddProvider(index, provider, 0, 0, pTemplate->npc_data.transmit_entry, sizeof(npc_template::npc_statement::__st_ent)* num);
	}
	if (pTemplate->npc_data.service_enter_instance_num)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_ENTER_INSTANCE);
		AddProvider(index, provider, pTemplate->npc_data.service_enter_instance_tid, 0, pTemplate->npc_data.service_enter_instance_list, pTemplate->npc_data.service_enter_instance_num * (sizeof(npc_template::npc_statement::tid_count_t)));
	}
	if (pTemplate->npc_data.service_task_event_num)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TASK_EVENT);
		AddProvider(index, provider, 0, 0, pTemplate->npc_data.service_task_event_list, pTemplate->npc_data.service_task_event_num * sizeof(npc_template::npc_statement::task_event_t));
		AddProvider(index, provider, 0, tid);
	}
	if (pTemplate->npc_data.reputation_sell_service.cost_repu_id && pTemplate->npc_data.reputation_sell_service.cost_repu_num)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_REPU_MALL);
		AddProvider(index, provider, 0, 0, &pTemplate->npc_data.reputation_sell_service, sizeof(pTemplate->npc_data.reputation_sell_service));
	}
	if (pTemplate->npc_data.minigame_service)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MINI_GAME);
		AddProvider(index, provider, 0, 0, &pTemplate->npc_data.minigame_service, sizeof(pTemplate->npc_data.minigame_service));
	}

	//简单服务组合
	//COMBSEV4_FACTION_RACE_TRANSPORT
	if (pTemplate->npc_data.combined_services4 & COMBSEV4_FACTION_RACE_TRANSPORT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CORPS_BATTLE_TELEPORT);
		AddProvider(index, provider);
	}
	if ((pTemplate->npc_data.combined_services & CMBSEV_CHANGE_PROF) || (pTemplate->npc_data.combined_services4 & COMBSEV4_CHANGE_RACE))
	{
		//service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_WAYPOINT);
		//AddProvider(index, provider);
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CHANGE_PROF);
		AddProvider(index, provider);

		provider = service_manager::GetInstance().CreateProviderInstance(SID_NEW_PROF);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_REINFORCE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_EQUIP_REINFORCE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_ENCHASE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_EQUIP_ENCHASE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_TAKEOUT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(8);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_ATTACH)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(10);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_ATTACHCLEAR)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(11);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_ENCHANT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PRODUCE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_SEPERATE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(45);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_UPGRADE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(13);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_UPGRADECLEAR)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(17);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_EQUIP_HOLE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(24);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_BIND)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(35);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_BINDCLEAR)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(36);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_BINDRESTORE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(37);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_HUNT_DRAGON)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_HUNT_DRAGON);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & (CMBSEV_SWORN | CMBSEV_FACTION))
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_FACTION);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_STOREHOUSE)
	{
		//修改密码服务挪到了安全锁界面，不需要仓库老板的服务了
		//service_provider* provider = service_manager::GetInstance().CreateProviderInstance(SID_DEPOSITORY_PASSWD);
		//AddProvider(index,provider);
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_DEPOSITORY_OPEN);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_MAIL)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAIL);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_BUDDY_LEARNSKILL)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PET_SKILL);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_PET_ARTIFICE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PET_PARENTAGE_EXCHANGE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_FACTION_STORAGE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_STORAGE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services & CMBSEV_RECIPE_RESEARCH_INSTANCE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_RECIPE_INSTANCE);
		AddProvider(index, provider);
	}
	//简单服务组合2
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_EQUIPATTACHTRANSFER)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(49);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_EQUIPTRANSFORM)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(50);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_NATION_ESCROT_CHANGE_CARGO)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_ESCORT_CHANGE_CARGO);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_HORSEEQUIP)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(53);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_HORSETRANSFER)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(54);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_WORLDTRANSMIT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TRANSPORT);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_PET_RECYCLE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PET_RECYCLE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_GET_BUDDY_IDCARD)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PET_GET_IDENTITY);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_FACELIFT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_FACELIFT);
		AddProvider(index, provider);
		provider = service_manager::GetInstance().CreateProviderInstance(SID_CHANGE_BODY);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & (CMBSEV2_NPCBUY_NORMALITEM | CMBSEV2_NPCBUY_SPECITEM | CMBSEV2_NPCBUY_NORMALEQUIP | CMBSEV2_NPCBUY_SPECEQUIP))
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_BUY);
		AddProvider(index, provider);
		provider = service_manager::GetInstance().CreateProviderInstance(SID_BUY_BACK);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_NPC_REPAIR)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_REPAIR);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_MARRIAGE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MARRIAGE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_PET_DEPOSIT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PET_DEPO);
		AddProvider(index, provider);
	}


	if (pTemplate->npc_data.combined_services2 & CMBSEV2_PET_BLOOD_IDENTIFY)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PET_BLOOD_IDENTIFY);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_FACTION_GRANT_BENIFIT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_WELFARE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_ALLIANCEWAR)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_ALLIANCEWAR_APPLY);
		AddProvider(index, provider);

		service_provider *provider2 = service_manager::GetInstance().CreateProviderInstance(SID_ALLIANCEWAR_JOIN);
		AddProvider(index, provider2);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_SUB_FACTION_APPLY)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_SUB_MAFIA_APPLY);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_TRANSFER_TO_FACTION)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TRANSFER_TO_MAFIA);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_TRANSFER_TO_THISMAP_BASE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TRANSFER_TO_LOCAL_MAFIA);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services2 & CMBSEV2_FACTIONBASE_BUILD)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_UP_BUILD);
		AddProvider(index, provider);
	}
	/*
	if(pTemplate->npc_data.combined_services2 & CMBSEV2_FACTIONBASE_RECOVER)
	{
		service_provider* provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_RECOVER);
		AddProvider(index,provider);
	}*/
	//简单服务组合3
	/*
	if(pTemplate->npc_data.combined_services3 & COMBSEV3_WORLD_INFO_SUBMIT)
	{
		service_provider* provider = service_manager::GetInstance().CreateProviderInstance(SID_SUBMIT_INFORMATION);
		AddProvider(index,provider);
	}
	*/
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_QIANHE_CENTER_BATTLE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_QIANHE_CENTER_BATTLE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_DONGJING_CENTER_BATTLE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_DONGJING_CENTER_BATTLE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_MASTER_DECIPLE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_SECT);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_NORMAL_MARRIAGE_PARADE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PARADING);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_WORLD_INFO_MANAGEMENT)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_MSGSTORE);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_REWARD_TASK_PUBLISH)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_JUEWEI_TASK_PUBLISH);
		AddProvider(index, provider);
		provider = service_manager::GetInstance().CreateProviderInstance(SID_JUEWEI_TASK_SEARCH);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_REWARD_TASK_PAY)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_JUEWEI_TASK_SETTLE);
		AddProvider(index, provider);
		provider = service_manager::GetInstance().CreateProviderInstance(SID_JUEWEI_TASK_SEARCH);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_REWARD_TASK_OPEN)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_JUEWEI_TASK_OUT);
		AddProvider(index, provider);
		provider = service_manager::GetInstance().CreateProviderInstance(SID_JUEWEI_TASK_SEARCH);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_TREASUREHOUSE_MANAGE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_TREASURE_MAN);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_TREASUREHOUSE_TRAPSETTING)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_TREASURE_TRAP);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_TREASUREHOUSE_RECEIVE_GAIN)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_TREASURE_BUFF);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_TREASUREHOUSE_STEAL)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_TREASURE_STEAL);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_PET_XISUI)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PET_REBORN);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_WEDDING)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_WEDDING_APPLY);
		AddProvider(index, provider);
	}
	/*
	if(pTemplate->npc_data.combined_services3 & CMBSEV2_WEDDING_CELEBRATE_TRANSMIT)
	{
		service_provider* provider = service_manager::GetInstance().CreateProviderInstance(SID_WEDDING_TRANSMIT);
		AddProvider(index,provider);
	}
	*/
	/*
	if(pTemplate->npc_data.combined_services3 & COMBSEV3_COTTAGE_WAGE)
	{
		service_provider* provider = service_manager::GetInstance().CreateProviderInstance(SID_PART_TIME_ROB_ESCORT);
		AddProvider(index,provider);
	}
	*/
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_TIGUAN)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_TIGUAN);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services3 & COMBSEV3_UPGRADE_DONATION)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_UPGRADE_DONATION);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services4 & COMBSEV4_DUOQI_BATTLE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CENTERBATLLE_TELEPORT);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services3 & COMBSEV3_AUCTION)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_NEW_AUCTION);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services3 & COMBSEV3_SELL_REWARD)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_SELL_REWARD);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services4 & COMBSEV4_SECOND_HOME_TRANS)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_SECOND_HOMETOWN_ENTER);
		AddProvider(index, provider);
	}

	if ((pTemplate->npc_data.combined_services4 & COMBSEV4_HOME_SLAVE_WORK) || (pTemplate->npc_data.combined_services4 & COMBSEV4_HOME_SLAVE_CARE))
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_SECOND_HOMETOWN_SLAVE_OP);
		AddProvider(index, provider);
	}

	if ((pTemplate->npc_data.combined_services4 & COMBSEV4_PREGNATE) || (pTemplate->npc_data.combined_services4 & COMBSEV4_ADOPTION_BABY) ||
	        (pTemplate->npc_data.combined_services4 & COMBSEV4_BEAR_BABY) || (pTemplate->npc_data.combined_services4 & COMBSEV4_STORE_BABY))
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CHILDREN);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services4 & COMBSEV4_BABY_MARRIAGE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CHILD_MARRY);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services4 & COMBSEV4_BABY_DIVORCE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CHILD_DIVORCE);
		AddProvider(index, provider);
	}

	if ((pTemplate->npc_data.combined_services4 & COMBSEV4_BOAT_PARADE) || (pTemplate->npc_data.combined_services4 & COMBSEV4_FLYSOWRD_PARADE))
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PARADING);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services4 & COMBSEV4_WEDDING_VOW)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_MARRIAGE_VOWS);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_GUARD_MARRAY)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_GUARD_MARRY);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services4 & COMBSEV4_BABY_RETRIEVE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CHILD_TAKE_BACK);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services4 & COMBSEV4_EQUIP_UPGRADE)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_UPGRADE_EQUIPMENT);
		AddProvider(index, provider);
	}
	if (pTemplate->npc_data.combined_services4 & COMBSEV4_MANY_PUSH_CAR)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CENTER_ARENA_TEAM);
		AddProvider(index, provider);
	}


	if (pTemplate->npc_data.combined_services5 & COMBSEV5_HOST_PARTY || pTemplate->npc_data.combined_services5 & COMBSEV5_JOIN_PARTY)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PARTY_TELEPORT);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_OPEN_PARTY)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PARTY_APPLY);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_BUY_HOMETOWN)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_BUY_HOMETOWN);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_GEM_TRANSFER)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_PRODUCE_ITEM_SACRED_NUCLEUS);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_ACTIVITY_EFFIGY)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_ACTIVITY_EFFIGY);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_DANCE_TOGETHER)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_DANCE_TOGETHER);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_CORPS_BOSS)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_CORPS_BOSS);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_BUY_CONTRACTHOMETOWN)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_BUY_CONTRACT_HOMETOWN);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_BDSG)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_BDSG);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services5 & COMBSEV5_GO_TO_ROAM_NORMAL)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_TO_PVE_CENTER);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services6 & COMBSEV6_HONEY_GARDEN_OPEN)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_OPEN_HONEY_GARDEN);
		AddProvider(index, provider);
	}

	if (pTemplate->npc_data.combined_services6 & COMBSEV6_THUNDERDTRIKE_CHOICE_MAP)
	{
		service_provider *provider = service_manager::GetInstance().CreateProviderInstance(SID_THUNDERDTRIKE_CHOICE_MAP);
		AddProvider(index, provider);
	}


	service_provider *provider = NULL;
	switch (pTemplate->npc_data.type_faction_dyn_service)
	{
	case EXP_FACTION_DYN_SERVICE_TYPE_LOGISTIC:
	{
		provider = service_manager::GetInstance().CreateProviderInstance(SID_MAFIA_LOGISTIC);
		AddProvider(index, provider, 0, 0, NULL, 0);
	}
	break;

	case EXP_FACTION_DYN_SERVICE_TYPE_ACTIVITY:
	{
		provider = service_manager::GetInstance().CreateProviderInstance(SID_PLAYERDIY_ACTIVITY);
		AddProvider(index, provider, 0, 0, NULL, 0);
	}
	break;

	default:
	{
		//处理 index 选择镜像
	}
	break;
	}

	if (career_template_mgr::Instance().IsCookProduceNPC(tid))
	{
		provider = service_manager::GetInstance().CreateProviderInstance(SID_COOK_PRODUCE);
		AddProvider(index, provider, 0, 0, NULL, 0);
	}
}

void gnpc_imp::InitAllService()
{
	ASSERT(_template);
	tid_t tid = GetParent()->tid;
	const npc_template *pTemplateBase = npc_template_manager::GetInstance().Get(tid);
	if (!pTemplateBase)
	{
		return;
	}
	InitOneService(0, tid);
	for (int i = 0; i < npc_service_t::SERVICE_NPC_SHAPE_COUNT; ++i)
	{
		tid_t mirror_tid = pTemplateBase->npc_data.mirror_npc_ids[i];
		if (mirror_tid <= 0)
		{
			continue;
		}
		const npc_template *pTemplateMirror = npc_template_manager::GetInstance().Get(mirror_tid);
		if (!pTemplateMirror)
		{
			continue;
		}
		InitOneService(i + 1, mirror_tid);
	}
}

void gnpc_imp::SetOwner(const XID& player, const XID& team)
{
	gnpc *pNPC = GetParent();
	pNPC->owner = player;
	pNPC->owner_team = team;
	pNPC->SetObjectState(gobject::STATE_NPC_HAVE_OWNER);
}

bool gnpc_imp::CanMove(bool check_seal) const
{
	if (!_template)
	{
		return true;
	}
	if (MONATKSTRATEGY_FORTRESS == _template->strategy)
	{
		return false;
	}
	if (MONATKSTRATEGY_STUB == _template->strategy)
	{
		return false;
	}
	if (MONATKSTRATEGY_ARROWTOWER == _template->strategy)
	{
		return false;
	}
	if (check_seal && GetSealRootMode())
	{
		return false;
	}
	if (Parent()->IsRootMode())
	{
		return false;
	}
	return true;
}

bool gnpc_imp::CanTurn() const
{
	if (!_template)
	{
		return true;
	}
	if (MONATKSTRATEGY_STUB == _template->strategy)
	{
		return false;
	}
	if (MONATKSTRATEGY_ARROWTOWER == _template->strategy)
	{
		return false;
	}
	if (GetSealTurnMode())
	{
		return false;
	}
	return true;
}

bool gnpc_imp::StepMove(const A3DVECTOR3& offset, bool set_dir, int param, bool force_to_invalid_pos, int is_fly, unsigned int flags, unsigned char jump_type, unsigned char land_type, unsigned short pitch, unsigned char moveidle_act_idx, unsigned char param2, unsigned short client_speed, bool team_follow, bool )
{
	if (!CanMove(false))
	{
		return false;
	}
	A3DVECTOR3 old_pos = _parent->pos;
	if (gobject_imp::StepMove(offset, set_dir, param, force_to_invalid_pos, is_fly, flags, jump_type, land_type, pitch, moveidle_act_idx, param2, client_speed, team_follow, false))
	{
		A3DVECTOR3 new_pos = _parent->pos;
		if (_event_region && !_event_region->Update(this, true))
		{
			//检测到离开了限制区域，必须回退这次移动
			A3DVECTOR3 old_pos3 = old_pos;
			old_pos -= new_pos;
			gobject_imp::StepMove(old_pos, false, 0, false, is_fly, flags, jump_type, land_type, pitch, moveidle_act_idx, param2, client_speed, false);
			__PRINTF("STEP_MOVE IN REGION:%ld(%f,%f)->(%.9f,%.9f) old_pos(%.9f,%.9f) in restrict AREA:%s\n", Parent()->ID.id, new_pos.x, new_pos .z, _parent->pos.x, _parent->pos.z, old_pos3.x, old_pos3.z, _event_region->InRetrictArea() ? "true" : "false");
			return false;
		}
		if (IsPetClass())
		{
			//__PRINTF("Pet move gnpc_imp::StepMove target(%.2f,%.2f,%.2f)\n",Parent()->pos.x,Parent()->pos.y,Parent()->pos.z);
		}
		//给正在交互的人发同步位置的消息
		if (_interact_data_ptr)
		{
			_interact_data_ptr->NotifyPos();
		}
		return true;
	}
	return false;
}

bool gnpc_imp::CheckTarget(const XID& target, object_info& info)
{
	if (!target.IsActive())
	{
		return false;    //合法性
	}
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;    //存在性
	}
	if (info.zombie)
	{
		return false;    //活着
	}
	if (!info.Check(_parent))
	{
		return false;    //同世界场景
	}
	if (!(GetEnemyFaction() & info.faction))
	{
		// 帮派基地特殊逻辑
		if (!(GetWorldImp()->GetMafiaID() && MafiaAttackCheck(Parent()->ID, GetParent()->mafia_id, target, info.mafia_id)))
		{
			return false;
		}
	}
	if (!GetWorldImp()->CheckAttackTarget(_template, this, target))
	{
		return false;    //某些特殊的副本版面，需要脚本判断是否可攻击
	}
	return true;
}

bool gnpc_imp::CheckSelfAggroRange() const
{
	if (!IsCombatState())
	{
		return false;
	}
	if (horizontal_distance(_parent->pos, _combat_start_pos) > GetSquaredAggroRange())
	{
		return false;
	}
	return true;
}

void gnpc_imp::ForceGetTargetPosDir(const A3DVECTOR3& pos, dir_t dir)
{
	Runner()->object_move(pos, GP_MOVE_STOP | GP_MOVE_POS_NOTIFY | GP_MOVE_DIR_DIFF, 0, dir, 0, 65535, 0, 0, 0, 0);
	A3DVECTOR3 temp_pos = pos;
	temp_pos -= GetParent()->pos;
	StepMove(temp_pos);
	SetDir(dir);
	//__PRINTF("npc force pos(%f,%f,%f),dir(%d)\n",pos.x,pos.y,pos.z,dir);
}

int gnpc_imp::GetSelfParamValue(int key)
{
	return _self_param.GetValue(key);
}

void gnpc_imp::SetSelfParamValue(int key, int value)
{
	_self_param.SetValue(key, value);
}

int gnpc_imp::ModifySelfParamValue(int key, int offset)
{
	return _self_param.ModifyValue(key, offset);
}

void gnpc_imp::SetMaster(NPC_MASTER_TYPE master_type, const XID& master)
{
	_master_type = master_type;
	_master = master;
}

void gnpc_imp::BeforeDoDamage(int64_t& damage, const attacker_info_t *atk_info, bool ignore_invincible)
{
	if (_fixed_damage > 0)
	{
		damage = _fixed_damage;
	}

	//处理锁血的逻辑
	if (_hp_min_locked > 0)
	{
		int64_t hp = GetHP();
		if (hp > 0 && (hp - damage) < _hp_min_locked)
		{
			damage = hp - _hp_min_locked;
			if (damage < 0)
			{
				damage = 0;
			}
		}
	}
}

void gnpc_imp::SetNPCEquipData(unsigned int mask, const abase::octets& data)
{
	_equip_info.mask = mask;
	_equip_info.data = data;
}

void gnpc_imp::OnControlChange(int old_type, int new_type)
{
	gcreature_imp::OnControlChange(old_type, new_type);
	if (_ai_ctrl)
	{
		_ai_ctrl->EventControlChange(old_type, new_type);
	}
}

void gnpc_imp::UpdateEventRegion()
{
	if (_event_region)
	{
		_event_region->Update(this);
	}
}

void gnpc_imp::CreateEventRegion()
{
	if (_event_region)
	{
		return;
	}
	_event_region = new player_event_region();
	_event_region->SetParent(Parent());
	_event_region->OnEnterMap(this);
}

char gnpc_imp::CanBePushed() const
{
	if (_template)
	{
		return _template->can_be_pushed;
	}
	return CHAR_MAX;
}

void gnpc_imp::DecSP(float delta)
{
	if (!GetTemplate()->has_stiff_bar)
	{
		return;
	}
	if (_sp_restoring)
	{
		return;
	}
	if (delta < 1e-6)
	{
		return;
	}
	GetProperty().DecSP(delta);
	if (!GetProperty().GetSP())
	{
		// 释放技能
		object_interface oif(this);
		XID target;
		if (_ai_ctrl)
		{
			_ai_ctrl->GetAggroMan().GetFirst(target);
			GetSkill().InstantSkill(oif, GetTemplate()->sp_out_skill_id, 0, 0, 0, target, Parent()->pos, 0);
		}
		// 调用脚本
		OnSPOut();
		// 切换免疫掩码
		GetSkill().ModifyImmuneMask(GetTemplate()->immune_mask1, false);
		GetSkill().ModifyImmuneMask(GetTemplate()->immune_mask2, true);
		_sp_restoring = true;
		_prop.SetIncSP(1);
	}
}

void gnpc_imp::IncSP(float delta)
{
	if (delta + GetProperty().GetSP() > GetProperty().GetSPmax())
	{
		delta = GetProperty().GetSPmax() - GetProperty().GetSP();
	}
	if (delta < 1e-6)
	{
		return;
	}
	GetProperty().IncSP(delta);
	if (GetProperty().GetSP() >= GetProperty().GetSPmax())
	{
		// 切换免疫掩码
		GetSkill().ModifyImmuneMask(GetTemplate()->immune_mask2, false);
		GetSkill().ModifyImmuneMask(GetTemplate()->immune_mask1, true);
		_sp_restoring = false;
		_prop.SetIncSP(0);
		_sp_restoring_tick = 0;
	}
	PropertyUpdateAndNotify();
}

bool gnpc_imp::CanBeControl(int& control_type, float& control_move_dis_ratio, float& control_move_dis_extra, int time, bool need_check)
{
	if (_template)
	{
		control_move_dis_ratio = _template->control_move_dis_ratio;
		control_move_dis_extra = _template->control_move_dis_extra;
	}
	else
	{
		control_move_dis_ratio = 1.0f;
		control_move_dis_extra = 0.f;
	}
	//:硬直条
	if (!_sp_restoring && GetProperty().GetSPmax())
	{
		return false;
	}
	//破碎物不接受控制
	if (_template)
	{
		return !((LIFELESS_TYPE_CANNOT_SEL == _template->is_lifeness) || (LIFELESS_TYPE_CANNOT_SEL_OR_PASS == _template->is_lifeness));
	}
	return true;
}

void gnpc_imp::OnAttackFeedBack(const XID& who)
{
	if (_ai_ctrl)
	{
		_ai_ctrl->GetAggroMan().RefreshAggroTime();
	}
}

void gnpc_imp::AttachSleepBuff()
{
	_attach_sleep_buff_time = gmatrix::GetInstance().GetSysTime();
	if (_ai_ctrl)
	{
		_ai_ctrl->EventNPCAttachSleepBuff();
	}
}

void gnpc_imp::DeattachSleepBuff()
{
	_attach_sleep_buff_time = 0;
	if (_ai_ctrl)
	{
		_ai_ctrl->EventNPCDeattachSleepBuff();
	}
}

void gnpc_imp::OnAggroClear()
{
	_dm_map.Clear();
}

/*
void gnpc_imp::RedirectRuneSkill(int& id)
{
	if (!rune_skill)
	{
		return;
	}
	id = rune_skill->RedirectRuneSkill(id);
}
*/
bool gnpc_imp::TestCoolDown(unsigned short cd_id) const
{
	if (!_use_skill_cooldown)
	{
		return true;
	}
	return gcreature_imp::TestCoolDown(cd_id);
}

bool gnpc_imp::SetCoolDown(unsigned short cd_id, size_t cd_time)
{
	if (!_use_skill_cooldown)
	{
		return true;
	}
	return gcreature_imp::SetCoolDown(cd_id, cd_time);
}

bool gnpc_imp::ClrCoolDown(unsigned short cd_id)
{
	if (!_use_skill_cooldown)
	{
		return true;
	}
	return gcreature_imp::ClrCoolDown(cd_id);
}

float gnpc_imp::GetXPSkillRate() const
{
	if (_template->star > 0 && _template->star < 10)
	{
		return (10.0f - _template->star) / 10.0f;
	}
	else if (_template->star >= 10)
	{
		return 0;
	}
	else
	{
		return 1.0f;
	}
}

bool gnpc_imp::IsWeaponEquiped() const
{
	if ( GetParent()->CheckObjectState(gobject::STATE_NPC_PLAYER) ) //是玩家镜像
	{
		return true;
	}
	else
	{
		return false;//和修改前保持一致
	}
}

void gnpc_imp::BroadcastAfterDeath(XID who)
{
	XID targetPlayer = who;
	if ( targetPlayer.IsTeam() )
	{
		targetPlayer = player_team::GetValidMember(targetPlayer);
		if (!targetPlayer.IsValid())
		{
			return;
		}
		SystemSpeak(_template->speak_id_on_team_kill, targetPlayer);
	}
	else
	{
		SystemSpeak(_template->speak_id_on_death, targetPlayer);
	}
}

void gnpc_imp::BeginTransform(int tid, int attacker_newid)
{
	const npc_template *pTemplate = npc_template_manager::GetInstance().Get(tid);
	if (!pTemplate)
	{
		return;
	}
	//换技能
	ClearAction();
	if (_cnt.is_stage_npc)
	{
		pTemplate->BuildSkillWeight(_cnt.skill_weight_adjust, _cnt._skill_map);
	}
	else
	{
		_cnt._skill_map = _template->_skill_map;
	}
	GetParent()->transform_tid = tid;
	GetParent()->transform_attacker_newid = attacker_newid;
	_parent->SetObjectState(gobject::STATE_TRANSFORM);
	_runner->transform_state(tid, 0, attacker_newid);
}

void gnpc_imp::CancelTransform()
{
	if (!GetParent()->transform_tid)
	{
		return;
	}
	GetParent()->transform_tid = 0;
	GetParent()->transform_attacker_newid = 0;
	ClearAction();
	if (_cnt.is_stage_npc)
	{
		_template->BuildSkillWeight(_cnt.skill_weight_adjust, _cnt._skill_map);
	}
	else
	{
		_cnt._skill_map = _template->_skill_map;
	}
	_parent->ClrObjectState(gobject::STATE_TRANSFORM);
}

void gnpc_imp::EndTransform()
{
	CancelTransform();
	_runner->transform_state(0);
}

int gnpc_imp::GetTransformTid() const
{
	return GetParent()->transform_tid;
}

void gnpc_imp::AddAggro(const XID& who, int rage, const attacker_info_t& info)
{
	if (CheckCanAddAggro(who, info))
	{
		_ai_ctrl->GetAggroMan().ForceAddRage(who, rage);
	}
}

int gnpc_imp::NPCTestRemoteObjectCall(int a, float b, int64_t c, char d)
{
	__PRINTF("gnpc_imp::NPCTestRemoteObjectCall id=%ld a=%d b=%f c=%ld d=%c\n", _parent->ID.id, a, b, c, d);
	return 0;
}

const A3DVECTOR3 gnpc_imp::GetClientPos() const
{
	return _commander->GetClientPos();
}
void gnpc::MakeNPCDefiniteInfo(PB::gp_npc_definite_info& proto, const A3DVECTOR3& _pos) const
{
	object_state_t state = MakeCreatureState() | object_state;
	object_state_t state2 = object_state2;
	proto.set_state(state);
	proto.set_state2(state2);
	if (state & gobject::STATE_NPC_PLAYER)
	{
		proto.set_tid(tid);
		gplayer_npc *self = (gplayer_npc *)this;
		self->MakePlayerDefiniteInfoCommon(*(proto.mutable_player_com_info()), _pos, state, state2);
		self->MakePlayerExtendState(*(proto.mutable_player_ext_state()), state, state2);
	}
	else
	{
		MakeNPCDefiniteInfoCommon(*(proto.mutable_npc_com_info()));
		MakeNPCExtendState(*(proto.mutable_npc_ext_state()), state, state2);
		if (state & gobject::STATE_PLAYER_TWIN)
		{
			gplayer_twin *self = (gplayer_twin *)this;
			proto.mutable_npc_com_info()->set_player_id(self->player_xid.id);
		}
		else if (state & gobject::STATE_PLAYER_MECH)
		{
			gplayer_mech *self = (gplayer_mech *)this;
			proto.mutable_npc_ext_state()->set_mech_state(self->mech_state);
		}
		else if (state2 & gobject::STATE2_PLAYER_HEIR)
		{
			gplayer_heir *self = (gplayer_heir *)this;
			proto.mutable_npc_ext_state()->set_heir_tid(self->heir_tid);
			proto.mutable_npc_ext_state()->set_heir_stage(self->heir_stage);

			PB::fashion_detail clothes_data;
			if (clothes_data.ParseFromArray(child_clothes_data, child_clothes_size))
			{
				proto.mutable_npc_ext_state()->add_heir_fashions()->CopyFrom(clothes_data);
			}
			PB::fashion_detail hair_data;
			if (hair_data.ParseFromArray(child_hair_data, child_hair_size))
			{
				proto.mutable_npc_ext_state()->add_heir_fashions()->CopyFrom(hair_data);
			}
			proto.mutable_npc_ext_state()->set_heir_spouse_guid(self->spouse_guid);
			proto.mutable_npc_ext_state()->set_heir_spouse_name(self->spouse_name, self->spouse_name_size);
		}
		else if (state2 & gobject::STATE2_PLAYER_CHESS)
		{
			gplayer_chess *self = (gplayer_chess *)this;
			proto.mutable_npc_ext_state()->set_chess_id(self->chess_id);
		}
		else if (state2 & gobject::STATE2_PLAYER_REPLISOME)
		{
			gplayer_replisome *self = (gplayer_replisome *)this;
			proto.mutable_npc_com_info()->set_player_id(self->player_xid.id);
		}
		else if (state2 & gobject::STATE2_PLAYER_DOG)
		{
			gplayer_replisome *self = (gplayer_replisome *)this;
			proto.mutable_npc_com_info()->set_player_id(self->player_xid.id);
		}

	}
}
void gnpc::MakeNPCDefiniteInfo(PB::gp_npc_definite_info& proto) const
{
	MakeNPCDefiniteInfo(proto, pos);
}
void gnpc::MakeNPCEnterSlice(PB::gp_npc_enter_slice& proto, const A3DVECTOR3& _pos) const
{
	auto& data = *(proto.mutable_protoc_data());
	MakeNPCDefiniteInfo(data, _pos);
}
void gnpc::MakeNPCEnterScene(PB::gp_npc_enter_scene& proto) const
{
	gnpc_imp *pImp = (gnpc_imp *)imp;
	proto.set_init_action(pImp->_cnt.init_action);
	auto& data = *(proto.mutable_protoc_data());
	MakeNPCDefiniteInfo(data);
}
void gnpc::MakeNPCDefiniteInfoCommon(PB::npc_definite_info_common& proto) const
{
	proto.set_id(ID.id);
	proto.set_newobjid(ID.GetNewObjID());
	proto.set_level(base_info.level);
	proto.set_vis_tid(vis_tid);
	proto.mutable_pos()->set_x(pos.x);
	proto.mutable_pos()->set_y(pos.y);
	proto.mutable_pos()->set_z(pos.z);
	proto.set_faction(faction);
	proto.set_crc(crc);
	proto.set_dir(dir);
	proto.set_combat_state(combat_state);
	proto.set_control_state(control_state);
	proto.mutable_prop_notify()->set_percent_hp(base_info.prop_notify.percentHP);
	proto.mutable_prop_notify()->set_percent_sp(base_info.prop_notify.percentSP);
	proto.mutable_prop_notify()->set_inc_sp(base_info.prop_notify.IncSP);
	proto.mutable_prop_notify()->set_percent_shield(base_info.prop_notify.percentShield);
	proto.set_can_not_be_attack(can_not_be_attack);
	if (CheckObjectServerState(gobject::SERVER_STATE_BOSS_BROADCAST))
	{
		proto.set_target_id(cur_target.id);
	}
	proto.mutable_hometown_interact_npc()->set_put_index(hometown_interact_npc.put_index);
}
void gnpc::MakeNPCExtendState(PB::npc_extend_state& proto, object_state_t state, object_state_t state2) const
{
	if (state & gobject::STATE_EXTEND)
	{
		gfx.FillPB(*proto.mutable_gfx(), false);
	}
	if (state & (gobject::STATE_NATION_ESCORT | gobject::STATE_PLAYER_MECH | gobject::STATE_CORPS_GOIAF_MANIPULATOR)
	        || state2 & (gobject::STATE2_PLAYER_FAKER | gobject::STATE2_PLAYER_HEIR | gobject::STATE2_PLAYER_DOG | gobject::STATE2_PLAYER_HEIR))
	{
		proto.set_master_id(master.id);
		unsigned char name_size = player_name_size;
		if (name_size >= sizeof(player_name_buf))
		{
			name_size = sizeof(player_name_buf);
		}

		proto.set_player_name(player_name_buf, name_size);
	}
	if (state & gobject::STATE_NPC_HAVE_OWNER)
	{
		proto.set_owner_id(owner.id);
		proto.set_owner_team_id(owner_team.id);
		unsigned char name_size = player_name_size;
		if (name_size >= sizeof(player_name_buf))
		{
			name_size = sizeof(player_name_buf);
		}

		proto.set_player_name(player_name_buf, name_size);
	}
	if (state & gobject::STATE_TRANSFORM)
	{
		proto.set_transform_tid(transform_tid);
		proto.set_transform_attacker_newid(transform_attacker_newid);
	}
	if (state & gobject::STATE_NPC_NAME)
	{
		unsigned char _name_size = name_size;
		if (_name_size >= sizeof(npc_name))
		{
			_name_size = sizeof(npc_name);
		}

		proto.set_npc_name(npc_name, _name_size);
	}
	if (state & gobject::STATE_NPC_AFFIX)
	{
		proto.set_affix_data(affix_data);
	}
	/*if (state & gobject::STATE_BIND)
	{
		proto.set_bind_param1(bind_param1);
		proto.set_bind_param2(bind_param2);
		proto.set_bind_param3(bind_param3);
		proto.set_pos_in_bind(pos_in_bind);
		for (unsigned char i = 0; i < max_bind_count; i++)
		{
			auto& member =  bind_members[i];
			if (i != pos_in_bind && member.id != 0)
			{
				auto pMember = proto.add_bind_members();
				pMember->set_pos(i);
				pMember->set_id(member.id);
			}
		}
	}*/
	if (state & gobject::STATE_NPC_INTERACT)
	{
		for (int i = 0; i < interact_list_size; i++)
		{
			proto.add_interact_target_id(interact_list[i].id);
		}

		proto.set_interact_speed(interact_speed);
		proto.set_interact_active_time(interact_active_time);
		proto.set_interact_sub_operation(interact_sub_operation);
		for (int i = 0; i < interact_sub_operation_data_size; ++ i)
		{
			proto.add_interact_sub_operation_data(interact_sub_operation_data[i]);
		}
	}
	if (state2 & gobject::STATE2_CORPS_GARDEN_FIELD)
	{
		auto garden_attr = proto.mutable_garden_npc();
		garden_attr->set_region(garden_region);
		garden_attr->set_field(garden_field);
		garden_attr->set_garden_flower_id(garden_flower_id);
		garden_attr->set_garden_state(garden_state);
		garden_attr->set_state_end(garden_state_end);
		garden_attr->set_breed_end(garden_breed_end);
	}
	proto.set_body_fake_scale(body_fake_scale);
}

int gnpc_imp::GetAIPrioTarget()
{
	if (_ai_ctrl)
	{
		XID target;
		if (_ai_ctrl->GetPriorTarget(target))
		{
			MakeNewIDFromXID(target, target);
			return target.newtype;
		}
	}
	return GM_TYPE_INVALID;
}

void gnpc_imp::BubbleSay(int id)
{
	_runner->npc_bubble_say(id);
}

void gnpc_imp::BubbleSay2(int id)
{
	_runner->npc_bubble_say2(id);
}

void gnpc_imp::ServiceDeliverTask()
{
	if (_ai_ctrl)
	{
		_ai_ctrl->EventNPCServiceDeliverTask();
	}
}

void gnpc_imp::SubobjPause()
{
	for (SUBOBJECT_MAP::iterator it = _subobject_map.begin(); it != _subobject_map.end(); ++it)
	{
		std::list<xid_time_t>& list = it->second;
		for (auto& a : list)
		{
			SendTo(GM_MSG_SUBOBJ_PAUSE, a.xid, 0);
		}
	}
}

void gnpc_imp::SubobjContinue()
{
	for (SUBOBJECT_MAP::iterator it = _subobject_map.begin(); it != _subobject_map.end(); ++it)
	{
		std::list<xid_time_t>& list = it->second;
		for (auto& a : list)
		{
			SendTo(GM_MSG_SUBOBJ_CONTINUE, a.xid, 0);
		}
	}
}

void gnpc_imp::ChangeAITrigger(int policy_id)
{
	//这个需要异步的,自己删除自己是不对的
	gnpc *pNPC = GetParent();
	MSG msg;
	BuildMessage(msg, GM_MSG_NPC_CHANGE_AI_TRIGGER, pNPC->ID, pNPC->ID, pNPC->pos, policy_id);
	gmatrix::GetInstance().SendMessage(msg);
}

void gnpc_imp::RealChangeAIPolicy(int policy_id)
{
	if (_ai_ctrl)
	{
		_ai_ctrl->ChangeAIPolicy(policy_id);
	}
}

bool gnpc_imp::IsInNightSleepTime()
{
	int game_hour = 0;
	if (!SceneGameTime::GetInstance().GetGameHour(GetSceneImp()->GetTag(), game_hour))
	{
		game_hour = GetNowInGameHour();
	}

	return (!(game_hour >= NIGHT_SLEEP_GAME_DAY_BEGIN_HOUR && game_hour < NIGHT_SLEEP_GAME_DAY_END_HOUR));
}

void gnpc_imp::BeginNightSleep()
{
	if (!_template)
	{
		return;
	}

	if (!_template->can_night_sleep)
	{
		return;
	}

	if (_parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP))
	{
		return;
	}

	if (_parent->IsZombie())
	{
		return;
	}

	if (_ai_ctrl)
	{
		_ai_ctrl->ClearMission();
		ClearAction();
		_ai_ctrl->EventNPCBeginNightSleep();
	}
	_parent->SetObjectState(gobject::STATE_NIGHT_SLEEP);
	Runner()->update_object_state();
	__PRINTF("gnpc_imp::BeginNightSleep:npc_id=%ld\n", _parent->ID.id);
}

void gnpc_imp::EndNightSleep(int end_reason, ruid_t role_id)
{
	if (!_parent->CheckObjectState(gobject::STATE_NIGHT_SLEEP))
	{
		return;
	}

	if (_parent->IsZombie())
	{
		return;
	}

	if (_ai_ctrl)
	{
		_ai_ctrl->EventNPCEndNightSleep(end_reason, role_id);
	}
	_parent->ClrObjectState(gobject::STATE_NIGHT_SLEEP);
	Runner()->update_object_state();
	__PRINTF("gnpc_imp::EndNightSleep:npc_id=%ld, end_reason=%d\n", _parent->ID.id, end_reason);
}

void gnpc_imp::SyncDamagePlayerMapByNpcTid()
{
	if (GetParent()->combat_state && _template->sync_damage_player_to_instance)
	{
		DAMAGE_PLAYER_MAP damage_player_map;
		if (_dm_map.GetDamagePlayerMap(damage_player_map))
		{
			GetWorldImp()->SyncDamagePlayerMapByNpcTid(_template->tid, damage_player_map);
		}
	}
}

void gnpc_imp::SyncPlayerDamageToSceneByNpcTid()
{
	if (_template->player_damage_list_size > 0)
	{
		std::map<ruid_t, uint64_t> player_damage_map;
		if (_dm_map.GetPlayerDamageMap(player_damage_map))
		{
			GetSceneImp()->SyncPlayerDamageToSceneByNpcTid(_template->tid, player_damage_map);
		}
	}
}

void gnpc_imp::SyncCorpsDamageToSceneByNpcTid()
{
	if (_template->corps_damage_list_size > 0)
	{
		std::map<int64_t, uint64_t> corps_damage_map;
		if (_dm_map.GetCorpsDamageMap(corps_damage_map))
		{
			GetSceneImp()->SyncCorpsDamageToSceneByNpcTid(_template->tid, corps_damage_map);
		}
	}
}

void gnpc_imp::DamagePlayerRegionSend()
{
	if (_template->player_damage_region_send && _template->player_damage_list_size > 0)
	{
		DAMAGE_PLAYER_MAP damage_player_map;
		if (_dm_map.GetDamagePlayerMap(damage_player_map))
		{
			PB::gp_damage_rank_info proto;
			proto.set_dt(PB::DRI_TYPE_PLAYER);
			proto.set_npc_tid(_template->tid);
			int rank = 1;
			for (auto it = damage_player_map.begin(); it != damage_player_map.end(); ++it)
			{
				PB::gp_damage_rank_info::damage_rank_single_info *drsi = proto.add_drsi();
				drsi->set_rank(rank++);
				drsi->set_key(it->second.role_id);
				drsi->set_value(it->first);
				drsi->set_name(it->second.name, it->second.name_len);
				if (rank > CORPS_BOSS_SCORE_DAMAGE_PLAYER_INFO_CLIENT_SIZE)
				{
					break;
				}
			}
			_runner->RegionSend<S2C::CMD::PBS2C>(proto);
		}
	}
}

void gnpc_imp::DamageCorpsRegionSend()
{
	if (_template->corps_damage_region_send && _template->corps_damage_list_size > 0)
	{
		DAMAGE_CORPS_MAP damage_corps_map;
		if (_dm_map.GetDamageCorpsMap(damage_corps_map))
		{
			PB::gp_damage_rank_info proto;
			proto.set_dt(PB::DRI_TYPE_CORPS);
			proto.set_npc_tid(_template->tid);
			int rank = 1;
			for (auto it = damage_corps_map.begin(); it != damage_corps_map.end(); ++it)
			{
				PB::gp_damage_rank_info::damage_rank_single_info *drsi = proto.add_drsi();
				drsi->set_rank(rank++);
				drsi->set_key(it->second);
				drsi->set_value(it->first);
				if (rank > CORPS_BOSS_SCORE_DAMAGE_CORPS_INFO_CLIENT_SIZE)
				{
					break;
				}
			}
			_runner->RegionSend<S2C::CMD::PBS2C>(proto);
		}
	}
}

void gnpc_imp::InitMasterData()
{
	_master_data = new npc_master_data();
}

#define GET_MASTER_PROP_FUC(func_name, prop_name) \
int gnpc_imp::GetMaster##func_name() const\
{\
	if (!_master_data)\
	{\
		return 0;\
	}\
	return _master_data->_skill_prop.prop_name;\
}
GET_MASTER_PROP_FUC(CurPhyAtk, curPhyAtk)
GET_MASTER_PROP_FUC(CurMagAtk, curMagAtk)
GET_MASTER_PROP_FUC(CurPsychokinesisLevel, curPsychokinesisLevel)
GET_MASTER_PROP_FUC(CurCDReduLevel, curCDReduLevel)
#undef GET_MASTER_PROP_FUC

int gnpc_imp::GetMasterSkillLevel(int id) const
{
	if (!_master_data)
	{
		return 0;
	}
	auto iter = _master_data->_cur_skill_map.find(id);
	if (iter == _master_data->_cur_skill_map.end())
	{
		return 0;
	}
	return iter->second;
}

int gnpc_imp::GetMasterTalentLevel(int talent_id, int skill_id) const
{
	if (!_master_data)
	{
		return 0;
	}
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeTalent))
	{
		return 0;
	}
	auto iter = _master_data->_cur_talent.find(talent_id);
	if (iter == _master_data->_cur_talent.end())
	{
		return 0;
	}
	bool ret = player_talent_config::GetInstance().CanTalentEffectSkill(talent_id, skill_id);
	return (int)ret;
}

int gnpc_imp::GetMasterPurebloodTalentLevel(int talent_id, int skill_id) const
{
	if (!_master_data)
	{
		return 0;
	}
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodePureBlooded))
	{
		return 0;
	}
	auto iter = _master_data->_cur_blood_talent.find(talent_id);
	if (iter == _master_data->_cur_blood_talent.end())
	{
		return 0;
	}
	auto *pTalent = PureBloodedConfigManager::GetInstance().GetTalentConfig(talent_id);
	if (!pTalent)
	{
		pTalent = PureBloodedConfigManager::GetInstance().GetSpecialTalentConfig(talent_id);
	}
	if (!pTalent)
	{
		return 0;
	}
	if (pTalent->_skills.find(skill_id) == pTalent->_skills.end())
	{
		return 0;
	}
	return iter->second;
}

int gnpc_imp::GetMasterKotodamaStage(int index) const
{
	if (!_master_data)
	{
		return 0;
	}
	auto iter = _master_data->_cur_kotodama.find(index);
	if (iter == _master_data->_cur_kotodama.end())
	{
		return 0;
	}
	return iter->second;
}

void gnpc_imp::OnSkillCast(int skill_id, const XID& target)
{
	bool can_attack = CheckCanAttack(target);
	OnMasterSkillCast(skill_id, can_attack);
}

void gnpc_imp::UpdateExtentInfo()
{
	// 通知技能出来的召唤npc阵营等信息
	SyncSummonNpcMasterInfo();
}

void gnpc_imp::FillNpcMasterData(npc_master_data *p_master_data)
{
	if (!p_master_data)
	{
		return;
	}
	_prop.ExportTo(&(p_master_data->_skill_prop));
	p_master_data->_master_dmg_add = CalcMasterDmgAdd();
	FormPetMasterData(p_master_data->_master_prop);
}

