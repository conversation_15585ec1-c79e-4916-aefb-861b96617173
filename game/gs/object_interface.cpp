
#include "class_table.h"
#include "gprotoc/player_mech.pb.h"
#include "gprotoc/ipt_guard_breed_notify.pb.h"
#include "gprotoc/instance_info_t.pb.h"
#include "gprotoc/gp_corps_auction_withdraw.pb.h"
#include "gprotoc/player_twin.pb.h"
#include "gprotoc/ipt_reply_couple_tour.pb.h"
#include "gprotoc/gp_player_draw_redenvelope_result.pb.h"
#include "gprotoc/gp_player_send_redenvelope.pb.h"
#include "gprotoc/repu_info.pb.h"
#include "gprotoc/gp_player_draw_redenvelope.pb.h"
#include "gprotoc/gp_player_send_redenvelope_result.pb.h"
#include "gprotoc/db_red_envelope.pb.h"
#include "gprotoc/ipt_line_register_info.pb.h"
#include "gprotoc/player_replisome.pb.h"
#include "gprotoc/db_check_order.pb.h"
#include "gprotoc/register_info.pb.h"
#include "gprotoc/item_data.pb.h"
#include "gprotoc/buff_t.pb.h"

#include "item_manager.h"
#include "subobject.h"
#include "mine_matter.h"
#include "player.h"
#include "spawner/npc_spawner.h"
#include "skill_config_man.h"
#include "trigger.h"
#include "protoc_factory.h"
#include "idip_data_manager.h"
#include "chess_imp.h"

object_interface::object_interface() : _imp(NULL)
{
}

object_interface::object_interface(gcreature_imp *imp) : _imp(imp)
{
}

object_interface::object_interface(const object_interface& rhs) : _imp(rhs._imp)
{
}

void object_interface::NotifyPlayerMovePos()
{
	return ((gplayer_imp *)_imp)->NotifyPlayerMovePos();
}

object_interface& object_interface::operator=(const object_interface& rhs)
{
	_imp = rhs._imp;
	return *this;
}

void object_interface::Init(gcreature_imp *imp)
{
	_imp = imp;
}

object_interface::~object_interface()
{
	if (!_imp)
	{
		return;
	}
}

gcreature_imp *object_interface::GetImp()
{
	return _imp;
}

gcreature_imp *object_interface::GetImp() const
{
	return _imp;
}

bool object_interface::IsPlayerClass() const
{
	return _imp->IsPlayerClass();
}

bool object_interface::IsNPCClass() const
{
	return _imp->IsNPCClass();
}

bool object_interface::IsPetClass() const
{
	return _imp->IsPetClass();
}

bool object_interface::IsPlayerNpc() const
{
	return _imp->GetParent()->CheckObjectState(gobject::STATE_NPC_PLAYER);
}

const creature_prop& object_interface::GetProperty() const
{
	return _imp->GetProperty();
}

WMSKILL::SkillWrapper& object_interface::GetSkillWrapper() const
{
	return _imp->_skill;
}

int object_interface::GetCurSkillId() const
{
	return _imp->GetParent()->skillid;
}

const XID& object_interface::GetID() const
{
	return _imp->Parent()->ID;
}

bool object_interface::IsDead() const
{
	return _imp->Parent()->IsZombie();
}

bool object_interface::IsInTeam() const
{
	return _imp->IsInTeam();
}

ruid_t object_interface::GetTeamIDID() const
{
	return _imp->GetTeamIDID();
}

bool object_interface::IsTeamLeader() const
{
	return _imp->IsTeamLeader();
}

bool object_interface::ObjectIsTeamMember(const XID& object) const
{
	return _imp->IsTeamMember(object);
}

bool object_interface::IsTeamMemberNear(float range) const
{
	return _imp->IsTeamMemberNear(range);
}

unsigned int object_interface::GetFaction() const
{
	return _imp->GetFaction();
}

unsigned int object_interface::GetEnemyFaction() const
{
	return _imp->GetEnemyFaction();
}

unsigned short object_interface::GetWorldTag() const
{
	return _imp->Parent()->world_tid;
}

unsigned short object_interface::GetSceneTag() const
{
	return _imp->Parent()->scene_tag;
}

const A3DVECTOR3& object_interface::GetPos() const
{
	return _imp->Parent()->pos;
}

const A3DVECTOR3 object_interface::GetClientPos() const
{
	return _imp->GetClientPos();
}

const A3DVECTOR3& object_interface::GetGhostPos() const
{
	if (IsPlayerClass())
	{
		gplayer_imp *pImp = dynamic_cast<gplayer_imp *>(_imp);
		if (pImp != nullptr)
		{
			return pImp->GetParent()->GetGhostPos();
		}
	}
	return _imp->Parent()->pos;
}

const A3DVECTOR3& object_interface::GetDirection() const
{
	return _imp->Parent()->GetDirection();
}

unsigned short object_interface::GetDir() const
{
	return _imp->Parent()->dir;
}

void object_interface::SetDirection(const A3DVECTOR3& d, bool inform)
{
	_imp->Parent()->SetDirection(d);
	if (inform)
	{
		_imp->Runner()->object_turn(GP_TURN_STOP, _imp->Parent()->dir);
	}
}

float object_interface::GetTerrainHeight(float x, float z) const
{
	if (!_imp->GetSceneImp())
	{
		return 0.0f;
	}
	return _imp->GetSceneImp()->GetTerrainHeightAt(x, z);
}
int object_interface::InformIDIPAppointCorpsMaster()
{
	return _imp->InformIDIPAppointCorpsMaster();
}
int object_interface::InformIDIPDeposeCorpsMaster()
{
	return _imp->InformIDIPDeposeCorpsMaster();
}
float object_interface::GetBodySize() const
{
	return _imp->Parent()->body_size;
}

bool object_interface::CheckGMPrivilege() const
{
	return _imp->CheckGMPrivilege();
}

unsigned char object_interface::GetGender() const
{
	return _imp->GetGender();
}

unsigned char object_interface::GetLevel() const
{
	return _imp->GetLevel();
}

const GNET::user_account *object_interface::GetAccount() const
{
	if (IsPlayerClass())
	{
		return &(((gplayer_imp *)_imp)->GetAccount());
	}
	return NULL;
}

unsigned char object_interface::GetProf() const
{
	return _imp->GetProf();
}
int object_interface::GetVipLevel() const
{
	if (IsPlayerClass())
	{
		return ((gplayer_imp *)_imp)->GetVIPInfo().GetVIPLevel();
	}
	return 0;
}

unsigned char object_interface::GetTmpProf() const
{
	if (IsPlayerClass())
	{
		return ((gplayer_imp *)_imp)->GetTmpProf();
	}
	return _imp->GetProf();
}

unsigned char object_interface::GetProfLevel() const
{
	return _imp->GetProfLevel();
}

const XID& object_interface::GetCurTarget() const
{
	return _imp->GetCurTarget();
}

void object_interface::SetOverwhelmingMode(bool flag)
{
	_imp->SetOverwhelmingMode(flag);
}

void object_interface::FillAttackMsg(const XID& target, attack_msg& attack) const
{
	_imp->FillAttackMsg(target, attack);
}

void object_interface::PrintCoolDown(unsigned short cd_id) const
{
	_imp->GetCooldownMan().Print(cd_id);
}

bool object_interface::TestCoolDown(unsigned short cd_id) const
{
	return _imp->TestCoolDown(cd_id);
}

int object_interface::TestCoolDownCount(unsigned short cd_id) const
{
	return _imp->TestCoolDownCount(cd_id);
}
void  object_interface::SetCoolDownByEffectimp(unsigned cd_id, size_t cd_time)
{
	int min_cd_id_time = SkillConfig::GetInstance().GetMinCDTimeWithID(cd_id);
	if (!_imp->GetNoCDRedu()  || min_cd_id_time == -2 || min_cd_id_time > 0)
	{
		int cd_redu = _imp->GetCDRedu();
		if (cd_redu > 0 )
		{
			cd_time -= cd_time * cd_redu / 1000;
		}
		if (cd_time <= 0 && min_cd_id_time > 0)
		{
			cd_time = min_cd_id_time;
		}
		__PRINTF("CDRedu set rid=%ld,redu=%d,cd=%d,tm=%ld\n",
		         _imp->Parent()->ID.id, cd_redu, cd_id, cd_time);
	}
	_imp->SetCoolDown(cd_id, cd_time);
}
void object_interface::SetCoolDown(unsigned short cd_id, size_t cd_time, bool by_skill_cast, int skill_id, int cd_count)
{
	int min_cd_id_time = SkillConfig::GetInstance().GetMinCDTimeWithID(cd_id);
	if ((!_imp->GetNoCDRedu()  || min_cd_id_time == -2 || min_cd_id_time > 0)
	        && (gmatrix::GetInstance().IsSkillReduByProf(_imp->GetProf(), skill_id) || (by_skill_cast && gmatrix::GetInstance().IsOnlyCastReduCd(_imp->GetProf(), cd_id))))
	{
		int cd_redu = _imp->GetCDRedu();
		if (cd_redu > 0)
		{
			cd_time -= cd_time * cd_redu / 1000;
		}
		if (cd_time <= 0 && min_cd_id_time > 0)
		{
			cd_time = min_cd_id_time;
		}
		__PRINTF("CDRedu set rid=%ld,redu=%d,cd=%d,tm=%ld\n",
		         _imp->Parent()->ID.id, cd_redu, cd_id, cd_time);
	}
	_imp->SetCoolDown(cd_id, cd_time);
}

void object_interface::ClrCoolDown(unsigned short cd_id)
{
	_imp->ClrCoolDown(cd_id);
}

void object_interface::CostCoolDown(unsigned short cd_id, size_t cost_time)
{
	_imp->CostCoolDown(cd_id, cost_time);
}

void object_interface::AddCoolDown(unsigned short cd_id, size_t cost_time)
{
	_imp->AddCoolDown(cd_id, cost_time);
}

void object_interface::ClrActiveSkillCoolDown()
{
	_imp->ClrActiveSkillCoolDown();
}

void object_interface::ClrManualSkillCoolDown()
{
	_imp->ClrManualSkillCoolDown();
}

void object_interface::ScaleSkillCoolDown(float rate)
{
	_imp->ScaleSkillCoolDown(rate);
}

void object_interface::ScaleManualSkillCoolDown(float rate, int min_cost, int except_skill_id)
{
	_imp->ScaleManualSkillCoolDown(rate, min_cost, except_skill_id);
}

void object_interface::PauseManualSkillCoolDown(int ms)
{
	_imp->PauseManualSkillCoolDown(ms);
}

void object_interface::StopPauseManualSkillCoolDown()
{
	_imp->StopPauseManualSkillCoolDown();
}

void object_interface::ActiveUpgradeSkillCoolDown()
{
	_imp->ActiveUpgradeSkillCoolDown();
}

void object_interface::SetDelayBoomProess(int process)
{
	_imp->SetDelayBoomProess(process);
}

void object_interface::SetDelayAttackProess(int process)
{
	_imp->SetDelayAttackProess(process);
}

void object_interface::SetHP(int64_t hp)
{
	_imp->SetHP(hp);
}

int64_t object_interface::GetHPMax() const
{
	return _imp->GetHPMax();
}

int object_interface::GetMPMax() const
{
	return _imp->GetMPMax();
}

int object_interface::GetMP2Max() const
{
	return _imp->GetMP2Max();
}

int object_interface::GetMP3Max() const
{
	return _imp->GetMP3Max();
}

int object_interface::GetMP4() const
{
	return _imp->GetMP4();
}
void object_interface::SetMP4(int mp)
{
	_imp->SetMP4(mp);
}
void object_interface::IncMP4(int mp)
{
	UpdateMP4();
	_imp->IncMP4(mp);
}
bool object_interface::DecMP4(int mp)
{
	UpdateMP4();
	return _imp->DecMP4(mp);
}
int object_interface::GetMP4Max() const
{
	return _imp->GetMP4Max();
}
void object_interface::UpdateMP4()
{
	if (_imp->GetProf6P())
	{
		_imp->GetProf6P()->UpdateMP4();
	}
	else if (IsFaker())
	{
		_imp->SetMP4(500);//假人总是有能量
	}
}

float object_interface::AbsIncHP(int64_t hp, bool notify_client)
{
	return _imp->AbsIncHP(hp);
}

float object_interface::IncHP(int64_t hp, bool notify_client)
{
	return _imp->IncHP(hp);
}

void object_interface::DecHP(int64_t hp)
{
	//$$$$$$ 这里还有地方使用么？ 由于hp存在最大上限和最大回复上限，这个逻辑是否也受到影响?
	_imp->DecHP(hp);
}

void object_interface::DecSP(float delta)
{
	return _imp->DecSP(delta);
}

void object_interface::SetMP(int mp)
{
	_imp->SetMP(mp);
}
void object_interface::IncMP(int mp)
{
	_imp->IncMP(mp);
}
bool object_interface::DecMP(int mp)
{
	return _imp->DecMP(mp);
}

bool object_interface::UseMP(int mp)
{
	_imp->GetFilterMan().EF_UseMP(mp);
	return _imp->DecMP(mp);
}

void object_interface::SetMP2(int mp)
{
	_imp->SetMP2(mp);
}
void object_interface::IncMP2(int mp)
{
	_imp->IncMP2(mp);
}
bool object_interface::DecMP2(int mp)
{
	return _imp->DecMP2(mp);
}

void object_interface::SetMP3(int mp)
{
	_imp->SetMP3(mp);
}
void object_interface::IncMP3(int mp)
{
	_imp->IncMP3(mp);
}
bool object_interface::DecMP3(int mp)
{
	return _imp->DecMP3(mp);
}

bool object_interface::IsMPUnlimit()
{
	return _imp->GetSceneImp()->IsMPNoLimit();
}

int64_t object_interface::GetHP() const
{
	return _imp->GetHP();
}

int  object_interface::GetMP() const
{
	return _imp->GetMP();
}

int  object_interface::GetMP2() const
{
	return _imp->GetMP2();
}

int  object_interface::GetMP3() const
{
	return _imp->GetMP3();
}

int  object_interface::GetXP() const
{
	return 0.f;
}

void object_interface::SetXP(int xp)
{
}

void object_interface::IncXP(int xp)
{
	_imp->IncXP(xp);
}

void object_interface::DecXP(int xp)
{
	_imp->DecXP(xp);
}

float object_interface::IncVP(VIGOR_TYPE vp_type, float vp)
{
	return 0.f;
}

float object_interface::DecVP(VIGOR_TYPE vp_type, float vp)
{
	return 0.f;
}
void object_interface::SendDizzyMsg()
{
	MSG msg;
	gobject *pObj = _imp->Parent();
	BuildMessage(msg, GM_MSG_DIZZY_SELF, pObj->ID, pObj->ID, pObj->pos);
	{
		gmatrix::GetInstance().SendMessage(msg);
	}
}

void object_interface::Enchant(const XID& target, attack_msg& attack, int millisec_delay)
{
	int delay_tick = MILLISEC_TO_TICK(millisec_delay);
	gobject *pObj = _imp->Parent();
	_imp->TranslateAttack(target, attack);
	MSG msg;
	BuildMessage(msg, GM_MSG_ENCHANT, target, pObj->ID, pObj->pos, 0, &attack, sizeof(attack));

	if (_imp->GetSceneImp()->CanUseGroupMsg())
	{
		gnpc *p_target = gmatrix::GetInstance().GetNPCByXID(target.id);
		if (p_target && p_target->pGrouping && p_target->pGrouping == pObj->pGrouping)
		{
			if (p_target->pGrouping->SendMessage(msg, delay_tick))
			{
				return;
			}
		}
	}

	if (delay_tick <= 0 || delay_tick >= MAX_MESSAGE_LATENCY_TICK)
	{
		gmatrix::GetInstance().SendMessage(msg);
	}
	else
	{
		gmatrix::GetInstance().SendMessage(msg, delay_tick);
	}
}

void object_interface::AchivementAuctionOpen()
{
	gplayer_imp *pImp = (gplayer_imp *)_imp;
	pImp->GetAchievement().OnAuctionOpen(pImp);
}

void object_interface::ChildDivorceForce(uint64_t guid, uint64_t spouse_guid)
{
	_imp->ChildDivorceForce(guid, spouse_guid);
}

void object_interface::EnchantZombie(const XID& target, attack_msg& attack)
{
	gobject *pObj = _imp->Parent();
	_imp->TranslateAttack(target, attack);
	MSG msg;
	BuildMessage(msg, GM_MSG_ENCHANT_ZOMBIE, target, pObj->ID, pObj->pos, 0, &attack, sizeof(attack));
	gmatrix::GetInstance().SendMessage(msg);
}

bool object_interface::AT_LUANZHAN()
{
	if (!_imp->GetSceneImp())
	{
		return false;
	}
	gscene_imp *pSceneImp = _imp->GetRealSceneImp();
	if (!pSceneImp)
	{
		return false;
	}
	int pk_type = pSceneImp->GetPKType();
	if (pk_type == PT_DEAD)
	{
		return true;
	}
	return false;
}
void object_interface::RegionEnchant1(std::vector<int>& target_list, const A3DVECTOR3& pos, float radius, attack_msg& attack, size_t max_count, std::vector<XID>& idlist, const XID *spec_target, bool damage_delay, int millisec_delay, bool rand_select, ruid_t team_id, const std::set<ruid_t>& exclude)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	//球形
	_imp->TranslateAttack(XID(), attack);
	gobject *pObj = _imp->Parent();

	sphere_harmful_behavior_msg_param_t shbmp(_imp);
	shbmp.rand_select = rand_select;
	shbmp.team_id = team_id;
	shbmp.object = pObj->ID;
	shbmp.pos = pos;//target pos
	shbmp.max_count = max_count;
	shbmp.attack = attack;
	if (spec_target)
	{
		shbmp.spec_target = *spec_target;
	}
	shbmp.radius = radius;
	shbmp.radius += 1;
	shbmp.only_npc = attack.only_npc;

	MSG msg;
	BuildMessage(msg, GM_MSG_ENCHANT, XID(GM_TYPE_SELECTABLE), pObj->ID, pObj->pos, 0, &attack, sizeof(attack));

	int delay_tick = MILLISEC_TO_TICK(millisec_delay);
	if (delay_tick <= 0 || delay_tick >= MAX_MESSAGE_LATENCY_TICK)
	{
		delay_tick = 0;
	}

	if (!delay_tick || damage_delay)
	{
		IDList _idlist;
		_imp->GetSceneImp()->BroadcastSphereMessage(target_list, msg, shbmp, _idlist, delay_tick, exclude);
		idlist.insert(idlist.begin(), _idlist.begin(), _idlist.end());
	}
	else
	{
		//此消息要延迟 组织延迟消息
		MSG *pMsg  = NULL;
		size_t size = sizeof(MSG) + sizeof(MSG) + msg.content_length + sizeof(sphere_harmful_behavior_msg_param_t);
		pMsg = (MSG *)TLA_ALLOC(size);
		memset(pMsg, 0, sizeof(MSG));
		char *content = ((char *)pMsg) + sizeof(MSG);
		pMsg->content = content;
		memcpy(content, &msg, sizeof(MSG));
		content += sizeof(MSG);
		if (msg.content_length)
		{
			memcpy(content, msg.content, msg.content_length);
		}
		content += msg.content_length;
		memcpy(content, &shbmp, sizeof(sphere_harmful_behavior_msg_param_t));
		pMsg->message = GM_MSG_DELAY_SPHERE_MESSAGE;
		pMsg->target = _imp->GetSceneImp()->Parent()->xid;
		pMsg->source.Clear();
		pMsg->pos = pos;
		pMsg->content_length = sizeof(MSG) + msg.content_length + sizeof(sphere_harmful_behavior_msg_param_t);
		gmatrix::GetInstance().SendMessage(*pMsg, delay_tick);
		TLA_DEALLOC(pMsg, size);
	}
}

void object_interface::RegionEnchant2(std::vector<int>& target_list, const A3DVECTOR3& pos, const A3DVECTOR3& direction, float radius, float length, attack_msg& attack, size_t max_count, std::vector<XID>& idlist, const XID *spec_target, bool damage_delay, int millisec_delay, ruid_t team_id, const std::set<ruid_t>& exclude)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	_imp->TranslateAttack(XID(), attack);
	//圆柱
	//生成消息
	gobject *pObj = _imp->Parent();
	MSG msg;
	BuildMessage(msg, GM_MSG_ENCHANT, XID(GM_TYPE_SELECTABLE), pObj->ID, pObj->pos, 0, &attack, sizeof(attack));

	cylinder_harmful_behavior_msg_param_t chbmp(_imp);
	chbmp.team_id = team_id;
	chbmp.object = pObj->ID;
	chbmp.pos = pos;
	chbmp.max_count = max_count;
	chbmp.attack = attack;
	if (spec_target)
	{
		chbmp.spec_target = *spec_target;
	}
	chbmp.radius = radius;
	if (!target_list.empty())
	{
		chbmp.radius += 1;
	}
	chbmp.length = length;
	chbmp.direction = direction;
	chbmp.only_npc = attack.only_npc;
	if (chbmp.direction.y != 0)
	{
		chbmp.direction.y = 0; //只水平判定
		chbmp.direction.Normalize();
	}

	int delay_tick = MILLISEC_TO_TICK(millisec_delay);
	if (delay_tick <= 0 || delay_tick >= MAX_MESSAGE_LATENCY_TICK)
	{
		delay_tick = 0;
	}

	if (!delay_tick || damage_delay)
	{
		IDList _idlist;
		_imp->GetSceneImp()->BroadcastCylinderMessage(target_list, msg, chbmp, _idlist, delay_tick, exclude);
		idlist.insert(idlist.begin(), _idlist.begin(), _idlist.end());
	}
	else
	{
		MSG *pMsg  = NULL;
		size_t size = sizeof(MSG) + sizeof(MSG) + msg.content_length + sizeof(cylinder_harmful_behavior_msg_param_t);
		pMsg = (MSG *)TLA_ALLOC(size);
		memset(pMsg, 0, sizeof(MSG));
		char *content = ((char *)pMsg) + sizeof(MSG);
		pMsg->content = content;
		memcpy(content, &msg, sizeof(MSG));
		content += sizeof(MSG);
		if (msg.content_length)
		{
			memcpy(content, msg.content, msg.content_length);
		}
		content += msg.content_length;
		memcpy(content, &chbmp, sizeof(cylinder_harmful_behavior_msg_param_t));
		pMsg->message = GM_MSG_DELAY_CYLINDER_MESSAGE;
		pMsg->target = _imp->GetSceneImp()->Parent()->xid;
		pMsg->source.Clear();
		pMsg->pos = pos;
		pMsg->content_length = sizeof(MSG) + msg.content_length + sizeof(cylinder_harmful_behavior_msg_param_t);
		gmatrix::GetInstance().SendMessage(*pMsg, delay_tick);
		TLA_DEALLOC(pMsg, size);
	}
}

void object_interface::RegionEnchant3(std::vector<int>& target_list, const A3DVECTOR3& pos, const A3DVECTOR3& direction, float cos_half_angle, float radius, attack_msg& attack, size_t max_count, std::vector<XID>& idlist, const XID *spec_target, bool damage_delay, int millisec_delay, ruid_t team_id, const std::set<ruid_t>& exclude)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	//锥
	_imp->TranslateAttack(XID(), attack);
	gobject *pObj = _imp->Parent();
	MSG msg;
	BuildMessage(msg, GM_MSG_ENCHANT, XID(GM_TYPE_SELECTABLE), pObj->ID, pObj->pos, 0, &attack, sizeof(attack));

	taper_harmful_behavior_msg_param_t thbmp(_imp);
	thbmp.team_id = team_id;
	thbmp.object = pObj->ID;
	thbmp.pos = pos;
	thbmp.max_count = max_count;
	thbmp.attack = attack;
	if (spec_target)
	{
		thbmp.spec_target = *spec_target;
	}
	thbmp.radius = radius;
	if (!target_list.empty())
	{
		thbmp.radius += 1;
	}

	thbmp.cos_half_angle = cos_half_angle;
	thbmp.direction = direction;
	thbmp.only_npc = attack.only_npc;
	if (thbmp.direction.y != 0)
	{
		thbmp.direction.y = 0; //只水平判定
		thbmp.direction.Normalize();
	}

	int delay_tick = MILLISEC_TO_TICK(millisec_delay);
	if (delay_tick <= 0 || delay_tick >= MAX_MESSAGE_LATENCY_TICK)
	{
		delay_tick = 0;
	}

	if (!delay_tick || damage_delay)
	{
		IDList _idlist;
		_imp->GetSceneImp()->BroadcastTaperMessage(target_list, msg, thbmp, _idlist, delay_tick, exclude);
		idlist.insert(idlist.begin(), _idlist.begin(), _idlist.end());
	}
	else
	{
		MSG *pMsg  = NULL;
		size_t size = sizeof(MSG) + sizeof(MSG) + msg.content_length + sizeof(taper_harmful_behavior_msg_param_t);
		pMsg = (MSG *)TLA_ALLOC(size);
		memset(pMsg, 0, sizeof(MSG));
		char *content = ((char *)pMsg) + sizeof(MSG);
		pMsg->content = content;
		memcpy(content, &msg, sizeof(MSG));
		content += sizeof(MSG);
		if (msg.content_length)
		{
			memcpy(content, msg.content, msg.content_length);
		}
		content += msg.content_length;
		memcpy(content, &thbmp, sizeof(taper_harmful_behavior_msg_param_t));
		pMsg->message = GM_MSG_DELAY_TAPER_MESSAGE;
		pMsg->target = _imp->GetSceneImp()->Parent()->xid;
		pMsg->source.Clear();
		pMsg->pos = pos;
		pMsg->content_length = sizeof(MSG) + msg.content_length + sizeof(taper_harmful_behavior_msg_param_t);
		gmatrix::GetInstance().SendMessage(*pMsg, delay_tick);
		TLA_DEALLOC(pMsg, size);
	}
}

void object_interface::RegionEnchant4(std::vector<int>& target_list, const A3DVECTOR3& pos, float outside_radius, float inside_radius, attack_msg& attack, size_t max_count, std::vector<XID>& idlist, const XID *spec_target, bool damage_delay, int millisec_delay, bool rand_select, ruid_t team_id, const std::set<ruid_t>& exclude)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	//环形
	_imp->TranslateAttack(XID(), attack);
	gobject *pObj = _imp->Parent();
	MSG msg;
	BuildMessage(msg, GM_MSG_ENCHANT, XID(GM_TYPE_SELECTABLE), pObj->ID, pObj->pos, 0, &attack, sizeof(attack));

	ring_harmful_behavior_msg_param_t shbmp(_imp);
	shbmp.team_id = team_id;
	shbmp.rand_select = rand_select;
	shbmp.object = pObj->ID;
	shbmp.pos = pos;//target pos
	shbmp.max_count = max_count;
	shbmp.attack = attack;
	if (spec_target)
	{
		shbmp.spec_target = *spec_target;
	}
	shbmp.outside_radius = outside_radius;
	shbmp.outside_radius += 1;
	shbmp.inside_radius = inside_radius;
	shbmp.inside_radius -= 1;
	if (shbmp.inside_radius < -1e-5)
	{
		shbmp.inside_radius = 0.f;
	}
	shbmp.only_npc = attack.only_npc;

	int delay_tick = MILLISEC_TO_TICK(millisec_delay);
	if (delay_tick <= 0 || delay_tick >= MAX_MESSAGE_LATENCY_TICK)
	{
		delay_tick = 0;
	}

	if (!delay_tick || damage_delay)
	{
		IDList _idlist;
		_imp->GetSceneImp()->BroadcastRingMessage(target_list, msg, shbmp, _idlist, delay_tick, exclude);
		idlist.insert(idlist.begin(), _idlist.begin(), _idlist.end());
	}
	else
	{
		//此消息要延迟 组织延迟消息
		MSG *pMsg  = NULL;
		size_t size = sizeof(MSG) + sizeof(MSG) + msg.content_length + sizeof(sphere_harmful_behavior_msg_param_t);
		pMsg = (MSG *)TLA_ALLOC(size);
		memset(pMsg, 0, sizeof(MSG));
		char *content = ((char *)pMsg) + sizeof(MSG);
		pMsg->content = content;
		memcpy(content, &msg, sizeof(MSG));
		content += sizeof(MSG);
		if (msg.content_length)
		{
			memcpy(content, msg.content, msg.content_length);
		}
		content += msg.content_length;
		memcpy(content, &shbmp, sizeof(sphere_harmful_behavior_msg_param_t));
		pMsg->message = GM_MSG_DELAY_SPHERE_MESSAGE;
		pMsg->target = _imp->GetSceneImp()->Parent()->xid;
		pMsg->source.Clear();
		pMsg->pos = pos;
		pMsg->content_length = sizeof(MSG) + msg.content_length + sizeof(sphere_harmful_behavior_msg_param_t);
		gmatrix::GetInstance().SendMessage(*pMsg, delay_tick);
		TLA_DEALLOC(pMsg, size);
	}
}

void object_interface::RegionEnchant5(const A3DVECTOR3& pos, float radius, attack_msg& attack, size_t max_count, std::vector<XID>& idlist, const XID& spec_target, int millisec_delay)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	_imp->TranslateAttack(XID(), attack);
	gobject *pObj = _imp->Parent();
	sphere_harmful_behavior_msg_param_t shbmp(_imp);
	shbmp.rand_select = false;
	shbmp.object = pObj->ID;
	shbmp.pos = pos;//target pos
	shbmp.max_count = max_count;
	shbmp.attack = attack;
	shbmp.spec_target = spec_target;
	shbmp.radius = radius + 1;
	shbmp.only_npc = attack.only_npc;
	IDList _idlist;
	_imp->GetSceneImp()->SendSphereMessageSequence(attack, shbmp, _idlist, MILLISEC_TO_TICK(millisec_delay));
	idlist.insert(idlist.begin(), _idlist.begin(), _idlist.end());
}

void object_interface::TeamEnchant(attack_msg& attack, float range, bool exclude_self, bool norangelimit)
{
	gobject *pObj = _imp->Parent();
	MSG msg;
	BuildMessage(msg, GM_MSG_ENCHANT, XID(), pObj->ID, pObj->pos, 0, &attack, sizeof(attack));
	_imp->TranslateAttack(XID(), attack);
	_imp->SendTeamMsg(msg, range, exclude_self, norangelimit);
}

void object_interface::BeHurt(const XID& who, const attacker_info_t& info, float damage, int skill_id, bool ignore_invincible, bool ignore_prop_dam3)
{
	_imp->BeHurt(who, info, damage, skill_id, ignore_invincible, ignore_prop_dam3);
}

float object_interface::DoDamage(const XID& who, const attack_msg& msg, float damage, unsigned int flags, const A3DVECTOR3& pos, char prev_control_type, char control_type, int control_time)
{
	return _imp->BeAttacked(who, msg, damage, flags, pos, prev_control_type, control_type, control_time);
}

//技能过程的治疗
float object_interface::DoTreat(const XID& who, const attack_msg& msg, float value, unsigned int flags, const A3DVECTOR3& pos, char prev_control_type, char control_type, int control_time)
{
	_imp->GetFilterMan().EF_Treat(value);
	if (value <= 1e-6)
	{
		return 0.f;
	}
	return _imp->BeTreated(who, msg, value, flags, pos, prev_control_type, control_type, control_time);
}

void object_interface::SendControlInfo(const attack_msg& msg, char prev_control_type, char control_type, int control_time)
{
	_imp->SendControlInfo(msg, prev_control_type, control_type, control_time);
}

void object_interface::DoEnchant(const XID& who, const attack_msg& msg, int arg, unsigned int flags)
{
	_imp->BeEnchanted(who, msg, arg, flags);
}

//void object_interface::SkillMove(bool redir, A3DVECTOR3& target_pos, unsigned short& direction, bool check_dir_info, bool check_path, bool check_ydiff)
//{
//	_imp->SkillMove(check_path,redir,target_pos,direction,check_dir_info,check_ydiff);
//}
//
//bool object_interface::DirectMove(bool redir,A3DVECTOR3& target_pos,unsigned short& direction,bool nocheck)
//{
//	return _imp->DirectMove(redir,target_pos,direction,nocheck);
//}

bool object_interface::SkillMove(A3DVECTOR3& target_pos, unsigned short& direction, bool redir, bool check_path, bool reverse_check, int check_y_diff)
{
	return _imp->SkillMove(target_pos, direction, redir, check_path, reverse_check, check_y_diff);
}

void object_interface::GetLastReachablePos(const A3DVECTOR3& start, const A3DVECTOR3& end, A3DVECTOR3& stop_pos, int check_y_diff, bool can_fall)
{
	_imp->GetLastReachablePos(start, end, stop_pos, check_y_diff, can_fall);
}

void object_interface::BeKnockback(unsigned short skill_id, float speed, const A3DVECTOR3& direction, unsigned short ms_time, unsigned char type)
{
	return _imp->BeKnockback(skill_id, speed, direction, ms_time, type);
}

void object_interface::SendAttackFeedBack(const XID& target, const feedback_msg& msg)
{
	_imp->SendTo(GM_MSG_ATTACK_FEED_BACK, target, _imp->GetLevel(), &msg, sizeof(msg));
}

void object_interface::SendCheckAttackFeedBack(const XID& target, const checkfeedback_msg& msg, int delay_tick)
{
	if (delay_tick <= 0 || delay_tick >= MAX_MESSAGE_LATENCY_TICK)
	{
		return;
	}
	_imp->LazySendTo(GM_MSG_CHECK_FEEDBACK, target, 0, delay_tick, &msg, sizeof(msg));
}

void object_interface::SendMoveAttacker(const XID& target, const A3DVECTOR3& pos, uint64_t tick)
{
	struct
	{
		A3DVECTOR3 pos;
		uint64_t tick;
	} d;
	d.pos = pos;
	d.tick = tick;
	_imp->SendTo(GM_MSG_MOVE_ATTACKER, target, 0, &d, sizeof(d));
}

void object_interface::ChangeLockEnemyState(unsigned char state)
{
	_imp->ChangeLockEnemyState(state);
}

unsigned char object_interface::GetLockEnemyState() const
{
	return _imp->GetLockEnemyState();
}
void object_interface::TranslateSendSubObjAttack(XID& target, attack_msg& attack)
{
	if (_imp->IsPlayerClass())
	{
		((gplayer_imp *)_imp)->TranslateSubObjAttack(target, attack);
	}

}

void object_interface::CreateSubObject(tid_t tid, const A3DVECTOR3& pos, const A3DVECTOR3& dir, float speed, const subobj_env& env, const attack_msg& attack, int life_cycle)
{
	_imp->CreateSubObject(tid, pos, dir, speed, env, attack, life_cycle);
}

void object_interface::DelayCreateSubObject(const create_subobj_delay_t& delay_args, int delay_tick)
{
	gobject *pObj = _imp->Parent();
	MSG msg;
	BuildMessage(msg, GM_MSG_CREATE_SUBOBJECT, pObj->ID, pObj->ID, pObj->pos, 0, &delay_args, sizeof(delay_args));
	gmatrix::GetInstance().SendMessage(msg, delay_tick);
}

void object_interface::CreateBuffBox(tid_t tid, const A3DVECTOR3& pos)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	create_matter_t cmt;
	cmt.tid = tid;
	cmt.pos = pos;
	_imp->GetSceneImp()->CreateMine(cmt);
}

void object_interface::ClearRemoteCtrlSubobject(int param)
{
	_imp->ClearRemoteCtrlSubobject(param);
}

void object_interface::TriggerRemoteCtrlSubObject(const subobj_env& env, const attack_msg& attack)
{
	_imp->TriggerRemoteCtrlSubObject(env, attack);
}

void object_interface::TriggerSubobj(const XID& xid)
{
	gobject *pObj = _imp->Parent();
	MSG msg;
	BuildMessage(msg, GM_MSG_TRIGGER_SUBOBJECT_PERFORM, xid, pObj->ID, pObj->pos, 0);
	gmatrix::GetInstance().SendMessage(msg);
}

XID object_interface::GetRemoteCtrlSubObjectID() const
{
	return _imp->GetRemoteCtrlSubObjectID();
}

bool object_interface::IsFilterExist(int filter_id, ruid_t rid) const
{
	return _imp->_filter_man.IsFilterExist(filter_id, rid);
}

bool object_interface::IsTypeMaskExist(filter_typemask_t mask) const
{
	return _imp->_filter_man.IsTypeMaskExist(mask);
}

void object_interface::AddFilter(filter *pFilter)
{
	ASSERT(pFilter);
	int id = pFilter->GetID();
	if (_imp->_filter_man.AddFilter(pFilter))
	{
		_imp->_filter_man.EF_AddFilter(id);
	}
}

void object_interface::RemoveFilter(int id, ruid_t rid)
{
	bool by_self = rid == _imp->Parent()->ID.id;
	_imp->_filter_man.RemoveFilter(id, rid, filter::ER_UNKNOWN, by_self);
}

void object_interface::DelayRemoveFilter(int id, size_t delay_ms_time)
{
	_imp->DelayRemoveFilter(id, delay_ms_time);
}

bool object_interface::ModifyFitler(int id, int ctrlname, void *ctrlval, size_t ctrllen, ruid_t rid) const
{
	return _imp->_filter_man.ModifyFilter(id, ctrlname, ctrlval, ctrllen, rid);
}

void object_interface::RecordFilterTimeout(int tm)
{
	_imp->_filter_man.RecordNewTimeout(tm);
}

bool object_interface::ChangeFilterPolyCount(int id, int op, char times)
{
	return _imp->_filter_man.ChangeFilterPolyCount(id, op, times);
}

void object_interface::ClearSpecFilter(filter_eventmask_t mask)
{
	_imp->_filter_man.ClearSpecFilter(mask);
}

void object_interface::ClearSpecFilter2(filter_typemask_t mask2, int reason)
{
	_imp->_filter_man.ClearSpecFilter2(mask2, reason);
}

void object_interface::ClearSpecFilter2_Delay(filter_typemask_t mask2, int reason)
{
	_imp->SendTo2(GM_MSG_DISPEL_FILTER_TYPEMASK, _imp->GetParent()->ID, (int)mask2, reason);
}

void object_interface::FilterAdjustDamage(float& damage, const XID& attacker, const attack_msg& msg, int& attack_flag)
{
	_imp->_filter_man.EF_AdjustDamage(damage, attacker, msg, attack_flag);
}

void object_interface::FilterBeforeAdjustDamage(float& damage, const XID& attacker, const attack_msg& msg, int& attack_flag)
{
	_imp->_filter_man.EF_BeforeAdjustDamage(damage, attacker, msg, attack_flag);
}

void object_interface::BreakAction()
{
	//注意：BreakAction(true)必须对应控制，否则NPC动画不正确
	_imp->BreakAction(true);
}

void object_interface::ClearNextAction()
{
	_imp->ClearNextActions();
}

unsigned short object_interface::GetAction(unsigned short *action_arg) const
{
	gcreature *pParent = _imp->GetParent();
	*action_arg = pParent->action_arg;
	return pParent->action_type;
}

bool object_interface::HasAction() const
{
	return _imp->HasAction();
}

//from_rid>0说明这是个人buff
void object_interface::UpdateBuff(unsigned short buff_id, unsigned char buff_level, int buff_endtime, const float *data,
                                  size_t len, const abase::octets& from, bool skillfx_prio, ruid_t from_rid, int client_data_1_2)
{
	ASSERT(len == BUFF_DATA_NUM * sizeof(float));
	PB::buff_t pb;
	pb.set_buff_id(buff_id);
	pb.set_buff_level(buff_level);
	pb.set_buff_endtime(buff_endtime ? buff_endtime + gmatrix::GetInstance().GetSysTime() : 0);
	for (int i = 0; i < BUFF_DATA_NUM; i++)
	{
		pb.add_buff_data(data[i]);
	}
	pb.set_buff_from((const char *)from.begin(), from.size());
	pb.set_skillfx_prio(skillfx_prio);
	pb.set_from_rid(from_rid);
	pb.set_data_1_2(client_data_1_2);
	_imp->_buff.UpdateBuff(_imp, pb);
}

//from_rid>0说明这是个人buff
void object_interface::RemoveBuff(unsigned short buff_id, ruid_t from_rid)
{
	if (from_rid <= 0)
	{
		_imp->_buff.RemoveBuff(buff_id);
	}
	else
	{
		_imp->_buff.RemoveBuffPersonal(_imp, buff_id, from_rid);
	}
}

bool object_interface::HasBuff(unsigned short buff_id)
{
	return _imp->GetFilterMan().IsBuffExist(buff_id);
}

void object_interface::AttachSleepBuff()
{
	_imp->AttachSleepBuff();
}

void object_interface::DeattachSleepBuff()
{
	_imp->DeattachSleepBuff();
}

void object_interface::NotifyChallengeTargetAdd(const XID& caster_id, int buff_id, int damage_reduce)
{
    // 利用gmatrix消息队列系统发送消息
    MSG msg;
    BuildMessage2(msg, GM_MSG_CHALLENGE_TARGET_ADD, caster_id, GetID(), GetPos(), buff_id, damage_reduce);
    gmatrix::GetInstance().SendMessage(msg);
}

void object_interface::NotifyChallengeTargetRemove(const XID& caster_id)
{
    MSG msg;
    BuildMessage(msg, GM_MSG_CHALLENGE_TARGET_REMOVE, caster_id, GetID(), GetPos());
    gmatrix::GetInstance().SendMessage(msg);
}

void object_interface::NotifyChallengeTargetHeartbeat(const XID& caster_id, int buff_id, int damage_reduce)
{
    MSG msg;
    BuildMessage2(msg, GM_MSG_CHALLENGE_TARGET_HEARTBEAT, caster_id, GetID(), GetPos(), buff_id, damage_reduce);
    gmatrix::GetInstance().SendMessage(msg);
}

void object_interface::IncVisibleState(int state, unsigned char param1)
{
	_imp->_buff.IncVisibleState(state, param1);
}

void object_interface::DecVisibleState(int state)
{
	_imp->_buff.DecVisibleState(state);
}

void object_interface::ClearVisibleState(int state)
{
	_imp->_buff.ClearVisibleState(state);
}

void object_interface::SyncBuffInfo()
{
	_imp->_buff.Update(_imp);
}

void object_interface::ChangeShape(unsigned int index, unsigned int shape)
{
	_imp->ChangeVisualEffect(index, shape);
}

unsigned int object_interface::GetShape(unsigned int index) const
{
	return _imp->GetVisualEffect(index);
}

//void object_interface::IncActionSeal(int index)
//{
//	_imp->IncActionSeal(index);
//}
//
//void object_interface::DecActionSeal(int index)
//{
//	_imp->DecActionSeal(index);
//}
//
//void object_interface::IncActionSeal2(unsigned int mask)
//{
//	_imp->IncActionSeal2(mask);
//}
//
//void object_interface::DecActionSeal2(unsigned int mask)
//{
//	_imp->DecActionSeal2(mask);
//}
//
//int object_interface::GetActionSeal(int index) const
//{
//	return _imp->GetActionSeal(index);
//}

void object_interface::SetTurnSeal(bool isSeal)
{
	__PRINTF("SetTurnSeal %ld turn:%d\n", _imp->Parent()->ID.id, isSeal);
	if (isSeal)
	{
		_imp->IncActionSeal(SEAL_TURN);
		if (_imp->IsPlayerClass())
		{
			((gplayer_imp *)_imp)->Runner()->notify_object_pos();
		}
	}
	else
	{
		_imp->DecActionSeal(SEAL_TURN);
	}
}

void object_interface::SetRootSeal(bool isSeal)
{
	__PRINTF("SetRootSeal %ld root:%d\n", _imp->Parent()->ID.id, isSeal);
	if (isSeal)
	{
		_imp->IncActionSeal(SEAL_ROOT);
		if (_imp->IsPlayerClass())
		{
			((gplayer_imp *)_imp)->Runner()->notify_object_pos();
		}
	}
	else
	{
		_imp->DecActionSeal(SEAL_ROOT);
	}
}

void object_interface::SetSilentSeal(bool isSeal)
{
	if (isSeal)
	{
		_imp->IncActionSeal(SEAL_SILENT);
	}
	else
	{
		_imp->DecActionSeal(SEAL_SILENT);
	}
}

void object_interface::SetKotodamaSeal(bool isSeal)
{
	if (isSeal)
	{
		_imp->IncActionSeal(SEAL_KOTODAMA);
	}
	else
	{
		_imp->DecActionSeal(SEAL_KOTODAMA);
	}
}

void object_interface::SetSilentDragonbornSeal(bool isSeal)
{
	if (isSeal)
	{
		_imp->IncActionSeal(SEAL_SILENT_DRAGONBORN);
	}
	else
	{
		_imp->DecActionSeal(SEAL_SILENT_DRAGONBORN);
	}
}

void object_interface::SetSilentAbsoluteSeal(bool isSeal)
{
	_imp->SetSilentAbsoluteSeal(isSeal);
}

void object_interface::SetDietSeal(bool isSeal)
{
	if (isSeal)
	{
		_imp->IncActionSeal(SEAL_DIET);
	}
	else
	{
		_imp->DecActionSeal(SEAL_DIET);
	}
}

void object_interface::SetIdleSeal(bool isSeal)
{
	//晕的同时把root/silent/diet也置上
	if (isSeal)
	{
		_imp->IncActionSeal2((1 << SEAL_ROOT) | (1 << SEAL_SILENT) | (1 << SEAL_DIET) | (1 << SEAL_IDLE));
		//_imp->StopAttackLoop();
		if (_imp->IsPlayerClass())
		{
			((gplayer_imp *)_imp)->Runner()->notify_object_pos();
		}
	}
	else
	{
		_imp->DecActionSeal2((1 << SEAL_ROOT) | (1 << SEAL_SILENT) | (1 << SEAL_DIET) | (1 << SEAL_IDLE));
		if (_imp->IsPlayerClass())
		{
			((gplayer_imp *)_imp)->Runner()->notify_object_pos();
		}
	}

}

bool object_interface::IsMoveStoped() const
{
	return _imp->IsMoveStoped();
}

bool object_interface::IsInBind() const
{
	return _imp->IsInBind();
}

int object_interface::GetLinkIndex() const
{
	return _imp->GetLinkIndex();
}

int object_interface::GetLinkSID() const
{
	return _imp->GetLinkSID();
}

bool object_interface::SendProtocol(GNET::Protocol *p)
{
	return GSP::GetInstance().SendProtocol(GetLinkIndex(), p);
}

bool object_interface::CanReturnToTown() const
{
	return true;
}

void object_interface::ReturnToTown()
{
	_imp->ReturnToTown();
}

void object_interface::TranspotToRevivePos()
{
	_imp->TranspotToRevivePos();
}

void object_interface::TranspotTo(unsigned short to)
{
	if (_imp->Parent()->world_tid == GLOBAL_WORLD_TID)
	{
		_imp->TranspotTo(to);
	}
}

void object_interface::GetSubobjXidAndPos(int tid, std::map<XID, A3DVECTOR3>& m, float& body_size)
{
	_imp->GetSubobjXidAndPos(tid, m, body_size);
}

void object_interface::SendPerformSkill(
    unsigned short skill_id,
    unsigned char skill_level,
    unsigned char attack_stage,
    unsigned short ms_time,
    unsigned char extra_data,
    unsigned short target_count,
    const XID *target_list,
    unsigned short direction,
    unsigned char cycle_count,
    const A3DVECTOR3& target_pos,
    char range_type,
    float affect_radius,
    float affect_radius2,
    float affect_length,
    float affect_angle,
    const A3DVECTOR3& move2pos,
    const XID& prior_target
)
{
	unsigned char skill_sn = 1; //FIXME: 应liulie要求, 默认为1, 自动战斗才是0
	/*
	base_action * pAction = _imp->GetCurAction();
	if(pAction && pAction->IsSkillAction())
	{
		pAction->GetSkillID(&skill_sn);
	}*/

	_imp->Runner()->object_perform_skill(skill_id, skill_level, attack_stage, ms_time, extra_data, target_count, target_list, direction, cycle_count, skill_sn,
	                                     target_pos, range_type, affect_radius, affect_radius2, affect_length, affect_angle, move2pos, prior_target);
//	_imp->NotifyAllMP();
}

void object_interface::SendSubobjectTakeEffect(unsigned short perform_id,       // P1: 执行的技能段id
        const A3DVECTOR3& pos,           // P3: 产生效果的位置
        unsigned char target_count,      // P4: 相关的目标数
        const XID *target_list,          // P5: 相关的目标id表
        char range_type,
        float affect_radius,
        float affect_radius2,
        float affect_length,
        float affect_angle
                                              )
{
	_imp->Runner()->subobject_take_effect(perform_id, pos, target_count, target_list, range_type, affect_radius, affect_radius2, affect_length, affect_angle);
}

void object_interface::SendRunPerform(
    unsigned short perform_id,       // P1: 执行的技能段id
    unsigned short ms_time           // P2: 执行持续时间
)
{
	_imp->Runner()->object_run_perform(perform_id, ms_time);
}

void object_interface::SendBindThrowStart(
    unsigned short skill_id,         // P1: 投技技能id
    const XID& thrower               // P2: 投掷者
)
{
	_imp->Runner()->bindthrow_start(skill_id, thrower.id);
}

void object_interface::SendBindThrowStop(
    unsigned short skill_id,         // P1: 投技技能id
    const XID& thrower,              // P2: 投掷者
    const A3DVECTOR3& pos,           // p3: 投技结束后扔到的位置
    unsigned short dir,
    unsigned char type,              // p4: 投技结束后扔开的姿态: 击飞/击溃/击倒
    unsigned short time              // p5: 投技结束后扔开的持续时间
)
{
	_imp->Runner()->bindthrow_stop(skill_id, thrower.id, pos, dir, type, time);
}

void object_interface::SendMailInfo(send_mail_info_params& params)
{
	_imp->Runner()->mail_info(params);
}

size_t object_interface::MailAddItem(unsigned short mail_id, unsigned char category, const GDB::itemdata& data,  int common_pb_mail_type)
{
	return _imp->MailAddItem(mail_id, category, data, common_pb_mail_type);
}

uint64_t object_interface::IncBindCashFromItemData(unsigned short mail_id, unsigned char category, const GDB::itemdata& data)
{
	return _imp->IncBindCashFromItemData(mail_id, category, data);
}

void object_interface::MailModifyReputation(unsigned short mail_id, unsigned char category, int repu_id, int offset)
{
	return _imp->MailModifyReputation(mail_id, category, repu_id, offset);
}

bool object_interface::CanRecvDaily() const
{
	return _imp->CanRecvDaily();
}
unsigned int object_interface::GetItemRecommandPrice(int itemid) const
{
	const item_template *pItemTemplate = item_manager::GetInstance().GetItemTemplate(itemid);
	if (pItemTemplate)
	{
		return pItemTemplate->GetRecommandPrice();
	}
	return 0;
}
int object_interface::GetReputation(int index) const
{
	return _imp->GetReputation(index);
}
int object_interface::MailCanGetMore() const
{
	int repu = GetReputation(GNET::REPUID_MAIL_RECV);
	int repu_limit_config = SERVER_CONFIG.daily_get_value_config;
	int repu_limit = level_exp_factor_template_manager::GetInstance().GetFactor(repu_limit_config, GetLevel());
	if (repu >= repu_limit)
	{
		__PRINTF("gs:MailCanGetMore:roleid="   FMT_I64":repu=%d:repu_limit=%d\n", _imp->Parent()->ID.id, repu, repu_limit);
		return 1;
	}
	return 0;
}
int object_interface::MailCanGet(int count) const
{
	int repu = GetReputation(GNET::REPUID_MAIL_RECV);
	int repu_limit_config = SERVER_CONFIG.daily_get_value_config;
	int repu_limit = level_exp_factor_template_manager::GetInstance().GetFactor(repu_limit_config, GetLevel());
	if (repu + count > repu_limit)
	{
		__PRINTF("gs:MailCanGet:roleid="   FMT_I64":count=%d:repu=%d:repu_limit=%d\n", _imp->Parent()->ID.id, count, repu, repu_limit);
		return 1;
	}
	return 0;
}
int object_interface::ItemCanMail(int item_id, int item_count, item_location_t location, item_index_t index) const
{
	if (!_imp->ItemCanGift(location, index, item_id))
	{
		return S2C::ERR_MAIL_PACK_CAN_NOT_GIFT;
	}
	if (_imp->ItemIsEquipment(location, index, item_id))
	{
		return S2C::ERR_MAIL_PACK_IS_EQUIPMENT;
	}

	int repu = GetReputation(GNET::REPUID_MAIL_SEND);
	int repu_limit_config = SERVER_CONFIG.daily_give_value_config;
	int repu_limit = level_exp_factor_template_manager::GetInstance().GetFactor(repu_limit_config, GetLevel());
	if (repu + item_count > repu_limit)
	{
		return S2C::ERR_MAIL_PACK_COUNT_LIMIT;
	}
	return 0;
}
// 服务器自动取邮件附件，主要用于从DS到GS转移物品
void object_interface::ServerGetAttachment(unsigned short mail_id, int box_idx)
{
	((gplayer_imp *)_imp)->CmdGetAttachment(mail_id, 0, box_idx, 1);
}

//拍卖行相关接口
void object_interface::GetAuctionAttachment(int mail_id)
{
	((gplayer_imp *)_imp)->CmdGetAuctionAttachment(mail_id, false);
}
money_t object_interface::GetAuctionShopLevelUpCost(int level)
{
	return 0;
	/*
	const shop_level_info_template * pTemplate = auction_shop_template_manager::GetInstance().GetShopLevelInfoTemplate(level);
	if (!pTemplate)
		return 0;

	return pTemplate->cost_money;
	*/
}
bool object_interface::MailAuctionProcess(GNET::Protocol *p)
{
	//这个必须在player加锁之后调用
	return ((gplayer_imp *)_imp)->AuctionProcess(p) == 0;
}
bool object_interface::SystemMailBoxFull() const
{
	return _imp->IsSystemMailBoxFull();
}
bool object_interface::AuctionMailBoxFull() const
{
	return _imp->IsAuctionMailBoxFull();
}

bool object_interface::AuctionFull() const
{
	return _imp->IsAuctionFull();
}

int object_interface::CheckAuctionBuy() const
{
	return _imp->CheckAuctionBuy();
}

size_t object_interface::GetBackpackEmptySlot() const
{
	return _imp->GetBackpackEmptySlot();
}
/*
bool object_interface::LockItem(int tid,size_t count,unsigned char& location,unsigned short& index)
{
	return _imp->LockItem(tid,count,location,index);
}

bool object_interface::UnlockItem(unsigned char location,unsigned short index,size_t count)
{
	return _imp->UnlockItem(location,index,count);
}
*/

bool object_interface::DecItem(int code, int64_t arg, int tid, size_t count)
{
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return false;
	}
	return _imp->DecItem(tid, count, {static_cast<FUNC_CODE>(code), arg});
}

enum { MAX_SKILL_PER_PEPOLE = 64, };
abase::static_map<int, int, player_rune_manager::MAX_SKILL_PER_PEPOLE> be_bind_skill_map;	// rune_skill/bind_skill -> master_skill
std::map<int, std::vector<int>> bind_skill_map;							// master_skill -> rune_skill/bind_skill

void object_interface::LoadBindSkills(unsigned short skill_id, const std::vector<int>& bind_skill)
{
	bind_skill_map[skill_id] = bind_skill;
	for (auto it = bind_skill.begin(); it != bind_skill.end(); ++it)
	{
		be_bind_skill_map[*it] = skill_id;
	}
}

bool object_interface::CanMoveSkill(unsigned short skill_id)
{
	return SkillConfig::GetInstance().CanMoveSkill(skill_id);
}

void object_interface::OnSkillLearnTo(unsigned short skill_id, unsigned char skill_level, bool notify_client, std::map<int, int> *result)
{
}

void object_interface::SendLearnSkill(unsigned short skill_id, unsigned char skill_level)
{
	return _imp->Runner()->learn_skill(skill_id, skill_level);
}

/*bool object_interface::OnSellCash(int cash_onsale, int cash_serial)
{
	return _imp->OnSellCash(cash_onsale, cash_serial);
}

bool object_interface::OnBuyCash(int cash)
{
	return _imp->OnBuyCash(cash);
}*/

int object_interface::GetCurTitle() const
{
	return _imp->GetCurTitle();
}

ruid_t object_interface::GetRoleID() const
{
	return _imp->Parent()->ID.id;
}

const abase::octets *object_interface::GetName() const
{
	return _imp->GetName();
}

int object_interface::GetSpouseID() const
{
	return _imp->GetSpouseID();
}

const abase::octets *object_interface::GetSpouseName() const
{
	return _imp->GetSpouseName();
}

bool object_interface::IsInFamily() const
{
	return false;//_imp->GetFamilyID() > 0;
}

int object_interface::GetFamilyID() const
{
	return 0;//_imp->GetFamilyID();
}

const abase::octets *object_interface::GetFamilyName() const
{
	return NULL;//_imp->GetFamilyName();
}

bool object_interface::IsInMafia() const
{
	return _imp->GetMafiaID() > 0;
}

int object_interface::GetMafiaID() const
{
	return _imp->GetMafiaID();
}

const abase::octets *object_interface::GetMafiaName() const
{
	return _imp->GetMafiaName();
}


void object_interface::NPCAskForHelp(float range)
{
	if (range <= 0.0f)
	{
		return;
	}
	_imp->AskForHelp(range, true);
}


XID object_interface::GetMaster() const
{
	return _imp->GetMaster();
}


void object_interface::SystemSpeak(int speak_id)
{
	SystemSpeak(speak_id, XID());
}
void object_interface::SystemSpeak(int speak_id, const XID& id)
{
	_imp->SystemSpeak(speak_id, id);
}

void object_interface::error_message(unsigned char action_type, unsigned char show_type, unsigned short msg) const
{
	_imp->error_message(action_type, show_type, msg);
}

bool object_interface::QueryObject(const XID& who, A3DVECTOR3& pos, unsigned short& scene_tag, float& body_size, unsigned short& dir, bool& is_dead, bool *visible)
{
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(who, info))
	{
		return false;
	}
	pos = info.pos;
	scene_tag = info.scene_tag;
	body_size = info.body_size;
	dir = info.dir;
	is_dead = info.zombie;
	if (visible != NULL)
	{
		*visible = info.CanBeSee();
	}
	return true;
}

bool object_interface::CheckRangeAndGetInfo(const XID& target, float squared_range, A3DVECTOR3& pos, int64_t& hp, int64_t& max_hp) const
{
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;
	}
	if (!info.CheckRange(_imp->Parent(), squared_range))
	{
		return false;
	}
	pos = info.pos;
	hp = info.base_info.prop_hpmp.HP;
	max_hp = info.base_info.prop_hpmp.maxHP;
	return true;
}

float object_interface::GetAttackDefenseClassAdjust(unsigned char attack_class, unsigned char defense_class)
{
// return prof_template_manager::GetInstance().GetAtkDefClassAdjust(attack_class,defense_class);
//这个已经没有用， 技能那里应该删掉
	return 1.0f;
}

bool object_interface::IsCritical(float crit_rate)
{
	if (crit_rate < 0)
	{
		crit_rate = 0;
	}
	if (crit_rate > 1)
	{
		crit_rate = 1;
	}
	return (crit_rate > abase::RandUniform());
}

float object_interface::Rand()
{
	return abase::RandUniform();
}
bool object_interface::RandNormalize(float *r, int n)
{
	if (n <= 0)
	{
		return false;
	}

	double t = 0;
	float *slider = r;
	for (int i = 0; i < n; i++)
	{
		t += *slider;
		slider++;
	}
	if (t > 0.00001)
	{
		slider = r;
		for (int i = 0; i < n; i++)
		{
			*slider = float((*slider) / t);
			slider++;
		}
	}
	else
	{
		memset(r, 0, sizeof(float) * n);
		*r = 1.f;
	}
	return true;
}
int  object_interface::RandSelect(const float *option, int size)
{
	return abase::RandSelect(option, size);
}

uint64_t object_interface::GetTick()
{
	return g_timer.get_tick();
}

int object_interface::GetItemAuctionInfo(int inv_pos, int item_tid, int64_t& category, int& base_price, int sale_price, int sale_num, abase::octets& extra_data, int& longyu_id)
{
	return _imp->GetItemAuctionInfo(inv_pos, item_tid, category, base_price, sale_price, sale_num, extra_data, longyu_id);
}

int object_interface::CheckAuctionWarning(int item_tid, int sale_price, int sale_num, abase::octets& extra_data)
{
	return _imp->CheckAuctionWarning(item_tid, sale_price, sale_num, extra_data);
}

int object_interface::GetAuctionRepoTime(int price_ratio)
{
	return FAuctionCfgMgr::Instance().GetAuctionRepoTime(price_ratio);
}

int object_interface::GetTxnItem(int txn_id, GDB::itemdata& item_data, int item_pos)
{
	return _imp->GetTxnItem(txn_id, item_data, item_pos);
}

bool object_interface::IsIdipForbidPlayerFunc(int func_code)
{
	return ((gplayer_imp *)_imp)->IsIdipForbidPlayerFunc(func_code);
}

bool object_interface::TestUseLimit(tid_t tid, size_t count/* = 1*/, bool must_exist/* = false*/) const
{
	if (!IsPlayerClass())
	{
		return true;
	}

	return ((gplayer_imp *)_imp)->GetCommonUseLimit().TestUseLimit(tid, count, must_exist);
}

bool object_interface::AddUseLimit(tid_t tid, size_t count/* = 1*/)
{
	if (!IsPlayerClass())
	{
		return true;
	}

	gplayer_imp *imp = (gplayer_imp *)_imp;
	return imp->GetCommonUseLimit().AddUseLimit(imp, tid, count);
}

float object_interface::GetFarRangeHigAdjust()
{
	return SERVER_CONFIG.far_range_hit_adjust;
}

float object_interface::GetVeryFarRangeHigAdjust()
{
	return SERVER_CONFIG.veryfar_range_hit_adjust;
}

bool object_interface::CheckItem(int item_tid, size_t count) const
{
	return _imp->CheckItemExistAtBackpack(item_tid, count); //FIXME
}

int object_interface::TakeOutItem(int code, int64_t arg, int item_tid, size_t count)
{
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return 0;
	}
	return _imp->DecItem(item_tid, count, {static_cast<FUNC_CODE>(code), arg});
}

bool object_interface::TryTakeOutItem(int code, int64_t arg, int location, size_t item_index, int item_tid)
{
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return false;
	}
	FuncInfo func_info{static_cast<FUNC_CODE>(code), arg};
	if (_imp->CheckItemExistAtIndex(location, item_index, item_tid, 1))
	{
		return _imp->DecItemAtIndex(location, item_index, 1, func_info);
	}
	return _imp->DecItemAtBackpack(func_info, item_tid, 1);
}

money_t object_interface::GetMoney() const
{
	return _imp->GetMixMoney();
}

//void object_interface::DecMoney(money_t money)
void object_interface::DecMoney(int code, int64_t arg, uint64_t money)
{
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return;
	}
	_imp->DecMixMoney({static_cast<FUNC_CODE>(code), arg}, money);
}

money_t object_interface::GetCanUseMoney(MONEY_TYPE type) const
{
	return _imp->GetCanUseMoney(type);
}

bool object_interface::IsEquipWing()
{
	return false;
}

bool object_interface::IsAggressive()
{
	return _imp->Parent()->msg_mask & gobject::MSG_MASK_PLAYER_PEEP;
}

void object_interface::SetAggressive(bool isActive)
{
	if (isActive)
	{
		_imp->Parent()->msg_mask |= gobject::MSG_MASK_PLAYER_PEEP;
	}
	else
	{
		_imp->Parent()->msg_mask &= ~gobject::MSG_MASK_PLAYER_PEEP;
	}
}

void object_interface::EnterCombatState()
{
	_imp->ActiveCombatState(true, 0);
}

bool object_interface::IsCombatState() const
{
	return _imp->IsCombatState();
}

void object_interface::AddAggro(const XID& who, int rage, const attacker_info_t& info)
{
	if (rage < 0)
	{
		return;
	}
	_imp->AddAggro(who, rage, info);
}

void object_interface::AddAggroToEnemy(const XID& helper, int rage)
{
	if (rage < 0)
	{
		return;
	}
	_imp->AddAggroToEnemy(helper, rage);
}

void object_interface::RemoveAggro(const XID& who)
{
	_imp->RemoveAggro(who);
}

void object_interface::BeTaunted(const XID& who, float time, const attacker_info_t& info)
{
	if (time <= 0)
	{
		time = 1;
	}
	_imp->BeTaunted(who, time, &info);
}

void object_interface::BeDedicated(const XID& who, int rage_added, const attacker_info_t& info)
{
	if (rage_added <= 0)
	{
		rage_added = 1;
	}
	_imp->BeDedicated(who, rage_added, &info);
}

void object_interface::CreateMinors(const A3DVECTOR3& pos, const object_interface::minor_param& param)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	const npc_template *pTemplate = npc_template_manager::GetInstance().Get(param.npc_tid);
	if (!pTemplate)
	{
		return;
	}
	//初始化下
	_imp->InitSummonNpcMap();
	auto *p_summon_npc_map = _imp->GetSummonNpcMap();
	if (!p_summon_npc_map)
	{
		return;
	}
	auto& summon_npc_map = *p_summon_npc_map;
	int exist_count = 0;
	XID earliest_xid;
	int earliest_time = INT32_MAX;
	for (auto& kv : summon_npc_map)
	{
		auto *p_npc_templ = kv.second.p_templ;
		if (!p_npc_templ)
		{
			continue;
		}
		if (p_npc_templ->tid == param.npc_tid)
		{
			exist_count ++;
			if (kv.second.summon_time < earliest_time)
			{
				earliest_time = kv.second.summon_time;
				earliest_xid = kv.first;
			}
		}
	}
	if (exist_count >= param.limit_count || exist_count >= 10) //来个保底
	{
		if (param.reach_limit_stop)
		{
			return;
		}
		if (earliest_xid.IsValid())
		{
			MSG msg;
			BuildMessage(msg, GM_MSG_MASTER_DEAD, earliest_xid, XID(), A3DVECTOR3(), 0);
			gmatrix::GetInstance().SendMessage(msg);
		}
	}

	create_npc_t cnt;
	cnt.tid = param.npc_tid;
	if (param.use_parent_faction)
	{
	}
	else if (param.use_faction)
	{
		cnt.faction = param.use_faction;
	}
	cnt.npc_count = 1;
	cnt.msg_mask_or = 0;
	cnt.msg_mask_and = 0xFFFFFFFF;
	cnt.origin_pos = pos;
	cnt.pos = pos;
	cnt.dir = param.use_parent_dir ? GetDir() : abase::Rand(0, 65535);
	cnt.real_life = param.remain_time;
	cnt.level = param.npc_level;
	if (param.script_data && param.script_size)
	{
		strncpy(cnt.script_path, (const char *)param.script_data, MAX_SCRIPT_PATH_SIZE);
		cnt.script_path[MAX_SCRIPT_PATH_SIZE - 1] = '\0';
	}
	gnpc *pNPC = _imp->GetSceneImp()->CreateNPC(cnt, false, false);
	if (pNPC)
	{
		gnpc_imp *pImp = (gnpc_imp *)pNPC->imp;
		pImp->_faction = 0xFFFFFFFF;
		if (param.use_parent_faction)
		{
			pImp->_faction = _imp->GetFaction();
			pImp->_enemy_faction = _imp->GetEnemyFaction();
		}
		if (param.vis_tid)
		{
			pNPC->vis_tid = param.vis_tid;
		}
		if (param.npc_name_size)
		{
			ASSERT(param.npc_name_size <= sizeof(param.npc_name));
			memcpy(pNPC->npc_name, param.npc_name, param.npc_name_size);
			pNPC->name_size = param.npc_name_size;
			pNPC->object_state |= gobject::STATE_NPC_NAME;
		}
		pImp->SetMaster(gnpc_imp::NPC_MASTER_TYPE_COMMON, _imp->GetParent()->ID);
		pImp->SetOwner(_imp->GetParent()->ID, XID());
		pImp->SetSummonNPC();
		auto& summon_npc = summon_npc_map[pNPC->ID];
		summon_npc.die_with_owner = param.die_with_owner;
		summon_npc.p_templ = pTemplate;
		summon_npc.summon_time = gmatrix::GetInstance().GetSysTime();
		if (!pNPC->IsActive())
		{
			//初始化主人数据
			pImp->InitMasterData();
			_imp->FillNpcMasterData(pImp->GetMasterData());
			pImp->EnterScene(_imp->GetSceneImp());
		}
		pNPC->Unlock();
	}
}

void object_interface::CreateNPC(gscene_imp *pSceneImp, const A3DVECTOR3& pos, const  object_interface::minor_param& param)
{
	if (!pSceneImp)
	{
		return ;
	}
	const npc_template *pTemplate = npc_template_manager::GetInstance().Get(param.npc_tid);
	if (!pTemplate)
	{
		return;
	}
	create_npc_t cnt;
	cnt.tid = param.npc_tid;
	cnt.npc_count = 1;
	cnt.msg_mask_or = 0;
	cnt.msg_mask_and = 0xFFFFFFFF;
	cnt.origin_pos = pos;
	cnt.pos = pos;
	cnt.dir = abase::Rand(0, 65535);
	cnt.life = param.remain_time;
	gnpc *pNPC = pSceneImp->CreateNPC(cnt, false, false);
	if (pNPC)
	{
		if (param.vis_tid)
		{
			pNPC->vis_tid = param.vis_tid;
		}
		if (param.npc_name_size)
		{
			ASSERT(param.npc_name_size <= sizeof(param.npc_name));
			memcpy(pNPC->npc_name, param.npc_name, param.npc_name_size);
			pNPC->name_size = param.npc_name_size;
			pNPC->object_state |= gobject::STATE_NPC_NAME;
		}
		pNPC->imp->EnterScene(pSceneImp);
		pNPC->Unlock();
	}
}

void object_interface::CreateMinors(const object_interface::minor_param& param, float radius)
{
	CreateMinors(param, _imp->Parent()->pos, radius);
}

void object_interface::CreateMinors(const minor_param& param, const A3DVECTOR3& _pos, float radius)
{
	if (!_imp->GetSceneImp())
	{
		return;
	}
	A3DVECTOR3 pos;
	bool pos_valid = false;
	for (size_t i = 0; i < 5; ++i)
	{
		pos = _pos;
		pos.x += abase::Rand(-radius, radius);
		pos.z += abase::Rand(-radius, radius);
		if (_imp->GetSceneImp()->GetValidPFPos(pos))
		{
			pos_valid = true;
			break;
		}
	}
	if (!pos_valid)
	{
		pos = _pos;    //随机5次不成 使用原始点
	}
	CreateMinors(pos, param);
}

float object_interface::CalcLevelDamagePunish(int atk_level, int def_level)
{
	float adj = 1.0f;
	int delta = atk_level - def_level;
	player_template::GetAttackLevelPunishment(delta, adj);
	return adj;
}

bool object_interface::CreatePet(const A3DVECTOR3& pos, const pet_data *pData, const item_pet_bedge *it_pet, int tid, size_t pet_index, XID& who)
{
	/*
	if(!_imp->GetSceneImp()) return false;
	unsigned char dir = _imp->Parent()->dir;
	gnpc* pNPC = npc_spawner::CreatePetBase((gplayer_imp*)_imp,pos,pData,it_pet,dir,tid,pet_index);
	if(pNPC)
	{
		who = pNPC->ID;
		gnpc_pet_imp* pImp = (gnpc_pet_imp*)pNPC->imp;
		pImp->EnterScene(_imp->GetSceneImp());
		pNPC->Unlock();
		return true;
	}
	return false;
	*/

	return false;
}

void object_interface::StopCharge(skill_id_t skill_id)
{
	_imp->StopCharge(skill_id);
}

size_t object_interface::GetInventorySize()
{
	return _imp->GetCurInvSize();
}

int object_interface::GetInventoryDetail(GDB::itemdata *list, size_t size)
{
	return _imp->GetInventoryDetail(list, size);
}

size_t object_interface::GetEquipmentSize()
{
	return _imp->GetEqpInvSize();
}

int object_interface::GetEquipmentDetail(GDB::itemdata *list, size_t size)
{
	return _imp->GetEquipmentDetail(list, size);
}

size_t object_interface::GetTrashBoxCapacity()
{
	return _imp->GetTrashBoxSize();
}

int object_interface::GetTrashBoxDetail(GDB::itemdata *list, size_t size)
{
	return _imp->GetTrashBoxDetail(list, size);
}

money_t object_interface::GetTrashBoxMoney()
{
	return _imp->GetTrashBoxMoney();
}

bool object_interface::IsTrashBoxModified()
{
	return _imp->IsTrashBoxModified();
}

void
object_interface::SendClientSkillNotify(int param1, filter_typemask_t param2)
{
	_imp->Runner()->skill_notify(param1, param2);
}

void object_interface::SendClientSkillAddon(int skill, int addon)
{
	_imp->Runner()->player_skill_addon(skill, addon);
}

void object_interface::SendClientSkillCommonAddon(int addon)
{
	_imp->Runner()->player_skill_common_addon(addon);
}

void object_interface::SendClientExtraSkill(int skill, int level)
{
	_imp->Runner()->player_extra_skill(skill, level);
}

int
object_interface::GetDBTimeStamp()
{
	return _imp->GetDBTimeStamp();
}

int
object_interface::InceaseDBTimeStamp()
{
	return _imp->IncDBTimeStamp();
}

bool object_interface::IsCashModified()
{
	return _imp->IsCashModified();
}

int64_t object_interface::GetCurExp() const
{
	return _imp->GetProperty().GetExp();
}

void object_interface::IncExp(int64_t exp, int skill_id, int skill_level)
{
	_imp->ReceiveSkillExp(exp, skill_id, skill_level);
}
void object_interface::IncWineExp(int64_t exp)
{
	_imp->ReceiveWineExp(exp);
}
void object_interface::DecProfExp(int code, size_t force)
{
	if (force == 0)
	{
		return;
	}
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return;
	}
	_imp->DecProfExp(static_cast<FUNC_CODE>(code), force);
}

void object_interface::IncProfExp(int code, size_t force)
{
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return;
	}
	_imp->IncProfExp(static_cast<FUNC_CODE>(code), force);
}

void object_interface::EnhanceExpRatio(int type, float value)
{
//先注释掉吧
//	_imp->EnhanceExpRatio(type, value);
}
void object_interface::ModifyReputation(int type, int index, int offset, int64_t type_arg)
{
	if (type <= kFuncCodeUnknown || type > kFuncCodeMax)
	{
		return;
	}
	_imp->ModifyReputation({static_cast<FUNC_CODE>(type), type_arg}, index, offset);
}

void object_interface::SetNoRewardFlag()
{
	_imp->SetNoRewardFlag();
}

void object_interface::ClrNoRewardFlag()
{
	_imp->ClrNoRewardFlag();
}

float object_interface::GetMeleeWeaponAttackCycle() const
{
	return 1.0f;	//TODO 已经废弃
}

bool object_interface::IsInSanctuary()
{
	return _imp->IsInSanctuary();
}

void object_interface::SetAutoAttackDelay(int millisec)
{
}

void object_interface::SendClientDuelStart(const XID& target, int duel_type, const A3DVECTOR3& flag_pos)
{
	_imp->Runner()->duel_start(target, duel_type, flag_pos);
}

void object_interface::SendClientDuelStop(const XID& target)
{
	_imp->Runner()->duel_stop(target);
}

bool object_interface::ModifyTalentPoint(int offset, bool main_prof)
{
	return _imp->ModifyTalentPoint(offset, main_prof);
}

bool object_interface::TestSafeLock()
{
	return _imp->TestSafeLock();
}

void object_interface::SetEnterDyingState(bool flag)
{
	return _imp->SetEnterDyingState(flag);
}

void object_interface::Die(int delay_tick, bool ignore_statistics)
{
	_imp->LazySendTo(GM_MSG_DEATH, _imp->Parent()->ID, ignore_statistics, delay_tick);
}

void object_interface::SetLastSkill(int skill_id)
{
	_imp->SetLastSkill(skill_id);
}

void object_interface::OnSkillCast(int skill_id, const XID& target)
{
	_imp->OnSkillCast(skill_id, target);
}

void object_interface::TellAICast(const XID& npc, int skill_id)
{
	_imp->SendTo2(GM_MSG_AI_EVENT_NOTIFY, npc, 2, skill_id);
}

void object_interface::OnBuffSkillCast(int skill_id, const XID& target)
{
	_imp->OnBuffSkillCast(skill_id, target);
}

void object_interface::Disappear()
{
	if (_imp->Parent()->ID.IsNPC())
	{
		_imp->SendTo(GM_MSG_DISAPPEAR, _imp->Parent()->ID, 0);
	}
}

void object_interface::ClearBufSkill()
{
}

void object_interface::Sacrifile(const XID& pet, const attacker_info_t& info, float damage)
{
}

int object_interface::GetSceneParamValue(int key)
{
	return _imp->GetSceneParamValue(key);
}

void object_interface::SetSceneParamValue(int key, int value)
{
	_imp->SetSelfParamValue(key, value);
}

int object_interface::ModifySceneParamValue(int key, int offset)
{
	return _imp->ModifySceneParamValue(key, offset);
}

int object_interface::GetSelfParamValue(int key)
{
	return _imp->GetSelfParamValue(key);
}

void object_interface::SetSelfParamValue(int key, int value)
{
	_imp->SetSelfParamValue(key, value);
}

int object_interface::ModifySelfParamValue(int key, int offset)
{
	return _imp->ModifySelfParamValue(key, offset);
}

int object_interface::GetInstType()
{
	gscene_imp *pSceneImp = _imp->GetRealSceneImp();
	if (!pSceneImp)
	{
		return 1;
	}
	const scene_template *st = pSceneImp->GetTemplate();
	if (!st)
	{
		return 1;
	}
	return st->inst_type;
}

void object_interface::FactionMoney(int count)
{
	//增加来自帮派功能的绑定币
	//XOM 已经作废
//	_imp->IncMoney(INC_MONEY_TYPE_MAFIA_SKILL,MT_BIND,count);
}

void object_interface::FactionMoney(int skill_id, const XID& target, int money_config_id)
{
	//XOM 已经作废
//	_imp->IncMoneyWithResponse(skill_id,target,money_config_id);
}

bool object_interface::CheckMountState() const
{
	return _imp->IsMount();
}

bool object_interface::CheckFlySwordState() const
{
	return _imp->IsFlySword();
}
float object_interface::CalcMountDrop(float rate)
{
	return rate + _imp->_prop._mount_drop_rate_addon;
}

bool object_interface::IsEquipmentModified()
{
	//return _imp->OI_IsEquipmentModified();
	return true;
}

int object_interface::TxnExec(int reason, const FuncInfo& fi, unsigned int money_type_mask, money_t money, money_t bind_money, unsigned int cash_type_mask, int cash, int bind_cash, item_location_t& location, int item_index, int item_count, int item_tid, txn_id_t& txn_id, int repu_id, int repu_count)
{
	return _imp->TxnExec(reason, fi, money_type_mask, money, bind_money, cash_type_mask, cash, bind_cash, location, item_index, item_count, item_tid, txn_id, repu_id, repu_count);
}

int object_interface::TxnRet(txn_id_t txn_id, int txn_result, int ret_code)
{
	return _imp->TxnRet(txn_id, txn_result, ret_code);
}

int object_interface::TxnRet(ruid_t roleid, txn_id_t txn_id, int txn_result, int ret_code)
{
	TLA_KEEPER;
	// 事务结果返回时如果无法通知角色对象，就发送SyncTxnResult到数据库记录结果
	if (!txn_id)
	{
		GSP::GetInstance().SendSyncTxnResult(roleid, txn_id, txn_result);
		return -1;
	}
	XID player;
	MAKE_XID(player, roleid);
	gplayer *pPlayer = gmatrix::GetInstance().FindPlayer(player);
	if (!pPlayer)
	{
		GSP::GetInstance().SendSyncTxnResult(roleid, txn_id, txn_result);
		return -2;
	}
	AUTO_LOCK(pPlayer->spinlock, &debug::obj_lcs);
	if (!check_obj_base(pPlayer, player))
	{
		GSP::GetInstance().SendSyncTxnResult(roleid, txn_id, txn_result);
		return -3;
	}
	return ((gplayer_imp *)pPlayer->imp)->TxnRet(txn_id, txn_result, ret_code);
}

void
object_interface::DuelStart(const XID& target)
{
	_imp->OnDuelStart(target);
}

void
object_interface::DuelStop()
{
	_imp->OnDuelStop();
}


int object_interface::GetMafiaTrashBoxDetail(GDB::itemdata *list, size_t size)
{
	//return _imp->OI_GetMafiaTrashBoxDetail(list,size);
	return 0;
}

size_t object_interface::GetMafiaTrashBoxCapacity()
{
	//return _imp->OI_GetMafiaTrashBoxCapacity();
	return 0;
}


void object_interface::KnockBack(const XID& attacker, const A3DVECTOR3& source, float distance)
{
//	_imp->KnockBack(attacker,source, distance);
}

int object_interface::GetDBMagicNumber()
{
	return _imp->GetDBMagicNumber();
}

void object_interface::ClearMountFilter()
{
	_imp->_filter_man.RemoveFilter(FI_MOUNT);
}

int object_interface::CreateItem(int item_id, int count, int period)
{
	return _imp->SkillCreateItem(item_id, count, period);
}

int object_interface::GetSpecItem(int index)
{
	if (SII_PROPOSE == index)
	{
		return SERVER_CONFIG.id_propose_item;
	}
	return 0;
}

int object_interface::GetSpecialItem(unsigned char ipc_reason, GDB::itemdata& inv)
{
	if (ipc_reason == GNET::IPCTXN_REASON_FACTION_BASE_ACTIVE)
	{
		inv.id = SERVER_CONFIG.id_factionbase_recover_item;
		inv.index = -1;
		inv.count = 1;
		return inv.id;
	}

	if (ipc_reason == GNET::IPCTXN_REASON_FACTION_BASE_INIT)
	{
		inv.id = SERVER_CONFIG.id_create_faction_item;//等模板数据
		inv.index = -1;
		inv.count = SERVER_CONFIG.num_create_faction_item;
		return inv.id;
	}
	inv.id = 0;
	return 0;
}

void object_interface::ResetComboTimer()
{
	return _imp->ResetComboTimer();
}

void object_interface::ResetHitTimer(size_t tick)
{
	return _imp->ResetHitTimer(tick);
}

void object_interface::ResetSuspendTimer(size_t tick)
{
	return _imp->ResetSuspendTimer(tick);
}

void object_interface::MakeRoleData(abase::octets& rdata)
{
	_imp->MakeRoleData(rdata);
}

bool object_interface::IsObjectSummonPet(const XID& object) const
{
	return false;
}

//int object_interface::CheckPlayers(ruid_t leader_id,const std::vector<ruid_t>& member_id_list,ruid_t team_id,float range)
//{
//	XID xid;
//	MAKE_XID(xid,leader_id);
//	std::vector<XID> member_xid_list;
//	for(size_t i = 0;i < member_id_list.size();++i)
//	{
//		member_xid_list.push_back(XID(GM_TYPE_PLAYER,member_id_list[i]));
//	}
//	XID team(GM_TYPE_TEAM,team_id);
//	team_manager& tm = team_manager::GetInstance();
//	if(!tm.CheckPlayers(xid,member_xid_list,team)) return NOT_A_TEAM;
//	float squared_range = range * range;
//	object_info linfo;
//	if(!gmatrix::GetInstance().QueryObject(xid,linfo)) return OUT_OF_RANGE;
//	for(size_t i = 0;i < member_id_list.size();++i)
//	{
//		object_info minfo;
//		MAKE_XID(xid,member_id_list[i]);
//		if(!gmatrix::GetInstance().QueryObject(xid,minfo)) return OUT_OF_RANGE;
//		if(linfo.scene_tag != minfo.scene_tag) return OUT_OF_RANGE;
//		if(linfo.world_tid != minfo.world_tid) return OUT_OF_RANGE;
//		if(linfo.world_id != minfo.world_id) return OUT_OF_RANGE;
//		if(squared_distance(linfo.pos,minfo.pos) >= squared_range) return OUT_OF_RANGE;
//	}
//	return SUCESS;
//}

int object_interface::GetSubobjectPerformID(int tid)
{
	const subobject_template *pTemplate = subobject_template_manager::GetInstance().Get(tid);
	if (!pTemplate)
	{
		return 0;
	}
	return pTemplate->perform_id;
}

int object_interface::GetServerID()
{
	return gmatrix::GetInstance().GetServerID();
}

unsigned char object_interface::GetRealServerID()
{
	return gmatrix::GetInstance().GetRealServerID();
}

unsigned char object_interface::GetServerMode()
{
	return gmatrix::GetInstance().GetServerMode();
}

const std::string object_interface::GetServerName()
{
	return gmatrix::GetInstance().GetServerName();
}

const std::string object_interface::GetServerDataEdition()
{
	return gmatrix::GetInstance().GetServerDataEdition();
}

SERVER_STATUS object_interface::GetServerStatus()
{
	return gmatrix::GetInstance().GetServerStatus();
}

const std::vector<instance_info_t>& object_interface::GetServerInstanceInfo()
{
	return gmatrix::GetInstance().GetServerInstanceInfo();
}

void object_interface::GetServerGlobalWorldScene(PB::ipt_line_register_info& scenes)
{
	gmatrix::GetInstance().GetServerGlobalWorldScene(scenes);
}

void object_interface::StopQinggong(bool rush)
{
	_imp->StopQinggong(rush);
}

int object_interface::GetPrevQinggong(int skill_id)
{
	for (int i = EXPQINGGONGTYPE_NORMAL_START + 1; i <= EXPQINGGONGTYPE_NORMAL_END; ++i)
	{
		if (SERVER_CONFIG.id_skill_qinggong[i] == skill_id)
		{
			return SERVER_CONFIG.id_skill_qinggong[i - 1];
		}
	}
	return 0;
	//return _imp->GetPrevQinggong(skill_id);
}

void object_interface::SendStopQinggong(unsigned char type)
{
	_imp->Runner()->stop_qinggong(type);
}

/*
int object_interface::GetRushSkillId(char type)
{
	if (type == WMSKILL::RS_NORMAL)
		return SERVER_CONFIG.id_skill_qinggong[EXPQINGGONGTYPE_ACC_RUN];
	else if (type == WMSKILL::RS_ON_MOUNT)
		return SERVER_CONFIG.id_skill_qinggong[EXPQINGGONGTYPE_ACC_RUN_ONMOUNT];
	else
		return 0;
}
*/


float object_interface::GetRunSpeed() const
{
	return _imp->GetSpeed(true);
}


void object_interface::GetToGround()
{
	_imp->GetToGround();
}

object_shell::object_shell(ruid_t player_id)
{
	BEGIN_TLA;
	XID player;
	MAKE_XID(player, player_id);
	_player = gmatrix::GetInstance().FindPlayer(player);
	if (!_player)
	{
		return;
	}
	mutex_spinlock(&_player->spinlock);
	if (!_player->IsActive() || _player->ID.id != player_id || !check_player_state(_player, player))
	{
		mutex_spinunlock(&_player->spinlock);
		_player = NULL;
		return;
	}
	_oif._imp = (gplayer_imp *)_player->imp;
}

object_shell::~object_shell()
{
	if (_player)
	{
		mutex_spinunlock(&_player->spinlock);
	}
	END_TLA;
}

object_interface& object_shell::GetInterface()
{
	return _oif;
}

void object_interface::DecExp(long long)
{
}

void object_interface::SendClientSelfCombo(unsigned char combo)
{
	_imp->Runner()->self_combo(combo);
}

void object_interface::SendClientSelfHit(int hit, int time)
{
	_imp->Runner()->self_hit(hit, time);
}

void object_interface::SendClientDragPoint(float speed, A3DVECTOR3& pos, float dis_min, float dis_max, float body_size)
{
	_imp->Runner()->drag_point(speed, pos, dis_min, dis_max, body_size);
}

void object_interface::SendClientDragLine(float speed, A3DVECTOR3& pos, int dir, float width, float dis_min, float dis_max, float body_size)
{
	_imp->Runner()->drag_line(speed, pos, dir, width, dis_min, dis_max, body_size);
}

void object_interface::SendClientDragRemove()
{
	_imp->Runner()->drag_remove();
}

A3DVECTOR3& object_interface::RotateVector(A3DVECTOR3& v, float sina, float cosa)
{
	float x = v.x;
	float z = v.z;
	v.x = x * cosa + z * sina;
	v.z = z * cosa - x * sina;
	return v;
}

float object_interface::sinf(int n)
{
	return __sinf(n);
}
float object_interface::cosf(int n)
{
	return __cosf(n);
}

level_t object_interface::GetJobLevel(tid_t tid) const
{
	return 0;
}

bool object_interface::IsObjectStateSet(object_state_t st) const
{
	return (_imp->Parent()->object_state & st);
}

void object_interface::UpdateObjectState(object_state_t st, bool set, bool broadcast)
{
	object_state_t old = _imp->Parent()->object_state;
	if (set)
	{
		_imp->Parent()->SetObjectState(st);
	}
	else
	{
		_imp->Parent()->ClrObjectState(st);
	}
	if (old == _imp->Parent()->object_state)
	{
		return;
	}
	heir_control *heir = _imp->GetHeirControlP();
	if (heir)
	{
		heir->OnObjectStateChange(st, set);
	}
	if (broadcast)
	{
		_imp->Runner()->update_object_state();
	}
	else
	{
		_imp->Runner()->player_change_object_state(_imp->GetParent()->object_state, _imp->GetParent()->object_state2);
	}
}

void object_interface::UpdateObjectState2(object_state_t st, bool set, bool broadcast)
{
	object_state_t old = _imp->Parent()->object_state2;
	if (set)
	{
		_imp->Parent()->SetObjectState2(st);
	}
	else
	{
		_imp->Parent()->ClrObjectState2(st);
	}
	if (old == _imp->Parent()->object_state2)
	{
		return;
	}
	heir_control *heir = _imp->GetHeirControlP();
	if (heir)
	{
		heir->OnObjectState2Change(st, set);
	}
	if (broadcast)
	{
		_imp->Runner()->update_object_state();
	}
	else
	{
		_imp->Runner()->player_change_object_state(_imp->GetParent()->object_state, _imp->GetParent()->object_state2);
	}
}

void object_interface::UpdateObjectServerState(object_state_t st, bool set)
{
	if (set)
	{
		_imp->Parent()->SetObjectServerState(st);
	}
	else
	{
		_imp->Parent()->ClrObjectServerState(st);
	}
}

void object_interface::UpdatePlayerState(object_state_t st, bool set, bool broadcast)
{
	if (IsPlayerClass())
	{
		object_state_t old = _imp->Parent()->object_state;
		if (set)
		{
			_imp->Parent()->SetObjectState(st);
		}
		else
		{
			_imp->Parent()->ClrObjectState(st);
		}
		if (old == _imp->Parent()->object_state)
		{
			return;
		}
		if (broadcast)
		{
			_imp->Runner()->update_object_state();
		}
		else
		{
			_imp->Runner()->player_change_object_state(_imp->GetParent()->object_state, _imp->GetParent()->object_state2);
		}
	}
}

void object_interface::UpdateNpcState(object_state_t st, bool set)
{
	if (IsNPCClass())
	{
		object_state_t old = _imp->Parent()->object_state;
		if (set)
		{
			_imp->Parent()->SetObjectState(st);
		}
		else
		{
			_imp->Parent()->ClrObjectState(st);
		}
		if (old == _imp->Parent()->object_state)
		{
			return;
		}
		_imp->Runner()->update_object_state();
	}
}

void object_interface::DebugShow(const char *str) //用于skill模块, 让策划从客户端看自己填的数值计算结果
{
	GSP::GetInstance().DebugShow(GetLinkIndex(), GetLinkSID(), str);
}

void object_interface::BeginTransform(int tid)
{
	_imp->BeginTransform(tid);
}

void object_interface::EndTransform()
{
	_imp->EndTransform();
}

int object_interface::GetTransformTid()
{
	return _imp->GetTransformTid();
}

bool object_interface::IsTransfromAndActiveSkill(int skill_id) const
{
	return _imp->IsTransfromAndActiveSkill(skill_id);
}

void object_interface::AddAlias(int tid)
{
	//参数其实不需要
	_imp->AddAlias();
}

void object_interface::RemoveAlias()
{
	_imp->RemoveAlias();
}
void object_interface::NotifyLeaveMessage(unsigned short total, unsigned short unread)
{
	//_imp->Runner()->notify_leave_message(total, unread);
}

void object_interface::OnSmallControl()
{
	_imp->OnSmallControl();
}

void object_interface::OnBigControl()
{
	_imp->OnSmallControl();
}

void object_interface::CancelBeSelected()
{
	_imp->GetBuff().ClearSubscibeList(_imp);
}

bool object_interface::IsSutraSkillActive(int skill_id)
{
	return false;
}

void object_interface::SetSignature(const void *buf, unsigned char size)
{
	((gplayer_imp *)_imp)->CmdSetSignature(buf, size);
}

int64_t object_interface::GetCreateFactionTradeMoney()
{
	return SERVER_CONFIG.trade_money_to_create_faction;
}
time_t object_interface::GetSysTime()
{
	return gmatrix::GetInstance().GetSysTime();
}

void object_interface::RunNextPerform()
{
//	_imp->RunNextPerform();
// 格挡反击？ 先注释掉了，需要重新实现
	ASSERT(false);
}

void object_interface::SkillDrop(tid_t drop_table, size_t drop_times, const A3DVECTOR3& pos)
{
	if (!drop_table || !drop_times)
	{
		return;
	}
	ItemVector items;
	item_manager::GetInstance().GetItemFromSpecialDropTables(drop_table, drop_times, items);
	if (!items.empty())
	{
		tid_t npc_tid = 0;
		if (IsNPCClass())
		{
			npc_tid = ((gnpc_imp *)_imp)->GetTid();
		}
		gscene_imp *simp = _imp->GetSceneImp();
		if (simp)
		{
			simp->CreateItemMatter(pos, XID(), XID(), 0, 0, 0, 0, 5, 0, 0, npc_tid, _imp->Parent()->ID, items, false, false);
		}
	}
}

void object_interface::SkillDropSoul(tid_t drop_soul, const A3DVECTOR3& pos)
{
	/*
	if (!drop_soul) return;
	abase::vector<tid_t> souls;
	item_manager::GetInstance().GetSoulFromDropTable(drop_soul, souls);
	if (!souls.empty())
	{
		gscene_imp *simp = _imp->GetSceneImp();
		if (simp)
			simp->CreateSoulMatter(pos, XID(), XID(), 0, 0, 5, _imp->Parent()->ID, souls);
	}
	*/
}

unsigned char object_interface::GetNpcStar()
{
	if (!_imp->IsNPCClass())
	{
		return 0;
	}
	return ((gnpc_imp *)_imp)->GetNpcStar();
}

// 属性获取函数

const char *object_interface::GetPropertyType(size_t index, int *pType)
{
	return property_template::GetPropType(index, pType);
}
int object_interface::GetPropertyIndex(const char *name)
{
	return property_template::GetPropIndex(name);
}
int object_interface::GetPropertyByIndex(size_t index, int& ival, float& fval, int64_t& i64val, double& dval) const
{
	if (_imp->_prop.GetByIndex(index, ival, fval, i64val, dval) < 0)
	{
		__PRINTF ("试图获取不存在的属性Index%zd", index);
	}
	return 0;
}
int object_interface::GetPropertyByName(const char *name, int& ival, float& fval, int64_t& i64val, double& dval) const
{
	if (_imp->_prop.GetByName(name, ival, fval, i64val, dval) < 0)
	{
		__PRINTF ("试图获取不存在的属性%s", name);
	}
	return 0;
}
void object_interface::SetPropertyByIndex(size_t index, int ival, float fval, int64_t i64val, double dval, int mask)
{
	_imp->_prop.SetByIndex(index, ival, fval, i64val, dval, mask);
}

void object_interface::PropertyUpdateWithoutNotify()
{
	_imp->PropertyUpdateWithoutNotify();
}

void object_interface::PropertyUpdateAndNotify()
{
	_imp->PropertyUpdateAndNotify();
}

void object_interface::BeforeBuffChangeProp()
{
	PropertyUpdateAndNotify();
}

void object_interface::OnBuffChangeProp()
{
	//现在buff属性不在这里计算了，这块逻辑废弃
	return;

	if (!_imp->IsPlayerClass())
	{
		return;
	}
	gplayer_imp *pImp = (gplayer_imp *)_imp;
	int fightingcapacity = pImp->GetFightingCapactiy();
	if (pImp->GetProperty().Update())
	{
		//pImp->GetProperty().Constraint();
		int _cur_f = pImp->GetFightingCapactiy();
		int val = 0;
		if (_cur_f != fightingcapacity)
		{
			val = _cur_f - fightingcapacity;
			pImp->GetProperty().MODIFY_PROP_BY_NAME(BuffFightingCapacity, val, creature_prop::CPM_BASE);
			pImp->GetProperty().Update();
			//pImp->GetProperty().Constraint();
		}
		/*
		pImp->GetProperty().GetByName("BuffFightingCapacity", val, fval);
		__PRINTF("属性修正 beff:%d, after:%d, cur:%d, buff:%d\n",
			fightingcapacity, _cur_f, pImp->GetFightingCapactiy(), val);
		*/
		pImp->OnPropUpdate();
		pImp->GetProperty().ClearChangedSet();
	}
}

void object_interface::ModifyPropertyByIndex(size_t index, int offset, float foffset, int64_t i64offset, double doffset, int mask) const //修改属性，增量方式
{
	__PRINTF("修改属性%s,修改值:%d,%f,%ld,%f,mask=%d\n", property_template::GetPropName(index), offset, foffset, i64offset, doffset, mask);
	_imp->_prop.ModifyByIndex(index, offset, foffset, i64offset, foffset, mask);
}

/*
void object_interface::ModifyPropertyByName(const char *name, int offset, float foffset, int64_t i64offset, double doffset, int mask) const //修改属性，增量方式
{
	__PRINTF("修改属性%s,修改值:%d,%f,%ld,%f,mask=%d\n", name, offset, foffset, i64offset, doffset, mask);
	_imp->_prop.ModifyByName(name, offset, foffset, i64offset, doffset, mask);
}
*/

const gproperty *object_interface::GetGProperty() const
{
	return _imp->_prop.GetGProperty();
}

void object_interface::SetInvincebleExcept(bool invinceble, const XID& except)
{
	return _imp->SetInvincebleExcept(invinceble, except);
}

void object_interface::SendControlEnd(char mask, char resume_ctrl_type, unsigned short resume_ctrl_time)
{
	_imp->Runner()->control_end(mask, resume_ctrl_type, resume_ctrl_time);
}

//###################### 下面是作废，但是要编译通过的函数
bool object_interface::CanCatchPet() const
{
	return false;
}
void object_interface::BeCatchedByPlayer(const XID& catcher, int catch_type, float catch_pet_prob)
{
}
void object_interface::BeHijackedByPlayer(const XID& hijacker)
{
}
void object_interface::BeEscortMeet(const XID& id)
{
}
void object_interface::BeEscortMeet(const XID& id, int config_id)
{
}
int  object_interface::IsNeedSeed(int crop_tid)
{
	return -1;
}
int  object_interface::IsNeedCub(int animal_tid)
{
	return -1;
}
int  object_interface::PreFarmSow(const material_t& mt, unsigned char plant_type, farm_crop_t& fct)
{
	return 0;
}
int  object_interface::PostFarmSow(bool is_successful, int seed_id, unsigned char plant_type)
{
	return 0;
}
int  object_interface::FarmHarvestSuccess(int seed_id, unsigned char plant_type)
{
	return 0;
}
void object_interface::OnFarmSteal(bool is_thief, bool is_successful)
{
}
int  object_interface::PreBreedAnimal(const material_t& mt, breed_animal_t& animal_info)
{
	return 0;
}
int  object_interface::PostBreedAnimal(bool result, int animal_tid)
{
	return 0;
}
bool object_interface::CheckAnimalForage(int animal_tid, int forage_tid)
{
	return 0;
}
int  object_interface::PostBreedFieldHarvest(bool result, int animal_tid, int phase)
{
	return 0;
}
void object_interface::OnHomeRestResult(int retcode, unsigned int produce_point_delta, int timestamp)
{
	return;
}
void object_interface::OnHomeAttended(ruid_t dst_player_id, unsigned int servant_tid,
                                      unsigned int inc_vp_produce, unsigned int inc_vp_social, unsigned int inc_force,
                                      exp_t inc_exp, exp_t inc_compensate_exp, unsigned int special_buff_skill_id)
{
}

static std::vector<escort_path_t> t;
const std::vector<escort_path_t>& object_interface::GetServerEscortPath()
{
	return t;
}

unsigned int object_interface::GetCanBeHitMask() const
{
	return _imp->Parent()->can_be_hit_mask;
}

void object_interface::SetCanBeHitMask(unsigned int m)
{
	_imp->SetCanBeHitMask(m);
}

void object_interface::ResetCanBeHitMask()
{
	_imp->ResetCanBeHitMask();
}

void object_interface::ResetChuPozhanTimer(int tick)
{
	_imp->ResetChuPozhanTimer(tick);
}

bool object_interface::IsDying()
{
	return _imp->IsDying();
}

void object_interface::SendSkillPersistMove(int interval_tick)
{
	_imp->LazySendTo(GM_MSG_SKILL_PERSIST_MOVE, _imp->Parent()->ID, interval_tick, interval_tick);
}

bool object_interface::StartParryYingzhi(const A3DVECTOR3& pos)
{
	return _imp->StartParryYingzhi(pos);
}

void object_interface::OnControlChange(int old_type, int new_type)
{
	_imp->OnControlChange(old_type, new_type);
}

void object_interface::OnControlMaskAdd()
{
	_imp->GetFilterMan().EF_ControlStart();
}
void object_interface::OnControlMaskRemove()
{
	_imp->GetFilterMan().EF_ControlEnd();
}

void object_interface::SetInWeak(bool b)
{
	_imp->SetInWeak(b);
}

bool object_interface::IsInWeak() const
{
	return _imp->IsInWeak();
}

int object_interface::GetBodySizeType() const
{
	return _imp->GetBodySizeType();
}

int object_interface::GetActionID()
{
	base_action *act = _imp->GetCurAction();
	if (!act)
	{
		return 0;
	}
	return act->GetID();
}

char object_interface::CanBePushed() const
{
	return _imp->CanBePushed();
}

bool object_interface::CanBeControl(int& control_type, float& control_move_dis_ratio, float& control_move_dis_extra, int time, bool need_check)
{
	return _imp->CanBeControl(control_type, control_move_dis_ratio, control_move_dis_extra, time, need_check);
}
bool object_interface::IsWeaponEquiped() const
{
	return _imp->IsWeaponEquiped();
}

int object_interface::GetInnerType(const ::google::protobuf::Message& msg)
{
	return ::google::protobuf::CommonStructFactory::Instance().GetType(&msg, ::google::protobuf::GN_INNER);
}

bool object_interface::ItemCanGift(item_location_t location, item_index_t index, tid_t tid) const
{
	return _imp->ItemCanGift(location, index, tid);
}

bool object_interface::ItemCanDeal(item_location_t location, item_index_t index, tid_t tid) const
{
	return _imp->ItemCanDeal(location, index, tid);
}

bool object_interface::IsWiningAction() const
{
	return _imp->IsWineingAction();
}
void object_interface::StopWiningAction()
{
	if (_imp->GetParent()->action_type == AT2_WINE)
	{
		_imp->CancelAction();
	}
}

/*
策划不需要，如要以后要用，需要做优化，减少循环次数
void object_interface::dispelMultiFilters(int tid)
{
	const INTEGER_ARRAY_CONFIG *_template = player_template::GetInstance().GetIntegerArray(tid);
	if (!_template)
	{
		return;
	}
	for (int i = 0 ; i < (int)ARRAY_SIZE(_template->data); i ++)
	{
		if (_template->data[i] == 0)
		{
			continue;
		}
		RemoveFilter(_template->data[i] + FILTERID_BEGIN);
	}
}*/

float object_interface::GetXPSkillRate() const
{
	return _imp->GetXPSkillRate();
}

void object_interface::ChangeEscortSpeed(int inc_state_level)
{
	_imp->ChangeEscortSpeed(inc_state_level);
}

/*namespace
{
std::set<int> _jie_yun_skills;
}
void object_interface::LoadJieYun(const std::vector<int>& jieyun_skills)
{
	for (auto it = jieyun_skills.begin(); it != jieyun_skills.end(); ++it)
	{
		_jie_yun_skills.insert(*it);
	}
}

bool object_interface::IsJieYun(int skill_id)
{
	return _jie_yun_skills.find(skill_id) != _jie_yun_skills.end();
}*/

int object_interface::GetSkillCapacity(int skill_id, int sk_lv) const
{
	return SkillConfig::GetInstance().GetSkillCapacity(skill_id, sk_lv, IsPlayerClass()) + _imp->GetDragonbornInfo(DI_SKILL_BREAK_FC, skill_id);
}

int object_interface::GetNewAuctionCostPerYuanBaoWhenOpen() const
{
	return SERVER_CONFIG.auction_cost_money_per_yuanbao;
}

int object_interface::GetNewAuctionLeastCostWhenOpen() const
{
	return SERVER_CONFIG.auction_least_cost_money;
}

int object_interface::GetNewAuctionMaxCostWhenOpen() const
{
	return SERVER_CONFIG.auction_max_cost_money;
}

int object_interface::GetNewAuctionLeastPrice(int tid)const
{
	const item_template *pItemTemplate = item_manager::GetInstance().GetItemTemplate(tid);
	if (pItemTemplate)
	{
		return pItemTemplate->GetLeastNewAuctionOpenPrice();
	}
	return 1;
}

int object_interface::GetNewAucitonCost(int total) const
{
	return _imp->GetAuctionCost(total);
}

void object_interface::IncAuctionTurnover(int turnover, int cur_time, bool notify)
{
	_imp->IncAuctionTurnover(turnover, cur_time, notify);
}

money_t object_interface::IncMoney(int code, int64_t arg, MONEY_TYPE money_type, money_t inc)
{
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return 0;
	}
	return _imp->IncMoney({static_cast<FUNC_CODE>(code), arg}, money_type, inc);
}

bool object_interface::AddBindCash(int code, int64_t arg, int64_t delta)
{
	if (code <= kFuncCodeUnknown || code > kFuncCodeMax)
	{
		return false;
	}
	return _imp->AddBindCash({static_cast<FUNC_CODE>(code), arg}, delta);
}

void object_interface::SendPlayerSendRedEnvelope_Re(int retcode)
{
	PB::gp_player_send_redenvelope_result result;
	result.set_retcode(retcode);

	gplayer_imp *pImp = (gplayer_imp *)_imp;
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);
}

void object_interface::SendPlayerDrawRedEnvelope_Re(const PB::db_red_envelope& red_envelope, int64_t money_received, int draw_level, int retcode)
{
	gplayer_imp *pImp = (gplayer_imp *)_imp;
	if (0 == retcode)
	{
		//增加公共计次
		int type = (int)(RED_ENVELOPE_ID_DETACH_ZONEID(red_envelope.info().id()) >> RED_ENVELOPE_INDEX_OFFSET);
		if (type)
		{
			pImp->AddRedEnvelopeDrawCounter(type, draw_level);
		}
	}

	PB::gp_player_draw_redenvelope_result result;
	result.set_retcode(retcode);
	result.set_money_received(money_received);
	result.mutable_redenvelope()->CopyFrom(red_envelope.info());

	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);
}

void object_interface::OnSkillAddon(int skill_id, int skill_level, bool upgrade)
{
}

bool object_interface::IsSkillActived(int skill_id) const
{
	return _imp->IsSkillActived(skill_id);
}

bool object_interface::IsIdipForbidSkill(int skill_id)
{
	return GNET::IsIdipForbidSkill(skill_id);
}

bool object_interface::CanUseRetinueSkill()
{
	return GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRetinue) &&
	       GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRetinueSelect);
}

bool object_interface::CanUseSevenCrimeBuffCastSkill()
{
	return GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice);
}

int object_interface::GetStuntSkill(int master_skill) const
{
	return master_skill;
}

//技能效果的治疗
void object_interface::BeCure(const XID& who, const attacker_info_t& info, float value, bool abs, bool notify_client, int skill_id)
{
	_imp->GetFilterMan().EF_Treat(value);
	if (value <= 0)
	{
		return;
	}

	float hp = 0;
	if (abs)
	{
		hp = AbsIncHP(value, notify_client);
	}
	else
	{
		hp = IncHP(value, notify_client);
	}
	_imp->BeCure(who, info, hp, skill_id);
}

int object_interface::GetCreateArenaTeamCostMoney()
{
	return CREATE_ARENA_GROUP_COST;
}

void object_interface::OnReplyCoupleTour(const PB::ipt_reply_couple_tour& pb)
{
	/*if (!_imp->IsPlayerClass())
	{
		return;
	}
	gplayer_imp *pImp = (gplayer_imp *)_imp;
	pImp->OnReplyCoupleTour(pb);*/
}

void object_interface::AsyncHurt(const XID& target, int64_t damage, int skill_id, bool ignore_prop_dam3)
{
	if (damage <= 0)
	{
		return;
	}
	attack_msg attack;
	_imp->FillAttackMsg(target, attack);
	int flg = ignore_prop_dam3 ? MSG_HURT_MASK_IGNORE_PROP_DAM3 : 0;
	hurt_msg h_msg;
	h_msg.attacker_info = attack.attacker_info;
	h_msg.damage = damage;
	_imp->SendTo2(GM_MSG_HURT, target, skill_id, flg, &h_msg, sizeof(hurt_msg));
}

void object_interface::AsyncHurtSelf(attacker_info_t& atk_info, int64_t dmg, int skill_id, bool ignore_invincible)
{
	if (dmg <= 0)
	{
		return;
	}
	//把消息来源设置成攻击者
	MSG msg;
	int flg = ignore_invincible ? MSG_HURT_MASK_IGNORE_INVINCIBLE : 0;
	hurt_msg h_msg;
	h_msg.attacker_info = atk_info;
	h_msg.damage = dmg;
	BuildMessage2(msg, GM_MSG_HURT, _imp->GetParent()->ID/*target*/, atk_info.attacker/*source*/, _imp->GetParent()->pos, skill_id, flg, &h_msg, sizeof(h_msg));
	gmatrix::GetInstance().SendMessage(msg);
}

void object_interface::SetNoDot(int counter)
{
	_imp->SetNoDot(counter);
}

void object_interface::SetStance(int id)
{
	bool changed = false;
	switch (id)
	{
	case 0:
	{
		if (_imp->GetParent()->CheckObjectState(gobject::STATE_STANCE_1))
		{
			_imp->GetParent()->ClrObjectState(gobject::STATE_STANCE_1);
			changed = true;
		}
	}
	break;
	default:
	{
		if (!_imp->GetParent()->CheckObjectState(gobject::STATE_STANCE_1))
		{
			_imp->GetParent()->SetObjectState(gobject::STATE_STANCE_1);
			changed = true;
		}
	}
	break;
	}
	if (changed)
	{
		_imp->Runner()->update_object_state();
		_imp->OnStanceChange();
	}
}

int object_interface::HasTalent(int talent, int skill) const
{
	return _imp->HasTalent(talent, skill);
}

int object_interface::GetPureBloodedTalent(int talent, int skill) const
{
	return _imp->GetPureBloodedTalent(talent, skill);
}

int object_interface::GetHolyGhostTalent(int hg_id, int talent, int skill) const
{
	return _imp->GetHolyGhostTalent(hg_id, talent, skill);
}

int object_interface::GetPureBloodedCapacity() const
{
	return _imp->GetPureBloodedCapacity();
}
int object_interface::GetTalentCapacity() const
{
	return _imp->GetTalentCapacity();
}

int object_interface::GetMasterCurPhyAtk() const
{
	return _imp->GetMasterCurPhyAtk();
}

int object_interface::GetMasterCurMagAtk() const
{
	return _imp->GetMasterCurMagAtk();
}

int object_interface::GetMasterCurPsychokinesisLevel() const
{
	return _imp->GetMasterCurPsychokinesisLevel();
}

int object_interface::GetMasterCurCDReduLevel() const
{
	return _imp->GetMasterCurCDReduLevel();
}

int object_interface::GetMasterSkillLevel(int skill_id) const
{
	return _imp->GetMasterSkillLevel(skill_id);
}

int object_interface::GetMasterTalentLevel(int talent, int skill) const
{
	return _imp->GetMasterTalentLevel(talent, skill);
}

int object_interface::GetMasterPurebloodTalentLevel(int talent, int skill) const
{
	return _imp->GetMasterPurebloodTalentLevel(talent, skill);
}

int object_interface::GetMasterKotodamaStage(int index) const
{
	return _imp->GetMasterKotodamaStage(index);
}

int object_interface::PercentHp(int percent) const
{
	return _imp->GetHPPercent() >= percent ? 1 : 0;
}
int object_interface::GetPercetHP() const
{
	return _imp->GetHPPercent();
}

float object_interface::RatioHp() const
{
	int64_t hpmax = _imp->GetHPMax();
	if (hpmax <= 0)
	{
		return 1.f;
	}
	float ratio = (double)_imp->GetHP() / hpmax;
	if (ratio > 1.f)
	{
		return 1.f;
	}
	else if (ratio < 0.f)
	{
		return 0.f;
	}
	return ratio;
}

int object_interface::ControlType(int type) const
{
	return _imp->GetControlType() == type ? 1 : 0;
}

bool object_interface::HasGuardSkill(int skill) const
{
	return _imp->HasGuardSkill(skill);
}

bool object_interface::HasRetinueSkill(int skill) const
{
	return _imp->HasRetinueSkill(skill);
}

bool object_interface::CanRetinueActiveBuffSkill(int skill_id) const
{
	return _imp->CanRetinueActiveBuffSkill(skill_id);
}

void object_interface::SetRetinueActiveBuffSkill(int skill_id)
{
	_imp->SetRetinueActiveBuffSkill(skill_id);
}

void object_interface::OnRetinueActiveBuffSkill(int skill_id)
{
	_imp->OnRetinueActiveBuffSkill(skill_id);
}

void object_interface::SendClientObjectMove(A3DVECTOR3& pos, unsigned short move_dir, float speed, bool send_self)
{
	unsigned short face_dir = GetDir();
	unsigned short flags = move_dir == face_dir ? GP_MOVE_WALK : GP_MOVE_WALK | GP_MOVE_DIR_DIFF;
	if (send_self)
	{
		flags |= GP_MOVE_SEND_SELF;
	}
	unsigned short client_speed = speed * 256.0f + 0.5f;
	_imp->Runner()->object_move(pos, flags, move_dir, face_dir, 0, client_speed, 0, 0, 0, 0);
}

void object_interface::SetDragSkillAddSpeed(float s)
{
	_imp->SetDragSkillAddSpeed(s);
}
bool object_interface::CanDragonbornActiveBuffSkill(int skill_id) const
{
	return _imp->CanDragonbornActiveBuffSkill(skill_id);
}

int object_interface::GetGuardPropByIdx(int prop_idx) const
{
	return _imp->GetGuardPropByIdx(prop_idx);
}

int object_interface::GetRetinuePropByIdx(int skill_id, int prop_idx) const
{
	return _imp->GetRetinuePropByIdx(skill_id, prop_idx);
}

int object_interface::GetDragonbornPropByIdx(int skill_id, int prop_idx) const
{
	return _imp->GetDragonbornPropByIdx(skill_id, prop_idx);
}
int object_interface::GetDragonbornBreakLevel() const
{
	return _imp->GetDragonbornInfo(DI_BREAK_LEVEL);
}

int object_interface::GetAIPrioTarget() const
{
	return _imp->GetAIPrioTarget();
}

bool object_interface::CheckRange(const XID& target, float squared_range) const
{
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;
	}
	return info.CheckRange(_imp->Parent(), squared_range);
}

bool object_interface::CheckRangeAndBodySize(const XID& target, float range) const
{
	object_info info;
	if (!gmatrix::GetInstance().QueryObject(target, info))
	{
		return false;
	}
	float squared_range = range + _imp->Parent()->body_size + info.body_size;
	squared_range *= squared_range;
	return info.CheckRange(_imp->Parent(), squared_range);
}

int object_interface::GetWarm() const
{
	int n = GetGProperty()->PROP_NAME_GET(warm);
	if (n < 0)
	{
		n = 0;
	}
	else if (n > 4)
	{
		n = 4;
	}
	return n;
}

int object_interface::GetCool() const
{
	int n = GetGProperty()->PROP_NAME_GET(cool);
	if (n < 0)
	{
		n = 0;
	}
	else if (n > 4)
	{
		n = 4;
	}
	return n;
}

void object_interface::SetCastSkillFlag(int flag)
{
	_imp->SetCastSkillFlag(flag);
}

bool object_interface::IsSkillHeightDiffInRange(float diff) const
{
	int prof = 0;
	if (_imp->IsPlayerClass() || IsPlayerNpc())
	{
		prof = _imp->GetProf();
	}
	return SkillConfig::GetInstance().IsHeightDiffInRange(diff, prof);
}

void object_interface::SetSpeedMin(float a)
{
	_imp->SetSpeedMin(a);
}

void object_interface::SetSpeedMax(float a)
{
	_imp->SetSpeedMax(a);
}

void object_interface::SetMinHP(int64_t a)
{
	_imp->SetMinHPLocked(a);
}

void object_interface::Jump2SubobjPos(int tid, bool remove)
{
	_imp->Jump2SubobjPos(tid, remove);
}

void object_interface::Jump2FarthestSubobjPos(int tid, bool remove, int lantency)
{
	assert(lantency >= 0);
	if ( lantency == 0)
	{
		_imp->Jump2FarthestSubobjPos(tid, remove);
	}
	else
	{
		_imp->LazySendTo2(GM_MSG_JUMP_FARRHEST_SUBOBJECT_POS, _imp->GetParent()->ID, tid, remove, MILLISEC_TO_TICK(lantency));
	}
}

void object_interface::SetFakeReviveRate(float r)
{
	_imp->GetSkill().SetFakeReviveRate(r);
}

void object_interface::AddDamageBlock(int a)
{
	_imp->GetSkill().AddDamageBlock(a);
}

int object_interface::GetCombatNotBeHurtTm() const
{
	return _imp->GetCombatNotBeHurtTm();
}

int object_interface::GetCombatNotDoDmgTm() const
{
	return _imp->GetCombatNotDoDmgTm();
}

int object_interface::KotodamaStage(int skill) const
{
	return _imp->GetKotodamaStage(skill);
}

bool object_interface::IsImmuneMove() const
{
	return WMSKILL::SkillWrapper::IsControlImmuneMove(_imp->GetControlType());
}

void object_interface::CastSkill2Me(const XID& attacker, int skill_id, int skill_level) const
{
	_imp->SendTo2(GM_MSG_CAST_SKILL_2_ME, attacker, skill_id, skill_level);
}

void object_interface::DelayCastSkill2Me(const XID& attacker, int skill_id, int tick) const
{
	_imp->LazySendTo2(GM_MSG_CAST_SKILL_2_ME, attacker, skill_id, 1, tick);
}

void object_interface::CastSkillSelf(const XID& attacker, int skill_id) const
{
	_imp->SendTo2(GM_MSG_ADD_BUFF, attacker, skill_id, 1);
}

void object_interface::CastSkillAtHere(const XID& attacker, int skill_id) const
{
	_imp->SendTo(GM_MSG_CAST_SKILL_AT_HERE, attacker, skill_id);
}

void object_interface::EnterPelt()
{
	_imp->EnterPelt();
}

void object_interface::LeavePelt()
{
	_imp->LeavePelt();
}

void object_interface::Debug_ClientShowFlag(const A3DVECTOR3& pos)
{
	_imp->Debug_ClientShowFlag(pos);
}

bool object_interface::HasEnemyTarget() const
{
	return _imp->HasEnemyTarget();
}

bool object_interface::IsManualDamageSkill(int skill) const
{
	return SkillConfig::GetInstance().IsManualDamageSkill(_imp->GetProf(), skill);
}

void object_interface::SettlePersist(ruid_t from_rid, int filter_id, float rate, bool dispel)
{
	_imp->GetFilterMan().SettlePersist(from_rid, filter_id, rate, dispel);
}

void object_interface::TwinDetach(bool b)
{
	player_twin *t = _imp->GetTwinP();
	if (t != NULL)
	{
		t->Detach(b);
	}
}

void object_interface::TwinSkill(int skill_id, int skill_level)
{
	player_twin *t = _imp->GetTwinP();
	if (t != NULL)
	{
		t->CastSkill(skill_id, skill_level);
	}
}

void object_interface::MechSkill(int skill_id, int skill_level)
{
	player_mech *t = _imp->GetMechP();
	if (t != NULL)
	{
		t->CastSkill(skill_id, skill_level);
	}
}

void object_interface::TwinSkillAtPos(int skill_id, int skill_level, const A3DVECTOR3& pos)
{
	player_twin *t = _imp->GetTwinP();
	if (t != NULL)
	{
		t->CastSkillAtPos(skill_id, skill_level, pos);
	}
}

void object_interface::TwinJumpBack()
{
	player_twin *t = _imp->GetTwinP();
	if (t != NULL)
	{
		t->JumpBack();
	}
}

float object_interface::GetTwinSquaredDistance(bool& has)
{
	player_twin *t = _imp->GetTwinP();
	if (t != NULL)
	{
		return t->GetSquaredDistance(has);
	}
	has = false;
	return 0;
}

void object_interface::CoexistEnd(const XID& target, int buff_id)
{
	MSG msg;
	gobject *pObj = _imp->Parent();
	BuildMessage(msg, GM_MSG_COEXIST_END, target, pObj->ID, pObj->pos, buff_id);
	gmatrix::GetInstance().SendMessage(msg);
}

void object_interface::CoexistDelayAdd(const XID& target, int time_left)
{
	MSG msg;
	gobject *pObj = _imp->Parent();
	time_left /= 5;
	BuildMessage(msg, GM_MSG_COEXIST_ADD, pObj->ID, target/*source*/, pObj->pos, time_left);
	gmatrix::GetInstance().SendMessage(msg, MILLISEC_TO_TICK(3000));
}

int object_interface::GetSelfNewid() const
{
	return _imp->GetParent()->ID.newtype;
}

void object_interface::ChangeModel(int tid, int attacker_newid)
{
	_imp->ChangeModel(tid, attacker_newid);
}

void object_interface::ResetModel()
{
	_imp->ResetModel();
}

void object_interface::TwinBind(bool b)
{
	player_twin *t = _imp->GetTwinP();
	if (t != NULL)
	{
		t->Bind(b);
	}
}

bool object_interface::GetSubobjPos(int tid, A3DVECTOR3& pos) const
{
	return _imp->GetSubobjPos(tid, pos);
}

bool object_interface::CanBless(const XID& target) const
{
	return _imp->CheckCanBless(target);
}

bool object_interface::CanSkillBeyondMaxCoverage(int skill_id)
{
	return SkillConfig::GetInstance().CanBeyondMaxCoverage(skill_id);
}

bool object_interface::IsTwin() const
{
	return _imp->GetParent()->CheckObjectState(gobject::STATE_PLAYER_TWIN);
}

bool object_interface::IsMech() const
{
	return _imp->GetParent()->CheckObjectState(gobject::STATE_PLAYER_MECH);
}

bool object_interface::IsFaker() const
{
	return _imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_FAKER);
}

int object_interface::IsHeir() const
{
	return _imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_HEIR) ? 1 : 0;
}

bool object_interface::IsGhost() const
{
	return _imp->GetParent()->CheckObjectState(gobject::STATE_GHOST);
}
bool object_interface::IsReplisome() const
{
	return _imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_REPLISOME);
}
bool object_interface::IsTwining()const
{
	return _imp->GetParent()->IsTwining();
}


const XID& object_interface::GetFeedBackID() const
{
	if (_imp->GetParent()->CheckObjectState(gobject::STATE_PLAYER_TWIN))
	{
		return ((gplayer_twin *)(_imp->GetParent()))->player_xid;
	}
	else if (_imp->GetParent()->CheckObjectState(gobject::STATE_PLAYER_MECH)
	         || _imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_FAKER)
	         || _imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_HEIR))
	{
		return _imp->GetParent()->master;
	}
	return _imp->Parent()->ID;
}

int object_interface::GetTwinBuffLevel(int id, ruid_t rid) const
{
	return _imp->GetTwinBuffLevel(id, rid);
}

int object_interface::GetTwinBuffTime(int id) const
{
	return _imp->GetTwinBuffTime(id);
}

void object_interface::CorpsAuctionBuyOrder(int cash)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}
	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	pPlayer->GetMall().IncOrderID();
	FuncInfo fi{kFuncCodeCorpsAuction};
	player_mall::GenCheckOrder(pPlayer->GetRoleID(), pPlayer->GetParent()->account.ToStr(),
	                           cash, 0, 0, 0, fi, PB::db_check_order::COST, pPlayer->GetMall().GetMutableCheckOrders());
	pPlayer->GetAutoReward().OnConsume(pPlayer, cash, fi);
	pPlayer->BackNoorderCash(fi, cash);
	SLOG(FORMAT, "CorpsAuctionBuyOrder").P("roleid", pPlayer->GetRoleID()).PS(cash);
}

void object_interface::CorpsAuctionWithdrawCash(int cash, bool by_system)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}
	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	pPlayer->AddFreeCash({kFuncCodeCorpsAuction}, cash);
	SLOG(FORMAT, "CorpsAuctionWithdrawCash").P("roleid", pPlayer->GetRoleID()).PS(cash);

	if (by_system)
	{
		PB::gp_corps_auction_withdraw notify;
		pPlayer->Runner()->QuietSend<S2C::CMD::PBS2C>(notify);
	}
}

void object_interface::HometownSellOrMove(int bind_money, int bind_cash)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}

	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	if (bind_money > 0)
	{
		pPlayer->IncMoney({kFuncCodeHometown}, MT_BIND, bind_money);
		SLOG(FORMAT, "HometownSellOrMove").P("roleid", pPlayer->GetRoleID()).PS(bind_money);
	}

	if (bind_cash > 0)
	{
		pPlayer->AddBindCash({kFuncCodeHometown}, bind_cash);
		SLOG(FORMAT, "HometownSellOrMove").P("roleid", pPlayer->GetRoleID()).PS(bind_cash);
	}
}
void object_interface::ContractHometownSellOrMove(int bind_money, int bind_cash, const std::map<int, int>& repu_info)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}

	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	if (bind_money > 0)
	{
		pPlayer->IncMoney({kFuncCodeContractHome}, MT_BIND, bind_money);
		SLOG(FORMAT, "ContractHometownSellOrMove").P("roleid", pPlayer->GetRoleID()).PS(bind_money);
	}

	if (bind_cash > 0)
	{
		pPlayer->AddBindCash({kFuncCodeContractHome}, bind_cash);
		SLOG(FORMAT, "ContractHometownSellOrMove").P("roleid", pPlayer->GetRoleID()).PS(bind_cash);
	}

	for (auto it = repu_info.begin(); it != repu_info.end(); ++it)
	{
		if (it->second > 0)
		{
			pPlayer->ModifyReputation({kFuncCodeContractHome}, it->first, it->second);
			LOG_TRACE("object_interface::ContractHometownSellOrMove::roleid=" FMT_I64":repu_id=%d:repu_value=%d", pPlayer->GetRoleID(), it->first, it->second);
		}
	}
}
void object_interface::ContractHometownSellObject(int bind_money, int bind_cash, const std::map<int, int>& repu_info)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}

	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	if (bind_money > 0)
	{
		pPlayer->IncMoney({kFuncCodeContractHome}, MT_BIND, bind_money);
		SLOG(FORMAT, "ContractHometownSellObject").P("roleid", pPlayer->GetRoleID()).PS(bind_money);
	}

	if (bind_cash > 0)
	{
		pPlayer->AddBindCash({kFuncCodeContractHome}, bind_cash);
		SLOG(FORMAT, "ContractHometownSellObject").P("roleid", pPlayer->GetRoleID()).PS(bind_cash);
	}

	for (auto it = repu_info.begin(); it != repu_info.end(); ++it)
	{
		if (it->second > 0)
		{
			pPlayer->ModifyReputation({kFuncCodeContractHome}, it->first, it->second);
			LOG_TRACE("object_interface::ContractHometownSellObject::roleid=" FMT_I64":repu_id=%d:repu_value=%d", pPlayer->GetRoleID(), it->first, it->second);
		}
	}
}
void object_interface::ContractHometownDemolish(int bind_money, int bind_cash, const std::map<int, int>& repu_info)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}

	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	if (bind_money > 0)
	{
		pPlayer->IncMoney({kFuncCodeContractHome}, MT_BIND, bind_money);
		SLOG(FORMAT, "ContractHometownDemolish").P("roleid", pPlayer->GetRoleID()).PS(bind_money);
	}

	if (bind_cash > 0)
	{
		pPlayer->AddBindCash({kFuncCodeContractHome}, bind_cash);
		SLOG(FORMAT, "ContractHometownDemolish").P("roleid", pPlayer->GetRoleID()).PS(bind_cash);
	}

	for (auto it = repu_info.begin(); it != repu_info.end(); ++it)
	{
		if (it->second > 0)
		{
			pPlayer->ModifyReputation({kFuncCodeContractHome}, it->first, it->second);
			LOG_TRACE("object_interface::ContractHometownDemolish::roleid=" FMT_I64":repu_id=%d:repu_value=%d", pPlayer->GetRoleID(), it->first, it->second);
		}
	}
}
void object_interface::ContractHometownMove(int bind_money, int bind_cash, const std::map<int, int>& repu_info)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}

	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	if (bind_money > 0)
	{
		pPlayer->IncMoney({kFuncCodeContractHome}, MT_BIND, bind_money);
		SLOG(FORMAT, "ContractHometownMove").P("roleid", pPlayer->GetRoleID()).PS(bind_money);
	}

	if (bind_cash > 0)
	{
		pPlayer->AddBindCash({kFuncCodeContractHome}, bind_cash);
		SLOG(FORMAT, "ContractHometownMove").P("roleid", pPlayer->GetRoleID()).PS(bind_cash);
	}

	for (auto it = repu_info.begin(); it != repu_info.end(); ++it)
	{
		if (it->second > 0)
		{
			pPlayer->ModifyReputation({kFuncCodeContractHome}, it->first, it->second);
			LOG_TRACE("object_interface::ContractHometownMove::roleid=" FMT_I64":repu_id=%d:repu_value=%d", pPlayer->GetRoleID(), it->first, it->second);
		}
	}
}

void object_interface::DealLMFShopMail(int type, int index, int value)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}

	gplayer_imp *pPlayer = (gplayer_imp *)_imp;

	// 增加各种数值
	if (type == (int)LMFSHOP_AWARD_TYPE_FREE)
	{
		pPlayer->GetPlayerLmfshop().AddCredit(value);
	}
	else if (type == (int)LMFSHOP_AWARD_TYPE_MONEY)
	{
		pPlayer->AddBindCash({kFuncCodeLmfshopOpenAward}, value);
	}
	else
	{
		pPlayer->ModifyReputation({kFuncCodeLmfshopOpenAward}, GNET::REPUID_LMFSHOP_RACK_UNLOCK, value);
	}
}

void object_interface::ContractGridDemolish(int bind_money, int bind_cash, const std::map<int, int>& repu_info)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}

	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	if (bind_money > 0)
	{
		pPlayer->IncMoney({kFuncCodeContractHome}, MT_BIND, bind_money);
		SLOG(FORMAT, "ContractGridDemolish").P("roleid", pPlayer->GetRoleID()).PS(bind_money);
	}

	if (bind_cash > 0)
	{
		pPlayer->AddBindCash({kFuncCodeContractHome}, bind_cash);
		SLOG(FORMAT, "ContractGridDemolish").P("roleid", pPlayer->GetRoleID()).PS(bind_cash);
	}

	for (auto it = repu_info.begin(); it != repu_info.end(); ++it)
	{
		if (it->second > 0)
		{
			pPlayer->ModifyReputation({kFuncCodeContractHome}, it->first, it->second);
			LOG_TRACE("object_interface::ContractGridDemolish::roleid=" FMT_I64":repu_id=%d:repu_value=%d", pPlayer->GetRoleID(), it->first, it->second);
		}
	}
}
void object_interface::GuardBreedNotify(const PB::ipt_guard_breed_notify& cmd)
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return;
	}
	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	pPlayer->GetGuard().OnOtherBreed(pPlayer, cmd);
}

void object_interface::TwinFeedBackSkill(const checkfeedback_msg& msg, const std::vector<XID> *src)
{
	player_twin *t = _imp->GetTwinP();
	if (t != NULL)
	{
		t->FeedBackSkill(msg, src);
	}
}

void object_interface::MechFeedBackSkill(const checkfeedback_msg& msg, const std::vector<XID> *src)
{
	player_mech *t = _imp->GetMechP();
	if (t != NULL)
	{
		t->FeedBackSkill(msg, src);
	}
}
void object_interface::ReplisomeFeedBackSkill(const checkfeedback_msg& msg, const std::vector<XID> *src)
{

	for (unsigned int i = 0; i < PLAYER_REPLISOME_MAX; ++i)
	{
		player_replisome *t = _imp->GetReplisomeP(i);
		if (t != NULL)
		{
			t->FeedBackSkill(msg, src);
		}
	}
}

void object_interface::CreateFakerMoveForward(int exist_second)
{
	player_prof6 *t = _imp->GetProf6P();
	if (t != NULL)
	{
		t->CreateFakerMoveForward(exist_second);
	}
}

void object_interface::CreateFakerStandSkill(int exist_second)
{
	player_prof6 *t = _imp->GetProf6P();
	if (t != NULL)
	{
		t->CreateFakerStandSkill(exist_second);
	}
}

void object_interface::DeleteFakerMoveForward()
{
	player_prof6 *t = _imp->GetProf6P();
	if (t != NULL)
	{
		t->DeleteFakerMoveForward();
	}
}

void object_interface::DeleteFakerStandSkill()
{
	player_prof6 *t = _imp->GetProf6P();
	if (t != NULL)
	{
		t->DeleteFakerStandSkill();
	}
}

void object_interface::SwapPosWithFakerMove()
{
	player_prof6 *t = _imp->GetProf6P();
	if (t != NULL)
	{
		t->SwapPosWithFakerMove();
	}
}

void object_interface::SwapPosWithFakerStand()
{
	player_prof6 *t = _imp->GetProf6P();
	if (t != NULL)
	{
		t->SwapPosWithFakerStand();
	}
}

bool object_interface::IsLongyuEquip(GDB::itemdata& data, int longyu_id, int equip_tid)
{
	item *a = DBData2Item(data);
	if (!a)
	{
		return false;
	}
	bool b = a->GetTemplateType() == ITT_EQUIPMENT && a->GetTID() == equip_tid && a->GetLongyuID() == longyu_id;
	delete a;
	return b;
}

int64_t object_interface::GetFreeCash() const
{
	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	return pPlayer->GetFreeCash();
}

void object_interface::UseFreeCash(const FuncInfo& fi, size_t delta)
{
	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	pPlayer->UseFreeCash(fi, delta);
}

void object_interface::AddFreeCash(const FuncInfo& fi, size_t delta)
{
	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	pPlayer->PresentCash(fi, delta);
}

int64_t object_interface::GetBindCash() const
{
	gplayer_imp *pimp = (gplayer_imp *)_imp;
	return pimp->GetBindCash();
}

void object_interface::UseBindCash(const FuncInfo& fi, size_t delta)
{
	gplayer_imp *pimp = (gplayer_imp *)_imp;
	pimp->UseBindCash(fi, delta);
}

GDB::GMailInterface *object_interface::GetMailIf()
{
	if (!_imp || !_imp->IsPlayerClass())
	{
		return NULL;
	}
	gplayer_imp *pPlayer = (gplayer_imp *)_imp;
	return pPlayer->GetMailIf();
}

int object_interface::GetChessTid() const
{
	if (!_imp || !_imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_CHESS))
	{
		return 0;
	}
	gplayer_chess_imp *p = (gplayer_chess_imp *)_imp;
	return p->GetChessTid();
}

int object_interface::GetChessCamp() const
{
	if (!_imp || !_imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_CHESS))
	{
		return 0;
	}
	gplayer_chess_imp *p = (gplayer_chess_imp *)_imp;
	return chess_config::GetInstance().GetCampByChess(p->GetChessTid());
}

int object_interface::GetCrimeSwordLevel(int sword_id) const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetSwordLevel(sword_id);
}

int object_interface::GetCrimePower(int crime_type) const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetCrimePower(crime_type);
}

int object_interface::GetCrimeNum(int crime_type) const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetCrimeNum(crime_type);
}

int object_interface::GetCrimeMainSwordLevel() const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetMainSwordLevel();
}

int object_interface::GetCrimeSacrificeAddSkillCrit() const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetSacrificeAddSkillCrit();
}

int object_interface::GetCrimeSacrificeAddSkillCritRedu() const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetSacrificeAddSkillCritRedu();
}

int object_interface::GetCrimeSacrificeAddSkillDam() const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetSacrificeAddSkillDam();
}

int object_interface::GetCrimeSacrificeAddBuffSkillCrit() const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetSacrificeAddBuffSkillCrit();
}

int object_interface::GetCrimeSacrificeAddBuffSkillCritRedu() const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetSacrificeAddBuffSkillCritRedu();
}

int object_interface::GetCrimeSacrificeAddBuffSkillDam() const
{
	if (!_imp)
	{
		return 0;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return 0;
	}
	return c->GetSacrificeAddBuffSkillDam();
}

void object_interface::TryCrimeCostSkillCD(int& cooltime) const
{
	if (!_imp)
	{
		return;
	}
	player_seven_crime *c = _imp->GetSevenCrimeP();
	if (!c)
	{
		return;
	}
	c->TryCostSkillCD(cooltime);
}

void object_interface::AddChessMPRecover(int a)
{
	if (!_imp || !_imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_CHESS))
	{
		return;
	}
	gplayer_chess_imp *p = (gplayer_chess_imp *)_imp;
	p->AddMPRecover(a);
}

bool object_interface::MechCompose()
{
	player_mech *m = _imp->GetMechP();
	if (!m)
	{
		return false;
	}
	return m->Compose();
}

void object_interface::MechDecompose(bool is_switch_map)
{
	player_mech *m = _imp->GetMechP();
	if (!m)
	{
		return;
	}
	m->Decompose(is_switch_map);
}

bool object_interface::NeedDamageWeak()
{
	if (_imp->GetParent()->world_tid == GLOBAL_WORLD_TID)
	{
		return false;
	}
	return _imp->GetSceneImp()->GetLimit().damage_weak;
}

void object_interface::MechJumpSkill(int angle, float dis, int skill_id)
{
	player_mech *m = _imp->GetMechP();
	if (!m)
	{
		return;
	}
	m->JumpSkill(angle, dis, skill_id);
}

void object_interface::MechSkillToPos(int angle, float dis, int skill_id)
{
	player_mech *m = _imp->GetMechP();
	if (!m)
	{
		return;
	}
	m->SkillToPos(angle, dis, skill_id);
}

void object_interface::MechSkillToPos2(const A3DVECTOR3& pos, int skill_id)
{
	player_mech *m = _imp->GetMechP();
	if (!m)
	{
		return;
	}
	m->SkillToPos2(pos, skill_id);
}

bool object_interface::IsPubg() const
{
	return _imp->IsPubg();
}

void object_interface::NearDeath(bool b)
{
	_imp->NearDeath(b);
}

int object_interface::GetNearDeathNum() const
{
	return _imp->GetNearDeathNum();
}

int object_interface::GetPubgWeaponType() const
{
	return _imp->GetPubgWeaponType();
}

int object_interface::GetTeamMemberCount() const
{
	return _imp->GetTeamMemberCount();
}

int object_interface::GetGfxModify(uint64_t attacker_gfx_modify_mask, int gfx) const
{
	return SkillConfig::GetInstance().GetGfxModify(attacker_gfx_modify_mask, gfx);
}

uint64_t object_interface::GetGfxModifyMask() const
{
	return _imp->GetFashionGfxModifyMask();
}

int object_interface::GetLivingMs() const
{
	return _imp->GetLivingMs();
}

int object_interface::CalcFearReduTm(int tm)
{
	return _imp->GetSkill().CalcFearReduTm(tm);
}

int object_interface::CalcDeludedReduTm(int tm)
{
	return _imp->GetSkill().CalcDeludedReduTm(tm);
}

void object_interface::RecordHpPosDir(int max_tm)
{
	//if (_imp->GetProf6P())
	//{
	_imp->GetProf6()->RecordHpPosDir(max_tm);
	//}
}

void object_interface::NextSkillWithoutCD(int min_ms, int filter_id)
{
	_imp->GetSkill().NextSkillWithoutCD(min_ms, filter_id);
}

bool object_interface::IsRuneActiveSkill(int skill_id)
{
	if (_imp->GetStuntP())
	{
		return _imp->GetStuntP()->IsActived(skill_id);
	}
	return false;
}

int object_interface::IsEbdure(filter_typemask_t type_mask) const
{
	if (IsTypeMaskExist(type_mask))
	{
		return 1;
	}
	return 0;
}

void object_interface::UseHpPosDir(bool cost_hp, bool use_pos_dir)
{
	//if (_imp->GetProf6P())
	//{
	_imp->GetProf6()->UseHpPosDir(cost_hp, use_pos_dir);
	//}
}

void object_interface::PubgSafeRandTeleport()
{
	if (_imp->GetPubgP())
	{
		_imp->GetPubgP()->SafeRandTeleport(_imp);
	}
}

void object_interface::SoulChildDelete(ruid_t intimate_id)
{
	_imp->SoulChildDelete(intimate_id);
}

bool object_interface::IsMale() const
{
	return !_imp->IsObjectFemale();
}

void object_interface::SendTargetAddMP(const XID& target, int m)
{
	_imp->SendTo(GM_MSG_ADD_MP, target, m);
}

bool object_interface::CheckProfTalentSkill(int skill_id) const
{
	if (!_imp)
	{
		return false;
	}
	return player_talent_config::GetInstance().IsProfTalentSkill(_imp->GetProf(), skill_id);
}

void object_interface::CloseTalent()
{
	player_talent_manager *p = _imp->GetTalentP();
	if (p)
	{
		p->Close();
	}
}

void object_interface::OpenTalent()
{
	player_talent_manager *p = _imp->GetTalentP();
	if (p)
	{
		p->Open();
	}
}

void object_interface::SetIgnoreSomeDamageProp(int b, int ratio)
{
	_imp->GetSkill().SetIgnoreSomeDamageProp(b, ratio);
}

void object_interface::ClrIgnoreSomeDamageProp()
{
	_imp->GetSkill().ClrIgnoreSomeDamageProp();
}

void object_interface::AddSymbioticNewID_A(int newid)
{
	_imp->GetSkill().AddSymbioticNewID_A(newid);
}

void object_interface::RemoveSymbioticNewID_A(int newid)
{
	_imp->GetSkill().RemoveSymbioticNewID_A(newid);
}

void object_interface::AddSymbioticNewID_B(int newid)
{
	_imp->GetSkill().AddSymbioticNewID_B(newid);
}

void object_interface::RemoveSymbioticNewID_B(int newid)
{
	_imp->GetSkill().RemoveSymbioticNewID_B(newid);
}

void object_interface::CheckTargetHasSymbioticA(const XID& target, int self_buff_id)
{
	MSG msg;
	gobject *pObj = _imp->Parent();
	BuildMessage2(msg, GM_MSG_SYMBIOTIC_CHECK, target, pObj->ID, pObj->pos, 1, self_buff_id);
	gmatrix::GetInstance().SendMessage(msg);
}

void object_interface::CheckTargetHasSymbioticB(const XID& target, int self_buff_id)
{
	MSG msg;
	gobject *pObj = _imp->Parent();
	BuildMessage2(msg, GM_MSG_SYMBIOTIC_CHECK, target, pObj->ID, pObj->pos, 2, self_buff_id);
	gmatrix::GetInstance().SendMessage(msg);
}
void object_interface::SetCage(bool status, A3DVECTOR3 center_pos, float radius )
{
	_imp->SetCage(status, center_pos, radius);
}
void  object_interface::SwapPosWithCagePos() const
{
	_imp->SwapPosWithCagePos();
}
void  object_interface::ReplisomeSkill(int skill, int skill_level) const
{
	for (unsigned int i = 0; i < PLAYER_REPLISOME_MAX; ++i)
	{
		player_replisome *t = _imp->GetReplisomeP(i);
		if (t != NULL)
		{
			t->CastSkill(skill, skill_level);
		}
	}
}
int object_interface::GetReplisomeBuffLevel(int id, ruid_t rid) const
{
	return _imp->GetReplisomeBuffLevel(id, rid);
}
int  object_interface::GetReplisomeBuffTime(int id) const
{
	return _imp->GetReplisomeBuffTime(id);
}
unsigned int object_interface::TryCreateReplisome()
{
	return _imp->TryCreateReplisome();
}
void object_interface::TryDeleteReplisome(unsigned int index)
{
	_imp->TryDeleteReplisome(index);
}
int object_interface::GetPlayerReplisomeCount() const
{
	return _imp->GetPlayerReplisomeCount();
}
void object_interface::SetRelisomeDamRatio(int ratio)
{
	GetSkillWrapper().SetReplisomeDamageRatio(ratio);
}
void object_interface::GetReplisomeDamageRatio()
{
	GetSkillWrapper().GetReplisomeDamageRatio();
}
bool object_interface::IsForbidMP14Obtain()
{
	return _imp->GetParent()->IsForbidMP14Obtain();
}
void object_interface::SealDiet(bool set)
{
	if (IsPlayerClass())
	{
		auto& _state = ((gplayer_imp *)_imp)->GetState();
		if (set)
		{
			_state.SetSealDiet();
		}
		else
		{
			_state.ClrSealDiet();
		}
	}
}
bool object_interface::IsForbidPassiveLongyu()
{
	return _imp->GetParent()->IsForBidPassiveLongyu();
}
bool object_interface::IsForbidPassiveTalent()
{
	return _imp->GetParent()->IsForbidPassiveTalent();
}
size_t object_interface::GetSubObjectCount(int tid) const
{
	return _imp->GetSubObjectCount(tid);
}

void  object_interface::TryCreateDog(int skill_level)
{
	_imp->TryCreateDog(skill_level);
}
void object_interface::TryDeleteDog()
{
	_imp->TryDeleteDog();
}
bool object_interface::IsDog() const
{
	return _imp->GetParent()->CheckObjectState2(gobject::STATE2_PLAYER_DOG);
}

int object_interface::GetDogBuffLevel(int id, ruid_t rid) const
{
	return _imp->GetDogBuffLevel(id, rid);
}
int  object_interface::GetDogBuffTime(int id) const
{
	return _imp->GetDogBuffTime(id);
}
void object_interface::TryRecallSubobject(int tid, bool is_destory)
{
	return _imp->TryRecallSubobject(tid, is_destory);
}
bool object_interface::GetNearbyValidPos(A3DVECTOR3& pos)
{
	return _imp->GetSceneImp()->GetNearbyValidPos(pos);
}
void object_interface::StartRecordData(int data_type, uint64_t from_rid)
{
	return _imp->GetSkill().StartRecordData(data_type, from_rid);
}
void object_interface::FinishRecordData(int data_type, uint64_t from_rid)
{
	return _imp->GetSkill().FinishRecordData(data_type, from_rid);
}
uint64_t object_interface::GetRecordData(int data_type, uint64_t from_rid) const
{
	return _imp->GetSkill().GetRecordData(data_type, from_rid);
}
void object_interface::TryRecordBuffHistory(int buff_id)
{
	return _imp->TryRecordBuffHistory(buff_id);
}
void object_interface::ClearBuffHistory()
{
	return _imp->ClearBuffHistory();
}
bool object_interface::CheckBuffHistory(int skill_id)
{
	return _imp->CheckBuffHistory(skill_id);
}
int object_interface::GetBuffHistory(int index) const
{
	return _imp->GetBuffHistory(index);
}
void object_interface::OnDetachBuff(int buff_id)
{
	return _imp->OnDetachBuff(buff_id);
}
void object_interface::ReduceShieldInstant(int value)
{
	return _imp->_filter_man.EF_ReduceShieldInstant(value);
}

bool object_interface::IsRoamCategory(int item_tid)
{
	return FAuctionCfgMgr::Instance().IsRoamCategory(item_tid);
}

const std::set<int>& object_interface::GetForbidBuffPool() const
{
	return _imp->_filter_man.GetAllPassiveLongyuBuffId();
}

void object_interface::DebugSay(const std::string& msg) const
{
	return _imp->DebugUTF8Say(msg.c_str());
}

void object_interface::DebugNotifyClientResetSkill(std::function<std::string()> msg_getter) const
{
	if (!SERVER_CONFIG.debug_notify_client_reset_skill)
	{
		return;
	}
	if (!msg_getter)
	{
		return;
	}
	std::string msg = msg_getter();
	if (msg.size() == 0)
	{
		return;
	}
	DebugSay(msg);
}

void object_interface::ForEachPassiveSkillWithFunctionalLabel(const std::function<void(int skill_id, int level)>& func)
{
	if (!func)
	{
		return;
	}
	
	// 通过SkillWrapper的ForEachSkill遍历所有技能
	GetSkillWrapper().ForEachSkill(*this, [&func](object_interface& player, WMSKILL::Skill* skill) {
		// 检查技能类型是否为被动技能
		if (player.GetSkillWrapper().IsPassiveSkillOf(player, skill))
		{
			int skill_id = 0;
			int level = 0;
			int functional_label = 0;
			player.GetSkillWrapper().ExtractPassiveSkillInfo(skill, skill_id, level, functional_label);
			if (functional_label > 0)
			{
				func(skill_id, level);
			}
		}
	});
}
