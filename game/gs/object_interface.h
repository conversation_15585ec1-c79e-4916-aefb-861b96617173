#ifndef __GS_OBJECT_INTERFACE_H__
#define __GS_OBJECT_INTERFACE_H__

#include "property.h"
#include "attack.h"
#include <vector>
#include <set>
#include <string>
#include <map>
#include <functional>

namespace PB { namespace ipt_guard_breed_notify_NS { class ipt_guard_breed_notify; } using namespace ipt_guard_breed_notify_NS; }
namespace PB { namespace ipt_reply_couple_tour_NS { class ipt_reply_couple_tour; } using namespace ipt_reply_couple_tour_NS; }
namespace PB { namespace ipt_line_register_info_NS { class ipt_line_register_info; } using namespace ipt_line_register_info_NS; }
namespace PB { namespace db_red_envelope_NS { class db_red_envelope; } using namespace db_red_envelope_NS; }

extern float __sinf(int n);
extern float __cosf(int n);

namespace google  { namespace  protobuf { class Message; }; };
namespace abase { class octets; }
struct gplayer;
class gcreature_imp;
class filter;
class gscene_imp;
class item_pet_bedge;
class scene_info;
struct escort_path_t;
struct FuncInfo;
class pet_data;
namespace WMSKILL { class SkillWrapper; }
namespace GNET { class Protocol; struct TxnInfo; }
namespace GDB { struct itemdata; struct shoplog; struct role_data; class GMailInterface;}

struct material_t
{
	int id;
	unsigned short pos;
	unsigned short count;
};

struct farm_crop_t
{
	int seed_id;                           //种子模板ID
	unsigned int grow_duration;            //生长期
	unsigned int product_protect_duration; //采摘保护期
	unsigned int product_life_duration;    //果实保存期
	unsigned char product_reserved;        //果实保留个数
	struct product_t
	{
		unsigned int tid;                  //果实模板ID
		unsigned int default_count;        //默认产量
	};
	std::vector<product_t> products;
};

struct breed_animal_t
{
	int animal_id;                   // 动物模板ID
	unsigned char fence_type;        // 养殖圈类型
	int growold_duration;            // 衰老期
	unsigned char forage_per_minute; // 每分钟消耗的饲料数量
	struct phase_t
	{
		unsigned int max_grow_point;
		struct product_t
		{
			unsigned int tid;           //产物模板ID
			unsigned int default_count; //默认产量
		};
		std::vector<product_t> products;
	};
	std::vector<phase_t> phase_cfg; // 成长阶段设置
};

struct instance_info_t
{
	instance_info_t()
	{
		memset(this,0,sizeof(*this));
	}

	int tid;
	int max_size;
	int capacity;
	unsigned char category;
	unsigned char spec_type;
	unsigned char min_creators;
	size_t spectator_limit;
	unsigned char cannot_return;
	short return_count_limit;
	short return_time_limit;
	unsigned char room_count;
	unsigned char max_players_per_group;
};

struct send_mail_info_params
{
	unsigned short system_new_mail_count;
	unsigned short system_total_mail_count;
	unsigned char system_mail_usage;
	unsigned short player_new_mail_count;
	unsigned short player_total_mail_count;
	unsigned char player_mail_usage;
	unsigned short offline_msg_count;
	unsigned short auction_total;
	unsigned short auction_unread;
	unsigned short corps_auction_total;
	unsigned short corps_auction_unread;
	unsigned short un_recycle_total;
	unsigned short un_recycle_unread;
	unsigned short bouquet_total;
	unsigned short bouquet_unread;
	unsigned short pray_total;
	unsigned short pray_unread;
};

class object_interface
{
	friend class object_shell;

	gcreature_imp* _imp;

public: 
	object_interface();
	object_interface(gcreature_imp* imp);
	object_interface(const object_interface& rhs);
	object_interface& operator=(const object_interface& rhs);
	void Init(gcreature_imp* imp);

	~object_interface();
	void TranslateSendSubObjAttack(XID& xid,attack_msg &msg);//新添加的给子物体添加buff效果的
	void SendDizzyMsg();
public:
//查询属性相关
	gcreature_imp* GetImp();
	gcreature_imp* GetImp() const;
	bool IsPlayerClass() const;
	bool IsNPCClass() const;
	bool IsPetClass() const;
	bool IsPlayerNpc() const;
	bool IsMale() const;
	//属性
	const creature_prop& GetProperty() const;
	const gproperty * GetGProperty() const;
	//技能
	WMSKILL::SkillWrapper& GetSkillWrapper() const;
	int GetCurSkillId() const;//正在释放的技能id
	//查询 
	//查询ID
	const XID& GetID() const;
	// 查询account
	const GNET::user_account* GetAccount() const;
	//是否死亡
	bool IsDead() const;
	//是否在队伍中
	bool IsInTeam() const;
	//队伍id
	ruid_t GetTeamIDID() const;
	//当前目标是否在乱斗模式，不能给友方加buff，因为没有友方
	bool AT_LUANZHAN();
	//是否为队长
	bool IsTeamLeader() const;
	//判断对象目标是否为队友
	bool ObjectIsTeamMember(const XID& object) const;
	//判断队友是不是在附近
	bool IsTeamMemberNear(float range) const;
	//阵营
	unsigned int GetFaction() const;
	//敌对阵营
	unsigned int GetEnemyFaction() const;
	//世界tag
	unsigned short GetWorldTag() const;
	//场景tag
	unsigned short GetSceneTag() const;
	//位置
	const A3DVECTOR3& GetPos() const;
	const A3DVECTOR3 GetClientPos() const;
	const A3DVECTOR3& GetGhostPos() const;
	//方向
	const A3DVECTOR3& GetDirection() const;
	unsigned short GetDir() const;
	void SetDirection(const A3DVECTOR3& d,bool inform=false);
	//地形高度
	float GetTerrainHeight(float x, float z) const;
	//身体大小
	float GetBodySize() const;
	//gm权限
	bool CheckGMPrivilege() const;
	//获得等级
	unsigned char GetLevel() const;
	//获得性别
	unsigned char GetGender() const;
	//获得职业
	unsigned char GetProf() const;
	unsigned char GetTmpProf() const; //FIXME: 临时凑活，去掉
	//获得职业级别
	unsigned char GetProfLevel() const;
	//获得目标
	const XID& GetCurTarget() const;
	//和目标的距离是否太远
	bool CheckRange(const XID& target, float squared_range) const;
	bool CheckRangeAndGetInfo(const XID& target, float squared_range, A3DVECTOR3& pos, int64_t& hp, int64_t& max_hp) const;
	bool CheckRangeAndBodySize(const XID& target, float range) const;
	// 获得VIP等级
	int GetVipLevel() const;

public:
//调整对象属性相关

	static const char* GetPropertyType(size_t index, int *pType);                 // 获得属性的类型pType 0--int 1--float,  返回属性英文名
	static int GetPropertyIndex(const char *name);                                // 获得英文属性名对应的index
	int GetPropertyByIndex(size_t index, int &ival, float& fval, int64_t & i64val, double & dval) const;           // 获取某个属性值
	int GetPropertyByName(const char *name, int &ival, float& fval, int64_t & i64val, double & dval) const;        // 根据属性名称获得值
	void ModifyPropertyByIndex(size_t index, int offset, float foffset, int64_t i64val, double dval, int mask = creature_prop::CPM_CUR) const;    // 修改属性，增量方式
	//void ModifyPropertyByName(const char *name, int offset, float foffset, int64_t i64val, double dval, int mask = creature_prop::CPM_CUR) const; // 修改属性，增量方式
	void SetPropertyByIndex(size_t index, int ival, float fval, int64_t i64val, double dval, int mask = creature_prop::CPM_CUR);	// 设置某个属性值，不可对有计算公式的值进行修改
	void SetPropertyByName(const char *name, int ival, float fval, int64_t i64val, double dval);               // 根据属性名称设置属性值，不可对有计算公式的值操作
	void PropertyUpdateAndNotify();
	void PropertyUpdateWithoutNotify();
	void BeforeBuffChangeProp();
	void OnBuffChangeProp();
	void SetSpeedMin(float a);
	void SetSpeedMax(float a);
	void SetMinHP(int64_t a);

	void SetOverwhelmingMode(bool flag);
public:
//creature相关其他操作
	void FillAttackMsg(const XID& target,attack_msg& attack) const;
	//冷却
	void PrintCoolDown(unsigned short cd_id) const;
	bool TestCoolDown(unsigned short cd_id) const;
	int  TestCoolDownCount(unsigned short cd_id) const;
	void SetCoolDown(unsigned short cd_id, size_t cd_time, bool by_skill_cast = false, int skill_id = 0,int cd_count = 0);
	void ClrCoolDown(unsigned short cd_id);
	void CostCoolDown(unsigned short cd_id, size_t cost_time);
	void AddCoolDown(unsigned short cd_id, size_t cost_time);
	void ClrActiveSkillCoolDown();
	void ClrManualSkillCoolDown();
	void ScaleSkillCoolDown(float rate);
	void ScaleManualSkillCoolDown(float rate, int min_cost, int except_skill_id);
	void PauseManualSkillCoolDown(int ms);
	void StopPauseManualSkillCoolDown();
	void ActiveUpgradeSkillCoolDown();
	void SetDelayBoomProess(int proess);
	void SetDelayAttackProess(int proess);
	void SetCoolDownByEffectimp(unsigned cd_id,size_t cd_time);

	//增减hp mp
	void SetHP(int64_t hp);
	float IncHP(int64_t hp,bool notify_client);		//考虑治疗增效的治疗
	float AbsIncHP(int64_t hp,bool notify_client);	//增加hp,绝对值
	void DecHP(int64_t hp);

	void SetMP(int mp);
	void IncMP(int mp);
	bool DecMP(int mp);
	bool UseMP(int mp);

	int  GetMP2() const;
	void SetMP2(int mp);
	void IncMP2(int mp);
	bool DecMP2(int mp);
	int GetMP2Max() const;

	int  GetMP3() const;
	void SetMP3(int mp);
	void IncMP3(int mp);
	bool DecMP3(int mp);
	int GetMP3Max() const;

	int  GetMP4() const;
	void SetMP4(int mp);
	void IncMP4(int mp);
	bool DecMP4(int mp);
	int GetMP4Max() const;
	void UpdateMP4();

	bool IsMPUnlimit();
	int64_t GetHPMax() const;
	int GetMPMax() const;

	int64_t  GetHP() const;
	int  GetMP() const;
	int  GetXP() const;
	void SetXP(int xp);
	void IncXP(int xp);
	void DecXP(int xp);

	float IncVP(VIGOR_TYPE vp_type,float vp);
	float DecVP(VIGOR_TYPE vp_type,float vp);

	void DecSP(float delta);
	//技能
	void Enchant(const XID& target,attack_msg& msg,int millisec_delay = 0);
	void EnchantZombie(const XID& target,attack_msg& msg);
	//给位置为pos半径为radius内的目标发送enchant消息
	void RegionEnchant1(std::vector<int>&,const A3DVECTOR3& pos,float radius,attack_msg& msg,size_t max_count,std::vector<XID>& idlist,const XID* target = NULL,bool damage_delay=false,int millisec_delay = 0,bool rand_select=false,ruid_t team_id=0,const std::set<ruid_t>& exclude=std::set<ruid_t>());//球
	//给位置为pos方向为direction长度为length的半径为radius的柱形区域发送enchant消息
	void RegionEnchant2(std::vector<int>&,const A3DVECTOR3& pos,const A3DVECTOR3& direction,float radius,float length,attack_msg& msg,size_t max_count,std::vector<XID>& idlist,const XID* target = NULL,bool damage_delay=false,int millisec_delay = 0,ruid_t team_id=0,const std::set<ruid_t>& exclude=std::set<ruid_t>());//柱
	//为位置pos方向为direction角度为angle半径为radius的扇形发enchant消息
	void RegionEnchant3(std::vector<int>&,const A3DVECTOR3& pos,const A3DVECTOR3& direction,float cos_half_angle,float radius,attack_msg& msg,size_t max_count,std::vector<XID>& idlist,const XID* target = NULL,bool damage_delay=false,int millisec_delay = 0,ruid_t team_id=0,const std::set<ruid_t>& exclude=std::set<ruid_t>());//椎
	//给位置为pos外环半径为outside_radius，内环半径为inside_radius内的目标发送enchant消息
	void RegionEnchant4(std::vector<int>&,const A3DVECTOR3& pos,float outside_radius,float inside_radius,attack_msg& msg,size_t max_count,std::vector<XID>& idlist,const XID* target = NULL,bool damage_delay=false,int millisec_delay = 0,bool rand_select=false,ruid_t team_id=0,const std::set<ruid_t>& exclude=std::set<ruid_t>());//球
	//给位置为pos半径为radius内的目标发送enchant消息 串行
	void RegionEnchant5(const A3DVECTOR3& pos,float radius,attack_msg& msg,size_t max_count,std::vector<XID>& idlist,const XID& target, int millisec_delay);//球
	void TeamEnchant(attack_msg& msg,float range,bool exclude_self,bool norangelimit);
	void DecExp(long long);

	void BeHurt(const XID& who, const attacker_info_t& info, float damage, int skill_id, bool ignore_invincible = false, bool ignore_prop_dam3 = false);
	float DoDamage(const XID& who,const attack_msg& msg,float damage,unsigned int flags,const A3DVECTOR3& pos,char prev_control_type,char control_type,int control_time);
	float DoTreat(const XID& who,const attack_msg& msg,float value,unsigned int flags,const A3DVECTOR3& pos,char prev_control_type,char control_type,int control_time);
	void DoEnchant(const XID& who,const attack_msg& msg,int arg,unsigned int flags);
	void SendControlInfo(const attack_msg& msg,char prev_control_type,char control_type,int control_time);
	//位移技能,技能ID,时间(毫秒),目标点
	//void SkillMove(bool redir, A3DVECTOR3& target_pos, unsigned short& direction, bool check_dir_info, bool check_path, bool check_ydiff);
	//bool DirectMove(bool redir,A3DVECTOR3& target_pos,unsigned short& dir,bool nocheck);
	bool SkillMove(A3DVECTOR3& target_pos,unsigned short& direction,bool redir,bool check_path,bool reverse_check,int check_y_diff);
	void GetLastReachablePos(const A3DVECTOR3& start,const A3DVECTOR3& end,A3DVECTOR3& stop_pos,int check_y_diff,bool can_fall);
	//被击退,技能ID,速度,方向,时间(毫秒)
	void BeKnockback(unsigned short skill_id,float speed,const A3DVECTOR3& direction,unsigned short ms_time,unsigned char type);

	void SendAttackFeedBack(const XID& target,const feedback_msg& msg);
	void SendCheckAttackFeedBack(const XID& target,const checkfeedback_msg& msg,int delay_tick);
	void SendMoveAttacker(const XID& target,const A3DVECTOR3& pos,uint64_t tick);

	void ChangeLockEnemyState(unsigned char state);
	unsigned char GetLockEnemyState() const;
	void CancelBeSelected();

	//子物体
	void CreateSubObject(tid_t tid,const A3DVECTOR3& pos,const A3DVECTOR3& dir,float speed,const subobj_env& env,const attack_msg& attack,int life_cycle);
	void DelayCreateSubObject(const create_subobj_delay_t& delay_args,int delay_tick);
	//状态盒子
	void CreateBuffBox(tid_t tid,const A3DVECTOR3& pos);
	//清除所有遥控类子物体
	void ClearRemoteCtrlSubobject(int param);
	//出发所有遥控类子物体
	void TriggerRemoteCtrlSubObject(const subobj_env& env, const attack_msg& attack);
	XID GetRemoteCtrlSubObjectID() const;
	void TriggerSubobj(const XID& xid);

	//filter 相关
	bool IsFilterExist(int id, ruid_t rid = 0) const;
	bool IsTypeMaskExist(filter_typemask_t mask) const;
	void AddFilter(filter* pFilter);
	void RemoveFilter(int id, ruid_t rid = 0);
	void DelayRemoveFilter(int id,size_t delay_ms_time);
	bool ModifyFitler(int id, int ctrlname, void* ctrlval, size_t ctrllen, ruid_t rid) const;
	bool ChangeFilterPolyCount(int id,int op,char times);	// 直接修改filter的叠加次数, 仅为实现弹仓, 慎用!
	void ClearSpecFilter(filter_eventmask_t mask);
	void ClearSpecFilter2(filter_typemask_t mask2, int reason=0);
	void ClearSpecFilter2_Delay(filter_typemask_t mask2, int reason);
	void FilterAdjustDamage(float& damage,const XID& attacker,const attack_msg& msg, int& attack_flag);
	void FilterBeforeAdjustDamage(float& damage,const XID& attacker,const attack_msg& msg, int& attack_flag);
	void RecordFilterTimeout(int tm);

	//action相关
	void ClearNextAction();
	void BreakAction();
	unsigned short GetAction(unsigned short* action_arg) const;
	bool HasAction() const;
	int GetActionID();

	//buff 相关
	void UpdateBuff(unsigned short buff_id,unsigned char buff_level,int buff_endtime,const float *data,
	                size_t len,const abase::octets& from, bool skillfx_prio, ruid_t from_rid, int client_data_1_2);
	void RemoveBuff(unsigned short buff_id, ruid_t from_rid);
	bool HasBuff(unsigned short buff_id);
	void AttachSleepBuff();
	void DeattachSleepBuff();

	// 挑战buff通信接口
	void NotifyChallengeTargetAdd(const XID& caster_id, int buff_id, int damage_reduce);
	void NotifyChallengeTargetRemove(const XID& caster_id);
	void NotifyChallengeTargetHeartbeat(const XID& caster_id, int buff_id, int damage_reduce);

	//表面状态操作
	void IncVisibleState(int state, unsigned char param1);
	void DecVisibleState(int state);
	void ClearVisibleState(int state);
	//立刻广播buff信息, 用于精确时间状态包包含的战斗光效
	void SyncBuffInfo();

	//当前表现形态
	void ChangeShape(unsigned int index,unsigned int shape);
	unsigned int GetShape(unsigned int index) const;

	//策略状态操作
	//void IncActionSeal(int index);
	//void DecActionSeal(int index);
	//void IncActionSeal2(unsigned int mask);
	//void DecActionSeal2(unsigned int mask);
	//int GetActionSeal(int index) const;

	void SetRootSeal(bool isSeal);
	void SetTurnSeal(bool isSeal);
	void SetSilentSeal(bool isSeal);
	void SetSilentAbsoluteSeal(bool isSeal);
	void SetSilentDragonbornSeal(bool isSeal);
	void SetDietSeal(bool isSeal);
	void SetIdleSeal(bool isSeal);
	void SetKotodamaSeal(bool isSeal);

	//控制状态调整
	bool IsMoveStoped() const;
	bool IsInBind() const;

	//当对象被控制了调用这个
	void OnSmallControl();
	void OnBigControl();

public:
//player相关操作
	int GetLinkIndex() const;
	int GetLinkSID() const;
	bool SendProtocol(GNET::Protocol* p);
	//回城
	bool CanReturnToTown() const;
	void ReturnToTown();
	//传送回复活点
	void TranspotToRevivePos();
	//大地图传送
	void TranspotTo(unsigned short to);
	//给客户端发送的协议操作
	void SendPerformSkill(
		unsigned short skill_id,         // P3: 技能id
		unsigned char skill_level,       // P4: 技能级别
		unsigned char attack_stage,      // P5: 过程索引
		unsigned short ms_time,          // P6: 持续时间
		unsigned char extra_data,        // P7: 附加数据掩码 0x01-附加目标对象ID列表; 0x02-附加位移后坐标; 0x04-附加位移角色朝向; 0x20-附加目标点坐标
		unsigned short target_count,     // P8: 目标个数
		const XID* target_list,          // P9: 目标id列表
		unsigned short direction,        // P10: 位移过程中保持的方向
		unsigned char cycle_count,       // P11: 循环过程记数，(extra_data&0x08)==true时生效。
		const A3DVECTOR3& target_pos,    // P14: 技能目标点坐标
		char range_type,
		float affect_radius,
		float affect_radius2,
		float affect_length,
		float affect_angle,
		const A3DVECTOR3& move2pos,	//位移目标点，非瞬移有效
		const XID& prior_target
	);
	void SendSubobjectTakeEffect(
		unsigned short perform_id,       // P1: 执行的技能段id
		const A3DVECTOR3& pos,           // P3: 产生效果的位置
		unsigned char target_count,      // P4: 相关的目标数
		const XID* target_list,          // P5: 相关的目标id表
		char range_type,
		float affect_radius,
		float affect_radius2,
		float affect_length,
		float affect_angle
	);
	void SendRunPerform(
		unsigned short perform_id,       // P1: 执行的技能段id
		unsigned short ms_time           // P2: 执行持续时间
	);
	void SendBindThrowStart(
		unsigned short skill_id,         // P1: 投技技能id
		const XID& thrower               // P2: 投掷者
	);
	void SendBindThrowStop(
		unsigned short skill_id,         // P1: 投技技能id
		const XID& thrower,              // P2: 投掷者
		const A3DVECTOR3& pos,           // p3: 投技结束后扔到的位置
		unsigned short dir,
		unsigned char type,              // p4: 投技结束后扔开的姿态: 击飞/击溃/击倒
		unsigned short time              // p5: 投技结束后扔开的持续时间
	);
	void SendMailInfo(send_mail_info_params& params);

	size_t MailAddItem(unsigned short mail_id, unsigned char category, const GDB::itemdata& data, int common_pb_mail_type = 0); //返回值代表拿出来多少个
	uint64_t IncBindCashFromItemData(unsigned short mail_id, unsigned char category, const GDB::itemdata& data);
	void MailModifyReputation(unsigned short mail_id, unsigned char category, int repu_id, int offset);
	bool CanRecvDaily() const;	//是否可以接收物品
	void ServerGetAttachment(unsigned short mail_id, int box_idx); // 服务器自动取邮件附件，主要用于从DS到GS转移物品
	//物品是否可以邮件交易
	unsigned int GetItemRecommandPrice(int itemid) const;
	int ItemCanMail(int item_id, int item_count, item_location_t location, item_index_t index) const;
	int MailCanGetMore() const;
	int MailCanGet(int count) const;

	//拍卖行相关
	void GetAuctionAttachment(int mailid);
	money_t GetAuctionShopLevelUpCost(int level);
	bool SystemMailBoxFull() const;
	bool AuctionMailBoxFull() const;
	bool MailAuctionProcess(GNET::Protocol* p);
	bool AuctionFull() const;
	int CheckAuctionBuy() const;
	size_t GetBackpackEmptySlot() const;

	//发起事务，暂扣所需金钱或物品
	int TxnExec(int reason, const FuncInfo& fi,unsigned int money_type_mask,money_t money,money_t bind_money,unsigned int cash_type_mask,int cash,int bind_cash,item_location_t& location,int item_index,int item_count,int item_tid,txn_id_t& txn_id, int repu_id = 0, int repu_count = 0);
	//事务返回，扣除或归还金钱/物品
	int TxnRet(txn_id_t txn_id,int txn_result,int ret_code = 0);
	static int TxnRet(ruid_t roleid, txn_id_t txn_id,int txn_result,int ret_code);

	bool DecItem(int code, int64_t arg, int tid,size_t count);

	//void dispelMultiFilters(int tid);
	static void LoadBindSkills(unsigned short skill_id, const std::vector<int>& bind_skills);
	static bool CanMoveSkill(unsigned short skill_id);
	void OnSkillLearnTo(unsigned short skill_id,unsigned char skill_level,bool notify_client = true,std::map<int,int>* result = NULL);
	void SendLearnSkill(unsigned short skill_id,unsigned char skill_level);
	bool IsSkillHeightDiffInRange(float diff) const;
	//bool OnSellCash(int cash_onsale, int cash_serial);
	//bool OnBuyCash(int cash);

	//店铺上架成就
	void AchivementAuctionOpen();

	//孩子强制离婚
	void ChildDivorceForce(uint64_t guid, uint64_t spouse_guid);

	//用户id 
	int GetUserID() const;
	//当前称号ID
	int GetCurTitle() const;
	//角色id
	ruid_t GetRoleID() const;
	//名字
	const abase::octets* GetName() const;
	//配偶ID
	int GetSpouseID() const;
	//配偶名字
	const abase::octets* GetSpouseName() const;
	//是否有家族
	bool IsInFamily() const;
	//家族ID
	int GetFamilyID() const;
	//家族名字
	const abase::octets* GetFamilyName() const;
	//是否有帮派
	bool IsInMafia() const;
	//帮派ID
	int GetMafiaID() const;
	//帮派名字
	const abase::octets* GetMafiaName() const;
	//声望
	int GetReputation(int index) const;

	//经验
	int64_t GetCurExp() const;
	void IncExp(int64_t exp, int skill_id, int skill_level);
	void IncWineExp(int64_t exp);
	void DecProfExp(int code, size_t exp);
	void IncProfExp(int code, size_t exp);
	void EnhanceExpRatio(int type, float value);

	//变身
	void BeginTransform(int tid);
	void EndTransform();
	int GetTransformTid();//返回当前的变身模板id，未变身则返回0
	bool IsTransfromAndActiveSkill(int skill_id) const;//处于变身并且这个技能在变身模板里

	void AddAlias(int tid);
	void RemoveAlias();
	void NotifyLeaveMessage(unsigned short total, unsigned short unread);

    int GetNewAuctionCostPerYuanBaoWhenOpen() const;
	int GetNewAuctionLeastCostWhenOpen() const;
	int GetNewAuctionMaxCostWhenOpen() const;
	int GetNewAuctionLeastPrice(int tid)const;
	int GetNewAucitonCost(int total) const;
	void IncAuctionTurnover(int turnover, int cur_time, bool notify);

	int GetCombatNotBeHurtTm() const;
	int GetCombatNotDoDmgTm() const;

	int64_t GetFreeCash() const;
	void UseFreeCash(const FuncInfo& fi, size_t delta);
	void AddFreeCash(const FuncInfo& fi, size_t delta);

	int64_t GetBindCash() const;
	void UseBindCash(const FuncInfo& fi, size_t delta);

	void CloseTalent();
	void OpenTalent();

public:
//npc相关操作
	//npc呼救
	void NPCAskForHelp(float range);
	XID GetMaster() const;

public:
//npc和player相关操作
	void SystemSpeak(int speak_id);
	void SystemSpeak(int speak_id, const XID& id);
	void error_message(unsigned char action_type, unsigned char show_type, unsigned short msg) const;

public:
//全局可调用跟object没实际关系的
	//查询目标状态
	static bool QueryObject(const XID& who,A3DVECTOR3& pos,unsigned short &scene_tag,float& body_size,unsigned short& dir,bool& is_dead, bool* visible = NULL); //返回值表示对象是否存在
	//获得攻防类型修正系数
	static float GetAttackDefenseClassAdjust(unsigned char attack_class,unsigned char defense_class);
	//获得攻击是否为暴击
	static bool IsCritical(float crit_rate);
	//随机数 0.0 ~ 1.0
	static float Rand();
	//将概率数组进行归一化处理
	static bool RandNormalize(float* r, int n);
	//归一化的概率数组中选一个
	static int RandSelect(const float * option, int size);
	//获得gs系统tick
	static uint64_t GetTick();
	//远距攻击的命中修正
	static float GetFarRangeHigAdjust();
	//超距攻击的命中修正
	static float GetVeryFarRangeHigAdjust();
	//将向量沿y轴逆时针旋转
	static A3DVECTOR3& RotateVector(A3DVECTOR3& v, float sina, float cosa);

	void ClearBufSkill();

	void Sacrifile(const XID& pet,const attacker_info_t& info,float damage);

	//场景参数
	int GetSceneParamValue(int key);
	void SetSceneParamValue(int key,int value);
	int ModifySceneParamValue(int key,int offset);

	//npc自身参数
	int GetSelfParamValue(int key);
	void SetSelfParamValue(int key,int value);
	int ModifySelfParamValue(int key,int offset);
	float GetXPSkillRate() const;

	int GetInstType();

	//帮派得钱(比如卖艺)
	void FactionMoney(int count);
	//需要对象回应表情的得钱，配置复杂一些
	void FactionMoney(int skill_id,const XID& target,int money_config_id);

	int GetItemAuctionInfo(int inv_pos, int item_tid, int64_t& category, int& base_price, int sale_price, int sale_num, abase::octets& extra_data, int& longyu_id);
	int CheckAuctionWarning(int item_tid, int sale_price, int sale_num, abase::octets& extra_data);
	int GetAuctionRepoTime(int price_ratio);
	int  GetTxnItem(int txn_id, GDB::itemdata& item_data, int item_pos);
	bool IsIdipForbidPlayerFunc(int func_code);

	bool TestUseLimit(tid_t tid, size_t count = 1, bool must_exist = false) const;
	bool AddUseLimit(tid_t tid, size_t count = 1);
public:
	class MsgAdjust
	{
	public:
		virtual ~MsgAdjust() {}
		virtual void AdjustAttack(attack_msg & attack) {}
	};
	
	void EnterCombatState();
	bool IsCombatState() const;
	void KnockBack(const XID& attacker,const A3DVECTOR3& source,float distance);
	void SendClientSkillNotify(int param1, filter_typemask_t param2);
	void SendClientSkillAddon(int skill, int level);
	void SendClientSkillCommonAddon(int common);
	void SendClientExtraSkill(int skill, int level);
	void SendClientSelfCombo(unsigned char combo);
	void SendClientSelfHit(int hit, int time);
	void SendClientDragPoint(float speed, A3DVECTOR3& pos, float dis_min, float dis_max, float body_size);
	void SendClientDragLine(float speed, A3DVECTOR3& pos, int dir, float width, float dis_min, float dis_max, float body_size);
	void SendClientDragRemove();
	void SendClientObjectMove(A3DVECTOR3& pos, unsigned short move_dir, float speed, bool send_self);

	void NotifyPlayerMovePos();

public:
	bool IsAggressive();
	void SetAggressive(bool isActive = true);

	void DuelStart(const XID & target);
	void DuelStop();
	
	//在自己的仇恨列表中添加一定仇恨
	void AddAggro(const XID& attacker,int rage,const attacker_info_t& info);
	void AddAggroToEnemy(const XID & helper, int rage);
	//去除目标仇恨
	void RemoveAggro(const XID& who);
	//挑衅 （仇恨暂时置顶）
	void BeTaunted(const XID& who,float time,const attacker_info_t& info); // in second 秒
	//专注 （仇恨瞬间变成最高仇恨值+一定“数值”）
	void BeDedicated(const XID& who,int rage_added,const attacker_info_t& info);

	bool CheckMountState() const;
	bool CheckFlySwordState() const;
	float CalcMountDrop(float rate);
	void ClearMountFilter();

	bool IsEquipWing();
	bool ModifyTalentPoint(int offset, bool main_prof);

	void SetEnterDyingState(bool flag);
	void Die(int delay_tick = 0, bool ignore_statistics = false);
	void Disappear();

	void GetSubobjXidAndPos(int tid, std::map<XID, A3DVECTOR3>& m, float& body_size);
	void SetLastSkill(int skill_id);
	void OnSkillCast(int skill_id, const XID& target);	// 告诉gs使用某技能了
	void TellAICast(const XID& npc, int skill_id);		// 告诉ai npc使用某技能了
	void OnBuffSkillCast(int skill_id, const XID& target);// 告诉gs buffskill被触发
	int CalcFearReduTm(int tm);
	int CalcDeludedReduTm(int tm);

public:
	float CalcLevelDamagePunish(int atk_level,int def_level);	

public:
	//物品
	int CreateItem(int item_id,int count,int period);//创建物品
	int TakeOutItem(int code, int64_t arg, int item_id,size_t count = 1);	//去掉一个物品
	bool TryTakeOutItem(int code, int64_t arg, int location, size_t item_idx, int item_id);	//删除指定位置的物品
	bool CheckItem(int item_id,size_t count) const;	//检查是否存在某种物品
	bool ItemCanGift(item_location_t location,item_index_t index, tid_t tid) const;
	bool ItemCanDeal(item_location_t location,item_index_t index, tid_t tid) const;
	money_t GetMoney() const;
	money_t IncMoney(int code, int64_t arg, MONEY_TYPE money_type,money_t inc);
	void DecMoney(int code, int64_t arg, uint64_t money);
	bool AddBindCash(int code, int64_t arg, int64_t delta);
	money_t GetCanUseMoney(MONEY_TYPE type) const;
	bool TestSafeLock();
	int GetDBMagicNumber();

	size_t GetInventorySize();
	int GetInventoryDetail(GDB::itemdata * list, size_t size);
	size_t GetEquipmentSize();
	int GetEquipmentDetail(GDB::itemdata * list, size_t size);
	int GetTrashBoxDetail(GDB::itemdata * list, size_t size);
	size_t GetTrashBoxCapacity();
	int GetMafiaTrashBoxDetail(GDB::itemdata * list, size_t size);
	bool IsCashModified();
	money_t GetTrashBoxMoney();
	bool IsTrashBoxModified();
	bool IsInSanctuary();
	
	size_t GetMafiaTrashBoxCapacity();
	bool IsEquipmentModified();
	int GetDBTimeStamp();
	int InceaseDBTimeStamp();

	void SetAutoAttackDelay(int millisec);

	void AddSymbioticNewID_A(int newid);
	void RemoveSymbioticNewID_A(int newid);
	void AddSymbioticNewID_B(int newid);
	void RemoveSymbioticNewID_B(int newid);
	void CheckTargetHasSymbioticA(const XID& target, int self_buff_id);
	void CheckTargetHasSymbioticB(const XID& target, int self_buff_id);

	void SendClientDuelStart(const XID& target, int duel_type, const A3DVECTOR3& flag_pos);
	void SendClientDuelStop(const XID & target);

	void ModifyReputation(int type,int index, int offset, int64_t type_arg);//没用了

	void SetNoRewardFlag();
	void ClrNoRewardFlag();

	//获得武器攻击间隔
	float GetMeleeWeaponAttackCycle() const;

	void ResetComboTimer();
	void ResetHitTimer(size_t tick);
	void ResetSuspendTimer(size_t tick);
	void ResetChuPozhanTimer(int tick);

	void MakeRoleData(abase::octets& rdata);

	bool IsObjectSummonPet(const XID& object) const;
	void SetCastSkillFlag(int flag);
	bool IsPubg() const;

public:
	//造一个小弟
	struct minor_param
	{
		int npc_tid;		//模板ID是多少
		int vis_tid;		//可见id，如果为0此值无效
		int remain_time;	//0 表示永久 否则表示存留的秒数
		int policy_classid;	//不能随意填写， 很重要默认填写0
		int policy_aggro;	//不能随意填写， 很重要默认填写0
		XID spec_leader_id;	//指定的leader是谁 
		bool parent_is_leader;	//调用者就是leader 此时 spec_leader_id 无效
		bool use_parent_faction;//使用调用者的阵营信息，否则使用默认数据
		bool use_parent_dir; //使用调用者的方向
		bool die_with_leader;	//leader 死亡或消失则自己也消失
		unsigned char npc_name_size;	//非0则用名称
		char npc_name[MAX_NPC_NAME_LENGTH];
		const void * script_data;
		size_t script_size;
		faction_t use_faction;	//!=0则使用该阵营
		bool reach_limit_stop; //是否替换老的同npc_tid的召唤物
		int limit_count; //上限数量
		bool die_with_owner; //是否跟随主人死亡
		int npc_level; //npc等级
	};

	bool GetNearbyValidPos(A3DVECTOR3& pos);
	void CreateMinors(const A3DVECTOR3& pos,const minor_param& p);		//在指定位置创建小弟
	void CreateMinors(const minor_param& param,float radius = 6.0f);	//在附近随机的位置创建小弟
	void CreateMinors(const minor_param& param,const A3DVECTOR3& _pos, float radius);	//在pos附近随机的位置创建小弟
	static void CreateNPC(gscene_imp* pSceneImp,const A3DVECTOR3& pos,const minor_param& p);//创建一个怪物 非小弟

	struct pet_param
	{
		int npc_tid;		//从哪一种模板里创建出来
		int vis_tid;		//可见id
		float body_size;	//体型
		int exp;
		int sp;
		char use_pet_name;
		unsigned char pet_name_size;
		char pet_name[MAX_NPC_NAME_LENGTH];
	};
	
        bool CreatePet(const A3DVECTOR3& pos,const pet_data* pData,const item_pet_bedge* it_pet,int tid,size_t pet_index,XID& who);

	void StopCharge(skill_id_t skill_id);
	//订婚 0 
	//结婚 1
	static int GetSpecItem(int index);
	static int GetSpecialItem(unsigned char ipc_reason, GDB::itemdata& inv);
	//检测 leader member 的合法性,
	//team是否存在
	//members是否都为队员
	//是否在1个队伍中 leader是否为队长
	//所有相关成员是否在队长range米内
	//满足所有条件返回ture否则false
	enum
	{
		SUCESS = 0,
		NOT_A_TEAM,
		OUT_OF_RANGE,
	};
	//static int CheckPlayers(ruid_t leader_id,const std::vector<ruid_t>& member_id_list,ruid_t team_id,float range);

	static int GetSubobjectPerformID(int tid);

	//获得当前线号
	static int GetServerID();
	static unsigned char GetRealServerID();
	//获得当前线模式
	static unsigned char GetServerMode();
	//获得服务器名称
	static const std::string GetServerName();
	//获得数据版本信息
	static const std::string GetServerDataEdition();
	//获得服务器状态
	static SERVER_STATUS GetServerStatus();
	//获得副本信息
	static const std::vector<instance_info_t>& GetServerInstanceInfo();
	//获得大世界支持场景信息
	static void GetServerGlobalWorldScene(PB::ipt_line_register_info& scenes);
	//获得镖道信息
	static const std::vector<escort_path_t>& GetServerEscortPath();
	//创建帮会消耗交易币
	static int64_t GetCreateFactionTradeMoney();
	//获取当前服务器时间
	static time_t GetSysTime();

	void StopQinggong(bool rush); //等待停
	static int GetPrevQinggong(int skill_id);
	void SendStopQinggong(unsigned char type);

	float GetRunSpeed() const;

	void GetToGround(); //拉到地面
	level_t GetJobLevel(tid_t tid) const;

	bool IsObjectStateSet(object_state_t st) const;
	void UpdateObjectState(object_state_t st, bool set, bool broadcast);
	void UpdatePlayerState(object_state_t st, bool set, bool broadcast);
	void UpdateNpcState(object_state_t st, bool set);
	void UpdateObjectServerState(object_state_t st, bool set);
	void UpdateObjectState2(object_state_t st, bool set, bool broadcast);

	void DebugShow(const char *str); //用于skill模块, 让策划从客户端看自己填的数值计算结果
	bool IsSutraSkillActive(int skill_id); //心法技能是否被激活

	void SetSignature(const void *buf, unsigned char size);
	void RunNextPerform();

	void SkillDrop(tid_t drop_table, size_t drop_times, const A3DVECTOR3& pos);
	void SkillDropSoul(tid_t drop_soul, const A3DVECTOR3& pos);

	unsigned char GetNpcStar();

	void SetInvincebleExcept(bool invinceble, const XID& except);
	void SendControlEnd(char mask, char resume_ctrl_type, unsigned short resume_ctrl_time);

	int InformIDIPAppointCorpsMaster();
	int InformIDIPDeposeCorpsMaster();

	void SetStance(int id);

//######################### 下面是作废，但是要编译通过的函数
	//是否可以抓宠物,宠物装满了等条件下返回true
	bool CanCatchPet() const;
	//npc被抓
	void BeCatchedByPlayer(const XID& catcher,int catch_type,float catch_pet_prob);
//npc和player相关操作
	void BeHijackedByPlayer(const XID& hijacker);
	void BeEscortMeet(const XID& id);
	void BeEscortMeet(const XID& id, int config_id);
	// retcode = -1: invalid crop; 0: seed is not needed; 1: seed is needed
	int IsNeedSeed(int crop_tid);
	int IsNeedCub(int animal_tid);

	int PreFarmSow(const material_t& mt, unsigned char plant_type, farm_crop_t& fct);
	int PostFarmSow(bool is_successful, int seed_id, unsigned char plant_type);
	int FarmHarvestSuccess(int seed_id, unsigned char plant_type);
	void OnFarmSteal(bool is_thief, bool is_successful);
	int PreBreedAnimal(const material_t& mt, breed_animal_t& animal_info);
	int PostBreedAnimal(bool result, int animal_tid);
	bool CheckAnimalForage(int animal_tid, int forage_tid);
	int PostBreedFieldHarvest(bool result, int animal_tid, int phase);

	void OnHomeRestResult(int retcode, unsigned int produce_point_delta, int timestamp);
	void OnHomeAttended(ruid_t dst_player_id, unsigned int servant_tid, 
	                    unsigned int inc_vp_produce, unsigned int inc_vp_social, unsigned int inc_force, 
	                    exp_t inc_exp, exp_t inc_compensate_exp, unsigned int special_buff_skill_id);

	unsigned int GetCanBeHitMask() const;
	void SetCanBeHitMask(unsigned int m);
	void ResetCanBeHitMask();

	bool IsDying();
	void OnControlChange(int old_type, int new_type);

	void SendSkillPersistMove(int interval_tick);

	bool StartParryYingzhi(const A3DVECTOR3& pos);

	void SetInWeak(bool b);
	bool IsInWeak() const;

	int GetBodySizeType() const;

	char CanBePushed() const;
	bool CanBeControl(int& control_type, float& control_move_dis_ratio, float& control_move_dis_extra, int time,bool need_check);

	bool IsWeaponEquiped() const;

	bool IsWiningAction() const;
	void StopWiningAction();

	int GetSkillCapacity(int skill_id, int sk_lv)  const;

	void ChangeEscortSpeed(int inc_state_levl);

	void SendPlayerSendRedEnvelope_Re(int retcode);
	void SendPlayerDrawRedEnvelope_Re(const PB::db_red_envelope& red_envelope, int64_t money_received, int draw_level, int retcode);

	void SetNoDot(int counter);
	void AsyncHurt(const XID& target, int64_t damage, int skill_id, bool ignore_prop_dam3);//我对target造成伤害
	void AsyncHurtSelf(attacker_info_t& atk_info, int64_t dmg, int skill_id, bool ignore_invincible);//异步对自己造成伤害

	//取得创建战队消耗的钱数
	int GetCreateArenaTeamCostMoney();
public:
	static int GetInnerType(const ::google::protobuf::Message& msg);

	void OnSkillAddon(int skill_id,int skill_level,bool upgrade);
	int GetStuntSkill(int master_skill) const;
	void BeCure(const XID& who, const attacker_info_t& info, float value, bool abs, bool notify_client, int skill_id);
	void OnReplyCoupleTour(const PB::ipt_reply_couple_tour& pb);

	bool IsSkillActived(int skill_id) const;
	static bool IsIdipForbidSkill(int skill_id);
	static bool CanUseRetinueSkill();
	static bool CanUseSevenCrimeBuffCastSkill();
	static bool CanSkillBeyondMaxCoverage(int skill_id);
	int HasTalent(int talent, int skill) const;
	int GetPureBloodedTalent(int talent, int skill) const;
	int GetHolyGhostTalent(int hg_id, int talent, int skill) const;
	int GetMasterCurPhyAtk() const;
	int GetMasterCurMagAtk() const;
	int GetMasterCurPsychokinesisLevel() const;
	int GetMasterCurCDReduLevel() const;
	int GetMasterSkillLevel(int skill_id) const;
	int GetMasterTalentLevel(int talent, int skill) const;
	int GetMasterPurebloodTalentLevel(int talent, int skill) const;
	int GetMasterKotodamaStage(int index) const;
	int PercentHp(int percent) const;
	int GetPercetHP()const;
	float RatioHp() const;//当前生命比率 [0,1]
	int ControlType(int type) const;
	int GetTalentCapacity() const;
	int GetPureBloodedCapacity() const;
	bool HasGuardSkill(int skill) const;
	bool HasRetinueSkill(int skill) const;
	bool CanRetinueActiveBuffSkill(int skill_id) const;
	void SetRetinueActiveBuffSkill(int skill_id);
	void OnRetinueActiveBuffSkill(int skill_id);
	bool CanDragonbornActiveBuffSkill(int skill_id) const;
	void SetDragSkillAddSpeed(float s);
	int GetGuardPropByIdx(int prop_idx) const;
	int GetRetinuePropByIdx(int skill_id, int prop_idx) const;
	int GetDragonbornPropByIdx(int skill_id, int prop_idx) const;
	int GetDragonbornBreakLevel() const;
	int GetAIPrioTarget() const;
	int GetWarm() const;
	int GetCool() const;
	void Jump2SubobjPos(int tid, bool remove);
	void Jump2FarthestSubobjPos(int tid, bool remove, int lantency);
	void SetFakeReviveRate(float r);
	int KotodamaStage(int skill) const;
	bool IsImmuneMove() const;
	void CastSkill2Me(const XID& attacker, int skill_id, int skill_level = 1) const;
	void DelayCastSkill2Me(const XID& attacker, int skill_id, int tick) const;
	void CastSkillAtHere(const XID& attacker, int skill_id) const;
	void CastSkillSelf(const XID& attacker, int skill_id) const;
	void EnterPelt();//进入疾行
	void LeavePelt();//取消疾行
	void Debug_ClientShowFlag(const A3DVECTOR3& pos);
	bool HasEnemyTarget() const;
	bool IsManualDamageSkill(int skill) const;
	void SettlePersist(ruid_t from_rid, int filter_id, float rate, bool dispel);
	void TwinDetach(bool b);
	void TwinSkill(int skill_id, int skill_level);
	void TwinSkillAtPos(int skill_id, int skill_level, const A3DVECTOR3& pos);
	float GetTwinSquaredDistance(bool& has);
	void CoexistEnd(const XID& target, int buff_id);
	void CoexistDelayAdd(const XID& target, int time_left);
	int GetSelfNewid() const;
	void ChangeModel(int tid, int attacker_newid);
	void ResetModel();
	void TwinJumpBack();
	void TwinBind(bool b);
	bool GetSubobjPos(int tid, A3DVECTOR3& pos) const;
	bool CanBless(const XID& target) const;
	bool IsTwin() const;
	int GetTwinBuffLevel(int id, ruid_t rid) const;
	int GetTwinBuffTime(int id) const;
	bool IsMech() const;
	void MechSkill(int skill_id, int skill_level);
	bool MechCompose();
	void MechDecompose(bool is_switch_map);
	void MechJumpSkill(int angle, float dis, int skill_id);
	void MechSkillToPos(int angle, float dis, int skill_id);
	void MechSkillToPos2(const A3DVECTOR3& pos, int skill_id);
	int GetLivingMs() const;
	bool IsFaker() const;
	int IsHeir() const;
	bool IsGhost() const;
	void NextSkillWithoutCD(int min_ms, int filter_id);
	bool IsRuneActiveSkill(int skill_id);
	int IsEbdure(filter_typemask_t type_mask) const;

	//社团拍卖
	void CorpsAuctionBuyOrder(int cash);
	void CorpsAuctionWithdrawCash(int cash, bool by_system);

	//家园出售搬家
	void HometownSellOrMove(int bind_money, int bind_cash);
	void ContractHometownSellOrMove(int bind_money, int bind_cash, const std::map<int, int>& repu_info);
	void ContractHometownSellObject(int bind_money, int bind_cash, const std::map<int, int>& repu_info);
	void ContractHometownDemolish(int bind_money, int bind_cash, const std::map<int, int>& repu_info);
	void ContractHometownMove(int bind_money, int bind_cash, const std::map<int, int>& repu_info);
	void ContractGridDemolish(int bind_money, int bind_cash, const std::map<int, int>& repu_info);

	void DealLMFShopMail(int type, int index, int value);

	const XID& GetFeedBackID() const;
	void TwinFeedBackSkill(const checkfeedback_msg& msg, const std::vector<XID>* src);
	void MechFeedBackSkill(const checkfeedback_msg& msg, const std::vector<XID>* src);

	bool IsLongyuEquip(GDB::itemdata& data, int longyu_id, int equip_tid);
	GDB::GMailInterface* GetMailIf();
	bool NeedDamageWeak();
	void NearDeath(bool b);
	int GetNearDeathNum() const;
	int GetPubgWeaponType() const;
	int GetTeamMemberCount() const;
	int GetChessTid() const;
	int GetChessCamp() const;
	int GetCrimeSwordLevel(int sword_id) const;
	int GetCrimePower(int crime_type) const;
	int GetCrimeNum(int crime_type) const;
	int GetCrimeMainSwordLevel() const;
	int GetCrimeSacrificeAddSkillCrit() const;
	int GetCrimeSacrificeAddSkillCritRedu() const;
	int GetCrimeSacrificeAddSkillDam() const;
	int GetCrimeSacrificeAddBuffSkillCrit() const;
	int GetCrimeSacrificeAddBuffSkillCritRedu() const;
	int GetCrimeSacrificeAddBuffSkillDam() const;
	void TryCrimeCostSkillCD(int& cooltime) const;

	void SetIgnoreSomeDamageProp(int b ,int ratio = 1000);
	void ClrIgnoreSomeDamageProp();

	void OnControlMaskAdd();
	void OnControlMaskRemove();

	//守护灵培养
	void GuardBreedNotify(const PB::ipt_guard_breed_notify& cmd);

	int GetGfxModify(uint64_t attacker_gfx_modify_mask, int gfx) const;
	uint64_t GetGfxModifyMask() const;

	void CreateFakerMoveForward(int exist_second);
	void CreateFakerStandSkill(int exist_second);
	void DeleteFakerMoveForward();
	void DeleteFakerStandSkill();
	void SwapPosWithFakerMove();
	void SwapPosWithFakerStand();
	void RecordHpPosDir(int max_tm);
	void UseHpPosDir(bool cost_hp, bool use_pos_dir);
	void PubgSafeRandTeleport();
	void SoulChildDelete(ruid_t intimate_id);
	void SendTargetAddMP(const XID& target, int m);
	void AddChessMPRecover(int a);
	void AddDamageBlock(int a);
	//职业10 
	void SetCage(bool status = false,A3DVECTOR3 center_pos = A3DVECTOR3(0.f),float radius = 0);
	void SwapPosWithCagePos() const;
	void ReplisomeSkill(int skill,int skill_level)const;
	bool IsReplisome() const;
	int GetReplisomeBuffLevel(int id, ruid_t rid) const;
	int GetReplisomeBuffTime(int id) const;
	void ReplisomeFeedBackSkill(const checkfeedback_msg& msg, const std::vector<XID>* src);
	unsigned int TryCreateReplisome();
	void TryDeleteReplisome(unsigned int index );
	int  GetPlayerReplisomeCount() const;
	void SetRelisomeDamRatio(int ratio);
	void GetReplisomeDamageRatio();
	bool IsForbidMP14Obtain();
	void SealDiet(bool set);
	bool IsForbidPassiveLongyu();
	bool IsForbidPassiveTalent();
	bool IsTwining()const;
	//获取玩家释放的tid子物体个数
	size_t GetSubObjectCount(int tid) const; 
	void TryCreateDog(int skill_level);
	void TryDeleteDog();
	bool IsDog() const;
	int  GetDogBuffLevel(int id, ruid_t rid) const;
	int  GetDogBuffTime(int id) const;
	bool CheckProfTalentSkill(int skill) const;

    void TryRecallSubobject(int tid, bool is_destroy);
	void StartRecordData(int data_type, uint64_t from_rid);
	void FinishRecordData(int data_type, uint64_t from_rid);
	uint64_t GetRecordData(int data_type, uint64_t from_rid) const;
	void TryRecordBuffHistory(int buff_id);
	void ClearBuffHistory();
	bool CheckBuffHistory(int skill_id);
	int GetBuffHistory(int index) const;
	void OnDetachBuff(int buff_id);
	void ReduceShieldInstant(int value);
	bool IsRoamCategory(int item_tid);
	const std::set<int>& GetForbidBuffPool() const;
	void DebugSay(const std::string& msg) const;
	void DebugNotifyClientResetSkill(std::function<std::string()> msg_getter) const;
	//遍历身上所有被动技能并执行function
	void ForEachPassiveSkillWithFunctionalLabel(const std::function<void(int skill_id, int level)>& func);

	static float sinf(int n);
	static float cosf(int n);


};

//提供给gamed获取制定定玩家接口的方法
//构造后验证valid,成功则加锁
//析购解锁
//注意!!!!!!!!
//对象object_shell 构造后须及时使用尽快销毁,需保证函数内生存期
//不销毁会死锁
class object_shell
{
	gplayer* _player;
	object_interface _oif;
public:
	object_shell(ruid_t player_id);
	~object_shell();

	bool IsValid() const { return _player; }
	object_interface& GetInterface();
};

#endif

