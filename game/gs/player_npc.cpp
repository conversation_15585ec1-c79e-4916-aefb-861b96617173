#include "player_npc.h"
#include "level_control.h"
#include "gprotoc/gp_object_buff.pb.h"
#include "gprotoc/retinue_group_info.pb.h"
#include "gprotoc/player_mech.pb.h"
#include "gprotoc/player_heir.pb.h"
#include "gprotoc/gp_pubg_battle_end.pb.h"
#include "gprotoc/guard_essence.pb.h"
#include "gprotoc/player_faker.pb.h"
#include "gprotoc/reputation.pb.h"
#include "gprotoc/player_extend_state.pb.h"
#include "gprotoc/retinue_info.pb.h"
#include "gprotoc/ipt_remote_call.pb.h"
#include "gprotoc/gp_pubg.pb.h"
#include "gprotoc/player_universal_data_t.pb.h"
#include "gprotoc/player_definite_info_common.pb.h"
#include "gprotoc/buff_t.pb.h"
#include "gprotoc/player_replisome.pb.h"
#include "gprotoc/player_guard_essence.pb.h"

#include "hall_of_heroes.h"
#include "action/action_mine.h"
#include "action/ai_action.h"
#include "skill_config_man.h"
#include "protocol_imp.h"
#include "grolebase"
#include "grolestatus"
#include "transform_manager.h"
#include "funcswitchmgr.h"

DEFINE_SUBSTANCE(gplayer_npc_dispatcher, gnpc_dispatcher, CLS_PLAYER_NPC_DISPATCHER)
DEFINE_SUBSTANCE(gplayer_npc_imp, gnpc_imp, CLS_PLAYER_NPC_IMP)

void gplayer_npc_dispatcher::player_extend_data(unsigned short flag, unsigned char mask, const link_id_t& lid, unsigned int equip_mask_add, const void *equip_buf, size_t equip_bufsize)
{
	gplayer_npc_imp *pImp = (gplayer_npc_imp *)_imp;
	gnpc *pNPC = reinterpret_cast<gnpc *>(pImp->Parent());
	if (!pNPC->CheckObjectState(gcreature::STATE_NPC_PLAYER))
	{
		return;
	}
	packet_tla_wrapper h1(4096);
	using namespace S2C;
	CMD::Make<CMD::player_extend_data>::From(h1, pNPC->ID.id, flag, mask);
	if (mask & C2S::CMD::get_others_extend_data::EQUIP_DATA_MASK)
	{
		CMD::Make<CMD::player_extend_data>::EquipmentInfoChange(h1, pNPC, equip_mask_add, 0xFFFFFFFF, equip_buf, equip_bufsize);
	}
	if (mask & C2S::CMD::get_others_extend_data::APPEARANCE_DATA_MASK)
	{
		CMD::Make<CMD::player_extend_data>::AppearanceInfo(h1, pNPC->base_info.appearance_crc, pNPC->base_info.appearance_data, pNPC->base_info.appearance_size);
	}
	if (mask & C2S::CMD::get_others_extend_data::FASHION_DATA_MASK)
	{
		CMD::Make<CMD::player_extend_data>::FashionInfo(h1, pNPC->base_info.fashion_crc, pNPC->base_info.fashions, pNPC->base_info.fashions_ext, pNPC->base_info.fashions_offset);
	}
	send_ls_msg(lid, h1);
}

void gplayer_npc::MakePlayerExtendState(PB::player_extend_state& proto, object_state_t state, object_state_t state2) const
{
	state &= ~(gobject::STATE_ROAM_OBJECT | gobject::STATE_VISUAL_EFFECT /*| gobject::STATE_SPEC_BODY_SIZE*/ | gobject::STATE_ADV_MODE);
	if (state & gobject::STATE_EXTEND)
	{
		gfx.FillPB(*proto.mutable_gfx(), true);
	}
	if (state & gobject::STATE_ACTION)
	{
		proto.set_action_type(action_type);
		proto.set_action_arg(action_arg);
	}
	if (state & gobject::STATE_MAFIA)
	{
		proto.set_mafia_id(mafia_id);
		proto.set_mafia_rank(mafia_rank);
		proto.set_mafia_pos_type(mafia_pos_type);
		proto.set_mafia_support_side(mafia_support);
		proto.set_mafia_misc_index(mafia_misc_index);
		proto.set_mafia_spec_rank(mafia_spec_rank);
		proto.set_mafia_crown(mafia_crown);
		proto.set_mafia_crown_timeout(mafia_crown_timeout);
		proto.set_mafia_hundred_crown(mafia_hundred_crown);
		proto.set_mafia_hundred_crown_timeout(mafia_hundred_crown_timeout);
	}
	if (state & gobject::STATE_TRANSFORM)
	{
		proto.set_transform_tid(transform_tid);
		proto.set_transform_attacker_newid(transform_attacker_newid);
	}
	if (state & gobject::STATE_TITLE)
	{
		proto.set_title_id(title_id);
		proto.set_title_data(title_data, title_data_size);
	}
	if (state & gobject::STATE_SPOUSE)
	{
		proto.set_spouse_id(spouse_id);
	}
	if (state & gobject::STATE_TEAM)
	{
		proto.set_team_id(team.id);
	}
	if (state & gobject::STATE_GUARD)
	{
		proto.set_guard_id(guard_id);
		proto.set_guard_shape(guard_shape);
		proto.set_guard_name(guard_name, guard_name_size);
		proto.set_guard_skin_id(guard_skin_id);
		proto.set_guard_gfx_id(guard_gfx_id);
		proto.set_guard_generation(guard_generation);
	}
	proto.set_twin_id(twin_xid.id);
}

void gplayer_npc::MakePlayerDefiniteInfoCommon(PB::player_definite_info_common& proto, const A3DVECTOR3& pos, object_state_t state, object_state_t state2) const
{
	proto.set_roleid(ID.id);
	proto.set_newobjid(ID.GetNewObjID());
	bool use_alias = player_alias_size;
	proto.set_use_alias(use_alias);
	if (use_alias)
	{
		proto.set_player_alias(player_alias_buf, player_alias_size);
		proto.set_level(0);
	}
	else
	{
		proto.set_player_name(player_name_buf, player_name_size);
		proto.set_level(base_info.level);
	}
	proto.set_idphoto(base_info.idphoto);
	proto.set_race(base_info.race);
	proto.set_gender(base_info.gender);
	proto.set_prof(base_info.prof);
	proto.mutable_pos()->set_x(pos.x);
	proto.mutable_pos()->set_y(pos.y);
	proto.mutable_pos()->set_z(pos.z);
	proto.set_dir(dir);
	proto.set_faction(faction);
	proto.set_crc(crc);
	proto.set_appearance_crc(base_info.appearance_crc);
	proto.set_combat_state(combat_state);
	proto.set_control_state(control_state);
	proto.mutable_prop_notify()->set_percent_hp(base_info.prop_notify.percentHP);
	proto.mutable_prop_notify()->set_percent_sp(base_info.prop_notify.percentSP);
	proto.mutable_prop_notify()->set_inc_sp(base_info.prop_notify.IncSP);
	proto.mutable_prop_notify()->set_percent_shield(base_info.prop_notify.percentShield);
	proto.set_nation_id(nation_id);
	proto.set_face_id(base_info.faceid);
	proto.set_body_physic(body_physic);
	proto.set_weapon_id(weapon_id);
	proto.set_weapon_star(weapon_star);
	proto.set_weapon_hide(weapon_hide);
	proto.set_pubg_prof(pubg_prof);
	proto.set_appearance_tid(appearance_npc_id);
	proto.set_fashion_crc(base_info.fashion_crc);
	proto.set_gem_gfx_suit_id(gem_gfx_suit_id);
	if (fake_roleid)
	{
		proto.set_fake_roleid(fake_roleid);
	}
	proto.set_comeback_repu_value(player_comeback_repu_value);
	if (edit_data_size > 0)
	{
		proto.set_edit_diaoxiang_data(edit_data, edit_data_size);
		proto.set_edit_diaoxiang_data_crc(edit_data_crc);
	}
	if (diaoxiang_id > 0)
	{
		proto.set_diaoxiang_id(diaoxiang_id);
	}
}

gplayer_npc_imp::gplayer_npc_imp() : _stunt_rune(this), _guard(this), _kotodama(this), _retinue(this),
	_talent(this), _pureblooded(this), _collection(this), _prof_manager(this), _twin(this), _mech(this),
	_prof6(this), _heir_control(this), _seven_crime(this), _prof12(this), _diaoxiang_crc(0)
{
	InitPlayerReplisome();
}

bool gplayer_npc_imp::OnCreate(const level_control::player_summon_t& param, const GDB::hero_list& heros)
{
	//装备强化需要使用攻击类型属性，必须提前初始化基本属性
	GetProperty().Rebuild(GetProperty().GetProfLevel(), GetProperty().GetProf(), GetProperty().GetLevel());

	//设置装备数据
	SetNPCEquipData(param.equip_mask, param.equip_data);

	//设置时装外观
	UpdateFashionToParent(param.fashions, GNET::LOCAL_FASHION_TYPE_END);
	//玩家形象
	GetParent()->SetAppearance(param.appearance, param.appearance_size);

	PB::player_universal_data_t pud;
	try
	{
		if (!pud.ParseFromArray(param.univ_data, param.univ_data_size))
		{
			throw "error";
		}
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家: " FMT_I64" 镜像加载统一数据出错\n", param.player_id);
	}

	//在加载技能前加载天赋，可以减少一次技能刷新
	try
	{
		// 加载多职业数据，并且获当前职业的天赋和技能方案
		GetProfManager().OnLoad(pud);
		_pureblooded.OnPlayerLogin(false, false);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 多职业数据出错\n", _parent->ID.id);
	}
	_auto_combat_set.Active(this, 3);

	//加载技能
	if (param.skill_data && param.skill_size)
	{
		object_interface oif(this);
		try
		{
			raw_wrapper ar(param.skill_data, param.skill_size);
			GetSkill().LoadDatabase(oif, ar);
		}
		catch (...)
		{
			GLog::log(LOG_WARNING, "玩家化身: "   FMT_I64" 技能数据装载出错\n", param.player_id);
		}
		GetSkill().EventReset(oif);
	}
	_stunt_rune.OnLogin(this, false);

	//加载称号
	try
	{
		raw_wrapper ar(param.title_list, param.title_list_size);
		_title.Load(this, ar);
		_title.UpdateTitleAddon(this);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 称号数据出错\n", param.player_id);
	}
	_title.SelectTitle(this, param.current_title);
	if (param.title_id > 0)
	{
		//策划自己配的显示称号
		GetParent()->SetObjectState(gobject::STATE_PLAYER_NPC_TITLE);
		GetParent()->SetObjectState(gobject::STATE_TITLE); //客户端要求这个也要置上
		GetParent()->title_id = param.title_id;
		memset(GetParent()->title_data, 0, sizeof(GetParent()->title_data));
		GetParent()->title_data_size = 0;
	}

	if (param.player_id)
	{
		GetParent()->tmp_fake_roleid = param.player_id;
		__PRINTF("gplayer_npc_imp::OnCreate::InitFakeRoleID::fake_roleid=" FMT_I64"\n", param.player_id);
	}

	//加载言灵
	try
	{
		_kotodama.Load(pud);
		_kotodama.OnLogin();
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 言灵数据出错\n", _parent->ID.id);
	}

	//坐骑属性修正
	try
	{
		GetHeroes().LoadFromDB(this, heros);
		GetHeroes().OnPlayerLogin(false);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 名人数据出错\n", param.player_id);
	}

	//加载守护灵
	PB::player_guard_essence guard_data;
	try
	{
		if (param.guard_data_size > 0 )
		{
			if (!guard_data.ParseFromArray(param.guard_data, param.guard_data_size))
			{
				throw "error";
			}
			GetGuard().Load(this, guard_data);
			GetGuard().OnPlayerLogin(this);
		}
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 守护灵数据出错\n", _parent->ID.id);
	}

	//加载随从以及随从组合
	try
	{
		raw_wrapper rw(pud.retinue_info().c_str(), pud.retinue_info().size());
		_retinue.Load(this, rw);
		_retinue.OnPlayerLogin();

		raw_wrapper rw2(pud.retinue_group_info().c_str(), pud.retinue_group_info().size());
		_retinue_group.Load(this, rw2);
		_retinue_group.OnPlayerLogin(this);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 伙伴数据出错\n", param.player_id);
	}

	//加载时装
	try
	{
		std::vector<uint64_t> create_role_fashions;
		GetFashion().Load(this, pud, create_role_fashions);
		GetFashion().ActivateAllFashionAddon(this);
		GetFashion().UpdateBeautyPoint(this, false, false);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 时装数据出错\n", param.player_id);
	}

	//装备强化以及宝石
	try
	{
		_enhance.Load(this, pud);
		_enhance.OnPlayerLogin(this);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身装备强化位: " FMT_I64" 数据出错\n", param.player_id);
	}

	try
	{
		_collection.Load(pud);
		_collection.OnPlayerLogin();
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身手办收藏: " FMT_I64" 数据出错\n", param.player_id);
	}

	// 魔盒
	try
	{
		GetTetrahedron().Load(this, pud);
		GetTetrahedron().OnPlayerLogin(this);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 魔盒数据出错\n", _parent->ID.id);
	}

	// 声望
	try
	{
		_reputation.Load((const char *)param.repu_data, param.repu_data_size);
		GetParent()->player_comeback_repu_value = _reputation.Get(GNET::REPUID_PLAYER_COMEBACK_FLAG);
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 声望数据出错\n", _parent->ID.id);
	}

	//重建所有属性
	GetProperty().Rebuild(GetProperty().GetProfLevel(), GetProperty().GetProf(), GetProperty().GetLevel());
	GetProperty().Constraint();
	GetProperty().RenewAll();

	try
	{
		GetSevenCrime().OnLoad(pud.seven_crime());
	}
	catch (...)
	{
		GLog::log(LOG_WARNING, "玩家化身: " FMT_I64" 七宗罪数据出错\n", _parent->ID.id);
	}

	_ai_ctrl->InitSkill();
	PrintProperty();

	if (param.mafia_name && param.mafia_name_size > 0)
	{
		_mafia_name.clear();
		_mafia_name.push_back(param.mafia_name, param.mafia_name_size);
	}

	return true;
}
bool gplayer_npc_imp::StepMove(const A3DVECTOR3& offset, bool set_dir, int param, bool force_to_invalid_pos, int is_fly, unsigned int flags, unsigned char jump_type, unsigned char land_type, unsigned short pitch, unsigned char moveidle_act_idx, unsigned char param2, unsigned short client_speed, bool team_follow, bool )
{
	if (gnpc_imp::StepMove(offset, set_dir, param, force_to_invalid_pos, is_fly, flags, jump_type, land_type, pitch, moveidle_act_idx, param2, client_speed, team_follow, false))
	{
		if (IsNPCTeamLeader() && GetParent()->team.IsValid())
		{
			//此处会不会有点费
			if (_first_follower.IsValid())
			{
				__PRINTF("gplayer_npc_imp::NPCTeamLeader::StepMove:fake_roleid=" PRINT64":team_id=" FMT_I64":first_follower=" FMT_I64":index=%d:offset=[%.2f,%.2f,%.2f]:set_dir=%d\n",
				         GetFakeRoleID(), GetParent()->team.id, _first_follower.id, _first_follower_index, offset.x, offset.y, offset.z, set_dir);

				msg_team_follow_move_t team_follow_move;
				memset(&team_follow_move, 0, sizeof(team_follow_move));
				team_follow_move.index = _first_follower_index;
				team_follow_move.speed = client_speed;
				team_follow_move.fly = is_fly;
				team_follow_move.flags = flags;
				team_follow_move.jump_type = jump_type;
				team_follow_move.land_type = land_type;
				team_follow_move.pitch = pitch;
				team_follow_move.moveidle_act_idx = moveidle_act_idx;
				team_follow_move.face_dir = GetParent()->dir;
				team_follow_move.is_in_fly = 0;

				MSG msg;
				BuildMessage(msg, GM_MSG_TEAM_FOLLOW_MOVE, _first_follower, GetParent()->ID, GetParent()->pos, 0, &team_follow_move, sizeof(team_follow_move));
				gmatrix::GetInstance().SendMessage(msg);
			}
			else
			{
				__PRINTF("gplayer_npc_imp::NPCTeamLeader::StepMove:fake_roleid=" PRINT64":team_id=" FMT_I64":no member in team follow\n", GetFakeRoleID(), GetParent()->team.id);
			}
		}
		return true;
	}
	return false;
}
bool gplayer_npc_imp::IsNPCTeamLeader() const
{
	if (!GET_FUNC_SWITCH(kFuncCodeNpcTeamPlatform))
	{
		return false;
	}

	if (GetWorldImp()->GetTeamMemberNpcRoleid() != GetFakeRoleID())
	{
		return false;
	}

	if (!GetParent()->CheckObjectState(gobject::STATE_TEAM))
	{
		return false;
	}

	if (!GetParent()->CheckObjectState(gobject::STATE_TEAMLEADER))
	{
		return false;
	}

	if (!GetParent()->team.IsValid())
	{
		return false;
	}

	return true;
}

void gplayer_npc_imp::TryGatherMine(const XID& mine_target)
{
	LOG_TRACE("gplayer_npc_imp::TryGatherMine::mine_target=" FMT_I64"", mine_target.id);
	if (!mine_target.IsValid())
	{
		return;
	}
	if (_parent->IsZombie())
	{
		return;
	}
	if (HasAction())
	{
		LOG_TRACE("gplayer_npc_imp::TryGatherMine::mine_target=" FMT_I64":HasAction", mine_target.id);
		return;
	}

	player_gather_t pg;
	memset(&pg, 0, sizeof(pg));
	pg.level = _prop.GetLevel();
	pg.faction = GetFaction();
	pg.enemy_faction = GetEnemyFaction();
	gmatrix::GetInstance().SendTo(GM_MSG_GATHER_REQUEST, _parent, mine_target, 0, &pg, sizeof(pg));
}

void gplayer_npc_imp::TryGatherItemMine(const XID& mine_target, tid_t mine_tid)
{
	if (!mine_target.IsValid() || mine_tid <= 0)
	{
		return;
	}
	if (_parent->IsZombie())
	{
		return;
	}
	if (HasAction())
	{
		return;
	}

	gmatrix::GetInstance().SendTo(GM_MSG_PLAYER_PICKUP_ITEM, _parent, mine_target, mine_tid);
}

void gplayer_npc_imp::MessageHandler(const MSG& msg)
{
	switch (msg.message)
	{
	case GM_MSG_CAST_GUARD_SKILL:
	{
		int guard_id = msg.param;
		GetGuard().DebugCastAtiveSkill(this, guard_id, msg.source);
	}
	return;

	case GM_MSG_DUMP_PROP:
	{
		GetProperty().GetGProperty()->dump(stderr);
	}
	return;

	// gplayer_npc移植，只适用战场机器人
	case GM_MSG_GATHER_REPLY:
	{
		if (!HasAction())
		{
			StartAction(new action_mine(this, msg.source, msg.param, msg.param2, msg.param3));
		}
		else
		{
			SendTo(GM_MSG_GATHER_CANCEL, msg.source, 0);
		}
	}
	return;


	case GM_MSG_GET_MINE_DROP:
	{
		if (IsNPCTeamLeader() && GetParent()->team.IsValid())
		{
			int nMineID = msg.param;
			//int career_ability_id = msg.param2;
			XID teamXID = GetParent()->team;
			__PRINTF("GM_MSG_GET_MINE_DROP:mine_tie=%d:teamid=%ld\n", nMineID, teamXID.id);
			abase::vector<XID> teamMember_List;
			player_team::GetTeamMember(teamXID, teamMember_List);
			for (size_t i = 0; i < teamMember_List.size(); ++i)
			{
				XID& memberXID = teamMember_List[i];
				RemoteCall::CallObjectRemoteFunc("PlayerGetDrop", memberXID.id, PB::ipt_remote_call::OBJECT_TYPE_GS_GPLAYER_IMP, NULL,
				                                 GetParent()->world_tid, 0, nMineID, 0,
				                                 msg.pos.x, msg.pos.y, msg.pos.z, GetParent()->world_id.id,
				                                 GetParent()->scene_tag, GetParent()->mid);
			}
		}
	}
	break;

	// gplayer_npc移植，只适用战场机器人
	case GM_MSG_GATHER_RESULT:
	{
		ASSERT(sizeof(msg_gather_result_t) == msg.content_length);
		const msg_gather_result_t& mgr = *(const msg_gather_result_t *)msg.content;

		if (mgr.is_center_battle_mine)
		{
			//如果是跨服战场的矿
			GetWorldImp()->CheckGather(GetParent()->ID.id, mgr.tid);
		}
		if (HasAction())
		{
			SendTo(GM_MSG_GATHER_END, msg.source, 0);
			return;
		}
		SendTo(GM_MSG_GATHER_END, msg.source, 1);

		//发送消息给场景
		MSG msg;
		BuildMessage4(msg, GM_MSG_PLAYER_GATHER_NOTIFY_SCENE, GetRealSceneImp()->Parent()->xid, _parent->ID, A3DVECTOR3(0, 0, 0), mgr.tid);
		gmatrix::GetInstance().SendMessage(msg);
	}
	return;

	case GM_MSG_BATTLE_PLAYER_NPC_DEATH:
	{
		if (_ai_ctrl)
		{
			ASSERT(msg.content_length == sizeof(XID));
			aggro_manager& aggroman = _ai_ctrl->GetAggroMan();
			XID death_target = *((XID *)msg.content);
			aggroman.Remove(death_target);

			// 如果是player_npc正在追击的目标死亡，则清理player_npc的mission和action
			if (HasAction() && _action_man.GetCurAction()->GetType() == AT2_FOLLOW_MASTER)
			{
				ai_action_follow_master *cur_action = dynamic_cast<ai_action_follow_master *>(_action_man.GetCurAction());
				if (cur_action && death_target == cur_action->GetMaster())
				{
					__PRINTF("player_npc ClearMission and ClearAction:id=%ld, death=%ld\n", GetParent()->ID.id, death_target.id);
					_ai_ctrl->ClearMission();
					ClearAction();
				}
			}
		}
	}
	return;

	case GM_MSG_CAST_SKILL_2_ME:
	{
		object_interface oif(this);
		GetSkill().InstantSkill(oif, msg.param, msg.param2, 0, WMSKILL::CAST_PRIOR_ID, msg.source, msg.pos, false, NULL, false);
	}
	return;

	case GM_MSG_CAST_SKILL_AT_HERE:
	{
		object_interface oif(this);
		GetSkill().InstantSkill(oif, msg.param, 1, 0, WMSKILL::CAST_PRIOR_POS, msg.source, msg.pos, false, NULL, false);
	}
	return;

	case GM_MSG_COEXIST_END:
	{
		//玩家身上的契约目标buff可能有多个，该buff必须是个人buff, 才能正确驱掉(不把别人加的目标buff驱掉)
		_filter_man.RemoveFilter(msg.param, msg.source.id, filter::ER_COEXIST);
	}
	return;

	case GM_MSG_DIAOXIANG_CHECK_NEW:
	{
		CheckNewDiaoxiang(msg.param);
	}
	return;

	case GM_MSG_PUBG:
	{
		if (msg.param == 0)
		{
			std::vector<float> arg;
			for (int i = 0; i < msg.content_length / sizeof(float); ++i)
			{
				arg.push_back(*((float *)msg.content + i));
			}
			GetPubg().OnMsg_Battle(this, arg);
		}
		else if (msg.param == 1)
		{
			//std::vector<int64_t> arg;
			//for (int i = 0; i < msg.content_length / sizeof(int64_t); ++i)
			//{
			//	arg.push_back(*((int64_t *)msg.content + i));
			//}
			//GetPubg().OnMsg_Battle64(this, arg);
		}
		else if (msg.param == 2)
		{
			std::vector<float> arg;
			for (int i = 0; i < msg.content_length / sizeof(float); ++i)
			{
				arg.push_back(*((float *)msg.content + i));
			}
			GetPubg().OnMsg_Player(this, msg.source, arg);
		}
		else if (msg.param == 4)
		{
			//PB::gp_pubg_battle_end pb;
			//pb.ParseFromArray((char *)msg.content, msg.content_length);
			//GetPubg().OnMsg_BattleEnd(this, pb);
		}
		else if (msg.param == 5)
		{
			GetPubg().TeamDeath(this);
		}
	}
	break;

	case GM_MSG_MECH:
	{
		PB::player_mech m;
		m.ParseFromArray((char *)msg.content, msg.content_length);
		GetMech().OnMsg(m);
	}
	break;

	case GM_MSG_HEIR:
	{
		PB::player_heir m;
		m.ParseFromArray((char *)msg.content, msg.content_length);
		GetHeirControl().OnMsg(m);
	}
	break;

	case GM_MSG_FAKER:
	{
		PB::player_faker m;
		m.ParseFromArray((char *)msg.content, msg.content_length);
		if (GetProf6())
		{
			GetProf6()->OnMsg(msg.source, m);
		}
	}
	break;

	case GM_MSG_PICKUP_ITEM:
	{
		if (msg.param4)
		{
			gmatrix::GetInstance().SendTo(GM_MSG_LM_FEED_BACK, _parent, msg.source, msg.param);
		}
	}
	return;

	case GM_MSG_PUBG_NEAR_DEATH:
	{
		if (msg.param > 0)
		{
			ruid_t attacker_rid = *((ruid_t *)msg.content);
			GetWorldImp()->OnPubgNearDeath(this, attacker_rid);
		}
		else
		{
			GetWorldImp()->OnPubgNearDeathCancel(this);
		}
	}
	break;

	default:
		break;
	}
	gnpc_imp::MessageHandler(msg);
}

bool gplayer_npc_imp::OnHeartbeat()
{
	GetGuard().Heartbeat(this);
	_retinue.Heartbeat();
	_enhance.Heartbeat(this);
	IncMP3(GetMP3Recover());//每秒回复mp3/耐力
	_twin.Heartbeat();
	if (GetParent()->diaoxiang_id <= 0)
	{
		GetMech().Heartbeat();
	}
	GetPubg().Heartbeat(this);
	if (GetProf6())
	{
		GetProf6()->Heartbeat();
	}
	GetHeirControl().Heartbeat();
	GetProf12().Heartbeat();
	for (unsigned int i = 0; i < PLAYER_REPLISOME_MAX; ++i)
	{
		player_replisome *replisome = GetReplisomeP(i);
		if (replisome != NULL)
		{
			replisome->Heartbeat();
		}
	}
	if (IsNPCTeamLeader() && GetParent()->team.IsValid())
	{
		_first_follower = player_team::GetFirstFollow(GetParent()->team.id, _first_follower_index);
	}
	return gnpc_imp::OnHeartbeat();
}

void gplayer_npc_imp::OnSkillCast(int skill_id, const XID& target)
{
	bool can_attack = CheckCanAttack(target);
	if (can_attack)
	{
		GetGuard().TryCastActiveSkill(this, target);
		_retinue.TryCastActiveSkill(target);
	}

	OnMasterSkillCast(skill_id, can_attack);
	GetMech().OnUseSkill(skill_id);
}

void gplayer_npc_imp::UpdateCombatTimer(int timeout)
{
	if (!GetPubg().IsActive(this))
	{
		return;//目前只有吃鸡才需要让player_npc脱战
	}
	class CombatCB : public timeout_ctrl::callback
	{
		virtual int OnTimeout(gcreature_imp *pImp, int index)
		{
			//离开战斗状态
			((gplayer_npc_imp *)pImp)->ActiveCombatState(false, 0, true);
			return -1;
		}
		virtual bool NeedRelease()
		{
			return false;
		}
	};
	static CombatCB ccb;
	_timeout.Insert(TM_IDX_COMBATSTATE, timeout, &ccb, true);
}

bool gplayer_npc_imp::ActiveCombatState(bool combat_state, unsigned char reason, bool notify)
{
	if (_attack.IsCombatState() != combat_state)
	{
		if (combat_state)
		{
			_twin.TryCreate();
			GetMech().OnEnterCombat();
			GetPubg().OnEnterCombat(this);
			GetHeirControl().OnEnterCombat();
		}
		else
		{
			GetGuard().OnLeaveCombat(this);
			_retinue.OnLeaveCombat();
			_twin.TryDelete();
			GetMech().OnLeaveCombat();
			GetPubg().OnLeaveCombat(this);
			GetHeirControl().OnLeaveCombat();
			for (int i = 0; i < PLAYER_REPLISOME_MAX; ++i)
			{
				_replisome[i].TryDelete();
			}
			GetProf12().TryDelete();
		}
	}
	if (combat_state)
	{
		UpdateCombatTimer(NORMAL_COMBAT_TIME);
	}
	return gnpc_imp::ActiveCombatState(combat_state, reason, notify);
}

void gplayer_npc_imp::BeforeDoDamage(int64_t& damage, const attacker_info_t *atk_info, bool ignore_invincible)
{
	gnpc_imp::BeforeDoDamage(damage, atk_info, ignore_invincible);

	//致死
	if (GetPubg().IsActive(this))
	{
		int64_t hp = GetHP();
		if (hp > 0 && hp - damage < 1)
		{
			_filter_man.EF_FatalDamage();
			if (!ignore_invincible && GetOverwhelmingMode())
			{
				//角色受到致死伤害，但是由于技能效果而免死
				GetPubg().SetNearDeathAttacker(this, atk_info ? atk_info->attacker : XID());
				damage = 0;
			}
		}
	}
}

void gplayer_npc_imp::FillAttackMsg(const XID& target, attack_msg& attack)
{
	gcreature_imp::FillAttackMsg(target, attack);
	attack.attacker_info.attacker_mode |= attacker_info_t::AM_PLAYER_NPC;
	if (_whoCastSkill == GUARD_CAST_SKILL)
	{
		attack.attacker_info.attacker_mode |= attacker_info_t::AM_GUARD;
	}
	else if (_whoCastSkill == RETINUE_CAST_SKILL)
	{
		attack.attacker_info.attacker_mode |= attacker_info_t::AM_RETINUE;
	}
	else if (_whoCastSkill == DRAGONBORN_CAST_SKILL)
	{
		attack.attacker_info.attacker_mode |= attacker_info_t::AM_DRAGONBORN;
	}
}

void gplayer_npc_imp::FillPartnerAttackMsg(int partner_type, const XID& target, attack_msg& attack)
{
	if (partner_type == GUARD_CAST_SKILL)
	{
		GetGuard().FillAttackMsg(this, target, attack);
	}
	else if (partner_type == RETINUE_CAST_SKILL)
	{
		_retinue.FillAttackMsg(target, attack);
	}
	else if (partner_type == DRAGONBORN_CAST_SKILL)
	{
		//技能不会自动施法
	}
}

extern void GetPlayerManualSkills(const gcreature_imp *const imp, player_rune_manager& rune, player_auto_combat_set& auto_combat_set, player_kotodama& kotodama, std::vector<std::tuple<int, int>>& skill_arr);

void gplayer_npc_imp::GetAICastSkills(std::vector<std::tuple<int, int>>& skill_arr)
{
	int transform_tid = GetTransformTid();
	if (transform_tid > 0)
	{
		const TRANSFORM_CONFIG *temp = transform_manager::GetInstance().GetTemplate(transform_tid);
		if (temp)
		{
			player_transform::GetAiCastSkillsByTransformTemplate(temp, skill_arr);
		}
		return;
	}
	if (GetPubg().IsActive(this))
	{
		GetPubg().GetAICastSkills(skill_arr);
		return;
	}
	GetPlayerManualSkills(this, _stunt_rune, _auto_combat_set, _kotodama, skill_arr);
}

void gplayer_npc_imp::PrintProperty()
{
	if (!__PRINT_FLAG)
	{
		return;
	}

	std::ostringstream ss;
#define ONE_PROP(name) ss << #name << "=" << GetProperty().GetGProperty()->PROP_NAME_GET(name) << " ";

	ONE_PROP(maxHP)//血
	ONE_PROP(curPhyAtk)//双攻
	ONE_PROP(curMagAtk)
	ONE_PROP(curPhyDef)//双防
	ONE_PROP(curMagDef)
	ONE_PROP(curPierceLevel)//破甲等级
	ONE_PROP(curCDReduLevel)//冷却缩减等级
	ONE_PROP(curCritLevel)//暴击等级
	ONE_PROP(curCritResLevel)//暴抗等级
	ONE_PROP(curCritRatio)//暴击伤害
	ONE_PROP(curPsychokinesisLevel)//多重打击等级
	ONE_PROP(curHit)//命中等级
	ONE_PROP(curEvade)//闪避等级
	ONE_PROP(curPVPLevel)//强击等级（御剑破魂）
	ONE_PROP(curPVPResLevel)//韧性等级（御剑罡体）
	ONE_PROP(curStun)//硬直
	ONE_PROP(curStunRes)//硬直抵抗

	__PRINTF("gplayer_npc_imp::prop %s\n", ss.str().c_str());

	std::vector<std::tuple<int, int>> skill_arr;
	GetAICastSkills(skill_arr);
	ss.str("");
	for (auto& it : skill_arr)
	{
		ss << std::get<0>(it) << ":" << std::get<1>(it) << " ";
	}
	__PRINTF("gplayer_npc_imp::skill id:level %s\n", ss.str().c_str());
}

void gplayer_npc_imp::SendBuffData(const subscibe_id *begin, const subscibe_id *end, const PB::buff_t *buff/*=NULL*/)
{
	if (begin == end)
	{
		return;
	}

	PB::gp_object_buff proto;
	if (buff)
	{
		proto.set_mode(PB::gp_object_buff::ONE);
		PB::buff_t *b = proto.add_buff_list();
		b->CopyFrom(*buff);
	}
	else
	{
		proto.set_mode(PB::gp_object_buff::ALL);
		_buff.GetBuff(proto);
	}
	proto.set_id(_parent->ID.id);

	packet_tla_wrapper h1(256);
	S2C::CMD::Make<S2C::CMD::PBS2C>::From(h1, proto);
	//发给选中对象
	for (const subscibe_id *temp = begin; temp != end; ++temp)
	{
		send_ls_msg(temp->lid, h1);
	}
}

bool gplayer_npc_imp::CheckNewDiaoxiang(int diaoxiang_id)
{
	if (GetParent()->diaoxiang_id <= 0 || GetParent()->diaoxiang_id != diaoxiang_id)
	{
		return false;
	}

	bool delay_init = false;
	do
	{
		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{
			delay_init = true;
			break;
		}
		if (!ipd->ip_global_data_manager.IsInit())
		{
			delay_init = true;
			break;
		}

		GNET::Octets diaoxiangdata;
		if (!ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_DIAOXIANG, GetParent()->diaoxiang_id), diaoxiangdata))
		{
			delay_init = true;
			break;
		}
		unsigned short cur_crc = crc16((const unsigned char *)diaoxiangdata.begin(), diaoxiangdata.size());

		GNET::Octets edit_diaoxiangdata;
		ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_DIAOXIANG_EDIT, GetParent()->diaoxiang_id), edit_diaoxiangdata);
		unsigned short cur_edit_data_crc = crc16((const unsigned char *)edit_diaoxiangdata.begin(), edit_diaoxiangdata.size());

		if (cur_crc == _diaoxiang_crc && cur_edit_data_crc == GetParent()->edit_data_crc)
		{
			break;
		}

		// 发消息重新创建一个雕像（因为只有在EnterScene的时候才能向周围玩家更新形象）
		MSG msg;
		BuildMessage3(msg, GM_MSG_DIAOXIANG_CREATE, GetRealSceneImp()->Parent()->xid, GetParent()->ID, GetParent()->pos, _template->tid, GetParent()->dir, GetParent()->diaoxiang_id);
		gmatrix::GetInstance().SendMessage(msg);

		// 让自己死掉
		LifeExhaust();
	}
	while (false);

	if (delay_init)
	{
		// 给自己发个延时消息
		int delay_seconds = abase::Rand(10, 20);
		gmatrix::GetInstance().LazySendTo(GM_MSG_DIAOXIANG_CHECK_NEW, GetParent()->ID, GetParent()->ID, A3DVECTOR3(0, 0, 0), GetParent()->diaoxiang_id, SECOND_TO_TICK(delay_seconds));
		return false;
	}
	return true;
}

bool gplayer_npc_imp::TryInitDiaoxiang()
{
	if (GetParent()->diaoxiang_id <= 0)
	{
		return false;
	}

	bool delay_init = false;
	do
	{
		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{
			delay_init = true;
			break;
		}
		if (!ipd->ip_global_data_manager.IsInit())
		{
			delay_init = true;
			break;
		}

		GNET::Octets diaoxiangdata;
		if (!ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_DIAOXIANG, GetParent()->diaoxiang_id), diaoxiangdata))
		{
			delay_init = true;
			break;
		}
		unsigned short cur_crc = crc16((const unsigned char *)diaoxiangdata.begin(), diaoxiangdata.size());

		GNET::Octets edit_diaoxiangdata;
		ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_DIAOXIANG_EDIT, GetParent()->diaoxiang_id), edit_diaoxiangdata);
		unsigned short cur_edit_data_crc = crc16((const unsigned char *)edit_diaoxiangdata.begin(), edit_diaoxiangdata.size());

		if (cur_crc == _diaoxiang_crc && cur_edit_data_crc == GetParent()->edit_data_crc)
		{
			break;
		}

		GNET::GRoleBase base;
		GNET::GRoleStatus status;
		try
		{
			GNET::Marshal::OctetsStream ss(diaoxiangdata);
			ss >> base >> status;
		}
		catch (...)
		{
			GLog::log(LOG_ERR, "gplayer_npc_imp::TryInitDiaoxiang unmarshal err. tid=%d, diaoxiang_id=%d", _template->tid, GetParent()->diaoxiang_id);
			break;
		}

		GDB::base_info info;
		GDB::vecdata data;
		if (!GDB::Role2Info(&status, &base, NULL, info, data))
		{
			GLog::log(LOG_ERR, "gplayer_npc_imp::TryInitDiaoxiang Role2Info err. tid=%d, diaoxiang_id=%d", _template->tid, GetParent()->diaoxiang_id);
			break;
		}
		creature_prop prop(NULL);
		equipment_inventory::equipment_brief eb_list[EQUIPIVTR_SIZE];
		memset(&eb_list, 0, sizeof(eb_list));
		struct FOO
		{
			static int collect_e_brief(void *arg, item *pItem, int index)
			{
				if (index < 0 || index >= EQUIPIVTR_SIZE)
				{
					return 0;
				}
				auto *pList = (equipment_inventory::equipment_brief *)arg;
				pList[index].tid = pItem->GetTID();
				pList[index].star = pItem->GetStar();
				return 0;
			}
		};
		//计算出属性
		std::vector<int> equip_skill;
		int rst = BuildCommonProperty(prop, info.prof, info.level, info.prof_level,
		                              data.property.data, data.property.size,
		                              data.shadow_equip.data, data.shadow_equip.size,
		                              NULL, FOO::collect_e_brief, eb_list, equip_skill);
		if (rst)
		{
			GLog::log(LOG_ERR, "gplayer_npc_imp::TryInitDiaoxiang BuildCommonProperty err. tid=%d, diaoxiang_id=%d", _template->tid, GetParent()->diaoxiang_id);
			break;
		}

		level_control::player_summon_t param;
		memset(&param, 0, sizeof(param));
		param.player_id = info.player_id;
		param.level = info.level;
		equipment_inventory::AddEquipBriefList(param.equip_mask, param.equip_data, eb_list, EQUIPIVTR_SIZE);
		param.armor_tid = eb_list[EQUIPIVTR_ARMOUR].tid;
		param.armor_star = eb_list[EQUIPIVTR_ARMOUR].star;
		param.weapon_tid = eb_list[EQUIPIVTR_WEAPON].tid;
		param.weapon_star = eb_list[EQUIPIVTR_WEAPON].star;

		param.name =  data.player_name.data;
		param.name_size = data.player_name.size;
		param.faceid = info.faceid;
		param.gender = info.gender;
		param.prof = info.prof;
		param.body_size = info.body_size;
		param.race = info.race;
		param.idphoto = info.idphoto;

		CopyFashionsToArray(param.fashions, data.fashions);

		param.skill_data = data.skill_data.data;
		param.skill_size = data.skill_data.size;
		param.nation_id = info.nation;
		param.current_title = info.cur_title;

		param.title_list = data.title_list.data;
		param.title_list_size = data.title_list.size;

		param.shadow_pet = data.shadow_pet.data;
		param.shadow_pet_size = data.shadow_pet.size;

		param.univ_data = data.univ_data.data;
		param.univ_data_size = data.univ_data.size;

		param.guard_data = data.guard_data.data;
		param.guard_data_size = data.guard_data.size;

		param.appearance = data.appearance.data;
		param.appearance_size = data.appearance.size;

		GetParent()->SetObjectState(gobject::STATE_NPC_PLAYER);
		GetParent()->SetObjectState(gobject::STATE_PLAYER_NPC_TITLE);
		GetParent()->SetObjectState2(gobject::STATE2_DIAOXIANG);
		GetParent()->base_info.faceid = info.faceid;
		GetParent()->base_info.gender = info.gender;
		GetParent()->base_info.player_prof = info.prof;
		GetParent()->base_info.idphoto = info.idphoto;
		GetParent()->body_physic = info.body_size;
		GetParent()->base_info.race = info.race;
		GetParent()->fake_roleid = info.player_id;
		GetParent()->weapon_id = param.weapon_tid;
		GetParent()->weapon_star = param.weapon_star;
		//复制名字
		GetParent()->player_name_size = data.player_name.size < MAX_PLAYER_NAME_LENGTH ? data.player_name.size : MAX_PLAYER_NAME_LENGTH;
		memcpy(GetParent()->player_name_buf, data.player_name.data, GetParent()->player_name_size);
		//复制自定义雕像数据
		GetParent()->edit_data_size = edit_diaoxiangdata.size() < MAX_PLAYER_NPC_EDIT_DATA_SIZE ? edit_diaoxiangdata.size() : MAX_PLAYER_NPC_EDIT_DATA_SIZE;
		memcpy(GetParent()->edit_data, edit_diaoxiangdata.begin(), GetParent()->edit_data_size);
		GetParent()->edit_data_crc = crc16(GetParent()->edit_data, GetParent()->edit_data_size);

		_prop.SetProf(info.prof);
		_prop.SetBasicData(info.level, 0, 0, 0);

		GDB::hero_list heros;
		if (!OnCreate(param, heros))
		{
			GLog::log(LOG_WARNING, "NPC:%d 雕像:%d  装载出错\n", _template->tid, GetParent()->diaoxiang_id);
		}

		_diaoxiang_crc = cur_crc;

		LOG_TRACE("NPC:%d 雕像:%d roleid:%ld 加载成功", _template->tid, GetParent()->diaoxiang_id, info.player_id);
	}
	while (false);

	if (delay_init)
	{
		// 给自己发个延时消息
		int delay_seconds = abase::Rand(10, 20);
		gmatrix::GetInstance().LazySendTo(GM_MSG_DIAOXIANG_CHECK_NEW, GetParent()->ID, GetParent()->ID, A3DVECTOR3(0, 0, 0), GetParent()->diaoxiang_id, SECOND_TO_TICK(delay_seconds));
		return false;
	}
	return true;
}

void gplayer_npc_imp::EnterScene(gscene_imp *pSceneImp)
{
	GetParent()->diaoxiang_id = _cnt.diaoxiang_id > 0 ? _cnt.diaoxiang_id : _template->diaoxiang_id;
	TryInitDiaoxiang();
	if (GetParent()->diaoxiang_id > 0)
	{
		if (GetParent()->diaoxiang_id >= 2000)
		{
			GetParent()->SetObjectState2(gobject::STATE2_SPECIAL_NPC_PLAYER);
		}
		GetRealSceneImp()->InsertDX(GetParent()->ID);
	}
	if (IsValidPlayerNpcType(_cnt.player_npc_type) && _cnt.player_npc_type != PNT_NONE)
	{
		GetRealSceneImp()->InsertPlayerNpc(_cnt.player_npc_type, GetParent()->ID);
	}
	gnpc_imp::EnterScene(pSceneImp);
	if (GetProf6())
	{
		GetProf6()->OnEnterScene();
	}

	// 冷却缩减增加属性
	AddPropertyByCdReduLevel();
}

void gplayer_npc_imp::LeaveScene()
{
	if (GetParent()->diaoxiang_id > 0)
	{
		GetRealSceneImp()->RemoveDX(GetParent()->ID);
	}
	if (IsValidPlayerNpcType(_cnt.player_npc_type) && _cnt.player_npc_type != PNT_NONE)
	{
		GetRealSceneImp()->RemovePlayerNpc(_cnt.player_npc_type, GetParent()->ID);
	}
	gnpc_imp::LeaveScene();
	GetMech().OnLeaveScene();
	GetHeirControl().OnLeaveScene();
}

void gplayer_npc_dispatcher::be_healed(const XID& attacker, const link_id_t& lid, float hp)
{
	if (attacker.IsPlayer())
	{
		packet_tla_wrapper h1(1024);
		using namespace S2C;
		CMD::Make<CMD::be_healed>::From(h1, attacker, _imp->Parent()->ID, hp);
		send_ls_msg(lid, h1);
	}
}

void gplayer_npc_imp::SelectTarget(const XID& target, bool is_client_cmd)
{
	gnpc_imp::SelectTarget(target, is_client_cmd);
	GetMech().OnSelectTarget(target);
	GetHeirControl().OnSelectTarget(target);
}

void gplayer_npc_imp::UnselectTarget()
{
	gnpc_imp::UnselectTarget();
	GetMech().OnCancelTarget();
	GetHeirControl().OnCancelTarget();
}

void gplayer_npc_imp::FillInfoForMech(PB::player_mech::common_info& info)
{
	info.set_mafia_id(GetMafiaID());
	info.set_faction(GetFaction());
	info.set_region_setting(GetRegionSetting());
	info.set_mentor_id(GetParent()->mentor_id);
}

void gplayer_npc_imp::FillInfoForHeir(PB::player_heir::common_info& info)
{
	info.set_mafia_id(GetMafiaID());
	info.set_faction(GetFaction());
	info.set_region_setting(GetRegionSetting());
	info.set_mentor_id(GetParent()->mentor_id);
}

void gplayer_npc_imp::BeginTransform(int tid, int attacker_newid)
{
	gnpc_imp::BeginTransform(tid, attacker_newid);
	if (_ai_ctrl)
	{
		_ai_ctrl->InitSkill();
	}
}

void gplayer_npc_imp::EndTransform()
{
	gnpc_imp::EndTransform();
	if (_ai_ctrl)
	{
		_ai_ctrl->InitSkill();
	}
}

void gplayer_npc_imp::OnDeath(const XID& last_attacker, const attacker_info_t *pInfo, skill_id_t kill_skill_id, unsigned char kill_attack_stage, bool ignore_statistics)
{
	gnpc_imp::OnDeath(last_attacker, pInfo, kill_skill_id, kill_attack_stage, ignore_statistics);
	GetMech().OnDeath();
	GetPubg().OnDeath(this);
	GetHeirControl().OnDeath();
}

void gplayer_npc_imp::GetCommonAttackSkill(std::vector<int>& arr)
{
	if (GetPubg().IsActive(this))
	{
		GetPubg().GetAICastCommonSkills(arr);
	}
	else
	{
		_stunt_rune.GetCommonAttackSkill(this, arr);
	}
}

void gplayer_npc_imp::OnStanceChange()
{
	_prof6.OnStanceChange();
}

bool gplayer_npc_imp::GetAIPriorSkill(int& id, int& level)
{
	if (GetProf() != PROFTYPE_BOW)
	{
		return false;
	}
	int cat_mp4 = 0;
	int bird_mp4 = 0;
	_auto_combat_set.GetProf6SwapStanceMp4(cat_mp4, bird_mp4);
	return _prof6.GetAIPriorSkill(cat_mp4, bird_mp4, id, level);
}
void gplayer_npc_imp::InitPlayerReplisome()
{
	for (unsigned int i = 0 ; i < PLAYER_REPLISOME_MAX; ++i)
	{
		_replisome[i].init(this, i);
	}
}
unsigned int gplayer_npc_imp::TryCreateReplisome()
{
	if (GetProf() != PROFTYPE_10)
	{
		return 0xff;
	}
	gplayer *player = (gplayer *)GetParent();
	const unsigned int index = player->GetNextReplisomeIndex();
	__PRINTF("_replisome::trycreate index =%d\n", index);
	if (index >= PLAYER_REPLISOME_MAX )
	{
		return 0xff;
	}
	return _replisome[index].TryCreate();
}
void gplayer_npc_imp::TryDeleteReplisome(unsigned int index )
{
	__PRINTF("_replisome::trydelete index =%d\n", index);
	if (index < PLAYER_REPLISOME_MAX)
	{
		_replisome[index].TryDelete();
	}
}
int gplayer_npc_imp::GetPlayerReplisomeCount()
{
	if (GetProf() != PROFTYPE_10)
	{
		return 0x00;
	}
	return GetParent()->GetNowReplisomeCount();
}

void gplayer_npc_imp::TryCreateDog(int skill_level)
{
	GetProf12().TryCreate(skill_level);
}
void gplayer_npc_imp::TryDeleteDog()
{
	GetProf12().TryDelete();
}

void gplayer_npc_imp::FillNpcMasterData(npc_master_data *p_master_data)
{
	if (!p_master_data)
	{
		return;
	}
	_prop.ExportTo(&(p_master_data->_skill_prop));
	p_master_data->_master_dmg_add = CalcMasterDmgAdd();
	GetSkill().FillSkillLevelMap(p_master_data->_cur_skill_map);
	_talent.FillCurTalent(p_master_data->_cur_talent);
	_pureblooded.FillCurTalent(p_master_data->_cur_blood_talent);
	_kotodama.FillKotodama(p_master_data->_cur_kotodama);
	//FormPetMasterData(p_master_data->_master_prop);
}

void gplayer_npc_imp::UpdateExtentInfo()
{
	//通知技能出来的召唤npc阵营等信息
	SyncSummonNpcMasterInfo();
}
