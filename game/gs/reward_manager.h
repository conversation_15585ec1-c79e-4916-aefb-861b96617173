#ifndef __GS_REWARD_MANAGER_H__
#define __GS_REWARD_MANAGER_H__
#include "gprotoc/ipt_team_recharge_op.pb.h"
#include "gprotoc/player_reward_t.pb.h"
#include "gprotoc/player_auto_reward_t.pb.h"
namespace PB
{
namespace player_universal_data_t_NS
{
class player_universal_data_t;
} using namespace player_universal_data_t_NS;
}

#include <common/packet_wrapper.h>
#include <vector>
#include "ExpTypes.h"


#define GS_REWARD_VERSION	0x02
#define GS_AUTO_REWARD_VERSION 0x01 //制造冲突防止合并代码
enum
{
	MAX_REWARD_COUNT = 200,			//200个
	REWARD_EXPIRE_TIME = 7 * 24 * 3600,	//7 天
};

enum DEL_REWARD_REASION
{
	DRR_RECEIVE = 0,			//领取
	DRR_EXPIRE = 1,				//到期
	DRR_WELFARE = 2,			//帮会福利
};

enum REWARD_CATEGORY
{
	CAT_CHARGE	= 1, // 充值类活动
	CAT_LOGIN	= 2, // 登录类活动
	CAT_CONSUME	= 3, // 消费类活动
	CAT_LEVEL	= 4, // 升级类活动
	CAT_BENEFIT	= 5, // 权益类活动
	CAT_HOLIDAY	= 6, // 节日类活动
	CAT_EVENT	= 6, // 事件类活动
};

class gplayer_imp;
struct FuncInfo;

struct reward_template
{
	GRANT_REWARD_TYPE grant_reward_type;
	GRANT_REWARD_MASK grant_reward_mask;
	int basic_exp;
	int basic_exp_adjust_config;
	bool is_stored_exp;
	int basic_bound_money_factor_id;
	int basic_bound_money;
	int basic_trade_money;
	int basic_prof_exp;
	int basic_bound_yuanbao;
	int basic_yuanbao;
	int rune_new_id;
	int world_red_envelop_speak_id;
	int world_red_envelop_reward_id;
	int world_red_envelop_reward_num;
	struct
	{
		int repu_id;
		int repu_value;
	} basic_reput[8];

	struct
	{
		int id;
		int num;
		int genitem_cfg_id;
		int speak_id;
		int world_red_envelop_speak_id;
		int world_red_envelop_reward_id;
		int world_red_envelop_reward_num;
	} items[8];

	int is_count_bind;
	int auto_award;

	struct
	{
		int counter_id;
		int counter_value;
	} counters[5];

	struct
	{
		int switch_type;
		int switch_id;
		int switch_option;
	} switchs[5];

	//int tlog_activity_type;

public:
	void LoadFromConfig(const GRANT_REWARD_CONFIG& config);
	std::string GetItemID();
	std::string GetItemCount();
};

class reward_template_manager
{
	typedef abase::hash_map<tid_t, reward_template> REWARD_TEMPLATE_MAP;
	REWARD_TEMPLATE_MAP _reward_template_map;

public:
	bool Load(elementdataman& data_man);

	const reward_template *Get(tid_t tid) const;
	reward_template *Get(tid_t tid);

	static reward_template_manager& GetInstance()
	{
		static reward_template_manager _s_instance;
		return _s_instance;
	}
};

/*struct reward_t
{
    tid_t tid;			//奖励模板id
    int timestamp;			//奖励获得时间
    unsigned int mask;		//保存奖励时的掩码,这个需要保存,考虑到模板可能会变化
    exp_t exp;
    money_t bind_money;
    money_t trade_money;
    int prof_exp;
    int bind_yuanbao;
    int repu_value[8];
    int item_count[8];
    bool IsEmpty();
};*/

typedef PB::player_reward_t reward_t;

/*
//管理奖励倍率相关脚本，玩家、镖车身上都会有一份
class reward_ratio_manager
{
public:
    void GetRewardRatio(gcreature_imp* imp,int type,float* ratio,size_t count,int param1=0,int param2=0,int param3=0,int param4=0);
    bool GetSkillRewardConfig(int money_config_id,MONEY_TYPE& money_type,money_t& money,float& odds);
private:
    void LoadRewardRatio(abase::vector<float>& result,float* ratio,size_t count);
    //lua脚本相关
public:
    static bool InitScriptEnv();
private:
    static int script_GetPlayerLevel(lua_State* L);
    static int script_GetEscortCityCount(lua_State* L);
    static int script_GetEscortGoodsQuality(lua_State* L);
    static int script_GetEscortFactionParam(lua_State* L);
};
*/

//player reward管理
class player_reward
{
	typedef std::vector<reward_t> REWARD_VEC;
	REWARD_VEC _reward_vec;
	//reward_ratio_manager _ratio_man;

	void SaveReward(archive& ar) const;

public:
	void Clear();
	void Load(archive& ar);
	void Save(archive& ar) const;
	void SaveForClient(archive& ar) const;

	void SendRewardInfoToClient(gplayer_imp *imp);

	void OnLogin(gplayer_imp *imp);

	void Heartbeat(gplayer_imp *imp);

	bool CanDeliverReward(gplayer_imp *imp, const reward_t& rt) const;
	int AddReward(gplayer_imp *imp, GRANT_REWARD_TYPE reward_type, tid_t reward_tid, float *ratios, size_t ratio_count);
	int AddReward(gplayer_imp *imp, GRANT_REWARD_TYPE reward_type, reward_t& rt);
	int AddReward(gplayer_imp *imp, const FuncInfo& fi, GRANT_REWARD_TYPE reward_type, tid_t reward_tid, float *ratios, size_t ratio_count);
	int AddReward(gplayer_imp *imp, const FuncInfo& fi, GRANT_REWARD_TYPE reward_type, reward_t& rt);
	void DelReward(gplayer_imp *imp, unsigned char index, unsigned char reason);
	void DelReward(gplayer_imp *imp, GRANT_REWARD_TYPE reward_type, unsigned char reason);
	void OnReceiveOneReward(gplayer_imp *imp, unsigned char index, int tid, int64_t extra_param);
	bool AwardReward(gplayer_imp *imp, reward_t& rt, bool need_cache = false, bool direct_delivery_item = true, bool direct_delivery_other = true);
	void TempRewardLog(gplayer_imp *imp, const reward_t& rt, int add_or_reduce, int reason = 0);

	void GetRewardRatio(gplayer_imp *imp, int type, float *ratio, size_t count, int param1 = 0, int param2 = 0, int param3 = 0, int param4 = 0);
	//技能得钱配置
	bool GetSkillRewardConfig(int money_config_id, MONEY_TYPE& money_type, money_t& money, float& odds);
	size_t RewardCount() const
	{
		return _reward_vec.size();
	}
	void CheckActivityInstanceRewardAddon(gplayer_imp *imp, const FuncInfo& fi, tid_t originalReward, float *ratios, size_t ratio_count);
	void OnDelRoleRewardBagItem(gplayer_imp *imp, int itemid, int item_count);
};

class auto_reward_base
{
public:
#pragma pack(1)
	struct entry_t
	{
		int type = 0;
		PB::player_auto_reward_t data;

		bool sync_client = false;	//是否已经同步过客户端
		int64_t client_data = 0;	//通知客户端的属性

		int64_t unsave_param1 = 0;

		entry_t& operator=(const entry_t& rhs)
		{
			type = rhs.type;
			data = rhs.data;
			sync_client = rhs.sync_client;
			client_data = rhs.client_data;
			return *this;
		}
		bool operator != (const entry_t& rhs)
		{
			return !(*this == rhs);
		}
		bool operator == (const entry_t& rhs)
		{
			if (type != rhs.type)
			{
				return false;
			}
			if (data.reward() != rhs.data.reward())
			{
				return false;
			}
			if (data.params_size() != rhs.data.params_size())
			{
				return false;
			}
			for (int i = 0; i < data.params_size(); ++ i)
			{
				if (data.params(i) != rhs.data.params(i))
				{
					return false;
				}
			}
			if (sync_client != rhs.sync_client)
			{
				return false;
			}
			if (client_data != rhs.client_data)
			{
				return false;
			}

			return true;
		}
		DEFINE_PB_PARAM(data, reward, int);
#define PARAM(TYPE, INDEX) \
        TYPE param##INDEX()\
        { \
            for (int i = data.params_size(); i < INDEX; ++ i) \
            { \
                data.add_params(0); \
            } \
            return data.params(INDEX - 1); \
        } \
        void param##INDEX(TYPE value) \
        { \
            for (int i = data.params_size(); i < INDEX; ++ i) \
            { \
                data.add_params(0); \
            } \
            data.set_params(INDEX - 1, value); \
        }
		PARAM(int, 1)
		PARAM(int64_t, 2)
		PARAM(int, 3)
		PARAM(int, 4)
		PARAM(int, 5)
		PARAM(int64_t, 6)
#undef PARAM
	};
#pragma pack()
	virtual ~auto_reward_base() {}

	static bool Save(gplayer_imp *pImp, const entry_t& entry, archive& ar)
	{
		char tmp[entry.data.ByteSize()];
		entry.data.SerializeWithCachedSizesToArray((unsigned char *)tmp);
		ar << (unsigned short)entry.data.ByteSize();
		ar.push_back(tmp, entry.data.ByteSize());
		return true;
	}
	static bool Load(gplayer_imp *pImp, entry_t& entry, archive& ar, short version)
	{
		unsigned short size = 0;
		ar >> size;
		if (!entry.data.ParseFromArray(ar.cur_data(), size))
		{
			return false;
		}
		ar.shift(size);
		return true;
	}
	virtual void OnInit(gplayer_imp *imp, entry_t& ent) {}
	virtual bool ReInit(gplayer_imp *imp, entry_t& ent)
	{
		return false;
	}
	virtual bool PreGetReward(gplayer_imp *imp, entry_t& ent)
	{
		return true;
	}
	virtual void OnGetReward(gplayer_imp *imp, entry_t& ent) {}
	virtual void PostGetReward(gplayer_imp *imp, entry_t& ent) {}
	virtual void DailyUpdate(gplayer_imp *imp, entry_t& ent) {}		//0点
	virtual void DailyUpdate6(gplayer_imp *imp, entry_t& ent) {}		//6点调用
	virtual void OnCallback(gplayer_imp *imp, entry_t& ent) {}
	virtual void OnLogin(gplayer_imp *imp, entry_t& ent) {}	//进入世界时调用
	virtual void OnSave(gplayer_imp *imp, entry_t& ent) {}
	virtual void OnModifyReputation(gplayer_imp *imp, entry_t& ent) {}
	virtual void OnModifyReputationRepu(gplayer_imp *imp, entry_t& ent, int repu_id) {}
	virtual int64_t GetClientData(gplayer_imp *imp, entry_t& ent)
	{
		return 0;
	}
	virtual int64_t GetClientData2(gplayer_imp *imp, entry_t& ent)
	{
		return 0;
	}
	virtual int GetType()
	{
		return -1;
	}
	virtual int GetCategory()
	{
		return CAT_CHARGE;
	}
	virtual GRANT_REWARD_TYPE GetRewardType() const
	{
		return GRANT_REWARD_AUTO_REWARD;
	}
	virtual char GetRewardExtraPercent() const
	{
		return 0;    // 额外奖励百分比
	}
	virtual int  GetTaskIDForExtraReward() const
	{
		return 0;    // 额外奖励对应的任务
	}
	virtual bool PlayerOperation(gplayer_imp *pImp, int op, uint64_t value, entry_t& ent, int cost_value)
	{
		return false;
	}
	//virtual void OnMidasChanged(gplayer_imp* pImp, entry_t & ent) {}
	virtual void OnJoinNationWar(gplayer_imp *pImp, entry_t& ent, int war_id) {}
	virtual void OnUpdateGrcFriendCount(gplayer_imp *pImp, entry_t& ent, int count) {}
	virtual void OnGetPlatVIP(gplayer_imp *pImp, entry_t& ent) {}
	virtual void OnGetRechargeMsg(gplayer_imp *pImp, entry_t& ent, int vipkindMsk) {}
	virtual void OnCashUpdate(gplayer_imp *pImp, entry_t& ent) {}	//充值的时候会调用这里
	virtual void OnDayRechargeUpdate(gplayer_imp *pImp, entry_t& ent) {}	//每日充值变化会调用这个  这个函数需要自己判断今日充值数量。因为前面函数堆栈是没有判断条件的，充值了一定会调用，但是不充值也有可能触发，login时会调用三次，尽量使用ds的充值数据
	virtual void OnRechargeUpdate(gplayer_imp *pImp, entry_t& ent) {}	//总充值变化会调用这个
	virtual void OnConsume(gplayer_imp *pImp, entry_t& ent, int count, const FuncInfo& fi) {}	//消费
	virtual void OnFundActivedUpdate(gplayer_imp *pImp, entry_t& ent) {}	//每日基金变化
	virtual void OnMonthCardUpdate(gplayer_imp *pImp, entry_t& ent) {}	//月卡时间变化
	virtual void OnTimeLimitUpdate(gplayer_imp *pImp, entry_t& ent) {}	//限时活动变化
	virtual void OnCashInfo(gplayer_imp *pImp, entry_t& ent) {}	//初始化元宝信息
	virtual void OnFirstRechargeAwardUpdate(gplayer_imp *pImp, entry_t& ent) {}	//首冲奖励
	virtual void OnFirstRechargeDoubleUpdate(gplayer_imp *pImp, entry_t& ent) {}	//首冲双倍
	virtual void OnVIPLevelChanged(gplayer_imp *pImp, entry_t& ent) {}	//VIP等级变化
	virtual void OnSecondRechargeUpdate(gplayer_imp *pImp, entry_t& ent) {}	//次充奖励
	virtual void OnAchievementRechargeUpdate(gplayer_imp *pImp, entry_t& ent) {}	//成就充值
	virtual void OnSetOldPlayerBackState(gplayer_imp *pImp, entry_t& ent) {}  //设置 万老家状态时
	virtual void OnTssFirstRechargeUpdate(gplayer_imp *pImp, entry_t& ent) {}	//腾讯首充
	virtual void OnTssGiftsUpdate(gplayer_imp *pImp, entry_t& ent) {}	//腾讯礼包
	virtual void OnTssLevelFundUpdate(gplayer_imp *pImp, entry_t& ent) {}	//腾讯等级成长基金
	virtual void OnTssQuarterCardUpdate(gplayer_imp *pImp, entry_t& ent) {}	//腾讯季卡
	virtual void OnFriendHelpCountUpdate(gplayer_imp *pImp, entry_t& ent) {}	//好友助力变化
	virtual void OnAchievementComplete(gplayer_imp *pImp, entry_t& ent) {}	//完成成就
	virtual void OnTriggerGift(gplayer_imp *pImp, entry_t& ent) {}	//触发奖励
	virtual void OnTssTriggerGiftUpdate(gplayer_imp *pImp, entry_t& ent) {}	//购买触发奖励服务
	virtual void OnSpecialCardUpdate(gplayer_imp *pImp, entry_t& ent) {}   // 触发特殊周卡权益
	virtual void OnLotteryTriggerGift(gplayer_imp *pImp, entry_t& ent, int dice, int lottery_count, const std::set<int>& item_ids, int succ_count) {}  // 彩票触发礼包

	virtual void OnTeamRechargeChange(gplayer_imp *pImp, entry_t& ent, PB::ipt_team_recharge_op& cmd) {} // 组队充值变化
	virtual void OnTeamRechargeOp(gplayer_imp *pImp, entry_t& ent, PB::ipt_team_recharge_op& cmd) {}
	virtual int64_t GetTeamRechargeTotalCount(gplayer_imp *pImp, entry_t& ent, int& cur_draw_count)
	{
		return 0;
	}
	virtual void AddTeamRechargeLotteryCount(gplayer_imp *pImp, entry_t& ent, int count) {}
	virtual int64_t GetTurntableSilverCount(gplayer_imp *pImp, entry_t& ent) { return 0; }
	virtual void AddTurntableSilverUseCount(gplayer_imp *pImp, entry_t& ent, int count) {}

	virtual void DeliverAnniversaryReward(gplayer_imp *pImp, entry_t& ent) {}	//周年奖励
	virtual void OnDsSyncReputationTrrige(gplayer_imp *pImp, entry_t& ent) {}
	virtual void OnAchievementComplete(gplayer_imp *pImp, entry_t& ent, int achievement_id) {}
	virtual void OnCombatPowerChange(gplayer_imp *pImp, entry_t& ent) {} // 战力基金：战力变化
	enum
	{
		REWARD_NULL	= 0,
		//值一旦确定，不能随便改了，存盘的
		REWARD_SIGN_IN		= 1,	//签到奖励(每签到一次加一天，满多少天就重新开始，不会每周或者每月重置)
		REWARD_ONLINE		= 2,	//在线奖励(每日清空，根据累积在线时间分段领取奖励)
		REWARD_OPEN			= 3,	//开服奖励(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_MONTH_CARD 	= 4,	//月卡(购买月卡之后加30天，每天可以领一次奖励)
		REWARD_M_ACTIVITY	= 5,	//月活跃度(根据月活跃度声望段来领奖，每月清空一次)
		REWARD_D_ACTIVITY	= 6,	//日活跃度(根据日活跃度声望段来领奖，每日清空一次)
		REWARD_WEEK			= 7,	//周奖励(每周几可以领取一次奖励，每周1清空)
		REWARD_LOGIN_DAYS	= 8,	//登录奖励(和REWARD_OPEN类似，改成按照登陆天数来领奖，终身只能领一次)
		REWARD_LEVEL		= 9,	//等级礼包(达到某等级领取奖励，终身只能领一次)
		REWARD_CONTINUOUS_LOGIN	= 10,	//连续登陆(每天连续登陆，累积每达到7天就能领一次奖励，签到不连续累积天数清空，已经获得的奖励次数可以累积)
		REWARD_MONTH_SIGN_IN = 11,	//月签到(每天签到领取奖励，每个月重置一次，可以补签本月的)
		REWARD_FIRST_RECHARGE = 12,	//首冲奖励
		REWARD_DAY_RECHARGE = 13,	//每日充值
		REWARD_DAY_CONSUME = 14,	//每日消费
		REWARD_RECHARGE		= 15,	//累积充值
		REWARD_CONSUME		= 16,	//累积消费
		REWARD_DAY_FUND		= 17,	//每日成长基金
		REWARD_LEVEL_FUND	= 18,	//等级成长基金
		REWARD_VIP_LEVEL      	= 19,   //VIP等级奖励
		REWARD_WEEK_CARD		= 20,	//周卡
		REWARD_TIME_LIMIT1	= 21,	//限时活动1
		REWARD_TIME_LIMIT2	= 22,	//限时活动2
		REWARD_TIME_LIMIT3	= 23,	//限时活动3
		REWARD_TIME_LIMIT4	= 24,	//限时活动4
		REWARD_TIME_LIMIT5	= 25,	//限时活动5
		REWARD_TIME_LIMIT6	= 26,	//限时活动6
		REWARD_TIME_LIMIT7	= 27,	//限时活动7
		REWARD_TIME_LIMIT8	= 28,	//限时活动8
		REWARD_TIME_LIMIT9	= 29,	//限时活动9
		REWARD_TIME_LIMIT10	= 30,	//限时活动10
		REWARD_FIRST_RECHARGE_DOUBLE1 = 31,	//首冲双倍1
		REWARD_FIRST_RECHARGE_DOUBLE2 = 32,	//首冲双倍2
		REWARD_FIRST_RECHARGE_DOUBLE3 = 33,	//首冲双倍3
		REWARD_FIRST_RECHARGE_DOUBLE4 = 34,	//首冲双倍4
		REWARD_FIRST_RECHARGE_DOUBLE5 = 35,	//首冲双倍5
		REWARD_FIRST_RECHARGE_DOUBLE6 = 36,	//首冲双倍6
		REWARD_FIRST_RECHARGE_DOUBLE7 = 37,	//首冲双倍7
		REWARD_FIRST_RECHARGE_DOUBLE8 = 38,	//首冲双倍8
		REWARD_FIRST_RECHARGE_DOUBLE9 = 39,	//首冲双倍9
		REWARD_FIRST_RECHARGE_DOUBLE10 = 40,	//首冲双倍10
		REWARD_VIP_DAY_SIGN_IN = 41,	//日签到的vip加成
		REWARD_SECOND_RECHARGE = 42,	//次充奖励(就是终身的一次累积充值达到多少的奖励)
		REWARD_TIME_LIMIT_RECHARGE = 43,	//限时累积充值
		REWARD_MULTI_RECHARGE1 = 44,	//多次充值奖励1
		REWARD_MULTI_RECHARGE2 = 45,	//多次充值奖励2
		REWARD_MULTI_RECHARGE3 = 46,	//多次充值奖励3
		REWARD_MULTI_RECHARGE4 = 47,	//多次充值奖励4
		REWARD_MULTI_RECHARGE5 = 48,	//多次充值奖励5
		REWARD_MULTI_RECHARGE6 = 49,	//多次充值奖励6
		REWARD_MULTI_RECHARGE7 = 50,	//多次充值奖励7
		REWARD_MULTI_RECHARGE8 = 51,	//多次充值奖励8
		REWARD_MULTI_RECHARGE9 = 52,	//多次充值奖励9
		REWARD_MULTI_RECHARGE10 = 53,	//多次充值奖励10
		REWARD_TIME_LIMIT_CONSUME = 54, //限时累计消费
		REWARD_SUIXIN_RECHARGE = 55,	//随心好礼充值
		REWARD_OLDPLAYER_BACK_BY_VIP = 56, //回归专属活动 累计登录领取奖励
		REWARD_OLDPLAYER_BACK_BY_OFFTIME = 57, //回归  根据离线时长
		REWARD_OLDPLAYER_BACK_RECHARGE = 58, //老玩家回归 超值购  累计充值 按档位领取奖励
		REWARD_OLDPLAYER_BACK_SUIXIN = 59, //老玩家回归随心购
		REWARD_OLDPLAYER_BACK_LOGIN = 60,//我回来　根据vip等级发奖
		REWARD_CRAZY_CARD1			= 61, //狂欢活动卡1
		REWARD_CRAZY_CARD2     	 	= 62, //狂欢活动卡2
		REWARD_CRAZY_CARD3      	= 63, //狂欢活动卡3
		REWARD_CRAZY_CARD4      	= 64, //狂欢活动卡4
		REWARD_CRAZY_CARD5      	= 65, //狂欢活动卡5
		REWARD_PLAT_VIP_QQ_CENTER = 66, //平台vip--qq游戏中心启动
		REWARD_PLAT_VIP_WEIXIN_CENTER = 67, //平台vip--微信游戏中心启动
		REWARD_SOCIAL_SHOW_DAILY = 68, // 每日分享奖励
		REWARD_SOCIAL_SHOW_ACCUMULATE = 69, // 累计分享奖励
		REWARD_SERVER_OPEN_DAY_STRICT = 70,	//服务器开服天数奖励,严格限制天数
		REWARD_SERVER_OPEN_DAY		= 71,	//服务器开服天数奖励,不严格限制天数
		REWARD_TSS_FIRST_RECHARGE	= 72,	//腾讯首充服务
		REWARD_QUARTER_CARD_FREE1	= 73, //免费季卡1
		REWARD_QUARTER_CARD_FREE2	= 74, //免费季卡2
		REWARD_QUARTER_CARD_COST1_1	= 75, //收费季卡1_1
		REWARD_QUARTER_CARD_COST1_2	= 76, //收费季卡1_2
		REWARD_QUARTER_CARD_COST2_1	= 77, //收费季卡2_1
		REWARD_QUARTER_CARD_COST2_2	= 78, //收费季卡2_2
		REWARD_TSS_GIFTS_1			= 79, //腾讯礼包
		REWARD_TSS_GIFTS_2			= 80, //腾讯礼包
		REWARD_TSS_GIFTS_3			= 81, //腾讯礼包
		REWARD_TSS_GIFTS_4			= 82, //腾讯礼包
		REWARD_TSS_GIFTS_5			= 83, //腾讯礼包
		REWARD_TSS_GIFTS_6			= 84, //腾讯礼包
		REWARD_TSS_GIFTS_7			= 85, //腾讯礼包
		REWARD_TSS_GIFTS_8			= 86, //腾讯礼包
		REWARD_TSS_GIFTS_9			= 87, //腾讯礼包
		REWARD_TSS_GIFTS_10			= 88, //腾讯礼包
		REWARD_TSS_GIFTS_11			= 89, //腾讯礼包
		REWARD_TSS_GIFTS_12			= 90, //腾讯礼包
		REWARD_TSS_GIFTS_13			= 91, //腾讯礼包
		REWARD_TSS_GIFTS_14			= 92, //腾讯礼包
		REWARD_TSS_GIFTS_15			= 93, //腾讯礼包
		REWARD_TSS_LEVEL_FUND_1		= 94, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_2		= 95, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_3		= 96, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_4		= 97, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_5		= 98, //腾讯等级成长基金
		REWARD_PLAT_MEMBER_VIP_QQ_NORMAL = 99,  // QQ普通会员
		REWARD_PLAT_MEMBER_VIP_QQ_SUPER  = 100, // QQ超级会员
		REWARD_VIP_DAILY_GIFTBAG	= 101, //vip每日礼包
		REWARD_VIP_WEEKLY_GIFTBAG	= 102, //vip每周礼包
		REWARD_TSS_GIFTS_16			= 103, //腾讯礼包
		REWARD_TSS_GIFTS_17			= 104, //腾讯礼包
		REWARD_TSS_GIFTS_18			= 105, //腾讯礼包
		REWARD_TSS_GIFTS_19			= 106, //腾讯礼包
		REWARD_TSS_GIFTS_20			= 107, //腾讯礼包
		REWARD_TSS_GIFTS_21			= 108, //腾讯礼包
		REWARD_TSS_GIFTS_22			= 109, //腾讯礼包
		REWARD_TSS_GIFTS_23			= 110, //腾讯礼包
		REWARD_TSS_GIFTS_24			= 111, //腾讯礼包
		REWARD_TSS_GIFTS_25			= 112, //腾讯礼包
		REWARD_TSS_GIFTS_26			= 113, //腾讯礼包
		REWARD_TSS_GIFTS_27			= 114, //腾讯礼包
		REWARD_TSS_GIFTS_28			= 115, //腾讯礼包
		REWARD_TSS_GIFTS_29			= 116, //腾讯礼包
		REWARD_TSS_GIFTS_30			= 117, //腾讯礼包
		REWARD_LADDER_HISTORY_HIGHEST_RANK	= 118,	//屠龙考核历史最高排名奖励
		REWARD_LADDER_FIRST_CLEARANCE		= 119,	//屠龙考核首次通关奖励
		REWARD_SHARE_FRIEND			= 120,	//邀请未注册好友奖励
		REWARD_FRIEND_HELP_REWARD_1	= 121,	//伙伴助力奖励
		REWARD_FRIEND_HELP_REWARD_2	= 122,	//伙伴助力奖励
		REWARD_FRIEND_HELP_REWARD_3	= 123,	//伙伴助力奖励
		REWARD_FRIEND_HELP_REWARD_4	= 124,	//伙伴助力奖励
		REWARD_FRIEND_HELP_REWARD_5	= 125,	//伙伴助力奖励
		REWARD_ACHIEVEMENT_RECHARGE1 = 126,	//成就充值1
		REWARD_ACHIEVEMENT_RECHARGE2 = 127,	//成就充值2
		REWARD_ACHIEVEMENT_RECHARGE3 = 128,	//成就充值3
		REWARD_ACHIEVEMENT_RECHARGE4 = 129,	//成就充值4
		REWARD_ACHIEVEMENT_RECHARGE5 = 130,	//成就充值5
		REWARD_ACHIEVEMENT_RECHARGE6 = 131,	//成就充值6
		REWARD_ACHIEVEMENT_RECHARGE7 = 132,	//成就充值7
		REWARD_ACHIEVEMENT_RECHARGE8 = 133,	//成就充值8
		REWARD_ACHIEVEMENT_RECHARGE9 = 134,	//成就充值9
		REWARD_ACHIEVEMENT_RECHARGE10 = 135,	//成就充值10
		REWARD_ACHIEVEMENT_RECHARGE11 = 136,	//成就充值11
		REWARD_ACHIEVEMENT_RECHARGE12 = 137,	//成就充值12
		REWARD_ACHIEVEMENT_RECHARGE13 = 138,	//成就充值13
		REWARD_ACHIEVEMENT_RECHARGE14 = 139,	//成就充值14
		REWARD_ACHIEVEMENT_RECHARGE15 = 140,	//成就充值15
		REWARD_ACHIEVEMENT_RECHARGE16 = 141,	//成就充值16
		REWARD_ACHIEVEMENT_RECHARGE17 = 142,	//成就充值17
		REWARD_ACHIEVEMENT_RECHARGE18 = 143,	//成就充值18
		REWARD_ACHIEVEMENT_RECHARGE19 = 144,	//成就充值19
		REWARD_ACHIEVEMENT_RECHARGE20 = 145,	//成就充值20
		REWARD_OPEN2				= 146,	//开服奖励2(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_SERVER_OPEN_DAY_NEW_1	= 147,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_2	= 148,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_3	= 149,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_4	= 150,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_5	= 151,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_6	= 152,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_7	= 153,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_8	= 154,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_9	= 155,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_10	= 156,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_11	= 157,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_12	= 158,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_13	= 159,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_14	= 160,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_15	= 161,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_16	= 162,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_17	= 163,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_18	= 164,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_19	= 165,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_20	= 166,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_21	= 167,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_22	= 168,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_23	= 169,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_24	= 170,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_25	= 171,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_26	= 172,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_27	= 173,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_28	= 174,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_29	= 175,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_30	= 176,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_31	= 177,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_32	= 178,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_33	= 179,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_34	= 180,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_35	= 181,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_36	= 182,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_37	= 183,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_38	= 184,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_39	= 185,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_40	= 186,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_41	= 187,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_42	= 188,	//新的服务器开服天数奖励，通过成就来限制
		REWARD_SERVER_OPEN_DAY_NEW_SCORE	= 189,	//新的服务器开服天数积分奖励
		REWARD_W_ACTIVITY				= 190,	//活跃度(根据周活跃度声望段来领奖，每周清空一次)
		REWARD_OPEN3				= 191,	//开服奖励3(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN4				= 192,	//开服奖励4(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN5				= 193,	//开服奖励5(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN6				= 194,	//开服奖励6(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN7				= 195,	//开服奖励7(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN8				= 196,	//开服奖励8(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_DAY_RECHARGE_GIFT_1		= 197,	//每日充值任选奖励1
		REWARD_DAY_RECHARGE_GIFT_2		= 198,	//每日充值任选奖励2
		REWARD_DAY_RECHARGE_GIFT_3		= 199,	//每日充值任选奖励3
		REWARD_DAY_RECHARGE_GIFT_4		= 200,	//每日充值任选奖励4
		REWARD_DAY_RECHARGE_GIFT_5		= 201,	//每日充值任选奖励5
		REWARD_TRIGGER_GIFT_1			= 202,	//触发型奖励1
		REWARD_TRIGGER_GIFT_2			= 203,	//触发型奖励2
		REWARD_TRIGGER_GIFT_3			= 204,	//触发型奖励3
		REWARD_SENIOR_TOWER_REWARD		= 205,	//神之试炼奖励
		REWARD_TSS_FIRST_RECHARGE_2		= 206,	//腾讯首充2
		REWARD_ANNIVERSARY_REWARD_1		= 207,	//周年奖励1
		REWARD_ANNIVERSARY_REWARD_2		= 208,	//周年奖励2
		REWARD_ANNIVERSARY_REWARD_3		= 209,	//周年奖励3
		REWARD_ANNIVERSARY_REWARD_4		= 210,	//周年奖励4
		REWARD_ANNIVERSARY_REWARD_5		= 211,	//周年奖励5
		REWARD_ANNIVERSARY_REWARD_6		= 212,	//周年奖励6
		REWARD_ANNIVERSARY_REWARD_7		= 213,	//周年奖励7
		REWARD_ANNIVERSARY_REWARD_8		= 214,	//周年奖励8
		REWARD_ANNIVERSARY_REWARD_9		= 215,	//周年奖励9
		REWARD_OPEN9                = 216,  //开服奖励9(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN10               = 217,  //开服奖励10(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN11               = 218,  //开服奖励11(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN12               = 219,  //开服奖励12(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN13               = 220,  //开服奖励13(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN14               = 221,  //开服奖励14(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN15               = 222,  //开服奖励15(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_OPEN16               = 223,  //开服奖励16(和REWARD_ONLINE类似，只是不会每日清空，终身只能领一次)
		REWARD_RECHARGE_LIMIT			= 224,	// 限时累计充值
		REWARD_LOGIN_LIMIT_1			= 225,	//限时登录1
		REWARD_LOGIN_LIMIT_2			= 226,	//限时登录2
		REWARD_LOGIN_LIMIT_3			= 227,	//限时登录3
		REWARD_LOGIN_LIMIT_4			= 228,	//限时登录4
		REWARD_ACHIEVEMENT_RECHARGE21 = 229,     //成就充值21
		REWARD_ACHIEVEMENT_RECHARGE22 = 230,     //成就充值22
		REWARD_ACHIEVEMENT_RECHARGE23 = 231,     //成就充值23
		REWARD_ACHIEVEMENT_RECHARGE24 = 232,     //成就充值24
		REWARD_ACHIEVEMENT_RECHARGE25 = 233,     //成就充值25
		REWARD_ACHIEVEMENT_RECHARGE26 = 234,     //成就充值26
		REWARD_ACHIEVEMENT_RECHARGE27 = 235,     //成就充值27
		REWARD_ACHIEVEMENT_RECHARGE28 = 236,     //成就充值28
		REWARD_ACHIEVEMENT_RECHARGE29 = 237,     //成就充值29
		REWARD_ACHIEVEMENT_RECHARGE30 = 238,    //成就充值30
		REWARD_ACHIEVEMENT_RECHARGE31 = 239,    //成就充值31
		REWARD_ACHIEVEMENT_RECHARGE32 = 240,    //成就充值32
		REWARD_ACHIEVEMENT_RECHARGE33 = 241,    //成就充值33
		REWARD_ACHIEVEMENT_RECHARGE34 = 242,    //成就充值34
		REWARD_ACHIEVEMENT_RECHARGE35 = 243,    //成就充值35
		REWARD_ACHIEVEMENT_RECHARGE36 = 244,    //成就充值36
		REWARD_ACHIEVEMENT_RECHARGE37 = 245,    //成就充值37
		REWARD_ACHIEVEMENT_RECHARGE38 = 246,    //成就充值38
		REWARD_ACHIEVEMENT_RECHARGE39 = 247,    //成就充值39
		REWARD_ACHIEVEMENT_RECHARGE40 = 248,    //成就充值40
		REWARD_OLDPLAYER_BACK_LOGIN_NEW = 249, //新版回归玩家首次登入奖励
		REWARD_OLDPLAYER_BACK_DAILY_RECHARGE_GIFT = 250, //新版玩家回归每日充值专属礼包
		REWARD_OLDPLAYER_BACK_DIAMOUND_PURCHASE_GIFT = 251, //新版老玩家回归每日砖石购买礼包
		REWARD_OLDPLAYER_BACK_TICKET_PURCHASE_GIFT = 252,//新版老玩家回归每日点券购买礼包
		REWARD_OLDPLAYER_BACK_DAILY_POINT_GIFT    = 253, //新邦老玩家回归每日积分活动

		REWARD_LOGIN_DAYS_LIMIT1 = 254, //累计登陆活动1
		REWARD_LOGIN_DAYS_LIMIT2 = 255, //累计登陆活动2
		REWARD_LOGIN_DAYS_LIMIT3 = 256, //累计登陆活动3
		REWARD_LOGIN_DAYS_LIMIT4 = 257, //累计登陆活动4
		REWARD_OLDPLAYER_BACK_POINT_GIFT    = 258, //新邦老玩家回归每日积分活动
		REWARD_FESTIVAL_1			= 259,	//节日活动1
		REWARD_FESTIVAL_2			= 260,	//节日活动2
		REWARD_LOGIN_DAYS_BUY		= 261,	// 购买累计登陆
		REWARD_VIP_PURCHASE_GIFTS = 262, //vip可购买礼包
		REWARD_TSS_LEVEL_FUND_6		= 263, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_7		= 264, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_8		= 265, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_9		= 266, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_10	= 267, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_11	= 268, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_12	= 269, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_13	= 270, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_14	= 271, //腾讯等级成长基金
		REWARD_TSS_LEVEL_FUND_15	= 272, //腾讯等级成长基金
		REWARD_SPECIAL_WEEK_CARD		= 273, //周卡特权
		REWARD_SPECIAL_WEEK_CARD_DAILY_CASH		= 274, //周卡每天领取钻石
		REWARD_SPECIAL_MONTH_CARD = 275, //月卡订阅特权

		REWARD_LOTTERY_TRIGGER_GIFT_REPU_1	= 276, //玩家彩票抽奖声望触发礼包1
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_2	= 277, //玩家彩票抽奖声望触发礼包2
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_3	= 278, //玩家彩票抽奖声望触发礼包3
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_4	= 279, //玩家彩票抽奖声望触发礼包4
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_5	= 280, //玩家彩票抽奖声望触发礼包5
		REWARD_LOTTERY_TRIGGER_GIFT_DICE_1	= 281, //玩家长时间未抽奖再次彩票抽奖时触发礼包
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_1	= 282, //玩家彩票抽奖获得物品触发礼包1
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_2	= 283, //玩家彩票抽奖获得物品触发礼包2
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_3	= 284, //玩家彩票抽奖获得物品触发礼包3
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_4	= 285, //玩家彩票抽奖获得物品触发礼包4
		REWARD_PC_LOGIN				= 286,	// pc登录奖励
		REWARD_MALL_PURCHASE_DAYS_TOTAL_1  = 287, // 商城购买累计登入礼包1
		REWARD_MALL_PURCHASE_DAYS_TOTAL_2  = 288, // 商城购买累计登入礼包2
		REWARD_MALL_PURCHASE_DAYS_TOTAL_3  = 289, // 商城购买累计登入礼包3
		REWARD_MALL_PURCHASE_DAYS_TOTAL_4  = 290, // 商城购买累计登入礼包4
		REWARD_MALL_PURCHASE_DAYS_TOTAL_5  = 291, // 商城购买累计登入礼包5
		REWARD_MALL_PURCHASE_DAYS_TOTAL_6  = 292, // 商城购买累计登入礼包6
		REWARD_MALL_PURCHASE_DAYS_1        = 293, // 商城购买登入礼包1
		REWARD_MALL_PURCHASE_DAYS_2        = 294, // 商城购买登入礼包2
		REWARD_MALL_PURCHASE_DAYS_3        = 295, // 商城购买登入礼包3
		REWARD_MALL_PURCHASE_DAYS_4        = 296, // 商城购买登入礼包4
		REWARD_MALL_PURCHASE_DAYS_5        = 297, // 商城购买登入礼包5
		REWARD_MALL_PURCHASE_DAYS_6        = 298, // 商城购买登入礼包6
		REWARD_MALL_TRIGGER_DAYS_TOTAL_1   = 299, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_2   = 300, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_3   = 301, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_4   = 302, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_5   = 303, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_6   = 304, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_1        = 305, // 商城充值服务登入礼包1
		REWARD_MALL_TRIGGER_DAYS_2        = 306, // 商城充值服务登入礼包2
		REWARD_MALL_TRIGGER_DAYS_3        = 307, // 商城充值服务登入礼包3
		REWARD_MALL_TRIGGER_DAYS_4        = 308, // 商城充值服务登入礼包4
		REWARD_MALL_TRIGGER_DAYS_5        = 309, // 商城充值服务登入礼包5
		REWARD_MALL_TRIGGER_DAYS_6        = 310, // 商城充值服务登入礼包6
		REWARD_RECHARGE_DAY_LIMIT_1       = 311, // 限时累计登入第一天
		REWARD_RECHARGE_DAY_LIMIT_2       = 312, // 限时累计登入第二天
		REWARD_RECHARGE_DAY_LIMIT_3       = 313, // 限时累计登入第三天
		REWARD_RECHARGE_DAY_LIMIT_4       = 314, // 限时累计登入第四天
		REWARD_RECHARGE_DAY_LIMIT_5       = 315, // 限时累计登入第五天
		REWARD_RECHARGE_DAY_LIMIT_6       = 316, // 限时累计登入第六天
		REWARD_RECHARGE_DAY_LIMIT_7       = 317, // 限时累计登入第七天
		REWARD_RECHARGE_DAY_LIMIT_8       = 318, // 限时累计登入第八天
		REWARD_RECHARGE_DAY_LIMIT_9       = 319, // 限时累计登入第九天
		REWARD_RECHARGE_DAY_LIMIT_10       = 320, // 限时累计登入第十天
		REWARD_RECHARGE_DAY_LIMIT_11       = 321, // 限时累计登入第十一天
		REWARD_RECHARGE_DAY_LIMIT_12       = 322, // 限时累计登入十二天
		REWARD_RECHARGE_DAY_LIMIT_13       = 323, // 限时累计登入第十三天
		REWARD_RECHARGE_DAY_LIMIT_14       = 324, // 限时累计登入第十四天
		REWARD_TEAM_RECHARGE_CHANGE				= 325, // 组队充值，充值信息变化
		REWARD_TEAM_RECHARGE_TEAM_CHANGE		= 326, // 组队充值，队伍充值信息变化
		REWARD_MALL_PURCHASE_DAYS_TOTAL_7  = 327, // 商城购买累计登入礼包7
		REWARD_MALL_PURCHASE_DAYS_TOTAL_8  = 328, // 商城购买累计登入礼包8
		REWARD_MALL_PURCHASE_DAYS_TOTAL_9  = 329, // 商城购买累计登入礼包9
		REWARD_MALL_PURCHASE_DAYS_7        = 330, // 商城购买登入礼包7
		REWARD_MALL_PURCHASE_DAYS_8        = 331, // 商城购买登入礼包8
		REWARD_MALL_PURCHASE_DAYS_9        = 332, // 商城购买登入礼包9
		REWARD_RECHARGE_CONSUME				= 333, // 充值消费
		REWARD_PERSONAL_TARGET				= 334, // 个人目标
		REWARD_OPTIONAL_QUARTER_CARD_FREE1		= 335, //自选免费季卡1
		REWARD_OPTIONAL_QUARTER_CARD_COST1_1	= 336, //自选收费季卡1_1
		REWARD_OPTIONAL_QUARTER_CARD_COST1_2	= 337, //自选收费季卡1_2
		REWARD_OPTIONAL_QUARTER_CARD_COST1_3	= 338, //自选收费季卡1_3
		REWARD_OPTIONAL_QUARTER_CARD_COST1_4	= 339, //自选收费季卡1_4
		REWARD_OPTIONAL_QUARTER_CARD_COST1_5	= 340, //自选收费季卡1_5
		REWARD_OPTIONAL_QUARTER_CARD_COST1_6	= 341, //自选收费季卡1_6
		REWARD_OPTIONAL_QUARTER_CARD_COST1_7	= 342, //自选收费季卡1_7
		REWARD_OPTIONAL_QUARTER_CARD_COST1_8	= 343, //自选收费季卡1_8
		REWARD_OPTIONAL_QUARTER_CARD_FREE2		= 344, //自选免费季卡2
		REWARD_OPTIONAL_QUARTER_CARD_COST2_1	= 345, //自选收费季卡2_1
		REWARD_OPTIONAL_QUARTER_CARD_COST2_2	= 346, //自选收费季卡2_2
		REWARD_OPTIONAL_QUARTER_CARD_COST2_3	= 347, //自选收费季卡2_3
		REWARD_OPTIONAL_QUARTER_CARD_COST2_4	= 348, //自选收费季卡2_4
		REWARD_OPTIONAL_QUARTER_CARD_COST2_5	= 349, //自选收费季卡2_5
		REWARD_OPTIONAL_QUARTER_CARD_COST2_6	= 350, //自选收费季卡2_6
		REWARD_OPTIONAL_QUARTER_CARD_COST2_7	= 351, //自选收费季卡2_7
		REWARD_OPTIONAL_QUARTER_CARD_COST2_8	= 352, //自选收费季卡2_8

		REWARD_DAILY_ACHIEVEMENT_TRIGGER_REWRAD = 353, //每日成就触发礼包
		REWARD_FOREVER_ACHIEVEMENT_TRIGGER_REWRAD = 354,//永久成就触发礼包

		REWARD_BARGAIN_GIFT_1				= 355, // 砍价礼包1
		REWARD_BARGAIN_GIFT_2				= 356, // 砍价礼包2
		REWARD_BARGAIN_GIFT_3				= 357, // 砍价礼包3
		REWARD_BARGAIN_GIFT_4				= 358, // 砍价礼包4
		REWARD_BARGAIN_GIFT_5				= 359, // 砍价礼包5
		REWARD_BARGAIN_GIFT_6				= 360, // 砍价礼包6
		REWARD_BARGAIN_GIFT_7				= 361, // 砍价礼包7
		REWARD_BARGAIN_GIFT_8				= 362, // 砍价礼包8
		REWARD_BARGAIN_GIFT_9				= 363, // 砍价礼包9
		REWARD_BARGAIN_GIFT_10				= 364, // 砍价礼包10
		REWARD_BARGAIN_GIFT_11				= 365, // 砍价礼包11
		REWARD_BARGAIN_GIFT_12				= 366, // 砍价礼包12
		REWARD_BARGAIN_GIFT_13				= 367, // 砍价礼包13
		REWARD_BARGAIN_GIFT_14				= 368, // 砍价礼包14
		REWARD_BARGAIN_GIFT_15				= 369, // 砍价礼包15
		REWARD_BARGAIN_GIFT_16				= 370, // 砍价礼包16

		// 限时特典扩档：点券
		REWARD_MALL_PURCHASE_DAYS_TOTAL_10  = 371, // 商城购买累计登入礼包10
		REWARD_MALL_PURCHASE_DAYS_TOTAL_11  = 372, // 商城购买累计登入礼包11
		REWARD_MALL_PURCHASE_DAYS_TOTAL_12  = 373, // 商城购买累计登入礼包12
		REWARD_MALL_PURCHASE_DAYS_TOTAL_13  = 374, // 商城购买累计登入礼包13
		REWARD_MALL_PURCHASE_DAYS_TOTAL_14  = 375, // 商城购买累计登入礼包14
		REWARD_MALL_PURCHASE_DAYS_TOTAL_15  = 376, // 商城购买累计登入礼包15
		REWARD_MALL_PURCHASE_DAYS_10        = 377, // 商城购买登入礼包10
		REWARD_MALL_PURCHASE_DAYS_11        = 378, // 商城购买登入礼包11
		REWARD_MALL_PURCHASE_DAYS_12        = 379, // 商城购买登入礼包12
		REWARD_MALL_PURCHASE_DAYS_13        = 380, // 商城购买登入礼包13
		REWARD_MALL_PURCHASE_DAYS_14        = 381, // 商城购买登入礼包14
		REWARD_MALL_PURCHASE_DAYS_15        = 382, // 商城购买登入礼包15

		// 限时特典扩档：直购
		REWARD_MALL_TRIGGER_DAYS_TOTAL_7    = 383, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_8    = 384, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_9    = 385, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_10   = 386, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_11   = 387, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_12   = 388, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_13   = 389, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_14   = 390, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_TOTAL_15   = 391, // 商城充值服务累计登入礼包
		REWARD_MALL_TRIGGER_DAYS_7          = 392, // 商城充值服务登入礼包7
		REWARD_MALL_TRIGGER_DAYS_8          = 393, // 商城充值服务登入礼包8
		REWARD_MALL_TRIGGER_DAYS_9          = 394, // 商城充值服务登入礼包9
		REWARD_MALL_TRIGGER_DAYS_10         = 395, // 商城充值服务登入礼包10
		REWARD_MALL_TRIGGER_DAYS_11         = 396, // 商城充值服务登入礼包11
		REWARD_MALL_TRIGGER_DAYS_12         = 397, // 商城充值服务登入礼包12
		REWARD_MALL_TRIGGER_DAYS_13         = 398, // 商城充值服务登入礼包13
		REWARD_MALL_TRIGGER_DAYS_14         = 399, // 商城充值服务登入礼包14
		REWARD_MALL_TRIGGER_DAYS_15         = 400, // 商城充值服务登入礼包15

		// 开服特典：点券
		REWARD_OPEN_TICKET_DAYS_TOTAL_1     = 401, // 累计登录礼包
		REWARD_OPEN_TICKET_DAYS_TOTAL_2     = 402,
		REWARD_OPEN_TICKET_DAYS_TOTAL_3     = 403,
		REWARD_OPEN_TICKET_DAYS_TOTAL_4     = 404,
		REWARD_OPEN_TICKET_DAYS_TOTAL_5     = 405,
		REWARD_OPEN_TICKET_DAYS_TOTAL_6     = 406,
		REWARD_OPEN_TICKET_DAYS_TOTAL_7     = 407,
		REWARD_OPEN_TICKET_DAYS_TOTAL_8     = 408,
		REWARD_OPEN_TICKET_DAYS_TOTAL_9     = 409,
		REWARD_OPEN_TICKET_DAYS_1           = 410, // 登录礼包
		REWARD_OPEN_TICKET_DAYS_2           = 411,
		REWARD_OPEN_TICKET_DAYS_3           = 412,
		REWARD_OPEN_TICKET_DAYS_4           = 413,
		REWARD_OPEN_TICKET_DAYS_5           = 414,
		REWARD_OPEN_TICKET_DAYS_6           = 415,
		REWARD_OPEN_TICKET_DAYS_7           = 416,
		REWARD_OPEN_TICKET_DAYS_8           = 417,
		REWARD_OPEN_TICKET_DAYS_9           = 418,

		// 开服特典：直购
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_1    = 419, // 累计登录礼包
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_2    = 420,
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_3    = 421,
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_4    = 422,
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_5    = 423,
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_6    = 424,
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_7    = 425,
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_8    = 426,
		REWARD_OPEN_DEPOSIT_DAYS_TOTAL_9    = 427,
		REWARD_OPEN_DEPOSIT_DAYS_1          = 428, // 登录礼包
		REWARD_OPEN_DEPOSIT_DAYS_2          = 429,
		REWARD_OPEN_DEPOSIT_DAYS_3          = 430,
		REWARD_OPEN_DEPOSIT_DAYS_4          = 431,
		REWARD_OPEN_DEPOSIT_DAYS_5          = 432,
		REWARD_OPEN_DEPOSIT_DAYS_6          = 433,
		REWARD_OPEN_DEPOSIT_DAYS_7          = 434,
		REWARD_OPEN_DEPOSIT_DAYS_8          = 435,
		REWARD_OPEN_DEPOSIT_DAYS_9          = 436,

		REWARD_LOTTERY_TRIGGER_GIFT_REPU_6	= 437, //玩家彩票抽奖声望触发礼包6
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_7	= 438, //玩家彩票抽奖声望触发礼包7
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_8	= 439, //玩家彩票抽奖声望触发礼包8
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_9	= 440, //玩家彩票抽奖声望触发礼包9
		REWARD_LOTTERY_TRIGGER_GIFT_REPU_10	= 441, //玩家彩票抽奖声望触发礼包10
		REWARD_LOTTERY_TRIGGER_GIFT_DICE_2	= 442, //玩家长时间未抽奖再次彩票抽奖时触发礼包2
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_5	= 443, //玩家彩票抽奖获得物品触发礼包5
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_6	= 444, //玩家彩票抽奖获得物品触发礼包6
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_7	= 445, //玩家彩票抽奖获得物品触发礼包7
		REWARD_LOTTERY_TRIGGER_GIFT_ITEM_8	= 446, //玩家彩票抽奖获得物品触发礼包8

		// 战力基金
		REWARD_COMBAT_POWER_1				= 447,
		REWARD_COMBAT_POWER_2				= 448,
		REWARD_COMBAT_POWER_3				= 449,
		REWARD_COMBAT_POWER_4				= 450,
		REWARD_COMBAT_POWER_5				= 451,
		REWARD_COMBAT_POWER_6				= 452,
		REWARD_COMBAT_POWER_7				= 453,
		REWARD_COMBAT_POWER_8				= 454,
		REWARD_COMBAT_POWER_9				= 455,
		REWARD_COMBAT_POWER_10				= 456,

		REWARD_DAY_RECHARGE_ACTIVITY		= 457,

		// 累计消费
		REWARD_CUMULATIVE                   = 458,

		// 新成就点数
		REWARD_NEW_ACHIEVEMENT_POINT        = 459,

		REWARD_BARGAIN_GIFT_17				= 460, // 砍价礼包17
		REWARD_BARGAIN_GIFT_18				= 461, // 砍价礼包18
		REWARD_BARGAIN_GIFT_19				= 462, // 砍价礼包19
		REWARD_BARGAIN_GIFT_20				= 463, // 砍价礼包20
		REWARD_BARGAIN_GIFT_21				= 464, // 砍价礼包21
		REWARD_BARGAIN_GIFT_22				= 465, // 砍价礼包22
		REWARD_BARGAIN_GIFT_23				= 466, // 砍价礼包23
		REWARD_BARGAIN_GIFT_24				= 467, // 砍价礼包24

		REWARD_DAY_RECHARGE_ACTIVITY1       = 468, // 和457完全一样，只不过俩有可能同时存在
		REWARD_REPU_ACHIEVE_REWARD          = 469, // 声望达到触发领奖

		// 日服回流奖励
		REWARD_JP_COMEBACK_REWARD           = 470,
		// 日服回流签到奖励
		REWARD_JP_COMEBACK_SIGN_REWARD      = 471,

		REWARD_ANNIVERSARY_REWARD_10		= 472,	//周年奖励10
		REWARD_ANNIVERSARY_REWARD_11		= 473,	//周年奖励11
		REWARD_ANNIVERSARY_REWARD_12		= 474,	//周年奖励12
		REWARD_ANNIVERSARY_REWARD_13		= 475,	//周年奖励13
		REWARD_ANNIVERSARY_REWARD_14		= 476,	//周年奖励14
		REWARD_ANNIVERSARY_REWARD_15		= 477,	//周年奖励15
		REWARD_ANNIVERSARY_REWARD_16		= 478,	//周年奖励16
		REWARD_ANNIVERSARY_REWARD_17		= 479,	//周年奖励17
		REWARD_ANNIVERSARY_REWARD_18		= 480,	//周年奖励18
		REWARD_ANNIVERSARY_REWARD_19		= 481,	//周年奖励19

		REWARD_BARGAIN_GIFT_25				= 482, // 砍价礼包25
		REWARD_BARGAIN_GIFT_26				= 483, // 砍价礼包26
		REWARD_BARGAIN_GIFT_27				= 484, // 砍价礼包27
		REWARD_BARGAIN_GIFT_28				= 485, // 砍价礼包28
		REWARD_BARGAIN_GIFT_29				= 486, // 砍价礼包29
		REWARD_BARGAIN_GIFT_30				= 487, // 砍价礼包30
		REWARD_BARGAIN_GIFT_31				= 488, // 砍价礼包31
		REWARD_BARGAIN_GIFT_32				= 489, // 砍价礼包32

		REWARD_REPU_ACHIEVE_REWARD2         = 490, // 声望达到触发领奖
		REWARD_TURNTABLE_RECHARGE_LIMIT		= 491, // 转盘限时累计充值
		REWARD_TURNTABLE_DAY_RECHARGE_LIMIT	= 492, // 转盘每日充值

		REWARD_CASKET_RECHARGE_LIMIT		= 493, // 缘金绮匣限时累计充值
		REWARD_CASKET_DAY_RECHARGE_LIMIT	= 494, // 缘金绮匣每日充值

		REWARD_DAILY_CONTINUE_RECHARGE		= 495,	// 每日持续充值
		REWARD_MAGIC_GATHERING_BUY_SERVICE	= 496, // 魔力汇聚
		REWARD_MAGIC_GATHERING_GLOBAL_PROGRESS_AWARD	= 497, // 魔力汇聚
		REWARD_NEW_USER_ICE_BREAK_RECHARGE_LIMIT = 498, // 新用户付费破冰累计充值

		REWARD_DAY_RECHARGE_ACTIVITY1_1		= 499, // 和468完全一样，只不过俩有可能同时存在
		REWARD_REPU_ACHIEVE_REWARD_1		= 500, // 声望达到触发领奖，和469一毛一样

		REWARD_TSS_NEW_LEVEL_FUND_1		= 501, //新腾讯等级成长基金
		REWARD_TSS_NEW_LEVEL_FUND_2		= 502, //新腾讯等级成长基金
		REWARD_TSS_NEW_LEVEL_FUND_3		= 503, //新腾讯等级成长基金
		REWARD_TSS_NEW_LEVEL_FUND_4		= 504, //新腾讯等级成长基金
		REWARD_TSS_NEW_LEVEL_FUND_5		= 505, //新腾讯等级成长基金
		REWARD_TSS_NEW_LEVEL_FUND_6		= 506, //新腾讯等级成长基金
		REWARD_TSS_NEW_LEVEL_FUND_7		= 507, //新腾讯等级成长基金

		REWARD_NEW_TSS_FIRST_RECHARGE	= 508,	//腾讯首充服务累计登录礼包
		REWARD_NEW_TSS_FIRST_RECHARGE_2	= 509,	//腾讯首充服务累计登录礼包
		REWARD_NEW_TSS_FIRST_RECHARGE_3	= 510,	//腾讯首充服务累计登录礼包

		REWARD_NEW_MONTH_CARD				= 511,	//新月卡
		REWARD_NEW_MONTH_CARD_EXTRA         = 512,  //新月卡附加奖励

		REWARD_COMEBACK_NEW_TRAVEL_TIME		= 513,  //旅故新时通行证
		REWARD_NEW_BP_CARD_FREE 			= 514, //特训协议（新bp）免费
		REWARD_NEW_BP_CARD_COST 			= 515, //特训协议（新bp）充值

		REWARD_TRIGGER_GIFT_ACHIEVEMENT_1   = 516,  //成就触发奖励1
		REWARD_TRIGGER_GIFT_ACHIEVEMENT_2   = 517,  //成就触发奖励2
		REWARD_TRIGGER_GIFT_ACHIEVEMENT_3   = 518,  //成就触发奖励3
		REWARD_NEW_LEVEL_TRIGGER_GIFT = 519,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_2 = 520,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_3 = 521,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_4 = 522,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_5 = 523,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_6 = 524,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_7 = 525,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_8 = 526,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_9 = 527,  // 等级触发礼包
		REWARD_NEW_LEVEL_TRIGGER_GIFT_10 = 528,  // 等级触发礼包

		REWARD_COUNT,
	};

	static auto_reward_base *_instance_list[REWARD_COUNT];
};


class player_auto_reward
{
	abase::vector<auto_reward_base::entry_t> _list;
	std::unordered_map<int/*activity_tid*/, bool/*activity_open*/> _activity_state;

public:
	auto_reward_base::entry_t   *GetEntry(int type)
	{
		for (size_t i = 0; i < _list.size(); i ++)
		{
			if (_list[i].type == type)
			{
				return &_list[i];
			}
		}
		return NULL;
	}

	void Init();
	void Heartbeat(gplayer_imp *pImp);

	player_auto_reward();
	void GetReward(gplayer_imp *pImp, int type);
	void SetReward(gplayer_imp *pImp, int type, int reward);
	void SyncRewardData(gplayer_imp *pImp, int type);
	void RemoveReward(gplayer_imp *pImp, int type);
	void OnlineRewardTimer(gplayer_imp *pImp);
	void OpenRewardTimer(gplayer_imp *pImp);
	void CombatPowerRewardTimer(gplayer_imp *pImp);
	void InsertActivityObserver(int activity_id);
	void OnActivityStateChange(gplayer_imp *pImp, int activity_id);
	//void MonthRewardTimer(gplayer_imp * pImp);
	void OnLevelUp(gplayer_imp *pImp);
	int PlayerOperation(gplayer_imp *pImp, int type, int op, uint64_t value, int cost_value = 0);
	void OnPlayerModifyReputation(gplayer_imp *pImp, int repu_id);
	//void OnMidasChanged(gplayer_imp* pImp);
	void OnJoinNationWar(gplayer_imp *pImp, int is_free_war, int war_id);
	void OnUpdateGrcFriendCount(gplayer_imp *pImp, int count);
	void OnGetPlatVIP(gplayer_imp *pImp);
	virtual void OnGetRechargeMsg(gplayer_imp *pImp, int vipkindMsk);
	void DailyUpdate(gplayer_imp *imp);
	void DailyUpdate6(gplayer_imp *imp);
	void OnLogin(gplayer_imp *pImp, bool islogin);
	void Save(gplayer_imp *pImp, archive& ar);
	void Load(gplayer_imp *pImp, archive& ar);
	void OnCashUpdate(gplayer_imp *pImp);	//充值的时候会调用这里
	void OnDayRechargeUpdate(gplayer_imp *pImp);	//每日充值变化会调用这个
	void OnRechargeUpdate(gplayer_imp *pImp);	//总充值变化会调用这个
	void OnConsume(gplayer_imp *pImp, int count, const FuncInfo& fi);	//消费
	void OnFundActivedUpdate(gplayer_imp *pImp);	//每日基金变化
	void OnMonthCardUpdate(gplayer_imp *pImp);	//月卡时间变化
	void OnTimeLimitUpdate(gplayer_imp *pImp);	//限时活动变化
	void OnCashInfo(gplayer_imp *pImp);	//初始化元宝信息
	void OnFirstRechargeAwardUpdate(gplayer_imp *pImp);	//首冲奖励
	void OnFirstRechargeDoubleUpdate(gplayer_imp *pImp);	//首冲双倍
	void OnVIPLevelChanged(gplayer_imp *pImp);	//VIP等级变化
	void OnSecondRechargeUpdate(gplayer_imp *pImp);	//次充奖励
	void OnAchievementRechargeUpdate(gplayer_imp *pImp);	//成就充值
	void OnSetOldPlayerBackState(gplayer_imp *pImp);
	void OnSetName(gplayer_imp *pImp);
	int  GetPlatVIPTypeMask(gplayer_imp *pimp);
	int  GetPriorPlatVIPType(gplayer_imp *pimp, int *expiry_ts = nullptr);
	int  GetPlatStartupVIPType(gplayer_imp *pimp, int *expiry_ts = nullptr);
	int  GetPlatMemberVIPType(gplayer_imp *pimp, int *expiry_ts = nullptr);
	void OnTssFirstRechargeUpdate(gplayer_imp *pImp);	//腾讯首充
	void OnTssGiftsUpdate(gplayer_imp *pImp);	//腾讯礼包
	void OnTssLevelFundUpdate(gplayer_imp *pImp);	//腾讯成长基金
	void OnTssQuarterCardUpdate(gplayer_imp *pImp);	//腾讯季卡
	void OnFriendHelpCountUpdate(gplayer_imp *pImp);	//好友助力变化
	void OnAchievementComplete(gplayer_imp *pImp, int achievement_id);	//完成成就
	void OnTriggerGift(gplayer_imp *pImp);	//触发奖励
	void OnTssTriggerGiftUpdate(gplayer_imp *pImp);	//购买触发奖励服务
	void OnDsSyncReputationTrrige(gplayer_imp *pImp); // 同步回归玩家时使用
	void OnSpecialCardUpdate(gplayer_imp *pImp); // 触发特殊周卡权益
	void OnLotteryTriggerGift(gplayer_imp *pImp, int dice, int lottery_count, const std::set<int>& item_ids, int succ_count);
	void OnTeamRechargeChange(gplayer_imp *pImp, PB::ipt_team_recharge_op& cmd);
	void OnTeamRechargeOp(gplayer_imp *pImp, PB::ipt_team_recharge_op& cmd);
	int64_t GetTeamRechargeTotalCount(gplayer_imp *pImp, int& cur_draw_count);
	void AddTeamRechargeLotteryCount(gplayer_imp *pImp, int count);
	void OnCombatPowerChange(gplayer_imp *pImp); // 战力变化
	int64_t GetTurntableSilverCount(gplayer_imp *pImp);
	void AddTurntableSilverUseCount(gplayer_imp *pImp, int count);

	int64_t GetCasketOuterLeftTimes(gplayer_imp *pImp);
	void AddCasketOuterUseCount(gplayer_imp *pImp, int count);
	int64_t GetCasketInnerLeftTimes(gplayer_imp *pImp);
	void AddCasketInnerGetCount(gplayer_imp *pImp, int count);
	void AddCasketInnerUseCount(gplayer_imp *pImp, int count);
	int64_t GetCasketKeyLeftTimes(gplayer_imp *pImp);
	int64_t GetCasketKeyUseTimes(gplayer_imp *pImp);
	void AddCasketKeyGetTimes(gplayer_imp *pImp, int count);
	void AddCasketKeyUseCount(gplayer_imp *pImp, int count);
	void AddCasketGoodyShare(gplayer_imp *pImp, int count);


	//bool InitMonthReward(gplayer_imp * pImp);

	void Clear();
public:
	static bool  LoadAutoReward(const char *file);
};

struct direct_lottery_base
{
	int index;		//这个索引不重复
	int type;		//不同类型的type可能重复 ，比如1次和10次抽取的type会相同， 还有些类型的type不重要
	int is_free;		//是否免费抽奖
	int free_limit;		//通用限制模板
	int free_cooldown;	//免费冷却时间
	int cooldown_idx;	//冷却索引，这个不能随便改了，因为要存盘的 存盘的索引在700~799之间
	int init_free_cooldown;	//初始冷却时间（第一次进入游戏）
	int pay_money;		//付费时的价格
	int pay_cash;		//付费时的元宝，两个值都为0的话不能付费购买 注意钱数为N次的总钱数
	int lottery_id;		//彩票模板id，为0的时候走寻访逻辑
	int repeat_count;	//重复次数，10次与1次的区分在这里
	int start_in_day;	//一天限时起始段 <0 不限制
	int end_in_day;		//一天限时终结段 <0 不限制
};

//玩家直接抽奖逻辑
class player_direct_lottery
{
	static std::map< int, direct_lottery_base > _template;
	enum
	{
		MAX_SEEK_DATA_COUNT = 16
	};
private:
	std::vector<int> _seek_data;	//存盘

	bool Execute(gplayer_imp *pImp, const direct_lottery_base *pEnt);

	bool DoLottery(gplayer_imp *pImp, const direct_lottery_base *pEnt);
	bool DoSeek(gplayer_imp *pImp, const direct_lottery_base *pEnt);

	void FirstInit(gplayer_imp *pImp);
public:

	bool GetLottery(gplayer_imp *pImp, unsigned int index);

	static bool LoadDirectLottery(const char *file);

	void Load(gplayer_imp *pImp, PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
};

class player_exp_lottery
{
	//屏蔽客栈打听功能
	/*
	gplayer_imp* _imp;
	int exp_lottery_factor;
	int can_free;
	int got_exp;
	*/
public:
	//计算玩家的经验等级
	int GetLottery(gplayer_imp *pImp, bool is_free = true, int last_factor = 0);
	//获取经验
	void GetLotteryExp(gplayer_imp *pImp, bool is_forced);

	void OnEnterScene(gplayer_imp *pImp);
	void OnNewDay(gplayer_imp *pImp);
	void FirstInit(gplayer_imp *pImp);
	void Load(gplayer_imp *pImp, PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
};

#endif

