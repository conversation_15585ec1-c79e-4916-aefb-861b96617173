#ifndef __GS_TEMPLATE_LOADER_H__
#define __GS_TEMPLATE_LOADER_H__

#include <hashmap.h>

#include "property.h"
#include "player_template.h"
#include "activity_manager.h"
#include "script_wrapper.h"
#include "mall_catalog.h"
#include <unordered_set>

//gs层模板数据
//本身都是表结构,按理说template里的tid项是多余的,可能是考虑有时候保存template指针,debug的时候有用?//钓鱼挑战赛使用了tid

struct interact_task;
struct mine_template 
{
	tid_t tid;

	int file_minimap_icon;
	EXP_MINE_GATHER_TYPE gather_type;
	unsigned int faction;
	bool is_transmitbox;		//如果这个值为真，表示这是一个trasnmitbox
	bool is_buffbox;		//如果这个值为真，表示这是一个buffbox
	bool is_marriage_item;		//如果这个值为真，表示这是一个marriage lock（同心锁）
	bool no_interrupted;		//不会被攻击中断
	bool no_disappear;		//采集后不消失 应仅限于任务矿
	bool is_center_battle_mine;//是否是跨服战中的矿
	bool is_secondary;		//是否是交互矿
	bool is_scrawl_pond;	//是否是涂鸦矿
	bool is_fish_pond;		//是否是钓鱼矿
	bool is_thunderstrike;	//是否是天谴计划矿
	int max_gather_count;		//采集多少次消失
	int cooldown_class;			//cd种类
	float cooldown_time;		//cd
	bool is_continuous;		//是否使用持续采矿
	bool is_pot;			//是否罐子
	bool eliminate_tool;		//采集后删除装备
	int loot_table;			//loot表标记产生什么物品
	int time_min;			//采集时间下限
	int time_max;			//采集时间上限
	int need_tool;			//需要工具的ID
	int req_level;			//需求玩家等级
	int req_skill;			//需求玩家技能
	int req_skill_level;		//需求玩家技能级别
	int task_limit;			//采集需要能接取的任务ID
	int task_req;			//采集需要的任务ID
	int task_relate;		//采集激活的任务ID，(不是发放此任务，而是能采出此任务需要物品之类)
	int task_deliver;		//采集发放的任务ID
	unsigned int ask_help_faction;	//开始采集瞬间，是否要求周围怪物产生仇恨，若产生，产生仇恨的范围
	float ask_help_range;		//若产生仇恨，影响的范围
	int   ask_help_rage;		//若产生仇恨，产生的仇恨的数值
	struct 				//采集成功后产生的怪物
	{
		int id;
		int num;
		float radius;
		int remain_time;
		int aggro;
	} monster;			//挖完矿后召唤出来的怪物

	int open_ctrls[4];
	int close_ctrls[4];
	int start_skill;		//起始开挖技能
	int end_skill;			//终结挖技能

	struct
	{
		unsigned int faction;
		unsigned int open_ctrls[4];
		unsigned int close_ctrls[4];
	}mine_rlt[4];

	struct 
	{
		int shape;	//1球 2柱 3 待补充
		float radius;	//半径
		float bottom_height;	//下高度
		float top_height;		//上高度
		float long_dis_sight;	//远距离对象可见范围
		int req_level;	//需要玩家等级
		int trans_condition;
		int id_instance;
		int scene_tag;	//目标场景
		A3DVECTOR3 pos;	//目标坐标
	} transmit_info;

	int id_instance;
	bool is_show_process_bar;
	bool can_multi_open;
	size_t max_open_num;
	VIGOR_TYPE consume_point_type;
	int consume_point_per_gather;
	int exp; //每次采集所获的经验
	int skillpoint; //每次采集所获的熟练度
	int lifeprof_extra_exp; //差事获得额外经验

	int check_repu_id;
	int check_repu_value;
	int consume_repu_id;
	int consume_repu_value;
	int add_repu_id;
	int add_repu_value;

	int max_life_time;
	float max_use_dist;
	int max_use_count;
	int min_limit_level;
	int max_limit_level;
	PROFTYPE_MASK prof_limit_mask;
	bool can_use_in_fight;
	int skill_type;
	int skill_valid_dist;	//技能生效距离
	int target_type;	//1(触发者),2(触发者小队),3(触发者附近)
	int max_target_count;
	int fx_speed;	//光效飞行速度(m/s)

	struct skill_t
	{
		int id;                 //      ID
		int level;              //      等级
		float odds;             //      使用几率

	} skills[EXP_BUFFBOX_SKILL_NUM];        // 技能

	int mine_corpse_disappear_time;	//矿物实体消失时间
	
	struct  
	{
		int scene_param;	// 影响场景参数
		AFFECT_SCENE_PARAM_TYPE op;	// 影响场景参数方式0(加)、1(减)、2(乘)、3(除)、4(指定值)
		int val;					// 值
	}mined_affect[4]; // 采集完成后
	bool has_mined_affect;

	int buff_id_gather; // 前提buff

	int req_mingxing[EXP_MARRAIGE_ITEM_FAMOUSE_NUM]; //需要明星

	PROFTYPE_MASK character_combo_id;		//职业需求
	bool is_team_share_loot; // 奖励是否可以分享给队友
	int  cmn_uselimit_id; //活动期间可以开启次数
	int interact_template_id;	//交互模板ID
	WEATHER_MASK weather_limit;		//天气限制
	float temperature_min; // 温度限制最小值
	float temperature_max; // 温度限制最大值
	HUMIDITY_MASK humidity_limit; // 湿度限制
	int need_career_id;			//采集需要激活的身份ID
	int career_ability_id;		//获得熟练度的身份能力ID
	bool gather_keep_invisible;//采集者保持隐身
	bool is_pubg;//吃鸡战场矿
	int broadcast_radius; // 广播半径

public:
	bool GetIsInteract() const;
public:
	void LoadFromMineEssence(const MINE_ESSENCE& ess);
	void LoadFromTransmitBoxEssence(const TRANSMITBOX_ESSENCE& ess);
	void LoadFromBuffBoxEssence(const BUFFBOX_ESSENCE& ess);
	void LoadFromInteractEssence(const SCENEINTERACTION_CONFIG& ess);

};

class mine_template_manager
{
	typedef abase::hash_map<tid_t,mine_template> MAP;
	MAP _map;

	int _marriage_item_tid;

	bool Insert(const mine_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	mine_template_manager();

	bool Load(elementdataman& data_man);

	const mine_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	mine_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	const mine_template* GetMarriageItem() const
	{
		if (_marriage_item_tid > 0)
		{
			return Get(_marriage_item_tid);
		}
		return NULL;
	}

	static mine_template_manager& GetInstance()
	{
		static mine_template_manager _s_instance;
		return _s_instance;
	}
};

class rune_word_manager
{
public:
	struct KEY
	{
		unsigned int stones[EXP_RUNEWORD_STONE_COUNT];
		bool operator==(const KEY & rhs) const
		{
			return memcmp(stones, rhs.stones, sizeof(stones)) == 0;
		}
	};

	struct VALUE_NODE
	{
		VALUE_NODE * next;
		int rune_word_id;
		KEY original_key;
		int in_order;
		int addon_group_id;
		VALUE_NODE():next(0),rune_word_id(0),in_order(0),addon_group_id(0)
		{
			memset(&original_key,0,sizeof(original_key));
		}
		~VALUE_NODE()
		{
			if(next) delete next;
		}
	};
private:
	struct KeyHash
	{
		inline unsigned long operator()(const KEY& key) const
		{
			size_t k = 0;
			for(size_t i = 0;i < EXP_RUNEWORD_STONE_COUNT;++i)
			{
				k = k * 5 + key.stones[i];
			}
			return k;
		}
	};

	typedef abase::hash_map<KEY,VALUE_NODE,KeyHash> MAP;
	MAP _map;
public:
	bool Load(elementdataman& data_man);
	bool Add(int rune_word_id, const unsigned int * stones, int stone_count, int in_order, int addon_group)
	{
		if(stone_count <= 0 || stone_count > EXP_RUNEWORD_STONE_COUNT) return false;
		if(rune_word_id <= 0 || addon_group <= 0) return false;
		KEY key;
		memset(&key,0,sizeof(key));
		for(int i = 0; i < stone_count; i ++)
		{
			if(stones[i] <=0) return false;
			key.stones[i] = stones[i];
		}
		KEY old_key = key;
		std::sort(key.stones, &key.stones[stone_count]);
	
		VALUE_NODE &node = _map[key];
		if(node.addon_group_id == 0)
		{
			node.rune_word_id = rune_word_id;
			node.in_order = in_order;
			node.addon_group_id = addon_group;
			node.original_key = old_key;
		}
		else
		{
			VALUE_NODE * pNode = new VALUE_NODE;
			pNode->rune_word_id = rune_word_id;
			pNode->in_order = in_order;
			pNode->addon_group_id = addon_group;
			pNode->original_key = old_key;

			pNode->next = node.next;
			node.next = pNode;
		}
		return true;
	}

	bool FindRuneWord(const int * value, size_t size, int& rune_word_id, int& addon_group_id)
	{
		if(size == 0 || size >= EXP_RUNEWORD_STONE_COUNT) return 0;
		KEY key;
		memset(&key, 0 , sizeof(key));
		memcpy(&key, value , size*sizeof(int));
		std::sort(key.stones, &key.stones[size]);
		MAP::iterator it = _map.find(key);
		if(it == _map.end()) return 0;
		const VALUE_NODE *pNode = &(it->second);
		do
		{
			if(pNode->in_order == 0)		//不要求顺序 ，肯定满足条件返回即可
			{
				rune_word_id = pNode->rune_word_id;
				addon_group_id = pNode->addon_group_id;
				return true;
			}
			else if(key == pNode->original_key)	//要求顺序则比较原始Key
			{
				rune_word_id = pNode->rune_word_id;
				addon_group_id = pNode->addon_group_id;
				return true;
			}
			pNode = pNode->next;
		}while(pNode);
		return false;
	}

	static rune_word_manager& GetInstance()
	{
		static rune_word_manager _s_instance;
		return _s_instance;
	}
};

struct mail_template
{
	tid_t tid;		//模板ID
	EXP_MAIL_TYPE type;	//邮件类型
	size_t money;		//交易币
	                              //TODO: 是否支持绑定币？
	tid_t item_tid;		//物品
	unsigned int item_gencfg_id; //物品生成处理方式

public:
	void LoadFromEssence(const MAIL_ESSENCE& ess);
};

class mail_template_manager
{
	typedef abase::hash_map<int,mail_template> MAP;
	MAP _map;

	bool Insert(const mail_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const mail_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	mail_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static mail_template_manager& GetInstance()
	{
		static mail_template_manager _s_instance;
		return _s_instance;
	}
};

//技能组
struct skillset_template
{
	tid_t tid;
	int skill_ids[EXP_SKILLSEQ_SKILL_COUNT];
	int skill_lvl[EXP_SKILLSEQ_SKILL_COUNT];
	bool need_finish;		//是否在没有目标时也继续执行完操作

public:
	bool LoadFromEssence(const SKILLSEQ_ESSENCE& ess);
};

class skillset_template_manager
{
	typedef abase::hash_map<tid_t,skillset_template> MAP;
	MAP _map;

	bool Insert(const skillset_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const skillset_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	skillset_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static skillset_template_manager& GetInstance()
	{
		static skillset_template_manager _s_instance;
		return _s_instance;
	}
};

struct loottable_template
{
	struct item_tab
	{
		int     id_item;                // 掉落物品
		int	id_gen_config;		// 处理模板
		float   odds;                   // 掉落物品几率
		int 	min_count;
		int 	max_count;
	};

	tid_t tid;
	LOOT_TYPE_ENUM type;

	int time_from_drop; //从掉落起的消失时间（秒）

	item_tab                solo_items[8];                          // 单独掉落物品表
	item_tab                special_items[16];                      // 特殊掉落物品表
	item_tab                normal_items[128];                       // 普通掉落物品表

	float                   special_odds[5];                        // 特殊掉落产生0、1、2、3、4个物品的概率
	float                   normal_odds[EXP_LOOTTABLE_MAX_NORMAL_ITEM + 1]; // 普通掉落产生0、1、2、3、4个物品的概率

public:
	void LoadFromEssence(const LOOTTABLE_ESSENCE& ess);
};

class loottable_template_manager
{
	typedef abase::hash_map<tid_t,loottable_template> MAP;
	MAP _map;

	bool Insert(const loottable_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const loottable_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	loottable_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static loottable_template_manager& GetInstance()
	{
		static loottable_template_manager _s_instance;
		return _s_instance;
	}
};

struct droptable_template
{
	tid_t tid;
	LOOT_TYPE_ENUM type;
	struct item_tab
	{
		int             id_item;        // 掉落物品
		float           odds;           // 掉落物品几率
		int             mf_affect_init; // 受MF影响初始值
		float           mf_affect_rate; // 受MF影响率

	} drops[128]; 

public:
	void LoadFromEssence(const DROPTABLE_ESSENCE& ess);
};

class droptable_template_manager
{
	typedef abase::hash_map<tid_t,droptable_template> MAP;
	MAP _map;

	bool Insert(const droptable_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const droptable_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	droptable_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static droptable_template_manager& GetInstance()
	{
		static droptable_template_manager _s_instance;
		return _s_instance;
	}
};
struct hero_template
{
	tid_t tid;		//名人模板自身的id
	CELEBRITY_TYPE type;
	int prof;		//职业
	int equipment_lock;	//装备锁定mask
	tid_t upgrade_tid;	//培养模板ID
	int unlock_fly_level;	//解锁飞行驾照玩家等级
	int unlock_fly_capacity;//解锁飞行驾照玩家评分
	float cruise_speed;	//巡游速度
};

struct hero_template_manager
{
	typedef abase::hash_map<tid_t,hero_template>  HERO_TEMPLATE_MAP;
	HERO_TEMPLATE_MAP _template_map;
	
	const hero_template* GetTemplate(tid_t tid) const
	{
		HERO_TEMPLATE_MAP::const_iterator it = _template_map.find(tid);
		if(it == _template_map.end()) return NULL;
		return &(it->second);
	}

	static hero_template_manager& GetInstance()
	{
		static hero_template_manager _s_instance;
		return _s_instance;
	}
	bool Load(elementdataman& data_man);
};

struct wing_train_template
{
	tid_t tid;
	int wingMaxLevel;						//翅膀等级上限

	struct EachTrainingEffect
	{
		int gain_exp;					//每次培养可获得的进阶成长值
		int crit_x2_odd_percentage;			//培养暴击2倍概率百分数
		int crit_x3_odd_percentage;			//培养暴击3倍概率百分数
		int crit_x5_odd_percentage;			//培养暴击5倍概率百分数
		int crit_x10_odd_percentage;			//培养暴击10倍概率百分数
		float inc_prof[EXP_WING_UPGRADE_PROFS_COUNT]; //单次培养属性增加
	} each_trainig_effect;					//单次培养的效果

	struct Grade
	{
		int quality;					//第i阶品质 (0表示不能升到此阶)
		int upgrade_cost_exp;				//第i阶进阶到下一阶所需成长值
		int cost_item_id;				//第i阶培养消耗的道具ID
		int cost_item_num;				//第i阶培养消耗的道具数量
		int train_need_player_level;			//第i阶需要人物等级
		int max_training_num;				//培养次数上限
		int award_licence_index;			//解锁第几个执照
		int award_fixview_config_id;			//第i阶开启幻化形象ID
	} grades[EXP_WING_GRADE_MAX];				//各阶属性

	int	unlock_fly_grade;			//飞行解锁阶段
	int	unlock_double_fly_grade;		//双人飞行解锁阶段
	struct  
	{
		int addon_group_id;			//激活附加属性组ID
		int speed_up_percent;			//速度增加百分比
	} licences[EXP_WING_FLY_SKILL_MAX];			//座驾执照

	std::map<tid_t, int> surface_tid_level_map;
	std::map<int, tid_t> surface_level_tid_map;
public:
	void LoadFromEssence(const WING_UPGRADE_CONFIG& ess);
	tid_t GetSurfaceByLevel(int level) const
	{
		auto it = surface_level_tid_map.find(level);
		if(it == surface_level_tid_map.end())
			return 0;

		return it->second;
	}
};

class wing_train_template_manager
{
	typedef abase::hash_map<tid_t, wing_train_template> MAP;
	MAP _map;

	bool Insert(const wing_train_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}
public:
	bool Load(elementdataman& data_man);

	const wing_train_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	wing_train_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static wing_train_template_manager& GetInstance()
	{
		static wing_train_template_manager _s_instance;
		return _s_instance;
	}
};
struct color_random_template
{
	tid_t tid;
	struct
	{
		unsigned int color_argb;
		float odds;
	} color_info[EXP_PAINT_COLOR_CFG_NUM];
	int unlock_cost_item_id;			//解锁花费的道具id
	int unlock_cost_item_num;			//解锁花费的道具数量
public:
	void LoadFromEssence(const COLOR_RANDOM_CONFIG& ess);
};

struct color_random_template_manager
{
	typedef abase::hash_map<tid_t, color_random_template> MAP;
	MAP _map;

	bool Insert(const color_random_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const color_random_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	color_random_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static color_random_template_manager& GetInstance()
	{
		static color_random_template_manager _s_instance;
		return _s_instance;
	}

};
struct dyeing_config_template
{
	unsigned int tid;
	int color_unlock_tool_id; // 开启颜色使用物品id
	int color_unlock_cost_num; // 开启颜色使用物品数量
	int dye_tool_id; // 染色使用物品id
	int dye_cost_num; // 染色使用物品数量
	int saturation_adjust_tool_id; // 调饱和度使用物品id
	int saturation_adjust_cost_num; // 调饱和度使用物品数量
	int halo_adjust_tool_id; // 调光晕使用物品id
	int halo_adjust_cost_num; // 调光晕使用物品数量
public:
	void LoadFromEssence(const DYEING_CONFIG& ess);
};

struct dyeing_config_template_manager
{
	typedef abase::hash_map<tid_t, dyeing_config_template> MAP;
	MAP _map;

	bool Insert(const dyeing_config_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}
public:
	bool Load(elementdataman& data_man);

	const dyeing_config_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	dyeing_config_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static dyeing_config_template_manager& GetInstance()
	{
		static dyeing_config_template_manager _s_instance;
		return _s_instance;
	}
};
struct wing_invoke_template
{
	tid_t tid;
	int skill1_unlock_quality;			// 炫技1解锁品质
	int skill2_unlock_quality;			// 炫技2解锁品质
	int skill3_unlock_quality;			// 炫技3解锁品质
	int fly_unlock_quality;				// 飞行解锁品质
	int max_quality;				//品质上限
	struct
	{
		int add_color_num;			//染色色块增加数量，16，32，48，64按照这种格式填写
		int add_texture_num;			//特殊材质增加数量
		int prgs_count;       			//进度上限
		int need_item1_id;	  		//所需物品1tid
		int need_item1_count; 			//所需物品1数量
		int need_item2_id;	  		//所需物品2tid
		int need_item2_count; 			//所需物品2数量
		int need_bind_money;		//所需绑定金币数量
	} invoke_cfg[EXP_WING_INVOKE_MAX_COUNT];	//品质配置属性

public:
	void LoadFromEssence(const WING_INVOKE_CONFIG& ess);
};

struct wing_invoke_template_manager
{
	typedef abase::hash_map<tid_t, wing_invoke_template> MAP;
	MAP _map;

	bool Insert(const wing_invoke_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}
public:
	bool Load(elementdataman& data_man);

	const wing_invoke_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	wing_invoke_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static wing_invoke_template_manager& GetInstance()
	{
		static wing_invoke_template_manager _s_instance;
		return _s_instance;
	}
};

struct wing_color_cfg_template
{
	tid_t tid;
	int addon_group_id;
	int unlock_item_id1;
	int unlock_item_num1;
	int unlock_item_id2;
	int unlock_item_num2;
public:
	void LoadFromEssence(const WING_COLOR_BLOCK_CONFIG& ess);
};

class wing_color_cfg_template_manager
{
	typedef abase::hash_map<tid_t, wing_color_cfg_template> MAP;
	MAP _map;

	bool Insert(const wing_color_cfg_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}
public:
	bool Load(elementdataman& data_man);

	const wing_color_cfg_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	wing_color_cfg_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static wing_color_cfg_template_manager& GetInstance()
	{
		static wing_color_cfg_template_manager _s_instance;
		return _s_instance;
	}
};
struct surface_template
{
	tid_t tid;							// 幻化模板id
	bool is_fly;							// 是否初始可飞行
	bool can_swim;							// 能否下水
	bool can_dive;							// 能否潜水
	WING_FIXVIEW_TYPE  wing_type;					// 类型
	tid_t addon_group_id;						// 激活附加属性组ID
	int auto_equip;							// 是否自动装备
	int max_contain_people;						// 绑定中容纳的最大人数
	int paint_part_count;						// 染色部位数量
	struct {
		unsigned int color;		// 默认颜色
	} paint_part[EXP_WING_PAINT_MAX_COUNT];		// 染色区 
	int fashion_part_count;						// 改装部位数量
	struct {
		int part_fashion_count;					// 部位改装数量
		struct {
			int addon_group_id;		// 激活附加属性组ID
			bool is_advanced;		// 是否是高级装备
			int need_item1_id;		// 所需物品1ID
			int need_item1_count;		// 所需物品1数量
			int need_item2_id;		// 所需物品2ID
			int need_item2_count;		// 所需物品2数量
			int need_item3_id;		// 所需物品3ID
			int need_item3_count;		// 所需物品3数量
		} fashions[EXP_WING_FASHION_EACH_POS_MAX_COUNT];	// 改装
	} fashion_part[EXP_WING_FASHION_MAX_COUNT];		// 改装部位
	int paint_need_item_id;						// 染色需要的道具ID
	int paint_need_item_num;					// 染色需要的道具数量
	int wing_invoke_cfg;						//飞剑铸灵模板
	int skill_id1; // 炫技id1
	int skill_id2; // 炫技id2
	int skill_id3; // 炫技id3
	bool can_xyxw; // 是否可相依相偎
	bool xyxw_mutex_with_second; 				// 相依相偎是否与第二挂点互斥
	int produce_score;					// 制造所需要玩家评分
	int produce_level;					// 制造所需要玩家等级
	int produce_money;					// 制造所需金币
	int produce_item1_id;					// 制造所需物品1ID
	int produce_item1_count;				// 制造所需物品1数量
	int produce_item2_id;					// 制造所需物品2ID
	int produce_item2_count;				// 制造所需物品2数量
	int produce_item3_id;					// 制造所需物品3ID
	int produce_item3_count;				// 制造所需物品3数量
	int produce_item4_id;					// 制造所需物品4ID
	int produce_item4_count;				// 制造所需物品4数量
	int init_active_color_count;				// 座驾幻化初始激活颜色数量 	
	int max_active_color_count;				// 座驾最大激活颜色数量
	int special_material_count;				// 特殊材质数量
	struct {
		int addon_group_id;	//附加属性组ID
		int unlock_item_id;	//解锁物品ID
		int unlock_item_count;	//解锁物品数量
	} special_material[EXP_WING_TEXTURE_MAX_COUNT];	// 特殊材质
	int color_num;						// 颜色数量
	int colors[EXP_PAINT_COLOR_CFG_NUM];			// 拥有的颜色ID
	SURFACE_SPEED_LEVEL speed_config; // 座驾挡位配置
	int unlock_longyu;						// 解锁的龙语
	int prosperity = 0;
	int unlock_longhun;
	tid_t effect_tid;				// 焕彩模板id

public:
	void LoadFromEssence(const WING_FIXVIEW_CONFIG& ess);
	inline int GetMaxContainPeople() const
	{
		return max_contain_people;
	}
	inline bool CanSwim() const
	{
		return can_swim;
	}
	inline bool CanDive() const
	{
		return can_swim;
	}
	inline bool IsFly() const
	{
		return is_fly;
	}
	inline bool CanXYXW() const
	{
		return can_xyxw;
	}
	inline bool MutexWithSecond() const
	{
		return xyxw_mutex_with_second;
	}
};

class surface_template_manager
{
	typedef abase::hash_map<tid_t, surface_template> MAP;
	MAP _map;

	bool Insert(const surface_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const surface_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	surface_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static surface_template_manager& GetInstance()
	{
		static surface_template_manager _s_instance;
		return _s_instance;
	}
};

struct effect_template
{
public:
	static const int EFFECT_UNLOCK_LIST_LENGTH = 5;
	static const int EFFECT_UNLOCK_ADDON_LIST_LENGTH = 5;

	struct effect_addon{
		int addon_type; 	// 附加属性类型
		int addon_value; 	// 附加属性值
		PROFTYPE_MASK prof_limit_mask;	// 职业限制
	};

	struct effect_upgrade {
		int max_level;
		std::vector<effect_addon> upgrade_addon;
		std::vector<effect_addon> advance_addon;
	};

	struct effect_unlock {
		std::string name;
		uint required_level;
		uint cost_id;
		uint cost_num;
		std::vector<effect_addon> unlock_addon;
	};

	void LoadFromEssence(const WING_HUANCAI_CFG& ess);

	// 模板数据
	tid_t tid;				// 焕彩模板id
	std::vector<effect_upgrade> upgrade_list;
	std::vector<effect_unlock> unlock_list;
};

class effect_template_manager
{
public:
	struct effect_cost {
		int min_level = -1;
		int max_level = -1;
		int cost_money = 0;
		std::vector<int> cost;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			if(v.isMember(LuaAnyValue("min_level")) && v.isMember(LuaAnyValue("max_level")))
			{
				min_level = (int)v["min_level"];
				max_level = (int)v["max_level"];
			}
			cost_money = (int)v["cost_money"];
			v["cost"].Fetch(cost);
		}
	};

	bool Load(elementdataman& data_man);
	bool LoadFromLua();

	const effect_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static effect_template_manager& GetInstance()
	{
		static effect_template_manager _s_instance;
		return _s_instance;
	}

	const int& GetMaxStageLevel() const {return max_stage_level;}

	const std::vector<effect_cost>& GetUpgradeCost(int key) {return upgrade_cost[key];}

	const effect_cost& GetAdvanceCost(int key) {return advance_cost[key];}

private:
	typedef abase::hash_map<tid_t, effect_template> MAP;
	MAP _map;

	// 脚本数据
	int max_stage_level;
	std::map<int, std::vector<effect_cost>> upgrade_cost;
	std::map<int, effect_cost> advance_cost;

	bool Insert(const effect_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}
};

//在txt中配置的掉落组，每组物品个数不限
class loot_group_manager
{
	typedef abase::vector<int> GROUP_LIST;
	typedef abase::hash_map<int,GROUP_LIST > MAP;
	MAP _map;

public:
	bool Load(const std::string& file_name);
	void GetLootItem(int id, size_t count, abase::vector<int>& items);
	static loot_group_manager& GetInstance()
	{
		static loot_group_manager _s_instance;
		return _s_instance;
	}
};

struct level_exp_factor_template
{
	tid_t tid;
	float exp_factor[MAX_LEVEL];

public:
	void LoadFromConfig(const TASK_LVEXPFACTOR_CONFIG& cfg);
};

class level_exp_factor_template_manager
{
	typedef abase::hash_map<tid_t,level_exp_factor_template> MAP;
	MAP _map;

	bool Insert(const level_exp_factor_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	float GetFactor(tid_t tid, unsigned char level) const
	{
		const level_exp_factor_template* pTemp = Get(tid);
		if(!pTemp)
		{
			return .0f;
		}
		if (level > MAX_LEVEL)
		{
			return .0f;
		}
		if (level < MIN_LEVEL)
		{
			level = MIN_LEVEL;
		}
		return pTemp->exp_factor[level - 1];
	}

	const level_exp_factor_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static level_exp_factor_template_manager& GetInstance()
	{
		static level_exp_factor_template_manager _s_instance;
		return _s_instance;
	}
};

struct cluster_template
{
	int id_ctrl;
	int max_live_count;                         //      群组同时存活个数上限
	int max_gen_count;                          //      群组可以生成的对象总数
	int delay_time_gen;                         //      初始生成延时时间
	int min_count_when_not_enough;      //      对象不足时的最低只数
	int delay_time_add;                         //      进行补齐的时间间隔
	int per_add_count;                          //      进行补齐的每批只数
	int dead_refreshtime;                       //      对象死亡刷新统一时间  这个暂时不做from向楠
	unsigned int is_gen_counting;                        //      是否群组生成对象计数
	int gen_count_mark;                         //      群组生成对象总数超过多少数量
	int id_ctrl_exceed_gen_count_mark;// 群组生成对象总数超过上述数量触发控制器
	unsigned int is_dead_counting;                       //      是否群组对象死亡计数
	int dead_count_mark;                        //      群组死亡对象总数超过多少数量
	int id_ctrl_exceed_dead_count_mark;// 群组死亡对象总数超过上述数量触发控制器
	unsigned int is_trigger_event_exceed_max_gen_count;  //      是否群组生成对象到达总数上限触发事件
	int id_openctrl_exceed_max_gen_count;       //      群组生成对象到达总数上限时触发控制器
	int id_closectrl_exceed_max_gen_count;      //      群组生成对象到达总数上限时关闭控制器
	unsigned int is_trigger_event_exceed_max_gen_count_and_all_dead;     //      是否群组生成对象到达总数上限且全灭触发事件
	int id_openctrl_exceed_max_gen_count_and_all_dead;  //      群组生成对象到达总数上限且全灭时触发控制器
	int id_closectrl_exceed_max_gen_count_and_all_dead; //      群组生成对象到达总数上限且全灭时关闭控制器
	unsigned int close_self_ctrl_exceed_max_gen_count_and_all_dead;//群组生成对象到达总数上限且全灭时是否关闭群组自身控制器
};

class cluster_template_manager
{
	typedef abase::hash_map<tid_t,cluster_template> MAP;
	MAP _map;

	bool Insert(tid_t tid,const cluster_template& temp)
	{
		if(_map.find(tid) != _map.end()) return false;
		_map[tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const cluster_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	cluster_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static cluster_template_manager& GetInstance()
	{
		static cluster_template_manager _s_instance;
		return _s_instance;
	}
};
struct affixes_addon_group_config_template
{
	unsigned int id;
	std::vector<int> id_addon_group; // 词缀属性组
	std::map<int/*index*/, int/*id*/> group_index_map;
};
struct affixes_addon_group_id_config
{
	unsigned int id;
	unsigned int index;
};
class affixed_addon_group_config_manager
{
	typedef abase::hash_map<int, affixes_addon_group_config_template> MAP;
	MAP _map;
	std::map<int/*addon_group_id*/, affixes_addon_group_id_config> _addon_affixGroup_map;
	bool Insert(const affixes_addon_group_config_template& temp)
	{
		if(_map.find(temp.id) != _map.end())
			return false;
		_map[temp.id] = temp;
		return true;
	}
	bool NewAddonId(int id, unsigned int index, unsigned int affixes_group_id)
	{
		if(contains(_addon_affixGroup_map, id))
			return false;
		auto& id_config = _addon_affixGroup_map[id];
		id_config.id = affixes_group_id;
		id_config.index = index;
		return true;
	}
public:
	bool Load(elementdataman& data_man);

	const affixes_addon_group_config_template* Get(int id) const
	{
		MAP::const_iterator it = _map.find(id);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static affixed_addon_group_config_manager& GetInstance()
	{
		static affixed_addon_group_config_manager _s_instance;
		return _s_instance;
	}
	const affixes_addon_group_id_config* GetAddOnInfo(int id) const
	{
		auto it = _addon_affixGroup_map.find(id);
		if (it == _addon_affixGroup_map.end())
		{
			return NULL;
		}
		return &it->second;
	}
};
struct skillprop_group_config_template
{
	unsigned int id;
	bool is_mixed_draw;

	struct levelcfg_t
	{
		int		value;	//	值
		float	odds;	//	出现几率
	};
	std::vector<levelcfg_t> skilllevel_cfg;	// 技能等级随机范围

	float odds_skillprop_count[EXP_SKILLPROPGRP_GEN_COUNT];	// [0]~[2]分别表示出现1~3个技能属性的几率

	struct idcfg_t
	{
		std::vector<int> ids; //技能ID
	};
	std::vector<idcfg_t> skillid_cfg; //技能类型
};

class skillprop_group_config_manager
{
	typedef abase::hash_map<int, skillprop_group_config_template> MAP;
	MAP _map;

	bool Insert(const skillprop_group_config_template& temp)
	{
		if(_map.find(temp.id) != _map.end()) return false;
		_map[temp.id] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const skillprop_group_config_template* Get(int id) const
	{
		MAP::const_iterator it = _map.find(id);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static skillprop_group_config_manager& GetInstance()
	{
		static skillprop_group_config_manager _s_instance;
		return _s_instance;
	}
};

struct rankaward_config_template
{
	unsigned int id;
	struct
	{
		int	rank;		//	答题名次
		int id_task;	//	触发任务
	} config[EXP_RANKAWARD_COUNT];

	int GetTask(int rank) const;
};

class rankaward_config_manager
{
	typedef abase::hash_map<int, rankaward_config_template> MAP;
	MAP _map;

	bool Insert(const rankaward_config_template& temp)
	{
		if(_map.find(temp.id) != _map.end()) return false;
		_map[temp.id] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const rankaward_config_template* Get(int id) const
	{
		MAP::const_iterator it = _map.find(id);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	int GetRankTask(int config_id, int rank) const;

	static rankaward_config_manager& GetInstance()
	{
		static rankaward_config_manager _s_instance;
		return _s_instance;
	}
};

struct compensation_config_template
{
	unsigned int id;
	int activity_id;
	int reward_id;    
	int min_role_level;
	int account_repu;
};

class compensation_config_manager
{
	typedef abase::hash_map<int, compensation_config_template> MAP;
	MAP _map;

	bool Insert(const compensation_config_template& temp)
	{
		if(_map.find(temp.id) != _map.end()) return false;
		_map[temp.id] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const compensation_config_template* Get(int id) const
	{
		MAP::const_iterator it = _map.find(id);
		if(it == _map.end()) return NULL;
		return &it->second;
	}
	
	const compensation_config_template* IsValid(int activity_id) const
	{
		for(MAP::const_iterator it = _map.begin(); it != _map.end(); ++it)
		{
			if (it->second.activity_id == activity_id)
			    return &it->second;
		}
		
		return NULL;
	}

	const MAP GetAll() const 
	{
		return _map;
	}

	static compensation_config_manager& GetInstance()
	{
		static compensation_config_manager _s_instance;
		return _s_instance;
	}
};

struct equipment_levelup_config_template
{
	unsigned int id;
	unsigned int level_max;  // 装备等级上限

	struct config_t
	{
		unsigned int total_req_exp;  // 经验值需要达到
		unsigned int addon_group;    // 附加属性组
		unsigned int money_cost;     // 耗的金钱基数
    };
	std::vector<config_t> configs; // 装备升到等级

	const config_t* GetLevelUpConfig(unsigned char level) const
	{
		if (0 == level || level > configs.size()) return NULL;
		return &configs[level - 1];
	}

	unsigned char GetLevel(unsigned int exp) const
	{
		for (int level = configs.size(); level >= 1; --level)
		{
			if (exp >= configs[level - 1].total_req_exp) return level;
		}
		return 0;
	}

	bool GetLevelUpRequirement(unsigned int cur_level, unsigned int& req_exp, unsigned int& req_base_money) const
	{
		if (level_max <= 0 || configs.size() < level_max) return false;
		if (cur_level >= level_max) return false;
		req_exp = configs[cur_level].total_req_exp;
		req_base_money = configs[cur_level].money_cost;
		return true;
	}

	unsigned int GetMaxExp() const
	{
		if (level_max <= 0 || configs.size() < level_max) return 0;
		return configs[level_max - 1].total_req_exp;
	}
};

class equipment_levelup_config_manager
{
	typedef abase::hash_map<int, equipment_levelup_config_template> MAP;
	MAP _map;

	bool Insert(const equipment_levelup_config_template& temp)
	{
		if(_map.find(temp.id) != _map.end()) return false;
		_map[temp.id] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const equipment_levelup_config_template* Get(int id) const
	{
		MAP::const_iterator it = _map.find(id);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static equipment_levelup_config_manager& GetInstance()
	{
		static equipment_levelup_config_manager _s_instance;
		return _s_instance;
	}
};


struct pet_trans_force_config_template
{
	tid_t tid;
	struct
	{
		int cost_money;
		int cost_energy;
		float odds_succ;
	}level[EXP_PET_CHUANGONG_AIM_NUM];
};

class pet_trans_force_config_template_manager
{
	typedef abase::hash_map<tid_t,pet_trans_force_config_template> MAP;
	MAP _map;

	bool Insert(const pet_trans_force_config_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const pet_trans_force_config_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	pet_trans_force_config_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static pet_trans_force_config_template_manager& GetInstance()
	{
		static pet_trans_force_config_template_manager _s_instance;
		return _s_instance;
	}
};

struct task_grant_reward_config_template
{
	tid_t tid;
	char reward_prop_type;
	struct
	{    
		unsigned int    id;                     // 奖励发放模版
		float           probability;            // 发放概率
	} reward_lists[EXP_TASK_GRANT_REWARD_CONFIG_MAX];
};

class task_grant_reward_config_template_manager
{
	typedef abase::hash_map<tid_t,task_grant_reward_config_template> MAP;
	MAP _map;

	bool Insert(const task_grant_reward_config_template& temp)
	{
		if(_map.find(temp.tid) != _map.end()) return false;
		_map[temp.tid] = temp;
		return true;
	}

public:
	bool Load(elementdataman& data_man);

	const task_grant_reward_config_template* Get(tid_t tid) const
	{
		MAP::const_iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	task_grant_reward_config_template* Get(tid_t tid)
	{
		MAP::iterator it = _map.find(tid);
		if(it == _map.end()) return NULL;
		return &it->second;
	}

	static task_grant_reward_config_template_manager& GetInstance()
	{
		static task_grant_reward_config_template_manager _s_instance;
		return _s_instance;
	}

	int CalcRewardTempl(int task_grant_tid) const
	{
		const task_grant_reward_config_template *pRewardConfig = Get(task_grant_tid);
		if (!pRewardConfig)
		{
			return 0;
		}

		if (pRewardConfig->reward_prop_type == TASK_GRANT_REWARD_PROB_TYPE_INDEPEND) // 独立概率
		{
			for (int i = 0; i < EXP_TASK_GRANT_REWARD_CONFIG_MAX; ++i)
			{
				float prop = abase::RandUniform();
				if (prop >= pRewardConfig->reward_lists[i].probability)
				{
					continue;
				}

				return pRewardConfig->reward_lists[i].id;
			}
		}
		else if (pRewardConfig->reward_prop_type == TASK_GRANT_REWARD_PROB_TYPE_NORMALIZATION) // 归一化概率
		{
			float odds = 0.f;
			float prop = abase::RandUniform();
			for (int i = 0; i < EXP_TASK_GRANT_REWARD_CONFIG_MAX; ++i)
			{
				odds += pRewardConfig->reward_lists[i].probability;
				if (prop >= odds)
				{
					continue;
				}

				return pRewardConfig->reward_lists[i].id;
			}
		}

		return 0;
	}
};

struct idphoto_config_template
{
	int tid;
	int id_addon_group;
	ROLE_PHYSIC_MASK body_type_mask;
	int gender_mask;
	PROFTYPE_MASK prof_mask;
	void Load(const IDPHOTO_CONFIG & ess)
	{
		tid = ess.id;
		id_addon_group = ess.id_addon_group;
		body_type_mask = ess.body;
		gender_mask = ess.gender + 1;
		prof_mask = ess.character_combo_id;
	}
	void Load(const IDPHOTO_FRAME_CONFIG & ess)
	{
		tid = ess.id;
		id_addon_group = ess.id_addon_group;
		body_type_mask = ess.body;
		gender_mask = ess.gender + 1;
		prof_mask = ess.character_combo_id;
	}
		
};

struct color_block_template
{
	int tid;
	int color_value;
	void Load(const COLOR_BLOCK_CONFIG & ess)
	{
		tid = ess.id;
		color_value = ess.value;
	}
};

class TeamRepuManager
{
public:
	void PrintAllTeamRepuLog()
	{
		std::string log_str;
		char buf[64];
		for (auto it = m_all_team_repu.begin(); it != m_all_team_repu.end(); ++it)
		{
			memset(buf, 0, sizeof(buf));
			snprintf(buf, sizeof(buf), "[%d,%d]", it->first, it->second);
			log_str += buf;
		}
		__PRINTF("TeamRepuManager::PrintAllTeamRepuLog[repu_id, tid]=%s\n", log_str.c_str());
	}

	bool Load(const std::string &path)
	{
		lua_State *L = luaL_newstate();
		if (!L)
		{
			return false;
		}
		luaL_openlibs(L);
		luaL_requiref(L, "io", luaopen_io, 1);
		if (luaL_dofile(L, path.c_str()))
		{
			__PRINTF("TeamRepuManager::load fail:path=%s, err=%s\n", path.c_str(), lua_tostring(L, -1));
			lua_close(L);
			return false;
		}

		m_all_team_repu.clear();
		LuaTableAnalyze l(L, "GetTeamRepu", m_all_team_repu);
		lua_close(L);
		PrintAllTeamRepuLog();
		return true;
	}

	const std::map<int, int>& GetAllTeamRepu() const
	{
		return m_all_team_repu;
	}

	bool IsTeamRepuOpen(int repu_id) const
	{
		if (m_all_team_repu.empty())
		{
			return false;
		}

		std::map<int, int>::const_iterator it = m_all_team_repu.find(repu_id);
		if (it == m_all_team_repu.end())
		{
			return false;
		}
		return activity_manager::GetInstance().IsActivityOpen(it->second);
	}

	static TeamRepuManager& GetInstance()
	{
		static TeamRepuManager _s_instance;
		return _s_instance;
	}

private:
	TeamRepuManager() {};
	TeamRepuManager(const TeamRepuManager&) = delete;
	TeamRepuManager& operator = (const TeamRepuManager&) = delete;

protected:
	std::map<int, int> m_all_team_repu;	// 全部的队伍声望，k=repu_id，v=tid
};

class SceneGameTime 
{
private:
	SceneGameTime() { }
	SceneGameTime(const SceneGameTime&) = delete;
	SceneGameTime& operator = (const SceneGameTime&) = delete;

	std::map<int, int> scene_game_time_map;

public:
	static SceneGameTime& GetInstance()
	{
		static SceneGameTime _instance;
		return _instance;
	}

	bool Load(const std::string &path)
	{
		lua_State *L = luaL_newstate();
		if (!L)
		{
			return false;
		}
		luaL_openlibs(L);
		luaL_requiref(L, "io", luaopen_io, 1);
		if (luaL_dofile(L, path.c_str()))
		{
			lua_close(L);
			return false;
		}

		LuaTableAnalyze l(L, "GetSceneGameTime", scene_game_time_map);
		lua_close(L);
		return true;
	}

	bool GetGameHour(scene_tag_t scene_tag, int &game_hour)
	{
		auto it = scene_game_time_map.find(scene_tag);
		if (it == scene_game_time_map.end())
		{
			return false;
		}

		game_hour = it->second;
		return true;
	}
};

class CGRewardManager
{
	struct cg_info_t
	{
		int pre_task_id = 0;
		int reward_tid = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			pre_task_id = v["pre_task"];
			reward_tid = v["reward_tid"];
		}
	};
	using CG_INFO_MAP = std::unordered_map<int, cg_info_t>;

private:
	CGRewardManager() { }
	CGRewardManager(const CGRewardManager&) = delete;
	CGRewardManager& operator = (const CGRewardManager&) = delete;

public:
	static CGRewardManager & GetInstance()
	{
		static CGRewardManager _instance;
		return _instance;
	}
	bool Load(const std::string &path)
	{
		try
		{
			LuaTableNode root;
			if (!LuaConfReader::parseFromFile(path.c_str(), "cg_config", root))
			{
				__PRINTF("CGRewardManager::Load path:%s execute failed.\n", path.c_str());
			}
			root.Fetch(m_info_map);
		}
		catch (...)
		{
			__PRINTF("CGRewardManager::Load path:%s fetch failed.\n", path.c_str());
			return false;
		}
		return true;
	}
	bool GetCGInfo(int cg_id, int& pre_task_id, int& reward_tid) const
	{
		auto it = m_info_map.find(cg_id);
		if (it == m_info_map.end())
		{
			return false;
		}
		pre_task_id = it->second.pre_task_id;
		reward_tid = it->second.reward_tid;
		return true;
	}

private:
	CG_INFO_MAP m_info_map;
};

class HuntDragonManager
{
	struct team_repu_t
	{
		int leader_repu = 0;
		std::set<tid_t> instance_tids;

		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			leader_repu = v["leader_repu"];
			v["instance_tids"].Fetch(instance_tids);
		}
	};
	struct team_level_t
	{
		int team_level = 0;
		std::vector<team_repu_t> team_level_vec;

		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			team_level = v["team_level"];
			v["team_level_map"].Fetch(team_level_vec);
		}
	};
	struct hunt_dragon_day_t
	{
		std::vector<team_level_t> hunt_dragon_day_map;

		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			v["hunt_dragon_day_map"].Fetch(hunt_dragon_day_map);
		}
	};
	typedef std::map<int, hunt_dragon_day_t> hunt_dragon_config_t;

	typedef std::set<tid_t> INSTANCE_TIDS_SET;
	typedef std::map<int, INSTANCE_TIDS_SET> TEAM_REPU_MAP;
	typedef std::map<int, TEAM_REPU_MAP> TEAM_LEVEL_MAP;
	typedef std::map<int, TEAM_LEVEL_MAP> hunt_dragon_day_map;

private:
	HuntDragonManager() { }
	HuntDragonManager(const HuntDragonManager&) = delete;
	HuntDragonManager& operator = (const HuntDragonManager&) = delete;

	hunt_dragon_day_map hunt_dragon_map;

public:
	static HuntDragonManager& GetInstance()
	{
		static HuntDragonManager _instance;
		return _instance;
	}

	bool Load(const std::string &path)
	{
		LuaTableNode root;
		if (!LuaConfReader::parseFromFile(path.c_str(), "hunt_dragon_config", root))
		{
			__PRINTF("HuntDragonManager::Load fail for parseFromFile:path=%s\n", path.c_str());
			return false;
		}
		hunt_dragon_config_t hunt_dragon_config;
		root.Fetch(hunt_dragon_config);

		for (auto it1 = hunt_dragon_config.begin(); it1 != hunt_dragon_config.end(); ++it1)
		{
			if (it1->first < 1 || it1->first > HUNT_DRAGON_DAY_NUM)
			{
				__PRINTF("HuntDragonManager::Load fail for day err:path=%s, hunt_dragon_day=%d, hunt_dragon_day_num=%d\n", path.c_str(), it1->first, HUNT_DRAGON_DAY_NUM);
				return false;
			}
			TEAM_LEVEL_MAP team_level_map;
			for (auto it2 = it1->second.hunt_dragon_day_map.begin(); it2 != it1->second.hunt_dragon_day_map.end(); ++it2)
			{
				TEAM_REPU_MAP team_repu_map;
				for (auto it3 = it2->team_level_vec.begin(); it3 != it2->team_level_vec.end(); ++it3)
				{
					if (it3->instance_tids.empty())
					{
						__PRINTF("HuntDragonManager::Load fail for instance_tids:path=%s, hunt_dragon_day=%d, team_level=%d, team_repu=%d\n", path.c_str(), it1->first, it2->team_level, it3->leader_repu);
						return false;
					}
					std::string instance_tids_str;
					for (auto it4 = it3->instance_tids.begin(); it4 != it3->instance_tids.end(); ++it4)
					{
						if (*it4 <= 0)
						{
							__PRINTF("HuntDragonManager::Load fail for instance_tid <= 0:path=%s, hunt_dragon_day=%d, team_level=%d, team_repu=%d\n", path.c_str(), it1->first, it2->team_level, it3->leader_repu);
							return false;
						}
						instance_tids_str.append(",").append(std::to_string(*it4));
					}
					team_repu_map[it3->leader_repu] = it3->instance_tids;
					__PRINTF("HuntDragonManager::Load:hunt_dragon_day=%d, team_level=%d, team_repu=%d, instance_tids=(%s)\n", it1->first, it2->team_level, it3->leader_repu, instance_tids_str.c_str());
				}
				team_level_map.insert(std::make_pair(it2->team_level, team_repu_map));
			}
			hunt_dragon_map.insert(std::make_pair(it1->first, team_level_map));
		}

		int hunt_dragon_day_size = hunt_dragon_map.size();
		if (hunt_dragon_day_size != HUNT_DRAGON_DAY_NUM)
		{
			__PRINTF("HuntDragonManager::Load fail for day err:path=%s, hunt_dragon_day_size=%d, hunt_dragon_day_num=%d\n", path.c_str(), hunt_dragon_day_size, HUNT_DRAGON_DAY_NUM);
			return false;
		}

		return true;
	}

	bool CheckHuntDragonInstanceTid(int hunt_dragon_day, int team_level, int leader_repu, tid_t instance_tid)
	{
		auto it1 = hunt_dragon_map.find(hunt_dragon_day);
		if (it1 == hunt_dragon_map.end())
		{
			return false;
		}
		if (it1->second.empty())
		{
			return false;
		}
		const TEAM_LEVEL_MAP &team_level_map = it1->second;

		const TEAM_REPU_MAP *team_repu_map = nullptr;
		auto it2 = team_level_map.upper_bound(team_level);
		if (it2 == team_level_map.end())
		{
			team_repu_map = &(team_level_map.rbegin()->second);
		}
		else
		{
			team_repu_map = &(it2->second);
		}
		if (!team_repu_map || team_repu_map->empty())
		{
			return false;
		}

		const INSTANCE_TIDS_SET *instance_tids_set = nullptr;
		auto it3 = team_repu_map->upper_bound(leader_repu);
		if (it3 == team_repu_map->end())
		{
			instance_tids_set = &(team_repu_map->rbegin()->second);
		}
		else
		{
			instance_tids_set = &(it3->second);
		}
		if (!instance_tids_set || instance_tids_set->empty())
		{
			return false;
		}

		return (instance_tids_set->find(instance_tid) != instance_tids_set->end());
	}
};

class TriggerRewardManager
{
	using ID_SET = std::set<int>;
	using VIP_LEVEL_SECTION = std::vector<int>;

public:
	TriggerRewardManager() { }
	TriggerRewardManager(const TriggerRewardManager&) = delete;
	TriggerRewardManager& operator = (const TriggerRewardManager&) = delete;

	static TriggerRewardManager& GetInstance()
	{
		static TriggerRewardManager instance;
		return instance;
	}

	struct item_t
	{
		int id = 0;
		int num = 0;
		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			id = (int)v["item_id"];
			num = (int)v["item_num"];
		}
	};
	struct level_t
	{
		std::map<int, std::vector<int>> high_performance;
		std::map<int, std::vector<int>> low_performance;
		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			v["high_performance"].Fetch(high_performance);
			v["low_performance"].Fetch(low_performance);
		}
	};

	struct system_t
	{
		int player_level = 0;
		int counter_id = 0;
		std::vector<int> score_level;
		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			player_level = (int)v["player_level"];
			counter_id = (int)v["counter_id"];
			v["score_level"].Fetch(score_level);
		}
	};

	bool Load(const std::string &path)
	{
		LuaTableNode root;
		if (!LuaConfReader::parseFromFile(path.c_str(), "_G", root))
		{
			__PRINTF("TriggerRewardManager::Load fail for parseFromFile:path=%s\n", path.c_str());
			return false;
		}

		root["trigger_reward_config"]["task_ids"].Fetch(m_task_ids);
		root["trigger_reward_config"]["achievement_ids"].Fetch(m_achievement_ids);

		root["trigger_reward_config"]["vip_level_sections"].Fetch(m_vip_level_sections);
		std::sort(m_vip_level_sections.begin(), m_vip_level_sections.end());

		root["reward"].Fetch(_reward);
		root["level"].Fetch(_level);
		root["system"].Fetch(_system);
		_Init();

		return true;
	}

	inline bool IsTriggerTask(int task_id) const
	{
		return m_task_ids.find(task_id) != m_task_ids.end();
	}

	inline bool IsTriggerAchievement(int achievement_id) const
	{
		return m_achievement_ids.find(achievement_id) != m_achievement_ids.end();
	}

	inline int GetVipLevelSection(int vip_level) const
	{
		size_t i = 0;
		for ( ; i < m_vip_level_sections.size(); ++ i)
		{
			if (vip_level <= m_vip_level_sections[i])
			{
				break;
			}
		}
		return i;
	}

	//根据充值档位获取礼包里的奖励
	bool GetReward(gplayer_imp *imp, int recharge_level, int& reward_need, int& reward_high, int& reward_low) const;

	//根据奖励id获取道具
	bool GetItem(int reward_id, int& item_id, int& item_num);

	//获得奖励时修改计数器
	void ModifyCounterByReward(gplayer_imp *imp, int reward_id) const;

	float GetSystemScore(gplayer_imp *imp, int system_id) const;
	int GetSystemLevel(int system_id, float system_score) const;
	int GetCounterIDBySystem(int id) const;
private:
	void _Init();
	int _GetCounterIDByReward(int reward_id) const;
	void _GetSystemIDList(int recharge_level, int player_level, std::set<int>& id_list) const;
	bool _IsSystemOpen(int system_id, int player_level) const;
	void _SelectSystem(gplayer_imp *imp, std::map<int, int>& low_system_id_list, int& low_system_id) const;
	void _SelectRewardNeed(int recharge_level, int system_id, int& reward_need) const;
	void _SelectRewardHigh(gplayer_imp *imp, int recharge_level, int ignore_system_id, int& reward_high) const;
	void _SelectRewardLow(gplayer_imp *imp, int recharge_level, int ignore_system_id, int& reward_low) const;
	void _SelectRewardPerformance(gplayer_imp *imp, int ignore_system_id, const std::map<int, std::vector<int>>& conf, int& reward_select) const;
	void _ModifyCounterBySelectReward(gplayer_imp *imp, int reward_id) const;
	void _ErrorLog(gplayer_imp *imp, int recharge_level, int tag) const;
	int _GetCounter(gplayer_imp *imp, int counter_id) const;

private:
	ID_SET m_task_ids;
	ID_SET m_achievement_ids;
	VIP_LEVEL_SECTION m_vip_level_sections;

	std::map<int, item_t> _reward;//<奖励id, xxx>
	std::map<int, level_t> _level;//<充值档位，xxx>
	std::map<int, system_t> _system;//<系统id，xxx>
	std::unordered_map<int, int> _reward_2_counter;//<奖励id，计数器id>
};

class GlobalWorldReviveConfig
{
	struct scene_revive_config
	{
		struct pos_t
		{
			A3DVECTOR3 pos;
			void BuildFromLuaAnyValue(const LuaAnyValue &v)
			{
				pos.x = v["x"];
				pos.y = v["y"];
				pos.z = v["z"];
			}
		};
		std::vector<pos_t> pos_list;
		int max_death_count = 0;
		std::vector<int> wait_revive_time;
		int revive_time_reset_limit = 0;
		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			v["pos"].Fetch(pos_list);
			max_death_count = v["max_death_count"];
			v["wait_revive_time"].Fetch(wait_revive_time);
			revive_time_reset_limit = v["revive_time_reset_limit"];
			ASSERT(max_death_count <= (int)wait_revive_time.size());
		}
	};

	std::map<scene_tag_t, scene_revive_config> scene_map;

public:
	static GlobalWorldReviveConfig& GetInstance()
	{
		static GlobalWorldReviveConfig instance;
		return instance;
	}

	scene_revive_config *GetSceneConfig(scene_tag_t scene)
	{
		auto it = scene_map.find(scene);
		if (it != scene_map.end()) return &it->second;
		return NULL;
	}

	bool LoadConfig()
	{
		LuaTableNode root;
		if (!LuaConfReader::parseFromFile("./config/script/global_world_revive.lua", "_G", root))
		{
			__PRINTF("GlobalWorldReviveConfig::LoadConfig fail for parseFromFile\n");
			return false;
		}

		root["world_revive_config"].Fetch(scene_map);

		return true;
	}
};

class ThunderStrikeSkillSetConfig
{
	struct SkillConfig
	{
		int type = 0;
		int skill_id = 0;
		int skill_level = 0;
		int task_id = 0;
		int prof_limit = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			type = v["config_type"];
			skill_id = v["skill_id"];
			skill_level = v["skill_level"];
			task_id = v["task_id"];
			prof_limit = v["prof_limit"];
		}
	};

public:
	static ThunderStrikeSkillSetConfig& GetInstance()
	{
		static ThunderStrikeSkillSetConfig _instance;
		return _instance;
	}

	bool LoadConfig()
	{
		LuaTableNode root;
		if (!LuaConfReader::parseFromFile("./config/script/thunder_strike_skill_config_interface.lua", "_G", root))
		{
			__PRINTF("ThunderStrikeSkillSetConfig::LoadConfig fail for parseFromFile\n");
			return false;
		}

		root["skill_config"].Fetch(m_skill_map);
		return true;
	}

	const std::vector<SkillConfig>* GetSkillConfig(int skill_set) const
	{
		auto it = m_skill_map.find(skill_set);
		if (it == m_skill_map.end())
		{
			return NULL;
		}
		return &(it->second);
	}

private:
	std::map<int/*skill_set*/, std::vector<SkillConfig>> m_skill_map;
};

class activity_task_manager
{
	activity_task_manager() { }
	activity_task_manager(const activity_task_manager&) = delete;
	activity_task_manager& operator = (const activity_task_manager&) = delete;

public:
	struct activity_task_t
	{
		int repu_id = 0;
		std::vector<int/*task_id*/> tasks;

		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			repu_id = v["repu_id"];
			v["tasks"].Fetch(tasks);
		}
	};

public:
	static activity_task_manager& GetInstance()
	{
		static activity_task_manager man;
		return man;
	}

	bool LoadConfig()
	{
		LuaTableNode root;
		if (!LuaConfReader::parseFromFile("./config/script/activity_task.lua", "_G", root))
		{
			return false;
		}

		root["activity_task"].Fetch(m_activity_tasks);
		return true;
	}

	const activity_task_t* GetActivityTask(int activity_id) const
	{
		auto it = m_activity_tasks.find(activity_id);
		if (it == m_activity_tasks.end())
		{
			return NULL;
		}
		return &(it->second);
	}

	void ForEach(std::function<void(int activity_id, activity_task_t& activity_task)>&& fun)
	{
		for (auto kv: m_activity_tasks)
		{
			fun(kv.first, kv.second);
		}
	}

private:
	std::map<int/*activity_id*/, activity_task_t> m_activity_tasks;
};

class trigger_achievment_manager
{
	trigger_achievment_manager() { }
	trigger_achievment_manager(const trigger_achievment_manager&) = delete;
	trigger_achievment_manager& operator = (const trigger_achievment_manager&) = delete;

public:
	struct trigger_achievement_reward_t
	{
		int reward_id = 0;
		int pay_type = 0;
		int price = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue &v)
		{
			reward_id = v["reward_id"];
			pay_type = v["pay_type"];
			price = v["price"];

			ASSERT(reward_id > 0);
			ASSERT((pay_type == 0 || 
					(pay_type == MALL_PT_UNBIND_CASH && price > 0) || 
					(pay_type == MALL_PT_BIND_CASH && price > 0)));
		}
	};

public:
	static trigger_achievment_manager& GetInstance()
	{
		static trigger_achievment_manager man;
		return man;
	}

	bool LoadConfig()
	{
		LuaTableNode root;
		if (!LuaConfReader::parseFromFile("./config/script/trigger_achievement.lua", "_G", root))
		{
			return false;
		}
		root["trigger_achievement_reward_config"].Fetch(m_trigger_achievement_reward_configs);
		root["trigger_achievement_config"].Fetch(m_trigger_achievement_configs);
		for (auto& kv: m_trigger_achievement_configs)
		{
			ASSERT(m_trigger_achievement_reward_configs.find(kv.second) != m_trigger_achievement_reward_configs.end());
		}
		root["trigger_auto_reward_config"].Fetch(m_trigger_auto_reward_configs);
		for (auto& kv: m_trigger_auto_reward_configs)
		{
			ASSERT(m_trigger_achievement_reward_configs.find(kv.second) != m_trigger_achievement_reward_configs.end());
		}
		return true;
	}

	int GetRewardIndex(int achievement_id) const
	{
		auto it = m_trigger_achievement_configs.find(achievement_id);
		if (it == m_trigger_achievement_configs.end())
		{
			return 0;
		}
		return it->second;
	}

	int GetAutoRewardConfigIndex(int auto_reward_type) const
	{
		auto it = m_trigger_auto_reward_configs.find(auto_reward_type);
		if (it == m_trigger_auto_reward_configs.end())
		{
			return 0;
		}
		return it->second;
	}

	const trigger_achievement_reward_t* GetReward(int reward_index, int seq) const
	{
		auto it = m_trigger_achievement_reward_configs.find(reward_index);
		if (it == m_trigger_achievement_reward_configs.end())
		{
			return nullptr;
		}
		if (seq < 0 || seq >= it->second.size())
		{
			return nullptr;
		}
		return &(it->second[seq]);
	}

private:
	std::map<int/*achievement_id*/, int/*reward_index*/> m_trigger_achievement_configs;
	std::map<int/*auto_reward_type*/, int/*reward_index*/> m_trigger_auto_reward_configs;
	std::map<int/*reward_index*/, std::vector<trigger_achievement_reward_t>> m_trigger_achievement_reward_configs;
};

#include "adventuretaskcommon.h"

#define DEFINE_CONFIG_MANAGER(__TYPE__, __CONFIG__, __NAME__) \
class config_template_manager_##__CONFIG__ \
{ \
	typedef abase::hash_map<tid_t, __TYPE__> MAP; \
	MAP _map; \
\
	bool Insert(const __TYPE__& temp) \
	{\
		if(_map.find(temp.tid) != _map.end()) return false; \
		_map[temp.tid] = temp; \
		return true; \
	}\
\
public: \
\
	bool Load(elementdataman& data_man) \
	{\
		for (auto& tmp : CONVEX_MAP(__CONFIG__)) \
		{\
			const __CONFIG__ & ess = *tmp.second.get(); \
			__TYPE__ temp; \
			temp.Load(ess); \
			Insert(temp); \
		} \
		return true; \
	}\
	const __TYPE__ * Get(tid_t tid) const \
	{\
		typename MAP::const_iterator it = _map.find(tid); \
		if(it == _map.end()) return NULL; \
		return &it->second; \
	}\
\
	__TYPE__ * Get(tid_t tid) \
	{\
		typename MAP::iterator it = _map.find(tid);\
		if(it == _map.end()) return NULL; \
		return &it->second; \
	}\
	static config_template_manager_##__CONFIG__& GetInstance() \
	{\
		static config_template_manager_##__CONFIG__ _s_instance; \
		return _s_instance; \
	}\
};\
typedef config_template_manager_##__CONFIG__ __NAME__;

int DebugHotUpdateTemplate(tid_t tid,int space_id,const void* buf,size_t size);

typedef bool each_template_handle(const void* _template);
int InnerForEachTemplate(elementdataman& data_man, int space_id, int dataid, each_template_handle* handle);

#endif

