#include "centerarenaeliminatebattle.h"
#include "eliminategroupmanager.h"
#include "gprotoc/ipt_center_battle_roam_teleport_player.pb.h"
#include "gprotoc/ipt_eliminate_battle_notify.pb.h"
#include "gprotoc/ipt_eliminate_battle_debug.pb.h"
#include "gprotoc/eliminate_group.pb.h"
#include "gprotoc/ipt_center_battle_roam_join_check.pb.h"
#include "gprotoc/corps_battle_info.pb.h"

#include "eliminatebattlemanager.h"
#include "teammanager.h"
#include "global_config.h"
#include "campaignmanager.h"
#include "corps_battle_man.h"
#include "corps_eliminate_battle.h"

bool CenterArenaEliminateBattleClient::Load(elementdataman& data_man)
{
	int eliminate_group_type = GLOBAL_CONFIG.eliminate_group_type;
	LIST_DATA_BEGIN(ID_SPACE_CONFIG, INSTANCE_CONFIG, config)
	{
		if ((eliminate_group_type == EGT_5V5 && config.instance_class == INSTTYPE_ELIMINATE_2)
		        || (eliminate_group_type == EGT_3V3 && config.instance_class == INSTTYPE_ELIMINATE_3v3_2)
		        || (eliminate_group_type == EGT_2V2 && config.instance_class == INSTTYPE_ELIMINATE_2v2_2))
		{
			ASSERT(config.max_player_num && config.max_player_num % 2 == 0);
			ASSERT(!m_team_member_need || (m_team_member_need && m_team_member_need == (unsigned int)config.max_player_num / 2));
			ASSERT(m_battle_activity_map.empty());
			m_battle_tid = config.id;
			m_battle_activity_map.insert(std::make_pair(config.id, config.require_activity_id));
			m_team_member_need = (unsigned int)config.max_player_num / 2;
			LOG_TRACE("CenterArenaEliminateBattleClient::Load add battle inst_tid=%d activity_tid=%d team_member_need=%d eliminate_group_type=%d\n", config.id, config.require_activity_id, m_team_member_need, eliminate_group_type);
		}
	}
	LIST_DATA_END
	return true;
}
bool CenterArenaEliminateBattleClient::Update()
{
	if (!CenterBattleClientStub::Update())
	{
		return false;
	}
	return true;
}
zone_id_t CenterArenaEliminateBattleClient::GetCenterZoneID(int battle_tid) const
{
	std::string service_name = GetServiceName() + "_" + std::to_string(battle_tid);
	return DCENTER.CenterServer(service_name);
}
zone_id_t CenterArenaEliminateBattleClient::GetCenterZoneID() const
{
	return GetCenterZoneID(m_battle_tid);
}
void CenterArenaEliminateBattleClient::OnEliminateBattleNotify(const PB::ipt_eliminate_battle_notify& notify)
{
	LOG_TRACE("CenterArenaEliminateBattleClient::OnEliminateBattleNotify::notify_type=%d", (int)notify.notify_type());

	switch (notify.notify_type())
	{
	case PB::ipt_eliminate_battle_notify::EBNT_STATE:
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnEliminateBattleNotify::EBNT_STATE::state=%d:set=%d",
		          notify.eliminate_battle_state(), notify.eliminate_battle_state_set());

		EliminateBattleManager::GetInstance().SetLocalState(notify.eliminate_battle_state(), notify.eliminate_battle_state_set());
	}
	break;
	default:
		break;
	}

	auto pEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(CORPS_BATTLE_STUB_TYPE_ELIMINATE);
	if (pEntry)
	{
		pEntry->CorpsEliminateBattleNotify(notify);
	}
}
int CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck(ruid_t roleid, int battle_type)
{
	LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d", roleid, battle_type);
	auto *pInfo = RoleMap::Instance().FindOnline(roleid);
	if (!pInfo)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d::PlayerOffline", roleid, battle_type);
		return -1;
	}

	auto state = EliminateBattleManager::GetInstance().GetState();
	if (state != PB::EBS_CENTER_SCORE_BATTLE_BEGIN)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d:state=%d", roleid, battle_type, state);
		return -2;
	}

	int ret = CenterArenaSingleBattleClient::PrePlayerApplyBattleCheck(roleid, battle_type);
	if (ret)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::CenterArenaSingleBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d:ret=%d", roleid, battle_type, ret);
		return ret;
	}

	if (!pInfo->SNSReady() || !pInfo->friends.GetEliminateGroupID())
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d::RoleNoGroup", roleid, battle_type);
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_GROUP;
	}

	if (!pInfo->teamid)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d::RoleNoTeam", roleid, battle_type);
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_TEAM;
	}
	auto pTeam = TeamManager::Instance().FindTeam(pInfo->teamid);
	if (!pTeam)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d:teamid=%u::TeamNotFound", roleid, battle_type, pInfo->teamid);
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d:teamid=%u:teamleader=" PRINT64"::NotTeamLeader",
		          roleid, battle_type, pInfo->teamid, pTeam->GetLeaderID());
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != GetTeamMemberNeed())
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d:teamid=%u:team_size=%u:member_need=%d::NotEnoughMember",
		          roleid, battle_type, pInfo->teamid, team_size, GetTeamMemberNeed());
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NOT_ENOUGH_MEMBER;
	}

	auto group_id = pInfo->friends.GetEliminateGroupID();
	auto pGroup = ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetEliminateGroup(group_id);
	if (!pGroup)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d:teamid=%u:group_id=%ld::NoEliminateGroup", roleid, battle_type, pInfo->teamid, group_id);
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_GROUP;
	}
	int now_time = Timer::GetTime();
	/*int center_match_success_time = pGroup->GetCenterMatchSuccessTime();
	if (pGroup->IsMatching() || (center_match_success_time > 0 && now_time - center_match_success_time <= 600))
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":battle_type=%d:teamid=%u:group_id=%ld:center_match_success_time=%d::IsMatching", roleid, battle_type, pInfo->teamid, group_id, center_match_success_time);
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_IS_MATCHING;
	}*/

	std::map<int, int> prof_set;
	for (const auto& m : pTeam->members)
	{
		auto& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_MEMBER_OFFLINE;
		}

		if (!member->info()->SNSReady() || member->info()->friends.GetEliminateGroupID() != pInfo->friends.GetEliminateGroupID())
		{
			LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":memger_eliminate_group_id=%ld",
			          roleid, pInfo->teamid, member->roleid(), member->info()->friends.GetEliminateGroupID());
			return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NOT_SAME_GROUP;
		}

		auto iter = prof_set.find(member->info()->profession);
		if (iter != prof_set.end() && iter->second >= ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetMaxProfCount())
		{
			LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":profession=%d",
			          roleid, pInfo->teamid, member->roleid(), member->info()->profession);
			if (GLOBAL_CONFIG.eliminate_group_type == EGT_3V3)
			{
				return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_3V3_DUP_PROFESSION;
			}
			else if (GLOBAL_CONFIG.eliminate_group_type == EGT_2V2)
			{
				return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_2V2_DUP_PROFESSION;
			}
			return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_DUP_PROFESSION;
		}
		else if (iter != prof_set.end())
		{
			iter->second ++;
		}
		else
		{
			if (GET_FUNC_SWITCH(kFuncCodeEliminateBattleProf16Limit))
			{
				if (member->info()->profession == PROFTYPE_16)
				{
					LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":prof=%d,ProfForbid", roleid, pInfo->teamid, member->roleid(), member->info()->profession);
					return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF_16;
				}
			}
			if (GET_FUNC_SWITCH(kFuncCodeEliminateBattleProf18Limit))
			{
				if (member->info()->profession == PROFTYPE_18)
				{
					LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":prof=%d,ProfForbid", roleid, pInfo->teamid, member->roleid(), member->info()->profession);
					return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF_18;
				}
			}

			prof_set.insert(std::make_pair(member->info()->profession, 1));
		}

		auto it = m_local_player_entry_map.find(member->roleid());
		if (it != m_local_player_entry_map.end())
		{
			auto& local_player_entry = it->second;
			auto pit = local_player_entry.local_player_punish_map.find(battle_type);
			if (pit != local_player_entry.local_player_punish_map.end() && pit->second > now_time)
			{
				LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":punish_time=%ld:now_time=%d",
				          roleid, pInfo->teamid, member->roleid(), pit->second, now_time);
				return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_PUNISHED;
			}
		}

		if (CenterManager::GetInstance().IsPlayerApplyed(member->roleid(), GetType()))
		{
			LOG_TRACE("CenterArenaEliminateBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":ERROR_ELIMINATE_GROUP_ARENA_BATTLE_ALREADY_MATCHED",
			          roleid, pInfo->teamid, member->roleid());
			return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_ALREADY_MATCHED;
		}
	}
	return 0;
}
//玩家申请加入战场
int CenterArenaEliminateBattleClient::PlayerApplyBattle(ruid_t roleid, int battle_type, int param1, int param2)
{
	LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::roleid=" PRINT64":battle_type=%d:param1=%d:param2=%d", roleid, battle_type, param1, param2);

	if (PrePlayerApplyBattleCheck(roleid, battle_type) != 0)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::PrePlayerApplyBattleCheckFailed:roleid=" PRINT64":battle_type=%d", roleid, battle_type);
		return -1;
	}
	RoleInfo *pInfo = RoleMap::Instance().FindOnline(roleid);
	if (!pInfo)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::roleid=" PRINT64":battle_type=%d::RoleOffline", roleid, battle_type);
		return -1;
	}
	auto pGroup = ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetEliminateGroup(pInfo->friends.GetEliminateGroupID());
	if (!pGroup)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::roleid=" PRINT64":battle_type=%d::GroupNotFound", roleid, battle_type);
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_GROUP;
	}

	if (!pGroup->InCenterScore())
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::roleid=" PRINT64":battle_type=%d:group_id=%ld:GroupNotInCenterScore", roleid, battle_type, pGroup->GetGroupID());
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_CENTER;
	}

	auto pTeam = TeamManager::Instance().FindTeam(pInfo->teamid);
	if (!pTeam)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::roleid=" PRINT64":battle_type=%d:teamid=%u::TeamNotFound", roleid, battle_type, pInfo->teamid);
		return ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_TEAM;
	}

	unsigned int team_size = pTeam->members.size();
	std::vector<ruid_t> team_members;
	for (unsigned int i = 0; i < team_size; ++i)
	{
		auto pMem = pTeam->members[i].Role();
		team_members.push_back(pMem->roleid());
	}

	zone_id_t zoneid = GetCenterZoneID(battle_type);
	if (0 == zoneid)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::roleid=" PRINT64":battle_type=%d:GetCenterZoneIDFailed", roleid, battle_type);
		return -1;
	}
	int now_time = Timer::GetTime();
	for (unsigned int i = 0; i < team_size; ++i)
	{
		auto& player_entry = m_local_player_entry_map[team_members[i]];
		LocalPlayerApplyEntryPtr plpae = std::make_shared<LocalPlayerApplyEntry>();
		player_entry.local_player_apply_entry_map[battle_type] = plpae;
		auto& lpae = *plpae;
		lpae.roleid = team_members[i];
		lpae.battle_type = battle_type;
		lpae.leader_roleid = roleid;
		lpae.apply_time = now_time;
		lpae.check_state_time = now_time;
		if (0 == lpae.center_zoneid)
		{
			lpae.center_zoneid = zoneid;
		}
		else
		{
			zoneid = lpae.center_zoneid;
		}
		auto pMember = RoleMap::Instance().FindOnline(team_members[i]);
		if (pMember)
		{
			pMember->CheckArenaZone();
		}
	}

	Octets data;
	{
		PB::corps_battle_info info;
		info.set_index(0);
		info.set_id(pGroup->GetGroupID());
		info.set_name((const char *)pGroup->GetGroupName().begin(), pGroup->GetGroupName().size());
		info.set_battle_level(0);
		info.set_order_index(0);
		info.set_eliminate_battle_score(ELIMINATE_GROUP_SCORE_BATTLE_INIT_SCORE);
		info.set_total_count(0);
		info.set_win_count(0);
		info.set_consecutive_win_count(0);
		info.set_zoneid(0);
		for (unsigned int i = 0; i < team_size; ++i)
		{
			auto *pPlayer = info.add_eliminate_battle_players();
			pPlayer->mutable_role()->set_id(team_members[i]);
		}
		data = PB2Octets(info);
	}
	ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().SetIsMatching(pInfo->friends.GetEliminateGroupID(), true);
	LOG_TRACE("CenterArenaEliminateBattleClient::PlayerApplyBattle::roleid=" PRINT64":battle_type=%d:group_id=%ld:param1=%d:param2=%d",
	          roleid, battle_type, pGroup->GetGroupID(), param1, param2);
	return SendRoamPlayerApplyCenterBattle(roleid, battle_type, zoneid, team_members, param1, param2, data);
}
void CenterArenaEliminateBattleClient::BattleMatchLog(LocalPlayerApplyEntryPtr lpae, RoleInfo *pInfo, int center_battle_type, int match_result) const
{
	if (!lpae || !pInfo)
	{
		return;
	}
	BI_LOG_GLOBAL(pInfo->account);
	DEFINE_slogger(FORMAT, "center_arena_eliminate_match");
	slogger.BI_HEADER(pInfo->roleid)
	.P("roleid", pInfo->roleid)
	.P("battle_type", center_battle_type)
	.P("match_time", Timer::GetTime() - lpae->apply_time)
	.P("match_result", match_result); // 0 匹配成功, 1 取消
	ruid_t group_id = pInfo->friends.GetEliminateGroupID();
	auto pGroup = ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetEliminateGroup(pInfo->friends.GetEliminateGroupID());
	if (pGroup)
	{
		slogger.P("eliminategroup_id", group_id)
		.P("group_grade", pGroup->GetGroupGrade());
	}
}
int CenterArenaEliminateBattleClient::OnJoinCheck(ruid_t roleid, zone_id_t zoneid, const PB::ipt_center_battle_roam_join_check& proto)
{
	LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64"::center_battle_type=%d:battle_type=%d:zoneid=%d",
	          roleid, proto.center_battle_type(), proto.battle_type(), zoneid);

	auto it = m_local_player_entry_map.find(roleid);
	if (it == m_local_player_entry_map.end())
	{
		return -1;
	}
	auto tit = it->second.local_player_apply_entry_map.find(proto.battle_type());
	if (tit == it->second.local_player_apply_entry_map.end())
	{
		return -1;
	}
	LocalPlayerApplyEntry& lpae = *(tit->second);
	lpae.center_zoneid = zoneid;

	auto *pInfo = RoleMap::Instance().FindOnline(roleid);
	if (!pInfo || !pInfo->SNSReady() || pInfo->friends.GetEliminateGroupID() == 0)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64"::playerNotReady", roleid);
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, -1);
		return -1;
	}

	if (!pInfo->teamid)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64"::ERROR_CENTER_ARENA_NO_TEAM", roleid);
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pInfo->teamid);
	if (!pTeam)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64":teamid=%d::ERROR_CENTER_ARENA_NO_TEAM", roleid, pInfo->teamid);
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64":teamid=%d:leader=" PRINT64":ERROR_CENTER_ARENA_NO_TEAM",
		          roleid, pInfo->teamid, pTeam->GetLeaderID());
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64":teamid=%d::ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER", roleid, pInfo->teamid);
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER);
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	int now_time = Timer::GetTime();
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_MEMBER_NOT_AROUND);
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (!member->info()->SNSReady() || member->info()->friends.GetEliminateGroupID() != pInfo->friends.GetEliminateGroupID())
		{
			LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64":teamid=%d::member=" PRINT64":ERROR_CENTER_ARENA_MEMBER_NOT_AROUND",
			          roleid, pInfo->teamid, member->roleid());
			SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_MEMBER_NOT_AROUND);
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}

		auto it = m_local_player_entry_map.find(member->roleid());
		if (it != m_local_player_entry_map.end())
		{
			LocalPlayerEntry& local_player_entry = it->second;
			LOCAL_PLAYER_PUNISH_MAP::iterator pit = local_player_entry.local_player_punish_map.find(proto.battle_type());
			if (pit != local_player_entry.local_player_punish_map.end() && pit->second > now_time)
			{
				LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64":teamid=%d::member=" PRINT64":ERROR_CENTER_BATTLE_PUNISHED",
				          roleid, pInfo->teamid, member->roleid());
				SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_BATTLE_PUNISHED);
				return ERROR_CENTER_BATTLE_PUNISHED;
			}
			auto tit = local_player_entry.local_player_apply_entry_map.find(proto.battle_type());
			if (tit == local_player_entry.local_player_apply_entry_map.end())
			{
				LOG_TRACE("CenterArenaEliminateBattleClient::OnJoinCheck::roleid=" PRINT64":teamid=%d::member=" PRINT64":ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER",
				          roleid, pInfo->teamid, member->roleid());
				SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER);
				return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
			}
			LocalPlayerApplyEntry& member_lpae = *(tit->second);
			member_lpae.center_zoneid = zoneid;
		}
	}

	pInfo->SendMessage2GS(proto);
	BattleMatchLog(tit->second, pInfo, proto.center_battle_type(), 0 /*匹配成功*/);
	if (GetType() == CBT_ARENA_ELIMINATE)
	{
		ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().SetCenterMatchSuccessTime(pInfo->friends.GetEliminateGroupID(), Timer::GetTime());
	}
	return 0;
}
int CenterArenaEliminateBattleClient::OnTryTeleportPlayer(ruid_t roleid, PB::ipt_center_battle_roam_teleport_player& proto)
{
	LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64 ":center_battle_type=%d", roleid, proto.center_battle_type());
	RoleInfo *pInfo = RoleMap::Instance().FindOnline(roleid);
	if (!pInfo || pInfo->friends.GetEliminateGroupID() == 0)
	{
		return -1;
	}

	if (!pInfo->teamid)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64 ":ERROR_CENTER_ARENA_NO_TEAM", roleid);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pInfo->teamid);
	if (!pTeam)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64":ERROR_CENTER_ARENA_NO_TEAM", roleid);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64":ERROR_CENTER_ARENA_NOT_LEADER", roleid);
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64":ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER", roleid);
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline())
		{
			LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64":ERROR_CENTER_ARENA_MEMBER_NOT_AROUND", roleid);
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (member->info()->friends.GetEliminateGroupID() != pInfo->friends.GetEliminateGroupID())
		{
			LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64":member=" PRINT64":ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP", roleid, member->roleid());
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}

		auto it = m_local_player_entry_map.find(member->roleid());
		if (it == m_local_player_entry_map.end())
		{
			LOG_TRACE("CenterArenaEliminateBattleClient::OnTryTeleportPlayer::roleid=" PRINT64":member=" PRINT64":ERROR_CENTER_ARENA_MEMBER_NOT_AROUND", roleid, member->roleid());
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}
	}
	proto.set_center_battle_type(GetType());

	//给玩家所在gs发消息
	pInfo->SendMessage2GS(proto);
	return 0;

}
void CenterArenaEliminateBattleClient::DebugSetForceCenterZoneID(int battle_type, int zoneid)
{
	LOG_TRACE("CenterArenaEliminateBattleClient::DebugSetForceCenterZoneID::battle_type=%d:zoneid=%d", battle_type, zoneid);
	PB::ipt_eliminate_battle_debug proto;
	proto.set_debug_type(PB::ipt_eliminate_battle_debug::EBDT_FORCE_ZONE);
	proto.set_param1(zoneid);
	CenterManager::GetInstance().SendMessage2Hub(proto, 0, GetServiceName(), GetCenterZoneID());
}
