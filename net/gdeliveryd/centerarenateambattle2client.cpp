#include "centerarenateambattle.h"
#include "centerarenateambattle2.h"
#include "centermanager.h"
#include "teammanager.h"
#include "arenagroupmanager.h"
#include "gprotoc/ipt_center_battle_roam_teleport_player.pb.h"
#include "gprotoc/ipt_center_battle_roam_join_check.pb.h"
#include "gprotoc/ipt_center_battle_group_get_player_status.pb.h"
#include "gprotoc/ipt_center_battle_debug_set_max_player_num.pb.h"
#include "gprotoc/npt_center_battle_join_answer.pb.h"
#include "gprotoc/ipt_center_battle_group_get_member_status.pb.h"
#include "gprotoc/npt_center_battle_join_answer_re.pb.h"

#include "funcswitchmgr.h"
#include "global_config.h"
#include "gdeliveryserver.hpp"
#include "gproviderserver.hpp"

//初始化支持的战场信息
bool CenterArenaTeamBattle2Client::Load(elementdataman& data_man)
{
	LIST_DATA_BEGIN(ID_SPACE_CONFIG, INSTANCE_CONFIG, config)
	{
		if (config.instance_class == INSTTYPE_CENTER_ARENA_TEAM_BATTLE_NEW)
		{
			ASSERT(config.max_player_num && config.max_player_num % 2 == 0);
			ASSERT(!m_team_member_need || (m_team_member_need && m_team_member_need == (unsigned int)config.max_player_num / 2));
			ASSERT(m_battle_activity_map.empty());
			m_battle_tid = config.id;
			m_battle_activity_map.insert(std::make_pair(config.id, config.require_activity_id));
			m_team_member_need = (unsigned int)config.max_player_num / 2;
			LOG_TRACE("CenterArenaTeamBattle2Client::Load add battle inst_tid=%d activity_tid=%d team_member_need=%d\n",
			          config.id, config.require_activity_id, m_team_member_need);
		}
	}
	LIST_DATA_END
	return true;
}

int CenterArenaTeamBattle2Client::PrePlayerApplyBattleCheck(ruid_t roleid, int battle_type)
{
	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo)
	{
		return -1;
	}
	int ret = CenterArenaSingleBattleClient::PrePlayerApplyBattleCheck(roleid, battle_type);
	if (ret)
	{
		return ret;
	}

	if (!pinfo->SNSReady() || !pinfo->friends.GetArenaGroup().id)
	{
		return ERROR_ARENA_GROUP_NOT_IN_GROUP;
	}

	const ARENA_GROUP_MANAGER::ArenaGroup *pArenaGroup = ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().GetArenaGroup(pinfo->friends.GetArenaGroup().id);
	if (!pArenaGroup)
	{
		return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
	}

	if (!pinfo->teamid)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	std::map<int, int> prof_set;
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (!member->info()->SNSReady() || member->info()->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}
		if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf10Limit) && member->info()->profession == PROFTYPE_10)
		{
			return ERROR_CENTER_ARENA_PROF_10_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf12Limit) && member->info()->profession == PROFTYPE_12)
		{
			return ERROR_CENTER_ARENA_PROF_12_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf13Limit) && member->info()->profession == PROFTYPE_13)
		{
			return ERROR_CENTER_ARENA_PROF_13_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf14Limit) && member->info()->profession == PROFTYPE_14)
		{
			return ERROR_CENTER_ARENA_PROF_14_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf15Limit) && member->info()->profession == PROFTYPE_15)
		{
			return ERROR_CENTER_ARENA_PROF_15_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf16Limit) && member->info()->profession == PROFTYPE_16)
		{
			return ERROR_CENTER_ARENA_PROF_16_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf17Limit) && member->info()->profession == PROFTYPE_17)
		{
			return ERROR_CENTER_ARENA_PROF_17_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf18Limit) && member->info()->profession == PROFTYPE_18)
		{
			return ERROR_CENTER_ARENA_PROF_18_LIMIT;
		}
		int max_prof_count = 2;
		int dup_prof_err_code = ERROR_ELIMINATE_GROUP_ARENA_BATTLE_DUP_PROFESSION;
		auto iter = prof_set.find(member->info()->profession);
		if (iter != prof_set.end() && iter->second >= max_prof_count)
		{
			LOG_TRACE("CenterArenaTeamBattle2Client::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":profession=%d:center_battle_type=%d",
			          roleid, pinfo->teamid, member->roleid(), member->info()->profession, GetType());
			return dup_prof_err_code;
		}
		else if (iter != prof_set.end())
		{
			iter->second ++;
		}
		else
		{
			prof_set.insert(std::make_pair(member->info()->profession, 1));
		}
		auto it = m_local_player_entry_map.find(member->roleid());
		if (it != m_local_player_entry_map.end())
		{
			LocalPlayerEntry& local_player_entry = it->second;
			LOCAL_PLAYER_PUNISH_MAP::iterator pit = local_player_entry.local_player_punish_map.find(battle_type);
			if (pit != local_player_entry.local_player_punish_map.end() && pit->second > Timer::GetTime())
			{
				return ERROR_CENTER_BATTLE_PUNISHED;
			}
		}
		if (CenterManager::GetInstance().IsPlayerApplyed(pinfo->roleid, GetType()))
		{
			return ERROR_CENTER_BATTLE_OTHER_ALREADY_MATCHED;
		}
	}
	return 0;
}

int CenterArenaTeamBattle2Client::PlayerApplyBattle(ruid_t roleid, int battle_type, int param1, int param2)
{
	//再判一次玩家报名条件
	if (PrePlayerApplyBattleCheck(roleid, battle_type) != 0)
	{
		return -1;
	}

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->SNSReady() || pinfo->friends.GetArenaGroup().id == 0)
	{
		return -1;
	}

	if (!pinfo->teamid)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	ARENA_GROUP_MANAGER::ArenaGroup *pArenaGroup = ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().GetArenaGroup(pinfo->friends.GetArenaGroup().id);
	if (!pArenaGroup)
	{
		return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
	}

	unsigned int team_size = pTeam->members.size();
	std::vector<ruid_t> team_members;
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		team_members.push_back(member->roleid());
	}
	zone_id_t zoneid = g_zoneid;
	if (0 == zoneid)
	{
		return -1;
	}
	for (unsigned int i = 0; i < team_size; ++ i)
	{
		LocalPlayerEntry& player_entry = m_local_player_entry_map[team_members[i]];
		LocalPlayerApplyEntryPtr plpae = std::make_shared<LocalPlayerApplyEntry>();
		player_entry.local_player_apply_entry_map[battle_type] = plpae;
		LocalPlayerApplyEntry& lpae = *plpae;
		lpae.roleid = team_members[i];
		lpae.battle_type = battle_type;
		lpae.leader_roleid = roleid;
		//lpae.check_state_time_list_iterator = m_check_state_time_list.end();
		lpae.apply_time = Timer::GetTime();
		lpae.check_state_time = lpae.apply_time;
		if (0 == lpae.center_zoneid)
		{
			lpae.center_zoneid = zoneid;
		}
		else
		{
			zoneid = lpae.center_zoneid;
		}

		RoleInfo *pMember = RoleMap::Instance().FindOnline(team_members[i]);
		if (!pMember || !pMember->SNSReady() || pMember->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}

		pMember->CheckArenaZone();
	}

	//如果连续失败2场及以上，要放到败者局里，匹配机器人
	if (m_debug_force_robot)
	{
		//强制匹配机器人
		param1 = 0;
		param2 = pinfo->level;
	}
	else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaTeamBattleRobot))
	{
		//连续失败2场以后，就匹配机器人
		if (pArenaGroup->GetArenaGroupPBInfo().consecutive_lose() >= 2 && pArenaGroup->GetGArenaGroup().group_grade <= CENTER_ARENA_TEAM_BATTLE_ROBOT_GRADE_MAX)
		{
			param1 = 0;
			param2 = pinfo->level;
		}
		else
		{
			param1 = 1;
			param2 = pArenaGroup->GetGArenaGroup().group_grade;
		}
	}
	else
	{
		param1 = 1;
		param2 = pArenaGroup->GetGArenaGroup().group_grade;
	}

	return SendRoamPlayerApplyCenterBattle(roleid, battle_type, zoneid, team_members, param1, param2);
}

int CenterArenaTeamBattle2Client::OnJoinCheck(ruid_t roleid, zone_id_t zoneid, const PB::ipt_center_battle_roam_join_check& proto)
{
	LOG_TRACE("CenterArenaTeamBattle2Client::OnJoinCheck roleid:" PRINT64 " zoneid:%d center_battle_type:%d battle_type:%d",
	          roleid, zoneid, proto.center_battle_type(), proto.battle_type());

	auto it = m_local_player_entry_map.find(roleid);
	if (it == m_local_player_entry_map.end())
	{
		return -1;
	}
	auto tit = it->second.local_player_apply_entry_map.find(proto.battle_type());
	if (tit == it->second.local_player_apply_entry_map.end())
	{
		return -1;
	}
	LocalPlayerApplyEntry& lpae = *(tit->second);
	lpae.center_zoneid = zoneid;

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->SNSReady() || pinfo->friends.GetArenaGroup().id == 0)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, -1);
		return -1;
	}

	if (!pinfo->teamid)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER);
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_MEMBER_NOT_AROUND);
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (!member->info()->SNSReady() || member->info()->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_MEMBER_NOT_AROUND);
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}

		auto it = m_local_player_entry_map.find(member->roleid());
		if (it != m_local_player_entry_map.end())
		{
			LocalPlayerEntry& local_player_entry = it->second;
			LOCAL_PLAYER_PUNISH_MAP::iterator pit = local_player_entry.local_player_punish_map.find(proto.battle_type());
			if (pit != local_player_entry.local_player_punish_map.end() && pit->second > Timer::GetTime())
			{
				SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_BATTLE_PUNISHED);
				return ERROR_CENTER_BATTLE_PUNISHED;
			}
			auto tit = local_player_entry.local_player_apply_entry_map.find(proto.battle_type());
			if (tit == local_player_entry.local_player_apply_entry_map.end())
			{
				SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER);
				return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
			}
			LocalPlayerApplyEntry& member_lpae = *(tit->second);
			member_lpae.center_zoneid = zoneid;
		}
	}

	//给玩家所在gs发消息
	pinfo->SendMessage2GS(proto);
	return 0;
}

//收到中心服请求让玩家跨服
int CenterArenaTeamBattle2Client::OnTryTeleportPlayer(ruid_t roleid, PB::ipt_center_battle_roam_teleport_player& proto)
{
	LOG_TRACE("CenterArenaTeamBattle2Client::OnTryTeleportPlayer roleid:" PRINT64 " center_battle_type:%d\n",
	          roleid, proto.center_battle_type());

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || pinfo->friends.GetArenaGroup().id == 0)
	{
		return -1;
	}

	if (!pinfo->teamid)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline())
		{
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (member->info()->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}

		auto it = m_local_player_entry_map.find(member->roleid());
		if (it == m_local_player_entry_map.end())
		{
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}
	}

	proto.set_center_battle_type(GetType());

	//给玩家所在gs发消息
	pinfo->SendMessage2GS(proto);
	return 0;
}

void CenterArenaTeamBattle2Client::OnPlayerChangeScene(ruid_t roleid, int world_tid)
{
	LOG_TRACE("CenterArenaTeamBattle2Client::OnPlayerChangeScene center_battle_type:%d roleid:%ld world_tid:%d\n",
	          GetType(), roleid, world_tid);

	/*RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->teamid)
		return;

	TeamInfo* pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
		return;

	PlayerQuitBattle(pTeam->GetLeaderID(), 0, QBT_C2S_BY_CLIENT);*/

	if (m_battle_tid == world_tid)
	{
		return;
	}

	PlayerQuitBattle(roleid, 0, QBT_C2S_BY_CLIENT);
}

void CenterArenaTeamBattle2Client::PlayerQuitBattle(ruid_t roleid, int battle_type, int tag)
{
	LOG_TRACE("CenterArenaTeamBattle2Client::PlayerQuitBattle roleid:" PRINT64 " center_battle_type:%d battle_type:%d tag:%d\n",
	          roleid, GetType(), battle_type, tag);

	LOCAL_PLAYER_ENTRY_MAP::iterator it = m_local_player_entry_map.find(roleid);
	if (it == m_local_player_entry_map.end())
	{
		return;
	}
	LocalPlayerEntry& lpe = it->second;

	if (battle_type != 0)
	{
		LOCAL_PLAYER_APPLY_ENTRY_MAP::iterator tit = lpe.local_player_apply_entry_map.find(battle_type);
		if (tit == lpe.local_player_apply_entry_map.end())
		{
			return;
		}

		//找到对应队长的信息
		LOCAL_PLAYER_ENTRY_MAP::iterator leader_it = m_local_player_entry_map.find(tit->second->leader_roleid);
		if (leader_it == m_local_player_entry_map.end())
		{
			return;
		}
		LocalPlayerEntry& leader_lpe = leader_it->second;
		LOCAL_PLAYER_APPLY_ENTRY_MAP::iterator leader_tit = leader_lpe.local_player_apply_entry_map.find(battle_type);
		if (leader_tit == leader_lpe.local_player_apply_entry_map.end())
		{
			return;
		}

		if (leader_tit->second->send_apply)
		{
			//请求已经发送过了，那就需要去中心服取消了
			SendRoamCenterBattleQuit(roleid, battle_type, leader_tit->second->center_zoneid, tag);
		}
		else
		{
			//标记一下自己已经退出，请求消息一会就不用再发了
			leader_tit->second->quit = true;
		}
	}
	else
	{
		if (lpe.local_player_apply_entry_map.empty())
		{
			return;
		}
		for (auto tit = lpe.local_player_apply_entry_map.begin(), teit = lpe.local_player_apply_entry_map.end(); tit != teit; ++ tit)
		{
			//找到对应队长的信息
			LOCAL_PLAYER_ENTRY_MAP::iterator leader_it = m_local_player_entry_map.find(tit->second->leader_roleid);
			if (leader_it == m_local_player_entry_map.end())
			{
				continue;
			}
			LocalPlayerEntry& leader_lpe = leader_it->second;
			LOCAL_PLAYER_APPLY_ENTRY_MAP::iterator leader_tit = leader_lpe.local_player_apply_entry_map.find(tit->first);
			if (leader_tit == leader_lpe.local_player_apply_entry_map.end())
			{
				continue;
			}

			if (leader_tit->second->send_apply)
			{
				//求已经发送过了，那就需要去中心服取消了
				SendRoamCenterBattleQuit(roleid, battle_type, leader_tit->second->center_zoneid, tag);
			}
			else
			{
				//标记一下自己已经退出，请求消息一会就不用再发了
				leader_tit->second->quit = true;
			}
		}
	}
}

void CenterArenaTeamBattle2Client::OnLocalJoinAnswer(ruid_t roleid, PB::npt_center_battle_join_answer& proto)
{
	LOG_TRACE("CenterArenaTeamBattle2Client::OnLocalJoinAnswer roleid:" PRINT64 " center_battle_type:%d battle_type:%d agree:%d\n",
	          roleid, proto.center_battle_type(), proto.battle_type(), proto.agree());

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->teamid)
	{
		return;
	}
	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return;
	}
	auto it = m_local_player_entry_map.find(roleid);
	if (it == m_local_player_entry_map.end())
	{
		return;
	}
	LocalPlayerEntry& local_player_entry = it->second;
	auto tit = local_player_entry.local_player_apply_entry_map.find(m_battle_tid);
	if (tit == local_player_entry.local_player_apply_entry_map.end())
	{
		return;
	}
	LocalPlayerApplyEntry& lpae = *(tit->second);

	CenterBattleClientStub::OnLocalJoinAnswer(roleid, proto);

	PB::npt_center_battle_join_answer_re re;
	re.set_ask_time(lpae.join_ask_time);
	re.set_center_battle_type(GetType());
	re.set_battle_type(proto.battle_type());
	auto *pAnswer = re.add_infos();
	pAnswer->set_roleid(roleid);
	pAnswer->set_agree(proto.agree());

	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			continue;
		}
		member->info()->SendMessage2Client(re);
	}
}

void CenterArenaTeamBattle2Client::OnPlayerLogin(RoleInfo *pinfo)
{
	CenterBattleClientStub::OnPlayerLogin(pinfo);

	auto it = m_local_player_entry_map.find(pinfo->roleid);
	if (it == m_local_player_entry_map.end())
	{
		return;
	}
	LocalPlayerEntry& local_player_entry = it->second;
	auto tit = local_player_entry.local_player_apply_entry_map.find(m_battle_tid);
	if (tit == local_player_entry.local_player_apply_entry_map.end())
	{
		return;
	}
	LocalPlayerApplyEntry& lpae = *(tit->second);
	PB::npt_center_battle_join_answer_re re;
	re.set_ask_time(lpae.join_ask_time);

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return;
	}

	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->info())
		{
			continue;
		}

		auto *pAnswer = re.add_infos();
		pAnswer->set_roleid(member->roleid());
		pAnswer->set_agree(-1);

		auto mit = m_local_player_entry_map.find(member->roleid());
		if (mit != m_local_player_entry_map.end())
		{
			LocalPlayerEntry& member_local_player_entry = mit->second;
			auto mtit = member_local_player_entry.local_player_apply_entry_map.find(m_battle_tid);
			if (mtit != member_local_player_entry.local_player_apply_entry_map.end())
			{
				LocalPlayerApplyEntry& member_lpae = *(mtit->second);
				pAnswer->set_agree(member_lpae.join_answer);
			}
		}
	}

	pinfo->SendMessage2Client(re);
	LOG_TRACE("CenterArenaTeamBattle2Client::OnPlayerLogin::roleid=" PRINT64":center_battle_type=%d", pinfo->roleid, GetType());
}

void CenterArenaTeamBattle2Client::DebugSetMaxPlayerNum(int battle_type, int max_player_num)
{
	LOG_TRACE("CenterArenaTeamBattle2Client::DebugSetMaxPlayerNum center_battle_type:%d battle_type:%d max_player_num:%d\n",
	          GetType(), battle_type, max_player_num);

	m_team_member_need = (unsigned int)max_player_num / 2;

	//PB::ipt_center_battle_debug_set_max_player_num pb;
	//pb.set_center_battle_type(GetType());
	//pb.set_battle_type(battle_type);
	//pb.set_max_player_num(max_player_num);

	//CenterManager::GetInstance().SendMessage2Hub(pb, 0, GetServiceName(), g_zoneid);
}

void CenterArenaTeamBattle2Client::DebugForceRobot(bool force)
{
	LOG_TRACE("CenterArenaTeamBattle2Client::DebugForceRobot force=%d\n", (int)force);

	m_debug_force_robot = force;
}

bool CenterArenaTeamBattle2Client::Update()
{
	if (!CenterBattleClientStub::Update())
	{
		return false;
	}

	return true;
}

void CenterArenaTeamBattle2Client::TryGetPlayerStatus(int diaoxiang_id, ruid_t roleid)
{
	//zone_id_t center_zone_id = g_zoneid;
	//if (0 == center_zone_id)
	//{
	//	return;
	//}

	//PB::ipt_center_battle_sync_info pb;
	//pb.set_center_battle_type(GetType());
	//pb.set_info_type(PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_CLIENT_GET_PLAYER_STATUS);
	//pb.set_get_player_status_roleid(roleid);
	//pb.set_get_player_status_diaoxiang_id(diaoxiang_id);

	//CenterManager::GetInstance().SendMessage2Hub(pb, 0, GetServiceName(), center_zone_id);
}

