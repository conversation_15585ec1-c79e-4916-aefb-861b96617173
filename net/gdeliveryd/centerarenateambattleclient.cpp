#include "centerarenateambattle.h"
#include "centermanager.h"
#include "gprotoc/ipt_center_battle_roam_teleport_player.pb.h"
#include "gprotoc/db_arena_group_ranks.pb.h"
#include "gprotoc/ipt_center_battle_sync_info.pb.h"
#include "gprotoc/db_center_arena_team.pb.h"
#include "gprotoc/ipt_center_battle_debug.pb.h"
#include "gprotoc/eliminate_group.pb.h"
#include "gprotoc/ipt_center_battle_roam_join_check.pb.h"
#include "gprotoc/npt_arena_group_global_rank_info.pb.h"
#include "gprotoc/ipt_center_battle_group_get_player_status.pb.h"
#include "gprotoc/ipt_center_battle_debug_set_max_player_num.pb.h"
#include "gprotoc/npt_center_battle_join_answer.pb.h"
#include "gprotoc/ipt_center_battle_group_get_member_status.pb.h"
#include "gprotoc/npt_center_battle_join_answer_re.pb.h"
#include "gprotoc/toplist_addon_data_t.pb.h"

#include "teammanager.h"
#include "campaignmanager.h"
#include "arenagroupmanager.h"
#include "tpmanager.h"
#include "diaoxiang_manager.h"
#include "funcswitchmgr.h"
#include "global_config.h"
#include "gdeliveryserver.hpp"
#include "gproviderserver.hpp"
#include "eliminategroupmanager.h"

extern std::vector<unsigned short> g_support_zone;

void CenterArenaTeamBattleClient::BattleMatchLog(LocalPlayerApplyEntryPtr lpae, RoleInfo *pinfo, int center_battle_type, int match_result) const
{
	if (!lpae || !pinfo)
	{
		return;
	}
	//for BI
	BI_LOG_GLOBAL(pinfo->account);
	DEFINE_slogger(FORMAT, "arena_group_match");
	slogger.BI_HEADER(pinfo->roleid)
	.P("roleid", pinfo->roleid)
	.P("battle_type", center_battle_type)
	.P("match_time", Timer::GetTime() - lpae->apply_time)
	.P("match_result", match_result); // 0 匹配成功, 1 取消
	if (center_battle_type == CBT_ARENA_TEAM)
	{
		ruid_t arenagroup_id = pinfo->friends.GetArenaGroup().id;
		const auto *pGroup = ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().GetArenaGroup(arenagroup_id);
		if (pGroup)
		{
			float win_rate = 0;
			if (pGroup->GetGArenaGroup().win_count > 0 || pGroup->GetGArenaGroup().lose_count > 0)
			{
				win_rate = pGroup->GetGArenaGroup().win_count * 1.0f / (pGroup->GetGArenaGroup().win_count + pGroup->GetGArenaGroup().lose_count);
			}
			slogger.P("arenagroup_id", arenagroup_id)
			.P("group_grade", pGroup->GetGArenaGroup().group_grade)
			.P("arena_zone", pinfo->GetReputation(REPUID_ARENA_ZONE))
			.P("win_rate", win_rate);
		}
	}
}


//初始化支持的战场信息
bool CenterArenaTeamBattleClient::Load(elementdataman& data_man)
{
	LIST_DATA_BEGIN(ID_SPACE_CONFIG, INSTANCE_CONFIG, config)
	{
		if (config.instance_class == INSTTYPE_CENTER_ARENA_TEAM_BATTLE)
		{
			ASSERT(config.max_player_num && config.max_player_num % 2 == 0);
			ASSERT(!m_team_member_need || (m_team_member_need && m_team_member_need == (unsigned int)config.max_player_num / 2));
			ASSERT(m_battle_activity_map.empty());
			m_battle_tid = config.id;
			m_battle_activity_map.insert(std::make_pair(config.id, config.require_activity_id));
			m_team_member_need = (unsigned int)config.max_player_num / 2;
			LOG_TRACE("CenterArenaTeamBattleClient::Load add battle inst_tid=%d activity_tid=%d team_member_need=%d\n",
			          config.id, config.require_activity_id, m_team_member_need);
		}
	}
	LIST_DATA_END
	return true;
}

int CenterArenaTeamBattleClient::PrePlayerApplyBattleCheck(ruid_t roleid, int battle_type)
{
	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo)
	{
		return -1;
	}
	int ret = CenterArenaSingleBattleClient::PrePlayerApplyBattleCheck(roleid, battle_type);
	if (ret)
	{
		return ret;
	}

	if (!pinfo->SNSReady() || !pinfo->friends.GetArenaGroup().id)
	{
		return ERROR_ARENA_GROUP_NOT_IN_GROUP;
	}

	const ARENA_GROUP_MANAGER::ArenaGroup *pArenaGroup = ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().GetArenaGroup(pinfo->friends.GetArenaGroup().id);
	if (!pArenaGroup)
	{
		return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
	}

	if (!pinfo->teamid)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	std::map<int, int> prof_set;
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (!member->info()->SNSReady() || member->info()->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}
		if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf10Limit) && member->info()->profession == PROFTYPE_10)
		{
			return ERROR_CENTER_ARENA_PROF_10_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf12Limit) && member->info()->profession == PROFTYPE_12)
		{
			return ERROR_CENTER_ARENA_PROF_12_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf13Limit) && member->info()->profession == PROFTYPE_13)
		{
			return ERROR_CENTER_ARENA_PROF_13_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf14Limit) && member->info()->profession == PROFTYPE_14)
		{
			return ERROR_CENTER_ARENA_PROF_14_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf15Limit) && member->info()->profession == PROFTYPE_15)
		{
			return ERROR_CENTER_ARENA_PROF_15_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf16Limit) && member->info()->profession == PROFTYPE_16)
		{
			return ERROR_CENTER_ARENA_PROF_16_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf17Limit) && member->info()->profession == PROFTYPE_17)
		{
			return ERROR_CENTER_ARENA_PROF_17_LIMIT;
		}
		else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaProf18Limit) && member->info()->profession == PROFTYPE_18)
		{
			return ERROR_CENTER_ARENA_PROF_18_LIMIT;
		}
		int max_prof_count = 2;
		int dup_prof_err_code = ERROR_ELIMINATE_GROUP_ARENA_BATTLE_DUP_PROFESSION;
		if (GetType() == CBT_ARENA_ELIMINATE)
		{
			max_prof_count = ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetMaxProfCount();
			if (GLOBAL_CONFIG.eliminate_group_type == EGT_3V3)
			{
				dup_prof_err_code = ERROR_ELIMINATE_GROUP_ARENA_BATTLE_3V3_DUP_PROFESSION;
			}
			else if (GLOBAL_CONFIG.eliminate_group_type == EGT_2V2)
			{
				dup_prof_err_code = ERROR_ELIMINATE_GROUP_ARENA_BATTLE_2V2_DUP_PROFESSION;
			}
		}
		auto iter = prof_set.find(member->info()->profession);
		if (iter != prof_set.end() && iter->second >= max_prof_count)
		{
			LOG_TRACE("CenterArenaTeamBattleClient::PrePlayerApplyBattleCheck::roleid=" PRINT64":teamid=%u:member=" PRINT64":profession=%d:center_battle_type=%d", roleid, pinfo->teamid, member->roleid(), member->info()->profession, GetType());
			return dup_prof_err_code;
		}
		else if (iter != prof_set.end())
		{
			iter->second ++;
		}
		else
		{
			prof_set.insert(std::make_pair(member->info()->profession, 1));
		}
		auto it = m_local_player_entry_map.find(member->roleid());
		if (it != m_local_player_entry_map.end())
		{
			LocalPlayerEntry& local_player_entry = it->second;
			LOCAL_PLAYER_PUNISH_MAP::iterator pit = local_player_entry.local_player_punish_map.find(battle_type);
			if (pit != local_player_entry.local_player_punish_map.end() && pit->second > Timer::GetTime())
			{
				return ERROR_CENTER_BATTLE_PUNISHED;
			}
		}
		if (CenterManager::GetInstance().IsPlayerApplyed(pinfo->roleid, GetType()))
		{
			return ERROR_CENTER_BATTLE_OTHER_ALREADY_MATCHED;
		}
	}
	return 0;
}

int CenterArenaTeamBattleClient::PlayerApplyBattle(ruid_t roleid, int battle_type, int param1, int param2)
{
	//再判一次玩家报名条件
	if (PrePlayerApplyBattleCheck(roleid, battle_type) != 0)
	{
		return -1;
	}

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->SNSReady() || pinfo->friends.GetArenaGroup().id == 0)
	{
		return -1;
	}

	if (!pinfo->teamid)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	ARENA_GROUP_MANAGER::ArenaGroup *pArenaGroup = ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().GetArenaGroup(pinfo->friends.GetArenaGroup().id);
	if (!pArenaGroup)
	{
		return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
	}

	auto it = m_zones.find(g_zoneid);
	if (it == m_zones.end())
	{
		GLog::log(LOG_ERR, "CenterArenaTeamBattleClient::PlayerApplyBattle no arenazone. roleid=%ld zoneid=%d", roleid, g_zoneid);
		return -1;
	}

	unsigned int team_size = pTeam->members.size();
	std::vector<ruid_t> team_members;
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		team_members.push_back(member->roleid());
	}
	zone_id_t zoneid = GetCenterZoneID(battle_type);
	if (0 == zoneid)
	{
		return -1;
	}
	for (unsigned int i = 0; i < team_size; ++ i)
	{
		LocalPlayerEntry& player_entry = m_local_player_entry_map[team_members[i]];
		LocalPlayerApplyEntryPtr plpae = std::make_shared<LocalPlayerApplyEntry>();
		player_entry.local_player_apply_entry_map[battle_type] = plpae;
		LocalPlayerApplyEntry& lpae = *plpae;
		lpae.roleid = team_members[i];
		lpae.battle_type = battle_type;
		lpae.leader_roleid = roleid;
		//lpae.check_state_time_list_iterator = m_check_state_time_list.end();
		lpae.apply_time = Timer::GetTime();
		lpae.check_state_time = lpae.apply_time;
		if (0 == lpae.center_zoneid)
		{
			lpae.center_zoneid = zoneid;
		}
		else
		{
			zoneid = lpae.center_zoneid;
		}

		RoleInfo *pMember = RoleMap::Instance().FindOnline(team_members[i]);
		if (!pMember || !pMember->SNSReady() || pMember->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}
		pMember->CheckArenaZone();
	}

	pArenaGroup->CheckRefreshArenaZone(ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().GetGroupGradeTimeStamp());

	//如果连续失败3场及以上，要放到败者局里，匹配机器人
	if (m_debug_force_robot)
	{
		//强制匹配机器人
		param1 = 0;
		param2 = pinfo->level;
	}
	else if (GET_FUNC_SWITCH(kFuncCodeCenterArenaTeamBattleRobot))
	{
		//连续失败3场以后，就匹配机器人
		if (pArenaGroup->GetArenaGroupPBInfo().consecutive_lose() >= 3 && pArenaGroup->GetGArenaGroup().group_grade <= CENTER_ARENA_TEAM_BATTLE_ROBOT_GRADE_MAX)
		{
			param1 = 0;
			param2 = pinfo->level;
		}
		else
		{
			param1 = pArenaGroup->GetArenaGroupPBInfo().arena_zone();
			param2 = pArenaGroup->GetArenaGroupPBInfo().arena_zone();
		}
	}
	else
	{
		param1 = pArenaGroup->GetArenaGroupPBInfo().arena_zone();
		param2 = pArenaGroup->GetGArenaGroup().group_grade;
	}

	return SendRoamPlayerApplyCenterBattle(roleid, battle_type, zoneid, team_members, param1, param2);
}

int CenterArenaTeamBattleClient::OnJoinCheck(ruid_t roleid, zone_id_t zoneid, const PB::ipt_center_battle_roam_join_check& proto)
{
	LOG_TRACE("CenterArenaTeamBattleClient::OnJoinCheck roleid:" PRINT64 " zoneid:%d center_battle_type:%d battle_type:%d",
	          roleid, zoneid, proto.center_battle_type(), proto.battle_type());

	auto it = m_local_player_entry_map.find(roleid);
	if (it == m_local_player_entry_map.end())
	{
		return -1;
	}
	auto tit = it->second.local_player_apply_entry_map.find(proto.battle_type());
	if (tit == it->second.local_player_apply_entry_map.end())
	{
		return -1;
	}
	LocalPlayerApplyEntry& lpae = *(tit->second);
	lpae.center_zoneid = zoneid;

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->SNSReady() || pinfo->friends.GetArenaGroup().id == 0)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, -1);
		return -1;
	}

	if (!pinfo->teamid)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NO_TEAM);
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER);
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_MEMBER_NOT_AROUND);
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (!member->info()->SNSReady() || member->info()->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_MEMBER_NOT_AROUND);
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}

		auto it = m_local_player_entry_map.find(member->roleid());
		if (it != m_local_player_entry_map.end())
		{
			LocalPlayerEntry& local_player_entry = it->second;
			LOCAL_PLAYER_PUNISH_MAP::iterator pit = local_player_entry.local_player_punish_map.find(proto.battle_type());
			if (pit != local_player_entry.local_player_punish_map.end() && pit->second > Timer::GetTime())
			{
				SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_BATTLE_PUNISHED);
				return ERROR_CENTER_BATTLE_PUNISHED;
			}
			auto tit = local_player_entry.local_player_apply_entry_map.find(proto.battle_type());
			if (tit == local_player_entry.local_player_apply_entry_map.end())
			{
				SendRoamCenterBattleJoinCheck_Re(roleid, proto.battle_type(), lpae.center_zoneid, ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER);
				return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
			}
			LocalPlayerApplyEntry& member_lpae = *(tit->second);
			member_lpae.center_zoneid = zoneid;
		}
	}

	//给玩家所在gs发消息
	pinfo->SendMessage2GS(proto);
	BattleMatchLog(tit->second, pinfo, proto.center_battle_type(), 0 /*匹配成功*/);
	return 0;
}

//收到中心服请求让玩家跨服
int CenterArenaTeamBattleClient::OnTryTeleportPlayer(ruid_t roleid, PB::ipt_center_battle_roam_teleport_player& proto)
{
	LOG_TRACE("CenterArenaTeamBattleClient::OnTryTeleportPlayer roleid:" PRINT64 " center_battle_type:%d\n",
	          roleid, proto.center_battle_type());

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || pinfo->friends.GetArenaGroup().id == 0)
	{
		return -1;
	}

	if (!pinfo->teamid)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return ERROR_CENTER_ARENA_NO_TEAM;
	}

	if (pTeam->GetLeaderID() != roleid)
	{
		return ERROR_CENTER_ARENA_NOT_LEADER;
	}

	unsigned int team_size = pTeam->members.size();
	if (team_size != m_team_member_need)
	{
		return ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER;
	}
	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline())
		{
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}

		if (member->info()->friends.GetArenaGroup().id != pinfo->friends.GetArenaGroup().id)
		{
			return ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP;
		}

		auto it = m_local_player_entry_map.find(member->roleid());
		if (it == m_local_player_entry_map.end())
		{
			return ERROR_CENTER_ARENA_MEMBER_NOT_AROUND;
		}
	}

	proto.set_center_battle_type(GetType());

	//给玩家所在gs发消息
	pinfo->SendMessage2GS(proto);
	return 0;
}

void CenterArenaTeamBattleClient::OnPlayerChangeScene(ruid_t roleid, int world_tid)
{
	LOG_TRACE("CenterArenaTeamBattleClient::OnPlayerChangeScene center_battle_type:%d roleid:%ld world_tid:%d\n",
	          GetType(), roleid, world_tid);

	/*RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->teamid)
		return;

	TeamInfo* pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
		return;

	PlayerQuitBattle(pTeam->GetLeaderID(), 0, QBT_C2S_BY_CLIENT);*/
	PlayerQuitBattle(roleid, 0, QBT_C2S_BY_CLIENT);
}

void CenterArenaTeamBattleClient::PlayerQuitBattle(ruid_t roleid, int battle_type, int tag)
{
	LOG_TRACE("CenterArenaTeamBattleClient::PlayerQuitBattle roleid:" PRINT64 " center_battle_type:%d battle_type:%d tag:%d\n",
	          roleid, GetType(), battle_type, tag);

	RoleInfo *pinfo = RoleMap::Instance().Find(roleid);
	LOCAL_PLAYER_ENTRY_MAP::iterator it = m_local_player_entry_map.find(roleid);
	if (it == m_local_player_entry_map.end())
	{
		return;
	}
	LocalPlayerEntry& lpe = it->second;

	if (battle_type != 0)
	{
		LOCAL_PLAYER_APPLY_ENTRY_MAP::iterator tit = lpe.local_player_apply_entry_map.find(battle_type);
		if (tit == lpe.local_player_apply_entry_map.end())
		{
			return;
		}

		//找到对应队长的信息
		LOCAL_PLAYER_ENTRY_MAP::iterator leader_it = m_local_player_entry_map.find(tit->second->leader_roleid);
		if (leader_it == m_local_player_entry_map.end())
		{
			return;
		}
		LocalPlayerEntry& leader_lpe = leader_it->second;
		LOCAL_PLAYER_APPLY_ENTRY_MAP::iterator leader_tit = leader_lpe.local_player_apply_entry_map.find(battle_type);
		if (leader_tit == leader_lpe.local_player_apply_entry_map.end())
		{
			return;
		}

		if (leader_tit->second->send_apply)
		{
			//请求已经发送过了，那就需要去中心服取消了
			SendRoamCenterBattleQuit(roleid, battle_type, leader_tit->second->center_zoneid, tag);
		}
		else
		{
			//标记一下自己已经退出，请求消息一会就不用再发了
			leader_tit->second->quit = true;
		}
		BattleMatchLog(leader_tit->second, pinfo, GetType(), 1 /*取消*/);
		if (pinfo && GetType() == CBT_ARENA_ELIMINATE && ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetCenterMatchSuccessTime(pinfo->friends.GetEliminateGroupID()) == 0)
		{
			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().SetIsMatching(pinfo->friends.GetEliminateGroupID(), false);
		}
	}
	else
	{
		if (lpe.local_player_apply_entry_map.empty())
		{
			return;
		}
		for (auto tit = lpe.local_player_apply_entry_map.begin(), teit = lpe.local_player_apply_entry_map.end(); tit != teit; ++ tit)
		{
			//找到对应队长的信息
			LOCAL_PLAYER_ENTRY_MAP::iterator leader_it = m_local_player_entry_map.find(tit->second->leader_roleid);
			if (leader_it == m_local_player_entry_map.end())
			{
				continue;
			}
			LocalPlayerEntry& leader_lpe = leader_it->second;
			LOCAL_PLAYER_APPLY_ENTRY_MAP::iterator leader_tit = leader_lpe.local_player_apply_entry_map.find(tit->first);
			if (leader_tit == leader_lpe.local_player_apply_entry_map.end())
			{
				continue;
			}

			if (leader_tit->second->send_apply)
			{
				//求已经发送过了，那就需要去中心服取消了
				SendRoamCenterBattleQuit(roleid, battle_type, leader_tit->second->center_zoneid, tag);
			}
			else
			{
				//标记一下自己已经退出，请求消息一会就不用再发了
				leader_tit->second->quit = true;
			}
			BattleMatchLog(leader_tit->second, pinfo, GetType(), 1 /*取消*/);
			if (pinfo && GetType() == CBT_ARENA_ELIMINATE && ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetCenterMatchSuccessTime(pinfo->friends.GetEliminateGroupID()) == 0)
			{
				ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().SetIsMatching(pinfo->friends.GetEliminateGroupID(), false);
			}
		}
	}
}

void CenterArenaTeamBattleClient::OnLocalJoinAnswer(ruid_t roleid, PB::npt_center_battle_join_answer& proto)
{
	LOG_TRACE("CenterArenaTeamBattleClient::OnLocalJoinAnswer roleid:" PRINT64 " center_battle_type:%d battle_type:%d agree:%d\n",
	          roleid, proto.center_battle_type(), proto.battle_type(), proto.agree());

	RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
	if (!pinfo || !pinfo->teamid)
	{
		return;
	}
	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return;
	}
	auto it = m_local_player_entry_map.find(roleid);
	if (it == m_local_player_entry_map.end())
	{
		return;
	}
	LocalPlayerEntry& local_player_entry = it->second;
	auto tit = local_player_entry.local_player_apply_entry_map.find(m_battle_tid);
	if (tit == local_player_entry.local_player_apply_entry_map.end())
	{
		return;
	}
	LocalPlayerApplyEntry& lpae = *(tit->second);

	CenterBattleClientStub::OnLocalJoinAnswer(roleid, proto);

	PB::npt_center_battle_join_answer_re re;
	re.set_ask_time(lpae.join_ask_time);
	re.set_center_battle_type(GetType());
	re.set_battle_type(proto.battle_type());
	auto *pAnswer = re.add_infos();
	pAnswer->set_roleid(roleid);
	pAnswer->set_agree(proto.agree());

	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->IsOnline() || !member->info())
		{
			continue;
		}
		member->info()->SendMessage2Client(re);
	}
}

void CenterArenaTeamBattleClient::OnPlayerLogin(RoleInfo *pinfo)
{
	CenterBattleClientStub::OnPlayerLogin(pinfo);

	auto it = m_local_player_entry_map.find(pinfo->roleid);
	if (it == m_local_player_entry_map.end())
	{
		return;
	}
	LocalPlayerEntry& local_player_entry = it->second;
	auto tit = local_player_entry.local_player_apply_entry_map.find(m_battle_tid);
	if (tit == local_player_entry.local_player_apply_entry_map.end())
	{
		return;
	}
	LocalPlayerApplyEntry& lpae = *(tit->second);
	PB::npt_center_battle_join_answer_re re;
	re.set_ask_time(lpae.join_ask_time);

	TeamInfo *pTeam = TeamManager::Instance().FindTeam(pinfo->teamid);
	if (!pTeam)
	{
		return;
	}

	for (const auto& m : pTeam->members)
	{
		const RoleWrapper& member = m.Role();
		if (!member || !member->Available() || !member->info())
		{
			continue;
		}

		auto *pAnswer = re.add_infos();
		pAnswer->set_roleid(member->roleid());
		pAnswer->set_agree(-1);

		auto mit = m_local_player_entry_map.find(member->roleid());
		if (mit != m_local_player_entry_map.end())
		{
			LocalPlayerEntry& member_local_player_entry = mit->second;
			auto mtit = member_local_player_entry.local_player_apply_entry_map.find(m_battle_tid);
			if (mtit != member_local_player_entry.local_player_apply_entry_map.end())
			{
				LocalPlayerApplyEntry& member_lpae = *(mtit->second);
				pAnswer->set_agree(member_lpae.join_answer);
			}
		}
	}

	pinfo->SendMessage2Client(re);
	LOG_TRACE("CenterArenaTeamBattleClient::OnPlayerLogin::roleid=" PRINT64":center_battle_type=%d", pinfo->roleid, GetType());
}

void CenterArenaTeamBattleClient::DebugSetMaxPlayerNum(int battle_type, int max_player_num)
{
	LOG_TRACE("CenterArenaTeamBattleClient::DebugSetMaxPlayerNum center_battle_type:%d battle_type:%d max_player_num:%d\n",
	          GetType(), battle_type, max_player_num);

	m_team_member_need = (unsigned int)max_player_num / 2;

	PB::ipt_center_battle_debug_set_max_player_num pb;
	pb.set_center_battle_type(GetType());
	pb.set_battle_type(battle_type);
	pb.set_max_player_num(max_player_num);

	CenterManager::GetInstance().SendMessage2Hub(pb, 0, GetServiceName(), GetCenterZoneID(GetBattleTid()));
}

void CenterArenaTeamBattleClient::DebugForceRobot(bool force)
{
	LOG_TRACE("CenterArenaTeamBattleClient::DebugForceRobot force=%d\n", (int)force);

	m_debug_force_robot = force;
}

bool CenterArenaTeamBattleClient::Update()
{
	if (!CenterBattleClientStub::Update())
	{
		return false;
	}

	int now_time = Timer::GetTime();
	int server_open_time = GDeliveryServer::GetInstance()->GetServerOpenTime();
	if (0 == server_open_time)
	{
		return true;
	}
	int server_level = DSTPManager::GetInstance().GetServerLevel();
	if (server_level < CENTER_ARENA_TEAM_BATTLE_SYNC_SERVER_INFO_LEVEL_LIMIT)
	{
		return true;
	}
	if (g_support_zone.empty())
	{
		return true;
	}
	zone_id_t center_zone_id = GetCenterZoneID(m_battle_tid);
	if (0 == center_zone_id)
	{
		return true;
	}
	if (DeltaDays(now_time, m_sync_server_info_timestamp) != 0 || !m_arena_zone_sync_ok)
	{
		m_sync_server_info_timestamp = now_time;

		if (GET_FUNC_SWITCH(kFuncCodeServerInfoReport))
		{
			//每日需要把服务器等级报给组队竞技场中心管理服
			PB::ipt_center_battle_sync_info pb;
			pb.set_center_battle_type(GetType());
			pb.set_server_open_time(server_open_time);
			pb.set_info_type(PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_SERVER_INFO);
			for (size_t i = 0; i < g_support_zone.size(); ++ i)
			{
				pb.add_support_zones(g_support_zone[i]);
			}
			CenterManager::GetInstance().SendMessage2Hub(pb, 0, GetServiceName(), center_zone_id);

			LOG_TRACE("CenterArenaTeamBattleClient::Update sync server_open_time:%d", server_open_time);
		}
	}
	TryLoadArenaZones();
	TrySaveArenaZones();
	TryLoadArenaGroupRanks();
	TrySaveArenaGroupRanks();
	TryLoadMemberStatus();
	TryGetMemberStatus();
	TrySaveMemberStatus();
	return true;
}

void CenterArenaTeamBattleClient::TryLoadArenaZones()
{
	if (m_arena_zone_init)
	{
		return;
	}

	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}
	if (!ipd->ip_global_data_manager.IsInit())
	{
		return;
	}

	m_zones.clear();
	m_arena_zones.clear();
	m_zones_last.clear();
	//m_arena_zones_last.clear();
	m_support_zones_last.clear();

	//加载上次重置战区时间
	ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_TIMESTAMP), m_arena_zone_timestamp);
	if (m_arena_zone_timestamp > Timer::GetTime())
	{
		//由于合服的bug，怕导致某些服的时间戳变成未来的，引起永远不刷新，这里尝试修复下。
		LOG_TRACE("CenterArenaTeamBattleClient::TryLoadArenaZones m_arena_zone_timestamp=%d in future", m_arena_zone_timestamp);
		m_arena_zone_timestamp = GetLocalMonthBegin();
	}
	LOG_TRACE("CenterArenaTeamBattleClient::TryLoadArenaZones m_arena_zone_timestamp=%d", m_arena_zone_timestamp);

	//加载本期的战区
	Octets data1;
	if (ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_ZONE), data1) && data1.size() > 0)
	{
		PB::db_center_arena_team pb;
		if (!pb.ParseFromArray(data1.begin(), data1.size()))
		{
			Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadArenaZones m_zones failed to parse data");
		}
		for (int i = 0; i < pb.zones_size(); ++ i)
		{
			auto *pZone = pb.mutable_zones(i);
			if (0 == pZone->server_open_time())
			{
				continue;
			}

			center_arena_team_battle_zone_info zone_info;
			zone_info.zoneid = pZone->zoneid();
			zone_info.server_open_time = pZone->server_open_time();
			zone_info.arena_zone = pZone->arena_zone();

			LOG_TRACE("CenterArenaTeamBattleClient::TryLoadArenaZone m_zones zoneid=%d server_open_time=%d arena_zone=%d",
			          zone_info.zoneid, zone_info.server_open_time, zone_info.arena_zone);

			AddArenaZone(zone_info);
		}
	}

	//加载上期的战区
	Octets data2;
	if (ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_ZONE_LAST), data2) && data2.size() > 0)
	{
		Marshal::OctetsStream os(data2), support_zones;
		Octets value;
		try
		{
			os >> support_zones >> value;

			int support_zones_count = 0;
			support_zones >> support_zones_count;
			for (int i = 0; i < support_zones_count; ++ i)
			{
				zone_id_t zoneid = 0;
				support_zones >> zoneid;
				m_support_zones_last.push_back(zoneid);
			}

			PB::db_center_arena_team pb;
			if (!pb.ParseFromArray(value.begin(), value.size()))
			{
				Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadArenaZones m_zones_last failed to parse data");
			}
			for (int i = 0; i < pb.zones_size(); ++ i)
			{
				auto *pZone = pb.mutable_zones(i);
				if (0 == pZone->server_open_time())
				{
					continue;
				}

				center_arena_team_battle_zone_info zone_info;
				zone_info.zoneid = pZone->zoneid();
				zone_info.server_open_time = pZone->server_open_time();
				zone_info.arena_zone = pZone->arena_zone();

				LOG_TRACE("CenterArenaTeamBattleClient::TryLoadArenaZone m_zones_last zoneid=%d server_open_time=%d arena_zone=%d",
				          zone_info.zoneid, zone_info.server_open_time, zone_info.arena_zone);

				m_zones_last[zone_info.zoneid] = zone_info;
			}
		}
		catch (...)
		{
			Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadArenaZone failed");
		}
	}

	m_arena_zone_init = true;
}

void CenterArenaTeamBattleClient::TrySaveArenaZones()
{
	if (!m_arena_zone_init || !m_arena_zone_dirty)
	{
		return;
	}

	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}
	if (!ipd->ip_global_data_manager.IsInit())
	{
		return;
	}

	//保存上次重置战区时间
	ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_TIMESTAMP), m_arena_zone_timestamp);
	LOG_TRACE("CenterArenaTeamBattleClient::TrySaveArenaZone m_arena_zone_timestamp=%d", m_arena_zone_timestamp);

	//保存上期本服支持的服务器列表
	Marshal::OctetsStream support_zones_os;
	support_zones_os << (int)m_support_zones_last.size();
	for (size_t i = 0; i < m_support_zones_last.size(); ++ i)
	{
		support_zones_os << m_support_zones_last[i];
	}

	//保存本期的战区
	PB::db_center_arena_team pb1;
	for (auto& kv : m_zones)
	{
		auto *pZone = pb1.add_zones();
		pZone->set_zoneid(kv.second.zoneid);
		pZone->set_server_open_time(kv.second.server_open_time);
		pZone->set_arena_zone(kv.second.arena_zone);

		LOG_TRACE("CenterArenaTeamBattleClient::TrySaveArenaZone m_zones zoneid=%d server_open_time=%d arena_zone=%d",
		          kv.second.zoneid, kv.second.server_open_time, kv.second.arena_zone);
	}
	Octets value;
	value.resize(pb1.ByteSize());
	pb1.SerializeWithCachedSizesToArray((uint8_t *)value.begin());

	if (!ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_ZONE), value))
	{
		Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TrySaveArenaZone m_zones failed to setdata");
	}

	//保存上期的战区
	PB::db_center_arena_team pb2;
	for (auto& kv : m_zones_last)
	{
		auto *pZone = pb2.add_zones();
		pZone->set_zoneid(kv.second.zoneid);
		pZone->set_server_open_time(kv.second.server_open_time);
		pZone->set_arena_zone(kv.second.arena_zone);

		LOG_TRACE("CenterArenaTeamBattleClient::TrySaveArenaZone m_zones_last zoneid=%d server_open_time=%d arena_zone=%d",
		          kv.second.zoneid, kv.second.server_open_time, kv.second.arena_zone);
	}
	value.resize(pb2.ByteSize());
	pb2.SerializeWithCachedSizesToArray((uint8_t *)value.begin());

	Marshal::OctetsStream os;
	os << support_zones_os << value;
	Octets o = os;
	if (!ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_ZONE_LAST), o))
	{
		Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TrySaveArenaZone m_zones_last failed to setdata");
	}

	m_arena_zone_dirty = false;
}

void CenterArenaTeamBattleClient::TryLoadArenaGroupRanks()
{
	if (m_arena_group_ranks_init)
	{
		return;
	}
	if (!GameDBClient::GetInstance()->IsConnect())
	{
		return;
	}
	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}
	if (!ipd->ip_global_data_manager.IsInit())
	{
		return;
	}

	m_arena_group_zone_ranks.clear();
	m_arena_group_zone_ranks_last.clear();

	Octets value;
	if (ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_RANK_CLIENT), value) && value.size() > 0)
	{
		try
		{
			Marshal::OctetsStream os(value), os_content;
			int count = 0;
			os >> count >> os_content;
			for (int i = 0; i < count; ++ i)
			{
				int arena_zone = 0;
				Octets data;
				os_content >> arena_zone >> data;
				PB::db_arena_group_ranks& ranks = m_arena_group_zone_ranks[arena_zone];
				if (!ranks.ParseFromArray(data.begin(), data.size()))
				{
					Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadArenaGroupRanks m_arena_group_zone_ranks failed to parse data");
					return;
				}
			}
		}
		catch (...)
		{
			Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadArenaGroupRanks m_arena_group_zone_ranks failed");
		}
	}

	Octets value2;
	if (ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_RANK_CLIENT_LAST), value2) && value2.size() > 0)
	{
		try
		{
			Marshal::OctetsStream os(value2), os_content;
			int count = 0;
			os >> count >> os_content;
			for (int i = 0; i < count; ++ i)
			{
				int arena_zone = 0;
				Octets data;
				os_content >> arena_zone >> data;
				PB::db_arena_group_ranks& ranks = m_arena_group_zone_ranks_last[arena_zone];
				if (!ranks.ParseFromArray(data.begin(), data.size()))
				{
					Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadArenaGroupRanks m_arena_group_zone_ranks_last failed to parse data");
					return;
				}
			}
			RefreshLastRank();
		}
		catch (...)
		{
			Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadArenaGroupRanks m_arena_group_zone_ranks_last failed");
		}
	}

	DumpArenaGroupRanks();

	m_arena_group_ranks_init = true;
}

void CenterArenaTeamBattleClient::TrySaveArenaGroupRanks()
{
	if (!m_arena_group_ranks_init || !m_arena_group_ranks_dirty)
	{
		return;
	}

	static int counter = 0;
	if (++ counter < 10)
	{
		return;
	}
	counter = 0;

	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}

	Marshal::OctetsStream os, os_final;
	for (auto it = m_arena_group_zone_ranks.begin(), eit = m_arena_group_zone_ranks.end(); it != eit; ++ it)
	{
		Octets value;
		value.resize(it->second.ByteSize());
		it->second.SerializeWithCachedSizesToArray((uint8_t *)value.begin());

		os << it->second.arena_zone() << value;
	}
	os_final << (int)m_arena_group_zone_ranks.size() << os;
	Octets o1 = os_final;
	if (!ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_RANK_CLIENT), o1))
	{
		Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TrySaveArenaGroupRanks m_arena_group_zone_ranks failed to setdata");
	}

	Marshal::OctetsStream os2, os_final2;
	for (auto it = m_arena_group_zone_ranks_last.begin(), eit = m_arena_group_zone_ranks_last.end(); it != eit; ++ it)
	{
		Octets value;
		value.resize(it->second.ByteSize());
		it->second.SerializeWithCachedSizesToArray((uint8_t *)value.begin());

		os2 << it->second.arena_zone() << value;
	}
	os_final2 << (int)m_arena_group_zone_ranks_last.size() << os2;
	Octets o2 = os_final2;
	if (!ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_RANK_CLIENT_LAST), o2))
	{
		Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TrySaveArenaGroupRanks m_arena_group_zone_ranks_last failed to setdata");
	}

	DumpArenaGroupRanks();

	m_arena_group_ranks_dirty = false;
}

void CenterArenaTeamBattleClient::DumpArenaGroupRanks()
{
	std::stringstream ss;
	for (auto& kv : m_arena_group_zone_ranks)
	{
		ss << "arena_zone=" << kv.first << ":";
		for (int i = 0; i < kv.second.infos_size(); ++ i)
		{
			auto *pInfo = kv.second.mutable_infos(i);
			ss << "(" << pInfo->info().id() << "-" << pInfo->grade() << ")";
		}
	}
	LOG_TRACE("CenterArenaTeamBattleClient::DumpArenaGroupRanks m_arena_group_zone_ranks=%s", ss.str().c_str());

	for (auto& kv : m_arena_group_zone_ranks_last)
	{
		ss << "arena_zone=" << kv.first << ":";
		for (int i = 0; i < kv.second.infos_size(); ++ i)
		{
			auto *pInfo = kv.second.mutable_infos(i);
			ss << "(" << pInfo->info().id() << "-" << pInfo->grade() << ")";
		}
	}
	LOG_TRACE("CenterArenaTeamBattleClient::DumpArenaGroupRanks m_arena_group_zone_ranks_last=%s", ss.str().c_str());
}

void CenterArenaTeamBattleClient::TryGetPlayerStatus(int diaoxiang_id, ruid_t roleid)
{
	zone_id_t center_zone_id = GetCenterZoneID(m_battle_tid);
	if (0 == center_zone_id)
	{
		return;
	}

	PB::ipt_center_battle_sync_info pb;
	pb.set_center_battle_type(GetType());
	pb.set_info_type(PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_CLIENT_GET_PLAYER_STATUS);
	pb.set_get_player_status_roleid(roleid);
	pb.set_get_player_status_diaoxiang_id(diaoxiang_id);

	CenterManager::GetInstance().SendMessage2Hub(pb, 0, GetServiceName(), center_zone_id);
}

void CenterArenaTeamBattleClient::TryGetMemberStatus()
{
	if (!m_arena_group_member_status_init || m_first_arena_group_save_timestamp >= m_arena_zone_timestamp)
	{
		return;
	}

	static int counter = 0;
	if (++ counter < 30)
	{
		return;
	}
	counter = 0;

	zone_id_t center_zone_id = GetCenterZoneID(m_battle_tid);
	if (0 == center_zone_id)
	{
		return;
	}
	PB::ipt_center_battle_sync_info pb;
	pb.set_center_battle_type(GetType());
	pb.set_info_type(PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_CLIENT_GET_MEMBER_STATUS);
	//拉取一下主服以及被合并服务器的战区的战队第一名
	std::stringstream ss;
	std::set<int> tmp_set;
	for (size_t i = 0; i < g_support_zone.size(); ++ i)
	{
		auto _it = m_zones_last.find(g_support_zone[i]);
		if (_it == m_zones_last.end())
		{
			//这个应该是2期前就被合服了的，不用管
			continue;
		}
		if (tmp_set.find(_it->second.arena_zone) != tmp_set.end())
		{
			continue;
		}
		auto _tit = m_arena_group_zone_ranks_last.find(_it->second.arena_zone);
		if (_tit == m_arena_group_zone_ranks_last.end())
		{
			continue;
		}
		if (_tit->second.infos_size() == 0)
		{
			continue;
		}
		int64_t arena_group_id = _tit->second.infos(_tit->second.infos_size() - 1).info().id();
		ss << "(" << _it->second.arena_zone << "," << arena_group_id << ")";
		pb.add_get_member_arena_group_ids(arena_group_id);
		tmp_set.insert(_it->second.arena_zone);
	}
	CenterManager::GetInstance().SendMessage2Hub(pb, 0, GetServiceName(), center_zone_id);

	LOG_TRACE("CenterArenaTeamBattleClient::TryGetMemberStatus zoneid=%d center_zone_id=%d get_member_arena_group_ids=%s",
	          (int)g_zoneid, (int)center_zone_id, ss.str().c_str());
}

void CenterArenaTeamBattleClient::TryLoadMemberStatus()
{
	if (m_arena_group_member_status_init)
	{
		return;
	}

	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}

	Octets value;
	if (ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_STATUS), value) && value.size() > 0)
	{
		try
		{
			Marshal::OctetsStream os(value);
			os >> m_first_arena_group_save_timestamp;
		}
		catch (...)
		{
			Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TryLoadMemberStatus failed");
		}
	}

	m_arena_group_member_status_init = true;
}

void CenterArenaTeamBattleClient::TrySaveMemberStatus()
{
	if (!m_arena_group_member_status_init || !m_arena_group_member_status_dirty)
	{
		return;
	}

	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}

	Marshal::OctetsStream ss;
	ss << m_first_arena_group_save_timestamp;

	Octets o = ss;
	if (!ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_CENTER_ARENA_GROUP_STATUS), o))
	{
		Log::log(LOG_ERR, "CenterArenaTeamBattleClient::TrySaveMemberStatus failed to setdata");
	}

	m_arena_group_member_status_dirty = false;
}

void CenterArenaTeamBattleClient::OnSyncArenaGroupZoneInfo(zone_id_t zoneid, const ipt_center_battle_sync_info& sync_info)
{
	if (sync_info.arena_group_zone_timestamp() > m_arena_zone_timestamp)
	{
		LOG_TRACE("CenterArenaTeamBattleClient::OnSyncArenaGroupZoneInfo SYNC_INFO_TYPE_SERVER_INFO_RE refresh arena zone. arena_group_zone_timestamp=%d m_arena_zone_timestamp=%d",
		          sync_info.arena_group_zone_timestamp(), m_arena_zone_timestamp);

		//战区重新计算了，把本期的存到上期的里边
		m_arena_zone_timestamp = sync_info.arena_group_zone_timestamp();

		//把本期的战区保存到上期的
		m_zones_last = m_zones;
		//m_arena_zones_last = m_arena_zones;
		m_support_zones_last = g_support_zone;
		m_arena_zone_dirty = true;

		//把本期战队保存到上期的
		m_arena_group_zone_ranks_last.clear();
		m_arena_group_zone_ranks_last.swap(m_arena_group_zone_ranks);
		RefreshLastRank();
		m_arena_group_ranks_dirty = true;

		//排行榜也清空了
		//改成用配置清空，新榜存老榜的时候清空
		//DSTPManager::GetInstance().Debug_Clear(TPN_ARENA_GROUP);

		DumpArenaGroupRanks();
	}

	m_arena_zones.clear();
	m_zones.clear();

	for (int i = 0; i < sync_info.arena_team().zones_size(); ++ i)
	{
		auto& zone = sync_info.arena_team().zones(i);

		center_arena_team_battle_zone_info zone_info;
		zone_info.zoneid = zone.zoneid();
		zone_info.server_open_time = zone.server_open_time();
		zone_info.arena_zone = zone.arena_zone();

		LOG_TRACE("CenterArenaTeamBattleClient::OnSyncArenaGroupZoneInfo zoneid=%d server_open_time=%d arena_zone=%d",
		          zone_info.zoneid, zone_info.server_open_time, zone_info.arena_zone);

		AddArenaZone(zone_info);
	}

	m_arena_zone_sync_ok = true;
}

//中心服给普通服同步信息
void CenterArenaTeamBattleClient::OnSyncServerInfo(zone_id_t zoneid, const ipt_center_battle_sync_info& sync_info)
{
	LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo zoneid=%d info_type=%d", zoneid, (int)sync_info.info_type());

	if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_SERVER_INFO_RE)
	{
		OnSyncArenaGroupZoneInfo(zoneid, sync_info);
		TryUpdateHistoryRanks(sync_info);
	}
	else if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_SERVER_INFO_BROADCAST)
	{
		if (!m_arena_zone_init)
		{
			GLog::log(LOG_ERR, "CenterArenaTeamBattleClient::OnSyncServerInfo m_arena_zone_init = false");
			return;
		}
		zone_id_t manager_zoneid = GetCenterZoneID(m_battle_tid);
		if (zoneid != manager_zoneid)
		{
			LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo zoneid=%d manager_zoneid=%d", zoneid, manager_zoneid);
			return;
		}
		OnSyncArenaGroupZoneInfo(zoneid, sync_info);
		TryUpdateHistoryRanks(sync_info);
	}
	else if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_ARENA_GROUP_RANKS)
	{
		if (!m_arena_group_ranks_init)
		{
			return;
		}
		if (sync_info.arena_group_rank_timestamp() < m_arena_zone_timestamp)
		{
			return;
		}
		PB::db_arena_group_ranks& ranks = m_arena_group_zone_ranks[sync_info.arena_group_zone()];
		ranks.CopyFrom(sync_info.arena_group_ranks());

		for (int i = 0; i < ranks.infos_size(); ++ i)
		{
			auto *pInfo = ranks.mutable_infos(i);
			//插入到对应战区的排行榜里
			PB::toplist_addon_data_t addon;
			addon.mutable_arenagroup_data()->CopyFrom(pInfo->addon_data());
			DSTPManager::GetInstance().UpdateItem(TPN_ARENA_GROUP_CENTER_0 + sync_info.arena_group_zone(),
			                                      pInfo->info().id(), (int64_t)pInfo->grade(),
			                                      Octets(pInfo->info().name().c_str(), pInfo->info().name().size()),
			                                      0, 0, true, addon);

			LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo SYNC_INFO_TYPE_ARENA_GROUP_RANKS arena_group_zone=%d index=%d arena_group_id=%ld grade=%d",
			          sync_info.arena_group_zone(), i, pInfo->info().id(), pInfo->grade());
		}

		m_arena_group_ranks_dirty = true;
	}
	/*else if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_ARENA_GROUP_ONE_RANK)
	{
		//用这种方式如果丢消息了会导致排行榜错乱，还是每次都发全了吧
		if (sync_info.arena_group_rank_timestamp() < m_arena_zone_timestamp)
		{
			return;
		}
	}*/
	else if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_GET_MEMBER_STATUS)
	{
		LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo SYNC_INFO_TYPE_GET_MEMBER_STATUS get_member_arena_group_ids=%ld get_member_zoneid=%d",
		          sync_info.get_member_arena_group_ids(0), sync_info.get_member_zoneid());

		if (sync_info.get_member_arena_group_ids_size() == 0)
		{
			return;
		}
		PB::ipt_center_battle_group_get_member_status get_member_status;
		get_member_status.set_arena_group_id(sync_info.get_member_arena_group_ids(0));
		get_member_status.set_get_member_zoneid(sync_info.get_member_zoneid());

		GameDBClient::GetInstance()->SendMessage(get_member_status);
	}
	else if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_CLIENT_GET_MEMBER_STATUS_RE)
	{
		LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo SYNC_INFO_TYPE_CLIENT_GET_MEMBER_STATUS_RE get_member_arena_group_ids=%ld status_size=%d",
		          sync_info.get_member_arena_group_ids(0), sync_info.get_member_status_size());

		do
		{
			if (sync_info.get_member_status_size() == 0)
			{
				break;
			}
			if (sync_info.get_member_arena_group_ids_size() == 0)
			{
				break;
			}

			auto it = m_zones_last.find(ZONE_ID(sync_info.get_member_arena_group_ids(0)));
			if (it == m_zones_last.end())
			{
				it = m_zones_last.find(MERGE_ZONE(sync_info.get_member_arena_group_ids(0)));
				if (it == m_zones_last.end())
				{
					break;
				}
			}

			int diaoxiang_index = 1 + it->second.arena_zone * ARENA_GROUP_MAX_MEMBER_COUNT;
			for (int i = 0; i < sync_info.get_member_status_size(); ++ i)
			{
				diaoxiang_manager::GetInstance().SetDiaoxiang(diaoxiang_index + i, Octets(sync_info.get_member_status(i).c_str(), sync_info.get_member_status(i).size()));
			}
		}
		while (false);

		m_first_arena_group_save_timestamp = m_arena_zone_timestamp;
		m_arena_group_member_status_dirty = true;
	}
	else if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_GET_PLAYER_STATUS)
	{
		LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo SYNC_INFO_TYPE_GET_PLAYER_STATUS zoneid=%d roleid=%ld diaoxiang_id=%d",
		          sync_info.get_player_zoneid(), sync_info.get_player_status_roleid(), sync_info.get_player_status_diaoxiang_id());

		PB::ipt_center_battle_group_get_player_status get_player_status;
		get_player_status.set_get_player_roleid(sync_info.get_player_status_roleid());
		get_player_status.set_get_player_diaoxiang_id(sync_info.get_player_status_diaoxiang_id());
		get_player_status.set_get_player_zoneid(sync_info.get_player_zoneid());

		GameDBClient::GetInstance()->SendMessage(get_player_status);
	}
	else if (sync_info.info_type() == PB::ipt_center_battle_sync_info::SYNC_INFO_TYPE_CLIENT_GET_PLAYER_STATUS_RE)
	{
		LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo SYNC_INFO_TYPE_CLIENT_GET_PLAYER_STATUS_RE roleid=%ld diaoxiang_id=%d",
		          sync_info.get_player_status_roleid(), sync_info.get_player_status_diaoxiang_id());

		diaoxiang_manager::GetInstance().SetDiaoxiang(sync_info.get_player_status_diaoxiang_id(),
		        Octets(sync_info.get_player_status().c_str(), sync_info.get_player_status().size()));
	}
}

void CenterArenaTeamBattleClient::TryUpdateHistoryRanks(const ipt_center_battle_sync_info& sync_info)
{
	LOG_TRACE("CenterArenaTeamBattleClient::OnSyncServerInfo SYNC_INFO_TYPE_SERVER_HIS_RANKS arena_rank_seasons =%d",
	          sync_info.has_arena_team() ? sync_info.arena_team().his_ranks_size() : 0);
	// 排行榜列表，第一个是上一赛季的排行，第二个是上上赛季的排行依次类推
	// 策略：对比上一季赛季排行的时间跟本服的排行榜上衣赛季的时间，不同则更新，相同则跳过
	if (sync_info.has_arena_team() && sync_info.arena_team().his_ranks_size() > 0)
	{
		bool need_update_toplist = true;
		int center_rank_save_ts = sync_info.arena_team().his_ranks(0).save_ts();
		std::vector<DSTopListKey> key_list;
		DSTPManager::GetInstance().GetTopN(TPN_ARENA_GROUP_HIS_RANK_START, 1, key_list);
		if (!key_list.empty())
		{
			if (key_list[0].changetime < center_rank_save_ts)
			{
				need_update_toplist = true;
			}
		}
		// 收集一下每个赛区有几个历史排行榜，发送给客户端
		std::map<int/*arena_zone_id*/, int/*rank_season_count*/> zone_season_count;
		PB::toplist_addon_data_t addon;
		for (int i = 0; i < sync_info.arena_team().his_ranks_size(); ++i)
		{
			const auto& season_ranks = sync_info.arena_team().his_ranks(i);
			for (const auto& zone_rank : season_ranks.arena_group_ranks())
			{
				int arena_zone_id = zone_rank.arena_zone();
				if (arena_zone_id > CENTER_ARENA_ZONE_CAPACITY)
				{
					break;
				}
				zone_season_count[arena_zone_id]++;
				if (need_update_toplist)
				{
					int tpn_id = GetRankHistoryTopId(arena_zone_id, i);
					if (tpn_id)
					{
						LOG_TRACE("CenterArenaTeamBattleClient::ClearRank tpnid=%d arena_zone_id=%d  i=%d", tpn_id, arena_zone_id, i);
						DSTPManager::GetInstance().Clear(tpn_id, false/*don't save*/);
						for (int i = zone_rank.infos_size(); i > 0; -- i)
							//for (const auto& rank_info : zone_rank.infos())
						{
							auto& rank_info = zone_rank.infos(i - 1);
							addon.mutable_arenagroup_data()->CopyFrom(rank_info.addon_data());
							DSTPManager::GetInstance().UpdateItem(tpn_id, rank_info.info().id(), (int64_t)rank_info.grade(),
							                                      Octets(rank_info.info().name().c_str(), rank_info.info().name().size()),
							                                      0, 0, true, addon);
						}
					}
				}
			}
		}
		PB::npt_arena_group_global_rank_info his_rank_count;
		for (int i = 0; i < 20; ++i)
		{
			his_rank_count.add_zone_rank_counts(zone_season_count[i]);
		}
		ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().UpdateHistoryRankCount(his_rank_count);
	}

	// 名人堂排行榜更新
	if (sync_info.has_arena_team() && sync_info.arena_team().has_single_rank())
	{
		DSTPManager::GetInstance().Clear(TPN_ARENA_GROUP_SINGLE_RANK, false/*don't save*/);
		PB::toplist_addon_data_t addon;
		for (const auto& rank_info : sync_info.arena_team().single_rank().records())
		{
			addon.mutable_arenagroup_single()->CopyFrom(rank_info.info());
			DSTPManager::GetInstance().UpdateItem(TPN_ARENA_GROUP_SINGLE_RANK, rank_info.info().roleid(), (int64_t)rank_info.score(),
			                                      Octets(rank_info.info().name().c_str(), rank_info.info().name().size()),
			                                      rank_info.info().level(), rank_info.info().prof(), true, addon, rank_info.info().photo_id());
		}
	}
}

int CenterArenaTeamBattleClient::GetRankHistoryTopId(int arena_zone_id, int season_before)
{
	if (season_before >= CENTER_AREAN_TEAM_MAX_HISTORY_RANK_COUNT)
	{
		return 0;
	}
	// 每个战队保留12个赛季的排行数据
	return TPN_ARENA_GROUP_HIS_RANK_START + arena_zone_id * CENTER_AREAN_TEAM_MAX_HISTORY_RANK_COUNT + season_before;
}

