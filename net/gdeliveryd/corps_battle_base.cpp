#include "corps_battle_base.h"
#include "script_wrapper.h"
#include "gprotoc/role_brief.pb.h"
#include "gprotoc/ipt_create_instance.pb.h"
#include "gprotoc/enter_instance_config.pb.h"
#include "gprotoc/npt_player_search_corps_battle_list_result.pb.h"
#include "gprotoc/corps_battle_config.pb.h"
#include "gprotoc/npt_player_search_corps_battle_list.pb.h"
#include "gprotoc/CORPS_SEASON_ACTIVITY_TYPE.pb.h"
#include "gprotoc/corps_battle_state.pb.h"
#include "gprotoc/ipt_corps_center_battle_update.pb.h"
#include "gprotoc/corps_battle_record.pb.h"
#include "gprotoc/corps_battle_order.pb.h"
#include "gprotoc/toplist_addon_data_t.pb.h"
#include "gprotoc/ipt_deliver_corps_season_repu.pb.h"
#include "gprotoc/corps_battle3_record_info.pb.h"
#include "gprotoc/corps_battle_info.pb.h"
#include "gprotoc/name_roleid_pair.pb.h"

#include "instancemanager.h"
#include "gamedbclient.hpp"
#include "dbloadcorpsbattle.hrp"
#include "dbsavecorpsbattle.hrp"
#include "tpmanager.h"
#include "corps_season_manager.h"
#include "tpmanager.h"
#include "brief_manager.h"

#include <random>
#include <vector>

namespace GNET
{
#define GET_BATTLE_INFO(key,id1,id2)\
	if(!lw.gExec("GetCorpsBattleInfo",LuaParameter(m_battle_type,key,id1,id2)))\
	{\
		LOG_TRACE("初始化竞技场执行脚本获取%s出错,battle_id=%d,id1=%d,id2=%d,err_msg=%s\n", \
				key, m_battle_type, id1, id2, lw.ErrorMsg());\
		return false;\
	}\
	if (!lua_isnumber(L,-1))\
	{\
		LOG_TRACE("初始化竞技场执行脚本获取%s出错,battle_id=%d,id1=%d,id2=%d\n", key, m_battle_type, id1, id2);\
		return false;\
	}

bool CorpsBattleEntryBase::Init(lua_State *L, LuaWrapper& lw)
{
	GET_BATTLE_INFO("battle_maps", -1, -1);
	int battle_maps_count = LUA_TOINTEGER(L, -1);
	for (int i = 1; i <= battle_maps_count; ++i)
	{
		GET_BATTLE_INFO("battle_maps", i, -1);
		int battle_tid = LUA_TOINTEGER(L, -1);
		m_battle_tids.push_back(battle_tid);
	}

	GET_BATTLE_INFO("battle_continue_time", -1, -1);
	m_battle_continue_time = LUA_TOINTEGER(L, -1);
	if (m_battle_continue_time <= 300)
	{
		LOG_TRACE("CorpsBattleEntryBase::Init failed. m_battle_continue_time=%d\n", m_battle_continue_time);
		return false;
	}

	GET_BATTLE_INFO("battle_count_max", -1, -1);
	m_battle_count_max = LUA_TOINTEGER(L, -1);
	if (m_battle_count_max < 1 || m_battle_count_max > 2000)
	{
		LOG_TRACE("CorpsBattleEntryBase::Init failed. m_battle_count_max =%d\n", m_battle_count_max);
		return false;
	}

	GET_BATTLE_INFO("battle_match_time", -1, -1);
	int battle_match_time_count = LUA_TOINTEGER(L, -1);
	if (battle_match_time_count <= 0)
	{
		LOG_TRACE("CorpsBattleEntryBase::Init failed. battle_match_time is empty.\n");
		return false;
	}
	for (int i = 1; i <= battle_match_time_count; ++i)
	{
		CorpsBattleTime cbt;
		memset(&cbt, 0, sizeof(cbt));

		GET_BATTLE_INFO("battle_match_time", i, 1);
		cbt.weekday = LUA_TOINTEGER(L, -1);
		if (cbt.weekday < 1 || cbt.weekday > 7)
		{
			LOG_TRACE("CorpsBattleEntryBase::Init failed. weekday=%d\n", cbt.weekday);
			return false;
		}

		GET_BATTLE_INFO("battle_match_time", i, 2);
		cbt.hour = LUA_TOINTEGER(L, -1);
		if (cbt.hour < 0 || cbt.hour > 23)
		{
			LOG_TRACE("CorpsBattleEntryBase::Init failed. hour=%d\n", cbt.hour);
			return false;
		}

		GET_BATTLE_INFO("battle_match_time", i, 3);
		cbt.min = LUA_TOINTEGER(L, -1);
		if (cbt.min < 0 || cbt.hour > 59)
		{
			LOG_TRACE("CorpsBattleEntryBase::Init failed. min=%d\n", cbt.min);
			return false;
		}

		LOG_TRACE("CorpsBattleEntryBase::Init battle_match_time weekday:%d hour:%d min:%d\n",
		          cbt.weekday, cbt.hour, cbt.min);

		m_battle_match_time.push_back(cbt);
	}

	GET_BATTLE_INFO("battle_begin_time", -1, -1);
	int battle_begin_time_count = LUA_TOINTEGER(L, -1);
	if (battle_begin_time_count != battle_match_time_count)
	{
		LOG_TRACE("CorpsBattleEntryBase::Init failed. battle_begin_time_count != battle_match_time_count.\n");
		return false;
	}
	for (int i = 1; i <= battle_begin_time_count; ++i)
	{
		CorpsBattleTime cbt;
		memset(&cbt, 0, sizeof(cbt));

		const CorpsBattleTime& cbt_match = m_battle_match_time[i - 1];

		GET_BATTLE_INFO("battle_begin_time", i, 1);
		cbt.weekday = LUA_TOINTEGER(L, -1);
		if (cbt.weekday < 1 || cbt.weekday > 7 || cbt_match.weekday > cbt.weekday)
		{
			LOG_TRACE("CorpsBattleEntryBase::Init failed. weekday=%d match_weekday=%d\n", cbt.weekday, cbt_match.weekday);
			return false;
		}

		GET_BATTLE_INFO("battle_begin_time", i, 2);
		cbt.hour = LUA_TOINTEGER(L, -1);
		if (cbt.hour < 0 || cbt.hour > 23)
		{
			LOG_TRACE("CorpsBattleEntryBase::Init failed. hour=%d\n", cbt.hour);
			return false;
		}

		GET_BATTLE_INFO("battle_begin_time", i, 3);
		cbt.min = LUA_TOINTEGER(L, -1);
		if (cbt.min < 0 || cbt.hour > 59)
		{
			LOG_TRACE("CorpsBattleEntryBase::Init failed. hour=%d min=%d battle_continue_time=%d\n",
			          cbt.hour, cbt.min, m_battle_continue_time);
			return false;
		}

		m_battle_begin_time.push_back(cbt);
	}

	return true;
}
#undef GET_BATTLE_INFO

bool CorpsBattleEntryBase::Update(int now_time, int day_begin, int week_begin)
{
	if (m_battle_total_time.empty())
	{
		InitBattleTotalTime(week_begin);
	}

	if (m_config.timestamp() < week_begin)
	{
		//保存的时间戳比本周开始时间早，进入新的一周了
		LOG_TRACE("CorpsBattleEntryBase::Update::battle_type=%d:week_begin=%d:config_week_begin=%d", m_battle_type, week_begin, m_config.timestamp());
		m_config.set_timestamp(week_begin);
		OnNewWeek(week_begin);
		m_config_dirty = true;
	}

	//每天的day_begin + 60 + g_zoneid % 100这个时间点开始处理OnNewDay逻辑，
	//防止中心服一次性收到过多的协议，也防止中心服与本服时间不一致
	if (now_time > day_begin + 60 + g_zoneid % 100)
	{
		if (m_config.day_timestamp() < day_begin)
		{
			LOG_TRACE("CorpsBattleEntryBase::Update::battle_type=%d:day_begin=%d:config_day_begin=%d", m_battle_type, day_begin, m_config.timestamp());
			m_config.set_day_timestamp(day_begin);
			OnNewDay(day_begin);
			m_config_dirty = true;
		}
	}
	JudgeState(now_time);
	TrySave();
	return true;
}
bool CorpsBattleEntryBase::InitBattleTotalTime(int week_begin)
{
	m_battle_total_time.clear();
	size_t size = m_battle_match_time.size();
	for (size_t i = 0; i < size; ++i)
	{
		CorpsBattleTime& cbt = m_battle_match_time[i];
		int timestamp = week_begin + SECOND_PER_DAY * (cbt.weekday - 1) + 3600 * cbt.hour + 60 * cbt.min;
		m_battle_total_time.push_back(timestamp);
		LOG_TRACE("DS::CorpsBattleEntryBase::InitBattleTotalTime:InitMatchTime:battle_type=%d:week_begin=%d:weekday=%d:hour=%d:min=%d:timestamp=%d",
		          GetBattleType(), week_begin, cbt.weekday, cbt.hour, cbt.min, timestamp);
	}
	for (size_t i = 0; i < size; ++i)
	{
		CorpsBattleTime& cbt = m_battle_begin_time[i];
		int timestamp = week_begin + SECOND_PER_DAY * (cbt.weekday - 1) + 3600 * cbt.hour + 60 * cbt.min;
		m_battle_total_time.push_back(timestamp);
		LOG_TRACE("DS::CorpsBattleEntryBase::InitBattleTotalTime:InitBeginTime:battle_type=%d:week_begin=%d:weekday=%d:hour=%d:min=%d:timestamp=%d",
		          GetBattleType(), week_begin, cbt.weekday, cbt.hour, cbt.min, timestamp);
		int end_timestamp = timestamp + m_battle_continue_time;
		m_battle_total_time.push_back(end_timestamp);
		LOG_TRACE("DS::CorpsBattleEntryBase::InitBattleTotalTime:InitEndTime:battle_type=%d:week_begin=%d:weekday=%d:hour=%d:min=%d:timestamp=%d",
		          GetBattleType(), week_begin, cbt.weekday, cbt.hour, cbt.min, end_timestamp);
	}
	m_battle_total_time.push_back(week_begin);

	//进行一次排序，这样一定是未开始阶段，匹配阶段，竞赛开始阶段，未开始阶段，匹配阶段，竞赛开始阶段。。。
	std::sort(m_battle_total_time.begin(), m_battle_total_time.end());
	return true;
}

void CorpsBattleEntryBase::ClearBattleOrder()
{
	LOG_TRACE("CorpsBattleEntryBase::ClearBattleOrder::battle_type=%d:m_corps_battle_orders.empty=%s", GetBattleType(), m_corps_battle_orders.empty() ? "true" : "false");

	if (!m_corps_battle_orders.empty())
	{
		size_t origin_orders_size = m_corps_battle_orders.size();
		m_corps_battle_orders.clear();
		m_order_dirty = true;
		LOG_TRACE("DS::CorpsBattleEntryBase::ClearBattleOrder::battle_type=%d:origin_orders_size=%lu", GetBattleType(), origin_orders_size);
	}
}
void CorpsBattleEntryBase::ClearBattleInfo()
{
	LOG_TRACE("CorpsBattleEntryBase::ClearBattleInfo::battle_type=%d:m_corps_battle_infos.empty=%s:m_battle_info_count=%d",
	          GetBattleType(), m_corps_battle_orders.empty() ? "true" : "false", m_battle_info_count);

	if (!m_corps_battle_infos.empty() || m_battle_info_count > 0)
	{
		LOG_TRACE("DS::CorpsBattleEntryBase::ClearBattleInfo::battle_type=%d:origin_infos_size=%zu", GetBattleType(), m_corps_battle_infos.size());
		m_corps_battle_infos.clear();
		m_corps_battle_ids.clear();
		m_battle_info_count = 0;
		m_info_dirty = true;
	}
}
void CorpsBattleEntryBase::OnNewDay(int day_begin)
{
	LOG_TRACE("CorpsBattleEntryBase::OnNewDay::battle_type=%d:day_begin=%d", GetBattleType(), day_begin);
}
//在OnNewWeek的时候，会清空对战表
void CorpsBattleEntryBase::OnNewWeek(int week_begin)
{
	LOG_TRACE("CorpsBattleEntryBase::OnNewWeek::battle_type=%d:week_begin=%d", GetBattleType(), week_begin);

	InitBattleTotalTime(week_begin);

	ClearBattleOrder();

	for (auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end(); it != eit; ++it)
	{
		CorpsBattleInfo& info = it->second;
		if (info.battle_info.order_index() || info.battle_info.last_order_index())
		{
			info.battle_info.set_order_index(0);
			info.battle_info.set_last_order_index(0);
			info.dirty = true;
			if (!m_info_dirty)
			{
				m_info_dirty = true;
			}
		}
	}
	//跨周时，将所有的在线社团的状态设置未未匹配状态
	int battle_type = GetBattleType();
	CorpsManager::GetInstance().ForEachCorps([battle_type](CorpsPtr pCorps)
	{
		if (!pCorps)
		{
			return;
		}
		pCorps->UpdateBattleState(battle_type, PB::corps_battle_state::BS_UN_MATCH);
	});
}

PB::corps_battle_config::CORPS_BATTLE_STATE CorpsBattleEntryBase::GetNowState(int now_time) const
{
	if (m_battle_total_time.empty())
	{
		return PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR;
	}

	size_t i = 1;
	for ( ; i < m_battle_total_time.size(); ++i)
	{
		if (now_time >= m_battle_total_time[i - 1] && now_time < m_battle_total_time[i])
		{
			break;
		}
	}

	if (i == m_battle_total_time.size())
	{
		return PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR;
	}

	return (PB::corps_battle_config::CORPS_BATTLE_STATE)(((i - 1) % 3) + 1);
}
int CorpsBattleEntryBase::GetEndBattleTime(int now_time) const
{
	if (m_battle_total_time.empty())
	{
		return 0;
	}
	auto now_stat = GetNowState(now_time);
	if (now_stat != PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
	{
		return 0;
	}

	size_t i = 1;
	for ( ; i < m_battle_total_time.size(); ++i)
	{
		if (now_time >= m_battle_total_time[i - 1] && now_time < m_battle_total_time[i])
		{
			break;
		}
	}

	if (i == m_battle_total_time.size())
	{
		return 0;
	}

	return m_battle_total_time.at(i);
}
int CorpsBattleEntryBase::GetBattleTimeIndex(int battle_time)
{
	if (m_battle_total_time.empty())
	{
		return 0;
	}

	size_t i = 1;
	for ( ; i < m_battle_total_time.size(); ++i)
	{
		if (battle_time >= m_battle_total_time[i - 1] && battle_time < m_battle_total_time[i])
		{
			break;
		}
	}

	if (i == m_battle_total_time.size())
	{
		return 0;
	}

	return (i / 3);
}
//社团竞赛，社团爬塔都是用这个规则
int CorpsBattleEntryBase::CalcFirstBattleLevel(int activity) const
{
	int init_level = 1;
	if (activity <= 150)
	{
		init_level = 2;
	}
	else if (activity > 150 && activity <= 300)
	{
		init_level = 3;
	}
	else if (activity > 300)
	{
		init_level = 4;
	}

	LOG_TRACE("CorpsBattleEntryBase::CalcFirstBattleLevel:activity=%d:init_level=%d", activity, init_level);

	return init_level;
}
/*
 *计算下一场战斗的时间，默认每60秒中开启40场战斗
 */
int CorpsBattleEntryBase::GetNextBattleTime(int now_time, int battle_index) const
{
	if (m_battle_total_time.empty())
	{
		return 0;
	}

	size_t i = 1;
	for ( ; i < m_battle_total_time.size(); ++i)
	{
		if (now_time >= m_battle_total_time[i - 1] && now_time < m_battle_total_time[i] && (i % 3 + 1) == PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
		{
			break;
		}
	}
	if (i < m_battle_total_time.size())
	{
		return m_battle_total_time[i] + ((battle_index - 1) / 40) * 60;
	}
	else
	{
		return 0;
	}
}
/*
 *merged_id为被合并社团的id
 *merge_id为接收社团的id
 */
void CorpsBattleEntryBase::OnCorpsBeMerged(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set)
{
	int now_time = Timer::GetTime();
	PB::corps_battle_config::CORPS_BATTLE_STATE state = GetNowState(now_time);
	LOG_TRACE("CorpsBattleEntryBase::OnCorpsBeMerged::battle_type=%d:merged_id=%ld:merge_id=%ld:be_merged_set=%zu:state=%d",
	          GetBattleType(), merged_id, merge_id, be_merged_set.size(), (int)state);
	switch (state)
	{
	case PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR:
	{
		OnCorpsBeMergedDuringBattleClosed(merged_id, merge_id, be_merged_set);
	}
	break;

	case PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED:
	{
		OnCorpsBeMergedDuringBattleMatched(merged_id, merge_id, be_merged_set);
	}
	break;

	case PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN:
	{
		OnCorpsBeMergedDuringBattleBegin(merged_id, merge_id, be_merged_set);
	}
	break;

	}
}
/*
 *由于社团战场匹配规则是在匹配时，收集在线的社团，
 *因此如果一个社团在匹配前被合帮，那么这个消失的社团不会出现在战场的匹配中
 */
void CorpsBattleEntryBase::OnCorpsBeMergedDuringBattleClosed(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set)
{
	LOG_TRACE("CorpsBattleEntryBase::OnCorpsBeMergedDuringBattleClosed::battle_type=%d:merged_id=%ld:merge_id=%ld", GetBattleType(), merged_id, merge_id);

}
/*
 * 防止对手查看不到信息，需要把数据设置上
 */
void CorpsBattleEntryBase::OnCorpsBeMergedDuringBattleMatched(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set)
{
	LOG_TRACE("CorpsBattleEntryBase::OnCorpsBeMergedDuringBattleMatched::battle_type=%d:merged_id=%ld:merge_id=%ld", GetBattleType(), merged_id, merge_id);

	auto it = m_corps_battle_ids.find(merged_id);
	if (it == m_corps_battle_ids.end())
	{
		return;
	}
	int corps_index = it->second;

	auto iter = m_corps_battle_infos.find(corps_index);

	if (iter == m_corps_battle_infos.end())
	{
		return;
	}

	auto& info = iter->second;

	CorpsPtr pCorps = CorpsManager::GetInstance().GetCorp(merged_id);
	if (!pCorps)
	{
		return;
	}

	info.battle_info.set_name(pCorps->Name());
	info.battle_info.set_badge(pCorps->Badge());

	LOG_TRACE("CorpsBattleEntryBase::OnCorpsBeMergedDuringBattleMatched::battle_type=%d:merged_id=%ld:bedge=%d:name=%s",
	          GetBattleType(), merged_id, info.battle_info.badge(), info.battle_info.name().c_str());

	info.dirty = true;
	m_info_dirty = true;
}
void CorpsBattleEntryBase::OnCorpsBeMergedDuringBattleBegin(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set)
{
	LOG_TRACE("CorpsBattleEntryBase::OnCorpsBeMergedDuringBattleBegin::battle_type=%d:merged_id=%ld:merge_id=%ld", GetBattleType(), merged_id, merge_id);

}
void CorpsBattleEntryBase::JudgeState(int now_time)
{
	if (DebugMode())
	{
		//如果是debug模式，那么手动设置战斗状态
		return;
	}
	//在判断状态开启匹配的时候，需要判断社团是否加载成功，如果没有加载成功，需要等加载成功之后在开启
	if (!CorpsManager::GetInstance().IsActivityCorpsLoadFinished() && !CenterManager::GetInstance().IsCenter())
	{
		return;
	}

	//检查功能码
	if (!GET_FUNC_SWITCH(GetFuncCode()))
	{
		return;
	}

	{
		// 社团竞赛2 灰度开关开启，周二打社团竞赛2，灰度开关关闭，周二打社团竞赛
		int wday = GetLocalWDay();
		if (wday == 1 || wday == 2)
		{
			if (GET_FUNC_SWITCH(kFuncCodeCorpsBattle2GrayScale))
			{
				if (GetBattleType() == CORPS_BATTLE_STUB_TYPE_COMMON)
				{
					return;
				}
			}
			else
			{
				if (GetBattleType() == CORPS_BATTLE2_STUB_TYPE_COMMON)
				{
					return;
				}
			}
		}

		// 社团竞赛3 灰度开关开启，周四打社团竞赛3，灰度开关关闭，周四打社团竞赛
		if (wday == 3 || wday == 4)
		{
			if (GET_FUNC_SWITCH(kFuncCodeCorpsBattle3GrayScale))
			{
				if (GetBattleType() == CORPS_BATTLE_STUB_TYPE_COMMON)
				{
					return;
				}
			}
			else
			{
				if (GetBattleType() == CORPS_BATTLE3_STUB_TYPE_COMMON)
				{
					return;
				}
			}
		}
	}

	PB::corps_battle_config::CORPS_BATTLE_STATE state = GetNowState(now_time);
	if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
		{
			//进入匹配阶段了
			LOG_TRACE("CorpsBattleEntryBase::JudgeState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_CLEAR->CORPS_BATTLE_STATE_MATCHED", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			MatchBattle(now_time);
		}
	}
	else if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR)
		{
			//跳过了战斗开始阶段，那就清了吧
			LOG_TRACE("CorpsBattleEntryBase::JudgeState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_MATCHED->CORPS_BATTLE_STATE_CLEAR", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;
		}
		else if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
		{
			//进入战斗开始阶段了
			LOG_TRACE("CorpsBattleEntryBase::JudgeState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_MATCHED->CORPS_BATTLE_STATE_BEGIN", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			BeginBattle(now_time);
		}
	}
	else if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR || state == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
		{
			//进入战斗结束阶段
			LOG_TRACE("CorpsBattleEntryBase::JudgeState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_BATTLE_BEGIN->CORPS_BATTLE_STATE_CLEAR", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			EndBattle(now_time);
			//战斗结束，也需要将所有的社团设置未未匹配状态
			int battle_type = GetBattleType();
			CorpsManager::GetInstance().ForEachCorps([battle_type](CorpsPtr pCorps)
			{
				if (!pCorps)
				{
					return;
				}
				pCorps->UpdateBattleState(battle_type, PB::corps_battle_state::BS_UN_MATCH);
			});
		}
		else
		{
			//尝试创建战场
			//由于某些战场是依次开启战斗，因此需要在JudgeState中循环开启战斗
			BeginBattle2(now_time);
		}
	}
}

bool CorpsBattleEntryBase::TrySave()
{
	if (m_save_db)
	{
		return false;
	}

	bool need_save = false;
	DBSaveCorpsBattleArg arg(m_battle_type);
	if (m_config_dirty || m_order_dirty || m_info_dirty)
	{
		need_save = true;
		m_config_dirty = false;

		arg.battle_config.resize(m_config.ByteSize());
		m_config.SerializeWithCachedSizesToArray((uint8_t *)arg.battle_config.begin());
		LOG_TRACE("CorpsBattleEntryBase::TrySave:battle_type=%d:state=%d:timestamp=%d", GetBattleType(), (int)m_config.state(), m_config.timestamp());
	}

	arg.battle_order_count = -1;
	if (m_order_dirty)
	{
		m_order_dirty = false;

		arg.battle_order_count = (int)m_corps_battle_orders.size();
		auto it = m_corps_battle_orders.begin(), eit = m_corps_battle_orders.end();
		for ( ; it != eit; ++it)
		{
			if (!it->second)
			{
				continue;
			}
			CorpsBattleOrderBase& order = *(it->second);
			if (order.dirty)
			{
				order.dirty = false;
				Octets data;
				data.resize(order.battle_order.ByteSize());
				order.battle_order.SerializeWithCachedSizesToArray((uint8_t *)data.begin());
				LOG_TRACE("DS::CorpsBattle:TrySave::battle_order::data_size=%lu", data.size());
				arg.battle_order.push_back(data);
			}
		}
	}

	arg.battle_info_count = -1;
	if (m_info_dirty)
	{
		m_info_dirty = false;

		arg.battle_info_count = m_battle_info_count;
		auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
		for ( ; it != eit; ++it)
		{
			CorpsBattleInfo& info = it->second;
			if (info.dirty)
			{
				info.dirty = false;
				Octets data;
				data.resize(info.battle_info.ByteSize());
				info.battle_info.SerializeWithCachedSizesToArray((uint8_t *)data.begin());
				arg.battle_info.push_back(data);
				LOG_TRACE("BATTLE_STATE_DEBUG::TrySave:corps_id=%ld:index=%d:battle_state=%d",
				          info.battle_info.id(), info.battle_info.index(), info.battle_info.battle_state());
			}
		}
	}

	if (need_save)
	{
		m_save_db = true;
		LOG_TRACE("CorpsBattleEntryBase::TrySave::battle_type=%d:battle_order_count=%d:battle_info_count=%d", arg.battle_type, arg.battle_order_count, arg.battle_info_count);
		DBSaveCorpsBattle *rpc = (DBSaveCorpsBattle *)Rpc::Call(RPC_DBSAVECORPSBATTLE, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
	return true;
}

void CorpsBattleEntryBase::OnDBSave(int ret)
{
	if (!m_save_db)
	{
		return;
	}

	if (ret)
	{
		Log::log(LOG_ERR, "DS::CorpsBattleManager::OnDBSave battle_type:%d ret:%d", m_battle_type, ret);
	}

	m_save_db = false;
}

void CorpsBattleEntryBase::OnDBLoad(int handle_index, const std::string& prefix, std::vector<Octets>& data)
{
	LOG_TRACE("CorpsBattleEntryBase::OnDBLoad type=%d handle_index=%d size=%zu prefix=%.*s",
	          m_battle_type, handle_index, data.size(), (int)prefix.size(), prefix.c_str());

	if (prefix.compare(CORPS_BATTLE_CONFIG) == 0)
	{
		if (data.size() == 1)
		{
			if (!m_config.ParseFromArray((char *)data[0].begin(), data[0].size()))
			{
				Log::log(LOG_ERR, "DS::CorpsBattleEntryBase::OnDBLoad config parse error. type=%d.", m_battle_type);
			}
			else
			{
				OnLoadCorpsBattleConfig();
			}
		}
		else if (!data.empty())
		{
			Log::log(LOG_ERR, "DS::CorpsBattleEntryBase::OnDBLoad config size error. type=%d size=%zu.", m_battle_type, data.size());
		}
	}
	else if (prefix.compare(CORPS_BATTLE_ORDER) == 0)
	{
		auto it = data.begin(), eit = data.end();
		for ( ; it != eit; ++it)
		{
			auto pOrder = CreateBattleOrder();
			if (pOrder->battle_order.ParseFromArray((char *)(*it).begin(), (*it).size()))
			{
				m_corps_battle_orders[pOrder->battle_order.index()] = pOrder;
				OnLoadBattleOrder(pOrder);
				LOG_TRACE("DS::CorpsBattleEntryBase::OnDBLoad order. index=%d, info1=%d, info2=%d, corps_index=%s, battle_time=%d, result=%d",
				          pOrder->battle_order.index(), pOrder->battle_order.corps_battle_index_1(), pOrder->battle_order.corps_battle_index_2(), pOrder->GetCorpsIndexStr().c_str(),
				          pOrder->battle_order.battle_time(), pOrder->battle_order.result());
			}
			else
			{
				Log::log(LOG_ERR, "DS::CorpsBattleEntryBase::OnDBLoad parse order error. type=%d.", m_battle_type);
			}
		}
	}
	else if (prefix.compare(CORPS_BATTLE_INFO) == 0)
	{
		auto it = data.begin(), eit = data.end();
		for ( ; it != eit; ++it)
		{
			CorpsBattleInfo info;
			if (info.battle_info.ParseFromArray((char *)(*it).begin(), (*it).size()))
			{
				__InsertBattleInfo(info);

				OnLoadCorpsBattleInfo(info);
                LOG_TRACE("DS::CorpsBattleEntryBase::OnDBLoad info BATTLE_STATE_DEBUG. corps_id=%ld:index=%d:battle_state=%d, battle_level=%d",
					info.battle_info.id(), info.battle_info.index(), info.battle_info.battle_state(), info.battle_info.battle_level());
			}
			else
			{
				Log::log(LOG_ERR, "DS::CorpsBattleEntryBase::OnDBLoad parse info error. type=%d.", m_battle_type);
			}
		}

		if (data.empty())
		{
			m_battle_info_count = handle_index > 0 ? handle_index - 1 : 0;
			LOG_TRACE("DS::CorpsBattleEntryBase::OnDBLoad finish. prefix=%.*s, type=%d, size=%zu, index=%d",
			          (int)prefix.size(), prefix.c_str(), m_battle_type, m_corps_battle_infos.size(), handle_index);

			OnLoadCorpsBattleFinished();

			CorpsManager::GetInstance().OnBattleOrderLoadFinish(m_battle_type);
		}
	}
}

static void DumpMatchInfo(const std::vector<CorpsBattleMatchInfo>& battle_match_infos)
{
	size_t size = battle_match_infos.size();
	for (size_t i = 0; i < size; ++i)
	{
		const CorpsBattleMatchInfo& match_info = battle_match_infos[i];
		LOG_TRACE("DumpMatchInfo:i=%ld:corps_id=%ld:battle_info_index=%d:battle_level=%d:activity=%d\n",
		          i, match_info.corps_id, match_info.battle_info_index, match_info.battle_level, match_info.activity);
	}
}
//社团爬塔，社团竞赛使用的规则，使用社团当前活跃度与上周活跃度的最大值作为排序的key
//如果其他社团战场需要其他的规则，请自行override
void CorpsBattleEntryBase::InitMatchInfo(CorpsPtr pCorp, CorpsBattleMatchInfo& match_info)
{
	match_info.corps_id = pCorp->ID();
	match_info.corps_name = pCorp->Data().data().name();
	match_info.corps_badge = pCorp->Badge();
	match_info.corps_support = pCorp->SupportSide();

	//策划希望使用本周活跃度与上周活跃度的最大值作为计算初始等级以及排序时使用的活跃度数值
	int activity = pCorp->GetReputation(CRID_ACTIVITY);
	int activity_last_week = pCorp->GetReputation(CRID_LASTWEEK_ACTIVITY);
	match_info.bak_activity = activity;
	match_info.bak_lastweek_activity = activity_last_week;
	match_info.bak_corps_level = pCorp->Level();
	match_info.activity = std::max(activity, activity_last_week);

	const CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(match_info.corps_id);
	bool first_match = false;
	if (pInfo)
	{
		match_info.battle_info_index = pInfo->battle_info.index();
		match_info.battle_level = GetBattleLevel(*pInfo);
	}
	else
	{
		//该帮派第一次进行匹配
		match_info.battle_level = CalcFirstBattleLevel(std::max(activity, activity_last_week));
		first_match = true;
	}
	LOG_TRACE("CorpsBattleEntryBase::InitMatchInfo:battle_type=%d:corps_id=%ld:corps_badge=%d:battle_info_index=%d:activity=%d:activity_last_week=%d:battle_level=%d:first_match=%s:score=%d",
	          GetBattleType(), pCorp->ID(), pCorp->Badge(), match_info.battle_info_index, activity, activity_last_week, match_info.battle_level,
	          first_match ? "true" : "false", match_info.score);

}

void CorpsBattleEntryBase::HandleAllCorps(std::vector<CorpsBattleMatchInfo>& battle_match_infos)
{
	int now_time = Timer::GetTime();
	LOG_TRACE("CorpsBattleEntryBase::HandleAllCorps::battle_type=%d:now_time=%d", m_battle_type, now_time);
	CorpsManager::GetInstance().ForEachCorps([&battle_match_infos, this](CorpsPtr pCorp)
	{
		if (!pCorp)
		{
			return;
		}

		CorpsBattleMatchInfo match_info;
		this->InitMatchInfo(pCorp, match_info);
		battle_match_infos.emplace_back(match_info);
	});
}

void CorpsBattleEntryBase::CollectMatchInfo(std::vector<CorpsBattleMatchInfo>& battle_match_infos)
{
	LOG_TRACE("CorpsBattleEntryBase::CollectMatchInfo:battle_type=%d", GetBattleType());

	HandleAllCorps(battle_match_infos);

	//按照天梯积分，战斗等级，活跃度，帮派ID的顺序从小到大进行排序
	struct foo2
	{
		static bool greater(const CorpsBattleMatchInfo& info1, const CorpsBattleMatchInfo& info2)
		{
			if (info1.score > info2.score)
			{
				return true;
			}
			else if (info1.score == info2.score)
			{
				if (info1.battle_level > info2.battle_level)
				{
					return true;
				}
				else if (info1.battle_level == info2.battle_level)
				{
					if (info1.activity > info2.activity)
					{
						return true;
					}
					else if (info1.activity == info2.activity)
					{
						return info1.corps_id > info2.corps_id;
					}
				}
			}
			return false;
		}
	};
	std::sort(battle_match_infos.begin(), battle_match_infos.end(), foo2::greater);

	//输出一下
	DumpMatchInfo(battle_match_infos);

}
int CorpsBattleEntryBase::ShuffleMatchInfo(std::vector<CorpsBattleMatchInfo>& battle_match_infos)
{
	LOG_TRACE("CorpsBattleEntryBase::ShuffleMatchInfo:battle_type=%d:match_infos.size=%lu", GetBattleType(), battle_match_infos.size());

	//给匹配上的前多少名保存匹配信息
	int battle_count = m_battle_count_max;
	int count = battle_count << 1;
	if (count > (int)battle_match_infos.size())
	{
		count = (int)battle_match_infos.size();
	}
	if ((count & (int)0x01) != 0)
	{
		count &= ~((int)0x01);
	}

	if (!count)
	{
		return 0;    //本周没有帮派能匹配
	}

	//再从头开始每5个进行一下乱序
	int shuffle_times = (count - 1) / 5;
	int i = 0;
	for ( ; i < shuffle_times; ++i)
	{
		std::random_shuffle(battle_match_infos.begin() + 5 * i, battle_match_infos.begin() + 5 * (i + 1) - 1);
	}
	if ((count - 1) % 5)
	{
		std::random_shuffle(battle_match_infos.begin() + 5 * i, battle_match_infos.begin() + 5 * i + (count - 1) % 5);
	}

	battle_match_infos.resize(count);

	DumpMatchInfo(battle_match_infos);

	return count;
}
void CorpsBattleEntryBase::ClearBattleInfoOrder()
{
	LOG_TRACE("CorpsBattleEntryBase::ClearBattleInfoOrder::battle_type=%d", GetBattleType());
	auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
	for ( ; it != eit; ++it)
	{
		CorpsBattleInfo& info = it->second;
		if (info.battle_info.order_index() > 0)
		{
			if (m_battle_type == CORPS_BATTLE_STUB_TYPE_COMMON || m_battle_type == CORPS_BATTLE2_STUB_TYPE_COMMON || m_battle_type == CORPS_BATTLE3_STUB_TYPE_COMMON)
			{
				info.battle_info.set_last_order_index(info.battle_info.order_index());
			}
			info.battle_info.set_order_index(0);
			info.dirty = true;
			if (!m_info_dirty)
			{
				m_info_dirty = true;
			}
		}
	}

}
int CorpsBattleEntryBase::InitCorpsBattleInfo(const CorpsBattleMatchInfo& match_info)
{
	int battle_index = GetBattleIndex();

	CorpsBattleInfo info;
	info.dirty = true;
	info.bak_activity = match_info.bak_activity;
	info.bak_lastweek_activity = match_info.bak_lastweek_activity;
	info.bak_corps_level = match_info.bak_corps_level;
	info.battle_info.set_index(battle_index);
	info.battle_info.set_id(match_info.corps_id);
	info.battle_info.set_name(std::string((char *)match_info.corps_name.begin(), match_info.corps_name.size()));
	info.battle_info.set_badge(match_info.corps_badge);
	info.battle_info.set_support_side(match_info.corps_support);
	info.battle_info.set_corps_battle_score(match_info.score);
	SetBattleLevel(info, match_info.battle_level);

	__InsertBattleInfo(info);

	m_info_dirty = true;

	LOG_TRACE("CorpsBattleEntryBase::InitCorpsBattleInfo:battle_type=%d:corps_index=%d:corps_id=%ld:corps_badge=%d:battle_level=%d:battle_score=%d",
	          GetBattleType(), battle_index, match_info.corps_id, match_info.corps_badge, match_info.battle_level, info.battle_info.corps_battle_score());
	return battle_index;
}
int CorpsBattleEntryBase::InitCorpsBattleInfo(int64_t corps_id)
{
	CorpsPtr pCorp = CorpsManager::GetInstance().GetCorp(corps_id);
	if (!pCorp)
	{
		return -1;
	}

	CorpsBattleMatchInfo match_info;
	InitMatchInfo(pCorp, match_info);
	int battle_index = InitCorpsBattleInfo(match_info);
	LOG_TRACE("CorpsBattleEntryBase::InitCorpsBattleInfo 2:battle_type=%d:corps_index=%d:corps_id=%ld:corps_badge=%d:battle_level=%d:battle_score=%d",
	          GetBattleType(), battle_index, match_info.corps_id, match_info.corps_badge, match_info.battle_level, match_info.score);
	return battle_index;
}
bool CorpsBattleEntryBase::IsRecentTarget(const CorpsBattleInfo& corps_info, int target_index, int recent_count) const
{
	int size = corps_info.battle_info.records_size();
	if (size == 0)
	{
		return false;
	}
	size--;
	for (int count = 0; size >= 0 && count < recent_count; size--, count++)
	{
		auto& record = corps_info.battle_info.records(size);
		if (record.target_index() == target_index)
		{
			return true;
		}
	}
	return false;
}
void CorpsBattleEntryBase::GenerateMatchOrder(const std::vector<CorpsBattleMatchInfo>& battle_match_infos, std::set<int64_t>& success_corps_set)
{
	LOG_TRACE("CorpsBattleEntryBase::GenerateMatchOrder::battle_type=%d:battle_match_infos.size=%lu", GetBattleType(), battle_match_infos.size());
	int count = battle_match_infos.size();
	int now_time = Timer::GetTime();
	int index = 0;
	int order_index = 0;
	CorpsBattleInfo *index1Info = NULL;
	for (int i = 0; i < count; ++i)
	{
		int battle_index = 0;
		const CorpsBattleMatchInfo& match_info = battle_match_infos[i];
		auto id_it = m_corps_battle_ids.find(match_info.corps_id);
		if (id_it == m_corps_battle_ids.end())
		{
			//如果这个帮派没有竞赛信息，给生成一个插入
			//这个battle_index实际上就是社团的数量
			battle_index = InitCorpsBattleInfo(match_info);
		}
		else
		{
			battle_index = id_it->second;
		}

		CorpsBattleInfo& info = m_corps_battle_infos[battle_index];
		info.dirty = true;

		if (i % 2 == 0)
		{
			//第一个帮派
			order_index = (int)m_corps_battle_orders.size() + 1;
			++ index;

			auto pOrder = CreateBattleOrder();
			pOrder->dirty = true;
			pOrder->battle_order.set_index(order_index);
			pOrder->battle_order.set_battle_time(GetNextBattleTime(now_time, index));
			pOrder->battle_order.set_corps_battle_index_1(battle_index);
			pOrder->battle_order.set_result(-1);

			m_corps_battle_orders[order_index] = pOrder;

			index1Info = &info;
			//调整一下顺序，先放在map中，然后在初始化
			InitBattleOrder(match_info, pOrder, true);

			LOG_TRACE("CorpsBattleEntryBase::MatchBattle:order_index=%d:corps_id_1=%ld:battle_time=%d", order_index, match_info.corps_id, pOrder->battle_order.battle_time());
		}
		else
		{
			//第2个帮派
			//上面一段代码保证了在order_index的位置一定会存在对应的BattleOrder
			auto pOrder = m_corps_battle_orders[order_index];
			InitBattleOrder(match_info, pOrder, false);
			pOrder->battle_order.set_corps_battle_index_2(battle_index);

			CorpsBattleInfo& info1 = m_corps_battle_infos[pOrder->battle_order.corps_battle_index_1()];

			LOG_TRACE("CorpsBattleEntryBase::MatchBattle:order_index=%d:corps_id_2=%ld:battle_time=%d", order_index, match_info.corps_id, pOrder->battle_order.battle_time());
			success_corps_set.insert(info1.ID());
			success_corps_set.insert(info.ID());
			GLog::formatlog("corps_battle_match", "order_index=%d:battle_time=%d:corps_index_1=%d:corps_id1=%ld:corps_index2=%d:corps_id2=%ld",
			                order_index, pOrder->battle_order.battle_time(), pOrder->battle_order.corps_battle_index_1(), info1.ID(),
			                pOrder->battle_order.corps_battle_index_2(), info.ID());
			PrintMatchLog(index1Info, 1, info.ID());
			PrintMatchLog(&info, 1, index1Info->ID());
			index1Info = NULL;
		}
		info.battle_info.set_order_index(order_index);
	}
	if (index1Info)
	{
		PrintMatchLog(index1Info, 0, 0);
		index1Info = NULL;
	}

}

void CorpsBattleEntryBase::PrintMatchLog(const CorpsBattleInfo *pInfo, int result, ruid_t target_corps_id, const std::map<int, int64_t>& corps_info/* = std::map<int, int64_t>()*/)
{
	DEFINE_slogger(FORMAT, "corps_battle_match_single");
	slogger.P("server_id", g_zoneid)
	.P("app_id", GDeliveryServer::GetInstance()->GetAppid())
	.P("os", GDeliveryServer::GetInstance()->GetOS())
	.P("corps_id", pInfo->ID())
	.P("corps_level", pInfo->bak_corps_level)
	.P("corps_ap_lastweek", pInfo->bak_lastweek_activity)
	.P("corps_ap", pInfo->bak_activity)
	.P("battle_type", m_battle_type)
	.P("match_level", pInfo->battle_info.battle_level())
	.P("match_result", result) //0 失败， 1 成功
	.P("match_corps_id", target_corps_id);

	int i = 1;
	for (auto it = corps_info.begin(); it != corps_info.end(); ++it)
	{
		if (it->second == pInfo->ID())
		{
			continue;
		}
		std::stringstream p_str;
		p_str << "match_corps_" << i;
		std::stringstream pp_str;
		pp_str << "<" << it->first << ", " << it->second << ">";
		slogger.P(p_str.str().c_str(), pp_str.str());
		++i;
	}
}

void CorpsBattleEntryBase::PrintEndLogBase(GNET::LogMessage *slogger, const CorpsBattleInfo *pInfo, int result, ruid_t target_corps_id)
{
	slogger->P("server_id", g_zoneid)
	.P("app_id", GDeliveryServer::GetInstance()->GetAppid())
	.P("os", GDeliveryServer::GetInstance()->GetOS())
	.P("corps_id", pInfo->ID())
	.P("corps_level", pInfo->bak_corps_level)
	.P("battle_type", m_battle_type)
	.P("battle_index", pInfo->OrderIndex())
	.P("result", result) //0 失败， 1 胜利
	.P("match_level", pInfo->battle_info.battle_level())
	.P("match_corps_id", target_corps_id);
}
void CorpsBattleEntryBase::PrintEndLog(const CorpsBattleOrderBasePtr pOrder, const CorpsBattleInfo *pInfo, int result, ruid_t target_corps_id, const std::map<int, std::tuple<int64_t, int>>& corps_rank/* = std::map<int, std::tuple<int64_t, int>>()*/)
{
	DEFINE_slogger(FORMAT, "corps_battle_result_single");
	PrintEndLogBase(&slogger, pInfo, result, target_corps_id);
}

void CorpsBattleEntryBase::FixCorpsInfoBattleIndexByCorpsID(int update_type, PB::corps_battle_order *pbOrder)
{
	if (m_battle_type == CORPS_BATTLE_STUB_TYPE_CENTER || m_battle_type == CORPS_BATTLE_STUB_TYPE_CENTER_TOWER)
	{
		auto it1 = m_corps_battle_ids.find(pbOrder->corps_battle_id_1());
		if (it1 != m_corps_battle_ids.end())
		{
			pbOrder->set_corps_battle_index_1(it1->second);
		}
		else
		{
			pbOrder->set_corps_battle_index_1(0);
			Log::log(LOG_ERR, "CorpsBattleEntryBase::FixCorpsInfoBattleIndexByCorpsID fail for corps1:update_type=%d, battle_type=%d, order_index=%d, corps_battle_id_1=%ld, corps_battle_index_1=%d, corps_battle_id_2=%ld, corps_battle_index_2=%d", update_type, m_battle_type, pbOrder->index(), pbOrder->corps_battle_id_1(), pbOrder->corps_battle_index_1(), pbOrder->corps_battle_id_2(), pbOrder->corps_battle_index_2());
		}
		auto it2 = m_corps_battle_ids.find(pbOrder->corps_battle_id_2());
		if (it2 != m_corps_battle_ids.end())
		{
			pbOrder->set_corps_battle_index_2(it2->second);
		}
		else
		{
			pbOrder->set_corps_battle_index_2(0);
			Log::log(LOG_ERR, "CorpsBattleEntryBase::FixCorpsInfoBattleIndexByCorpsID fail for corps 2:update_type=%d, battle_type=%d, order_index=%d, corps_battle_id_1=%ld, corps_battle_index_1=%d, corps_battle_id_2=%ld, corps_battle_index_2=%d", update_type, m_battle_type, pbOrder->index(), pbOrder->corps_battle_id_1(), pbOrder->corps_battle_index_1(), pbOrder->corps_battle_id_2(), pbOrder->corps_battle_index_2());
		}
	}
	else if (m_battle_type == CORPS_BATTLE3_STUB_TYPE_CENTER)
	{
		for (int i = 0; i < pbOrder->rank_info_size(); ++i)
		{
			auto it = m_corps_battle_ids.find(pbOrder->rank_info(i).id());
			if (it != m_corps_battle_ids.end())
			{
				pbOrder->mutable_rank_info(i)->set_index(it->second);
			}
			else
			{
				pbOrder->mutable_rank_info(i)->set_index(0);
				Log::log(LOG_ERR, "CorpsBattleEntryBase::FixCorpsInfoBattleIndexByCorpsID fail for corps:update_type=%d, battle_type=%d, order_index=%d, corps_battle_id=%ld, corps_battle_index=%d", update_type, m_battle_type, pbOrder->index(), pbOrder->rank_info(i).id(), pbOrder->rank_info(i).index());
			}
		}
	}
	else
	{
		Log::log(LOG_ERR, "CorpsBattleEntryBase::FixCorpsInfoBattleIndexByCorpsID fail for battle_type:update_type=%d, battle_type=%d, order_index=%d", update_type, m_battle_type, pbOrder->index());
	}
}

bool CorpsBattleEntryBase::IsCorpsInMatch(int64_t corps_id) const
{
	LOG_TRACE("CorpsBattleEntryBase::IsCorpsInMatch::corps_id=%ld", corps_id);
	auto *pCorpsInfo = GetBattleInfoByCorpsID(corps_id);
	if (!pCorpsInfo)
	{
		LOG_TRACE("CorpsBattleEntryBase::IsCorpsInMatch::corps_id=%ld::info not found", corps_id);
		return false;
	}

	if (pCorpsInfo->OrderIndex() == 0)
	{
		LOG_TRACE("CorpsBattleEntryBase::IsCorpsInMatch::corps_id=%ld:order index empty", corps_id);
		return false;
	}

	auto iter = m_corps_battle_orders.find(pCorpsInfo->OrderIndex());
	if (iter == m_corps_battle_orders.end())
	{
		return false;
	}
	auto pCorpsOrder = iter->second;
	if (!pCorpsOrder)
	{
		return false;
	}

	LOG_TRACE("CorpsBattleEntryBase::IsCorpsInMatch::corps_id=%ld:order_index=%d:order_result=%d:battle_time=%d",
	          corps_id, pCorpsInfo->OrderIndex(), pCorpsOrder->Result(), pCorpsOrder->BattleTime());

	//不等于-1的话，可能是上一次的对战记录
	if (pCorpsOrder->Result() != -1)
	{
		return false;
	}

	return true;
}
void CorpsBattleEntryBase::MatchBattle(int now_time)
{
	LOG_TRACE("CorpsBattleEntryBase::MatchBattle::battle_type=%d:now_time=%d", GetBattleType(), now_time);

	//服务器等级不大于等于55级，帮派竞技赛不开
	if (!DebugMode() && DSTPManager::GetInstance().GetServerLevel() < CORPS_BATTLE_NEED_SERVER_LEVEL)
	{
		return;
	}

	BeforeMatchBattle(now_time);

	MatchBattle2(now_time);
}

void CorpsBattleEntryBase::MatchBattle2(int now_time)
{
	//本周的匹配记录是在OnNewWeek里清空的，不在这里清空
	LOG_TRACE("CorpsBattleEntryBase::MatchBattle2::battle_type=%d:corps_battle_infos.size=%lu:now_time=%d", GetBattleType(), m_corps_battle_infos.size(), now_time);

	//先把帮派对应的本周竞赛记录清了
	ClearBattleInfoOrder();

	//收集一下全服的帮派信息
	std::vector<CorpsBattleMatchInfo> battle_match_infos;

	CollectMatchInfo(battle_match_infos);

	//按照不同的需求整理收集到的社团数据
	int count = ShuffleMatchInfo(battle_match_infos);
	if (count == 0)
	{
		return;
	}

	//将整理过后的社团数据生成对战表
	std::set<int64_t> success_set;
	GenerateMatchOrder(battle_match_infos, success_set);

	DebugShowBattleOrder();

	int battle_type = GetBattleType();
	CorpsManager::GetInstance().ForEachCorps([battle_type, success_set](CorpsPtr pCorps)
	{
		if (!pCorps)
		{
			return;
		}
		if (success_set.find(pCorps->ID()) == success_set.end())
		{
			pCorps->UpdateBattleState(battle_type, PB::corps_battle_state::BS_FAILED_MATCH);
		}
		else
		{
			pCorps->UpdateBattleState(battle_type, PB::corps_battle_state::BS_SUCCESS_MATCH);
		}
	});

	m_info_dirty = true;
	m_order_dirty = true;
}
void CorpsBattleEntryBase::SyncOrderToNormalServer()
{
	LOG_TRACE("CorpsBattleEntryBase::SyncOrderToNormalServer:battle_type=%d", m_battle_type);
	std::map<int/*zoneid*/, std::set<int/*order_index*/> > zone_order_map;
	auto it = m_corps_battle_orders.begin();
	auto ie = m_corps_battle_orders.end();
	//首先统计各个order对应与那些服务器
	for (; it != ie; ++it)
	{
		auto pOrder = it->second;
		if (!pOrder)
		{
			continue;
		}

		int64_t corps1_id = pOrder->battle_order.corps_battle_id_1();
		int64_t corps2_id = pOrder->battle_order.corps_battle_id_2();
		auto zoneid_1 = MERGE_ZONE(corps1_id);
		auto zoneid_2 = MERGE_ZONE(corps2_id);

		zone_order_map[zoneid_1].insert(pOrder->Index());
		zone_order_map[zoneid_2].insert(pOrder->Index());

		LOG_TRACE("CorpsBattleEntryBase::SyncOrderToNormalServer:battle_type=%d:order_index=%d:corps1_id=%ld:corps2_id=%ld:zoneid_1=%d:zoneid_2=%d",
		          m_battle_type, pOrder->Index(), corps1_id, corps2_id, zoneid_1, zoneid_2);
	}
	//将对应的对战表发到相关的服务器
	auto iter = zone_order_map.begin();
	auto eit = zone_order_map.end();
	for (; iter != eit; ++iter)
	{
		auto& order_set = iter->second;
		LOG_TRACE("CorpsBattleEntryBase::SyncOrderToNormalServer:battle_type=%d:zoneid=%d:order_size=%zu", m_battle_type, iter->first, order_set.size());
		PB::ipt_corps_center_battle_update update;
		update.set_battle_type(m_battle_type);
		update.set_update_type(PB::ipt_corps_center_battle_update::CCBUT_MATCH_RESULT);
		update.set_zoneid(g_zoneid);
		update.set_param(m_corps_battle_orders.size());

		for (auto i = order_set.begin(), e = order_set.end(); i != e; ++i)
		{
			auto order_iter = m_corps_battle_orders.find(*i);
			if (order_iter == m_corps_battle_orders.end() || !(order_iter->second))
			{
				continue;
			}
			auto pOrder = order_iter->second;

			PB::corps_battle_order *pbOrder = update.add_orders();
			pbOrder->CopyFrom(pOrder->battle_order);

			LOG_TRACE("CorpsBattleEntryBase::SyncOrderToNormalServer::SyncOrder:battle_type=%d:order_index=%d:corps1_id=%ld:corps2_id=%ld:zoneid=%d",
			          m_battle_type, pOrder->Index(), pOrder->battle_order.corps_battle_id_1(), pOrder->battle_order.corps_battle_id_2(), iter->first);
		}

		HUB_CLIENT.TransferProtocol(iter->first, update, 0);
	}
}
void CorpsBattleEntryBase::CorpsCenterBattleUpdate(PB::ipt_corps_center_battle_update& update)
{
	LOG_TRACE("CorpsBattleEntryBase::CorpsCenterBattleUpdate:battle_type=%d:update_type=%d:src_zoneid=%d:self_zoneid=%d",
	          m_battle_type, (int)update.update_type(), update.zoneid(), g_zoneid);

	switch (update.update_type())
	{
	case PB::ipt_corps_center_battle_update::CCBUT_MATCH_RESULT:
	{
		std::vector<PB::corps_battle_order> orders(update.orders().begin(), update.orders().end());
		OnReceiveMatchResult(orders, update.param(), update.zoneid());
	}
	break;

	case PB::ipt_corps_center_battle_update::CCBUT_BATTLE_BEGIN:
	{
		if (update.orders_size() <= 0)
		{
			break;
		}
		OnReceiveBattleBegin(update.mutable_orders(0), update.zoneid());
	}
	break;

	case PB::ipt_corps_center_battle_update::CCBUT_BATTLE_RESULT:
	{
		if (update.orders_size() <= 0)
		{
			break;
		}
		OnReceiveBattleResult(update.mutable_orders(0), update.param(), update.param2());
	}
	break;

	case PB::ipt_corps_center_battle_update::CCBUT_SYNC_SCORE:
	{
		Octets name(update.name().c_str(), update.name().size());
		if (CenterManager::GetInstance().IsCenter())
		{
			OnReceiveLocalScore(update.param(), update.param2(), update.roleid(), name, update.corps_id(), update.season_repu(), update.damage(), update.zoneid());
		}
		else
		{
			OnReceiveCenterScore(update.param(), update.param2(), update.roleid(), name, update.corps_id(), update.season_repu(), update.damage(), update.zoneid());
		}
	}
	break;

	case PB::ipt_corps_center_battle_update::CCBUT_SYNC_REPU:
	{
		if (CenterManager::GetInstance().IsCenter())
		{
			OnReceiveLocalSeasonRepu(update.param(), update.param2(), update.corps_id(), update.season_repu(), update.zoneid());
		}
		else
		{
			OnReceiveCenterSeasonRepu(update.param(), update.param2(), update.corps_id(), update.season_repu(), update.zoneid());
		}
	}
	break;

	default:
	{

	}
	break;
	}
}
/*
 * 对于社团模拟战，p3战场类型对应社团模拟战, p4挑战玩家的排名(0开始)，p5普通挑战还是困难挑战(0普通.1困难)，p6挑战社团真实玩家的数量，p7真实挑战还是模拟挑战(0尝试,1真打)
 * 对于社团竞赛，p6是社团1的天梯积分，p7是社团2的天梯积分
 * 对于社团竞赛2，p1是子战场类型
 */
int CorpsBattleEntryBase::CreateBattle(int battle_tid, int order_index, int64_t creator, const std::string& creator_name,
                                       int64_t challenger, const std::string& challenger_name, ruid_t player_npc,
                                       int p1, int p2, int p3, int p4, int p5, int p6, int p7,
                                       const std::string& player_npc_name, int64_t player_npc_idphoto, int player_npc_level, int64_t fightcapacity, int p9,
                                       int p10, int p11, int p12, bool is_corps_battle3, int64_t challenger2, const std::string& challenger2_name,
                                       int64_t challenger3, const std::string& challenger3_name, const std::vector<int64_t>& p64s)
{
	LOG_TRACE("CorpsBattleEntryBase::CreateBattle:battle_type=%d:battle_tid=%d:order_index=%d:creator=%ld:player_npc=%ld:p1=%d:p2=%d:p3=%d:p4=%d:p5=%d:p6=%d:p7=%d:player_npc_name.size=%lu:player_npc_idphoto=%ld:player_npc_level=%d:fightcapacity=%ld:p9=%d:p10=%d:p11=%d:p12=%d:challenger=%ld:challenger2=%ld:challenger3=%ld:p64.size=%zu",
	          m_battle_type, battle_tid, order_index, creator, player_npc, p1, p2, p3, p4, p5, p6, p7,
	          player_npc_name.size(), player_npc_idphoto, player_npc_level, fightcapacity, p9, p10, p11, p12, challenger, challenger2, challenger3, p64s.size());

	int ret = 0;
	InstanceData *inst = NULL;
	RoleCache temp;
	PB::enter_instance_config config;
	config.mutable_server()->set_jump_type(GNET::JIT_PATICIPATE);
	config.mutable_server()->add_params(m_battle_type);
	config.mutable_server()->add_params(order_index);
	config.mutable_server()->add_params(p4);

	inst = InstanceManager::Instance().Create(ret, battle_tid, temp.info, 0, config);

	if (!inst)
	{
		return 0;
	}

	PB::ipt_create_instance pb;
	pb.set_roleid(creator);
	pb.set_inst_id(inst->instid);
	pb.set_inst_tid(battle_tid);
	pb.add_params(p1);
	pb.add_params(p2);
	pb.add_params(p3);
	pb.add_params(p4);
	pb.add_params(p5);
	pb.add_params(p6);
	pb.add_params(p7);
	for (int64_t p64 : p64s)
	{
		pb.add_param64s(p64);
	}
	pb.add_creators(creator);
	pb.add_params(0);//gs有读p8，但是ds没有这个值，只能压个0进去！！！
	pb.add_params(p9);
	pb.add_params(p10);
	pb.add_params(p11);
	pb.add_params(p12);
	pb.set_player_npc_id(player_npc);
	pb.set_player_npc_name(player_npc_name);
	pb.set_player_npc_idphoto(player_npc_idphoto);
	pb.set_player_npc_level(player_npc_level);
	pb.set_fightcapacity(fightcapacity);
	PB::name_roleid_pair *pMember = pb.add_members();
	pMember->set_roleid(creator);
	pMember->set_name(creator_name);
	PB::name_roleid_pair *pMember2 = pb.add_members();
	pMember2->set_roleid(challenger);
	pMember2->set_name(challenger_name);
	if (is_corps_battle3)
	{
		PB::name_roleid_pair *pMember3 = pb.add_members();
		pMember3->set_roleid(challenger2);
		pMember3->set_name(challenger2_name);
		PB::name_roleid_pair *pMember4 = pb.add_members();
		pMember4->set_roleid(challenger3);
		pMember4->set_name(challenger3_name);
	}
	if (!GProviderServer::GetInstance()->DispatchProtocol(inst->lineid, pb))
	{
		InstanceManager::Instance().OnDestroy(inst->instid);
		return 0;
	}
	return inst->instid;
}
void CorpsBattleEntryBase::InitRecordNormalInfo(PB::npt_player_search_corps_battle_list_result& result, const CorpsBattleInfo& info)
{
	result.set_battle_level(info.battle_info.battle_level());
	result.set_total_count(info.battle_info.total_count());
	result.set_win_count(info.battle_info.win_count());
	result.set_consecutive_win_count(info.battle_info.consecutive_win_count());
	result.set_max_consecutive_win_count(info.battle_info.max_consecutive_win_count());
	result.set_battle_apply_time(info.battle_info.apply_time());

	LOG_TRACE("CorpsBattleEntryBase::InitRecordNormalInfo::battle_type=%d:corps_id=%ld:battle_level=%d:total_count=%d:win_count=%d:consecutive_win_count=%d:max_consecutive_win_count=%d:battle_apply_time=%d",
	          m_battle_type, info.ID(), result.battle_level(), result.total_count(), result.win_count(), result.consecutive_win_count(),
	          result.max_consecutive_win_count(), result.battle_apply_time());
}
bool CorpsBattleEntryBase::CopyRecords(PB::npt_player_search_corps_battle_list_result& result, const CorpsBattleInfo *pCorpsBattleInfo, ruid_t roleid, bool self, bool last, int param)
{
	if (!pCorpsBattleInfo)
	{
		return false;
	}
	LOG_TRACE("DS::CorpsBattleEntryBase::CopyRecords::battle_type=%d:corps_id=%ld:corps_index=%d:roleid=" PRINT64":self=%s:last=%s:param=%d",
	          m_battle_type, pCorpsBattleInfo->ID(), pCorpsBattleInfo->Index(), roleid, self ? "true" : "false", last ? "true" : "last", param);

	InitRecordNormalInfo(result, *pCorpsBattleInfo);

	if (self)
	{
		int order_index = last ? pCorpsBattleInfo->battle_info.last_order_index() : pCorpsBattleInfo->battle_info.order_index();
		if (order_index)
		{
			auto oit = m_corps_battle_orders.find(order_index);
			if (oit == m_corps_battle_orders.end() || !(oit->second))
			{
				return false;
			}
			const CorpsBattleOrderBase& order = *(oit->second);

			PB::corps_battle_record *pRecord = result.add_records();
			pRecord->set_battle_type(order.battle_order.battle_type());
			pRecord->set_battle_time(order.battle_order.battle_time());
			pRecord->set_self_id(pCorpsBattleInfo->battle_info.id());
			pRecord->set_self_name(pCorpsBattleInfo->battle_info.name());
			pRecord->set_self_badge(pCorpsBattleInfo->battle_info.badge());
			pRecord->set_self_support(pCorpsBattleInfo->battle_info.support_side());
			pRecord->set_self_level(pCorpsBattleInfo->battle_info.battle_level());

			int battle_result = -1;
			if (pRecord->battle_type() == CORPS_BATTLE3_STUB_TYPE_COMMON || pRecord->battle_type() == CORPS_BATTLE3_STUB_TYPE_CENTER)
			{
				battle_result = 1;
				std::vector<int> corps_index;
				order.GetCorpsIndex(corps_index);
				for (auto it = corps_index.begin(); it != corps_index.end(); ++it)
				{
					auto tit = m_corps_battle_infos.find(*it);
					if (tit == m_corps_battle_infos.end())
					{
						return false;
					}

					PB::corps_battle3_record_info *cbri = pRecord->add_target_info();
					cbri->set_id(tit->second.ID());
					cbri->set_name(tit->second.battle_info.name());
					cbri->set_level(tit->second.BattleLevel());
					cbri->set_badge(tit->second.Badge());
					cbri->set_support(tit->second.SupportSide());

					for (int i = 0; i < order.battle_order.rank_info_size(); ++i)
					{
						if (order.battle_order.rank_info(i).index() == tit->second.Index())
						{
							cbri->set_rank(order.battle_order.rank_info(i).rank());
						}
						if (order.battle_order.rank_info(i).index() == pCorpsBattleInfo->Index())
						{
							battle_result = order.GetResultByRank(order.battle_order.rank_info(i).rank());
						}
					}
				}
			}
			else
			{
				const CorpsBattleInfo *pTargetInfo = nullptr;
				if (order.battle_order.corps_battle_index_1() == pCorpsBattleInfo->battle_info.index())
				{
					auto tit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
					if (tit == m_corps_battle_infos.end() && !order.battle_order.single_battle())
					{
						return false;
					}
					if (tit != m_corps_battle_infos.end())
					{
						pTargetInfo = &(tit->second);
					}
					if (order.battle_order.result() >= 0)
					{
						battle_result = order.battle_order.result() == 2 ? 2 : (order.battle_order.result() == 1 ? 1 : 0);
					}
				}
				else
				{
					auto tit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
					if (tit == m_corps_battle_infos.end() && !order.battle_order.single_battle())
					{
						return false;
					}
					if (tit != m_corps_battle_infos.end())
					{
						pTargetInfo = &(tit->second);
					}
					if (order.battle_order.result() >= 0)
					{
						battle_result = order.battle_order.result() == 2 ? 2 : (order.battle_order.result() == 0 ? 1 : 0);
					}
				}
				if (!order.battle_order.single_battle() && pTargetInfo)
				{
					pRecord->set_target_index(pTargetInfo->battle_info.index());
					pRecord->set_target_id(pTargetInfo->battle_info.id());
					pRecord->set_target_name(pTargetInfo->battle_info.name());
					pRecord->set_target_level(pTargetInfo->battle_info.battle_level());
					pRecord->set_target_badge(pTargetInfo->battle_info.badge());
					pRecord->set_target_support(pTargetInfo->battle_info.support_side());
				}
			}
			if (battle_result >= 0)
			{
				pRecord->set_result(battle_result);
			}
			InitRecord(pRecord, oit->second, pCorpsBattleInfo, roleid, param);
		}
	}
	else
	{
		int size = pCorpsBattleInfo->battle_info.records_size();
		for (int i = 0; i < size; ++i)
		{
			PB::corps_battle_record *pRecord = result.add_records();
			pRecord->CopyFrom(pCorpsBattleInfo->battle_info.records(i));
			pRecord->set_self_id(pCorpsBattleInfo->battle_info.id());
			pRecord->set_self_name(pCorpsBattleInfo->battle_info.name());
			pRecord->set_self_badge(pCorpsBattleInfo->battle_info.badge());
			pRecord->set_self_support(pCorpsBattleInfo->battle_info.support_side());

			if (pRecord->battle_type() == CORPS_BATTLE3_STUB_TYPE_COMMON || pRecord->battle_type() == CORPS_BATTLE3_STUB_TYPE_CENTER)
			{
				for (int j = 0; j < pRecord->target_info_size(); ++j)
				{
					auto tit = m_corps_battle_infos.find(pRecord->target_info(j).index());
					if (tit == m_corps_battle_infos.end())
					{
						continue;
					}
					const CorpsBattleInfo& target_info = tit->second;

					PB::corps_battle3_record_info *cbri = pRecord->mutable_target_info(j);
					cbri->set_id(target_info.ID());
					cbri->set_name(target_info.battle_info.name());
					cbri->set_level(target_info.BattleLevel());
					cbri->set_badge(target_info.Badge());
					cbri->set_support(target_info.SupportSide());
				}
			}
			else
			{
				auto tit = m_corps_battle_infos.find(pRecord->target_index());
				if (tit == m_corps_battle_infos.end())
				{
					continue;
				}
				const CorpsBattleInfo& target_info = tit->second;

				pRecord->set_target_id(target_info.battle_info.id());
				pRecord->set_target_name(target_info.battle_info.name());
				pRecord->set_target_badge(target_info.battle_info.badge());
				pRecord->set_target_support(target_info.battle_info.support_side());

				LOG_TRACE("DS::CorpsBattleEntryBase::CopyRecords::battle_type=%d:corps_id=%ld:target_id=%ld:battle_time=%d:result=%d:self_level=%d:target_level=%d",
				          m_battle_type, pCorpsBattleInfo->ID(), target_info.ID(), pRecord->battle_time(), pRecord->result(), pRecord->self_level(), pRecord->target_level());
			}
		}
	}
	return true;
}
void CorpsBattleEntryBase::UpdateBattleInfoRecord(CorpsBattleInfo& info1, bool win, int target_index, int target_level, int battle_time)
{
	int battle_level = info1.battle_info.battle_level();
	if (win)
	{
		++ battle_level;
	}
	else
	{
		-- battle_level;
	}
	if (battle_level < CORPS_BATTLE_LEVEL_MIN)
	{
		battle_level = CORPS_BATTLE_LEVEL_MIN;
	}
	else if (battle_level > CORPS_BATTLE_LEVEL_MAX)
	{
		battle_level = CORPS_BATTLE_LEVEL_MAX;
	}
	info1.battle_info.set_battle_level(battle_level);
	info1.battle_info.set_total_count(info1.battle_info.total_count() + 1);
	PB::corps_battle_record *pRecord1 = info1.battle_info.add_records();
	pRecord1->set_target_index(target_index);
	pRecord1->set_self_level(info1.battle_info.battle_level());
	pRecord1->set_target_level(target_level);
	pRecord1->set_battle_time(battle_time);
	pRecord1->set_result(win ? 1 : 0);
	if (win)
	{
		info1.battle_info.set_win_count(info1.battle_info.win_count() + 1);
		info1.battle_info.set_consecutive_win_count(info1.battle_info.consecutive_win_count() + 1);
		if (info1.battle_info.max_consecutive_win_count() < info1.battle_info.consecutive_win_count())
		{
			info1.battle_info.set_max_consecutive_win_count(info1.battle_info.consecutive_win_count());
		}
	}
	else
	{
		info1.battle_info.set_consecutive_win_count(0);
	}
	if (info1.battle_info.records_size() > CORPS_BATTLE_RECORDS_MAX)
	{
		info1.battle_info.mutable_records()->DeleteSubrange(0, info1.battle_info.records_size() - CORPS_BATTLE_RECORDS_MAX);
	}
	info1.dirty = true;

	m_info_dirty = true;

	LOG_TRACE("CorpsBattleEntry::UpdateBattleInfoRecord::battle_type=%d:corps_id=%ld:corps_index=%d:win=%s:battle_level=%d:win_count=%d:target_index=%d:target_level=%d:battle_time=%d",
	          GetBattleType(), info1.ID(), info1.Index(), win ? "true" : "false", info1.battle_info.battle_level(), info1.battle_info.win_count(),
	          target_index, target_level, battle_time);

}
void CorpsBattleEntryBase::PlayerSearchBattleList(RoleInfo *pInfo, int64_t target_corps_id, int battle_begin_time, int page, int search_battle_type, int search_city_index, int param)
{
	LOG_TRACE("DS::CorpsBattleEntryBase::PlayerSearchBattleList::battle_type=%d:roleid=%ld:target_corps_id=%ld:battle_begin_time=%d:page=%d:search_battle_type=%d:search_city_index=%d:param=%d",
	          m_battle_type, pInfo->roleid, target_corps_id, battle_begin_time, page, search_battle_type, search_city_index, param);

	PB::npt_player_search_corps_battle_list_result result;
	result.set_battle_type(m_battle_type);
	result.set_battle_begin_time(battle_begin_time);
	result.set_page(page);
	result.set_corps_id(target_corps_id);

	if (target_corps_id)
	{
		//查询指定帮派的竞赛历史记录
		const CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(target_corps_id);
		CopyRecords(result, pCorpsBattleInfo, 0, false, false, param);
	}
	else if (battle_begin_time > 0 && page > 0)
	{
		//查询指定时间的帮派竞赛
		ruid_t corp_id = pInfo->GPSFacebook().corps().id();
		if (corp_id == 0)
		{
			return;
		}
		CorpsPtr pCorps = CorpsManager::GetInstance().GetCorp(corp_id);
		if (!pCorps)
		{
			return;
		}
		const CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(corp_id);
		if (pCorpsBattleInfo)
		{
			//赋值一下自己的帮派竞赛等级等信息
			InitRecordNormalInfo(result, *pCorpsBattleInfo);
		}
		if (page == 1)	//第一页把自己的信息置顶
		{
			if (pCorpsBattleInfo)
			{
				if (pCorpsBattleInfo->battle_info.order_index())
				{
					auto oit = m_corps_battle_orders.find(pCorpsBattleInfo->battle_info.order_index());
					if (oit != m_corps_battle_orders.end() && oit->second)
					{
						const CorpsBattleOrderBase& order = *(oit->second);
						int battle_time_index = GetBattleTimeIndex(order.battle_order.battle_time());
						if (battle_begin_time > battle_time_index)
						{
							//周2查周4的，不置顶
							LOG_TRACE("DS::CorpsBattleEntryBase::BattleRecord::NotSetTop::battle_type=%d:roleid=%ld:order_index=%d:battle_begin_time=%d:battle_time_index=%d",
							          m_battle_type, pInfo->roleid, order.Index(), battle_begin_time, battle_time_index);
						}
						else if (battle_begin_time < battle_time_index)
						{
							//周4查周2的，尝试把周2的置顶
							LOG_TRACE("DS::CorpsBattleEntryBase::BattleRecord::TrySetTop::battle_type=%d:roleid=%ld:order_index=%d:battle_begin_time=%d:battle_time_index=%d",
							          m_battle_type, pInfo->roleid, order.Index(), battle_begin_time, battle_time_index);
							CopyRecords(result, pCorpsBattleInfo, pInfo->roleid, true, true, param);
						}
						else
						{
							LOG_TRACE("DS::CorpsBattleEntryBase::BattleRecord::SetTop::battle_type=%d:roleid=%ld:order_index=%d:battle_begin_time=%d:battle_time_index=%d",
							          m_battle_type, pInfo->roleid, order.Index(), battle_begin_time, battle_time_index);
							CopyRecords(result, pCorpsBattleInfo, pInfo->roleid, true, false, param);
						}
					}
				}
			}
		}

		int count = 0;
		auto oit = m_corps_battle_orders.begin(), oeit = m_corps_battle_orders.end();
		for ( ; oit != oeit; ++oit)
		{
			if (!oit->second)
			{
				continue;
			}
			const CorpsBattleOrderBase& order = *(oit->second);
			int battle_time_index = GetBattleTimeIndex(order.battle_order.battle_time());
			if (battle_time_index == battle_begin_time)
			{
				++ count;
				if (count <= (page - 1)*CORPS_BATTLE_SEARCH_NUM_PER_PAGE)
				{
					continue;
				}
				else if (count > page * CORPS_BATTLE_SEARCH_NUM_PER_PAGE)
				{
					break;
				}

				if (order.battle_order.battle_type() == CORPS_BATTLE3_STUB_TYPE_COMMON || order.battle_order.battle_type() == CORPS_BATTLE3_STUB_TYPE_CENTER)
				{
					std::vector<int> corps_index_vec;
					order.GetCorpsIndex(corps_index_vec);

					if (!pCorpsBattleInfo)
					{
						--count;
						continue;
					}

					bool check_index = true;
					std::vector<CorpsBattleInfo *> infos;
					for (auto it = corps_index_vec.begin(); it != corps_index_vec.end(); ++it)
					{
						auto iit = m_corps_battle_infos.find(*it);
						if (iit == m_corps_battle_infos.end())
						{
							check_index = false;
							break;
						}
						infos.push_back(&(iit->second));
					}
					if (!check_index)
					{
						-- count;
						continue;
					}

					std::stringstream corps_id_str;
					corps_id_str << "<" << pCorpsBattleInfo->Index() << ", " << pCorpsBattleInfo->ID() << ">";

					PB::corps_battle_record *pRecord = result.add_records();
					pRecord->set_target_index(0);
					pRecord->set_battle_type(m_battle_type);
					pRecord->set_self_id(pCorpsBattleInfo->ID());
					pRecord->set_self_name(pCorpsBattleInfo->battle_info.name());
					pRecord->set_self_badge(pCorpsBattleInfo->Badge());
					pRecord->set_self_support(pCorpsBattleInfo->SupportSide());
					pRecord->set_battle_time(order.battle_order.battle_time());
					for (auto it = infos.begin(); it != infos.end(); ++it)
					{
						PB::corps_battle3_record_info *cbri = pRecord->add_target_info();
						cbri->set_index((*it)->Index());
						cbri->set_id((*it)->ID());
						cbri->set_name((*it)->battle_info.name());
						cbri->set_level((*it)->BattleLevel());
						cbri->set_badge((*it)->Badge());
						cbri->set_support((*it)->SupportSide());
						for (int i = 0; i < order.battle_order.rank_info_size(); ++i)
						{
							if (order.battle_order.rank_info(i).index() == (*it)->Index())
							{
								cbri->set_rank(order.battle_order.rank_info(i).rank());
							}
							if (order.battle_order.rank_info(i).index() == pCorpsBattleInfo->Index())
							{
								pRecord->set_result(order.GetResultByRank(order.battle_order.rank_info(i).rank()));
							}
						}
						corps_id_str << "<" << (*it)->Index() << ", " << (*it)->ID() << ">";
					}
					LOG_TRACE("DS::CorpsBattleEntryBase::BattleRecord::battle_type=%d:roleid=%ld:order_index=%d:corps=<corps_index, corps_id>%s",
					          m_battle_type, pInfo->roleid, order.Index(), corps_id_str.str().c_str());
				}
				else
				{
					auto iit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
					if (iit == m_corps_battle_infos.end())
					{
						-- count;
						continue;
					}
					const CorpsBattleInfo& info1 = iit->second;

					iit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
					if (iit == m_corps_battle_infos.end() && order.battle_order.single_battle())
					{
						-- count;
						continue;
					}

					PB::corps_battle_record *pRecord = result.add_records();
					pRecord->set_target_index(0);
					pRecord->set_battle_type(m_battle_type);
					pRecord->set_self_id(info1.battle_info.id());
					pRecord->set_self_name(info1.battle_info.name());
					pRecord->set_self_badge(info1.battle_info.badge());
					pRecord->set_self_support(info1.battle_info.support_side());
					pRecord->set_battle_time(order.battle_order.battle_time());
					pRecord->set_result(order.battle_order.result());
					if (iit != m_corps_battle_infos.end())
					{
						const CorpsBattleInfo& info2 = iit->second;
						pRecord->set_target_index(info2.Index());
						pRecord->set_target_id(info2.battle_info.id());
						pRecord->set_target_name(info2.battle_info.name());
						pRecord->set_target_badge(info2.battle_info.badge());
						pRecord->set_target_support(info2.battle_info.support_side());
					}

					LOG_TRACE("DS::CorpsBattleEntryBase::BattleRecord::battle_type=%d:roleid=%ld:order_index=%d:corps1_id=%ld:corps2_id=%ld",
					          m_battle_type, pInfo->roleid, order.Index(), pRecord->self_id(), pRecord->target_id());
				}
			}
		}
	}
	else
	{
		//查询自己帮派的竞赛历史记录
		ruid_t corp_id = pInfo->GPSFacebook().corps().id();
		if (corp_id == 0)
		{
			return;
		}
		CorpsPtr pCorps = CorpsManager::GetInstance().GetCorp(corp_id);
		if (!pCorps)
		{
			return;
		}

		const CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(corp_id);
		if (pCorpsBattleInfo)
		{
			if (m_battle_type != CORPS_BATTLE_STUB_TYPE_COMMON && m_battle_type != CORPS_BATTLE2_STUB_TYPE_COMMON && m_battle_type != CORPS_BATTLE3_STUB_TYPE_COMMON)
			{
				CopyRecords(result, pCorpsBattleInfo, pInfo->roleid, true, false, param);
			}
			if (m_battle_type == CORPS_BATTLE_STUB_TYPE_COMMON || m_battle_type == CORPS_BATTLE2_STUB_TYPE_COMMON || m_battle_type == CORPS_BATTLE3_STUB_TYPE_COMMON)
			{
				CopyRecords(result, pCorpsBattleInfo, pInfo->roleid, false, false, param);
			}
		}
	}

	pInfo->SendMessage2Client(result);
}
void CorpsBattleEntryBase::CollectCorpsIDFromOrder(std::vector<int64_t>& ids) const
{
	LOG_TRACE("CorpsBattleEntryBase::CollectCorpsIDFromOrder:battle_type=%d", m_battle_type);
	auto it = m_corps_battle_orders.begin();
	auto ie = m_corps_battle_orders.end();
	int index_1 = 0;
	int index_2 = 0;
	int64_t corps_id_1 = 0;
	int64_t corps_id_2 = 0;

	for (; it != ie; it++)
	{
		auto pOrder = it->second;
		if (!pOrder)
		{
			continue;
		}
		index_1 = pOrder->battle_order.corps_battle_index_1();
		index_2 = pOrder->battle_order.corps_battle_index_2();
		auto iter = m_corps_battle_infos.find(index_1);
		if (iter != m_corps_battle_infos.end())
		{
			const CorpsBattleInfo& info = iter->second;
			corps_id_1 = info.ID();
			ids.push_back(corps_id_1);
		}
		iter = m_corps_battle_infos.find(index_2);
		if (iter != m_corps_battle_infos.end())
		{
			const CorpsBattleInfo& info = iter->second;
			corps_id_2 = info.ID();
			ids.push_back(corps_id_2);
		}
		std::vector<int> corps_index_vec;
		pOrder->GetCorpsIndex(corps_index_vec);
		std::stringstream corps_info_str;
		for (auto it_index = corps_index_vec.begin(); it_index != corps_index_vec.end(); ++it_index)
		{
			iter = m_corps_battle_infos.find(*it_index);
			if (iter != m_corps_battle_infos.end())
			{
				const CorpsBattleInfo& info = iter->second;
				ids.push_back(info.ID());
				corps_info_str << "<" << *it_index << ", " << info.ID() << ">";
			}
		}

		LOG_TRACE("CorpsBattleEntryBase::CollectCorpsIDFromOrder::battle_type=%d:corps_1_id=%ld:corps_1_index=%d:corps_2_id=%ld:corps_2_index=%d:corps_info=<corps_index, corps_id>%s",
		          m_battle_type, corps_id_1, index_1, corps_id_2, index_2, corps_info_str.str().c_str());
	}
}
int CorpsBattleEntryBase::DebugGetBattleLevel(RoleInfo *pInfo)
{
	ruid_t corp_id = pInfo->GPSFacebook().corps().id();
	if (corp_id == 0)
	{
		return -1;
	}

	const CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(corp_id);
	if (!pCorpsBattleInfo)
	{
		return -1;
	}

	return pCorpsBattleInfo->battle_info.battle_level();
}

void CorpsBattleEntryBase::DebugSetBattleState(int battle_state)
{
	if (!DebugMode())
	{
		return;
	}
	int now_time = Timer::GetTime();
	PB::corps_battle_config::CORPS_BATTLE_STATE state = (PB::corps_battle_config::CORPS_BATTLE_STATE)battle_state;
	LOG_TRACE("DS::CorpsBattleEntryBase::DebugSetBattleState::battle_type=%d:now_time=%d:state=%d:config_state=%d", GetBattleType(), now_time, (int)state, (int)m_config.state());
	if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
		{
			//进入匹配阶段了
			LOG_TRACE("CorpsBattleEntryBase::DebugSetBattleState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_CLEAR->CORPS_BATTLE_STATE_MATCHED", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			MatchBattle(now_time);
		}
	}
	else if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR)
		{
			//跳过了战斗开始阶段，那就清了吧
			LOG_TRACE("CorpsBattleEntryBase::DebugSetBattleState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_MATCHED->CORPS_BATTLE_STATE_CLEAR", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			size_t origin_orders_size = m_corps_battle_orders.size();
			m_corps_battle_orders.clear();
			m_order_dirty = true;
			LOG_TRACE("CorpsBattleEntryBase::DebugSetBattleState:ClearOrder::battle_type=%d:origin_orders_size=%lu", GetBattleType(), origin_orders_size);
		}
		else if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
		{
			//进入战斗开始阶段了
			LOG_TRACE("CorpsBattleEntryBase::DebugSetBattleState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_MATCHED->CORPS_BATTLE_STATE_BEGIN", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			BeginBattle(now_time);
		}
	}
	else if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR || state == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
		{
			//进入战斗结束阶段
			LOG_TRACE("CorpsBattleEntryBase::DebugSetBattleState::battle_type=%d:now_time=%d:CORPS_BATTLE_STATE_BATTLE_BEGIN->CORPS_BATTLE_STATE_CLEAR", GetBattleType(), now_time);
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			EndBattle(now_time);
		}
		else
		{
			//尝试创建战场
			BeginBattle2(now_time);
		}
	}

}
void CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue(int64_t corps_id, int set_type, int set_value)
{
	LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:set_type=%d:set_value=%d",
	          GetBattleType(), corps_id, set_type, set_value);

	auto pInfo = GetBattleInfoByCorpsID(corps_id);

	if (!pInfo)
	{
		return;
	}

	switch (set_type)
	{
	case 1:
	{
		//修改天梯积分
		LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:corps_battle_score=[%d->%d]",
		          GetBattleType(), corps_id, pInfo->BattleScore(), set_value);

		pInfo->battle_info.set_corps_battle_score(set_value);
	}
	break;

	case 2:
	{
		//修改总场次
		LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:total_count=[%d->%d]",
		          GetBattleType(), corps_id, pInfo->battle_info.total_count(), set_value);

		pInfo->battle_info.set_total_count(set_value);
	}
	break;

	case 3:
	{
		//修改获胜总场次
		LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:win_count=[%d->%d]",
		          GetBattleType(), corps_id, pInfo->battle_info.win_count(), set_value);

		pInfo->battle_info.set_win_count(set_value);
	}
	break;

	case 4:
	{
		//修改当前连胜场次
		LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:consecutive_win_count=[%d->%d]",
		          GetBattleType(), corps_id, pInfo->battle_info.consecutive_win_count(), set_value);

		pInfo->battle_info.set_consecutive_win_count(set_value);
	}
	break;

	case 5:
	{
		//修改历史最大连胜场次
		LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:max_consecutive_win_count=[%d->%d]",
		          GetBattleType(), corps_id, pInfo->battle_info.max_consecutive_win_count(), set_value);

		pInfo->battle_info.set_max_consecutive_win_count(set_value);
	}
	break;

	case 6:
	{
		//修改中心服总场次
		LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:center_total_count=[%d->%d]",
		          GetBattleType(), corps_id, pInfo->battle_info.center_total_count(), set_value);

		pInfo->battle_info.set_center_total_count(set_value);
	}
	break;

	case 7:
	{
		//修改中心服获胜总场次
		LOG_TRACE("CorpsBattleEntryBase::IDIPSetCorpsBattleInfoValue::battle_type=%d:corps_id=%ld:center_win_count=[%d->%d]",
		          GetBattleType(), corps_id, pInfo->battle_info.center_win_count(), set_value);

		pInfo->battle_info.set_center_win_count(set_value);
	}
	break;

	default:
		return;
	}

	pInfo->dirty = true;
	m_info_dirty = true;
}
void CorpsBattleEntryBase::UpdateWinCount(CorpsBattleInfo& info, int is_win)
{
	info.battle_info.set_total_count(info.battle_info.total_count() + 1);
	if (is_win == 1)
	{
		info.battle_info.set_win_count(info.battle_info.win_count() + 1);
		info.battle_info.set_consecutive_win_count(info.battle_info.consecutive_win_count() + 1);
		if (info.battle_info.max_consecutive_win_count() < info.battle_info.consecutive_win_count())
		{
			info.battle_info.set_max_consecutive_win_count(info.battle_info.consecutive_win_count());
		}
	}
	else
	{
		info.battle_info.set_consecutive_win_count(0);
	}

	LOG_TRACE("CorpsBattleEntryBase::UpdateWinCount::battle_type=%d:corps_id=%ld:is_win=%d:win_count=%d:consecutive_win_count=%d:max_consecutive_win_count=%d",
	          GetBattleType(), info.ID(), is_win, info.battle_info.win_count(), info.battle_info.consecutive_win_count(), info.battle_info.max_consecutive_win_count());
}
void CorpsBattleEntryBase::HandleCorpsSeasonOnEndBattle(PB::CORPS_SEASON_ACTIVITY_TYPE act_type, int64_t id_1, int64_t origin_repu_1, int64_t score_1, int64_t id_2, int64_t origin_repu_2, int64_t score_2, int64_t key, bool draw, GNET::Octets name_1, GNET::Octets name_2) const
{
	LOG_TRACE("CorpsBattleEntryBase::HandleCorpsSeasonOnEndBattle::act_type=%d:id1=%ld:origin_repu_1=%ld:score_1=%ld:id2=%ld:origin_repu_2=%ld:score_2=%ld:key=%ld:draw=%s",
	          (int)act_type, id_1, origin_repu_1, score_1, id_2, origin_repu_2, score_2, key, draw ? "true" : "false");

	int64_t new_repu_1 = 0;
	int64_t new_repu_2 = 0;

	float S = 0.0;

	if (draw)
	{
		S = 0.5;
		new_repu_1 = (origin_repu_1 + origin_repu_2) * 0.05 * S;
		new_repu_2 = (origin_repu_1 + origin_repu_2) * 0.05 * S;
	}
	else
	{
		S = std::min(((((float)score_1) / score_2) - 1) / 10, (float)0.1);
		new_repu_1 = (origin_repu_1 + origin_repu_2) * 0.05 * (0.7 + S);
		new_repu_2 = (origin_repu_1 + origin_repu_2) * 0.05 * (0.3 - S);
	}
	auto fix_repu = [](int64_t& repu, int limit)
	{
		if (repu < limit)
		{
			repu = limit;
		}
	};

	fix_repu(new_repu_1, MIN_WINNER_SEASON_REPU);
	fix_repu(new_repu_2, MIN_LOSER_SEASON_REPU);

	PB::ipt_deliver_corps_season_repu proto;
	proto.set_incr_type(PB::ipt_deliver_corps_season_repu::IT_CORPS);
	proto.set_act_type(act_type);
	proto.set_repu_id(key);
	proto.add_target_names();

	//处理胜利者
	if (act_type == PB::CSAT_TOWER)
	{
		if (key == GNET::CSRID_ROAM_SCORE)
		{
			if (id_2 == 0)
			{
				proto.set_sub_type(28);
			}
			else
			{
				proto.set_sub_type(draw ? 27 : 25);
			}
		}
		else
		{
			if (id_2 == 0)
			{
				proto.set_sub_type(14);
			}
			else
			{
				proto.set_sub_type(draw ? 13 : 11);
			}
		}
	}
	else if (act_type == PB::CAST_CITY)
	{
		if (id_2 == 0)
		{
			proto.set_sub_type(7);
		}
		else
		{
			proto.set_sub_type(draw ? 6 : 4);
		}
	}
	proto.set_self_id(id_1);
	proto.add_target_ids(id_2);
	if (name_2.size() != 0)
	{
		proto.clear_target_names();
		proto.add_target_names(name_2.begin(), name_2.size());
	}
	proto.set_repu_val(new_repu_1);
	proto.set_param(draw ? 2 : 1);
	CorpsManager::GetInstance().HandleDeliverCorpsSeasonRepu(proto);

	if (id_2 == 0)
	{
		return;
	}
	//处理失败者
	if (act_type == PB::CSAT_TOWER)
	{
		if (key == GNET::CSRID_ROAM_SCORE)
		{
			proto.set_sub_type(draw ? 27 : 26);
		}
		else
		{
			proto.set_sub_type(draw ? 13 : 12);
		}
	}
	else if (act_type == PB::CAST_CITY)
	{
		proto.set_sub_type(draw ? 6 : 5);
	}
	proto.set_self_id(id_2);
	proto.clear_target_ids();
	proto.add_target_ids(id_1);
	if (name_1.size() != 0)
	{
		proto.clear_target_names();
		proto.add_target_names(name_1.begin(), name_1.size());
	}
	proto.set_repu_val(new_repu_2);
	proto.set_param(draw ? 2 : 0);
	CorpsManager::GetInstance().HandleDeliverCorpsSeasonRepu(proto);
}
void CorpsBattleEntryBase::UpdateScoreTopList(int64_t corps_id, int corps_battle_score)
{
	int tp_id = GetTopListID();
	LOG_TRACE("CorpsBattleEntryBase::UpdateScoreTopList::battle_type=%d:corps_id=%ld:toplist_id=%d:corps_battle_score=%d", m_battle_type, corps_id, tp_id, corps_battle_score);
	if (tp_id == 0)
	{
		return;
	}
	CorpsPtr pCorps = CorpsManager::GetInstance().GetCorp(corps_id);
	if (pCorps)
	{
		Octets corps_name = Octets(pCorps->Name().c_str(), pCorps->Name().size());
		int badge = pCorps->Badge();
		ruid_t master = pCorps->GetMaster().ID();
		auto *pMaster = RoleMap::Instance().Find(master);
		if (pMaster)
		{
			toplist_addon_data_t addon;
			addon.mutable_corps_battle_data()->set_master(master);
			addon.mutable_corps_battle_data()->set_master_idphoto(pMaster->idphoto);
			addon.mutable_corps_battle_data()->set_master_name(pMaster->GetShowName().begin(), pMaster->GetShowName().size());
			addon.mutable_corps_battle_data()->set_badge(badge);
			DSTPManager::GetInstance().UpdateItem(tp_id, corps_id, corps_battle_score, corps_name, 0, 0, true, addon);
			DSTPManager::GetInstance().Dump(tp_id);
		}
		else
		{
			RoleInfoBriefManager::GetInstance().GetOneBrief(master, std::string(),
			        [corps_name, badge, master, corps_id, corps_battle_score, tp_id]
			        (BriefStruct<PB::role_brief>::BRIEF_PTR pBrief)
			{
				if (!pBrief->valid)
				{
					return;
				}
				toplist_addon_data_t addon;
				addon.mutable_corps_battle_data()->set_master(master);
				addon.mutable_corps_battle_data()->set_master_idphoto(pBrief->brief.idphoto());
				addon.mutable_corps_battle_data()->set_master_name(pBrief->brief.name());
				addon.mutable_corps_battle_data()->set_badge(badge);
				DSTPManager::GetInstance().UpdateItem(tp_id, corps_id, corps_battle_score, corps_name, 0, 0, true, addon);
				DSTPManager::GetInstance().Dump(tp_id);
			});
		}
	}
}
void CorpsBattleEntryBase::DumpRecord(const corps_battle_record& record)
{
	std::stringstream corps_info_str;
	for (int i = 0; i < record.target_info_size(); ++i)
	{
		corps_info_str << "<" << record.target_info(i).index() << ", " << record.target_info(i).id() << ", " << record.target_info(i).level() << ", " << record.target_info(i).badge() << ", " << record.target_info(i).support() << ", " << record.target_info(i).rank() << ">";
	}
	LOG_TRACE("CorpsBattleEntryBase::DumpRecord:battle_type=%d:record_battle_type=%d:self_id=%ld:self_badge=%d:self_support=%d:self_level=%d:target_id=%ld:target_badge=%d:target_support=%d:target_level=%d::battle_time=%d:result=%d:corps_info=<index, id, level, badge, support, rank>%s",
	          m_battle_type, record.battle_type(), record.self_id(), record.self_badge(), record.self_support(), record.self_level(), record.target_id(), record.target_badge(),
	          record.target_support(), record.target_level(), record.battle_time(), record.result(), corps_info_str.str().c_str());
}
void CorpsBattleEntryBase::IDIPClearBattle()
{
	LOG_TRACE("CorpsBattleEntryBase::IDIPClearBattle:battle_type=%d:m_corps_battle_infos.size=%zu:m_corps_battle_ids.size=%zu:m_corps_battle_orders.size=%zu:m_battle_info_count=%d",
	          GetBattleType(), m_corps_battle_infos.size(), m_corps_battle_ids.size(), m_corps_battle_orders.size(), m_battle_info_count);
	//清空Info
	ClearBattleInfo();

	//清空Order
	ClearBattleOrder();
}
void CorpsBattleEntryBase::DebugClearBattle()
{
	LOG_TRACE("CorpsBattleEntryBase::DebugClearBattle:battle_type=%d", GetBattleType());
	if (!m_corps_battle_ids.empty() || !m_corps_battle_infos.empty())
	{
		m_corps_battle_ids.clear();
		m_corps_battle_infos.clear();
		m_corps_battle_ids.clear();
		m_info_dirty = true;
	}
	if (!m_corps_battle_orders.empty())
	{
		m_corps_battle_orders.clear();
		m_order_dirty = true;
	}
	m_config.Clear();
}
void CorpsBattleEntryBase::DebugSetCorpsBattleWinCount(int64_t corps_id, int total_count, int win_count)
{
	LOG_TRACE("CorpsBattleEntryBase::DebugSetCorpsBattleWinCount::battle_type=%d:corps_id=%ld:total_count=%d:win_count=%d",
	          GetBattleType(), corps_id, total_count, win_count);

	auto pInfo = GetBattleInfoByCorpsID(corps_id);
	if (!pInfo)
	{
		return;
	}
	int origin_total_count = pInfo->battle_info.total_count();
	int origin_win_count = pInfo->battle_info.win_count();

	pInfo->battle_info.set_total_count(total_count);
	pInfo->battle_info.set_win_count(win_count);

	pInfo->dirty = true;
	m_info_dirty = true;

	LOG_TRACE("CorpsBattleEntryBase::DebugSetCorpsBattleWinCount::battle_type=%d:corps_id=%ld:total_count=[%d->%d]:win_count=[%d-%d]",
	          GetBattleType(), corps_id, origin_total_count, total_count, origin_win_count, win_count);
}
void CorpsBattleEntryBase::DebugSetCorpsBattleCenterWinCount(int64_t corps_id, int total_count, int win_count)
{
	LOG_TRACE("CorpsBattleEntryBase::DebugSetCorpsBattleCenterWinCount::battle_type=%d:corps_id=%ld:total_count=%d:win_count=%d",
	          GetBattleType(), corps_id, total_count, win_count);

	auto pInfo = GetBattleInfoByCorpsID(corps_id);
	if (!pInfo)
	{
		return;
	}

	int origin_center_total_count = pInfo->battle_info.center_total_count();
	int origin_center_win_count = pInfo->battle_info.center_win_count();

	pInfo->battle_info.set_center_total_count(total_count);
	pInfo->battle_info.set_center_win_count(win_count);

	pInfo->dirty = true;
	m_info_dirty = true;

	LOG_TRACE("CorpsBattleEntryBase::DebugSetCorpsBattleCenterWinCount::battle_type=%d:corps_id=%ld:total_count=[%d->%d]:win_count=[%d-%d]",
	          GetBattleType(), corps_id, origin_center_total_count, total_count, origin_center_win_count, win_count);
}
void CorpsBattleEntryBase::DebugShowBattleInfo() const
{
	LOG_TRACE("CorpsBattleEntryBase::DebugShowBattleInfo:battle_type=%d", GetBattleType());
	auto it = m_corps_battle_infos.begin();
	auto ie = m_corps_battle_infos.end();

	for (; it != ie; it++)
	{
		const auto& info = it->second;
		LOG_TRACE("CorpsBattleEntryBase::DebugShowBattleInfo::corps_id=%ld:corps_index=%d:order_index=%d:corps_battle_score=%d:last_order_index=%d:battle_level=%d:total_count=%d:win_count=%d:consecutive_win_count=%d:max_consecutive_win_count=%d:apply_time=%d",
		          info.ID(), info.Index(), info.OrderIndex(), info.battle_info.corps_battle_score(), info.battle_info.last_order_index(), info.battle_info.battle_level(),
		          info.battle_info.total_count(), info.battle_info.win_count(), info.battle_info.consecutive_win_count(), info.battle_info.max_consecutive_win_count(),
		          info.battle_info.apply_time());
	}
}
void CorpsBattleEntryBase::DebugShowBattleOrder() const
{
	auto it = m_corps_battle_orders.begin();
	auto ie = m_corps_battle_orders.end();

	for (; it != ie; it++)
	{
		auto pOrder = it->second;
		if (!pOrder)
		{
			continue;
		}
		pOrder->ShowOrder(m_battle_type);
	}
}
void CorpsBattleEntryBase::DebugSetBattleTime()
{
	int now_time = Timer::GetTime();
	int week_begin = GetLocalWeekBegin();
	LOG_TRACE("CorpsBattleEntryBase::DebugSetBattleTime::battle_type=%d:now_time=%d:week_begin=%d", m_battle_type, now_time, week_begin);

	if (!m_corps_battle_orders.empty())
	{
		size_t origin_orders_size = m_corps_battle_orders.size();
		m_corps_battle_orders.clear();
		m_order_dirty = true;
		LOG_TRACE("CorpsBattleEntryBase::DebugSetBattleTime:ClearOrder::battle_type=%d:origin_orders_size=%lu", GetBattleType(), origin_orders_size);
	}

	m_battle_total_time.clear();
	//设置匹配时间
	//30秒后开始匹配，60秒后开战，450秒后结束
	m_battle_total_time.push_back(now_time + 30);
	m_battle_total_time.push_back(now_time + 60);
	m_battle_total_time.push_back(now_time + 450);
	m_battle_total_time.push_back(week_begin);

	std::sort(m_battle_total_time.begin(), m_battle_total_time.end());
}

}
