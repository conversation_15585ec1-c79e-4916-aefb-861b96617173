#ifndef __DS_CORPS_BATTLE_BASE_H__
#define __DS_CORPS_BATTLE_BASE_H__
#include "gprotoc/ipt_corps_eliminate_knockout_battle_enter.pb.h"
#include "gprotoc/ipt_corps_server_battle_update.pb.h"
#include "gprotoc/eliminate_battle_guess_data_t.pb.h"
namespace PB { namespace npt_player_search_corps_battle_list_result_NS { class npt_player_search_corps_battle_list_result; } using namespace npt_player_search_corps_battle_list_result_NS; }
#include "gprotoc/CORPS_BATTLE2_SUB_BATTLE_TYPE.pb.h"
#include "gprotoc/ipt_hundred_corps_battle_debug.pb.h"
#include "gprotoc/ipt_eliminate_battle_notify.pb.h"
#include "gprotoc/corps_battle_config.pb.h"
#include "gprotoc/ELIMINATE_BATTLE_STATE.pb.h"
#include "gprotoc/CORPS_SEASON_ACTIVITY_TYPE.pb.h"
#include "gprotoc/corps_member_info.pb.h"
#include "gprotoc/ipt_corps_center_battle_leave.pb.h"
#include "gprotoc/npt_corps_server_battle_swap_pos.pb.h"
namespace PB { namespace ipt_corps_center_battle_update_NS { class ipt_corps_center_battle_update; } using namespace ipt_corps_center_battle_update_NS; }
#include "gprotoc/ipt_corps_center_battle_enter.pb.h"
#include "gprotoc/corps_battle_record.pb.h"
#include "gprotoc/ipt_hundred_corps_battle_operator.pb.h"
#include "gprotoc/ipt_corps_center_battle_create_battle.pb.h"
#include "gprotoc/corps_battle_order.pb.h"
#include "gprotoc/corps_battle_info.pb.h"
#include "gprotoc/ipt_corps_server_battle_enter.pb.h"
#include "gprotoc/ipt_corps_eliminate_knockout_battle_enter_re.pb.h"
#include "gprotoc/HUNDRED_CORPS_BATTLE_STATE.pb.h"


#include <list>
#include <unordered_map>
#include <memory>
#include "commonmacro.h"

#include "octets.h"
#include "corps_manager.h"

class LuaWrapper;
class lua_State;
namespace GNET
{

class RoleInfo;

enum
{
	CORPS_BATTLE_SEARCH_NUM_PER_PAGE = 10,	//客户端查询对战信息时每页的数量
	CORPS_BATTLE_RECORDS_MAX = 20,		//每个社团保存的竞赛记录的上限
	CORPS_BATTLE_LEVEL_MIN = 1,		//社团竞赛最低等级
	CORPS_BATTLE_LEVEL_MAX = 7,		//社团竞赛最高等级
	CORPS_BATTLE_TICK_INTERVAL = 1,		//每多少秒做一次tick
	CORPS_BATTLE_CREATE_PER_TICK = 5,	//每次tick最多开多少场战斗
	CORPS_BATTLE_NEED_SERVER_LEVEL = 50,	//开启社团战场需要的服务器等级
	CORPS_BATTLE_LEVEL_GUARANTEED	= 10,	//保底社团竞赛段位
	CORPS_BATTLE_APPLY_CENTER_COUNT	= 5,	//上报中心服的社团数量
	CORPS_BATTLE3_CORPS_COUNT= 4,			// 社团竞赛3参与社团数量
	CORPS_CENTER_BATTLE3_SERVER_LEVEL_DIFF = 5,	// 跨服社团竞赛3允许匹配的最大等级差
};

enum
{
	CORPS_TOWER_MIN_ROLE_LEVEL		= 40,	//社团爬塔玩家最低等级
	CORPS_TOWER_PLAYER_RANK_SIZE		= 10,	//社团爬塔排行榜长度
	CORPS_TOWER_PLAYER_CHALLANGE_COUNT	= 3,	//社团爬塔挑战次数
	CORPS_CITY_MIN_ROLE_LEVEL		= 40,	//社团模拟战玩家最低等级
};

enum BattleInfoChangeType
{
    BATTLE_INFO_UPDATE,
    BATTLE_INFO_BATTLE_RESULT,
};

//用来进行匹配的临时结构
struct CorpsBattleMatchInfo
{
	int64_t corps_id;
	Octets corps_name;
	int corps_badge;
	int corps_support;					//社团拥护
	int battle_info_index;					//战斗信息索引
	int battle_level;					//社团战斗等级
	int activity;						//社团活跃度
	int server_level;					//跨服使用的本服服务器等级
	int score = 0;						//社团竞赛天梯积分
	int capacity_score = 0;					//社团战斗力积分
	int src_zoneid = 0;					//社团所在服务器zoneid
	int bak_activity = 0;					//社团活跃度
	int bak_lastweek_activity = 0;				//上周社团活跃度
	int bak_corps_level = 0;				//社团等级
	std::vector<const PB::corps_member_info*> players;	//给社团攻城战使用的玩家列表

	CorpsBattleMatchInfo()
	{
		corps_id = 0;
		corps_badge = 0;
		corps_support = 0;
		battle_info_index = 0;
		battle_level = 0;
		activity = 0;
	}
};

//每个社团的竞赛信息
struct CorpsBattleInfo
{
	PB::corps_battle_info battle_info;
	bool dirty;
	int bak_activity = 0;						//社团活跃度
	int bak_lastweek_activity = 0;				//上周社团活跃度
	int bak_corps_level = 0;					//社团等级

	CorpsBattleInfo()
	{
		dirty = false;
	}
	int64_t ID() const
	{
		return battle_info.id();
	}
	const std::string& Name()const 
	{
		return battle_info.name();
	}
	int Index() const
	{
		return battle_info.index();
	}
	int OrderIndex() const
	{
		return battle_info.order_index();
	}
    int ApplyTime() const
    {
        return battle_info.apply_time();
    }
    int ServerLevel() const
    {
        return battle_info.server_level();
    }
    int SrcZoneID() const
    {
        return battle_info.zoneid();
    }
    int64_t CorpsSeasonRepu() const
    {
        return battle_info.corps_season_repu();
    }
	int BattleScore() const
	{
		return battle_info.corps_battle_score();
	}
	int CapacityScore() const
	{
		return battle_info.corps_capacity_score();
	}
	int BattleLevel() const
	{
		return battle_info.battle_level();
	}
	int Badge() const
	{
		return battle_info.badge();
	}
	int SupportSide() const
	{
		return battle_info.support_side();
	}
	int TotalCount() const
	{
		return battle_info.total_count();
	}
	int WinCount() const
	{
		return battle_info.win_count();
	}
    int ConsecutiveWinCount() const
    {
        return battle_info.consecutive_win_count();
    }
    int EliminateBattleScore() const
    {
        return battle_info.eliminate_battle_score();
    }
    int BattleState() const
    {
        return battle_info.battle_state();
    }
	int CenterTotalCount() const { return battle_info.center_total_count(); }
	int CenterWinCount() const { return battle_info.center_win_count(); }
	void SetSeedPlayer()
	{
		battle_info.set_battle_state(0xFFFF);
		dirty = true;
	}
	void SetBattleState(int state)
	{
		int old_state = battle_info.battle_state();
		battle_info.set_battle_state(state);
		dirty = true;
		LOG_TRACE("BATTLE_STATE_DEBUG::SetBattleState:corps_id=%ld:index=%d:old_battle_state=%d:battle_state=%d",
		          battle_info.id(), battle_info.index(), old_state, state);
	}
	
	void ClearSeedPlayer()
	{
		battle_info.set_battle_state(0);
		dirty = true;
	}
	void ToNameRuid(NameRuidPair& info)
	{
		info.id = battle_info.id();
		info.name = Octets(battle_info.name().c_str(), battle_info.name().size());
	}
	std::string GatherStrInfo() const
	{
		std::stringstream ss;
		ss << "[id=" << battle_info.id() << ":index=" << battle_info.index() << ":order_index=" << battle_info.order_index();
		ss << ":src_zoneid=" << battle_info.zoneid();
		//收集玩家信息
		for(int i = 0; i < battle_info.eliminate_battle_players_size(); i++)
		{
			const auto& player = battle_info.eliminate_battle_players(i);
			ss << ":player_id=" << player.role().id();
		}
		ss << "]";
		return ss.str();
	}
};

//每场战斗匹配信息
struct CorpsBattleOrderBase : public std::enable_shared_from_this<CorpsBattleOrderBase>
{		
	enum
	{
		BATTLE_CLOSE	= 1,	//战场副本关闭中
		BATTLE_CREATING	= 2,	//战场副本创建中
		BATTLE_CREATE	= 3,	//战场副本创建完毕
	};

	struct CorpsBattleCache
	{
		int battle_inst_state;
		int battle_inst_id;		
		int battle_inst_tid;
		std::set<ruid_t> waitting_battle_list;
		
		CorpsBattleCache():battle_inst_state(BATTLE_CLOSE),battle_inst_id(0),battle_inst_tid(0)
		{

		}	
	};
	typedef std::map<int/*info_index*/, CorpsBattleCache> CorpsBattleCacheMap;

	PB::corps_battle_order battle_order;
	bool dirty;

	CorpsBattleOrderBase()
	{
		dirty = false;
	}
	virtual ~CorpsBattleOrderBase() { }
    void SetBattleOrder(const PB::corps_battle_order& order)
    {
        battle_order.CopyFrom(order);
    }
	int Index() const
	{
		return battle_order.index();
	}
	int CorpsOneIndex() const
	{
		return battle_order.corps_battle_index_1();
	}
	int CorpsTwoIndex() const
	{
		return battle_order.corps_battle_index_2();
	}
	bool GetCorpsIndex(std::vector<int> &corps_index) const
	{
		for (int i = 0; i < battle_order.rank_info_size(); ++i)
		{
			corps_index.push_back(battle_order.rank_info(i).index());
		}

		return (corps_index.size() == CORPS_BATTLE3_CORPS_COUNT);
	}
	std::string GetCorpsIndexStr() const
	{
		std::stringstream str;
		str << "<";
		for (int i = 0; i < battle_order.rank_info_size(); ++i)
		{
			str << battle_order.rank_info(i).index() << ", ";
		}
		str << ">";
		return str.str();
	}
	int GetResultByRank(int rank) const
	{
		if (rank == 1 || rank == 2)
		{
			return 1;
		}
		else if (rank == 3 || rank == 4)
		{
			return 0;
		}
		return 2;
	}
	int BattleTime() const
	{
		return battle_order.battle_time();
	}
	int Result() const
	{
		return battle_order.result();
	}
    void SetResult(int result)
    {
        battle_order.set_result(result);
    }
    int64_t CorpsOneID() const
    {
        return battle_order.corps_battle_id_1();
    }
    int64_t CorpsTwoID() const
    {
        return battle_order.corps_battle_id_2();
    }
    int CorpsOneBadge() const
    {
        return battle_order.corps_battle_badge_1();
    }
    int CorpsTwoBadge() const
    {
        return battle_order.corps_battle_badge_2();
    }
    int CorpsOneSupport() const
    {
        return battle_order.corps_battle_support_1();
    }
    int CorpsTwoSupport() const
    {
        return battle_order.corps_battle_support_2();
    }
    const std::string& CorpsOneName() const
    {
        return battle_order.corps_battle_name_1();
    }
    const std::string& CorpsTwoName() const
    {
        return battle_order.corps_battle_name_2();
    }
    int CorpsOneScore() const
    {
        return battle_order.corps_battle_score_1();
    }
    int CorpsTwoScore() const
    {
        return battle_order.corps_battle_score_2();
    }
    int CorpsOneBattleLevel() const 
    {
        return battle_order.corps_battle_level_1();
    }
    int CorpsTwoBattleLevel() const 
    {
        return battle_order.corps_battle_level_2();
    }
    int64_t CorpsOneRepu() const
    {
        return battle_order.season_repu_1();
    }
    int64_t CorpsTwoRepu() const
    {
        return battle_order.season_repu_2();
    }
    PB::ELIMINATE_BATTLE_STATE GetBattleStage() const
    {
        return battle_order.battle_stage();
    }
	PB::HUNDRED_CORPS_BATTLE_STATE  GetHundredCorpsBattleStage() const
	{
		return battle_order.hundred_corps_battle_stage();
	}
	virtual void ShowOrder(int battle_type) const
	{
	}
};

typedef std::shared_ptr<CorpsBattleOrderBase> CorpsBattleOrderBasePtr;
//战斗时间控制
struct CorpsBattleTime
{
	char weekday;
	char hour;
	char min;
};

struct CBI_Key
{
	int _index;
	int _value;
	int _time;

	CBI_Key(int index, int value, int time):_index(index), _value(value), _time(time)
	{

	}
	CBI_Key(const CBI_Key &rh)
	{
		memcpy(this, &rh, sizeof(CBI_Key));
	}
	bool operator>(const CBI_Key &rh) const
	{
		if(_value > rh._value)
		{
			return true;
		}
		else if (_value == rh._value)
		{
			if(_time < rh._time)
			{
				return true;
			}
			else if(_time == rh._time)
			{
				return _index < rh._index;
			}
		}
		return false;
	}
	bool operator==(const CBI_Key &rh) const
	{
		return _index == rh._index && _value == rh._value && _time == rh._time;
	}
};

typedef std::vector<int> BATTLE_TID_VEC;
typedef std::vector<int> BATTLE_TIMESTAMP_VEC;
typedef std::unordered_map<int/*info_index*/, CorpsBattleInfo> CORPS_BATTLE_INFO_MAP;
typedef std::map<int/*order_index*/, CorpsBattleOrderBasePtr> CORPS_BATTLE_ORDER_MAP;
typedef std::map<CBI_Key,CorpsBattleInfo*,std::greater<CBI_Key> > ORDER_BATTLE_INFO_MAP;//按照排名数据作为key
typedef std::set<CBI_Key,std::greater<CBI_Key> > ORDER_BATTLE_INFO_SET;//按照排名数据作为key
typedef std::unordered_map<int64_t/*corps_id*/, int/*info_index*/> CORPS_BATTLE_ID_MAP;
typedef std::vector<CorpsBattleTime> BATTLE_TIME_VEC;
typedef std::vector<int/*order_index*/> BATTLE_GUESS_VEC;
typedef std::set<int/*order_index*/> BATTLE_GUESS_SET;
typedef std::map<int/*stage*/, BATTLE_GUESS_SET > BATTLE_GUESS_MAP;

//社团竞赛的基础信息
class CorpsBattleEntryBase
{
protected:
	CorpsBattleEntryBase(int battle_type): m_battle_type(battle_type), m_battle_info_count(0), m_order_dirty(false), 
			m_info_dirty(false), m_config_dirty(false), m_guess_dirty(false), m_save_db(false), m_load_finished(false), m_battle_continue_time(0), m_battle_count_max(0),m_debug_mode(false)
	{
		m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR);
		m_config.set_timestamp(0);
	}
public:
	virtual ~CorpsBattleEntryBase() 
	{
		m_corps_battle_orders.clear();
	}
	
	inline bool IsLoadFinished() const
	{
		return m_load_finished;
	}
	virtual bool Init(lua_State *L, LuaWrapper& lw);
	virtual bool Update(int now_time, int day_begin, int week_begin);
	virtual void OnDBLoad(int handle_index, const std::string& prefix, std::vector<Octets>& data);
	virtual void OnDBSave(int ret);
	virtual void OnNewWeek(int week_begin);
	virtual void OnNewDay(int day_begin);
	virtual void MatchBattle(int now_time);
	virtual void BeforeMatchBattle(int now_time) {}
	virtual void MatchBattle2(int now_time);
	virtual void BeginBattle2(int now_time) = 0;
	virtual void BeginBattle(int now_time) = 0;
	virtual void EndBattle(int now_time) = 0;
	virtual CorpsBattleOrderBasePtr CreateBattleOrder() = 0;
	virtual FUNC_CODE GetFuncCode() const = 0;
	virtual void CollectMatchInfo(std::vector<CorpsBattleMatchInfo>& battle_match_infos);
	virtual void HandleAllCorps(std::vector<CorpsBattleMatchInfo>& battle_match_infos);
	virtual int ShuffleMatchInfo(std::vector<CorpsBattleMatchInfo>& battle_match_infos);
	virtual void GenerateMatchOrder(const std::vector<CorpsBattleMatchInfo>& battle_match_infos, std::set<int64_t>& success_corps_set);
	virtual void UpdateBattleInfoRecord(CorpsBattleInfo& info1, bool win, int target_index, int target_level, int battle_time);
	virtual void UpdateBattleInfoCapacityScore(CorpsBattleInfo& info, int capacity_score) {} 
	virtual void SyncOrderToNormalServer();
	virtual void SyncBattleRecordFromOtherBattle(const CorpsBattleInfo& info, int is_win) {}
	virtual void OnTryTeleportPlayer(RoleInfo* pInfo) {}
	virtual void OnRoamCreateBattleRequest(const PB::ipt_corps_center_battle_create_battle& msg) {}
	virtual void OnRoamTransferPlayer(ruid_t roleid, int64_t corps_id, int center_battle_type, int battle_tid, int battle_id) {}
	virtual void OnRoamBattleFinish(int battle_index, int src_zoneid, int creator_win, int param, int param2, const std::map<int64_t, std::tuple<int64_t, int>> &param_map = std::map<int64_t, std::tuple<int64_t, int>>()) {}
	virtual bool IsCorpsInMatch(int64_t corps_id) const;
    virtual void AddBattleInfoChanged(const CorpsBattleInfo* pInfoChanged, BattleInfoChangeType type) {}
    virtual void OnGSNotifyResult(ruid_t role_one, ruid_t role_two, int grade_change_one, int grade_change_two, int result) {}
    virtual void IdipSetEliminateBattleScore(int64_t group_id, int score) {}
	virtual void IdipReMatchBattle() {}
	virtual void IdipRenameCorps(int64_t group_id, const std::string& name) {}
	virtual void ClearEliminateBattleScore() {}
	virtual void CorpsEliminateBattleNotify(const PB::ipt_eliminate_battle_notify& notify) {}
	virtual void OnHundredCorpsBattleOperation(const PB::ipt_hundred_corps_battle_operator& proto) {}
	virtual void OnHundredCorpsBattleOperation(const PB::ipt_hundred_corps_battle_debug& proto) {}
	virtual void CorpsEliminateBattleEnter(ruid_t roleid, PB::ipt_corps_eliminate_knockout_battle_enter& enter) {}
	virtual void CorpsEliminateBattleEnterRe(ruid_t roleid, PB::ipt_corps_eliminate_knockout_battle_enter_re& re) {}
	virtual void OnInsertOrderBattleInfo(CorpsBattleInfo* info) {}
	virtual void OnUpdateOrderBattleInfo(CorpsBattleInfo* new_info, int origin_index, int origin_value, int origin_time) {}
	virtual int GetOrederedValue(const CorpsBattleInfo* info) const { return 0; }
	virtual int GetOrederedIndex(const CorpsBattleInfo* info) const { return -1; }
	virtual int GetOrederedTime(const CorpsBattleInfo* info) const { return 0; }
	virtual ruid_t GetOrederedID(const CorpsBattleInfo* info) const { return (info == NULL)? 0 : info->battle_info.id();; }
	virtual CorpsBattleOrderBase* GetBattleOrderByGroupID(ruid_t corps_id) { return NULL; }
	virtual int ClearBattleOrderResult(int state, int index) { return -1; }
	virtual void DebugSetScore(RoleInfo * pInfo, int score) {}
	virtual void DebugClearScore() {}
	virtual bool IsGuessValid(const PB::eliminate_battle_guess_data_t& guess_data) const { return false;}
	virtual int GetGuessRightCount(const PB::eliminate_battle_guess_data_t& guess_data) const { return 0;}
	virtual int GetGuessTotalRightCount(const ::google::protobuf::RepeatedPtrField< ::PB::eliminate_battle_guess_data_t >& guess_datas) const { return 0;}
	virtual bool FillRightGuessData(int state, PB::eliminate_battle_guess_data_t& guess_data) const { return false;}
    virtual void CheckFighterProf() const {}
    virtual void DebugSetWatchSize(int size) {}
	
	virtual void OnCreateBattle(int battle_id, int battle_index, int param, int ret) = 0;
	virtual void OnBattleClose(int battle_id, int battle_index, int param) = 0;
	virtual void PlayerSelectSubBattleType(RoleInfo* pInfo, CORPS_BATTLE2_SUB_BATTLE_TYPE sub_battle_type, bool debug) {}
	virtual void PlayerQuerySubBattleInfo(RoleInfo* pInfo, bool debug) {}
	virtual void PlayerEnterBattle(RoleInfo* pInfo, int param1, int param2, int param3, bool debug) = 0;
	virtual void OnPlayerLogin(RoleInfo* pInfo) = 0;
	virtual void OnSendBattleResult(int64_t target_id, int battle_id, int battle_index, int inst_param, int creator_win, int64_t param, int64_t param2, const std::map<int64_t, std::tuple<int64_t, int>> &param_map = std::map<int64_t, std::tuple<int64_t, int>>()) = 0;
	virtual void OnPlayerEnterCorpsBattleResult(int battle_id, int battle_index, ruid_t roleid, int result) = 0;
	virtual void OnCorpsBattle2SubBattleInfo(int battle_type, int battle_id, int battle_index, int corps_one_score, int corps_two_score) {}
	virtual void OnPlayerChangeScene(RoleInfo* pInfo, int world_tid, int scene_tag) = 0;
	virtual void OnPlayerLogout(RoleInfo* pInfo) = 0;
	virtual void OnPlayerLeaveCorps(ruid_t roleid, int64_t corps_id, int leave_type) {}
	virtual void  PlayerAssistBattle(RoleInfo* pInfo, int battle_type){}
	virtual void  DebugPlayerAssistBattle(int battle_type, int AddScore) {};

	virtual void IDIPSetBattleScore(int64_t corps_id, int value) {}
	virtual void IDIPSetWinCount(int64_t corps_id, int win_count) {}
	virtual void IDIPCreateOrder(int state, int offset_time) {}
	virtual void IDIPClearOrder(int state) {}
	virtual void IDIPClearCenterTop() {}
	virtual void IDIPClearAllData() {}
	virtual void IDIPCreateOneOrder(int64_t corps_id_1, int64_t corps_id_2, int state, int time) {}
	virtual void IDIPClearHundredBattleInfo() {}


	virtual void PlayerSearchBattleList(RoleInfo* pInfo, int64_t target_corps_id, int battle_begin_time, int page, int search_battle_type, int search_city_index, int param);
	virtual void InitMatchInfo(CorpsPtr pCorp, CorpsBattleMatchInfo& match_info);
	virtual void InitBattleOrder(const CorpsBattleMatchInfo& match_info, CorpsBattleOrderBasePtr porder, bool creator) { porder->battle_order.set_battle_type(m_battle_type); }
	virtual void OnLoadBattleOrder(CorpsBattleOrderBasePtr porder) {}
	virtual int PlayerGetTowerBattleAward(RoleInfo* pInfo) { return -1; }
	virtual int PlayerGetCityBattleAward(RoleInfo* pInfo) { return -1; }
	virtual int PlayerGetFactionBattleAward(RoleInfo* pInfo) { return -1; }
	virtual int PlayerGetServerBattleAward(RoleInfo* pInfo, int award_type) { return -1; }
	virtual void OnCorpsRename(int64_t corps_id) {}
    virtual void OnRefreshRoamCorpsSeason(int64_t corps_id, int64_t repu_value) {}
	virtual void OnCorpsBeMerged(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set);
	virtual void OnCorpsBeMergedDuringBattleClosed(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set);
	virtual void OnCorpsBeMergedDuringBattleMatched(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set);
	virtual void OnCorpsBeMergedDuringBattleBegin(int64_t merged_id, int64_t merge_id, const std::set<ruid_t>& be_merged_set);

	virtual int CalcFirstBattleLevel(int activity) const;
	virtual void InitRecord(PB::corps_battle_record * pRecord, const CorpsBattleOrderBasePtr pOrder, const CorpsBattleInfo* pCorpsBattleInfo, ruid_t roleid, int param) { }

	virtual int GetMaxRecordsCount() { return 0; }

	virtual bool CopyRecords(PB::npt_player_search_corps_battle_list_result& result, const CorpsBattleInfo* pCorpsBattleInfo, ruid_t roleid, bool self, bool last, int param);
	virtual void CorpsCenterBattleUpdate(PB::ipt_corps_center_battle_update& update);
    virtual void OnReceiveMatchResult(std::vector<PB::corps_battle_order>& orders, int param, int zoneid) {}
    virtual void OnReceiveBattleBegin(PB::corps_battle_order* pbOrder, int center_zoneid) {}
    virtual void OnReceiveBattleResult(PB::corps_battle_order* pbOrder, int param, int param2) {}
    virtual void OnReceiveLocalScore(int battle_index, int corps_index, ruid_t roleid, const Octets name, int64_t corps_id, int season_repu, int64_t damage, int zoneid) {}
    virtual void OnReceiveCenterScore(int battle_index, int corps_index, ruid_t roleid, const Octets name, int64_t corps_id, int season_repu, int64_t damage, int zoneid) {}
    virtual void OnReceiveLocalSeasonRepu(int battle_index, int corps_index, int64_t corps_id, int season_repu, int zoneid) {}
    virtual void OnReceiveCenterSeasonRepu(int battle_index, int corps_index, int64_t corps_id, int season_repu, int zoneid) {}
	virtual void CorpsServerBattleUpdate(PB::ipt_corps_server_battle_update& update) { }
	virtual void CorpsCenterBattleEnter(ruid_t roleid, PB::ipt_corps_center_battle_enter& enter) { }
	virtual void CorpsServerBattleEnter(ruid_t roleid, PB::ipt_corps_server_battle_enter& enter) { }
	virtual void CorpsCenterBattleLeave(ruid_t roleid, PB::ipt_corps_center_battle_leave& leave) { }
	
	virtual bool CheckRoamBattle(const ruid_t& roleid, int & inst_id) { return false; }

	virtual void CheckAddServerPlayer(RoleInfo *pInfo) { }
	virtual void CheckDelServerPlayer(RoleInfo *pInfo) { }
	virtual void CheckServerPlayers() { }
	virtual void CollectValidPlayers(int target_zoneid) { }
	virtual int CorpsServerBattleSwapPos(RoleInfo *pInfo, PB::npt_corps_server_battle_swap_pos &msg, bool debug = false){ return 0; }
	virtual void CorpsServerBattleControl(int type, bool enable, int zoneid) { }
	virtual zone_id_t GetCenterZoneID() const { return 0 ;}
	virtual void UploadBattleInfoToCenter(int day_begin) {}
	virtual void UpdateScoreTopList(int64_t corps_id, int corps_battle_score); 
    virtual int GetTopListID() const { return 0 ;}
    virtual int GetOldTopListID() const { return 0; }
    virtual void OnFinalsEnd() {}

	virtual void OnLoadCorpsBattleInfo(CorpsBattleInfo& info) { }
	virtual void OnLoadCorpsBattleFinished() { m_load_finished = true; }
	virtual void GetServerBattlePosInfo(ruid_t roleid, int& city_index, int& city_pos) { city_index = 0; city_pos = 0; }
	virtual void OnLoadCorpsBattleConfig() { }
	virtual bool IsCorpsInBattle(int64_t corps_id) const { return false; }
	virtual int GetServerLevelNeed() const { return CORPS_BATTLE_NEED_SERVER_LEVEL; }
	virtual void CollectCorpsIDFromOrder(std::vector<int64_t>& ids) const;
	virtual void DebugSetBattleTime();
	virtual void PrintMatchLog(const CorpsBattleInfo* pInfo, int result, ruid_t target_corps_id, const std::map<int, int64_t> &corps_info = std::map<int, int64_t>());
	virtual void PrintEndLog(const CorpsBattleOrderBasePtr pOrder, const CorpsBattleInfo* pInfo, int result, ruid_t target_corps_id, const std::map<int, std::tuple<int64_t, int>> &corps_rank = std::map<int, std::tuple<int64_t, int>>());
public:
	void FixCorpsInfoBattleIndexByCorpsID(int update_type, PB::corps_battle_order *pbOrder);

    inline size_t CorpsOrdersSize() const
    {
        return m_corps_battle_orders.size();
    }
	const CorpsBattleInfo* GetBattleInfoByCorpsID(int64_t corps_id) const
	{
		auto mit = m_corps_battle_ids.find(corps_id);
		if (mit != m_corps_battle_ids.end())
		{
			auto it = m_corps_battle_infos.find(mit->second);
			if (it != m_corps_battle_infos.end())
				return &(it->second);
		}
		return nullptr;
	}
	CorpsBattleInfo* GetBattleInfoByCorpsID(int64_t corps_id)
	{
		auto mit = m_corps_battle_ids.find(corps_id);
		if (mit != m_corps_battle_ids.end())
		{
			auto it = m_corps_battle_infos.find(mit->second);
			if (it != m_corps_battle_infos.end())
				return &(it->second);
		}
		return nullptr;
	}

	const CORPS_BATTLE_INFO_MAP& GetCorpsBattleInfos() const
	{
		return m_corps_battle_infos;
	}
    CorpsBattleInfo* GetBattleInfoByIndex(int index)
    {
        auto iter = m_corps_battle_infos.find(index);
        if (iter == m_corps_battle_infos.end())
        {
            return nullptr;
        }
        return &(iter->second);
    }
    const CorpsBattleInfo* GetBattleInfoByIndex(int index) const
    {
        auto iter = m_corps_battle_infos.find(index);
        if (iter == m_corps_battle_infos.end())
        {
            return nullptr;
        }
        return &(iter->second);
    }
	inline void __InsertBattleInfo(CorpsBattleInfo& bi)
	{
		m_corps_battle_infos[bi.battle_info.index()] = bi;
		m_corps_battle_ids[bi.battle_info.id()] = bi.battle_info.index();
	}
	bool InsertBattleInfo(const PB::corps_battle_info& battle_info)
	{
		if (m_corps_battle_ids.find(battle_info.id()) != m_corps_battle_ids.end())
			return false;
		CorpsBattleInfo bi;
		bi.battle_info = battle_info;
		bi.battle_info.set_index(GetBattleIndex());
		bi.dirty = true;
		__InsertBattleInfo(bi);
		m_info_dirty = true;
		LOG_TRACE("BATTLE_STATE_DEBUG::InsertBattleInfo:corps_id=%ld:index=%d:battle_state=%d",
		          battle_info.id(), bi.battle_info.index(), battle_info.battle_state());
		return true;
	}
	bool UpdateBattleInfo(const PB::corps_battle_info& battle_info)
	{
		auto it = m_corps_battle_ids.find(battle_info.id());
		if (it == m_corps_battle_ids.end())
			return InsertBattleInfo(battle_info);
		else
		{
			auto tit = m_corps_battle_infos.find(it->second);
			if (tit == m_corps_battle_infos.end())
				return InsertBattleInfo(battle_info);
			else
			{
				CorpsBattleInfo& bi = tit->second;
				int old_battle_state = bi.battle_info.battle_state();
				bi.battle_info = battle_info;
				bi.battle_info.set_index(it->second);
				bi.dirty = true;
				m_info_dirty = true;
				LOG_TRACE("BATTLE_STATE_DEBUG::UpdateBattleInfo:corps_id=%ld:index=%d:old_battle_state=%d:battle_state=%d",
				          battle_info.id(), bi.battle_info.index(), old_battle_state, bi.battle_info.battle_state());
				return true;
			}
		}
		return false;
	}

    CorpsBattleOrderBasePtr GetCorpsBattleOrder(int index)
    {
        auto it = m_corps_battle_orders.find(index);
        if (it != m_corps_battle_orders.end())
        {
            return it->second;
        }
        return CorpsBattleOrderBasePtr();
    }

    const CorpsBattleOrderBasePtr GetCorpsBattleOrder(int index) const
    {
        auto it = m_corps_battle_orders.find(index);
        if (it != m_corps_battle_orders.end())
        {
            return it->second;
        }
        return CorpsBattleOrderBasePtr();
    }

    CorpsBattleOrderBasePtr GetCorpsBattleOrderByID(int64_t corps_id)
    {
        auto pInfo = GetBattleInfoByCorpsID(corps_id);
        if (!pInfo)
        {
            return CorpsBattleOrderBasePtr();
        }
        if (pInfo->OrderIndex() <= 0)
        {
            return CorpsBattleOrderBasePtr();
        }
        return GetCorpsBattleOrder(pInfo->OrderIndex());
    }

    CorpsBattleInfo* GetOpponentCorpsBattleInfo(int64_t corps_id)
    {
        auto pInfo = GetBattleInfoByCorpsID(corps_id);
        if (!pInfo)
        {
            return nullptr;
        }
        if (pInfo->OrderIndex() <= 0)
        {
            return nullptr;
        }
        auto pOrder = GetCorpsBattleOrder(pInfo->OrderIndex());
        if (!pOrder)
        {
            return nullptr;
        }
        int index = (pOrder->CorpsOneIndex() == pInfo->Index()) ? pOrder->CorpsTwoIndex() : pOrder->CorpsOneIndex();

        auto it = m_corps_battle_infos.find(index);
        if (it == m_corps_battle_infos.end())
        {
            return nullptr;
        }
        return &(it->second);
    }

	void IDIPSetCorpsBattleInfoValue(int64_t corps_id, int set_type, int set_value);
	void ClearBattleOrder();
	void ClearBattleInfo();
	void ClearBattleInfoOrder();
	void IDIPClearBattle();
	virtual void IDIPSyncBattleMatchResult() {}
	inline void SetInfoDirty() { m_info_dirty = true; }
	inline void SetDebugMode() { m_debug_mode = true; }
	inline void ClearDebugMode() { m_debug_mode = false; }
	inline bool DebugMode() const { return m_debug_mode; }
	virtual int DebugGetBattleCorpsScore(int64_t corps_id) const
	{
		return 0;
	}
	virtual void DebugAddBattleCorpsScore(int64_t corps_id, int inc_score)
	{

	}	

    virtual int CheckEliminateGuessVaild(PB::ELIMINATE_BATTLE_STATE state, int64_t corps_id, int order_index) const
    {
        return -1;
    }

    virtual int GetEliminateGuessResult(PB::ELIMINATE_BATTLE_STATE state, int64_t corps_id, int order_index) const
    {
        return -1;
    }

	  virtual int CheckGuessVaild(int state, int64_t corps_id, int order_index) const
    {
        return -1;
    }

    virtual int GetGuessResult(int state, int64_t corps_id, int order_index) const
    {
        return -1;
    }
	virtual void UpdateWinCount(CorpsBattleInfo& info, int is_win);
	//设置社团竞赛天梯分数
	virtual void DebugSetCorpsBattleScore(int64_t corps_id, int value) { }
	//获取社团竞赛天梯分数
	virtual int DebugGetCorpsBattleScore(int64_t corps_id)
	{
		return -1;
	}
	void DebugSetCorpsBattleWinCount(int64_t corps_id, int total_count, int win_count);
	void DebugSetCorpsBattleCenterWinCount(int64_t corps_id, int total_count, int win_count);
	inline void DebugSetInLoading() 
	{
		m_load_finished = false;
	}
	void DebugSetBattleState(int battle_state);
	void DebugShowBattleInfo() const;
	void DebugShowBattleOrder() const;

	void DebugClearBattle();
	int DebugGetBattleLevel(RoleInfo* pInfo);

	PB::corps_battle_config& GetConfig() { return m_config; }
	inline int GetBattleType() const { return m_battle_type; }

	virtual bool IsRecentTarget(const CorpsBattleInfo& corps_info, int target_index, int recent_count) const;
	int InitCorpsBattleInfo(const CorpsBattleMatchInfo& match_info);
	int InitCorpsBattleInfo(int64_t corps_id);
	inline void SetBattleLevel(CorpsBattleInfo& info, int battle_level) { info.battle_info.set_battle_level(battle_level); }
	inline int GetBattleLevel(const CorpsBattleInfo& info) const { return info.battle_info.battle_level(); }
	inline void SetBattleScore(CorpsBattleInfo& info, int battle_score) { info.battle_info.set_corps_battle_score(battle_score); }
	inline int GetBattleScore(const CorpsBattleInfo& info) const { return info.battle_info.corps_battle_score(); }
    inline int64_t GetBattleCorpsSeasonRepu(const CorpsBattleInfo& info) const { return info.battle_info.corps_season_repu(); }
	
    //处理社团赛季积分在战斗结束的时候
    virtual void HandleCorpsSeasonOnEndBattle(PB::CORPS_SEASON_ACTIVITY_TYPE act_type, int64_t id_1, int64_t orgin_repu_1, int64_t score_1, 
                                                int64_t id_2, int64_t origin_repu_2, int64_t score_2, int64_t key, bool draw = false, 
                                                GNET::Octets name_1 = GNET::Octets(), GNET::Octets name_2 = GNET::Octets()) const;
	virtual void HandleHundredCorpsBattleCorpsRepu(int64_t id_1, int64_t score_1, int64_t id_2, int64_t score_2, int64_t key, bool draw = false, GNET::Octets name_1 = GNET::Octets(), GNET::Octets name_2 = GNET::Octets()) const{}
protected:
	bool TrySave();
	virtual void JudgeState(int now_time);
	int GetBattleIndex() { return ++m_battle_info_count; }
	virtual PB::corps_battle_config::CORPS_BATTLE_STATE GetNowState(int now_time) const;
	int GetEndBattleTime(int now_time) const;
	virtual int GetNextBattleTime(int now_time, int battle_index) const;
	virtual int GetBattleInstanceType() const { return INSTANCE_CORPS_BATTLE; }
	int GetBattleTimeIndex(int battle_time);
	int CreateBattle(int battle_tid, int order_index, int64_t creator, const std::string& creator_name, int64_t challenger, const std::string& challenger_name, ruid_t player_npc, int p1, int p2, int p3, int p4, int p5, int p6 = 0, int p7 = 0, const std::string& player_npc_name = std::string(), int64_t player_npc_idphoto = 0, int player_npc_level = 0, int64_t fightcapacity = 0, int p9 = 0, int p10 = 0, int p11 = 0, int p12 = 0, bool is_corps_battle3 = false, int64_t challenger2 = 0, const std::string& challenger2_name = "", int64_t challenger3 = 0, const std::string& challenger3_name = "", const std::vector<int64_t>& p64s = std::vector<int64_t>());
	bool InitBattleTotalTime(int week_begin);
	void InitRecordNormalInfo(PB::npt_player_search_corps_battle_list_result& result, const CorpsBattleInfo& info);
	void DumpRecord(const corps_battle_record& record);
	void PrintEndLogBase(GNET::LogMessage *slogger, const CorpsBattleInfo* pInfo, int result, ruid_t target_corps_id);

protected:
	int m_battle_type;				//社团竞赛的类型
	int m_battle_info_count;			//目前已经保存的社团竞赛的数量
	bool m_order_dirty;				//匹配信息是否需要保存数据库
	bool m_info_dirty;				//社团竞赛信息是否需要保存数据库
	bool m_config_dirty;				//战斗配置信息是否需要保存数据库
    bool m_guess_dirty;             //
	bool m_save_db;					//是否正在写数据库
	bool m_load_finished;				//是否加载成功
	int m_battle_continue_time;			//战斗持续时间
	int m_battle_count_max;				//最多开多少场战斗
	PB::corps_battle_config	m_config;		//战斗的配置信息(存库)
	BATTLE_TID_VEC m_battle_tids;			//战场可能随机到的地图
	CORPS_BATTLE_INFO_MAP m_corps_battle_infos;	//所有社团的竞赛相关信息(存库)
    ORDER_BATTLE_INFO_MAP m_order_battle_infos; //排序的帮派相关信息
	CORPS_BATTLE_ORDER_MAP m_corps_battle_orders;	//战斗的匹配信息(存库)
	CORPS_BATTLE_ID_MAP m_corps_battle_ids;		//社团ID对应竞赛信息索引的表
	BATTLE_TIME_VEC m_battle_match_time;		//社团竞赛匹配时间
	BATTLE_TIME_VEC m_battle_begin_time;		//社团竞赛开启时间
	BATTLE_TIMESTAMP_VEC m_battle_total_time;	//本周的社团竞赛时间戳的集合
    BATTLE_GUESS_MAP m_battle_guess_map;
	bool m_debug_mode;				//是否开启debug模式
};
}

#endif
