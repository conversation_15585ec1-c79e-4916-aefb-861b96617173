#include "corps_eliminate_battle.h"
#include "eliminatebattlemanager.h"
#include "gprotoc/ipt_center_battle_roam_teleport_player.pb.h"
#include "gprotoc/ipt_corps_eliminate_knockout_battle_enter.pb.h"
#include "gprotoc/npt_player_enter_corps_battle.pb.h"
#include "gprotoc/eliminate_battle_guess_data_t.pb.h"
#include "gprotoc/corps_battle_config.pb.h"
#include "gprotoc/npt_player_search_corps_battle_list_result.pb.h"
#include "gprotoc/ipt_eliminate_battle_notify.pb.h"
#include "gprotoc/eliminate_group.pb.h"
#include "gprotoc/npt_knockout_eliminate_battle_begin.pb.h"
#include "gprotoc/SEARCH_CORPS_SERVER_BATTLE_TYPE.pb.h"
#include "gprotoc/npt_player_search_corps_battle_list.pb.h"
#include "gprotoc/ELIMINATE_BATTLE_STATE.pb.h"
#include "gprotoc/corps_eliminate_battle_player.pb.h"
#include "gprotoc/guess_data_t.pb.h"
#include "gprotoc/CORPS_ELIMINATE_WATCH_STATE.pb.h"
#include "gprotoc/eliminate_battle_guess_data.pb.h"
#include "gprotoc/corps_battle_record.pb.h"
#include "gprotoc/npt_center_eliminate_battle_end.pb.h"
#include "gprotoc/corps_battle_order.pb.h"
#include "gprotoc/npt_player_enter_corps_battle_result.pb.h"
#include "gprotoc/npt_knockout_eliminate_battle_end.pb.h"
#include "gprotoc/corps_battle_info.pb.h"
#include "gprotoc/ipt_corps_eliminate_knockout_battle_enter_re.pb.h"
#include "gprotoc/ELIMINATE_ARENA_STAGE.pb.h"

#include "campaignmanager.h"
#include "centerbattle.h"
#include "eliminategroupmanager.h"
#include "conv_charset.h"
#include "localmacro.h"
#include "parsestring.h"
#include "speakmanager.h"
#include "diaoxiang_manager.h"

using namespace GNET;

int CORPS_ELIMINATE_BATTLE_WATCH_PLAYER_NUM_MAX = 200;

bool CorpsEliminateBattle::Init(lua_State *L, LuaWrapper& lw)
{
	if (!CorpsBattleEntry::Init(L, lw))
	{
		return false;
	}

	{
		if (!lw.gExec("GetCorpsVIPWatchPlayers"))
		{
			LOG_TRACE("初始化VIP观战者执行脚本出错,err_msg=%s\n", lw.ErrorMsg());
			return false;
		}
		if (!lua_isnumber(L, -1))
		{
			LOG_TRACE("初始化VIP观战者脚本返回出错2\n");
			return false;
		}
		int vip_count = LUA_TOINTEGER(L, -1);
		for (int i = 0; i < vip_count; ++i)
		{
			int index = -2 - i;
			if (!lua_isnumber(L, index))
			{
				LOG_TRACE("初始化VIP观战脚本出错3,%d\n", i);
				return false;
			}
			ruid_t roleid = LUA_TOINTEGER(L, index);
			m_vip_watch_players.insert(roleid);
			printf("CorpsEliminateBattle::Init::roleid=" PRINT64":vip_count=%d\n", roleid, vip_count);
		}
	}
	return true;
}

void CorpsEliminateBattle::ClearEliminateBattleScore()
{
	LOG_TRACE("CorpsEliminateBattle::ClearEliminateBattleScore");
	int local_begin_time = EliminateBattleManager::GetInstance().GetBattleTime(PB::EBS_LOCAL_SCORE_BATTLE_BEGIN);
	for (auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end(); it != eit; ++it)
	{
		CorpsBattleInfo& info = it->second;
		if (info.battle_info.eliminate_battle_score() != ELIMINATE_GROUP_SCORE_BATTLE_INIT_SCORE || info.battle_info.total_count() != 0 ||
		        info.battle_info.win_count() != 0 || info.battle_info.consecutive_win_count() != 0)
		{
			int origin_score = GetOrederedValue(&info);
			int index = GetOrederedIndex(&info);
			int time = GetOrederedTime(&info);
			info.battle_info.set_eliminate_battle_score(ELIMINATE_GROUP_SCORE_BATTLE_INIT_SCORE);
			info.battle_info.set_total_count(0);
			info.battle_info.set_win_count(0);
			info.battle_info.set_consecutive_win_count(0);
			info.dirty = true;
			m_info_dirty = true;
			LOG_TRACE("CorpsEliminateBattle::ClearEliminateBattleScore:corpsbattleid=%ld:corpsbattleindex=%d:apple_time=%d:local_begin_time=%d",
			          info.battle_info.id(), index, info.battle_info.apply_time(), local_begin_time);
			if (info.battle_info.apply_time() >= local_begin_time)
			{
				SortEliminateBattleScore(&info, index, origin_score, time);
			}
		}
	}
}

void CorpsEliminateBattle::AddBattleInfoChanged(const CorpsBattleInfo *pInfoChanged, BattleInfoChangeType type)
{
	if (!pInfoChanged)
	{
		return;
	}
	LOG_TRACE("CorpsEliminateBattle::AddBattleInfoChanged::group_id=%ld:change_type=%d", pInfoChanged->ID(), (int)type);
	if (m_info_changed.find(pInfoChanged->ID()) != m_info_changed.end())
	{
		return;
	}
	m_info_changed[pInfoChanged->ID()] = pInfoChanged;
}

void CorpsEliminateBattle::AddBattleOrderChanged(CorpsBattleOrderBasePtr pOrderChanged)
{
	if (!pOrderChanged)
	{
		return;
	}
	LOG_TRACE("CorpsEliminateBattle::AddBattleOrderChanged::order_index=%d", pOrderChanged->Index());
	if (m_order_changed.find(pOrderChanged->Index()) != m_order_changed.end())
	{
		return;
	}
	m_order_changed[pOrderChanged->Index()] = pOrderChanged;
}
void CorpsEliminateBattle::OnGSNotifyResult(ruid_t role_one, ruid_t role_two, int grade_change_one, int grade_change_two, int result) /*result为1的时候，role_one胜利*/
{
	if (!CenterManager::GetInstance().IsCenter())
	{
		return;
	}
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}

	//查找战队信息
	ruid_t battle_id_one = role_one;
	ruid_t battle_id_two = role_two;
	LOG_TRACE("CorpsEliminateBattle::OnGSNotifyResult::role_one=" PRINT64":group_one=%ld:grade_change_one=%d:role_two=" PRINT64":group_two=%ld:grade_change_two=%d:result=%d",
	          role_one, battle_id_one, grade_change_one, role_two, battle_id_two, grade_change_two, result);
	CorpsBattleInfo *pInfo_one = GetBattleInfoByCorpsID(battle_id_one);
	CorpsBattleInfo *pInfo_two = GetBattleInfoByCorpsID(battle_id_two);
	if (!pInfo_one && !pInfo_two)
	{
		return;
	}
	CorpsBattleInfo *pWinner = NULL;
	CorpsBattleInfo *pLoser = NULL;
	int grade_change_win = 0;
	int grade_change_lose = 0;
	if (result == 1)
	{
		pWinner = pInfo_one;
		pLoser = pInfo_two;
		grade_change_win = grade_change_one;
		grade_change_lose = grade_change_two;
	}
	else
	{
		pWinner = pInfo_two;
		pLoser = pInfo_one;
		grade_change_win = grade_change_two;
		grade_change_lose = grade_change_one;
	}

	int timestamp = GNET::Timer::GetTime();
	int index_winner = 0;
	int index_loser = 0;
	int origin_score_winner = 700;
	int origin_score_loser = 700;
	int origin_time_winner = 0;
	int origin_time_loser = 0;
	int consecutive_win_winner = 0;
	int consecutive_win_loser = 0;
	int total_count_winner = 0;
	int total_count_loser = 0;
	int win_count_winner = 0;
	int win_count_loser = 0;
	GNET::Octets name_winner_base;
	GNET::Octets name_winner;
	GNET::Octets name_loser_base;
	GNET::Octets name_loser;

	//开始dd计算积分
	if (pWinner)
	{
		index_winner = GetOrederedIndex(pWinner);		//->battle_info.index();
		origin_score_winner = GetOrederedValue(pWinner); 	//->battle_info.eliminate_battle_score();
		origin_time_winner = GetOrederedTime(pWinner);		//->battle_info.score_result_timestamp();
		name_winner_base = GNET::Octets(pWinner->battle_info.name().c_str(), pWinner->battle_info.name().size());
		CharsetConverter::conv_charset_u2l(name_winner_base, name_winner);
	}
	if (pLoser)
	{
		index_loser = GetOrederedIndex(pLoser);;
		origin_score_loser = GetOrederedValue(pLoser);
		origin_time_loser = GetOrederedTime(pLoser);
		name_loser_base = GNET::Octets(pLoser->battle_info.name().c_str(), pLoser->battle_info.name().size());
		CharsetConverter::conv_charset_u2l(name_loser_base, name_loser);
	}

	int new_score_winner = origin_score_winner + grade_change_win;
	int new_score_loser = origin_score_loser + grade_change_lose;

	if (pWinner)
	{
		UpdateWinCount(*pWinner, true);
		pWinner->battle_info.set_apply_time(timestamp);
		pWinner->battle_info.set_eliminate_battle_score(new_score_winner);
		pWinner->dirty = true;
		consecutive_win_winner = pWinner->ConsecutiveWinCount();
		total_count_winner = pWinner->TotalCount();
		win_count_winner = pWinner->WinCount();
	}
	if (pLoser)
	{
		UpdateWinCount(*pLoser, false);
		pLoser->battle_info.set_apply_time(timestamp);
		pLoser->battle_info.set_eliminate_battle_score(new_score_loser);
		pLoser->dirty = true;
		consecutive_win_loser = pLoser->ConsecutiveWinCount();
		total_count_loser = pLoser->TotalCount();
		win_count_loser = pLoser->WinCount();
	}

	m_info_dirty = true;

	SortEliminateBattleScore(pWinner, index_winner, origin_score_winner, origin_time_winner);
	SortEliminateBattleScore(pLoser, index_loser, origin_score_loser, origin_time_loser);

	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_SCORE_BATTLE_RESULT);

	if (pWinner)
	{
		PB::corps_battle_info *pInfo = notify.add_eliminate_battle_infos();
		pInfo->CopyFrom(pWinner->battle_info);
	}
	if (pLoser)
	{
		PB::corps_battle_info *pInfo = notify.add_eliminate_battle_infos();
		pInfo->CopyFrom(pLoser->battle_info);
	}
	if (notify.eliminate_battle_infos_size() != 0)
	{
		CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
	}

	SendScoreBattleResult(pWinner, 1, grade_change_win);
	SendScoreBattleResult(pLoser, 0, grade_change_lose);
	//通知客户端

	LOG_TRACE("CorpsEliminateBattle::OnGSNotifyResult:role_one=" PRINT64":role_two=" PRINT64":result=%d:winner_name=%.*s:loser_name=%.*s:origin_score_winner=%d:new_score_winner=%d:consecutive_win_count_winner=%d:origin_score_loser=%d:new_score_loser=%d:consecutive_win_count_loser=%d:total_count_winner=%d:total_count_loser=%d:win_count_winner=%d:win_count_loser=%d",
	          role_one, role_two, result, (int)name_winner.size(), (const char *)name_winner.begin(),
	          (int)name_loser.size(), (const char *)name_loser.begin(), origin_score_winner, new_score_winner,
	          consecutive_win_winner, origin_score_loser, new_score_loser, consecutive_win_loser,
	          total_count_winner, total_count_loser, win_count_winner, win_count_loser);
}
void CorpsEliminateBattle::IdipRenameCorps(int64_t group_id, const std::string& name)
{
	if (!CenterManager::GetInstance().IsCenter())
	{
		return;
	}
	CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(group_id);
	if (!pInfo)
	{
		return;
	}

	pInfo->battle_info.set_name(name);
	pInfo->dirty = true;
	m_info_dirty = true;

	PB::ipt_eliminate_battle_notify notify;
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_IDIP_RENAME);
	notify.add_eliminate_battle_infos()->CopyFrom(pInfo->battle_info);
	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);

	LOG_TRACE("CorpsEliminateBattle::IdipRenameCorps id=%ld name_size=%d", group_id, (int)name.size());
}
void CorpsEliminateBattle::IdipSetEliminateBattleScore(int64_t group_id, int score)
{
	if (!CenterManager::GetInstance().IsCenter())
	{
		return;
	}
	CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(group_id);
	if (!pInfo)
	{
		return;
	}
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	int index = GetOrederedIndex(pInfo);
	int origin_score = GetOrederedValue(pInfo);
	int origin_time = GetOrederedTime(pInfo);
	pInfo->battle_info.set_eliminate_battle_score(score);
	pInfo->dirty = true;
	m_info_dirty = true;
	SortEliminateBattleScore(pInfo, index, origin_score, origin_time);

	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_SCORE_BATTLE_RESULT);
	notify.add_eliminate_battle_infos()->CopyFrom(pInfo->battle_info);
	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);

	LOG_TRACE("CorpsEliminateBattle::IdipSetEliminateBattleScore::group_id=%ld, score=%d, index=%d, origin_score=%d, origin_time=%d", group_id, score, index, origin_score, origin_time);
}
void CorpsEliminateBattle::IdipReMatchBattle()
{
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_MATCH);

	//把帮派对应的本周竞赛记录清了
	for (auto it = m_corps_battle_infos.begin(); it != m_corps_battle_infos.end(); ++it)
	{
		CorpsBattleInfo& info = it->second;
		if (info.battle_info.order_index() > 0)
		{
			info.battle_info.set_order_index(0);
			info.dirty = true;
			if (!m_info_dirty)
			{
				m_info_dirty = true;
			}
		}
	}

	// 删除重复
	ORDER_BATTLE_INFO_SET need_remove_keys;
	for (auto it1 = m_order_battle_infos.begin(); it1 != m_order_battle_infos.end(); ++it1)
	{
		if (need_remove_keys.find(it1->first) != need_remove_keys.end())
		{
			continue;
		}
		auto it2 = it1;
		++it2;
		for (; it2 != m_order_battle_infos.end(); ++it2)
		{
			if (need_remove_keys.find(it2->first) != need_remove_keys.end())
			{
				continue;
			}
			if (it1->first._index != it2->first._index)
			{
				continue;
			}
			if (it1->first._time > it2->first._time)
			{
				need_remove_keys.insert(it2->first);
			}
			else
			{
				need_remove_keys.insert(it1->first);
			}
		}
	}
	LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle1:infos_size=%zu, need_remove_size=%zu", m_order_battle_infos.size(), need_remove_keys.size());
	for (auto it = need_remove_keys.begin(); it != need_remove_keys.end(); ++it)
	{
		m_order_battle_infos.erase(*it);
		LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle1:index=%d:value=%d:time=%d", it->_index, it->_value, it->_time);
	}
	LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle1:infos_size=%zu", m_order_battle_infos.size());

	size_t match_count = 36;
	size_t real_match_count = 0;
	bool has_single = false;
	std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> > candidate_guess;
	normal_step_tag tag = normal_step_tag();
	size_t order_size = GetOrderSizeHelper(m_order_battle_infos, tag);
	if (order_size >= match_count * 2)
	{
		real_match_count = match_count;
	}
	else
	{
		real_match_count = order_size / 2;
		if (order_size % 2 != 0)
		{
			has_single = true;
		}
	}
	m_corps_battle_orders.clear();

	auto iter = GetBeginIterHelper(m_order_battle_infos, tag);
	auto eiter = m_order_battle_infos.end();
	int counter = 0;

	std::vector<int/*index*/> candidate_one;	//上半区的队伍
	std::vector<int/*index*/> candidate_two;	//下半区的队伍
	std::map<int/*index*/, int/*rank*/> rank_one;
	std::map<int/*index*/, int/*rank*/> rank_two;

	int guess_count = 0;
	int candidate_single = 0;			//落单的队伍
	int init_battle_time = EliminateBattleManager::GetInstance().GetBattleTime((PB::ELIMINATE_BATTLE_STATE)(PB::EBS_CENTER_SCORE_BATTLE_END + 1));
	PB::ELIMINATE_BATTLE_STATE battle_state = (PB::ELIMINATE_BATTLE_STATE)(PB::EBS_CENTER_SCORE_BATTLE_END + 1);
	int battle_interval = EliminateBattleManager::GetInstance().GetBattleInterval(battle_state);
	int index = 0;
	int single_index = -1;
	int total_count = real_match_count * 2 + (has_single ? 1 : 0);
	int rand_num = has_single ? abase::Rand(0, total_count - 1) : -1;

	//TODO:填充上半区的队伍
	for (; iter != eiter && index < (int)real_match_count; iter++, counter++)
	{
		int tmp_index = GetIndex(iter);
		LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle2:battle_state=%d:index=%d:count=%d:rand_num=%d", (int)battle_state, tmp_index, counter, rand_num);
		if (counter == rand_num)
		{
			single_index = tmp_index;
			LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle2:battle_state=%d:index=%d:count=%d:rand_num=%d", (int)battle_state, tmp_index, counter, rand_num);
		}
		else
		{
			candidate_one.push_back(tmp_index);
			rank_one[tmp_index] = counter;
			index++;
		}
	}
	//TODO:填充下半区的队伍
	index = 0;
	for (; iter != eiter && index < (int)real_match_count; iter++, counter++)
	{
		int tmp_index = GetIndex(iter);
		LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle3:battle_state=%d:index=%d:count=%d:rand_num=%d", (int)battle_state, tmp_index, counter, rand_num);
		if (counter == rand_num)
		{
			single_index = tmp_index;
			LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle3:battle_state=%d:index=%d:count=%d:rand_num=%d", (int)battle_state, tmp_index, counter, rand_num);
		}
		else
		{
			candidate_two.push_back(tmp_index);
			rank_two[tmp_index] = counter;
			index++;
		}
	}

	if (iter != eiter && has_single && counter == rand_num)
	{
		single_index = GetIndex(iter);
		LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle4:battle_state=%d:index=%d:count=%d:rand_num=%d", (int)battle_state, single_index, counter, rand_num);

	}

	std::reverse(candidate_two.begin(), candidate_two.end());

	//TODO:填充落单的队伍
	if (has_single && single_index > 0)
	{
		candidate_two.push_back(single_index);
		LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle5:battle_state=%d:index=%d", (int)battle_state, single_index);
	}
	// TODO set battle_info
	auto state = ((PB::EBS_CENTER_SCORE_BATTLE_END - EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_BEGIN + 1) / 2) * 3 + 1;
	for (iter = GetBeginIterHelper(m_order_battle_infos, tag); iter != m_order_battle_infos.end(); ++iter)
	{
		// get info from candidate
		auto p_battle_info = GetBattleInfo(iter);
		if (p_battle_info)
		{
			auto tmp = state;
			// 轮空队伍自动获胜
			if (p_battle_info->battle_info.index() == single_index)
			{
				tmp += 2;
			}
			p_battle_info->SetBattleState(tmp);
		}
	}

	LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle6:candidate_one=%ld:candidate_two=%ld:battle_state=%d:battle_interval=%d:real_match_count=%ld:has_single=%s:single_index=%d:rand_num=%d:total_count=%d", candidate_one.size(), candidate_two.size(), (int)battle_state, battle_interval, real_match_count, has_single ? "true" : "false", single_index, rand_num, total_count);
	counter = 0;
	int rand_order = 0;
	for (index = 0; index < (int)candidate_one.size(); index++, counter++)
	{
		int index_one = candidate_one.at(index);
		int index_two = candidate_two.at(index);
		int order_index = 0;
		auto& battle_info_one = m_corps_battle_infos[index_one];
		auto& battle_info_two = m_corps_battle_infos[index_two];
		int diff = rank_two[index_two] - rank_one[index_one];
		rand_order = abase::Rand(0, 1);
		if (rand_order == 0)
		{
			order_index = __CreateBattleOrder(&battle_info_one, &battle_info_two, notify, init_battle_time + counter * battle_interval);
		}
		else
		{
			order_index = __CreateBattleOrder(&battle_info_two, &battle_info_one, notify, init_battle_time + counter * battle_interval);
		}

		if (order_index <= 0)
		{
			continue;
		}

		if ((int)candidate_guess.size() < guess_count)
		{
			candidate_guess.insert(std::make_pair(diff, order_index));
			LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle7:Direct:rank_one=%d:index_one=%d:rank_two=%d:index_two=%d:diff=%d:order_index=%d", rank_one[index_one], index_one, rank_two[index_two], index_two, diff, order_index);
		}
		else if (candidate_guess.begin()->first > diff)
		{
			candidate_guess.erase(candidate_guess.begin());
			candidate_guess.insert(std::make_pair(diff, order_index));
			LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle7:Replace:rank_one=%d:index_one=%d:rank_two=%d:index_two=%d:diff=%d:order_index=%d", rank_one[index_one], index_one, rank_two[index_two], index_two, diff, order_index);
		}
	}
	//TODO:	打印竞猜列表
	for (auto it = candidate_guess.begin(); it != candidate_guess.end(); ++it)
	{
		LOG_TRACE("CorpsEliminateBattle::IdipReMatchBattle8:candidate_guess:battle_state=%d:rank_diff=%d:order_index=%d", (int)battle_state, it->first, it->second);
	}
	//TODO: 处理落单的前面分组的玩家队伍
	if (has_single && index < (int)candidate_two.size())
	{
		candidate_single = candidate_two.at(index);
		auto& battle_info = m_corps_battle_infos[candidate_single];
		int order_index = __CreateBattleOrder(&battle_info, NULL, notify, init_battle_time + counter * battle_interval);
		if ((int)candidate_guess.size() < guess_count && order_index > 0)
		{
			candidate_guess.insert(std::make_pair(0, order_index));
			LOG_TRACE("CorpsEliminateBattle:IdipReMatchBattle9:FillSingle:index_single=%d:order_index=%d", candidate_single, order_index);
		}
	}

	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
	m_info_dirty = true;
	m_order_dirty = true;
}
void CorpsEliminateBattle::SendScoreBattleResult(const CorpsBattleInfo *pInfo, int result, int score_diff)
{
	if (!pInfo)
	{
		return;
	}

	LOG_TRACE("CorpsEliminateBattle::SendScoreBattleResult::group_id=%ld:result=%d:socre_diff=%d", pInfo->ID(), result, score_diff);
	PB::npt_center_eliminate_battle_end notify;
	notify.set_battle_result(result);
	notify.set_grade(pInfo->EliminateBattleScore());
	notify.set_grade_diff(score_diff);
	for (int i = 0; i < pInfo->battle_info.eliminate_battle_players_size(); i++)
	{
		const auto& player = pInfo->battle_info.eliminate_battle_players(i);
		RoleInfo *pRole = RoleMap::Instance().FindOnline(player.role().id());
		if (!pRole)
		{
			continue;
		}

		pRole->SendMessage2Client(notify);
		LOG_TRACE("CorpsEliminateBattle::SendScoreBattleResult:roleid=" PRINT64":battleid=" PRINT64":result=%d:score_diff=%d",
		          player.role().id(), pInfo->battle_info.id(), result, score_diff);
	}
}
void CorpsEliminateBattle::SendKnockoutBattleResult(const CorpsBattleOrder *pOrder, int result)
{
	if (!pOrder)
	{
		return;
	}

	PB::npt_knockout_eliminate_battle_end notify;
	notify.set_battle_result(result);
	notify.set_battle_index(pOrder->battle_order.index());
	notify.set_group_one_index(pOrder->battle_order.corps_battle_index_1());
	notify.set_group_two_index(pOrder->battle_order.corps_battle_index_2());

	auto iit1 = m_corps_battle_infos.find(pOrder->battle_order.corps_battle_index_1());
	if (iit1 != m_corps_battle_infos.end())
	{
		notify.set_self_index(pOrder->battle_order.corps_battle_index_1());
		auto *pInfo = &(iit1->second);
		for (int i = 0; i < pInfo->battle_info.eliminate_battle_players_size(); i++)
		{
			const auto& player = pInfo->battle_info.eliminate_battle_players(i);
			RoleInfo *pRole = RoleMap::Instance().FindOnline(player.role().id());
			if (!pRole)
			{
				continue;
			}

			pRole->SendMessage2Client(notify);
			LOG_TRACE("CorpsEliminateBattle::SendKnockoutBattleResult:Corps_One:roleid=" PRINT64":self_index=%d:battle_index=%d:result=%d",
			          player.role().id(), notify.self_index(), notify.battle_index(), notify.battle_result());
		}
	}

	auto iit2 = m_corps_battle_infos.find(pOrder->battle_order.corps_battle_index_2());
	if (iit2 != m_corps_battle_infos.end())
	{
		notify.set_self_index(pOrder->battle_order.corps_battle_index_2());
		auto *pInfo = &(iit2->second);
		for (int i = 0; i < pInfo->battle_info.eliminate_battle_players_size(); i++)
		{
			const auto& player = pInfo->battle_info.eliminate_battle_players(i);
			RoleInfo *pRole = RoleMap::Instance().FindOnline(player.role().id());
			if (!pRole)
			{
				continue;
			}

			pRole->SendMessage2Client(notify);
			LOG_TRACE("CorpsEliminateBattle::SendKnockoutBattleResult:Corp_Two:roleid=" PRINT64":self_index=%d:battle_index=%d:result=%d",
			          player.role().id(), notify.self_index(), notify.battle_index(), notify.battle_result());
		}
	}

}
bool CorpsEliminateBattle::Update(int now_time, int day_begin, int week_begin)
{
	if (!CenterManager::GetInstance().IsCenter())
	{
		TrySave();
		return true;
	}

	if (!CorpsBattleEntry::Update(now_time, day_begin, week_begin))
	{
		return false;
	}

	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return false;
	}

	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_UPDATE);

	for (auto it = m_info_changed.begin(), eit = m_info_changed.end(); it != eit; ++ it)
	{
		const CorpsBattleInfo *pBattleInfo = it->second;
		if (!pBattleInfo)
		{
			continue;
		}
		PB::corps_battle_info *pInfo = notify.add_eliminate_battle_infos();
		pInfo->CopyFrom(pBattleInfo->battle_info);
	}
	m_info_changed.clear();

	for (auto it = m_order_changed.begin(), eit = m_order_changed.end(); it != eit; ++ it)
	{
		auto pBattleOrder = it->second;
		if (!pBattleOrder)
		{
			continue;
		}
		PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
		pOrder->CopyFrom(pBattleOrder->battle_order);
	}
	m_order_changed.clear();

	if (notify.eliminate_battle_infos_size() > 0 || notify.eliminate_battle_orders_size() > 0)
	{
		CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
	}

	return true;
}
void CorpsEliminateBattle::DumpOrder()
{
	LOG_TRACE("CorpsEliminateBattle::DumpOrder");
	int rank = 1;
	for (auto it = m_order_battle_infos.begin(); it != m_order_battle_infos.end() && rank <= CLIENT_ORDER_SIZE; it++, rank++)
	{
		const auto *pInfo = it->second;
		std::string info_str = pInfo->GatherStrInfo();
		LOG_TRACE("CorpsEliminateBattle::DumpOrder:rank=%d:group_id=%ld:index=%d:value=%d:time=%d:info_address=%p:info=%s",
		          rank, pInfo->ID(), it->first._index, it->first._value, it->first._time, it->second, info_str.c_str());
	}
}
void CorpsEliminateBattle::SortEliminateBattleScore(CorpsBattleInfo *new_info, int origin_index, int origin_value, int origin_time)
{
	if (!new_info)
	{
		return;
	}
	CBI_Key key(origin_index, origin_value, origin_time);

	auto it_origin = m_order_battle_infos.find(key);
	CorpsBattleInfo *origin_info = NULL;
	if (it_origin != m_order_battle_infos.end())
	{
		origin_info = it_origin->second;
	}

	m_order_battle_infos.erase(key);

	OnInsertOrderBattleInfo(new_info);

	LOG_TRACE("CorpsEliminateBattle::SortEliminateBattleScore:origin_index=%d:origin_value=%d:origin_time=%d:origin_info=%p:new_index=%d:new_value=%d:new_time=%d:new_info=%p",
	          origin_index, origin_value, origin_time, origin_info, new_info->battle_info.index(),
	          new_info->battle_info.eliminate_battle_score(), GetOrederedTime(new_info), new_info);
}
void CorpsEliminateBattle::OnInsertOrderBattleInfo(CorpsBattleInfo *info)
{
	if (!info)
	{
		return;
	}

	CBI_Key key(GetOrederedIndex(info), GetOrederedValue(info), GetOrederedTime(info));
	if (info->battle_info.apply_time() < EliminateBattleManager::GetInstance().GetBattleTime(EBS_LOCAL_SCORE_BATTLE_BEGIN))
	{
		LOG_TRACE("CorpsEliminateBattle::OnInsertOrderBattleInfo:index=%d:value=%d:time=%d:info_address=%p:apply_time=%d:battle_time=%d",
		          key._index, key._value, key._time, info, info->battle_info.apply_time(), EliminateBattleManager::GetInstance().GetBattleTime(EBS_LOCAL_SCORE_BATTLE_BEGIN));
		return;
	}

	m_order_battle_infos.insert(std::make_pair(key, info));
	LOG_TRACE("CorpsEliminateBattle::OnInsertOrderBattleInfo:group_id=%ld:index=%d:value=%d:time=%d:info_address=%p", info->ID(), key._index, key._value, key._time, info);
	//DumpOrder();
}

void CorpsEliminateBattle::OnLoadCorpsBattleInfo(CorpsBattleInfo& info)
{
	auto *pInfo = GetBattleInfoByCorpsID(info.ID());
	if (!pInfo)
	{
		return;
	}
	CBI_Key key(GetOrederedIndex(pInfo), GetOrederedValue(pInfo), GetOrederedTime(pInfo));

	if (pInfo->battle_info.apply_time() < EliminateBattleManager::GetInstance().GetBattleTime(EBS_LOCAL_SCORE_BATTLE_BEGIN))
	{
		LOG_TRACE("CorpsEliminateBattle::OnLoadCorpsBattleInfo:id=%ld:index=%d:value=%d:time=%d:pInfo->address=%p:apply_time=%d:battle_time=%d",
		          pInfo->ID(), key._index, key._value, key._time, pInfo, pInfo->battle_info.apply_time(), EliminateBattleManager::GetInstance().GetBattleTime(EBS_LOCAL_SCORE_BATTLE_BEGIN));
		return;
	}
	m_order_battle_infos.insert(std::make_pair(key, pInfo));
	LOG_TRACE("CorpsEliminateBattle::OnLoadCorpsBattleInfo:group_id=%ld:group_index=%d:group_score=%d:total_count=%d:win_count=%d:index=%d:value=%d:time=%d:pInfo->address=%p:src_zone_id=%d", pInfo->ID(), info.Index(), pInfo->EliminateBattleScore(), pInfo->TotalCount(), pInfo->WinCount(), key._index, key._value, key._time, pInfo, info.SrcZoneID());
	//DumpOrder();
}
void CorpsEliminateBattle::OnLoadBattleOrder(CorpsBattleOrderBasePtr pOrder)
{
	if (!pOrder)
	{
		return;
	}
	LOG_TRACE("CorpsEliminateBattle::OnLoadBattleOrder::order_index=%d:battle_time=%d:result=%d:group_index1=%d:group_id1=%ld:group_index2=%d:group_id2=%ld:order_map_size=%zu", pOrder->Index(), pOrder->BattleTime(), pOrder->Result(), pOrder->CorpsOneIndex(), pOrder->CorpsOneID(), pOrder->CorpsTwoIndex(), pOrder->CorpsTwoID(), m_corps_battle_orders.size());
}
void CorpsEliminateBattle::PlayerSearchBattleList(RoleInfo *pInfo, int64_t target_corps_id, int battle_begin_time, int page, int search_battle_type, int search_city_index, int param)
{
	LOG_TRACE("DS::CorpsEliminateBattle::PlayerSearchBattleList battle_type:%d roleid:%ld target_corps_id:%ld search_type:%d battle_begin_time:%d page:%d",
	          m_battle_type, pInfo->roleid, target_corps_id, search_battle_type, battle_begin_time, page);

	PB::npt_player_search_corps_battle_list_result result;
	result.set_battle_type(m_battle_type);
	result.set_battle_begin_time(battle_begin_time);
	result.set_page(page);
	result.set_search_battle_brief((PB::SEARCH_CORPS_SERVER_BATTLE_TYPE)search_battle_type);

	switch (search_battle_type)
	{
	case PB::SEARCH_CORPS_SERVER_BATTLE_TYPE::SCSBT_BATTLE_OEDER:
	{
		if (CenterManager::GetInstance().IsCenter())
		{
			return ;
		}
		auto it = m_corps_battle_orders.begin(), ie = m_corps_battle_orders.end();
		for (; it != ie; it++)
		{
			if (!it->second)
			{
				continue;
			}
			auto pOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
			if (!pOrder)
			{
				continue;
			}
			const auto& order = *pOrder;
			int battle_time_index = GetBattleTimeIndex(order.battle_order.battle_time());
			if (battle_time_index == battle_begin_time)
			{
				bool first_null = false;
				auto iit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
				if (iit == m_corps_battle_infos.end())
				{
					first_null = true;
					iit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
					if (iit == m_corps_battle_infos.end())
					{
						continue;
					}
				}
				const CorpsBattleInfo& info1 = iit->second;

				if (first_null)
				{
					iit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
				}
				else
				{
					iit = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
				}
				const CorpsBattleInfo *info2 = NULL;
				if (iit != m_corps_battle_infos.end())
				{
					info2 = &(iit->second);
				}

				PB::corps_battle_record *pRecord = result.add_records();
				pRecord->set_self_index(order.battle_order.corps_battle_index_1());
				pRecord->set_self_id(info1.battle_info.id());
				pRecord->set_self_zoneid(info1.battle_info.zoneid());
				pRecord->set_self_name(info1.battle_info.name());
				pRecord->set_battle_time(order.battle_order.battle_time());
				{
					int result_tmp = order.battle_order.result();
					if (first_null && result_tmp == 0)
					{
						result_tmp = 1;
					}
					else if (first_null && result_tmp == 1)
					{
						result_tmp = 0;
					}
					pRecord->set_result(result_tmp);
				}
				pRecord->set_battle_index(it->first);
				pRecord->set_battle_stage(order.battle_order.battle_stage());

				pRecord->set_target_index(order.battle_order.corps_battle_index_2());
				pRecord->set_target_id(info2 ? info2->battle_info.id() : 0);
				pRecord->set_target_name(info2 ? info2->battle_info.name() : std::string());
				pRecord->set_target_zoneid(info2 ? info2->battle_info.zoneid() : 0);

				if (order.param < (m_debug_watch_size > 0 ? m_debug_watch_size : CORPS_ELIMINATE_BATTLE_WATCH_PLAYER_NUM_MAX))
				{
					pRecord->set_watch_state(PB::CORPS_ELIMINATE_WATCH_STATE_NORMAL);
				}
				else
				{
					pRecord->set_watch_state(PB::CORPS_ELIMINATE_WATCH_STATE_CROWD);
				}

				LOG_TRACE("DS::CorpsEliminateBattle::PlayerSearchBattleList:SCSBT_BATTLE_OEDER:order_id=%d:group_index_1=%d:group_id_1=" PRINT64":group_index_2=%d:group_id_2=" PRINT64":self_zoneid=%d:target_zoneid=%d:battle_time=%d:battle_result=%d", pRecord->battle_index(), pRecord->self_index(), pRecord->self_id(), pRecord->target_index(), pRecord->target_id(), pRecord->self_zoneid(), pRecord->target_zoneid(), pRecord->battle_time(), pRecord->result());
			}
		}
		pInfo->SendMessage2Client(result);
	}
	break;

	case PB::SEARCH_CORPS_SERVER_BATTLE_TYPE::SCSBT_BATTLE_INFO:
	{
		if (CenterManager::GetInstance().IsCenter())
		{
			return;
		}

		CBI_Key _key(0, 0, 0);
		int rank = -1;
		if (pInfo->SNSReady() && pInfo->friends.GetEliminateGroupID())
		{
			const CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(pInfo->friends.GetEliminateGroupID());
			_key._index = GetOrederedIndex(pCorpsBattleInfo);
			_key._value = GetOrederedValue(pCorpsBattleInfo);
			_key._time = GetOrederedTime(pCorpsBattleInfo);
			if (pCorpsBattleInfo)
			{
				//赋值一下自己的帮派竞赛等级等信息
				result.set_battle_level(pCorpsBattleInfo->battle_info.battle_level());
				result.set_total_count(pCorpsBattleInfo->battle_info.total_count());
				result.set_win_count(pCorpsBattleInfo->battle_info.win_count());
				result.set_eliminate_battle_score(pCorpsBattleInfo->battle_info.eliminate_battle_score());
				result.set_consecutive_win_count(pCorpsBattleInfo->battle_info.consecutive_win_count());
			}
		}
		int count = 0;
		auto it = m_order_battle_infos.begin();
		auto ie = m_order_battle_infos.end();
		for (; it != ie && count < CLIENT_ORDER_SIZE; ++it, ++count)
		{
			auto *pItem = it->second;
			if (!pItem)
			{
				continue;
			}
			if (it->first == _key)
			{
				rank = count;
			}

			PB::corps_battle_info *pBattleInfo = result.add_battle_infos();
			pBattleInfo->CopyFrom(pItem->battle_info);
			LOG_TRACE("DS::CorpsEliminateBattle::PlayerSearchBattleList:SCSBT_BATTLE_INFO:battle_id=" PRINT64":zoneid=%d",
			          pItem->battle_info.id(), pItem->battle_info.zoneid());
		}
		result.set_eliminate_battle_rank(rank);
		pInfo->SendMessage2Client(result);
		LOG_TRACE("DS::CorpsEliminateBattle::PlayerSearchBattleList:SCSBT_BATTLE_INFO:roleid=" PRINT64":top_list.size=%zu:battle_info.size=%d:rank=%d", pInfo->roleid, m_order_battle_infos.size(), result.battle_infos_size(), rank);
	}
	break;

	case PB::SEARCH_CORPS_SERVER_BATTLE_TYPE::SCSBT_BATTLE_GUESS:
	{
		if (CenterManager::GetInstance().IsCenter())
		{
			return;
		}

		int battle_state = (int)EliminateBattleManager::GetInstance().GetState() + 1;
		auto it = m_battle_guess_map.find(battle_state);
		if (it != m_battle_guess_map.end())
		{
			auto& guess_set = it->second;
			auto *pGuess = result.mutable_candidate_guess();
			pGuess->set_battle_state((PB::ELIMINATE_BATTLE_STATE)it->first);
			for (auto iter = guess_set.begin(); iter != guess_set.end(); ++iter)
			{
				pGuess->add_candidate_guess(*iter);
				LOG_TRACE("DS::CorpsEliminateBattle::PlayerSearchBattleList:SCSBT_BATTLE_GUESS:roleid=" PRINT64":battle_state=%d:candidate_guess=%d", pInfo->roleid, battle_state, *iter);
			}
		}
		pInfo->SendMessage2Client(result);
	}
	break;

	default:
	{
		CorpsBattleEntryBase::PlayerSearchBattleList(pInfo, target_corps_id, battle_begin_time, page, search_battle_type, search_city_index, param);
	}
	break;
	}
}
void CorpsEliminateBattle::CorpsEliminateBattleNotify(const PB::ipt_eliminate_battle_notify& notify)
{
	LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify type=%d", (int)notify.notify_type());
	switch (notify.notify_type())
	{
	case PB::ipt_eliminate_battle_notify::EBNT_STATE:
	{
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_STATE center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}
		if (notify.eliminate_battle_state_set() && (notify.eliminate_battle_state() == PB::EBS_NOT_BEGIN || notify.eliminate_battle_state() == PB::EBS_LOCAL_SCORE_BATTLE_END))
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_STATE:ClearEliminateBattleScore:battle_state=%d", (int)notify.eliminate_battle_state());
			ClearEliminateBattleScore();
		}
		else if (notify.eliminate_battle_state_set() && notify.eliminate_battle_state() == PB::EBS_ENROLL_BEGIN)
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_STATE:ClearBattleInfo:battle_state=%d", (int)notify.eliminate_battle_state());
			ClearBattleInfo();
		}
		else if (notify.eliminate_battle_state_set() && notify.eliminate_battle_state() == PB::EBS_CENTER_KNOCKOUT_BATTLE_2_END)
		{
			//喊话
			std::vector<NameRuidPair> battle_info_vec;
			for (auto it = m_corps_battle_orders.begin(); it != m_corps_battle_orders.end(); ++it)
			{
				auto pOrder = it->second;
				if (pOrder->battle_order.battle_stage() == (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() - 1))
				{
					int battle_index = 0;
					if (pOrder->battle_order.result() == 1)
					{
						battle_index = pOrder->battle_order.corps_battle_index_1();
					}
					else if (pOrder->battle_order.result() == 0)
					{
						battle_index = pOrder->battle_order.corps_battle_index_2();
					}
					if (battle_index == 0)
					{
						break;
					}
					auto iter = m_corps_battle_infos.find(battle_index);
					if (iter != m_corps_battle_infos.end())
					{
						auto& battle_info = iter->second;
						NameRuidPair _info;
						battle_info.ToNameRuid(_info);
						battle_info_vec.push_back(_info);
						LOG_TRACE("CorpsEliminateBattle:CorpsEliminateBattleNotify EBNT_STATE Top One Speak order_index=%d:battle_info_id=" PRINT64":battle_info_index=%d", pOrder->battle_order.index(), _info.id, battle_index);
					}
					break;
				}
			}
			//处理喊话
			if (battle_info_vec.size() > 0)
			{
				BroadcastSpeak(battle_info_vec);
			}
		}
		LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify EBNT_STATE eliminate_battle_state_set=%d eliminate_battle_state=%d",
		          notify.eliminate_battle_state_set(), notify.eliminate_battle_state());
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_SCORE_BATTLE_RESULT:
	{
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_SCORE_BATTLE_RESULT center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}
		for (int i = 0; i < notify.eliminate_battle_infos_size(); ++ i)
		{
			const PB::corps_battle_info& battle_info = notify.eliminate_battle_infos(i);
			CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(battle_info.id());
			int origin_score = 0;
			int index = -1;
			int time = 0;
			if (pInfo)
			{
				origin_score = GetOrederedValue(pInfo);
				index = GetOrederedIndex(pInfo);
				time = GetOrederedTime(pInfo);
			}
			UpdateBattleInfo(battle_info);
			pInfo = GetBattleInfoByCorpsID(battle_info.id());
			if (!pInfo)
			{
				continue;
			}
			pInfo->battle_info.set_index(battle_info.index());
			pInfo->battle_info.set_zoneid(MERGE_ZONE(battle_info.id()));
			if (m_battle_info_count <= battle_info.index())
			{
				m_battle_info_count = battle_info.index() + 1;
			}
			//排个序
			SortEliminateBattleScore(pInfo, index, origin_score, time);

			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GradeSet(pInfo->ID(), GetOrederedValue(pInfo));
			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().RewardChange(pInfo->ID(), PB::EA_STAGE_WHOLE);
			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().SetIsMatching(pInfo->ID(), false);
			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().SetCenterMatchSuccessTime(pInfo->ID(), 0);
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_SCORE_BATTLE_RESULT::group_id=%ld:index=%d:score=%d:total_count=%d:win_count=%d:consecutive_win_count=%d:zoneid=%d",
			          pInfo->ID(), pInfo->Index(), pInfo->EliminateBattleScore(), pInfo->TotalCount(), pInfo->WinCount(), pInfo->ConsecutiveWinCount(), pInfo->battle_info.zoneid());
		}
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_UPDATE:
	case PB::ipt_eliminate_battle_notify::EBNT_CONNECT_REFRESH:
	{
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_UPDATE center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}
		for (int i = 0; i < notify.eliminate_battle_infos_size(); ++ i)
		{
			const PB::corps_battle_info& battle_info = notify.eliminate_battle_infos(i);

			UpdateBattleInfo(battle_info);

			CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(battle_info.id());
			if (!pInfo)
			{
				continue;
			}

			pInfo->battle_info.set_index(battle_info.index());
			pInfo->battle_info.set_zoneid(MERGE_ZONE(battle_info.id()));
			if (m_battle_info_count <= battle_info.index())
			{
				m_battle_info_count = battle_info.index() + 1;
			}

			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify notify_type=%d group_id=%ld:index=%d:score=%d:zoneid=%d",
			          (int)notify.notify_type(), battle_info.id(), battle_info.index(),
			          battle_info.eliminate_battle_score(), pInfo->battle_info.zoneid());
		}

		for (int i = 0; i < notify.eliminate_battle_orders_size(); ++ i)
		{
			const PB::corps_battle_order& battle_order = notify.eliminate_battle_orders(i);
			auto it = m_corps_battle_orders.find(battle_order.index());
			if (it == m_corps_battle_orders.end())
			{
				auto porder = CreateBattleOrder();
				auto& order = *porder;
				order.battle_order = battle_order;
				order.dirty = true;
				m_corps_battle_orders[porder->battle_order.index()] = porder;

				m_order_dirty = true;

				LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify 1 notify_type=%d order_index=%d",
				          (int)notify.notify_type(), battle_order.index());
				continue;
			}

			auto pOrder = it->second;
			if (!pOrder)
			{
				continue;
			}

			pOrder->battle_order.CopyFrom(battle_order);
			pOrder->dirty = true;

			m_order_dirty = true;

			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify 2 notify_type=%d order_index=%d",
			          (int)notify.notify_type(), battle_order.index());
		}

		//TODO:处理竞猜
		for (int i = 0; i < notify.candidate_guess_size(); ++i)
		{
			auto& candidate_guess = notify.candidate_guess(i);

			auto iter = m_battle_guess_map.find(candidate_guess.battle_state());
			if (iter == m_battle_guess_map.end())
			{
				continue;
			}
			if (iter != m_battle_guess_map.end())
			{
				iter->second.clear();
			}
			for (int j = 0; j < candidate_guess.candidate_guess_size(); j++)
			{
				iter->second.insert(candidate_guess.candidate_guess(j));
				LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify EBNT_UPDATE|EBNT_CONNECT_REFRESH candidate_guess battle_state=%d:order_index=%d",
				          candidate_guess.battle_state(), candidate_guess.candidate_guess(j));
			}
			m_guess_dirty = true;
		}

	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_WARMUP_END:
	{
		if (CenterManager::GetInstance().IsCenter())
		{
			break;
		}

		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:WarmUpEnd center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}

		LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:WarmUpEnd");
		m_corps_battle_orders.clear();
		m_order_dirty = true;

		//把帮派对应的本周竞赛记录清了
		auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
		for (; it != eit; ++it)
		{
			CorpsBattleInfo& info = it->second;
			if (info.battle_info.order_index() > 0)
			{
				info.battle_info.set_last_order_index(0);
				info.battle_info.set_order_index(0);
				info.dirty = true;
				if (!m_info_dirty)
				{
					m_info_dirty = true;
				}
			}
		}

		TrySave();
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_MATCH:
	{
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_MATCH center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}
		//把帮派对应的本周竞赛记录清了
		auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
		for (; it != eit; ++it)
		{
			CorpsBattleInfo& info = it->second;
			if (info.battle_info.order_index() > 0)
			{
				if (info.battle_info.has_order_index())
				{
					info.battle_info.set_last_order_index(info.battle_info.order_index());
				}
				info.battle_info.set_order_index(0);
				info.dirty = true;
				if (!m_info_dirty)
				{
					m_info_dirty = true;
				}
			}
		}
		std::vector<NameRuidPair> battle_info_vec;
		for (int i = 0; i < notify.eliminate_battle_infos_size(); ++ i)
		{
			const PB::corps_battle_info& battle_info = notify.eliminate_battle_infos(i);

			UpdateBattleInfo(battle_info);

			CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(battle_info.id());
			if (!pInfo)
			{
				continue;
			}

			pInfo->battle_info.set_index(battle_info.index());
			pInfo->battle_info.set_zoneid(MERGE_ZONE(battle_info.id()));
			if (m_battle_info_count <= battle_info.index())
			{
				m_battle_info_count = battle_info.index() + 1;
			}
			if (PB::EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_END <= EliminateBattleManager::GetInstance().GetState())
			{
				ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().RewardChange(battle_info.id(), PB::EA_STAGE_KNOCK);
				if (PB::EBS_CENTER_KNOCKOUT_BATTLE_8_END <= EliminateBattleManager::GetInstance().GetState())
				{
					NameRuidPair _info;
					pInfo->ToNameRuid(_info);
					battle_info_vec.push_back(_info);
					LOG_TRACE("CorpsEliminateBattle:CorpsEliminateBattleNotify EBNT_MATCH Top Speak order_index=%d:battle_info_id=" PRINT64":battle_info_index=%d",
					          pInfo->battle_info.order_index(), pInfo->battle_info.id(), pInfo->battle_info.index());
				}
			}

			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_MATCH:group_id=%ld:score=%d:zoneid=%d",
			          battle_info.id(), battle_info.eliminate_battle_score(), pInfo->battle_info.zoneid());
		}

		for (int i = 0; i < notify.eliminate_battle_orders_size(); ++ i)
		{
			const PB::corps_battle_order& battle_order = notify.eliminate_battle_orders(i);
			auto porder = CreateBattleOrder();
			auto& order = *porder;
			order.battle_order = battle_order;
			order.dirty = true;
			m_corps_battle_orders[porder->battle_order.index()] = porder;

			m_order_dirty = true;

			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_MATCH:order_index=%d:index1=%d:index2=%d:id1=%ld:id2=%ld",
			          battle_order.index(), battle_order.corps_battle_index_1(), battle_order.corps_battle_index_2(),
			          battle_order.corps_battle_id_1(), battle_order.corps_battle_id_2());
		}
		//处理喊话
		if (PB::EBS_CENTER_KNOCKOUT_BATTLE_8_END <= EliminateBattleManager::GetInstance().GetState() && battle_info_vec.size() > 0)
		{
			BroadcastSpeak(battle_info_vec);
		}
		//处理竞猜
		if (notify.candidate_guess_size() != 1)
		{
			break;
		}
		auto& candidate_guess = notify.candidate_guess(0);
		int battle_state = (int)EliminateBattleManager::GetInstance().GetState() + 1;
		if (battle_state != (int)candidate_guess.battle_state())
		{
			break;
		}
		auto i = m_battle_guess_map.find(battle_state);
		if (i != m_battle_guess_map.end())
		{
			m_battle_guess_map[battle_state].clear();
		}
		for (int j = 0; j < candidate_guess.candidate_guess_size(); j++)
		{
			m_battle_guess_map[battle_state].insert(candidate_guess.candidate_guess(j));
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify EBNT_MATCH candidate_guess order_index=%d",
			          candidate_guess.candidate_guess(j));
		}
		m_guess_dirty = true;
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_BATTLE_END:
	{
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_BATTLE_END center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}
		if (!m_corps_battle_orders.empty())
		{
			m_corps_battle_orders.clear();
			m_order_dirty = true;
		}
		if (!m_battle_guess_map.empty())
		{
			m_battle_guess_map.clear();
			m_guess_dirty = true;
		}
		//把帮派对应的本周竞赛记录清了
		auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
		for (; it != eit; ++it)
		{
			CorpsBattleInfo& info = it->second;
			if (info.battle_info.order_index() > 0)
			{
				if (info.battle_info.has_order_index())
				{
					info.battle_info.set_last_order_index(info.battle_info.order_index());
				}
				info.battle_info.set_order_index(0);
				info.dirty = true;
				if (!m_info_dirty)
				{
					m_info_dirty = true;
				}
			}
		}

		LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify EBNT_BATTLE_END");
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_INST_CREATE:
	{
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBNT_INST_CREATE center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}
		if (notify.eliminate_battle_infos_size() != 2 || notify.eliminate_battle_orders_size() != 1)
		{
			break;
		}

		ruid_t group_ids[2] = {0};
		std::set<ruid_t> roleid_set;
		for (int i = 0; i < notify.eliminate_battle_infos_size(); i++)
		{
			const PB::corps_battle_info& battle_info = notify.eliminate_battle_infos(i);
			UpdateBattleInfo(battle_info);
			CorpsBattleInfo *pBattleInfo = GetBattleInfoByCorpsID(battle_info.id());
			if (!pBattleInfo)
			{
				break;
			}

			pBattleInfo->battle_info.set_index(battle_info.index());
			pBattleInfo->battle_info.set_zoneid(MERGE_ZONE(battle_info.id()));
			group_ids[i] = battle_info.id();
			for (int j = 0; j < battle_info.eliminate_battle_players_size(); ++j)
			{
				const PB::corps_eliminate_battle_player& player = battle_info.eliminate_battle_players(j);
				roleid_set.insert(player.role().id());
			}
			if (m_battle_info_count <= battle_info.index())
			{
				m_battle_info_count = battle_info.index() + 1;
			}
		}


		const PB::corps_battle_order& _order = notify.eliminate_battle_orders(0);
		auto it = m_corps_battle_orders.find(_order.index());
		if (it == m_corps_battle_orders.end())
		{
			auto porder = CreateBattleOrder();
			auto& order = *porder;
			order.battle_order = _order;
			order.dirty = true;
			m_corps_battle_orders[porder->battle_order.index()] = porder;

			it = m_corps_battle_orders.find(porder->battle_order.index());
			m_order_dirty = true;
		}

		auto pBattleOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
		if (!pBattleOrder)
		{
			break;
		}
		CorpsBattleOrder& order = *pBattleOrder;
		order.battle_inst_tid = notify.battle_inst_tid();
		order.battle_inst_id = notify.battle_inst_id();

		for (int i = 0; i < 2; i++)
		{
			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().NotifyKnockoutBegin(group_ids[i]);
		}
		LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify EBNT_INST_CREATE group_id1=%ld group_id2=%ld order_index=%d inst_tid=%d inst_id=%d",
		          group_ids[0], group_ids[1], _order.index(), order.battle_inst_tid, order.battle_inst_id);
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBTE_BATTLE_RESULT:
	{
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (notify.zoneid() != 0 && notify.zoneid() != pCenterBattle->GetCenterZoneID())
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify:EBTE_BATTLE_RESULT center_zoneid not match. notify_zoneid=%d center_zoneid=%d",
			          notify.zoneid(), pCenterBattle->GetCenterZoneID());
			break;
		}
		if (notify.eliminate_battle_orders_size() != 1)
		{
			break;
		}

		const PB::corps_battle_order& _order = notify.eliminate_battle_orders(0);
		if (notify.eliminate_battle_infos_size() != 2)
		{
			// 轮空时候，battle_infos_size应该是1
			if ((_order.corps_battle_index_1() > 0 && _order.corps_battle_index_2() > 0) || notify.eliminate_battle_infos_size() != 1)
			{
				break;
			}
		}

		auto it = m_corps_battle_orders.find(_order.index());
		if (it == m_corps_battle_orders.end())
		{
			auto porder = CreateBattleOrder();
			auto& order = *porder;
			order.battle_order = _order;
			order.dirty = true;
			m_corps_battle_orders[porder->battle_order.index()] = porder;

			m_order_dirty = true;
			break;
		}

		auto pBattleOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
		if (!pBattleOrder)
		{
			break;
		}
		CorpsBattleOrder& order = *pBattleOrder;
		order.battle_order.set_result(_order.result());
		order.battle_inst_tid = 0;
		order.battle_inst_id = 0;
		order.dirty = true;
		m_order_dirty = true;

		for (auto i = 0; i < notify.eliminate_battle_infos_size(); ++i)
		{
			const PB::corps_battle_info& battle_info = notify.eliminate_battle_infos(i);

			// 记录更新前的 battle_state
			CorpsBattleInfo *pInfoBefore = GetBattleInfoByCorpsID(battle_info.id());
			int old_battle_state = pInfoBefore ? pInfoBefore->BattleState() : 0;

			UpdateBattleInfo(battle_info);

			CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(battle_info.id());
			if (!pInfo)
			{
				continue;
			}

			pInfo->battle_info.set_index(battle_info.index());
			pInfo->battle_info.set_zoneid(MERGE_ZONE(battle_info.id()));
			if (m_battle_info_count <= battle_info.index())
			{
				m_battle_info_count = battle_info.index() + 1;
			}

			LOG_TRACE("BATTLE_STATE_DEBUG::ProcessNotify:corps_id=%ld:index=%d:old_battle_state=%d:battle_state=%d:cur_state=%d",
			          battle_info.id(), pInfo->battle_info.index(), old_battle_state, pInfo->BattleState(), (int)EliminateBattleManager::GetInstance().GetState());
		}
		LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify EBTE_BATTLE_RESULT order_index=%d:result=%d:state=%d",
		          _order.index(), _order.result(), (int)EliminateBattleManager::GetInstance().GetState());
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_NOTIFY_CHAMPION:
	{
		if (!notify.has_eliminate_champion())
		{
			break;
		}
		if (CenterManager::GetInstance().IsCenter())
		{
			break;
		}

		LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify::EBNT_NOTIFY_CHAMPION:eliminate_champion=%ld", notify.eliminate_champion());
		ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().OnGenerateChampion(notify.eliminate_champion());
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_BROADCAST_CHAMPION:
	{
		if (notify.champion_list_size() <= 0)
		{
			break;
		}
		if (CenterManager::GetInstance().IsCenter())
		{
			break;
		}
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		auto center_zoneid = pCenterBattle->GetCenterZoneID();
		if (notify.zoneid() != 0 && notify.zoneid() != center_zoneid)
		{
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify::EBNT_BROADCAST_CHAMPION zoneid=%d center_zoneid=%d",
			          notify.zoneid(), center_zoneid);
			break;
		}
		std::map<ruid_t, Octets> champion_list;
		for (int i = 0; i < notify.champion_list_size(); ++i)
		{
			auto& champion = notify.champion_list(i);
			champion_list.insert(std::make_pair(champion.roleid(), Octets(champion.status().c_str(), champion.status().size())));
			LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify::EBNT_BROADCAST_CHAMPION:eliminate_champion_roleid=" PRINT64"", champion.roleid());
		}
		diaoxiang_manager::GetInstance().OnRecvEliminateDiaoxiang(champion_list);
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_IDIP_RENAME:
	{
		if (notify.eliminate_battle_infos_size() != 1)
		{
			break;
		}
		if (CenterManager::GetInstance().IsCenter())
		{
			break;
		}
		auto& battle_info = notify.eliminate_battle_infos(0);
		UpdateBattleInfo(battle_info);
	}
	break;
	case PB::ipt_eliminate_battle_notify::EBNT_IDIP_REFRESH_BATTLE_INFO:
	{
		if (CenterManager::GetInstance().IsCenter())
		{
			break;
		}

		LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleNotify::EBNT_IDIP_REFRESH_BATTLE_INFO:count=%d",
		          notify.eliminate_battle_infos_size());

		for (int i = 0; i < notify.eliminate_battle_infos_size(); ++i)
		{
			const auto& battle_info = notify.eliminate_battle_infos(i);

			// 记录更新前的 battle_state
			CorpsBattleInfo *pInfoBefore = GetBattleInfoByCorpsID(battle_info.id());
			int old_battle_state = pInfoBefore ? pInfoBefore->BattleState() : 0;

			UpdateBattleInfo(battle_info);

			CorpsBattleInfo *pInfo = GetBattleInfoByCorpsID(battle_info.id());
			if (pInfo)
			{
				pInfo->battle_info.set_index(battle_info.index());
				pInfo->battle_info.set_zoneid(MERGE_ZONE(battle_info.id()));
				if (m_battle_info_count <= battle_info.index())
				{
					m_battle_info_count = battle_info.index() + 1;
				}

				LOG_TRACE("BATTLE_STATE_DEBUG::IDIPRefresh:corps_id=%ld:old_battle_state=%d:received_battle_state=%d:final_battle_state=%d",
				          battle_info.id(), old_battle_state, battle_info.battle_state(), pInfo->BattleState());
			}
		}

		// 保存更新后的数据
		TrySave();
	}
	break;
	default:
		break;
	}
}
bool CorpsEliminateBattle::IsGuessValid(const PB::eliminate_battle_guess_data_t& guess_data) const
{
	int battle_state = guess_data.state();
	auto it = m_battle_guess_map.find(battle_state);
	if (it == m_battle_guess_map.end())
	{
		return false;
	}

	auto& guess_set = it->second;
	if ((int)guess_set.size() < guess_data.guess_data_size())
	{
		return false;
	}

	for (int i = 0; i < guess_data.guess_data_size(); ++i)
	{
		int order_index = guess_data.guess_data(i).order_index();
		auto iter = guess_set.find(order_index);
		if (iter == guess_set.end())
		{
			return false;
		}
	}

	return true;
}
void CorpsEliminateBattle::CheckFighterProf() const
{
	int now_state = EliminateBattleManager::GetInstance().GetState();
	int now_time = Timer::GetTime();
	LOG_TRACE("CorpsEliminateBattle::CheckFighterProf:now_state=%d:now_time=%d", now_state, now_time);

	for (auto it = m_corps_battle_orders.begin(), eit = m_corps_battle_orders.end(); it != eit; ++ it)
	{
		auto pOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
		if (!pOrder)
		{
			continue;
		}
		if (pOrder->BattleTime() < now_time)
		{
			continue;
		}
		if (pOrder->Result() == 1 || pOrder->Result() == 0 || pOrder->Result() == 2)
		{
			continue;
		}
		auto iter = m_corps_battle_infos.find(pOrder->CorpsOneIndex());
		if (iter != m_corps_battle_infos.end())
		{
			auto& group_one_info = iter->second;
			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().CheckFighterProf(group_one_info.ID());
		}

		iter = m_corps_battle_infos.find(pOrder->CorpsTwoIndex());
		if (iter != m_corps_battle_infos.end())
		{
			auto& group_two_info = iter->second;
			ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().CheckFighterProf(group_two_info.ID());
		}
	}
}
bool CorpsEliminateBattle::FillRightGuessData(int state, PB::eliminate_battle_guess_data_t& guess_data) const
{
	//如果不是64进32
	if (state != (int)PB::EBS_CENTER_KNOCKOUT_BATTLE_64_BEGIN)
	{
		return false;
	}

	auto it = m_battle_guess_map.find(state);
	if (it == m_battle_guess_map.end())
	{
		return false;
	}

	auto& guess_set = it->second;

	//如果这个数据没有被设置
	if (!guess_data.has_state())
	{
		guess_data.set_state(PB::EBS_CENTER_KNOCKOUT_BATTLE_64_BEGIN);
		guess_data.set_get_prize(1);

		for (auto iter = guess_set.begin(); iter != guess_set.end(); ++iter)
		{
			auto pData = guess_data.add_guess_data();
			int order_index = *iter;
			auto pOrder = GetCorpsBattleOrder(order_index);
			if (!pOrder)
			{
				continue;
			}
			pData->set_order_index(order_index);
			pData->set_choice(pOrder->battle_order.result() + 1);
			LOG_TRACE("CorpsEliminateBattle::FillRightGuessData::order_index=%d:choice=%d", order_index, pData->choice());
		}
		return true;
	}
	else if ((guess_data.get_prize() == 0 || guess_data.get_prize() == 1) && guess_data.state() == PB::EBS_CENTER_KNOCKOUT_BATTLE_64_BEGIN)	//这段数据已经被设置，同时没有领奖
	{
		guess_data.clear_guess_data();
		guess_data.set_get_prize(1);
		for (auto iter = guess_set.begin(); iter != guess_set.end(); ++iter)
		{
			auto pData = guess_data.add_guess_data();
			int order_index = *iter;
			auto pOrder = GetCorpsBattleOrder(order_index);
			if (!pOrder)
			{
				continue;
			}
			pData->set_order_index(order_index);
			pData->set_choice(pOrder->battle_order.result() + 1);
			LOG_TRACE("CorpsEliminateBattle::FixRightGuessData::order_index=%d:choice=%d", order_index, pData->choice());
		}
		return true;
	}
	return false;
}
int CorpsEliminateBattle::GetGuessRightCount(const PB::eliminate_battle_guess_data_t& guess_data) const
{
	int battle_state = guess_data.state();
	auto iter = m_battle_guess_map.find(battle_state);
	if (iter == m_battle_guess_map.end())
	{
		return 0;
	}

	auto& guess_set = iter->second;

	if (guess_data.guess_data_size() > (int)guess_set.size())
	{
		return 0;
	}

	int right_count = 0;

	for (int i = 0; i < guess_data.guess_data_size(); ++i)
	{
		int order_index = guess_data.guess_data(i).order_index();
		auto it = guess_set.find(order_index);
		if (it == guess_set.end())
		{
			return 0;
		}

		auto pOrder = GetCorpsBattleOrder(order_index);
		if (!pOrder)
		{
			continue;
		}
		int guess = guess_data.guess_data(i).choice();
		if (guess > 0 && (guess == pOrder->battle_order.result() + 1 || pOrder->battle_order.result() == 2/*平局玩家不管猜哪个都认为是对的*/))
		{
			++ right_count;
		}
	}

	return right_count;
}
int CorpsEliminateBattle::GetGuessTotalRightCount(const ::google::protobuf::RepeatedPtrField< ::PB::eliminate_battle_guess_data_t >& guess_datas) const
{
	int right_count = 0;
	for (int i = 0; i < guess_datas.size(); i++)
	{
		auto& guess_data = guess_datas.Get(i);
		int battle_state = guess_data.state();

		auto iter = m_battle_guess_map.find(battle_state);
		if (iter == m_battle_guess_map.end())
		{
			continue;
		}
		auto& guess_set = iter->second;
		if (guess_data.guess_data_size() > (int)guess_set.size())
		{
			return 0;
		}

		for (int j = 0; j < guess_data.guess_data_size(); ++j)
		{
			int order_index = guess_data.guess_data(j).order_index();
			auto it = guess_set.find(order_index);
			if (it == guess_set.end())
			{
				return 0;
			}

			auto pOrder = GetCorpsBattleOrder(order_index);
			if (!pOrder)
			{
				continue;
			}
			int guess = guess_data.guess_data(j).choice();
			if (guess > 0 && (guess == pOrder->battle_order.result() + 1 ||
			                  pOrder->battle_order.result() == 2/*平局玩家不管猜哪个都认为是对的*/))
			{
				++ right_count;
			}
		}
	}

	return right_count;
}
void CorpsEliminateBattle::BroadcastSpeak(const std::vector<NameRuidPair>& battle_info_vec)
{
	LOG_TRACE("CorpsEliminateBattle::BroadcastSpeak:state=%d:info_size=%lu", (int)EliminateBattleManager::GetInstance().GetState(), battle_info_vec.size());
}
void CorpsEliminateBattle::ClearWarmUpOrderMap()
{
	if (!CenterManager::GetInstance().IsCenter())
	{
		return;
	}

	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}

	LOG_TRACE("CorpsEliminateBattle:ClearWarmUpOrderMap");
	if (!m_corps_battle_orders.empty())
	{
		m_corps_battle_orders.clear();
		m_order_dirty = true;
	}

	//把帮派对应的本周竞赛记录清了
	auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
	for (; it != eit; ++it)
	{
		CorpsBattleInfo& info = it->second;
		if (info.battle_info.order_index() > 0)
		{
			info.battle_info.set_last_order_index(0);
			info.battle_info.set_order_index(0);
			info.dirty = true;
			if (!m_info_dirty)
			{
				m_info_dirty = true;
			}
		}
	}

	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_WARMUP_END);

	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
}
void CorpsEliminateBattle::OnPlayerLogin(RoleInfo *pInfo)
{
	if (!pInfo->SNSReady() || !pInfo->friends.GetEliminateGroupID())
	{
		return;
	}
	CorpsBattleInfo *pBattleInfo = GetBattleInfoByCorpsID(pInfo->friends.GetEliminateGroupID());
	if (!pBattleInfo)
	{
		return;
	}
	if (pBattleInfo->battle_info.order_index() <= 0)
	{
		return;
	}
	auto it = m_corps_battle_orders.find(pBattleInfo->battle_info.order_index());
	if (it == m_corps_battle_orders.end())
	{
		return;
	}
	auto pBattleOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
	if (!pBattleOrder)
	{
		return;
	}
	CorpsBattleOrder& order = *pBattleOrder;
	LOG_TRACE("CorpsEliminateBattle::OnPlayerLogin::roleid=" PRINT64":eliminategroupid=%ld:battle_inst_tid=%d:battle_inst_id=%d",
	          pInfo->roleid, pInfo->friends.GetEliminateGroupID(), order.battle_inst_tid, order.battle_inst_id);
	if (order.battle_inst_tid != 0 && order.battle_inst_id != 0)
	{
		/*
		PB::npt_knockout_eliminate_battle_begin begin;
		pInfo->SendMessage2Client(begin);
		*/
		ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().NotifyKnockoutBegin(pInfo->friends.GetEliminateGroupID(), pInfo->roleid);
	}
}

void CorpsEliminateBattle::OnNewWeek(int week_begin)
{
	//情侣淘汰赛不需要每周重置，手动来控制
}

PB::corps_battle_config::CORPS_BATTLE_STATE CorpsEliminateBattle::GetNowState(int now_time) const
{
	if (EliminateBattleManager::GetInstance().GetState() < PB::EBS_CENTER_SCORE_BATTLE_END)
	{
		return PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR;
	}
	else if (EliminateBattleManager::GetInstance().GetState() >= PB::EBS_REWARD_BEGIN)
	{
		return PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN;
	}

	return (PB::corps_battle_config::CORPS_BATTLE_STATE)((EliminateBattleManager::GetInstance().GetState() + 1) % 2 + 2);
}

void CorpsEliminateBattle::JudgeState(int now_time)
{
	if (!EliminateBattleManager::GetInstance().IsInit())
	{
		LOG_TRACE("CorpsEliminateBattle::JudgeState::now_time=%d:EliminateBattleManagerNotInit", now_time);
		return;
	}
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	if (pCenterBattle->GetCenterZoneID() != g_zoneid)
	{
		return;
	}

	PB::corps_battle_config::CORPS_BATTLE_STATE state = GetNowState(now_time);
	int now_state = (int) EliminateBattleManager::GetInstance().GetState();

	if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
		{
			//进入匹配阶段了
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;
			LOG_TRACE("CorpsEliminateBattle::JudgeState:MatchBattle:CORPS_BATTLE_STATE_CLEAR->CORPS_BATTLE_STATE_MATCHED:state=%d", now_state);
			MatchBattle(now_time);
		}
	}
	else if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR)
		{
			//跳过了战斗开始阶段，那就清了吧
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			LOG_TRACE("CorpsEliminateBattle::EndBattle:EndBattle:CORPS_BATTLE_STATE_MATCHED->CORPS_BATTLE_STATE_CLEAR:state=%d", now_state);
			EndBattle(now_time);
		}
		else if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
		{
			//进入战斗开始阶段了
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			LOG_TRACE("CorpsEliminateBattle::JudgeState:BeginBattle:CORPS_BATTLE_STATE_MATCHED->CORPS_BATTLE_STATE_BATTLE_BEGIN:state=%d", now_state);
			BeginBattle(now_time);
		}
	}
	else if (m_config.state() == PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
	{
		if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR)
		{
			//进入战斗结束阶段
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_CLEAR);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			LOG_TRACE("CorpsEliminateBattle::JudgeState:EndBattle:CORPS_BATTLE_STATE_BATTLE_BEGIN->CORPS_BATTLE_STATE_CLEAR:state=%d", now_state);
			EndBattle(now_time);
		}
		else if (state == PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED)
		{
			//重新开始匹配
			m_config.set_state(PB::corps_battle_config::CORPS_BATTLE_STATE_MATCHED);
			m_config.set_timestamp(now_time);
			m_config_dirty = true;

			LOG_TRACE("CorpsEliminateBattle::JudgeState:MatchBattle:CORPS_BATTLE_STATE_BATTLE_BEGIN->CORPS_BATTLE_STATE_MATCHED:state=%d", now_state);
			MatchBattle(now_time);
		}
		else
		{
			//尝试创建战场
			BeginBattle2(now_time);
		}
	}
}

void CorpsEliminateBattle::MatchBattle(int now_time)
{
	LOG_TRACE("CorpsEliminateBattle::MatchBattle battle_type:%d now_time:%d", m_battle_type, now_time);

	MatchBattle2(now_time);
}
void CorpsEliminateBattle::__MatchBattleWarmUp(PB::ipt_eliminate_battle_notify& notify)
{
	size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
	size_t real_match_count = 0;
	bool has_single = false;
	std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> > candidate_guess;
	//把上次胜利的order里的玩家取出来重新匹配
	GetMatchCountInfo(m_order_battle_infos, real_match_count, has_single);

	LOG_TRACE("CorpsEliminateBattle::MatchBattleWarmUp:state=%d:match_count=%lu:info.size=%lu:real_match_count=%lu:has_single=%s",
	          (int)EliminateBattleManager::GetInstance().GetState(), match_count, m_order_battle_infos.size(), real_match_count, has_single ? "true" : "false");

	DoMatchBattle(m_order_battle_infos, notify, real_match_count, has_single, candidate_guess);
}
void CorpsEliminateBattle::__MatchBattleFirstStep(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess)
{
	size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
	size_t real_match_count = 0;
	bool has_single = false;
	first_step_tag tag;
	//将前八个对设置成种子选手
	int count = 0;
	auto it = m_order_battle_infos.begin();
	auto ie = m_order_battle_infos.end();
	for (; it != ie && count < SEED_PLAYER_COUNT; ++it, ++count)
	{
		auto *pItem = it->second;
		//auto& key = it->first;
		if (!pItem)
		{
			continue;
		}
		pItem->SetSeedPlayer();
		pItem->dirty = true;
		AddBattleInfoChanged(pItem, BATTLE_INFO_UPDATE);
	}

	m_info_dirty = true;

	//把上次胜利的order里的玩家取出来重新匹配
	GetMatchCountInfo(m_order_battle_infos, real_match_count, has_single, tag);

	//需要将热身赛的数据清空
	ClearWarmUpOrderMap();

	LOG_TRACE("CorpsEliminateBattle::MatchBattleFirstStep:state=%d:match_count=%lu:info.size=%lu:real_match_count=%lu:has_single=%s",
	          (int)EliminateBattleManager::GetInstance().GetState(), match_count, m_order_battle_infos.size(), real_match_count, has_single ? "true" : "false");

	DoMatchBattle(m_order_battle_infos, notify, real_match_count, has_single, candidate_guess, tag);
}
void CorpsEliminateBattle::__MatchBattleAddonStep1(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess)
{
	size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
	size_t real_match_count = 0;
	bool has_single = false;
	PB::ELIMINATE_BATTLE_STATE last_state = (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() - 1);

	//TODO:收集上一轮获胜的队伍
	ORDER_BATTLE_INFO_SET candidate_set;
	__GatherWinnerFromLastState(candidate_set);

	//TODO:从排行榜里手机前八的队伍
	int count = 0;
	auto it = m_order_battle_infos.begin();
	auto ie = m_order_battle_infos.end();
	for (; it != ie && count < SEED_PLAYER_COUNT; ++it, ++count)
	{
		auto *pItem = it->second;
		auto& key = it->first;
		if (!pItem)
		{
			continue;
		}

		pItem->ClearSeedPlayer();
		pItem->dirty = true;
		AddBattleInfoChanged(pItem, BATTLE_INFO_UPDATE);

		std::string info_str = pItem->GatherStrInfo();
		LOG_TRACE("CorpsEliminateBattle::GatherTop8:state=%d:id=" PRINT64":index=%d:value=%d:time=%d:battle_info=%s",
		          (int)last_state, pItem->battle_info.id(), key._index, key._value, key._time, info_str.c_str());
		candidate_set.insert(key);
	}
	m_info_dirty = true;

	GetMatchCountInfo(candidate_set, real_match_count, has_single);

	LOG_TRACE("CorpsEliminateBattle::MatchBattleAddonStep1:state=%d:match_count=%lu:info.size=%lu:real_match_count=%lu:has_single=%s",
	          (int)EliminateBattleManager::GetInstance().GetState(), match_count, candidate_set.size(), real_match_count, has_single ? "true" : "false");

	DoMatchBattle(candidate_set, notify, real_match_count, has_single, candidate_guess);
}
void CorpsEliminateBattle::__MatchBattleAddonStep2(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess)
{
	size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
	size_t real_match_count_winner = 0, real_match_count_loser = 0;
	bool has_single_winner = false, has_single_loser = false;

	// 收集上一轮获胜和失败的队伍
	ORDER_BATTLE_INFO_SET candidate_set_winner;
	__GatherWinnerFromLastState(candidate_set_winner);
	ORDER_BATTLE_INFO_SET candidate_set_loser;
	__GatherLoserFromLastState(candidate_set_loser);

	GetMatchCountInfo(candidate_set_winner, real_match_count_winner, has_single_winner);
	GetMatchCountInfo(candidate_set_loser, real_match_count_loser, has_single_loser);

	LOG_TRACE("CorpsEliminateBattle::MatchBattleAddonStep2:state=%d:match_count=%lu:info_winner.size=%lu:real_match_count_winner=%lu:has_single_winner=%s:info_loser.size=%lu:real_match_count_loser=%lu:has_single_loser=%s:",
	          (int)EliminateBattleManager::GetInstance().GetState(), match_count, candidate_set_winner.size(), real_match_count_winner, has_single_winner ? "true" : "false",
	          candidate_set_loser.size(), real_match_count_loser, has_single_loser ? "true" : "false");

	std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> > candidate_guess_winner, candidate_guess_loser;
	DoMatchBattle(candidate_set_winner, notify, real_match_count_winner, has_single_winner, candidate_guess_winner);
	DoMatchBattle(candidate_set_loser, notify, real_match_count_loser, has_single_loser, candidate_guess_loser);

	for (auto kv : candidate_guess_winner)
	{
		candidate_guess.insert(std::make_pair(kv.first, kv.second));
	}
	for (auto kv : candidate_guess_loser)
	{
		candidate_guess.insert(std::make_pair(kv.first + 10, kv.second));
	}
}
void CorpsEliminateBattle::__MatchBattleAddonStep3(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess)
{
	size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
	size_t real_match_count_one = 0, real_match_count_two = 0;
	bool has_single_one = false, has_single_two = false;
	PB::ELIMINATE_BATTLE_STATE last_state = (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() - 1);

	// 收集上一轮上半场失败的队伍以及下半场胜利的队伍
	ORDER_BATTLE_INFO_SET candidate_set_one;
	__GatherCandidateOne(candidate_set_one, last_state, false);
	ORDER_BATTLE_INFO_SET candidate_set_two;
	__GatherCandidateTwo(candidate_set_two, last_state, true);

	GetMatchCountInfo(candidate_set_one, real_match_count_one, has_single_one);
	GetMatchCountInfo(candidate_set_two, real_match_count_two, has_single_two);

	LOG_TRACE("CorpsEliminateBattle::MatchBattleAddonStep3:state=%d:match_count=%lu:info_one.size=%lu:real_match_count_one=%lu:has_single_one=%s:info_two.size=%lu:real_match_count_two=%lu:has_single_two=%s:",
	          (int)EliminateBattleManager::GetInstance().GetState(), match_count, candidate_set_one.size(), real_match_count_one, has_single_one ? "true" : "false",
	          candidate_set_two.size(), real_match_count_two, has_single_two ? "true" : "false");

	std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> > candidate_guess_one, candidate_guess_two;
	DoMatchBattle(candidate_set_one, notify, real_match_count_one, has_single_one, candidate_guess_one);
	DoMatchBattle(candidate_set_two, notify, real_match_count_two, has_single_two, candidate_guess_two);

	for (auto kv : candidate_guess_one)
	{
		candidate_guess.insert(std::make_pair(kv.first, kv.second));
	}
	for (auto kv : candidate_guess_two)
	{
		candidate_guess.insert(std::make_pair(kv.first + 10, kv.second));
	}
}
void CorpsEliminateBattle::__MatchBattleAddonStep4(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess)
{
	size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
	size_t real_match_count = 0;
	bool has_single = false;
	PB::ELIMINATE_BATTLE_STATE last_state = (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() - 1);
	PB::ELIMINATE_BATTLE_STATE second_state = (PB::ELIMINATE_BATTLE_STATE)(last_state - 2);

	// 收集第二轮和第三轮选出来的
	ORDER_BATTLE_INFO_SET candidate_set;
	//第二场上半场胜的4个队伍直接晋级
	__GatherCandidateOne(candidate_set, second_state, true);
	//第三场上半场胜的和下半场胜的晋级
	__GatherCandidateOne(candidate_set, last_state, true);
	__GatherCandidateTwo(candidate_set, last_state, true);

	GetMatchCountInfo(candidate_set, real_match_count, has_single);

	LOG_TRACE("CorpsEliminateBattle::MatchBattleAddonStep4:state=%d:match_count=%lu:info.size=%lu:real_match_count=%lu:has_single=%s",
	          (int)EliminateBattleManager::GetInstance().GetState(), match_count, candidate_set.size(), real_match_count, has_single ? "true" : "false");

	DoMatchBattle(candidate_set, notify, real_match_count, has_single, candidate_guess);
}
void CorpsEliminateBattle::__MatchBattleFollowStep(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess)
{
	//TODO:收集所有上一轮获胜的队伍
	ORDER_BATTLE_INFO_SET candidate_set;
	//PB::ELIMINATE_BATTLE_STATE last_state = (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() - 1);

	__GatherWinnerFromLastState(candidate_set);

	size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
	size_t real_match_count = 0;
	bool has_single = false;

	//把上次胜利的order里的玩家取出来重新匹配
	GetMatchCountInfo(candidate_set, real_match_count, has_single);

	LOG_TRACE("CorpsEliminateBattle::MatchBattleFollowStep:state=%d:match_count=%lu:info.size=%lu:real_match_count=%lu:has_single=%s",
	          (int)EliminateBattleManager::GetInstance().GetState(), match_count, candidate_set.size(), real_match_count, has_single ? "true" : "false");

	DoMatchBattle(candidate_set, notify, real_match_count, has_single, candidate_guess);
}
void CorpsEliminateBattle::__GatherCandidateOne(ORDER_BATTLE_INFO_SET& candidate_set, PB::ELIMINATE_BATTLE_STATE state, bool winner)
{
	CORPS_BATTLE_ORDER_MAP tmp_orders;
	for (auto it = m_corps_battle_orders.begin(); it != m_corps_battle_orders.end(); ++it)
	{
		const auto pOrder = it->second;
		if (!pOrder || pOrder->battle_order.battle_stage() != state)
		{
			continue;
		}

		tmp_orders[it->first] = it->second;
	}

	size_t candidate_one_count = tmp_orders.size() / 2;
	if (tmp_orders.size() % 2 != 0)
	{
		candidate_one_count ++;
	}

	for (auto kv : tmp_orders)
	{
		if (candidate_one_count-- == 0)
		{
			break;
		}

		const auto pOrder = kv.second;
		int index = 0;
		if (pOrder->battle_order.result() == 0)
		{
			if (winner)
			{
				index = pOrder->battle_order.corps_battle_index_2();
			}
			else
			{
				index = pOrder->battle_order.corps_battle_index_1();
			}
		}
		else if (pOrder->battle_order.result() == 1)
		{
			if (winner)
			{
				index = pOrder->battle_order.corps_battle_index_1();
			}
			else
			{
				index = pOrder->battle_order.corps_battle_index_2();
			}
		}
		else
		{
			continue;
		}
		if (index <= 0)
		{
			continue;
		}
		auto& battle_info = m_corps_battle_infos[index];
		std::string info_str = battle_info.GatherStrInfo();

		CBI_Key key(GetOrederedIndex(&battle_info), GetOrederedValue(&battle_info), GetOrederedTime(&battle_info));
		LOG_TRACE("CorpsEliminateBattle::GatherCandidateOne:state=%d:id=" PRINT64":index=%d:value=%d:time=%d:winner=%d:battle_info=%s",
		          (int)state, battle_info.battle_info.id(), key._index, key._value, key._time, (int)winner, info_str.c_str());
		candidate_set.insert(key);
	}
}
void CorpsEliminateBattle::__GatherCandidateTwo(ORDER_BATTLE_INFO_SET& candidate_set, PB::ELIMINATE_BATTLE_STATE state, bool winner)
{
	CORPS_BATTLE_ORDER_MAP tmp_orders;
	for (auto it = m_corps_battle_orders.begin(); it != m_corps_battle_orders.end(); ++it)
	{
		const auto pOrder = it->second;
		if (!pOrder || pOrder->battle_order.battle_stage() != state)
		{
			continue;
		}

		tmp_orders[it->first] = it->second;
	}

	size_t candidate_one_count = tmp_orders.size() / 2;
	if (tmp_orders.size() % 2 != 0)
	{
		candidate_one_count ++;
	}

	for (auto kv : tmp_orders)
	{
		if (candidate_one_count > 0)
		{
			candidate_one_count --;
			continue;
		}

		const auto pOrder = kv.second;
		int index = 0;
		if (pOrder->battle_order.result() == 0)
		{
			if (winner)
			{
				index = pOrder->battle_order.corps_battle_index_2();
			}
			else
			{
				index = pOrder->battle_order.corps_battle_index_1();
			}
		}
		else if (pOrder->battle_order.result() == 1)
		{
			if (winner)
			{
				index = pOrder->battle_order.corps_battle_index_1();
			}
			else
			{
				index = pOrder->battle_order.corps_battle_index_2();
			}
		}
		else
		{
			continue;
		}
		if (index <= 0)
		{
			continue;
		}
		auto& battle_info = m_corps_battle_infos[index];
		std::string info_str = battle_info.GatherStrInfo();

		CBI_Key key(GetOrederedIndex(&battle_info), GetOrederedValue(&battle_info), GetOrederedTime(&battle_info));
		LOG_TRACE("CorpsEliminateBattle::GatherCandidateTwo:state=%d:id=" PRINT64":index=%d:value=%d:time=%d:winner=%d:battle_info=%s",
		          (int)state, battle_info.battle_info.id(), key._index, key._value, key._time, (int)winner, info_str.c_str());
		candidate_set.insert(key);
	}
}
void CorpsEliminateBattle::__GatherWinnerFromLastState(ORDER_BATTLE_INFO_SET& candidate_set)
{
	PB::ELIMINATE_BATTLE_STATE last_state = (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() - 1);
	for (auto it = m_corps_battle_orders.begin(); it != m_corps_battle_orders.end(); ++it)
	{
		const auto pOrder = it->second;
		if (!pOrder || pOrder->battle_order.battle_stage() != last_state)
		{
			continue;
		}

		int winner_index = 0;
		if (pOrder->battle_order.result() == 0)
		{
			winner_index = pOrder->battle_order.corps_battle_index_2();
		}
		else if (pOrder->battle_order.result() == 1)
		{
			winner_index = pOrder->battle_order.corps_battle_index_1();
		}
		else
		{
			continue;
		}
		if (winner_index <= 0)
		{
			continue;
		}
		auto& battle_info = m_corps_battle_infos[winner_index];
		std::string info_str = battle_info.GatherStrInfo();

		CBI_Key key(GetOrederedIndex(&battle_info), GetOrederedValue(&battle_info), GetOrederedTime(&battle_info));
		LOG_TRACE("CorpsEliminateBattle::GatherWinnerFromLastState:state=%d:id=" PRINT64":index=%d:value=%d:time=%d:battle_info=%s",
		          (int)last_state, battle_info.battle_info.id(), key._index, key._value, key._time, info_str.c_str());
		candidate_set.insert(key);
	}
}
void CorpsEliminateBattle::__GatherLoserFromLastState(ORDER_BATTLE_INFO_SET& candidate_set)
{
	PB::ELIMINATE_BATTLE_STATE last_state = (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() - 1);
	for (auto it = m_corps_battle_orders.begin(); it != m_corps_battle_orders.end(); ++it)
	{
		const auto pOrder = it->second;
		if (!pOrder || pOrder->battle_order.battle_stage() != last_state)
		{
			continue;
		}

		int loser_index = 0;
		if (pOrder->battle_order.result() == 0)
		{
			loser_index = pOrder->battle_order.corps_battle_index_1();
		}
		else if (pOrder->battle_order.result() == 1)
		{
			loser_index = pOrder->battle_order.corps_battle_index_2();
		}
		else
		{
			continue;
		}
		if (loser_index <= 0)
		{
			continue;
		}
		auto& battle_info = m_corps_battle_infos[loser_index];
		std::string info_str = battle_info.GatherStrInfo();

		CBI_Key key(GetOrederedIndex(&battle_info), GetOrederedValue(&battle_info), GetOrederedTime(&battle_info));
		LOG_TRACE("CorpsEliminateBattle::GatherLoserFromLastState:state=%d:id=" PRINT64":index=%d:value=%d:time=%d:battle_info=%s",
		          (int)last_state, battle_info.battle_info.id(), key._index, key._value, key._time, info_str.c_str());
		candidate_set.insert(key);
	}
}
void CorpsEliminateBattle::MatchBattle2(int now_time)
{
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_MATCH);
	std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> > candidate_guess;

	//把帮派对应的本周竞赛记录清了
	auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
	for (; it != eit; ++it)
	{
		CorpsBattleInfo& info = it->second;
		if (info.battle_info.order_index() > 0)
		{
			if (info.battle_info.has_order_index())
			{
				info.battle_info.set_last_order_index(info.battle_info.order_index());
			}
			info.battle_info.set_order_index(0);
			info.dirty = true;
			if (!m_info_dirty)
			{
				m_info_dirty = true;
			}
		}
	}

	//TODO gxw 根据排行榜生成对战信息，并把对应的corps_battle_info和corps_battle_order放入notify中
	//在每一个阶段结束的时候
	if (PB::EBS_CENTER_SCORE_BATTLE_END == EliminateBattleManager::GetInstance().GetState())
	{
		//淘汰赛热身赛
		__MatchBattleWarmUp(notify);
	}
	else if (PB::EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_END == EliminateBattleManager::GetInstance().GetState())
	{
		//第一次正式淘汰赛开始，排名前八的队伍不参加
		__MatchBattleFirstStep(notify, candidate_guess);
	}
	else if (PB::EBS_CENTER_KNOCKOUT_BATTLE_16_END == EliminateBattleManager::GetInstance().GetState())
	{
		//淘汰赛附加赛第一轮，排名前八的玩家和16进8打上来的玩家在打一轮
		__MatchBattleAddonStep1(notify, candidate_guess);
	}
	else if (PB::EBS_CENTER_KNOCKOUT_BATTLE_16_AGAIN_1_END == EliminateBattleManager::GetInstance().GetState())
	{
		//淘汰赛附加赛第二轮，胜利的8名和失败的8名再各打4场
		__MatchBattleAddonStep2(notify, candidate_guess);
	}
	else if (PB::EBS_CENTER_KNOCKOUT_BATTLE_16_AGAIN_2_END == EliminateBattleManager::GetInstance().GetState())
	{
		//淘汰赛附加赛第三轮，失败的4名和胜利的4名再各打2场
		__MatchBattleAddonStep3(notify, candidate_guess);
	}
	else if (PB::EBS_CENTER_KNOCKOUT_BATTLE_16_AGAIN_3_END == EliminateBattleManager::GetInstance().GetState())
	{
		//从前边3轮的结果里选出8名重新匹配出8进4
		__MatchBattleAddonStep4(notify, candidate_guess);
	}
	else
	{
		__MatchBattleFollowStep(notify, candidate_guess);
	}
	if (candidate_guess.size() != 0)
	{
		int state = (int)EliminateBattleManager::GetInstance().GetState() + 1;
		auto i = m_battle_guess_map.find(state);
		if (i != m_battle_guess_map.end())
		{
			LOG_TRACE("CorpsEliminateBattle::MatchBattle:Already Has Guess Date:state=%d", state);
			m_battle_guess_map[state].clear();
		}

		auto *pCandidateGuess = notify.add_candidate_guess();
		pCandidateGuess->set_battle_state((PB::ELIMINATE_BATTLE_STATE)state);
		for (auto iter = candidate_guess.begin(); iter != candidate_guess.end(); ++iter)
		{
			pCandidateGuess->add_candidate_guess(iter->second);
			m_battle_guess_map[state].insert(iter->second);
			LOG_TRACE("CorpsEliminateBattle::MatchBattle2:CandidateGuess:size=%d:order_index=%d", (int)candidate_guess.size(), iter->second);
		}
		m_guess_dirty = true;
	}
	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);

	m_info_dirty = true;
	m_order_dirty = true;
}
void CorpsEliminateBattle::IDIPSyncBattleMatchResult()
{
	PB::ipt_eliminate_battle_notify notify;
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_MATCH);

	LOG_TRACE("CorpsEliminateBattle::IDIPSyncBattleMatchResult:order_size=%zu, info_size=%zu", m_corps_battle_orders.size(), m_corps_battle_infos.size());
	for (auto it = m_corps_battle_orders.begin(); it != m_corps_battle_orders.end(); ++it)
	{
		if (!it->second)
		{
			continue;
		}
		auto pOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
		if (!pOrder)
		{
			continue;
		}

		PB::corps_battle_order *ebo = notify.add_eliminate_battle_orders();
		ebo->CopyFrom(pOrder->battle_order);

		bool has_one = false;
		auto it_one = m_corps_battle_infos.find(pOrder->CorpsOneIndex());
		if (it_one != m_corps_battle_infos.end())
		{
			PB::corps_battle_info *ebi = notify.add_eliminate_battle_infos();
			ebi->CopyFrom(it_one->second.battle_info);
			has_one = true;
		}

		bool has_two = false;
		auto it_two = m_corps_battle_infos.find(pOrder->CorpsTwoIndex());
		if (it_two != m_corps_battle_infos.end())
		{
			PB::corps_battle_info *ebi = notify.add_eliminate_battle_infos();
			ebi->CopyFrom(it_two->second.battle_info);
			has_two = true;
		}

		LOG_TRACE("CorpsEliminateBattle::IDIPSyncBattleMatchResult:order_index=%d, battle_time=%d, result=%d, has_one=%d, index1=%d, id1=%ld, has_two=%d, index2=%d, id2=%ld", pOrder->Index(), pOrder->BattleTime(), pOrder->Result(), has_one, pOrder->CorpsOneIndex(), pOrder->CorpsOneID(), has_two, pOrder->CorpsTwoIndex(), pOrder->CorpsTwoID());
	}

	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
}
int CorpsEliminateBattle::__CreateBattleOrder(CorpsBattleInfo *info_one, CorpsBattleInfo *info_two, PB::ipt_eliminate_battle_notify& notify, int init_battle_time)
{
	if (info_one && info_two)
	{
		return __CreateBattleOrderBoth(info_one, info_two, notify, init_battle_time);
	}
	else if (info_one)
	{
		return __CreateBattleOrderSingle(info_one,  notify, init_battle_time);
	}
	else if (info_two)
	{
		return __CreateBattleOrderSingle(info_two, notify, init_battle_time);
	}
	return 0;
}
CorpsBattleOrderBase *CorpsEliminateBattle::GetBattleOrderByGroupID(ruid_t corps_id)
{
	CorpsBattleInfo *pBattle = GetBattleInfoByCorpsID(corps_id);
	if (!pBattle)
	{
		return NULL;
	}

	int order_index = pBattle->battle_info.order_index();
	auto it = m_corps_battle_orders.find(order_index);
	if (it == m_corps_battle_orders.end())
	{
		return NULL;
	}

	return it->second.get();

}
int CorpsEliminateBattle::__CreateBattleOrderBoth(CorpsBattleInfo *info_one, CorpsBattleInfo *info_two, PB::ipt_eliminate_battle_notify& notify, int init_battle_time)
{
	if (!info_one || !info_two)
	{
		return 0;
	}

	int order_index = (int)m_corps_battle_orders.size() + 1;
	auto porder = CreateBattleOrder();
	auto& order = *porder;
	order.dirty = true;
	order.battle_order.set_index(order_index);
	order.battle_order.set_battle_time(init_battle_time);
	order.battle_order.set_corps_battle_index_1(info_one->battle_info.index());
	order.battle_order.set_corps_battle_id_1(info_one->battle_info.id());
	order.battle_order.set_corps_battle_index_2(info_two->battle_info.index());
	order.battle_order.set_corps_battle_id_2(info_two->battle_info.id());
	order.battle_order.set_battle_stage((PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() + 1));
	order.battle_order.set_result(-1);
	m_corps_battle_orders[porder->battle_order.index()] = porder;

	PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
	pOrder->CopyFrom(order.battle_order);

	info_one->battle_info.set_order_index(order_index);
	info_two->battle_info.set_order_index(order_index);

	PB::corps_battle_info *pInfo1 = notify.add_eliminate_battle_infos();
	pInfo1->CopyFrom(info_one->battle_info);

	PB::corps_battle_info *pInfo2 = notify.add_eliminate_battle_infos();
	pInfo2->CopyFrom(info_two->battle_info);

	info_one->dirty = true;
	info_two->dirty = true;

	m_info_dirty = true;
	m_order_dirty = true;

	std::string info_one_str = info_one->GatherStrInfo();
	std::string info_two_str = info_two->GatherStrInfo();
	GLog::formatlog("corps_eliminate_create_corder_both", "battle_order_index=%d:battle_time=%d:battle_result=%d:battle_info_one=%s:battle_info_two=%s",
	                order.battle_order.index(), order.battle_order.battle_time(), order.battle_order.result(),
	                info_one_str.c_str(), info_two_str.c_str());

	LOG_TRACE("CorpsEliminateBattle::MatchBattle2:CreateBattleOrderBoth:order_index=%d:battle_one_index=%d:battle_one_id=" PRINT64":battle_two_index=%d:battle_two_index=" PRINT64":battle_time=%d",
	          order_index, info_one->battle_info.index(), info_one->battle_info.id(), info_two->battle_info.index(),
	          info_two->battle_info.id(), order.battle_order.battle_time());
	return order_index;
}
int CorpsEliminateBattle::__CreateBattleOrderSingle(CorpsBattleInfo *info, PB::ipt_eliminate_battle_notify& notify, int init_battle_time)
{
	if (!info)
	{
		return 0;
	}

	int order_index = (int)m_corps_battle_orders.size() + 1;
	auto porder = CreateBattleOrder();
	auto& order = *porder;
	order.dirty = true;
	order.battle_order.set_index(order_index);
	order.battle_order.set_battle_time(init_battle_time);
	order.battle_order.set_corps_battle_index_1(info->battle_info.index());
	order.battle_order.set_corps_battle_id_1(info->battle_info.id());
	order.battle_order.set_corps_battle_index_2(-1);
	order.battle_order.set_corps_battle_id_2(0);
	order.battle_order.set_battle_stage((PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() + 1));
	order.battle_order.set_result(1);
	m_corps_battle_orders[porder->battle_order.index()] = porder;

	PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
	pOrder->CopyFrom(order.battle_order);

	info->battle_info.set_order_index(order_index);

	PB::corps_battle_info *pInfo = notify.add_eliminate_battle_infos();
	pInfo->CopyFrom(info->battle_info);

	info->dirty = true;

	m_info_dirty = true;
	m_order_dirty = true;

	std::string info_str = info->GatherStrInfo();
	GLog::formatlog("corps_eliminate_create_corder_single", "battle_order_index=%d:battle_time=%d:battle_result=%d:battle_info=%s",
	                order.battle_order.index(), order.battle_order.battle_time(), order.battle_order.result(),
	                info_str.c_str());

	LOG_TRACE("CorpsEliminateBattle::MatchBattle2:CreateBattleOrderSingle:order_index=%d:battle_one_index=%d:battle_one_id=" PRINT64":battle_time=%d",
	          order_index, info->battle_info.index(), info->battle_info.id(), order.battle_order.battle_time());
	return order_index;
}
void CorpsEliminateBattle::BeginBattle2(int now_time)
{
	if (m_create_battle_ok || m_corps_battle_orders.empty())
	{
		return;
	}

	bool all_begin = true;
	int count = 0;
	auto it = m_corps_battle_orders.begin(), eit = m_corps_battle_orders.end();
	for (; it != eit; ++it)
	{
		auto porder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
		if (!porder)
		{
			continue;
		}
		CorpsBattleOrder& order = *porder;
		if (!order.battle_inst_id && order.battle_order.result() < 0)
		{
			if (now_time >= order.battle_order.battle_time())
			{
				//找到帮派1的信息
				auto info1_it = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
				if (info1_it == m_corps_battle_infos.end())
				{
					Log::log(LOG_ERR, "CorpsEliminateBattle::BeginBattle cant find corps_battle_index_1 = %d, order_index=%d",
					         order.battle_order.corps_battle_index_1(), order.battle_order.index());

					//对阵表出问题了，打个补丁
					auto info2_it_tmp = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
					if (info2_it_tmp != m_corps_battle_infos.end())
					{
						order.battle_order.set_result(0);
						order.dirty = true;
						m_order_dirty = true;

						//通知源服
						PB::ipt_eliminate_battle_notify notify;
						notify.set_zoneid(g_zoneid);
						notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBTE_BATTLE_RESULT);

						PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
						pOrder->CopyFrom(order.battle_order);

						int state = ((EliminateBattleManager::GetInstance().GetState() - EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_BEGIN) / 2) * 3 + 1;
						CorpsBattleInfo& info2_tmp = info2_it_tmp->second;
						int old_battle_state = info2_tmp.BattleState();
						info2_tmp.SetBattleState(state + 2);
						info2_tmp.dirty = true;
						m_info_dirty = true;
						auto pinfo2 = notify.add_eliminate_battle_infos();
						pinfo2->CopyFrom(info2_tmp.battle_info);
						LOG_TRACE("BATTLE_STATE_DEBUG::BeginBattle_Refix1:corps_id=%ld:index=%d:old_battle_state=%d:battle_state=%d:cur_state=%d",
						          info2_tmp.battle_info.id(), info2_tmp.battle_info.index(), old_battle_state, info2_tmp.BattleState(), (int)EliminateBattleManager::GetInstance().GetState());

						CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
						LOG_TRACE("CorpsEliminateBattle::BeginBattle cant find corps_battle_index_1 = %d, order_index=%d, win_idx=%d, win_id=%ld", order.battle_order.corps_battle_index_1(), order.battle_order.index(), info2_tmp.battle_info.index(), info2_tmp.battle_info.id());
					}
					continue;
				}
				CorpsBattleInfo& info1 = info1_it->second;
				if (info1.battle_info.order_index() != order.battle_order.index())
				{
					continue;    //不是这次的比赛，是本周的上一次的
				}

				//找到帮派2的信息
				auto info2_it = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
				if (info2_it == m_corps_battle_infos.end())
				{
					Log::log(LOG_ERR, "CorpsEliminateBattle::BeginBattle cant find corps_battle_index_2 = %d, order_index=%d",
					         order.battle_order.corps_battle_index_2(), order.battle_order.index());

					//对阵表出问题了，打个补丁
					{
						order.battle_order.set_result(1);
						order.dirty = true;
						m_order_dirty = true;

						//通知源服
						PB::ipt_eliminate_battle_notify notify;
						notify.set_zoneid(g_zoneid);
						notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBTE_BATTLE_RESULT);

						PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
						pOrder->CopyFrom(order.battle_order);

						int state = ((EliminateBattleManager::GetInstance().GetState() - EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_BEGIN) / 2) * 3 + 1;
						int old_battle_state = info1.BattleState();
						info1.SetBattleState(state + 2);
						info1.dirty = true;
						m_info_dirty = true;
						auto pinfo1 = notify.add_eliminate_battle_infos();
						pinfo1->CopyFrom(info1.battle_info);
						LOG_TRACE("BATTLE_STATE_DEBUG::BeginBattle_Refix2:corps_id=%ld:index=%d:old_battle_state=%d:battle_state=%d:cur_state=%d",
						          info1.battle_info.id(), info1.battle_info.index(), old_battle_state, info1.BattleState(), (int)EliminateBattleManager::GetInstance().GetState());

						CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
						LOG_TRACE("CorpsEliminateBattle::BeginBattle cant find corps_battle_index_2 = %d, order_index=%d, win_idx=%d, win_id=%ld", order.battle_order.corps_battle_index_2(), order.battle_order.index(), info1.battle_info.index(), info1.battle_info.id());
					}
					continue;
				}
				CorpsBattleInfo& info2 = info2_it->second;
				if (info2.battle_info.order_index() != order.battle_order.index())
				{
					continue;    //不是这次的比赛，是本周的上一次的
				}

				//开始创建战场
				int tid_index = rand() % m_battle_tids.size();
				int battle_tid = m_battle_tids[tid_index];
				std::vector<ruid_t> creators;
				std::stringstream ss;
				ss << "[";
				for (int i = 0; i < info1.battle_info.eliminate_battle_players_size(); ++ i)
				{
					const PB::corps_eliminate_battle_player& player = info1.battle_info.eliminate_battle_players(i);
					creators.push_back(player.role().id());
					ss << player.role().id() << ",";
				}
				for (int i = 0; i < info2.battle_info.eliminate_battle_players_size(); ++ i)
				{
					const PB::corps_eliminate_battle_player& player = info2.battle_info.eliminate_battle_players(i);
					creators.push_back(player.role().id());
					ss << player.role().id() << ",";
				}
				ss << "]";
				std::string tmp;
				int inst_id = CreateBattle(battle_tid, order.Index(), info1.ID(), tmp, info2.ID(), tmp, 0/*player_npc*/, 0/*p1*/, 0, m_battle_type, 0, 0);
				if (inst_id)
				{
					order.battle_inst_tid = battle_tid;
					order.battle_inst_id = inst_id;
					LOG_TRACE("CorpsEliminateBattle::BeginBattle2:order_index=%d:group_index_1=%d:group_id_1=" PRINT64":group_index_2=%d:group_id_2=" PRINT64":battle_time=%d:now_time=%d:state=%d:players=%s", order.battle_order.index(), GetOrederedIndex(&info1), GetOrederedID(&info1),
					          GetOrederedIndex(&info2), GetOrederedID(&info2), order.battle_order.battle_time(), now_time,
					          (int)EliminateBattleManager::GetInstance().GetState(), ss.str().c_str());
					if (++ count >= CORPS_BATTLE_CREATE_PER_TICK)
					{
						all_begin = false;
						break;
					}
				}
				else
				{
					all_begin = false;
				}
			}
			else
			{
				all_begin = false;
			}
		}
	}

	m_create_battle_ok = all_begin;
}

void CorpsEliminateBattle::EndBattle(int now_time)
{
	LOG_TRACE("CorpsEliminateBattle::EndBattle");

	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}

	CorpsBattleEntry::EndBattle(now_time);

	if (!m_corps_battle_orders.empty())
	{
		m_corps_battle_orders.clear();
		m_order_dirty = true;
	}
	if (!m_battle_guess_map.empty())
	{
		m_battle_guess_map.clear();
		m_guess_dirty = true;
	}
	if (m_order_changed.size())
	{
		m_order_changed.clear();
	}

	//把帮派对应的本周竞赛记录清了
	auto it = m_corps_battle_infos.begin(), eit = m_corps_battle_infos.end();
	for (; it != eit; ++it)
	{
		CorpsBattleInfo& info = it->second;
		if (info.battle_info.order_index() > 0)
		{
			if (info.battle_info.has_order_index())
			{
				info.battle_info.set_last_order_index(info.battle_info.order_index());
			}
			info.battle_info.set_order_index(0);
			info.dirty = true;
			if (!m_info_dirty)
			{
				m_info_dirty = true;
			}
		}
		if (info.battle_info.eliminate_battle_score() != ELIMINATE_GROUP_SCORE_BATTLE_INIT_SCORE || info.battle_info.total_count() != 0 ||
		        info.battle_info.win_count() != 0 || info.battle_info.consecutive_win_count() != 0)
		{
			info.battle_info.set_eliminate_battle_score(ELIMINATE_GROUP_SCORE_BATTLE_INIT_SCORE);
			info.battle_info.set_total_count(0);
			info.battle_info.set_win_count(0);
			info.battle_info.set_consecutive_win_count(0);
			info.dirty = true;
			if (!m_info_dirty)
			{
				m_info_dirty = true;
			}
		}
	}

	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_BATTLE_END);

	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
}

void CorpsEliminateBattle::OnCreateBattle(int battle_id, int battle_index, int param, int ret)
{
	LOG_TRACE("DS::CorpsEliminateBattle::OnCreateBattle m_battle_type:%d battle_id:%d battle_index:%d ret:%d",
	          m_battle_type, battle_id, battle_index, ret);

	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}

	auto oit = m_corps_battle_orders.find(battle_index);
	if (oit == m_corps_battle_orders.end() || !(oit->second))
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnCreateBattle cant find battle_id:%d battle_index=%d m_battle_type=%d ret=%d",
		         battle_id, battle_index, m_battle_type, ret);
		return;
	}
	auto porder = std::dynamic_pointer_cast<CorpsBattleOrder>(oit->second);
	CorpsBattleOrder& order = *porder;

	auto iit1 = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
	if (iit1 == m_corps_battle_infos.end())
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnCreateBattle cant find corps_index1=%d battle_id=%d battle_index=%d m_battle_type=%d ret=%d",
		         order.battle_order.corps_battle_index_1(), battle_id, battle_index, m_battle_type, ret);
		return;
	}
	CorpsBattleInfo& info1 = iit1->second;

	auto iit2 = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
	if (iit2 == m_corps_battle_infos.end())
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnCreateBattle cant find corps_index2=%d battle_id=%d battle_index=%d m_battle_type=%d ret=%d",
		         order.battle_order.corps_battle_index_2(), battle_id, battle_index, m_battle_type, ret);
		return;
	}
	CorpsBattleInfo& info2 = iit2->second;

	if (0 == ret)
	{
		PB::ipt_eliminate_battle_notify notify;
		notify.set_zoneid(pCenterBattle->GetCenterZoneID());
		notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_INST_CREATE);

		PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
		pOrder->CopyFrom(order.battle_order);
		notify.set_battle_inst_tid(order.battle_inst_tid);
		notify.set_battle_inst_id(order.battle_inst_id);

		auto pinfo1 = notify.add_eliminate_battle_infos();
		pinfo1->CopyFrom(info1.battle_info);
		auto pinfo2 = notify.add_eliminate_battle_infos();
		pinfo2->CopyFrom(info2.battle_info);

		CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
	}
	else
	{
		order.battle_inst_tid = 0;
		order.battle_inst_id = 0;
	}
}

void CorpsEliminateBattle::OnBattleClose(int battle_id, int battle_index, int param)
{
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	auto oit = m_corps_battle_orders.find(battle_index);
	if (oit == m_corps_battle_orders.end() || !(oit->second))
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnBattleClose cant find battle_id=%d battle_index=%d m_battle_type=%d",
		         battle_id, battle_index, m_battle_type);
		return;
	}
	auto porder = std::dynamic_pointer_cast<CorpsBattleOrder>(oit->second);
	CorpsBattleOrder& order = *porder;
	order.battle_inst_tid = 0;
	order.battle_inst_id = 0;

	if (order.battle_order.result() < 0)
	{
		//在战场关闭的时候没有收到战斗结果，那就按平局处理
		auto iit1 = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
		if (iit1 == m_corps_battle_infos.end())
		{
			Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnBattleClose cant find corps_index1=%d battle_id=%d battle_index=%d m_battle_type=%d",
			         order.battle_order.corps_battle_index_1(), battle_id, battle_index, m_battle_type);
			return;
		}
		CorpsBattleInfo& info1 = iit1->second;

		auto iit2 = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
		if (iit2 == m_corps_battle_infos.end())
		{
			Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnBattleClose cant find corps_index2=%d battle_id=%d battle_index=%d m_battle_type=%d",
			         order.battle_order.corps_battle_index_2(), battle_id, battle_index, m_battle_type);
			return;
		}
		CorpsBattleInfo& info2 = iit2->second;

		//记录战斗结果
		int state = ((EliminateBattleManager::GetInstance().GetState() - EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_BEGIN) / 2) * 3 + 1;

		// 双负
		info1.SetBattleState(state + 1);
		info2.SetBattleState(state + 1);

		info1.dirty = true;
		info2.dirty = true;
		m_info_dirty = true;

		order.battle_order.set_result(2);
		order.dirty = true;
		m_order_dirty = true;

		//通知源服
		PB::ipt_eliminate_battle_notify notify;
		notify.set_zoneid(pCenterBattle->GetCenterZoneID());
		notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBTE_BATTLE_RESULT);

		PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
		pOrder->CopyFrom(order.battle_order);

		auto pinfo1 = notify.add_eliminate_battle_infos();
		pinfo1->CopyFrom(info1.battle_info);
		auto pinfo2 = notify.add_eliminate_battle_infos();
		pinfo2->CopyFrom(info2.battle_info);

		CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
		LOG_TRACE("DS::CorpsEliminateBattle::OnBattleClose corps_index2=%d battle_id=%d battle_index=%d m_battle_type=%d",
		          order.battle_order.corps_battle_index_2(), battle_id, battle_index, m_battle_type);

		GLog::formatlog("corps_eliminate_battle_result", "battle_type=%d:order_index=%d:corps_index1=%d:corps_id1=%ld:corps_index2=%d:corps_id2=%ld:result=%d",
		                m_battle_type, battle_index, order.battle_order.corps_battle_index_1(), info1.battle_info.id(),
		                order.battle_order.corps_battle_index_2(), info2.battle_info.id(), 2);
	}
}

void CorpsEliminateBattle::OnSendBattleResult(int64_t target_id, int battle_id, int battle_index, int inst_param, int creator_win, int64_t param, int64_t param2, const std::map<int64_t, std::tuple<int64_t, int>>& param_map/* = std::map<int64_t, std::tuple<int64_t, int>>()*/)
{
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	auto oit = m_corps_battle_orders.find(battle_index);
	if (oit == m_corps_battle_orders.end() || !(oit->second))
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnSendBattleResult cant find battle_id=%d battle_index=%d m_battle_type=%d creator_win=%d",
		         battle_id, battle_index, m_battle_type, creator_win);
		return;
	}
	auto porder = std::dynamic_pointer_cast<CorpsBattleOrder>(oit->second);
	CorpsBattleOrder& order = *porder;
	if (!order.battle_inst_tid || !order.battle_inst_id || order.battle_order.result() >= 0)
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnSendBattleResult battle not begin. battle_id=%d battle_index=%d m_battle_type=%d creator_win=%d",
		         battle_id, battle_index, m_battle_type, creator_win);
		return;
	}

	auto iit1 = m_corps_battle_infos.find(order.battle_order.corps_battle_index_1());
	if (iit1 == m_corps_battle_infos.end())
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnSendBattleResult cant find corps_index1=%d battle_id=%d battle_index=%d m_battle_type=%d",
		         order.battle_order.corps_battle_index_1(), battle_id, battle_index, m_battle_type);
		return;
	}
	CorpsBattleInfo& info1 = iit1->second;

	auto iit2 = m_corps_battle_infos.find(order.battle_order.corps_battle_index_2());
	if (iit2 == m_corps_battle_infos.end())
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::OnSendBattleResult cant find corps_index2=%d battle_id=%d battle_index=%d m_battle_type=%d",
		         order.battle_order.corps_battle_index_2(), battle_id, battle_index, m_battle_type);
		return;
	}
	CorpsBattleInfo& info2 = iit2->second;

	//记录战斗结果
	order.battle_order.set_result(creator_win);
	order.dirty = true;
	m_order_dirty = true;

	int state = ((EliminateBattleManager::GetInstance().GetState() - EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_BEGIN) / 2) * 3 + 1;
	int old_battle_state1 = info1.BattleState();
	int old_battle_state2 = info2.BattleState();
	if (creator_win == 1)
	{
		//creator胜利
		info1.SetBattleState(state + 2);
		info2.SetBattleState(state + 1);
	}
	else if (creator_win == 0)
	{
		// creator失败
		info1.SetBattleState(state + 1);
		info2.SetBattleState(state + 2);
	}
	else
	{
		// 双负
		info1.SetBattleState(state + 1);
		info2.SetBattleState(state + 1);
	}
	info1.dirty = true;
	info2.dirty = true;
	m_info_dirty = true;

	SendKnockoutBattleResult(porder.get(), creator_win);

	//通知源服
	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBTE_BATTLE_RESULT);

	PB::corps_battle_order *pOrder = notify.add_eliminate_battle_orders();
	pOrder->CopyFrom(order.battle_order);

	auto pinfo1 = notify.add_eliminate_battle_infos();
	pinfo1->CopyFrom(info1.battle_info);
	auto pinfo2 = notify.add_eliminate_battle_infos();
	pinfo2->CopyFrom(info2.battle_info);

	CenterManager::GetInstance().BroadcastMessageNormal(notify, true);

	LOG_TRACE("BATTLE_STATE_DEBUG::OnSendBattleResult:corps_id1=%ld:index1=%d:old_battle_state1=%d:battle_state1=%d:corps_id2=%ld:index2=%d:old_battle_state2=%d:battle_state2=%d:cur_state=%d",
	          info1.battle_info.id(), info1.battle_info.index(), old_battle_state1, info1.BattleState(), info2.battle_info.id(), info2.battle_info.index(), old_battle_state2, info2.BattleState(), (int)EliminateBattleManager::GetInstance().GetState());

	GLog::formatlog("corps_eliminate_battle_result", "battle_type=%d:order_index=%d:corps_index1=%d:corps_id1=%ld:corps_index2=%d:corps_id2=%ld:result=%d",
	                m_battle_type, battle_index, order.battle_order.corps_battle_index_1(), info1.battle_info.id(),
	                order.battle_order.corps_battle_index_2(), info2.battle_info.id(), creator_win);
}

void CorpsEliminateBattle::PlayerEnterBattle(RoleInfo *pInfo, int param1, int param2, int param3, bool debug)
{
	if (!pInfo)
	{
		return;
	}
	LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":param1=%d:param2=%d:debug=%s", pInfo->roleid, param1, param2, debug ? "true" : "false");
#ifdef CHECK_ROAM_ACTIVITY
	/*
	if (!debug && !CampaignManager::GetInstance().IsActive(ROAM_ACTIVITY_INDEX_CENTER_ELIMINATE_KNOCKOUT))
	{
		LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":campaig is not active", pInfo->roleid);
		return;
	}
	*/
#endif

	int now_state = EliminateBattleManager::GetInstance().GetState();
	if (now_state <= PB::EBS_CENTER_SCORE_BATTLE_END || now_state >= PB::EBS_CENTER_KNOCKOUT_BATTLE_2_END)
	{
		LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":now_state=%d:state not match", pInfo->roleid, now_state);
		return;
	}

	PB::npt_player_enter_corps_battle_result result;
	result.set_battle_type(m_battle_type);

	CorpsBattleOrderPtr porder;
	if (param1 > 0)
	{
		//玩家要观战，param1为观战的order_index
		if (!GET_FUNC_SWITCH(kFuncCodeEliminateCenterWatcher))
		{
			return;
		}
		auto pOrderBase = GetCorpsBattleOrder(param1);
		if (!pOrderBase)
		{
			LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":order_index=%d:battle order not found", pInfo->roleid, param1);
			return;
		}
		porder = std::dynamic_pointer_cast<CorpsBattleOrder>(pOrderBase);
		//相同战队的玩家禁止观战
		if (pInfo->SNSReady() && pInfo->friends.GetEliminateGroupID() != 0)
		{
			CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(pInfo->friends.GetEliminateGroupID());
			if (pCorpsBattleInfo)
			{
				if (pCorpsBattleInfo->battle_info.order_index() == param1)
				{
					//相同战队禁止观战
					result.set_result(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_WATCH_SAME_GROUP);
					pInfo->SendMessage2Client(result);
					LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":order_index=%d:group_id=%ld:groupMemberCanNotWatch",
					          pInfo->roleid, param1, pInfo->friends.GetEliminateGroupID());
					return;
				}
			}
		}
	}
	else
	{
		//玩家要进入自己的战场
		if (!pInfo->SNSReady() || !pInfo->friends.GetEliminateGroupID())
		{
			LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":no group id", pInfo->roleid);
			return;
		}
		//判断职业
		if (!ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().CheckFighter(pInfo))
		{
			result.set_result(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_FIGHTER_PROF_NOT_MATCH);
			pInfo->SendMessage2Client(result);
			LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":CheckFighterFailed", pInfo->roleid);
			return;
		}
		CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(pInfo->friends.GetEliminateGroupID());
		if (!pCorpsBattleInfo)
		{
			result.set_result(-1);
			pInfo->SendMessage2Client(result);
			LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":battle info not found", pInfo->roleid);
			return;
		}

		auto oit = m_corps_battle_orders.find(pCorpsBattleInfo->battle_info.order_index());
		if (oit == m_corps_battle_orders.end() || !(oit->second))
		{
			result.set_result(-2);
			pInfo->SendMessage2Client(result);
			LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":battle order not found 2", pInfo->roleid);
			return;
		}
		porder = std::dynamic_pointer_cast<CorpsBattleOrder>(oit->second);
	}

	if (!porder)
	{
		result.set_result(-3);
		pInfo->SendMessage2Client(result);
		LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":battle order not found 3", pInfo->roleid);
		return;
	}
	CorpsBattleOrder& order = *porder;
	if (order.battle_order.battle_stage() != now_state)
	{
		LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":battle_stage=%d:now_state=%d",
		          pInfo->roleid, order.battle_order.battle_stage(), now_state);
		return;
	}
	if (!order.battle_inst_tid || !order.battle_inst_id)
	{
		result.set_result(ERROR_CENTER_BATTLE_CORPS_NOT_CREATE);
		pInfo->SendMessage2Client(result);
		LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":inst_tid=%d:inst_id=%d:no inst tid or id",
		          pInfo->roleid, order.battle_inst_tid, order.battle_inst_id);
		return;
	}

	if (param1 > 0 && m_vip_watch_players.find(pInfo->roleid) == m_vip_watch_players.end() &&
	        order.param >= (m_debug_watch_size > 0 ? m_debug_watch_size : CORPS_ELIMINATE_BATTLE_WATCH_PLAYER_NUM_MAX))
	{
		result.set_result(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_WATCH_TOO_MANY_PLAYER);
		pInfo->SendMessage2Client(result);
		LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":order.param=%d", pInfo->roleid, order.param);
		return;
	}

	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		result.set_result(-4);
		pInfo->SendMessage2Client(result);
		LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle:roleid=" PRINT64":no battle", pInfo->roleid);
		return;
	}

	PB::ipt_corps_eliminate_knockout_battle_enter enter;
	enter.set_eliminate_group_id(pInfo->friends.GetEliminateGroupID());
	enter.set_watch_order_index(param1);

	auto center_zoneid = pCenterBattle->GetCenterZoneID();
	CenterManager::GetInstance().SendMessage2Hub(enter, pInfo->roleid, pCenterBattle->GetServiceName(), center_zoneid);
	LOG_TRACE("CorpsEliminateBattle::PlayerEnterBattle::ipt_corps_eliminate_knockout_battle_enter::roleid=" PRINT64":eliminategroupid=%ld:center_zoneid=%d:watch_order_index=%d",
	          pInfo->roleid, pInfo->friends.GetEliminateGroupID(), center_zoneid, param1);
}

void CorpsEliminateBattle::CorpsEliminateBattleEnter(ruid_t roleid, PB::ipt_corps_eliminate_knockout_battle_enter& enter)
{
	LOG_TRACE("DS::CorpsEliminateBattleEntry::CorpsEliminateBattleEnter:roleid=%ld:group_id=%ld:watch_order_index=%d",
	          roleid, enter.eliminate_group_id(), enter.watch_order_index());

	if (m_config.state() != PB::corps_battle_config::CORPS_BATTLE_STATE_BATTLE_BEGIN)
	{
		return;
	}

	CorpsBattleOrderPtr porder;
	if (enter.watch_order_index() > 0)
	{
		//玩家要观战
		auto pOrderBase = GetCorpsBattleOrder(enter.watch_order_index());
		if (!pOrderBase)
		{
			return;
		}
		porder = std::dynamic_pointer_cast<CorpsBattleOrder>(pOrderBase);
	}
	else if (enter.eliminate_group_id() > 0)
	{
		//玩家要进入自己的战场
		CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(enter.eliminate_group_id());
		if (!pCorpsBattleInfo)
		{
			return;
		}

		auto oit = m_corps_battle_orders.find(pCorpsBattleInfo->battle_info.order_index());
		if (oit == m_corps_battle_orders.end() || !(oit->second))
		{
			Log::log(LOG_ERR, "DS::CorpsEliminateBattleEntry::CorpsEliminateBattleEnter cant find battle_index=%d roleid=%ld group_id=%ld",
			         pCorpsBattleInfo->battle_info.index(), roleid, enter.eliminate_group_id());
			return;
		}
		porder = std::dynamic_pointer_cast<CorpsBattleOrder>(oit->second);
	}
	else
	{
		return;
	}

	if (!porder)
	{
		return;
	}

	CorpsBattleOrder& order = *porder;
	if (!order.battle_inst_tid || !order.battle_inst_id || order.battle_order.result() >= 0)
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattleEntry::CorpsEliminateBattleEnter battle not begin. battle_index=%d roleid=%ld group_id=%ld watch_order_index=%d",
		         order.battle_order.index(), roleid, enter.eliminate_group_id(), enter.watch_order_index());

		PB::ipt_corps_eliminate_knockout_battle_enter_re re;
		re.set_result(ERROR_CENTER_BATTLE_CORPS_NOT_CREATE);
		re.set_order_index(order.battle_order.index());
		re.set_watch_player_num(order.corps_one_players.size());
		HUB_CLIENT.TransferProtocol(MERGE_ZONE(roleid), re, roleid);
		return;
	}

	if (enter.watch_order_index() > 0)
	{
		//观战需要判断观战者人数
		if (m_vip_watch_players.find(roleid) == m_vip_watch_players.end() && order.corps_one_players.size() >= (size_t)(m_debug_watch_size > 0 ? m_debug_watch_size : CORPS_ELIMINATE_BATTLE_WATCH_PLAYER_NUM_MAX))
		{
			LOG_TRACE("DS::CorpsEliminateBattleEntry::CorpsEliminateBattleEnter:roleid=%ld:group_id=%ld:watch_order_index=%d:watch_player_num=%zu",
			          roleid, enter.eliminate_group_id(), enter.watch_order_index(), order.corps_one_players.size());
			PB::ipt_corps_eliminate_knockout_battle_enter_re re;
			re.set_result(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_WATCH_TOO_MANY_PLAYER);
			re.set_order_index(order.battle_order.index());
			re.set_watch_player_num(order.corps_one_players.size());
			HUB_CLIENT.TransferProtocol(MERGE_ZONE(roleid), re, roleid);
			return;
		}
		auto it = order.corps_one_players.begin(), eit = order.corps_one_players.end();
		for (; it != eit; ++ it)
		{
			if (*it == roleid)
			{
				break;
			}
		}
		if (it == eit)
		{
			order.corps_one_players.insert(roleid);
		}
	}

	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}

	BattlePlayerEntry player_entry;
	player_entry.time_stamp = Timer::GetTime();
	player_entry.eliminate_group_id = enter.eliminate_group_id();
	player_entry.watch_order_index = enter.watch_order_index();
	m_player_info_map[roleid] = player_entry;

	PB::ipt_corps_eliminate_knockout_battle_enter_re re;
	re.set_result(0);
	re.set_order_index(order.battle_order.index());
	re.set_watch_player_num(order.corps_one_players.size());
	if (enter.watch_order_index() > 0)
	{
		re.set_is_watch_battle(true);
	}

	HUB_CLIENT.TransferProtocol(MERGE_ZONE(roleid), re, roleid);

	PB::ipt_center_battle_roam_teleport_player proto;
	proto.set_roleid(roleid);
	proto.set_center_battle_type(pCenterBattle->GetType());
	proto.set_center_zoneid(g_zoneid);
	proto.set_world_tid(order.battle_inst_tid);
	proto.set_world_id(order.battle_inst_id);

	HUB_CLIENT.TransferProtocol(MERGE_ZONE(roleid), proto, roleid);
}

void CorpsEliminateBattle::CorpsEliminateBattleEnterRe(ruid_t roleid, PB::ipt_corps_eliminate_knockout_battle_enter_re& re)
{
	if (re.result())
	{
		RoleInfo *pInfo = RoleMap::Instance().FindOnline(roleid);
		if (pInfo)
		{
			PB::npt_player_enter_corps_battle_result result;
			result.set_battle_type(m_battle_type);
			result.set_result(re.result());
			pInfo->SendMessage2Client(result);
		}
	}

	auto pOrderBase = GetCorpsBattleOrder(re.order_index());
	if (!pOrderBase)
	{
		return;
	}
	auto porder = std::dynamic_pointer_cast<CorpsBattleOrder>(pOrderBase);
	if (!porder)
	{
		return;
	}
	CorpsBattleOrder& order = *porder;
	order.param = re.watch_player_num();

	if (re.is_watch_battle())
	{
		//处理TLOG
		RoleInfo *pInfo = RoleMap::Instance().FindOnline(roleid);
		if (pInfo)
		{
			DEFINE_slogger(FORMAT, "eliminate_watch");
			BI_LOG_GLOBAL(pInfo->account);
			slogger.BI_HEADER2_DS(pInfo);
			slogger.P("battle_state", (int)porder->GetBattleStage())
			.P("battle_index", porder->Index())
			.P("eliminate_group_id_1", porder->CorpsOneID())
			.P("eliminate_group_leader_1", ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetEliminateGroupLeader(porder->CorpsOneID()))
			.P("eliminate_group_id_2", porder->CorpsTwoID())
			.P("eliminate_group_leader_2", ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().GetEliminateGroupLeader(porder->CorpsTwoID()));
		}
	}
	LOG_TRACE("CorpsEliminateBattle::CorpsEliminateBattleEnterRe:roleid=%ld:result=%d:order_index=%d:watch_player_num=%d",
	          roleid, re.result(), re.order_index(), re.watch_player_num());
}

bool CorpsEliminateBattle::CheckRoamBattle(const ruid_t& roleid, int& inst_id)
{
	auto it = m_player_info_map.find(roleid);
	if (it == m_player_info_map.end())
	{
		return false;
	}
	BattlePlayerEntry& player_entry = it->second;

	if (Timer::GetTime() - player_entry.time_stamp > 3600)
	{
		return false;
	}

	CorpsBattleOrderPtr porder;
	if (player_entry.watch_order_index > 0)
	{
		//玩家是观战的
		auto pOrderBase = GetCorpsBattleOrder(player_entry.watch_order_index);
		if (!pOrderBase)
		{
			return false;
		}
		porder = std::dynamic_pointer_cast<CorpsBattleOrder>(pOrderBase);
	}
	else if (player_entry.eliminate_group_id > 0)
	{
		//玩家是进入自己的比赛
		CorpsBattleInfo *pCorpsBattleInfo = GetBattleInfoByCorpsID(player_entry.eliminate_group_id);
		if (!pCorpsBattleInfo)
		{
			return false;
		}
		auto oit = m_corps_battle_orders.find(pCorpsBattleInfo->battle_info.order_index());
		if (oit == m_corps_battle_orders.end() || !(oit->second))
		{
			Log::log(LOG_ERR, "DS::CorpsEliminateBattle::CheckRoamBattle cant find battle_index=%d roleid=%ld group_id=%ld",
			         pCorpsBattleInfo->battle_info.index(), roleid, player_entry.eliminate_group_id);
			return false;
		}
		porder = std::dynamic_pointer_cast<CorpsBattleOrder>(oit->second);
	}
	else
	{
		return false;
	}

	if (!porder)
	{
		return false;
	}

	CorpsBattleOrder& order = *porder;
	if (!order.battle_inst_tid || !order.battle_inst_id ||
	        order.battle_order.result() >= 0)
	{
		Log::log(LOG_ERR, "DS::CorpsEliminateBattle::CheckRoamBattle battle not begin. battle_index=%d roleid=%ld group_id=%ld",
		         order.battle_order.index(), roleid, player_entry.eliminate_group_id);
		return false;
	}

	if (player_entry.watch_order_index > 0)
	{
		//如果玩家是进来观战的，需要统计玩家数量，调用OnPlayerChangeScene和OnPlayerLogout函数
		RoleCache *pcache = RoleMap::Instance().FindEntry(roleid);
		if (!pcache || !pcache->info)
		{
			return false;
		}

		pcache->info->SetCorpsBattle(m_battle_type, order.battle_order.index());
	}

	inst_id = order.battle_inst_id;
	return true;
}
//清空结果重新开启比赛
int CorpsEliminateBattle::ClearBattleOrderResult(int state, int index)
{
	int now_state = EliminateBattleManager::GetInstance().GetState();
	LOG_TRACE("CorpsEliminateBattle::ClearBattleOrderResult::now_state=%d:state=%d:index=%d", now_state, state, index);
	if (now_state != state)
	{
		return -1;
	}

	int _index = 0;
	for (auto it = m_corps_battle_orders.begin(), eit = m_corps_battle_orders.end(); it != eit; ++ it)
	{
		auto pOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
		if (!pOrder)
		{
			continue;
		}
		if (pOrder->battle_order.battle_stage() != state)
		{
			continue;
		}

		if (++ _index == index)
		{
			if (pOrder->battle_inst_tid == 0 && pOrder->battle_inst_id == 0)
			{
				if (pOrder->battle_order.result() == -1)
				{
					return -2;
				}
				GLog::formatlog("clear_eliminate_battle_result", "state=%d:index=%d:old_result=%d",
				                state, index, pOrder->battle_order.result());
				pOrder->battle_order.set_result(-1);
				pOrder->dirty = true;
				m_order_dirty = true;
				AddBattleOrderChanged(pOrder);
				m_create_battle_ok = false;
				return 0;
			}
			else
			{
				return -3;
			}
		}
	}
	return -4;
}
void CorpsEliminateBattle::OnFinalsEnd()
{
	int battle_time = EliminateBattleManager::GetInstance().GetBattleTime(PB::EBS_CENTER_KNOCKOUT_BATTLE_2_BEGIN);
	LOG_TRACE("CorpsEliminateBattle::OnFinalsEnd");
	for (auto it = m_corps_battle_orders.begin(), eit = m_corps_battle_orders.end(); it != eit; ++ it)
	{
		auto pOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(it->second);
		if (!pOrder)
		{
			continue;
		}
		if (pOrder->battle_order.battle_stage() != PB::EBS_CENTER_KNOCKOUT_BATTLE_2_BEGIN)
		{
			continue;
		}
		if (pOrder->BattleTime() < battle_time)
		{
			continue;
		}
		if (pOrder->Result() != 1 && pOrder->Result() != 0)
		{
			continue;
		}
		int winner_index = pOrder->Result() == 1 ? pOrder->CorpsOneIndex() : pOrder->CorpsTwoIndex();

		auto& battle_info = m_corps_battle_infos[winner_index];

		int64_t group_id = battle_info.ID();

		int src_zoneid = MERGE_ZONE(group_id);

		LOG_TRACE("CorpsEliminateBattle::OnFinalsEnd::eliminate_group_id=%ld:order_index=%d:src_zoneid=%d", group_id, pOrder->Index(), src_zoneid);

		PB::ipt_eliminate_battle_notify notify;
		notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_NOTIFY_CHAMPION);
		notify.set_eliminate_champion(group_id);
		GET_CENTER_BATTLE(centerbattle, CBT_KNOCKOUT_ELIMINATE)
		CenterManager::GetInstance().SendMessage2Hub(notify, 0, centerbattle->GetServiceName(), src_zoneid);
		GET_CENTER_BATTLE_END
	}
}
void CorpsEliminateBattle::OnPlayerChangeScene(RoleInfo *pInfo, int world_tid, int scene_tag)
{
	LOG_TRACE("DS::CorpsEliminateBattle::OnPlayerChangeScene battle_index=%d m_battle_type=%d roleid=%ld world_tid=%d scene_tag=%d",
	          pInfo->corps_battle_index, m_battle_type, pInfo->roleid, world_tid, scene_tag);

	if (!pInfo->corps_battle_index)
	{
		return;
	}

	auto pOrderBase = GetCorpsBattleOrder(pInfo->corps_battle_index);
	if (!pOrderBase)
	{
		return;
	}
	auto pOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(pOrderBase);
	if (!pOrder)
	{
		return;
	}
	CorpsBattleOrder& order = *pOrder;

	if (order.battle_inst_tid == 0 || world_tid == order.battle_inst_tid)
	{
		return;
	}

	auto it = order.corps_one_players.begin(), eit = order.corps_one_players.end();
	for (; it != eit; ++ it)
	{
		if (*it == pInfo->roleid)
		{
			order.corps_one_players.erase(it);
			break;
		}
	}
	if (it != eit)
	{
		PB::ipt_corps_eliminate_knockout_battle_enter_re re;
		re.set_result(0);
		re.set_order_index(order.battle_order.index());
		re.set_watch_player_num(order.corps_one_players.size());
		HUB_CLIENT.TransferProtocol(MERGE_ZONE(pInfo->roleid), re, 0);
	}
}

void CorpsEliminateBattle::OnPlayerLogout(RoleInfo *pInfo)
{
	LOG_TRACE("DS::CorpsEliminateBattle::OnPlayerLogout battle_index=%d m_battle_type=%d roleid=%ld",
	          pInfo->corps_battle_index, m_battle_type, pInfo->roleid);

	if (!pInfo->corps_battle_index)
	{
		return;
	}

	auto pOrderBase = GetCorpsBattleOrder(pInfo->corps_battle_index);
	if (!pOrderBase)
	{
		return;
	}
	auto pOrder = std::dynamic_pointer_cast<CorpsBattleOrder>(pOrderBase);
	if (!pOrder)
	{
		return;
	}
	CorpsBattleOrder& order = *pOrder;

	if (order.battle_inst_tid == 0)
	{
		return;
	}

	auto it = order.corps_one_players.begin(), eit = order.corps_one_players.end();
	for (; it != eit; ++ it)
	{
		if (*it == pInfo->roleid)
		{
			order.corps_one_players.erase(it);
			break;
		}
	}
	if (it != eit)
	{
		PB::ipt_corps_eliminate_knockout_battle_enter_re re;
		re.set_result(0);
		re.set_order_index(order.battle_order.index());
		re.set_watch_player_num(order.corps_one_players.size());
		HUB_CLIENT.TransferProtocol(MERGE_ZONE(pInfo->roleid), re, 0);
	}
}
int CorpsEliminateBattle::CheckEliminateGuessVaild(PB::ELIMINATE_BATTLE_STATE state, int64_t corps_id, int order_index) const
{
	LOG_TRACE("CorpsEliminateBattle::CheckEliminateGuessVaild:state=%d:corps_id=%ld:order_index=%d", (int)state, corps_id, order_index);

	auto pOrder = GetCorpsBattleOrder(order_index);
	//没有找到对阵表
	if (!pOrder)
	{
		LOG_TRACE("CorpsEliminateBattle::CheckEliminateGuessVaild:state=%d:corps_id=%ld:order_index=%d:ERROR_ELIMINATE_GUESS_NO_ORDER",
		          (int)state, corps_id, order_index);
		return GNET::ERROR_ELIMINATE_GUESS_NO_ORDER;
	}
	//state不匹配
	if (pOrder->GetBattleStage() != state)
	{
		LOG_TRACE("CorpsEliminateBattle::CheckEliminateGuessVaild:state=%d:corps_id=%ld:order_index=%d:order_stage=%d:ERROR_ELIMINATE_GUESS_WRONG_STATE",
		          (int)state, corps_id, order_index, pOrder->GetBattleStage());
		return GNET::ERROR_ELIMINATE_GUESS_WRONG_STATE;
	}
	//检查战队ID
	if (pOrder->CorpsOneID() != corps_id && pOrder->CorpsTwoID() != corps_id)
	{
		LOG_TRACE("CorpsEliminateBattle::CheckEliminateGuessVaild:state=%d:corps_id=%ld:corps_id_1=%ld:corps_id_2=%ld:ERROR_ELIMINATE_GUESS_WRONG_ID",
		          (int)state, corps_id, pOrder->CorpsOneID(), pOrder->CorpsTwoID());
		return GNET::ERROR_ELIMINATE_GUESS_WRONG_ID;
	}
	return 0;
}
int CorpsEliminateBattle::GetEliminateGuessResult(PB::ELIMINATE_BATTLE_STATE state, int64_t corps_id, int order_index) const
{
	LOG_TRACE("CorpsEliminateBattle::GetEliminateGuessResult:state=%d:corps_id=%ld:order_index=%d", (int)state, corps_id, order_index);

	auto pOrder = GetCorpsBattleOrder(order_index);
	if (!pOrder)
	{
		return -1;
	}

	if (pOrder->GetBattleStage() != state)
	{
		return -1;
	}

	switch (pOrder->Result())
	{
	case 0:
	{
		if (corps_id == pOrder->CorpsTwoID())
		{
			return 1;
		}
		else
		{
			return 0;
		}
	}
	break;

	case 1:
	{
		if (corps_id == pOrder->CorpsOneID())
		{
			return 1;
		}
		else
		{
			return 0;
		}
	}
	break;

	case 2:
	{
		return 0;
	}
	break;

	default:
		break;
	}

	return -1;
}
void CorpsEliminateBattle::DebugSetScore(RoleInfo *pInfo, int score)
{
	if (!pInfo)
	{
		return;
	}
	if (!CenterManager::GetInstance().IsCenter())
	{
		return;
	}
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	ruid_t battle_id = pInfo->friends.GetEliminateGroupID();
	LOG_TRACE("CorpsEliminateBattle::DebugSetScore:roleid=" PRINT64":battle_id=" PRINT64":score=%d", pInfo->roleid, battle_id, score);
	CorpsBattleInfo *pBattle = GetBattleInfoByCorpsID(battle_id);
	if (!pBattle)
	{
		return;
	}

	int now_time = GNET::Timer::GetTime();
	int index = GetOrederedIndex(pBattle);		//->battle_info.index();
	int origin_score = GetOrederedValue(pBattle);	//->battle_info.eliminate_battle_score();
	int origin_time = GetOrederedTime(pBattle);	//->battle_info.score_result_timestamp();
	LOG_TRACE("CorpsEliminateBattle::DebugSetScore:roleid=" PRINT64":battle_id=" PRINT64":score=%d:origin_index=%d:origin_score=%d:origin_time=%d",
	          pInfo->roleid, battle_id, score, index, origin_score, origin_time);

	pBattle->battle_info.set_consecutive_win_count(pBattle->battle_info.consecutive_win_count() + 1);
	pBattle->battle_info.set_eliminate_battle_score(score);
	pBattle->battle_info.set_total_count(pBattle->battle_info.total_count() + 1);
	pBattle->battle_info.set_win_count(pBattle->battle_info.win_count() + 1);
	pBattle->battle_info.set_apply_time(now_time);
	pBattle->dirty = true;

	m_info_dirty = true;

	SortEliminateBattleScore(pBattle, index, origin_score, origin_time);

	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_SCORE_BATTLE_RESULT);

	PB::corps_battle_info *pBattle_info = notify.add_eliminate_battle_infos();
	pBattle_info->CopyFrom(pBattle->battle_info);

	if (notify.eliminate_battle_infos_size() != 0)
	{
		CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
	}

	SendScoreBattleResult(pBattle, 1, score - origin_score);

}

bool CorpsEliminateBattle::IDIPRefreshBattleInfoToNormal()
{
	try
	{
		LOG_TRACE("CorpsEliminateBattle::IDIPRefreshBattleInfoToNormal");

		// 检查是否为中心服
		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle || pCenterBattle->GetCenterZoneID() != g_zoneid)
		{
			LOG_TRACE("CorpsEliminateBattle::IDIPRefreshBattleInfoToNormal:not_center_server:current_zoneid=%d:center_zoneid=%d",
			          g_zoneid, pCenterBattle ? pCenterBattle->GetCenterZoneID() : 0);
			return false;
		}

		// 创建通知消息
		PB::ipt_eliminate_battle_notify notify;
		notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_IDIP_REFRESH_BATTLE_INFO);

		// 获取所有战队的battleinfo
		const auto& battle_infos = GetCorpsBattleInfos();
		for (const auto& pair : battle_infos)
		{
			auto pinfo = notify.add_eliminate_battle_infos();
			pinfo->CopyFrom(pair.second.battle_info);
		}

		LOG_TRACE("CorpsEliminateBattle::IDIPRefreshBattleInfoToNormal:refresh_all_corps:count=%d",
		          notify.eliminate_battle_infos_size());

		if (notify.eliminate_battle_infos_size() > 0)
		{
			// 广播给所有普通服
			CenterManager::GetInstance().BroadcastMessageNormal(notify, true);
			LOG_TRACE("CorpsEliminateBattle::IDIPRefreshBattleInfoToNormal:broadcast_success:count=%d",
			          notify.eliminate_battle_infos_size());
			return true;
		}

		return false;
	}
	catch (...)
	{
		GLog::log(LOG_ERR, "CorpsEliminateBattle::IDIPRefreshBattleInfoToNormal exception");
		return false;
	}
}
