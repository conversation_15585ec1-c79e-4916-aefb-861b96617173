#ifndef __DS_CORPS_ELIMINATE_BATTLE_H__
#define __DS_CORPS_ELIMINATE_BATTLE_H__
namespace PB { namespace ipt_corps_eliminate_knockout_battle_enter_NS { class ipt_corps_eliminate_knockout_battle_enter; } using namespace ipt_corps_eliminate_knockout_battle_enter_NS; }
namespace PB { namespace eliminate_battle_guess_data_t_NS { class eliminate_battle_guess_data_t; } using namespace eliminate_battle_guess_data_t_NS; }
namespace PB { namespace corps_battle_config_NS { class corps_battle_config; } using namespace corps_battle_config_NS; }
#include "gprotoc/ipt_eliminate_battle_notify.pb.h"
#include "gprotoc/ELIMINATE_BATTLE_STATE.pb.h"
#include "gprotoc/corps_battle_info.pb.h"
namespace PB { namespace ipt_corps_eliminate_knockout_battle_enter_re_NS { class ipt_corps_eliminate_knockout_battle_enter_re; } using namespace ipt_corps_eliminate_knockout_battle_enter_re_NS; }

#include "corps_battle.h"
#include "eliminatebattlemanager.h"
#include "eliminategroupmanager.h"
#include "arandomgen.h"
#include <unordered_map>

#define CLIENT_ORDER_SIZE 100
#define SEED_PLAYER_COUNT 8
namespace GNET{

class CorpsEliminateBattle: public CorpsBattleEntry
{
	struct BattlePlayerEntry
	{
		ruid_t eliminate_group_id;
		int watch_order_index;
		int time_stamp;
		bool enter;

		BattlePlayerEntry()
		{   
			eliminate_group_id = 0;
			watch_order_index = 0;
			time_stamp = 0;
			enter = false;
		}   
	};  
	struct normal_step_tag {};
	struct first_step_tag {};
	typedef std::map<ruid_t, BattlePlayerEntry> PLAYER_INFO_MAP;

	typedef std::unordered_map<ruid_t/*eliminate_group_id*/, const CorpsBattleInfo *> CORPS_BATTLE_INFO_CHANGED;
	typedef std::unordered_map<int/*order_index*/, CorpsBattleOrderBasePtr> CORPS_BATTLE_ORDER_CHANGED;

	typedef std::set<ruid_t> CORPS_BATTLE_VIP_WATCH_PLAYERS;
public:
	CorpsEliminateBattle(int battle_type): CorpsBattleEntry(battle_type), m_debug_watch_size(-1)
	{
	}
	virtual ~CorpsEliminateBattle()
	{
	}
	virtual bool Init(lua_State *L, LuaWrapper& lw) override;
	void SortEliminateBattleScore(CorpsBattleInfo* new_info, int origin_index, int origin_value, int origin_time);
	virtual bool Update(int now_time, int day_begin, int week_begin) override;
	virtual void OnNewWeek(int week_begin) override;
	virtual void MatchBattle(int now_time) override;
	virtual void MatchBattle2(int now_time) override;
	virtual void IDIPSyncBattleMatchResult() override;
	virtual void BeginBattle2(int now_time) override;
	virtual void EndBattle(int now_time) override;

    virtual int CheckEliminateGuessVaild(PB::ELIMINATE_BATTLE_STATE state, int64_t corps_id, int order_index) const override;
    virtual int GetEliminateGuessResult(PB::ELIMINATE_BATTLE_STATE state, int64_t corps_id, int order_index) const override;
	
	virtual void OnCreateBattle(int battle_id, int battle_index, int param, int ret) override;
	virtual void OnPlayerLogin(RoleInfo* pInfo) override;
	virtual void OnBattleClose(int battle_id, int battle_index, int param) override;
	virtual void OnSendBattleResult(int64_t target_id, int battle_id, int battle_index, int inst_param, int creator_win, int64_t param, int64_t param2, const std::map<int64_t, std::tuple<int64_t, int>> &param_map = std::map<int64_t, std::tuple<int64_t, int>>()) override;
	virtual void OnLoadCorpsBattleInfo(CorpsBattleInfo& info) override;
	virtual void OnLoadBattleOrder(CorpsBattleOrderBasePtr porder) override;

	virtual void ClearEliminateBattleScore() override;
	virtual void AddBattleInfoChanged(const CorpsBattleInfo* pInfoChanged, BattleInfoChangeType type) override;
	virtual void AddBattleOrderChanged(CorpsBattleOrderBasePtr pOrderChanged);
	virtual void CorpsEliminateBattleNotify(const PB::ipt_eliminate_battle_notify& notify) override;
	virtual void PlayerSearchBattleList(RoleInfo* pInfo, int64_t target_corps_id, int battle_begin_time, int page, int search_battle_type, int search_city_index, int param) override;
	virtual void PlayerEnterBattle(RoleInfo* pInfo, int param1, int param2, int param3, bool debug) override;
	virtual void CorpsEliminateBattleEnter(ruid_t roleid, PB::ipt_corps_eliminate_knockout_battle_enter& enter) override;
	virtual void CorpsEliminateBattleEnterRe(ruid_t roleid, PB::ipt_corps_eliminate_knockout_battle_enter_re& re) override;
	virtual bool CheckRoamBattle(const ruid_t& roleid, int & inst_id) override;
    virtual void OnFinalsEnd() override;
	
	virtual PB::corps_battle_config::CORPS_BATTLE_STATE GetNowState(int now_time) const override;
	virtual void JudgeState(int now_time) override;
	
	virtual void OnGSNotifyResult(ruid_t role_one, ruid_t role_two, int grade_change_one, int grade_change_two, int result) override; /*result为1的时候，role_one胜利*/
    virtual void IdipSetEliminateBattleScore(int64_t group_id, int score) override;
	virtual void IdipReMatchBattle() override;
	virtual void IdipRenameCorps(int64_t group_id, const std::string& name) override;
	virtual void OnInsertOrderBattleInfo(CorpsBattleInfo* info) override;
	virtual void OnUpdateOrderBattleInfo(CorpsBattleInfo* new_info, int origin_index, int origin_value, int origin_time) override
	{
		if (!new_info)
			return;
		if (new_info->battle_info.apply_time() < EliminateBattleManager::GetInstance().GetBattleTime(EBS_LOCAL_SCORE_BATTLE_BEGIN))
			return;

		LOG_TRACE("CorpsEliminateBattle::OnUpdateOrderBattleInfo:index=%d:value=%d:time=%d", origin_index, origin_value, origin_time);
		SortEliminateBattleScore(new_info, origin_index, origin_value, origin_time);
	}
	virtual int GetOrederedValue(const CorpsBattleInfo* info) const override { return (info == NULL)? 0 : info->battle_info.eliminate_battle_score();}
	virtual int GetOrederedIndex(const CorpsBattleInfo* info) const override { return (info == NULL)? -1 : info->battle_info.index(); }
	virtual int GetOrederedTime(const CorpsBattleInfo* info) const override { return (info == NULL)? 0 : info->battle_info.apply_time(); }
	
	virtual int GetBattleInstanceType() const override { return INSTANCE_CENTER_ELIMINATE_KNOCKOUT_BATTLE; }
	//virtual void OnOtherZoneConnect(zone_id_t zoneid) override;
	virtual CorpsBattleOrderBase* GetBattleOrderByGroupID(ruid_t corps_id) override;
	
	virtual int ClearBattleOrderResult(int state, int index) override;
	virtual bool InsertBattleInfo(const PB::corps_battle_info& battle_info)
	{
		if (CenterManager::GetInstance().IsCenter())
			return CorpsBattleEntry::InsertBattleInfo(battle_info);

		if (m_corps_battle_ids.find(battle_info.id()) != m_corps_battle_ids.end())
			return false;
		CorpsBattleInfo bi;
		bi.battle_info = battle_info;
		bi.dirty = true;
		m_corps_battle_infos[bi.battle_info.index()] = bi;
		m_corps_battle_ids[bi.battle_info.id()] = bi.battle_info.index();
		OnInsertOrderBattleInfo(&(m_corps_battle_infos[bi.battle_info.index()]));
		m_info_dirty = true;
		return true;
	}
	virtual void DebugSetScore(RoleInfo * pInfo, int score) override;
	virtual void OnPlayerChangeScene(RoleInfo* pInfo, int world_tid, int scene_tag) override;
	virtual void OnPlayerLogout(RoleInfo* pInfo) override;
	virtual bool IsGuessValid(const PB::eliminate_battle_guess_data_t& guess_data) const override;
	virtual int GetGuessRightCount(const PB::eliminate_battle_guess_data_t& guess_data) const override;
	virtual int GetGuessTotalRightCount(const ::google::protobuf::RepeatedPtrField< ::PB::eliminate_battle_guess_data_t >& guess_datas) const override;
	virtual bool FillRightGuessData(int state, PB::eliminate_battle_guess_data_t& guess_data) const override;
    virtual void CheckFighterProf() const override;

    virtual void DebugSetWatchSize(int size) override
    {
        m_debug_watch_size = size;
    }

    // IDIP指令：刷新中心服battleinfo到普通服
    bool IDIPRefreshBattleInfoToNormal();

private:
	void DumpOrder();
	void SendScoreBattleResult(const CorpsBattleInfo* pInfo, int result, int score_diff);
	void SendKnockoutBattleResult(const CorpsBattleOrder* pOrder, int result);
	void BroadcastSpeak(const std::vector<NameRuidPair>& battle_info_vec);
	void __MatchBattleWarmUp(PB::ipt_eliminate_battle_notify& notify);
	void __MatchBattleFirstStep(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess);
	void __MatchBattleAddonStep1(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess);
	void __MatchBattleAddonStep2(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess);
	void __MatchBattleAddonStep3(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess);
	void __MatchBattleAddonStep4(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess);
	void __MatchBattleFollowStep(PB::ipt_eliminate_battle_notify& notify, std::multimap<int/*rank_diff*/, int/*order_index*/, std::greater<int> >& candidate_guess);
	void __GatherWinnerFromLastState(ORDER_BATTLE_INFO_SET& candidate_set);
	void __GatherLoserFromLastState(ORDER_BATTLE_INFO_SET& candidate_set);
	void __GatherCandidateOne(ORDER_BATTLE_INFO_SET& candidate_set, PB::ELIMINATE_BATTLE_STATE state, bool winner);
	void __GatherCandidateTwo(ORDER_BATTLE_INFO_SET& candidate_set, PB::ELIMINATE_BATTLE_STATE state, bool winner);
	int __CreateBattleOrder(CorpsBattleInfo* info_one, CorpsBattleInfo* info_two, PB::ipt_eliminate_battle_notify& notify, int init_battle_time);
	int __CreateBattleOrderBoth(CorpsBattleInfo* info_one, CorpsBattleInfo* info_two, PB::ipt_eliminate_battle_notify& notify, int init_battle_time);
	int __CreateBattleOrderSingle(CorpsBattleInfo* info_one, PB::ipt_eliminate_battle_notify& notify, int init_battle_time);

	template<typename Orders>
	size_t GetOrderSizeHelper(const Orders& orders, normal_step_tag) const
	{
		return orders.size();
	}

	template<typename Orders>
	size_t GetOrderSizeHelper(const Orders& orders, first_step_tag) const
	{
		return orders.size() > SEED_PLAYER_COUNT ? (orders.size() - SEED_PLAYER_COUNT) : 0;
	}

	template<typename Orders, class Tag = normal_step_tag>
	void GetMatchCountInfo(const Orders& orders, size_t& real_match_count, bool& has_single, Tag tag = Tag())
	{
		size_t match_count = EliminateBattleManager::GetInstance().GetMatchCount();
		//把上次胜利的order里的玩家取出来重新匹配
		size_t order_size = GetOrderSizeHelper(orders, tag);
		if(order_size >= match_count*2)
		{
			real_match_count = match_count;
		}
		else
		{
			real_match_count = order_size / 2;
			if(order_size % 2 != 0)
			{
				has_single = true;
			}
		}
	}
	inline int GetIndex(const ORDER_BATTLE_INFO_MAP::const_iterator& iter) const { return iter->first._index; }
	inline int GetIndex(const ORDER_BATTLE_INFO_SET::const_iterator& iter) const { return iter->_index; }

	template<typename Container >
	const typename Container::const_iterator GetBeginIterHelper(const Container& candidates, normal_step_tag tag) const
	{
		return candidates.begin();
	}
	template<class Container >
	const typename Container::const_iterator GetBeginIterHelper(const Container& candidates, first_step_tag tag) const
	{
		auto it = candidates.begin();
		auto ie = candidates.end();
		int count = 0;
		for(; it != ie && count < SEED_PLAYER_COUNT; it++, count++)
		{

		}
		return it;
	}
	inline CorpsBattleInfo* GetBattleInfo(const ORDER_BATTLE_INFO_SET::const_iterator& iter) const 
	{
		auto mit = m_order_battle_infos.find(*iter);
		if (mit != m_order_battle_infos.end())
			return mit->second;
		return NULL;
	}
	inline CorpsBattleInfo* GetBattleInfo(const ORDER_BATTLE_INFO_MAP::const_iterator& iter) const { return iter->second; }
	template<class Candidates, class Tag = normal_step_tag>
	void DoMatchBattle(const Candidates& candidates, PB::ipt_eliminate_battle_notify& notify, size_t real_match_count, bool has_single, std::multimap<int, int, std::greater<int> >& candidate_guess, Tag tag = Tag())
	{
		//auto iter = candidates.begin();
		auto iter = GetBeginIterHelper(candidates, tag);
		auto eiter = candidates.end();
		int counter = 0;

		std::vector<int/*index*/> candidate_one;	//上半区的队伍
		std::vector<int/*index*/> candidate_two;	//下半区的队伍
		std::map<int/*index*/, int/*rank*/> rank_one;
		std::map<int/*index*/, int/*rank*/> rank_two;

		int guess_count = EliminateBattleManager::GetInstance().GetGuessCount();
		int candidate_single = 0;			//落单的队伍
		int init_battle_time = EliminateBattleManager::GetInstance().GetBattleTime((PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() + 1));
		PB::ELIMINATE_BATTLE_STATE battle_state = (PB::ELIMINATE_BATTLE_STATE)(EliminateBattleManager::GetInstance().GetState() + 1);
		int battle_interval = EliminateBattleManager::GetInstance().GetBattleInterval(battle_state);
		int index = 0;
		int single_index = -1;
		int total_count = real_match_count * 2 + (has_single ? 1:0);
		int rand_num = has_single ? abase::Rand(0, total_count - 1) : -1;
		
		LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:battle_state=%d:guess_count=%d", (int)battle_state, guess_count);
		//TODO:填充上半区的队伍
		for(; iter != eiter && index < (int)real_match_count; iter++, counter++)
		{
			int tmp_index = GetIndex(iter);
			LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Gather_Candidate_One:battle_state=%d:index=%d:count=%d:rand_num=%d", 
					(int)battle_state, tmp_index, counter, rand_num);
			if(counter == rand_num)
			{
				single_index = tmp_index;
				LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Gather_Candidate_Single:battle_state=%d:index=%d:count=%d:rand_num=%d", 
						(int)battle_state, tmp_index, counter, rand_num);
			}
			else
			{
				candidate_one.push_back(tmp_index);
				rank_one[tmp_index] = counter;
				index++;
			}
		}
		//TODO:填充下半区的队伍
		index = 0;
		for(; iter != eiter && index < (int)real_match_count; iter++, counter++)
		{
			int tmp_index = GetIndex(iter);
			LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Gather_Candidate_Two:battle_state=%d:index=%d:count=%d:rand_num=%d", 
					(int)battle_state, tmp_index, counter, rand_num);
			if(counter == rand_num)
			{
				single_index = tmp_index;
				LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Gather_Candidate_Single:battle_state=%d:index=%d:count=%d:rand_num=%d", 
						(int)battle_state, tmp_index, counter, rand_num);
			}
			else
			{
				candidate_two.push_back(tmp_index);
				rank_two[tmp_index] = counter;
				index++;
			}
		}

		if(iter != eiter && has_single && counter == rand_num)
		{
			single_index = GetIndex(iter);
			LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Gather_Candidate_Single:battle_state=%d:index=%d:count=%d:rand_num=%d", 
					(int)battle_state, single_index, counter, rand_num);

		}

		//std::random_shuffle(candidate_one.begin(), candidate_one.end());
		//std::random_shuffle(candidate_two.begin(), candidate_two.end());
        std::reverse(candidate_two.begin(), candidate_two.end());

		//TODO:填充落单的队伍
		if(has_single && single_index > 0)
		{
			candidate_two.push_back(single_index);
			LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Gather_Candidate_Two_Single:battle_state=%d:index=%d", 
					(int)battle_state, single_index);
		}
		// TODO set battle_info
		auto state = ((EliminateBattleManager::GetInstance().GetState() - EBS_CENTER_KNOCKOUT_BATTLE_WARMUP_BEGIN + 1) / 2) * 3 + 1;
		for (iter = GetBeginIterHelper(candidates, tag); iter != candidates.end(); ++iter) {
			// get info from candidate
			auto p_battle_info = GetBattleInfo(iter);
			if (p_battle_info)
			{
				auto tmp = state;
				// 轮空队伍自动获胜
				if (p_battle_info->battle_info.index() == single_index) tmp += 2;
				p_battle_info->SetBattleState(tmp);
			}
		}

		LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:candidate_one=%ld:candidate_two=%ld:battle_state=%d:battle_interval=%d:real_match_count=%ld:has_single=%s:single_index=%d:rand_num=%d:total_count=%d",
				candidate_one.size(), candidate_two.size(), (int)battle_state, battle_interval, 
				real_match_count, has_single ? "true":"false", single_index, rand_num, total_count);
		counter = 0;
		int rand_order = 0;
		for(index = 0; index < (int)candidate_one.size(); index++, counter++)
		{
			int index_one = candidate_one.at(index);
			int index_two = candidate_two.at(index);
			int order_index = 0;
			auto& battle_info_one = m_corps_battle_infos[index_one];
			auto& battle_info_two = m_corps_battle_infos[index_two];
			int diff = rank_two[index_two] - rank_one[index_one];
			rand_order = abase::Rand(0, 1);
			if(rand_order == 0)
				order_index = __CreateBattleOrder(&battle_info_one, &battle_info_two, notify, init_battle_time + counter * battle_interval);
			else
				order_index = __CreateBattleOrder(&battle_info_two, &battle_info_one, notify, init_battle_time + counter * battle_interval);

			if(order_index <= 0) continue;
 
			if((int)candidate_guess.size() < guess_count)
			{
				candidate_guess.insert(std::make_pair(diff, order_index));
				LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Direct:rank_one=%d:index_one=%d:rank_two=%d:index_two=%d:diff=%d:order_index=%d", 
						rank_one[index_one], index_one, rank_two[index_two], index_two,
						diff, order_index);
			}
			else if(candidate_guess.begin()->first > diff)
			{
				candidate_guess.erase(candidate_guess.begin());
				candidate_guess.insert(std::make_pair(diff, order_index));
				LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:Replace:rank_one=%d:index_one=%d:rank_two=%d:index_two=%d:diff=%d:order_index=%d", 
						rank_one[index_one], index_one, rank_two[index_two], index_two,
						diff, order_index);
			}
		}
		//TODO:	打印竞猜列表
		for(auto it = candidate_guess.begin(); it != candidate_guess.end(); ++it)
		{
			LOG_TRACE("CorpsEliminateBattle::DoMatchBattle:candidate_guess:battle_state=%d:rank_diff=%d:order_index=%d", (int)battle_state, it->first, it->second);
		}
		//TODO: 处理落单的前面分组的玩家队伍
		if(has_single && index < (int)candidate_two.size())
		{
			candidate_single = candidate_two.at(index);
			auto& battle_info = m_corps_battle_infos[candidate_single];
			int order_index = __CreateBattleOrder(&battle_info, NULL, notify, init_battle_time + counter * battle_interval);
			if((int)candidate_guess.size() < guess_count && order_index > 0)
			{
				candidate_guess.insert(std::make_pair(0, order_index));
				LOG_TRACE("CorpsEliminateBattle:DoMatchBattle:FillSingle:index_single=%d:order_index=%d", candidate_single, order_index);
			}
		}
	}
	void ClearWarmUpOrderMap();
private:
    int m_debug_watch_size;
	CORPS_BATTLE_INFO_CHANGED m_info_changed;
	CORPS_BATTLE_ORDER_CHANGED m_order_changed;
	PLAYER_INFO_MAP m_player_info_map;
	CORPS_BATTLE_VIP_WATCH_PLAYERS m_vip_watch_players;
};
}//GNET

#endif
