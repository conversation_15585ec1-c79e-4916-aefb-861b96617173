#include "eliminatebattlemanager.h"
#include "eliminategroupmanager.h"
#include "gprotoc/ipt_eliminate_battle_notify.pb.h"
#include "gprotoc/eliminate_group.pb.h"
#include "gprotoc/ELIMINATE_BATTLE_STATE.pb.h"

#include "centermanager.h"
#include "global_config.h"
#include "centerbattle.h"
#include "centerarenaeliminatebattle.h"
#include "diaoxiang_manager.h"
#include "corps_battle_man.h"
#include "corps_battle_base.h"

using namespace GNET;

namespace
{
const int ELIMINATE_BATTLE_MANAGER_UPDATE_INTERVAL  = 5;
}

struct battle_time
{
	int timestamp;
	battle_time(): timestamp(0) {}
	void BuildFromLuaAnyValue(const LuaAnyValue& v)
	{
#define FETCH_VALUE(value)  \
int value = v[#value];
		FETCH_VALUE(year)
		FETCH_VALUE(month)
		FETCH_VALUE(mday)
		FETCH_VALUE(hour)
		FETCH_VALUE(min)
		FETCH_VALUE(sec)
#undef FETCH_VALUE

		struct tm t;
		t.tm_year = year - 1900;
		t.tm_mon = month - 1;
		t.tm_mday = mday;
		t.tm_hour = hour;
		t.tm_min = min;
		t.tm_sec = sec;
		t.tm_isdst = -1;
		printf("EliminateBattleManager::BuildFromLuaAnyValue::year=%d:month=%d:mday=%d:hour=%d:min=%d:sec=%d\n", year, month, mday, hour, min, sec);
		timestamp = mktime(&t);

	}
};

void guess_config_t::BuildFromLuaAnyValue(const LuaAnyValue& v)
{
	int local_state = v["state"];
	int local_cost = v["cost_money"];
	state           = (PB::ELIMINATE_BATTLE_STATE)local_state;
	max_guess_count = (int)(v["max_guess_count"]);
	cost_money      = local_cost;
	right_award     = v["right_award"];
	wrong_award     = v["wrong_award"];

	printf("guess_config_t::BuildFromLuaAnyValue::state=%d:max_guess_count=%d:cost_money=%ld:right_award=%d:wrong_award=%d\n",
	       (int)state, max_guess_count, cost_money, right_award, wrong_award);
}
bool EliminateBattleManager::Initialize()
{
	std::string path("eliminate_battle_config.lua");
	lua_State *L = luaL_newstate();
	if (NULL == L)
	{
		return false;
	}

	luaL_openlibs(L);
	if (luaL_dofile(L, path.c_str()))
	{
		printf("DS::ELiminateBattleManager::Initialize syntax error in script.\n");
		lua_close(L);
		return false;
	}

	LuaTableNode root;
	LuaConfReader::parseFromState(L, "root", root);
	std::map<int, battle_time> tmp;
	root["battle_times"].Fetch(tmp);
	for (auto it = tmp.begin(), eit = tmp.end(); it != eit; ++it)
	{
		printf("ELiminateBattleManager::Initialize id=%d timestamp=%d\n", it->first, it->second.timestamp);
		m_battle_times.push_back(it->second.timestamp);
	}

	if (m_battle_times.size() != PB::EBS_END - 2)
	{
		printf("ELiminateBattleManager::Initialize size failed. size=%zu", m_battle_times.size());
		lua_close(L);
		return false;
	}

	for (auto it = m_eliminate_battle_interval.begin(), ie = m_eliminate_battle_interval.end(); it != ie; it++)
	{
		LOG_TRACE("ELiminateBattleManager::Initialize:battle_interval:state=%d:interval=%d\n", it->first, it->second);
	}

	root["guess_config"].Fetch(m_guess_config);

	IntervalTimer::Attach(this, (ELIMINATE_BATTLE_MANAGER_UPDATE_INTERVAL * 1000000) / IntervalTimer::Resolution());

	lua_close(L);

	return true;

}

bool EliminateBattleManager::Update()
{
	if (!CenterManager::GetInstance().IsCenter())
	{
		TryGetState();
		TryCheckProf();
		return true;
	}
	InitState();
	if (!m_init)
	{
		return true;
	}

	CheckState();
	return true;
}

void EliminateBattleManager::SetState(PB::ELIMINATE_BATTLE_STATE state)
{
	m_state = state;

	//TODO:处理全局变量
	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (ipd)
	{
		ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_ELIMINATE_BATTLE_STATE), (int)m_state);
	}

	NotifyState(0, true);

	LOG_TRACE("EliminateBattleManager::SetState:state=%d", (int)state);
	if (m_state == PB::EBS_CENTER_KNOCKOUT_BATTLE_2_END)
	{
		GET_CENTER_BATTLE(centerbattle, CBT_ARENA_ELIMINATE)
		if (centerbattle->GetCenterZoneID() == g_zoneid)
		{
			auto *pCorpsBattleEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(GNET::CORPS_BATTLE_STUB_TYPE_ELIMINATE);
			if (pCorpsBattleEntry)
			{
				pCorpsBattleEntry->OnFinalsEnd();
			}
		}
		GET_CENTER_BATTLE_END
	}
	if (m_state == PB::EBS_ENROLL_BEGIN)
	{
		GET_CENTER_BATTLE(centerbattle, CBT_ARENA_ELIMINATE)
		if (centerbattle->GetCenterZoneID() == g_zoneid)
		{
			auto *pCorpsBattleEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(GNET::CORPS_BATTLE_STUB_TYPE_ELIMINATE);
			if (pCorpsBattleEntry )
			{
				pCorpsBattleEntry->ClearBattleInfo();
			}
		}
		GET_CENTER_BATTLE_END
	}
}

void EliminateBattleManager::TryGetState()
{
	if (m_get_state)
	{
		return;
	}
	m_time_counter += 1;
	if (m_time_counter % 10 != 0)
	{
		return;
	}

	int zoneid = 0;
	PB::ipt_eliminate_battle_notify notify;
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_GET_STATE);
	notify.set_zoneid(g_zoneid);
	GET_CENTER_BATTLE(centerbattle, CBT_ARENA_ELIMINATE)
	zoneid = centerbattle->GetCenterZoneID();
	LOG_TRACE("EliminateBattleManager::TryGetState:center_zoneid=%d", zoneid);
	CenterManager::GetInstance().SendMessage2Hub(notify, 0, centerbattle->GetServiceName(), zoneid);
	GET_CENTER_BATTLE_END
}

int EliminateBattleManager::GetMaxGuessCountByState(PB::ELIMINATE_BATTLE_STATE state) const
{
	auto it = m_guess_config.find((int)state);
	if (it != m_guess_config.end())
	{
		return it->second.max_guess_count;
	}
	return 0;
}

int EliminateBattleManager::GetGuessAwardByState(PB::ELIMINATE_BATTLE_STATE state, bool guess_right) const
{
	auto it = m_guess_config.find((int)state);
	if (it != m_guess_config.end())
	{
		if (guess_right)
		{
			return it->second.right_award;
		}
		else
		{
			return it->second.wrong_award;
		}
	}
	return 0;
}

int EliminateBattleManager::CheckEliminateGuessVaild(PB::ELIMINATE_BATTLE_STATE state, int64_t cost_money) const
{
	LOG_TRACE("EliminateBattleManager::CheckEliminateGuessVaild:state=%d:cost_money=%ld", (int)state, cost_money);

	//检查是否是可以投注的状态
	auto it = m_guess_config.find((int)state);
	if (it == m_guess_config.end())
	{
		LOG_TRACE("EliminateBattleManager::CheckEliminateGuessVaild:state=%d:cost_money=%ld:ERROR_ELIMINATE_GUESS_WRONG_STATE", (int)state, cost_money);
		return GNET::ERROR_ELIMINATE_GUESS_WRONG_STATE;
	}

	//只能在开展前的state竞猜
	if ((int)state != m_state + 1)
	{
		LOG_TRACE("EliminateBattleManager::CheckEliminateGuessVaild:state=%d:cost_money=%ld:cur_state=%d:ERROR_ELIMINATE_GUESS_WRONG_STATE",
		          (int)state, cost_money, (int)m_state);
		return GNET::ERROR_ELIMINATE_GUESS_WRONG_STATE;
	}
	int now_time = Timer::GetTime();
	int battle_time = GetBattleTime(state);
	//开战前1小时，不能竞猜
	if (now_time >= battle_time - 3600)
	{
		LOG_TRACE("EliminateBattleManager::CheckEliminateGuessVaild:state=%d:cost_money=%ld:cur_state=%d:now_time=%d:battle_time=%d:ERROR_ELIMINATE_GUESS_WRONG_TIME",
		          (int)state, cost_money, (int)m_state, now_time, battle_time);
		return GNET::ERROR_ELIMINATE_GUESS_WRONG_TIME;
	}
	//检查金钱合法性
	if (cost_money != it->second.cost_money)
	{
		LOG_TRACE("EliminateBattleManager::CheckEliminateGuessVaild:state=%d:cost_money=%ld:cur_state=%d:cost_money=%ld:config_cost_money=%ld:ERROR_ELIMINATE_GUESS_WRONG_MONEY",
		          (int)state, cost_money, (int)m_state, cost_money, it->second.cost_money);
		return GNET::ERROR_ELIMINATE_GUESS_WRONG_MONEY;
	}

	return 0;
}
void BattleProfChecker::Run()
{
	int now_time = Timer::GetTime();
	LOG_TRACE("BattleProfChecker::Run::now_time=%d", now_time);

	if (EliminateBattleManager::GetInstance().InKnockoutBattle())
	{
		EliminateBattleManager::GetInstance().ClearCheckProf();
		delete this;
		return;
	}
	else
	{
		auto *pCorpsBattleEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(GNET::CORPS_BATTLE_STUB_TYPE_ELIMINATE);
		if (!pCorpsBattleEntry)
		{
			EliminateBattleManager::GetInstance().ClearCheckProf();
			delete this;
			return;
		}

		pCorpsBattleEntry->CheckFighterProf();

		IntervalTimer::Schedule(this, 60 * 1000000 / IntervalTimer::Resolution());
	}
}
void EliminateBattleManager::TryCheckProf()
{
	if (!IsBattleEndState())
	{
		return;
	}
	if (m_check_prof)
	{
		return;
	}
	int now_time = Timer::GetTime();
	int next_time = GetBattleTime((PB::ELIMINATE_BATTLE_STATE)((int)m_state + 1));
	if (next_time - now_time > 600)
	{
		return;
	}
	m_check_prof = true;

	IntervalTimer::Schedule(new BattleProfChecker(), 60 * 1000000 / IntervalTimer::Resolution());
	LOG_TRACE("EliminateBattleManager::TryCheckProf");
}
void EliminateBattleManager::SetLocalState(char state, bool is_set)
{
	LOG_TRACE("EliminateBattleManager::SetLocalState::state=%d:is_set=%s", state, is_set ? "true" : "false");
	m_state = (PB::ELIMINATE_BATTLE_STATE)state;
	m_get_state = true;

	if (is_set && m_state == PB::EBS_NOT_BEGIN)
	{
		ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().ClearAllGrade(GLOBAL_CONFIG.eliminate_group_type);
	}
	else if (is_set && m_state == PB::EBS_ENROLL_BEGIN)
	{
		diaoxiang_manager::GetInstance().ClearDiaoxiangIndexMap();
	}
	else if (is_set && m_state == PB::EBS_LOCAL_SCORE_BATTLE_END)
	{
		ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().LocalScoreEnd();
	}
	/*
	else if (is_set && m_state == PB::EBS_CENTER_KNOCKOUT_BATTLE_2_END)
	{
		auto *pCorpsBattleEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(GNET::CORPS_BATTLE_STUB_TYPE_ELIMINATE);
		if (pCorpsBattleEntry)
		{
			pCorpsBattleEntry->OnFinalsEnd();
		}
	}
	*/
}
void EliminateBattleManager::CheckState()
{
	int now_time = GetNowTime();
	LOG_TRACE("EliminateBattleManager::CheckState::m_state=%d:now_time=%d", (int)m_state, now_time);
	if (now_time < m_battle_times[0])
	{
		if (m_state != PB::EBS_NOT_BEGIN)
		{
			LOG_TRACE("EliminateBattleManager::CheckState::m_state=%d:now_time=%d:m_battle_time=%d",
			          (int)m_state, now_time, m_battle_times[0]);
			SetState(PB::EBS_NOT_BEGIN);
		}
	}
	else
	{
		for (int i = 0; i < (int)PB::EBS_END - 2; ++i)
		{
			if (now_time >= m_battle_times[i] && now_time < m_battle_times[i + 1])
			{
				if (m_state != (PB::ELIMINATE_BATTLE_STATE)(i + 2))
				{
					LOG_TRACE("EliminateBattleManager::CheckState::m_state=%d:now_time=%d:index=%d:m_battle_time_begin=%d:m_battle_time_end=%d",
					          (int)m_state, now_time, i, m_battle_times[i], m_battle_times[i + 1]);
					SetState((PB::ELIMINATE_BATTLE_STATE)(i + 2));
				}
				return ;
			}
		}
		if (m_state != PB::EBS_NOT_BEGIN)
		{
			LOG_TRACE("EliminateBattleManager::CheckState::m_state=%d:now_time=%d:Else", (int)m_state, now_time);
			SetState(PB::EBS_NOT_BEGIN);
		}
	}
}

void EliminateBattleManager::InitState()
{
	if (m_init)
	{
		return;
	}

	if (!GameDBClient::GetInstance()->IsConnect())
	{
		LOG_TRACE("EliminateBattleManager::InitState::GameDBClient::IsNotConnect");
		return;
	}
	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		CheckState();
		LOG_TRACE("EliminateBattleManager::InitState::GameDBClient::interprocess_data::NotFound");
	}
	else
	{
		if (!ipd->ip_global_data_manager.IsInit())
		{
			LOG_TRACE("EliminateBattleManager::InitState::GameDBClient::global_data::NotInit");
			return;
		}
		int value = 0;
		if (ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_ELIMINATE_BATTLE_STATE), value))
		{
			m_state = (PB::ELIMINATE_BATTLE_STATE)value;
		}
		else
		{
			m_state = PB::EBS_NOT_BEGIN;
			ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_ELIMINATE_BATTLE_STATE), (int)m_state);
		}
	}
	m_init = true;
	NotifyState(0, false);
	LOG_TRACE("EliminateBattleManager::InitState:state=%d", (int)m_state);
}
void EliminateBattleManager::NotifyState(zone_id_t zoneid, bool set)
{
	if (!m_init)
	{
		return;
	}
	auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
	if (!pCenterBattle)
	{
		return;
	}
	PB::ipt_eliminate_battle_notify notify;
	notify.set_zoneid(pCenterBattle->GetCenterZoneID());
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_STATE);
	notify.set_eliminate_battle_state(m_state);
	notify.set_eliminate_battle_state_set((int)set);

	LOG_TRACE("EliminateBattleManager::NotifyState::zoneid=%d:state=%d:set=%s", zoneid, (int)m_state, set ? "true" : "false");
	if (0 == zoneid)
	{
		CenterManager::GetInstance().BroadcastMessage(notify, true);
	}
	else
	{
		GET_CENTER_BATTLE(centerbattle, CBT_ARENA_ELIMINATE)
		CenterManager::GetInstance().SendMessage2Hub(notify, 0, centerbattle->GetServiceName(), zoneid);
		GET_CENTER_BATTLE_END
	}
}
int EliminateBattleManager::GetBattleTime(PB::ELIMINATE_BATTLE_STATE state) const
{
	if (state <= PB::EBS_NOT_BEGIN || state >= PB::EBS_END - 2)
	{
		return 0;
	}
	return m_battle_times[(int)state - 2];
}

int EliminateBattleManager::GetCurSeasonBeginTimestamp() const
{
	return m_battle_times[0];
}

int EliminateBattleManager::GetBattleInterval(PB::ELIMINATE_BATTLE_STATE state) const
{
	auto it = m_eliminate_battle_interval.find((int)state - 1);
	if (it == m_eliminate_battle_interval.end())
	{
		return 0;
	}
	return it->second;
}
