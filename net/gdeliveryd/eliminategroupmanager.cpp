#include "eliminategroupmanager.h"
#include "gdeliveryserver.hpp"
#include "gprotoc/npt_eliminate_group_info.pb.h"
#include "gprotoc/npt_eliminate_group_get_reward_re.pb.h"
#include "gprotoc/db_eliminate_group_ranks.pb.h"
#include "gprotoc/db_eliminate_group_info.pb.h"
#include "gprotoc/ipt_eliminate_battle_notify.pb.h"
#include "gprotoc/ELIMINATE_BATTLE_STATE.pb.h"
#include "gprotoc/npt_knockout_eliminate_battle_begin.pb.h"
#include "gprotoc/eliminate_group.pb.h"
#include "gprotoc/npt_eliminate_group_get_reward.pb.h"
#include "gprotoc/npt_eliminate_group_invite.pb.h"
#include "gprotoc/eliminate_group_player.pb.h"
#include "gprotoc/ipt_eliminate_group_info.pb.h"
#include "gprotoc/ELIMINATE_ARENA_STAGE.pb.h"
#include "gprotoc/npt_knockout_eliminate_battle_prof.pb.h"
#include "gprotoc/corps_battle_info.pb.h"

#include "gamedbclient.hpp"
#include "gproviderserver.hpp"
#include "glog.h"
#include "func_info.h"
#include "global_config.h"
#include "eliminategroupcreate_re.hpp"
#include "eliminategroupinfo.hpp"
#include "eliminategroupgradeinfo.hpp"
#include "eliminategroupmemberonline.hpp"
#include "eliminategroupinvite_re.hpp"
#include "eliminategroupquit_re.hpp"
#include "eliminategroupabdicate_re.hpp"
#include "eliminategroupkick_re.hpp"
#include "dbeliminategroupload.hrp"
#include "dbeliminategroupsave.hrp"
#include "dbeliminategroupget.hrp"
#include "releasename.hrp"
#include "uniquenameclient.hpp"

#include "commoninvite.hrp"
#include "tpmanager.h"
#include "eliminatebattlemanager.h"
#include "corps_battle_man.h"
#include "corps_battle_base.h"
#include "arenamanager.h"
#include "arenaeliminatebattle.h"
#include "diaoxiang_manager.h"
#include "centerbattle.h"

namespace GNET
{
namespace ELIMINATE_GROUP_MANAGER
{
static void DumpGroup(const EliminateGroup& group)
{
	auto group_id = group.GetGroupID();
	auto leader = group.GetGroupLeader();
	auto grade = group.GetGroupGrade();
	int group_type = group.GetGroupType();
	group.ForEachMember([group_id, leader, grade, group_type](const GEliminateGroupPlayer & member)
	{
		LOG_TRACE("EliminateGroup::id=%ld:leader=" PRINT64":group_grade=%d:member=" PRINT64":member_reward=%zu:group_type=%d", group_id, leader, grade, member.id, member.reward.size(), group_type);
	});
}
template<typename PROTOCOL>
void EliminateGroup::SendResult(ruid_t roleid, int retcode, unsigned int localsid, unsigned int linksid)
{
	PROTOCOL re;
	re.roleid = roleid;
	re.retcode = retcode;
	re.localsid = localsid;
	GDeliveryServer::GetInstance()->Send(linksid, re);
	LOG_TRACE("EliminateGroup::SendResult::protocol=%d:roleid=" PRINT64":retcode=%d", re.GetType(), roleid, retcode);
}

void EliminateGroup::MemberChangeLog(MEMBER_CHANGE_TYPE mct, const RoleInfo *pMember, const RoleInfo *pLeader, bool use_leader_bi) const
{
	if (!pMember || !pLeader)
	{
		return;
	}
	const RoleInfo *pBiInfo = pMember;
	if (use_leader_bi)
	{
		pBiInfo = pLeader;
	}
	//for BI
	BI_LOG_GLOBAL(pBiInfo->account);
	// 复用队长的bi信息,但是封装好的宏里使用的bi.account,所以这里修正bi.account为队员的account
	bi.account = pMember->account.tostr();
	SLOG(FORMAT, "eliminate_group_member")
	.BI_HEADER3_DS(pMember)
	.P("leader_openid", GetOpenid(pLeader->account))
	.P("leader_roleid", pLeader->roleid)
	.P("eliminategroup_id", m_eliminate_group.info.id)
	.P("change_type", mct)
	.P("group_type", GetGroupType());
}

void LoadMemberRoleInfoForLog(int retcode, int64_t roleid, const RoleInfo *pinfo, const EliminateGroup *pAg, MEMBER_CHANGE_TYPE mct, const RoleInfo *pMember, const RoleInfo *pLeader, bool is_leader, bool use_leader_bi, ruid_t member_rid, ruid_t leader_rid)
{
	if (retcode != 0 || !pinfo)
	{
		return;
	}

	if (is_leader)
	{
		pLeader = pinfo;
	}
	else
	{
		pMember = pinfo;
	}
	if (!pMember || !pLeader)
	{
		if (!pLeader)
		{
			getroleinfo_handle call = std::bind(&LoadMemberRoleInfoForLog, std::placeholders::_1,
			                                    std::placeholders::_2, std::placeholders::_3, pAg, mct, pMember, pLeader, true, use_leader_bi, member_rid, leader_rid);
			RoleMap::GetRoleInfo(leader_rid, Octets(), &call);
		}
		else
		{
			getroleinfo_handle call = std::bind(&LoadMemberRoleInfoForLog, std::placeholders::_1,
			                                    std::placeholders::_2, std::placeholders::_3, pAg, mct, pMember, pLeader, false, use_leader_bi, member_rid, leader_rid);
			RoleMap::GetRoleInfo(member_rid, Octets(), &call);
		}
	}
	else
	{
		pAg->MemberChangeLog(mct, pMember, pLeader, use_leader_bi);
	}
}
bool EliminateGroup::InCenterScore() const
{
	if (GetFlag() == FLAG_CENTER_SCORE)
	{
		return true;
	}
	int top_id = GetEliminateGroupTopTid(GetGroupType());
	if (top_id <= 0)
	{
		return false;
	}

	int rank = TPManager::GetInstance().GetSelfRank(top_id, GetGroupID(), NULL);
	if (rank <= 0 || rank > EliminateGroupManager::GetInstance().GetMatchCenterToplistCount())
	{
		return false;
	}
	return true;
}
void EliminateGroup::LogMemberChange(ruid_t roleid, MEMBER_CHANGE_TYPE mct, bool use_leader_bi) const
{
	RoleInfo *ri = RoleMap::Instance().Find(roleid);
	RoleInfo *pLeader = RoleMap::Instance().Find(m_eliminate_group.leader);
	if (!ri || !pLeader)
	{
		bool load_leader = false;
		ruid_t load_roleid = 0;
		if (!ri)
		{
			load_leader = false;
			load_roleid = roleid;
		}
		else
		{
			load_leader = true;
			load_roleid = m_eliminate_group.leader;
		}
		getroleinfo_handle call = std::bind(&LoadMemberRoleInfoForLog, std::placeholders::_1,
		                                    std::placeholders::_2, std::placeholders::_3, this, mct, ri, pLeader, load_leader, use_leader_bi, roleid, m_eliminate_group.leader);
		RoleMap::GetRoleInfo(load_roleid, Octets(), &call);
	}
	else
	{
		MemberChangeLog(mct, ri, pLeader, use_leader_bi);
	}
}
void EliminateGroup::SetGEliminateGroup(const GEliminateGroup& eliminategroup)
{
	m_eliminate_group = eliminategroup;
	if (m_eliminate_group.pbinfo.value.size() > 0)
	{
		try
		{
			Octets2PB(m_eliminate_group.pbinfo.value, pbinfo);
		}
		catch (...)
		{
			LOG_TRACE("EliminateGroup::SetGEliminateGroup::group_id=%ld:loadpbFailed", GetGroupID());
		}
	}
	LOG_TRACE("EliminateGroup::SetGEliminateGroup::group_id=%ld:fighters=%d:pbinfo=%p:group_type=%d", GetGroupID(), pbinfo.fighters_size(), &pbinfo, GetGroupType());
}
void EliminateGroup::OnLogin(RoleInfo *ri)
{
	if (!ri)
	{
		return;
	}
	ruid_t cur_group_id = ri->friends.GetEliminateGroupID();
	LOG_TRACE("EliminateGroup::OnLogin::roleid=" PRINT64":group_id=%ld:cur_group_id=%ld:group_type=%d", ri->roleid, GetGroupID(), cur_group_id, GetGroupType());
	m_active_timestamp = Timer::GetTime();
	++ m_online_num;

	if (cur_group_id != GetGroupID())
	{
		return;
	}

	//通知所有成员玩家上线
	EliminateGroupMemberOnline online;
	online.roleids.push_back(ri->roleid);
	online.online = 1;
	EliminateGroupMemberOnline online2;
	online2.online = 1;
	std::string str_members = "";
	ForEachMember([&online, &online2, this, ri, &str_members](GEliminateGroupPlayer & player)
	{
		LOG_TRACE("EliminateGroup::OnLogin::roleid=" PRINT64":group_id=%ld:member=" PRINT64":member_reward=%zu:group_type=%d", ri->roleid, this->GetGroupID(), player.id, player.reward.size(), this->GetGroupType());

		str_members += std::to_string(player.id) + ",";

		auto *pRole = RoleMap::Instance().FindOnline(player.id);
		if (pRole)
		{
			online2.roleids.push_back(player.id);
			online.localsid = pRole->localsid;
			GDeliveryServer::GetInstance()->Send(pRole->linksid, online);
		}
	});
	//通知上线玩家其余成员在线信息
	if (online2.roleids.size())
	{
		online2.localsid = ri->localsid;
		GDeliveryServer::GetInstance()->Send(ri->linksid, online2);
	}

	//通知玩家战队信息
	//发给客户端的
	DumpGroup(*this);
	EliminateGroupInfo info(ri->localsid, m_eliminate_group);
	GDeliveryServer::GetInstance()->Send(ri->linksid, info);
	LOG_TRACE("EliminateGroup::EliminateGroupInfo:roleid=" PRINT64":group_id=%ld:group_type=%d", ri->roleid, GetGroupID(), GetGroupType());
	PB::ipt_eliminate_group_info aginfo;
	aginfo.set_eliminate_group_type(GetGroupType());
	if (GetGroupType() == EGT_5V5)
	{
		aginfo.set_eliminate_group_id(m_eliminate_group.info.id);
		aginfo.set_eliminate_group_grade(m_eliminate_group.group_grade);
	}
	else if (GetGroupType() == EGT_3V3)
	{
		aginfo.set_eliminate_group_2_id(m_eliminate_group.info.id);
		aginfo.set_eliminate_group_2_grade(m_eliminate_group.group_grade);
	}
	else if (GetGroupType() == EGT_2V2)
	{
		aginfo.set_eliminate_group_3_id(m_eliminate_group.info.id);
		aginfo.set_eliminate_group_3_grade(m_eliminate_group.group_grade);
	}
	ri->SendMessage2GS(aginfo);

	const Octets uname = GetGroupName();
	Octets utf8_name;
	CharsetConverter::conv_charset_u2t(uname, utf8_name);

	//for BI
	BI_LOG_GLOBAL(ri->account);
	SLOG(FORMAT, "eliminate_team_info")
	.BI_HEADER2_DS(ri)
	.P("eliminate_group_id", m_eliminate_group.info.id)
	.P("name", utf8_name)
	.P("group_type", GetGroupType())
	.P("members", str_members.c_str());

	if (ri->SNSReady())
	{
		ri->friends.SendTitleUpdate(TITLE_INDEX_ELIMINATE_GROUP);
	}
}

void EliminateGroup::OnLogout(ruid_t roleid)
{
	RoleInfo *ri = RoleMap::Instance().FindOnline(roleid);
	if (!ri)
	{
		return;
	}
	ruid_t cur_group_id = ri->friends.GetEliminateGroupID();
	LOG_TRACE("EliminateGroup::OnLogout::roleid=" PRINT64":group_id=" PRINT64":cur_group_id=%ld:group_type=%d", roleid, GetGroupID(), cur_group_id, GetGroupType());
	//m_active_timestamp = Timer::GetTime();
	-- m_online_num;

	if (cur_group_id != GetGroupID())
	{
		return;
	}

	//通知所有成员玩家离线
	EliminateGroupMemberOnline online;
	online.roleids.push_back(roleid);
	online.online = 0;
	ForEachMember([&online, roleid](GEliminateGroupPlayer & member)
	{
		if (member.id != roleid)
		{
			auto *pInfo = RoleMap::Instance().FindOnline(member.id);
			if (pInfo)
			{
				online.localsid = pInfo->localsid;
				GDeliveryServer::GetInstance()->Send(pInfo->linksid, online);
			}
		}
	});
}

void EliminateGroup::DebugSetActiveTimestampOffset(int offset)
{
	m_active_timestamp += offset;

	LOG_TRACE("DS::EliminateGroup::DebugSetActiveTimestampOffset offset=%d m_active_timestamp=%d:group_type=%d", offset, m_active_timestamp, GetGroupType());
}

void EliminateGroup::DebugSetGrade(int grade)
{
	LOG_TRACE("EliminateGroup::DebugSetGrade::group_id=" PRINT64":grade=%d:group_type=%d", GetGroupID(), grade, GetGroupType());
	m_active_timestamp = Timer::GetTime();
	m_eliminate_group.group_grade = grade;
	if (m_eliminate_group.group_grade < 0)
	{
		m_eliminate_group.group_grade = 0;
	}
	m_eliminate_group.last_battle_timestamp = m_active_timestamp;
	EliminateGroupManager::GetInstance().UpdateTopList(GetGroupID(), GetGroupGrade(), GetGroupName());
	SetDirty(true);

	NotifyGSPlayerGrade();
}
void EliminateGroup::OnIDIPRename(Octets& name)
{
	LOG_TRACE("EliminateGroup::OnIDIPRename id=%ld name_size=%zu", GetGroupID(), name.size());

	m_eliminate_group.info.name = name;
	SetDirty(true);

	GEliminateGroupPlayerVector::iterator pit = m_eliminate_group.members.begin(), peit = m_eliminate_group.members.end();
	for ( ; pit != peit; ++pit)
	{
		RoleInfo *pRole = RoleMap::Instance().FindOnline((*pit).id);
		if (pRole)
		{
			EliminateGroupInfo info(pRole->localsid, m_eliminate_group);
			GDeliveryServer::GetInstance()->Send(pRole->linksid, info);
		}
	}

	//刷新本服排行榜
	EliminateGroupManager::GetInstance().UpdateTopList(GetGroupID(), GetGroupGrade(), GetGroupName());

	//通知中心服刷新
	GET_CENTER_BATTLE(centerbattle, CBT_ARENA_ELIMINATE)
	int zoneid = centerbattle->GetCenterZoneID();
	PB::ipt_eliminate_battle_notify notify;
	notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_IDIP_RENAME);
	notify.set_zoneid(g_zoneid);
	PB::corps_battle_info *pInfo = notify.add_eliminate_battle_infos();
	pInfo->set_id(GetGroupID());
	pInfo->set_name(std::string((const char *)name.begin(), name.size()));
	CenterManager::GetInstance().SendMessage2Hub(notify, 0, centerbattle->GetServiceName(), zoneid);
	GET_CENTER_BATTLE_END
}
void EliminateGroup::OnSetGrade(ruid_t roleid, int grade)
{
	LOG_TRACE("EliminateGroup::OnSetGrade::roleid=" PRINT64":grade=%d:group_id=%ld:group_type=%d", roleid, grade, GetGroupID(), GetGroupType());
	m_active_timestamp = Timer::GetTime();

	EliminateGroupGradeInfo info;
	info.roleid = roleid;
	if (!roleid)
	{
		m_eliminate_group.group_grade = grade;
		if (m_eliminate_group.group_grade < 0)
		{
			m_eliminate_group.group_grade = 0;
		}
		info.grade = m_eliminate_group.group_grade;
	}

	EliminateGroupManager::GetInstance().UpdateTopList(GetGroupID(), GetGroupGrade(), GetGroupName());

	SetDirty(true);

	NotifyGSPlayerGrade();
}

void EliminateGroup::OnGradeChange(int battle_result, int grade)
{
	LOG_TRACE("EliminateGroup::OnGradeChange id=%ld battle_result=%d grade_change=%d grade=%d:group_type=%d", m_eliminate_group.info.id, battle_result, grade, m_eliminate_group.group_grade, GetGroupType());

	m_active_timestamp = Timer::GetTime();

	m_eliminate_group.group_grade += grade;
	if (m_eliminate_group.group_grade < 0)
	{
		m_eliminate_group.group_grade = 0;
	}
	m_eliminate_group.last_battle_timestamp = m_active_timestamp;

	EliminateGroupManager::GetInstance().UpdateTopList(GetGroupID(), GetGroupGrade(), GetGroupName());
	SetDirty(true);

	NotifyGSPlayerGrade();
}
void EliminateGroup::OnSetChange(int grade)
{
	LOG_TRACE("EliminateGroup::OnSetChange::id=%ld:origin_grade=%d:new_grade=%d:group_type=%d", GetGroupID(), GetGroupGrade(), grade, GetGroupType());
	m_active_timestamp = Timer::GetTime();

	m_eliminate_group.group_grade = grade;
	if (m_eliminate_group.group_grade < 0)
	{
		m_eliminate_group.group_grade = 0;
	}
	m_eliminate_group.last_battle_timestamp = m_active_timestamp;

	EliminateGroupManager::GetInstance().UpdateTopList(GetGroupID(), GetGroupGrade(), GetGroupName());
	SetDirty(true);

	NotifyGSPlayerGrade();
}
void EliminateGroup::OnRewardChange(int stage, int reward)
{
	LOG_TRACE("EliminateGroup::OnRewardChange:id=%ld:stage=%d:reward=%d:group_type=%d", GetGroupID(), stage, reward, GetGroupType());
	ForEachMember([reward, stage](GEliminateGroupPlayer & player)
	{
		for (size_t j = player.reward.size(); j <= (size_t)stage; ++j)
		{
			player.reward.push_back(PB::eliminate_group_player::EAR_TYPE_NONE);
		}
		if (player.reward[stage] >= 0)
		{
			player.reward[stage] = reward;
		}
	});
	SetDirty(true);
}
void EliminateGroup::OnGenerateChampion()
{
	LOG_TRACE("EliminateGroup::OnGenerateChampion:id=%ld:group_type=%d", GetGroupID(), GetGroupType());
	std::vector<ruid_t> roleid_list;
	ForEachMember([&roleid_list, this](GEliminateGroupPlayer & player)
	{
		LOG_TRACE("EliminateGroup::OnGenerateChampion:id=%ld:roleid=" PRINT64":group_type=%d", this->GetGroupID(), player.id, this->GetGroupType());
		roleid_list.push_back(player.id);
	});
	diaoxiang_manager::GetInstance().BeginCollectEliminateDiaoxiang(roleid_list);
}
void EliminateGroup::NotifyGSPlayerGrade()
{
	EliminateGroupGradeInfo info;
	info.roleid = 0;
	info.grade = m_eliminate_group.group_grade;

	std::stringstream ss;
	ss << "(";
	ForEachMember([&ss, &info, this](const GEliminateGroupPlayer & member)
	{
		ss << member.id <<  ",";
		RoleInfo *pInfo = RoleMap::Instance().FindOnline(member.id);
		if (pInfo)
		{
			info.localsid = pInfo->localsid;
			GDeliveryServer::GetInstance()->Send(pInfo->linksid, info);
			PB::ipt_eliminate_group_info groupinfo;
			groupinfo.set_eliminate_group_type(GetGroupType());
			if (GetGroupType() == EGT_5V5)
			{
				groupinfo.set_eliminate_group_id(this->GetGroupID());
				groupinfo.set_eliminate_group_grade(this->GetGroupGrade());
			}
			else if (GetGroupType() == EGT_3V3)
			{
				groupinfo.set_eliminate_group_2_id(this->GetGroupID());
				groupinfo.set_eliminate_group_2_grade(this->GetGroupGrade());
			}
			else if (GetGroupType() == EGT_2V2)
			{
				groupinfo.set_eliminate_group_3_id(this->GetGroupID());
				groupinfo.set_eliminate_group_3_grade(this->GetGroupGrade());
			}
			pInfo->SendMessage2GS(groupinfo);
			if (pInfo->SNSReady())
			{
				pInfo->friends.SendTitleUpdate(TITLE_INDEX_ELIMINATE_GROUP);
			}
		}
	});
	ss << ")";

	LOG_TRACE("EliminateGroup::NotifyGSPlayerGrade id=%ld grade=%d members=%s group_type=%d", m_eliminate_group.info.id, m_eliminate_group.group_grade, ss.str().c_str(), GetGroupType());
}
void EliminateGroup::OnNotifyKnockoutBegin(ruid_t roleid)
{
	LOG_TRACE("EliminateGroup::OnNotifyKnockoutBegin::group_id=%ld:roleid=" PRINT64":fighters_size=%d:group_type=%d", GetGroupID(), roleid, pbinfo.fighters_size(), GetGroupType());

	if (!GET_FUNC_SWITCH(kFuncCodeEliminateCenterKnockout))
	{
		return;
	}

	PB::npt_knockout_eliminate_battle_begin begin;
	for (int i = 0; i < pbinfo.fighters_size(); i++)
	{
		if (roleid != 0 && pbinfo.fighters(i).roleid() != roleid)
		{
			continue;
		}

		RoleInfo *pInfo = RoleMap::Instance().FindOnline(pbinfo.fighters(i).roleid());

		if (!pInfo || !pInfo->SNSReady() || pInfo->friends.GetEliminateGroupID() != GetGroupID())
		{
			continue;
		}

		pInfo->SendMessage2Client(begin);
		LOG_TRACE("EliminateGroup::OnNotifyKnockoutBegin::group_id=%ld:fighter=" PRINT64":group_type=%d", GetGroupID(), pInfo->roleid, GetGroupType());
	}
}
bool EliminateGroup::OnCheckFighter(ruid_t roleid, int profession) const
{
	for (int i = 0; i < pbinfo.fighters_size(); i++)
	{
		if (pbinfo.fighters(i).roleid() != roleid)
		{
			continue;
		}
		if (pbinfo.fighters(i).prof() != profession)
		{
			LOG_TRACE("EliminateGroup::OnCheckFighter::roleid=" PRINT64":group_id=%ld:profession=%d:record_prof=%d:group_type=%d", roleid, GetGroupID(), profession, pbinfo.fighters(i).prof(), GetGroupType());
			return false;
		}
		return true;
	}
	LOG_TRACE("EliminateGroup::OnCheckFighter::roleid=" PRINT64":group_id=%ld:profession=%d:group_type=%d:IsNotFighter", roleid, GetGroupID(), profession, GetGroupType());
	return false;
}
void EliminateGroup::OnCheckFighterProf() const
{
	for (int i = 0; i < pbinfo.fighters_size(); i++)
	{
		ruid_t roleid = pbinfo.fighters(i).roleid();
		int origin_prof = pbinfo.fighters(i).prof();
		auto pInfo = RoleMap::Instance().FindOnline(roleid);
		if (!pInfo)
		{
			continue;
		}
		if (pInfo->profession == origin_prof)
		{
			continue;
		}
		PB::npt_knockout_eliminate_battle_prof proto;
		proto.set_origin_prof(origin_prof);
		proto.set_curr_prof(pInfo->profession);
		pInfo->SendMessage2Client(proto);
	}
}
void EliminateGroup::OnSelectFighter(RoleInfo *pInfo, const std::vector<ruid_t>& fighters)
{
	LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:fighters=%zu:group_type=%d", pInfo->roleid, GetGroupID(), fighters.size(), GetGroupType());

	if (EliminateBattleManager::GetInstance().InKnockoutBattle() && !EliminateGroupManager::GetInstance().IsDebugMode())
	{
		LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:state=%d:group_type=%d", pInfo->roleid, GetGroupID(), EliminateBattleManager::GetInstance().GetState(), GetGroupType());
		pInfo->SendServerMessage(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_FIGHTER_TIME);
		return;
	}

	if (fighters.size() > EliminateGroupManager::GetInstance().GetMaxMatchMemberCount())
	{
		LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:group_type=%d:SizeInValid", pInfo->roleid, GetGroupID(), GetGroupType());
		pInfo->SendServerMessage(-1);
		return;
	}

	if (pInfo->roleid != GetGroupLeader())
	{
		LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:real_leader=" PRINT64":group_type=%d", pInfo->roleid, GetGroupID(), GetGroupLeader(), GetGroupType());
		pInfo->SendServerMessage(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_SELECT_FIGHTER_NO_LEADER);
		return;
	}

	std::map<int, int> prof_count;
	PB::db_eliminate_group_info pb;

	for (size_t i = 0; i < fighters.size(); i++)
	{
		if (!IsPlayerExist(fighters[i]))
		{
			LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:member=" PRINT64":group_type=%d:IsNotPlayerExist", pInfo->roleid, GetGroupID(), fighters[i], GetGroupType());
			return;
		}
		auto *pMember = RoleMap::Instance().Find(fighters[i]);
		if (!pMember)
		{
			LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:member=" PRINT64":group_type=%d:RoleNotFound", pInfo->roleid, GetGroupID(), fighters[i], GetGroupType());
			pInfo->SendServerMessage(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_MEMBER_OFFLINE);
			return;
		}
		int prof = pMember->profession;
		if (GET_FUNC_SWITCH(kFuncCodeEliminateBattleProf9))
		{
			//功能码开启的情况下，禁止职业9参加
			if (prof == PROFTYPE_PLAYER_9)
			{
				LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:member=" PRINT64":prof=%d:group_type=%d:Prof9Forbid", pInfo->roleid, GetGroupID(), fighters[i], pMember->profession, GetGroupType());
				pInfo->SendServerMessage(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF);
				return;
			}
		}
		if (GET_FUNC_SWITCH(kFuncCodeEliminateBattleProf16Limit))
		{
			//功能码开启的情况下，禁止职业16参加
			if (prof == PROFTYPE_16)
			{
				LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:member=" PRINT64":prof=%d:group_type=%d:ProfForbid", pInfo->roleid, GetGroupID(), fighters[i], prof, GetGroupType());
				pInfo->SendServerMessage(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF_16);
				return;
			}
		}
		if (GET_FUNC_SWITCH(kFuncCodeEliminateBattleProf18Limit))
		{
			//功能码开启的情况下，禁止职业18参加
			if (prof == PROFTYPE_18)
			{
				LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:member=" PRINT64":prof=%d:group_type=%d:ProfForbid", pInfo->roleid, GetGroupID(), fighters[i], prof, GetGroupType());
				pInfo->SendServerMessage(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF_18);
				return;
			}
		}
		auto it = prof_count.find(pMember->profession);
		if (it != prof_count.end() && it->second >= EliminateGroupManager::GetInstance().GetMaxProfCount())
		{
			LOG_TRACE("EliminateGroup::OnSelectFighter::roleid=" PRINT64":group_id=%ld:member=" PRINT64":prof=%d:group_type=%d:RoleNotFound", pInfo->roleid, GetGroupID(), fighters[i], pMember->profession, GetGroupType());
			pInfo->SendServerMessage(ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF);
			return;
		}
		else if (it != prof_count.end())
		{
			it->second ++;
		}
		else
		{
			prof_count.insert(std::make_pair(prof, 1));
		}
		auto pFighter = pb.add_fighters();
		pFighter->set_roleid(fighters[i]);
		pFighter->set_prof(prof);
	}
	//判断成功后更新到社团数据中
	pb.set_fighter_timestamp(Timer::GetTime());

	if (m_eliminate_group.pbinfo.value.size() == 0)
	{
		m_eliminate_group.pbinfo.type = PB::GUT_ELIMINATE_GROUP_INFO;
		m_eliminate_group.pbinfo.value = PB2Octets(pb);
		pbinfo.CopyFrom(pb);
	}
	else
	{
		PB::db_eliminate_group_info origin_pb;
		Octets2PB(m_eliminate_group.pbinfo.value, origin_pb);
		origin_pb.mutable_fighters()->CopyFrom(pb.fighters());
		origin_pb.set_fighter_timestamp(Timer::GetTime());
		m_eliminate_group.pbinfo.value = PB2Octets(origin_pb);
		pbinfo.CopyFrom(origin_pb);
	}
	SetDirty(true);
	EliminateGroupManager::GetInstance().SendEliminateGroupInfo(pInfo);
}
void EliminateGroup::OnGetReward(RoleInfo *pInfo, int stage)
{
	LOG_TRACE("EliminateGroup::OnGetReward::roleid=" PRINT64":group_id=%ld:stage=%d:group_type=%d", pInfo->roleid, GetGroupID(), stage, GetGroupType());
	if (stage < 0 || stage > EA_STAGE_KNOCK)
	{
		return;
	}
	bool ret_flag = false;
	ForEachMember([pInfo, stage, this, &ret_flag](GEliminateGroupPlayer & player)
	{
		if (ret_flag)
		{
			return;
		}
		if (player.id != pInfo->roleid)
		{
			return;
		}
		if ((size_t)stage >= player.reward.size())
		{
			LOG_TRACE("EliminateGroup::OnGetReward::roleid=" PRINT64":group_id=%ld:stage=%d:reward.size=%zu:group_type=%d", pInfo->roleid, this->GetGroupID(), stage, player.reward.size(), GetGroupType());
			ret_flag = true;
			return;
		}
		int reward = player.reward[stage];
		if (stage == EA_STAGE_KNOCK)
		{
			if (reward != -2 && EliminateBattleManager::GetInstance().GetState() == PB::EBS_REWARD_BEGIN)
			{
				auto pBattleInfo = CorpsBattleManager::GetInstance().GetCorpsBattleInfo(CORPS_BATTLE_STUB_TYPE_ELIMINATE, this->GetGroupID());
				if (!pBattleInfo)
				{
					LOG_TRACE("BATTLE_STATE_DEBUG::OnGetReward_NoBattleInfo:roleid=" PRINT64":group_id=%ld:stage=%d", pInfo->roleid, this->GetGroupID(), stage);
					ret_flag = true;
					return;
				}
				auto state = pBattleInfo->BattleState() - 3;
				auto rank = state / 3 + 1;
				reward = EliminateGroupManager::GetInstance().GetRankReward(rank);
				LOG_TRACE("BATTLE_STATE_DEBUG::OnGetReward_Calculate:roleid=" PRINT64":group_id=%ld:stage=%d:battle_state=%d:adjusted_state=%d:calculated_rank=%d:final_reward=%d",
				          pInfo->roleid, this->GetGroupID(), stage, pBattleInfo->BattleState(), state, rank, reward);
			}
			else
			{
				reward = 0;
				LOG_TRACE("BATTLE_STATE_DEBUG::OnGetReward_NoReward:roleid=" PRINT64":group_id=%ld:stage=%d:original_reward=%d:eliminate_state=%d",
				          pInfo->roleid, this->GetGroupID(), stage, player.reward[stage], (int)EliminateBattleManager::GetInstance().GetState());
			}
		}
		if (reward <= 0)
		{
			ret_flag = true;
			return;
		}
		pInfo->DeliverGeneralTask(reward);
		LOG_TRACE("EliminateGroup::OnGetReward::roleid=" PRINT64":group_id=%ld:stage=%d:reward=%d:group_type=%d", pInfo->roleid, GetGroupID(), stage, reward, GetGroupType());
		player.reward[stage] = -2;
		this->SetDirty(true);

		PB::npt_eliminate_group_get_reward_re re;
		re.set_ret(0);
		re.set_stage((PB::ELIMINATE_ARENA_STAGE)stage);
		pInfo->SendMessage2Client(re);
		ret_flag = true;
	});
}
void EliminateGroup::OnInvite(RoleInfo *ri, ruid_t target)
{
	LOG_TRACE("EliminateGroup::OnInvite::roleid=" PRINT64":target=" PRINT64":group_id=" PRINT64":group_type=%d", ri->roleid, target, GetGroupID(), GetGroupType());
	if (!ri->friends.IsReady() || target == ri->roleid)
	{
		return;
	}

	if (GetTotalMemberCount() >= EliminateGroupManager::GetInstance().GetMaxMemberCount())
	{
		SendResult<EliminateGroupInvite_Re>(target, ERROR_ELIMINATE_GROUP_MEMBER_MAX_COUNT, ri->localsid, ri->linksid);
		return;
	}

	RoleInfo *pRole = RoleMap::Instance().FindOnline(target);
	if (!pRole)
	{
		SendResult<EliminateGroupInvite_Re>(target, ERROR_ELIMINATE_GROUP_PLAYER_NOT_ONLINE, ri->localsid, ri->linksid);
		return;
	}

	if (pRole->IsInCooldown(COOLDOWN_ID_ELIMINATE_GROUP_INVITE, 1))
	{
		SendResult<EliminateGroupInvite_Re>(target, ERROR_ELIMINATE_GROUP_INVITE_COOLDOWN, ri->localsid, ri->linksid);
		return;
	}

	if (pRole->friends.GetEliminateGroupID())
	{
		SendResult<EliminateGroupInvite_Re>(target, ERROR_ELIMINATE_GROUP_TARGET_ALREADY_IN, ri->localsid, ri->linksid);
		return;
	}

	if (pRole->level < 70)
	{
		SendResult<EliminateGroupInvite_Re>(target, ERROR_ELIMINATE_GROUP_PLAYER_LEVEL_WRONG, ri->localsid, ri->linksid);
		return;
	}

	if (GLOBAL_CONFIG.eliminate_group_type == EGT_2V2)
	{
		/*if (ri->friends.GetFacebook().spouse != target || pRole->friends.GetFacebook().spouse != ri->roleid)
		{
			SendResult<EliminateGroupInvite_Re>(target, ERROR_ELIMINATE_GROUP_ARENA_BATTLE_2V2_MARRIAGE_LIMIT, ri->localsid, ri->linksid);
			return;
		}*/
		if (ri->gender == pRole->gender)
		{
			SendResult<EliminateGroupInvite_Re>(target, ERROR_ELIMINATE_GROUP_ARENA_BATTLE_2V2_MARRIAGE_LIMIT, ri->localsid, ri->linksid);
			return;
		}
	}

	PB::npt_eliminate_group_invite invite;
	invite.set_name(ri->GetShowName().begin(), ri->GetShowName().size());
	invite.mutable_eliminategroup()->set_id(ri->friends.GetEliminateGroupID());
	invite.mutable_eliminategroup()->set_name(ri->friends.GetEliminateGroupInfo().name());
	CommonInviteArg arg;
	arg.roleid = ri->roleid;
	arg.target = target;
	arg.isinvite = true;
	arg.localsid = pRole->localsid;
	arg.dtype = invite.type();
	arg.datainfo.resize(invite.ByteSize());
	if (!invite.SerializeWithCachedSizesToArray((uint8_t *)arg.datainfo.begin()))
	{
		return ;
	}
	pRole->SendInvite2Client(arg);

	pRole->SetCooldown(COOLDOWN_ID_ELIMINATE_GROUP_INVITE, COOLDOWN_ELIMINATE_GROUP_INVITE);
}

void EliminateGroup::OnQuit(RoleInfo *ri)
{
	LOG_TRACE("EliminateGroup::OnQuit::group_id=%ld:roleid=" PRINT64":leader=" PRINT64":member_size=%d:group_type=%d", GetGroupID(), ri->roleid, GetGroupLeader(), GetTotalMemberCount(), GetGroupType());
	if (ri->roleid == m_eliminate_group.leader && GetTotalMemberCount() != 1)
	{
		SendResult<EliminateGroupQuit_Re>(ri->roleid, ERROR_ELIMINATE_GROUP_LEADER_CANT_QUIT,  ri->localsid, ri->linksid);
		return;
	}

	bool found = IsPlayerExist(ri->roleid);

	if (found)
	{
		SetSaving(true);

		DBEliminateGroupSaveArg arg;
		arg.roleid = ri->roleid;
		GEliminateGroupSave ags;
		ags.eliminategroup.info.id = m_eliminate_group.info.id;
		if (GetTotalMemberCount() != 1)
		{
			ags.operation = AGST_QUIT;
		}
		else
		{
			ags.operation = AGST_DELETE;
		}
		arg.eliminategroups.push_back(ags);
		DBEliminateGroupSave *rpc = (DBEliminateGroupSave *) Rpc::Call(RPC_DBELIMINATEGROUPSAVE, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
	else
	{
		SendResult<EliminateGroupQuit_Re>(ri->roleid, ERROR_ELIMINATE_GROUP_NOT_IN_GROUP,  ri->localsid, ri->linksid);
	}
}

void EliminateGroup::OnAbdicate(RoleInfo *ri, ruid_t target)
{
	LOG_TRACE("EliminateGroup::OnAbdicate:roleid=" PRINT64":target=" PRINT64":group_id=%ld:group_type=%d", ri->roleid, target, GetGroupID(), GetGroupType());
	if (ri->roleid != m_eliminate_group.leader)
	{
		SendResult<EliminateGroupAbdicate_Re>(ri->roleid, ERROR_ELIMINATE_GROUP_NO_RIGHT,  ri->localsid, ri->linksid);
		return;
	}

	bool found = IsPlayerExist(target);

	if (found)
	{
		SetSaving(true);

		DBEliminateGroupSaveArg arg;
		arg.roleid = target;
		GEliminateGroupSave ags;
		ags.operation = AGST_ABDICATE;
		ags.eliminategroup.info.id = m_eliminate_group.info.id;
		arg.eliminategroups.push_back(ags);
		DBEliminateGroupSave *rpc = (DBEliminateGroupSave *) Rpc::Call(RPC_DBELIMINATEGROUPSAVE, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
	else
	{
		SendResult<EliminateGroupAbdicate_Re>(ri->roleid, ERROR_ELIMINATE_GROUP_NOT_IN_GROUP,  ri->localsid, ri->linksid);
	}
}

void EliminateGroup::OnKick(RoleInfo *ri, ruid_t target)
{
	LOG_TRACE("EliminateGroup::OnKick::roleid=" PRINT64":target=" PRINT64":group_id=%ld:group_type=%d", ri->roleid, target, GetGroupID(), GetGroupType());
	if (ri->roleid != m_eliminate_group.leader)
	{
		SendResult<EliminateGroupKick_Re>(ri->roleid, ERROR_ELIMINATE_GROUP_NO_RIGHT,  ri->localsid, ri->linksid);
		return;
	}

	bool found = IsPlayerExist(target);
	if (found)
	{
		SetSaving(true);

		DBEliminateGroupSaveArg arg;
		arg.roleid = target;
		GEliminateGroupSave ags;
		ags.operation = AGST_KICK;
		ags.eliminategroup.info.id = m_eliminate_group.info.id;
		arg.eliminategroups.push_back(ags);
		DBEliminateGroupSave *rpc = (DBEliminateGroupSave *) Rpc::Call(RPC_DBELIMINATEGROUPSAVE, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
	else
	{
		SendResult<EliminateGroupKick_Re>(ri->roleid, ERROR_ELIMINATE_GROUP_NOT_IN_GROUP,  ri->localsid, ri->linksid);
	}
}

void EliminateGroup::OnInviteAnswer(ruid_t roleid, ruid_t invitee, int retcode)
{
	LOG_TRACE("EliminateGroup::OnInviteAnswer::roleid=" PRINT64":invitee=" PRINT64":retcode=%d:group_type=%d", roleid, invitee, retcode, GetGroupType());
	//注意，这里没判断ri，在下边用到的时候判断，ri有可能是空的，玩家不在线
	RoleInfo *ri = RoleMap::Instance().FindOnline(roleid);
	RoleInfo *pInvitee = RoleMap::Instance().FindOnline(invitee);
	if (!pInvitee)
	{
		if (ri)
		{
			SendResult<EliminateGroupInvite_Re>(invitee, -1,  ri->localsid, ri->linksid);
		}
		return;
	}

	if (retcode)
	{
		//拒绝了
		if (ri)
		{
			SendResult<EliminateGroupInvite_Re>(invitee, ERROR_ELIMINATE_GROUP_INVITE_REFUSED,  ri->localsid, ri->linksid);
		}
		return;
	}

	if (GetTotalMemberCount() >= EliminateGroupManager::GetInstance().GetMaxMemberCount())
	{
		//已经满了
		SendResult<EliminateGroupInvite_Re>(roleid, ERROR_ELIMINATE_GROUP_MEMBER_MAX_COUNT,  pInvitee->localsid, pInvitee->linksid);
		return;
	}

	SetSaving(true);

	GEliminateGroupPlayer eliminate_group_player;
	eliminate_group_player.id = invitee;
	eliminate_group_player.reward.push_back(EliminateGroupManager::GetInstance().GetRewardByStage(PB::EA_STAGE_ENROLL));

	DBEliminateGroupSaveArg arg;
	arg.roleid = invitee;
	GEliminateGroupSave ags;
	ags.operation = AGST_INVITE;
	ags.eliminategroup.info.id = m_eliminate_group.info.id;
	ags.eliminategroup.members.push_back(eliminate_group_player);
	arg.eliminategroups.push_back(ags);
	DBEliminateGroupSave *rpc = (DBEliminateGroupSave *) Rpc::Call(RPC_DBELIMINATEGROUPSAVE, arg);
	GameDBClient::GetInstance()->SendProtocol(rpc);
}

void EliminateGroup::OnDBSave(ruid_t roleid, char operation, const GEliminateGroup& eliminategroup)
{
	LOG_TRACE("EliminateGroup::OnDBSave::roleid=" PRINT64":operation=%d:group_id=%ld:group_type=%d", roleid, operation, GetGroupID(), GetGroupType());
	if (operation == AGST_INVITE)
	{
		if (GetTotalMemberCount() >= EliminateGroupManager::GetInstance().GetMaxMemberCount())
		{
			GLog::log(LOG_ERR, "EliminateGroup OnDBSave members size=%d . eliminategroupid:" PRINT64 " invitee" PRINT64 ":group_type=%d", GetTotalMemberCount(), eliminategroup.info.id, roleid, GetGroupType());
			return;
		}
		if (eliminategroup.members[0].id != roleid)
		{
			GLog::log(LOG_ERR, "EliminateGroup OnDBSave roleid=" PRINT64 " . eliminategroupid:" PRINT64 " invitee" PRINT64 ":group_type=%d", eliminategroup.members[0].id, eliminategroup.info.id, roleid, GetGroupType());
			return;
		}
		m_eliminate_group.members.push_back(eliminategroup.members[0]);

		ForEachMember([roleid, this](const GEliminateGroupPlayer & member)
		{
			if (roleid == member.id)
			{
				return;
			}
			RoleInfo *ri = RoleMap::Instance().FindOnline(member.id);
			if (ri)
			{
				EliminateGroupInfo info(ri->localsid, this->GetGEliminateGroup());
				GDeliveryServer::GetInstance()->Send(ri->linksid, info);
				LOG_TRACE("EliminateGroup::OnDBSave::AGST_INVITE::SendEliminateGroupInfo:roleid=" PRINT64":member=" PRINT64":group_type=%d", roleid, member.id, GetGroupType());
				this->SendResult<EliminateGroupInvite_Re>(roleid, 0, ri->localsid, ri->linksid);
			}
		});

		RoleInfo *ri = RoleMap::Instance().Find(roleid);
		if (ri)
		{
			if (ri->SNSReady())
			{
				ri->friends.SetEliminateGroup(m_eliminate_group.info);
			}

			if (ri->IsOnline())
			{
				OnLogin(ri);
				SendResult<EliminateGroupInvite_Re>(roleid, 0, ri->localsid, ri->linksid);
			}
		}

		SetDirty(true);

		LogMemberChange(roleid, MCT_INVITE, true);
	}
	else if (operation == AGST_ABDICATE)
	{
		GLog::formatlog("EliminateGroupAbdicate", "id=" PRINT64 ":leader=" PRINT64 ":target=" PRINT64 ":group_type=%d", eliminategroup.info.id, m_eliminate_group.leader, roleid, GetGroupType());

		ruid_t old_leader_id = m_eliminate_group.leader;
		m_eliminate_group.leader = roleid;

		ForEachMember([roleid, this](const GEliminateGroupPlayer & member)
		{
			RoleInfo *ri = RoleMap::Instance().FindOnline(member.id);
			if (ri)
			{
				this->SendResult<EliminateGroupAbdicate_Re>(roleid, 0, ri->localsid, ri->linksid);
			}
		});
		LogMemberChange(old_leader_id, MCT_ABDICATE, false);
	}
	else if (operation == AGST_QUIT)
	{
		GEliminateGroupPlayerVector::iterator pit = m_eliminate_group.members.begin();
		for ( ; pit != m_eliminate_group.members.end(); )
		{
			RoleInfo *ri = RoleMap::Instance().Find((*pit).id);
			if (ri && ri->IsOnline())
			{
				SendResult<EliminateGroupQuit_Re>(roleid, 0, ri->localsid, ri->linksid);
			}
			if ((*pit).id == roleid)
			{
				if (ri)
				{
					if (ri->IsOnline())
					{
						PB::ipt_eliminate_group_info info;
						info.set_eliminate_group_type(GetGroupType());
						if (GetGroupType() == EGT_5V5)
						{
							info.set_eliminate_group_id(0);
							info.set_eliminate_group_grade(0);
						}
						else if (GetGroupType() == EGT_3V3)
						{
							info.set_eliminate_group_2_id(0);
							info.set_eliminate_group_2_grade(0);
						}
						else if (GetGroupType() == EGT_2V2)
						{
							info.set_eliminate_group_3_id(0);
							info.set_eliminate_group_3_grade(0);
						}
						ri->SendMessage2GS(info);
					}
					if (ri->SNSReady())
					{
						ri->friends.ClearEliminateGroup();
					}
				}
				pit = m_eliminate_group.members.erase(pit);
				continue;
			}
			++ pit;
		}

		SetDirty(true);

		LogMemberChange(roleid, MCT_QUIT, false);
	}
	else if (operation == AGST_KICK)
	{
		GEliminateGroupPlayerVector::iterator pit = m_eliminate_group.members.begin();
		for ( ; pit != m_eliminate_group.members.end(); )
		{
			RoleInfo *ri = RoleMap::Instance().Find((*pit).id);
			if (ri && ri->IsOnline())
			{
				SendResult<EliminateGroupKick_Re>(roleid, 0, ri->localsid, ri->linksid);
			}
			if ((*pit).id == roleid)
			{
				if (ri)
				{
					//EliminateGroupSyncGS sync((*pit).id, NameRuidPair(), 0);
					//GProviderServer::GetInstance()->DispatchProtocol(ri->lineid, sync);
					if (ri->IsOnline())
					{
						PB::ipt_eliminate_group_info info;
						info.set_eliminate_group_type(GetGroupType());
						if (GetGroupType() == EGT_5V5)
						{
							info.set_eliminate_group_id(0);
							info.set_eliminate_group_grade(0);
						}
						else if (GetGroupType() == EGT_3V3)
						{
							info.set_eliminate_group_2_id(0);
							info.set_eliminate_group_2_grade(0);
						}
						else if (GetGroupType() == EGT_2V2)
						{
							info.set_eliminate_group_3_id(0);
							info.set_eliminate_group_3_grade(0);
						}
						ri->SendMessage2GS(info);
					}
					if (ri->SNSReady())
					{
						ri->friends.ClearEliminateGroup();
					}
				}
				pit = m_eliminate_group.members.erase(pit);
				continue;
			}
			++ pit;
		}

		SetDirty(true);

		LogMemberChange(roleid, MCT_KICK, true);
	}
}

bool EliminateGroup::TrySaveAndCheckDelete(int now)
{
	if (IsDirty() && !IsSaving())
	{
		GEliminateGroupSave ags(AGST_MODIFY, GetGEliminateGroup());
		if (GetGEliminateGroup().last_battle_timestamp == 0)
		{
			ags.operation = AGST_DELETE;
		}

		DBEliminateGroupSaveArg arg;
		arg.eliminategroups.push_back(ags);
		SetDirty(false);
		SetSaving(true);

		DBEliminateGroupSave *rpc = (DBEliminateGroupSave *) Rpc::Call(RPC_DBELIMINATEGROUPSAVE, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
	else if (!IsDirty() && !IsSaving() && !GetOnlineNum())
	{
		//一定时间没有人在线，也没有东西需要保存，就先从内存里删除,有人上线了再重新加载
		/*
		if (GetActiveTimestamp() + ELIMINATE_GROUP_FREE_MEMORY_TIME < now)
		{
			return true;
		}
		*/
	}
	return false;
}
void EliminateGroup::Save2PB(PB::eliminate_group& pb, ruid_t roleid, bool save_member) const
{
	pb.mutable_info()->set_id(GetGroupID());
	pb.mutable_info()->set_name((const char *)GetGroupName().begin(), GetGroupName().size());
	pb.set_grade(GetGroupGrade());

	if (save_member)
	{
		ForEachMember([roleid, &pb](const GEliminateGroupPlayer & player)
		{
			auto *pPlayer = pb.add_members();
			pPlayer->set_roleid(player.id);
			if (roleid == player.id)
			{
				for (int i = 0; i < PB::EA_STAGE_KNOCK; ++i)
				{
					auto state = PB::eliminate_group_player::EAR_TYPE_NONE;
					if (i < player.reward.size())
					{
						auto reward = player.reward[i];
						if (reward < 0)
						{
							state = PB::eliminate_group_player::EAR_TYPE_USED;
						}
						else if (reward > 0)
						{
							state = PB::eliminate_group_player::EAR_TYPE_UNUSE;
						}
					}
					pPlayer->add_reward_status(state);
				}
			}

			{
				auto state = PB::eliminate_group_player::EAR_TYPE_NONE;
				if (PB::EA_STAGE_KNOCK < player.reward.size())
				{
					auto reward = player.reward[PB::EA_STAGE_KNOCK];
					if (reward < -1)
					{
						state = PB::eliminate_group_player::EAR_TYPE_USED;
					}
					else if (reward > 0 || reward == 1)
					{
						state = PB::eliminate_group_player::EAR_TYPE_UNUSE;
					}
					pPlayer->add_reward_status(state);
				}
			}

		});
	}
}
bool EliminateGroupManager::Initialize()
{
	m_now_time = Timer::GetTime();
	std::string path("eliminate_battle_config.lua");
	lua_State *L = luaL_newstate();
	if (NULL == L)
	{
		return false;
	}

	luaL_openlibs(L);
	if (luaL_dofile(L, path.c_str()))
	{
		printf("DS::ELiminateBattleManager::Initialize syntax error in script.\n");
		lua_close(L);
		return false;
	}

	LuaTableNode root;
	LuaConfReader::parseFromState(L, "root", root);
	root["stage_rewards"].Fetch(m_rewards);
	if (m_rewards.size() != 4)
	{
		printf("DS::CoupleGroupManager::Initialize rewards error in script.\n");
		lua_close(L);
		return false;
	}

	if (!IsValidEliminateGroupType(GLOBAL_CONFIG.eliminate_group_type))
	{
		printf("DS::CoupleGroupManager::Initialize eliminate_group_type error in script.\n");
		lua_close(L);
		return false;
	}

	m_max_member_count = root["max_member_count"];
	if (m_max_member_count <= 0)
	{
		printf("DS::CoupleGroupManager::Initialize max_member_count error in script.\n");
		lua_close(L);
		return false;
	}

	m_max_match_member_count = root["max_match_member_count"];
	if (m_max_match_member_count <= 0)
	{
		printf("DS::CoupleGroupManager::Initialize max_match_member_count error in script.\n");
		lua_close(L);
		return false;
	}

	m_max_prof_count = root["max_prof_count"];
	if (m_max_prof_count <= 0)
	{
		printf("DS::CoupleGroupManager::Initialize max_prof_count error in script.\n");
		lua_close(L);
		return false;
	}

	m_match_center_toplist_count = root["match_center_toplist_count"];
	if (m_match_center_toplist_count <= 0)
	{
		printf("DS::CoupleGroupManager::Initialize match_center_toplist_count error in script.\n");
		lua_close(L);
		return false;
	}

	root["rank_rewards"].Fetch(m_rank_rewards);

	IntervalTimer::Attach(this, (ELIMINATE_GROUP_MANAGER_UPDATE_INTERVAL * 1000000) / IntervalTimer::Resolution());
	return true;
}

bool EliminateGroupManager::Update()
{
	m_now_time = Timer::GetTime();

	if (!m_init)
	{
		return true;
	}
	TryLoadEliminateGroupRanks();
	if (m_eliminate_group_map.empty())
	{
		return true;
	}

	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(m_update_id);
	if (it == m_eliminate_group_map.end())
	{
		it = m_eliminate_group_map.begin();
	}

	static const unsigned int update_count_each_time = 256;	//每次最多遍历256个战队
	static const unsigned int update_time_per_minute = 60 / ELIMINATE_GROUP_MANAGER_UPDATE_INTERVAL;	//每分钟执行多少次
	static const unsigned int max_count_per_minute = update_count_each_time * update_time_per_minute;	//每分钟预计能遍历的最多次数

	unsigned int size = m_eliminate_group_map.size();
	unsigned int max_count = size > max_count_per_minute ? (size / update_time_per_minute + 1) : update_count_each_time;	//最多每分钟遍历一次所有的战队
	if (max_count > size)
	{
		max_count = size;    //全部战队都不够update_count_each_time这么多个，那就每次全遍历了
	}
	std::vector<ruid_t> del_vec;
	unsigned int count = 0;
	while (count < max_count)
	{
		++ count;
		++ it;
		if (it == m_eliminate_group_map.end())
		{
			it = m_eliminate_group_map.begin();
		}

		EliminateGroup& eliminategroup = it->second;

		bool need_del = eliminategroup.TrySaveAndCheckDelete(m_now_time);
		if (need_del)
		{
			del_vec.push_back(eliminategroup.GetGEliminateGroup().info.id);
		}

		int center_match_success_time = eliminategroup.GetCenterMatchSuccessTime();
		if (center_match_success_time > 0 && m_now_time - center_match_success_time > 600)
		{
			eliminategroup.SetCenterMatchSuccessTime(0);
			eliminategroup.SetIsMatching(false);
		}
	}

	for (auto id : del_vec)
	{
		if (it->first == id)
		{
			++ it;
			if (it == m_eliminate_group_map.end())
			{
				it = m_eliminate_group_map.begin();
			}
		}
		m_eliminate_group_map.erase(id);
		GLog::log(LOG_INFO, "DS::EliminateGroupManager::Update erase eliminategrop %ld in memory.", id);
	}

	if (m_eliminate_group_map.empty())
	{
		m_update_id = 0;
	}
	else
	{
		m_update_id = it->first;
	}
	return true;
}

int EliminateGroupManager::GetGroupGradeTimeStamp()
{
	if (m_debug_group_grade_timestamp)
	{
		return m_debug_group_grade_timestamp;
	}
	return TIME_HELPER::TimeHelper::GetLocalMonthBegin();
}

void EliminateGroupManager::OnDBSave(ruid_t roleid, std::vector<GEliminateGroupSave>& eliminate_groups, int ret)
{
	LOG_TRACE("EliminateGroupManager::OnDBSave::roleid=" PRINT64":ret=%d:group_size=%zu:eliminate_group_type=%d", roleid, ret, eliminate_groups.size(), GLOBAL_CONFIG.eliminate_group_type);
	if (ret != 0)
	{
		std::vector<GEliminateGroupSave>::iterator it = eliminate_groups.begin(), eit = eliminate_groups.end();
		for ( ; it != eit; ++it)
		{
			GEliminateGroup& eliminategroup = (*it).eliminategroup;
			ELIMINATE_GROUP_MAP::iterator mit = m_eliminate_group_map.find(eliminategroup.info.id);
			if (mit == m_eliminate_group_map.end())
			{
				continue;
			}
			EliminateGroup& ag = mit->second;

			ag.SetDirty(true);
			ag.SetSaving(false);

			GLog::log(LOG_ERR, "EliminateGroupManager OnDBSave err. eliminate_group_id=%ld roleid=" PRINT64 ", size=%zu, ret=%d, eliminate_group_type=%d", eliminategroup.info.id, roleid, eliminate_groups.size(), ret, GLOBAL_CONFIG.eliminate_group_type);
		}
		return;
	}

	std::vector<GEliminateGroupSave>::iterator it = eliminate_groups.begin(), eit = eliminate_groups.end();
	for ( ; it != eit; ++it)
	{
		char oper = (*it).operation;
		GEliminateGroup& eliminategroup = (*it).eliminategroup;
		ELIMINATE_GROUP_MAP::iterator mit = m_eliminate_group_map.find(eliminategroup.info.id);
		if (mit == m_eliminate_group_map.end())
		{
			continue;
		}
		EliminateGroup& ag = mit->second;

		if (oper == AGST_DELETE)
		{
			GLog::formatlog("EliminateGroupDelete", "id=" PRINT64 ":leader=" PRINT64 ":eliminate_group_type=%d", eliminategroup.info.id, ag.GetGEliminateGroup().leader, GLOBAL_CONFIG.eliminate_group_type);

			ReleaseName *rpc = (ReleaseName *)Rpc::Call(RPC_RELEASENAME,
			                   ReleaseNameArg(ALLOC_ARENA_GROUP_NAME, g_zoneid, ag.GetGEliminateGroup().info.id, ag.GetGEliminateGroup().info.name));
			UniqueNameClient::GetInstance()->SendProtocol(rpc);

			ag.ForEachMember([&ag](GEliminateGroupPlayer & member)
			{
				RoleInfo *ri = RoleMap::Instance().Find(member.id);
				if (ri)
				{
					if (ri->IsOnline())
					{
						if (ag.GetGroupType() == GLOBAL_CONFIG.eliminate_group_type)
						{
							EliminateGroupInfo info(ri->localsid, GEliminateGroup());
							GDeliveryServer::GetInstance()->Send(ri->linksid, info);

							PB::ipt_eliminate_group_info aginfo;
							aginfo.set_eliminate_group_type(ag.GetGroupType());
							if (ag.GetGroupType() == EGT_5V5)
							{
								aginfo.set_eliminate_group_id(0);
								aginfo.set_eliminate_group_grade(0);
							}
							else if (ag.GetGroupType() == EGT_3V3)
							{
								aginfo.set_eliminate_group_2_id(0);
								aginfo.set_eliminate_group_2_grade(0);
							}
							else if (ag.GetGroupType() == EGT_2V2)
							{
								aginfo.set_eliminate_group_3_id(0);
								aginfo.set_eliminate_group_3_grade(0);
							}
							ri->SendMessage2GS(aginfo);
						}
					}
					if (ri->SNSReady())
					{
						ri->friends.ClearEliminateGroup();
					}
				}
				if (member.id == ag.GetGroupLeader())
				{
					if (ri)
					{
						ag.MemberChangeLog(MCT_QUIT, ri, ri, true);
					}
				}
			});
			m_eliminate_group_map.erase(mit);
			continue;
		}
		else if (oper == AGST_INVITE)
		{
			ag.OnDBSave(roleid, AGST_INVITE, eliminategroup);
		}
		else if (oper == AGST_ABDICATE)
		{
			ag.OnDBSave(roleid, AGST_ABDICATE, eliminategroup);
		}
		else if (oper == AGST_QUIT)
		{
			ag.OnDBSave(roleid, AGST_QUIT, eliminategroup);
		}
		else if (oper == AGST_KICK)
		{
			ag.OnDBSave(roleid, AGST_KICK, eliminategroup);
		}

		ag.SetSaving(false);
	}
}

void EliminateGroupManager::OnDBGet(ruid_t roleid, GEliminateGroup& eliminategroup, bool idip_rename, Octets& idip_name, int ret)
{
	LOG_TRACE("EliminateGroupManager::OnDBGet::roleid=" PRINT64":eliminategroup=" PRINT64":ret=%d:eliminate_group_type=%d:idip_rename=%d",
	          roleid, eliminategroup.info.id, ret, GLOBAL_CONFIG.eliminate_group_type, (int)idip_rename);

	if (ret != 0)
	{
		return;
	}

	EliminateGroup *pAg = NULL;
	auto it = m_eliminate_group_map.find(eliminategroup.info.id);
	if (it == m_eliminate_group_map.end())
	{
		pAg = &(m_eliminate_group_map[eliminategroup.info.id]);
		pAg->SetGEliminateGroup(eliminategroup);
	}
	else
	{
		pAg = &(it->second);
	}

	if (!idip_rename)
	{
		RoleInfo *ri = RoleMap::Instance().FindOnline(roleid);
		if (ri)
		{
			pAg->OnLogin(ri);
		}
	}
	else
	{
		pAg->OnIDIPRename(idip_name);
	}
}

int EliminateGroupManager::CheckCreate()
{
	if (!m_init)
	{
		return ERROR_GENERAL;
	}
	//需要检查是否处于报名阶段
	if (!m_debug_mode)
	{
		auto stage = EliminateBattleManager::GetInstance().GetState();
		if (stage < PB::EBS_ENROLL_BEGIN || stage > PB::EBS_ENROLL_END)
		{
			LOG_TRACE("EliminateGroupManager::CheckCreate:stage=%d:eliminate_group_type=%d:InvalidStage", stage, GLOBAL_CONFIG.eliminate_group_type);
			return -1;
		}
	}
	if (m_eliminate_group_map.size() >= ELIMINATE_GROUP_FREE_ID_MAX)
	{
		LOG_TRACE("EliminateGroupManager::CheckCreate:size=%zu:eliminate_group_type=%d:SizeLimit", m_eliminate_group_map.size(), GLOBAL_CONFIG.eliminate_group_type);
		return ERROR_ELIMINATE_GROUP_MAX_COUNT;
	}

	return ERROR_SUCCESS;
}

void EliminateGroupManager::OnCreate(GEliminateGroup& eliminategroup)
{
	ruid_t eliminategroup_id = eliminategroup.info.id;
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroup_id);
	if (it != m_eliminate_group_map.end())
	{
		GLog::log(LOG_ERR, "DS::EliminateGroupManager OnCreate dup id:" PRINT64 ":eliminate_group_type=%d", eliminategroup_id, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}

	GLog::formatlog("EliminateGroupCreate", "eliminategroupid=" PRINT64 ":roleid=" PRINT64 ":eliminate_group_type=%d", eliminategroup_id, eliminategroup.leader, GLOBAL_CONFIG.eliminate_group_type);

	EliminateGroup& ag = m_eliminate_group_map[eliminategroup_id];
	ag.SetGEliminateGroup(eliminategroup);

	ag.ForEachMember([this, eliminategroup_id](GEliminateGroupPlayer & player)
	{
		player.reward.push_back(this->m_rewards[PB::EA_STAGE_ENROLL]);
		LOG_TRACE("EliminateGroupManager::OnCreate::group_id=%ld:roleid=" PRINT64":reward_size=%zu:eliminate_group_type=%d", eliminategroup_id, player.id, player.reward.size(), GLOBAL_CONFIG.eliminate_group_type);
	});

	ag.SetDirty(true);

	RoleInfo *ri = RoleMap::Instance().Find(eliminategroup.leader);
	if (ri && ri->SNSReady())
	{
		ri->friends.SetEliminateGroup(eliminategroup.info);
		//通知玩家创建成功
		if (ri->IsOnline())
		{
			EliminateGroupCreate_Re re(0, eliminategroup.leader, eliminategroup.info);
			re.localsid = ri->localsid;
			GDeliveryServer::GetInstance()->Send(ri->linksid, re);
			//调用一次OnLogin，为了给玩家同步一次战队信息
			ag.OnLogin(ri);
		}
		SendEliminateGroupInfo(ri);
		// 记日志
		ag.MemberChangeLog(MCT_CREATE, ri, ri, true);
	}
	else
	{
		getroleinfo_handle handleRoleInfo = [this, eliminategroup_id](int retcode, int64_t roleid, const RoleInfo * pRole)
		{
			if (retcode == 0)
			{
				m_eliminate_group_map[eliminategroup_id].MemberChangeLog(MCT_CREATE, pRole, pRole, true);
			}
		};
		RoleMap::GetRoleInfo(eliminategroup.leader, Octets(), &handleRoleInfo);
	}
}

void EliminateGroupManager::__OnLogin(RoleInfo *ri, ruid_t eliminategroupid)
{
	if (eliminategroupid == 0)
	{
		return;
	}

	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		auto& group = it->second;
		if (GET_FUNC_SWITCH(kFuncCodeEliminateGroupAutoDismiss)
		        && ((group.GetGroupType() == EGT_3V3 && group.GetTotalMemberCount() >= GLOBAL_CONFIG.eliminate_3v3_auto_dismiss_count)
		            || (group.GetGroupType() == EGT_2V2 && group.GetTotalMemberCount() >= GLOBAL_CONFIG.eliminate_2v2_auto_dismiss_count)))
		{
			group.OnDismiss();
			return ;
		}
		(it->second).OnLogin(ri);
		if (eliminategroupid == ri->friends.GetEliminateGroupID())
		{
			SendEliminateGroupInfo(ri);
		}
	}
	else
	{
		//战队信息内存还没有加载，发送消息去数据库重新获取
		DBEliminateGroupGet *rpc = (DBEliminateGroupGet *) Rpc::Call(RPC_DBELIMINATEGROUPGET, DBEliminateGroupGetArg(eliminategroupid));
		rpc->roleid = ri->roleid;
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
}

void EliminateGroupManager::OnLogin(RoleInfo *ri)
{
	if (!ri)
	{
		return;
	}
	if (!ri->SNSReady())
	{
		return;
	}

	if (CenterManager::GetInstance().IsCenter())
	{
		//中心服不加载战队数据
		return;
	}

	ruid_t eliminategroupid = ri->friends.GPSFacebook().eliminate_group().id();
	ruid_t eliminategroupid_2 = ri->friends.GPSFacebook().eliminate_group_2().id();
	ruid_t eliminategroupid_3 = ri->friends.GPSFacebook().eliminate_group_3().id();
	LOG_TRACE("EliminateGroupManager::OnLogin::roleid=" PRINT64":eliminategroupid=%ld:eliminategroupid_2=%ld:eliminategroupid_3=%ld:eliminate_group_type=%d", ri->roleid, eliminategroupid, eliminategroupid_2, eliminategroupid_3, GLOBAL_CONFIG.eliminate_group_type);

	if (GLOBAL_CONFIG.eliminate_group_type == EGT_5V5 && !eliminategroupid)
	{
		//在这里也同步一次gs，防止gplayer上缓存的数据是老的
		PB::ipt_eliminate_group_info aginfo;
		aginfo.set_eliminate_group_type(GLOBAL_CONFIG.eliminate_group_type);
		aginfo.set_eliminate_group_id(0);
		aginfo.set_eliminate_group_grade(0);
		ri->SendMessage2GS(aginfo);
		return;
	}
	if (GLOBAL_CONFIG.eliminate_group_type == EGT_3V3 && !eliminategroupid_2)
	{
		//在这里也同步一次gs，防止gplayer上缓存的数据是老的
		PB::ipt_eliminate_group_info aginfo;
		aginfo.set_eliminate_group_type(GLOBAL_CONFIG.eliminate_group_type);
		aginfo.set_eliminate_group_2_id(0);
		aginfo.set_eliminate_group_2_grade(0);
		ri->SendMessage2GS(aginfo);
		return;
	}
	if (GLOBAL_CONFIG.eliminate_group_type == EGT_2V2 && !eliminategroupid_3)
	{
		//在这里也同步一次gs，防止gplayer上缓存的数据是老的
		PB::ipt_eliminate_group_info aginfo;
		aginfo.set_eliminate_group_type(GLOBAL_CONFIG.eliminate_group_type);
		aginfo.set_eliminate_group_3_id(0);
		aginfo.set_eliminate_group_3_grade(0);
		ri->SendMessage2GS(aginfo);
		return;
	}

	__OnLogin(ri, eliminategroupid);
	__OnLogin(ri, eliminategroupid_2);
	__OnLogin(ri, eliminategroupid_3);
}

void EliminateGroupManager::OnLogout(ruid_t eliminategroupid, ruid_t roleid)
{
	LOG_TRACE("EliminateGroupManager::OnLogout::eliminategroupid=" PRINT64":roleid=" PRINT64":eliminate_group_type=%d", eliminategroupid, roleid, GLOBAL_CONFIG.eliminate_group_type);
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).OnLogout(roleid);
	}
}

void EliminateGroupManager::DebugSetActiveTimestampOffset(ruid_t eliminategroupid, int offset)
{
	LOG_TRACE("EliminateGroupManager::DebugSetActiveTimestampOffset::eliminategroupid=" PRINT64":offset=%d:eliminate_group_type=%d", eliminategroupid, offset, GLOBAL_CONFIG.eliminate_group_type);
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).DebugSetActiveTimestampOffset(offset);
	}
}

void EliminateGroupManager::DebugSetGrade(ruid_t eliminategroupid, int grade)
{
	LOG_TRACE("EliminateGroupManager::DebugSetGrade::eliminategroupid=" PRINT64":grade=%d:eliminate_group_type=%d", eliminategroupid, grade, GLOBAL_CONFIG.eliminate_group_type);
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).DebugSetGrade(grade);
	}
}

void EliminateGroupManager::IDIPRename(ruid_t eliminategroupid, Octets& name)
{
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).OnIDIPRename(name);
	}
	else
	{
		//战队信息内存还没有加载，发送消息去数据库重新获取
		DBEliminateGroupGet *rpc = (DBEliminateGroupGet *) Rpc::Call(RPC_DBELIMINATEGROUPGET, DBEliminateGroupGetArg(eliminategroupid));
		rpc->idip_rename = true;
		rpc->idip_name = name;
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
}

void EliminateGroupManager::SetGrade(ruid_t eliminategroupid, ruid_t roleid, int grade)
{
	LOG_TRACE("EliminateGroupManager::SetGrade::eliminategroupid=" PRINT64":roleid=" PRINT64":grade=%d:eliminate_group_type=%d", eliminategroupid, roleid, grade, GLOBAL_CONFIG.eliminate_group_type);
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).OnSetGrade(roleid, grade);
	}
}
void EliminateGroupManager::GradeChange(ruid_t eliminategroupid, int battle_result, int grade)
{
	LOG_TRACE("EliminateGroupManager::GradeChange::eliminategroupid=" PRINT64":battle_result=%d:grade=%d:eliminate_group_type=%d", eliminategroupid, battle_result, grade, GLOBAL_CONFIG.eliminate_group_type);
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).OnGradeChange(battle_result, grade);
	}
}
void EliminateGroupManager::GradeSet(ruid_t eliminategroupid, int grade)
{
	LOG_TRACE("EliminateGroupManager::GradeSet::eliminategroupid=" PRINT64":grade=%d:eliminate_group_type=%d", eliminategroupid, grade, GLOBAL_CONFIG.eliminate_group_type);
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).OnSetChange(grade);
	}
}
void EliminateGroupManager::RewardChange(ruid_t eliminategroupid, int stage)
{
	LOG_TRACE("EliminateGroupManager::RewardChange::eliminategroupid=%ld:stage=%d:eliminate_group_type=%d", eliminategroupid, stage, GLOBAL_CONFIG.eliminate_group_type);
	if (stage >= (int)m_rewards.size() || stage < 0)
	{
		LOG_TRACE("EliminateGroupManager::RewardChange::eliminategroupid=%ld:stage=%d:reward_size=%zu:eliminate_group_type=%d", eliminategroupid, stage, m_rewards.size(), GLOBAL_CONFIG.eliminate_group_type);
		return;
	}
	if (!m_rewards[stage])
	{
		LOG_TRACE("EliminateGroupManager::RewardChange::eliminategroupid=%ld:stage=%d:eliminate_group_type=%d:RewardIsNull", eliminategroupid, stage, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).OnRewardChange(stage, m_rewards[stage]);
	}
}
void EliminateGroupManager::Invite(ruid_t roleid, ruid_t target)
{
	LOG_TRACE("EliminateGroupManager::Invite::roleid=" PRINT64":target=" PRINT64":eliminate_group_type=%d", roleid, target, GLOBAL_CONFIG.eliminate_group_type);

	if (!m_debug_mode)
	{
		auto stage = EliminateBattleManager::GetInstance().GetState();
		if (stage < PB::EBS_ENROLL_BEGIN || stage > PB::EBS_ENROLL_END)
		{
			LOG_TRACE("EliminateGroupManager::InviteCheck:stage=%d:eliminate_group_type=%d:InvalidStage", stage, GLOBAL_CONFIG.eliminate_group_type);
			return;
		}
	}

	RoleInfo *ri = RoleMap::Instance().FindOnline(roleid);
	if (ri && ri->SNSReady())
	{
		ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(ri->friends.GetEliminateGroupID());
		if (it != m_eliminate_group_map.end())
		{
			(it->second).OnInvite(ri, target);
		}
	}
}

void EliminateGroupManager::Quit(ruid_t roleid)
{
	LOG_TRACE("EliminateGroupManager::Quit::roleid=" PRINT64":eliminate_group_type=%d", roleid, GLOBAL_CONFIG.eliminate_group_type);
	RoleInfo *ri = RoleMap::Instance().FindOnline(roleid);
	if (ri && ri->SNSReady())
	{
		ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(ri->friends.GetEliminateGroupID());
		if (it != m_eliminate_group_map.end())
		{
			(it->second).OnQuit(ri);
		}
	}
}

void EliminateGroupManager::Abdicate(ruid_t roleid, ruid_t target)
{
	LOG_TRACE("EliminateGroupManager::Abdicate::roleid=" PRINT64":target=" PRINT64":eliminate_group_type=%d", roleid, target, GLOBAL_CONFIG.eliminate_group_type);

	if (!m_debug_mode)
	{
		auto stage = EliminateBattleManager::GetInstance().GetState();
		if (stage < PB::EBS_ENROLL_BEGIN || stage > PB::EBS_ENROLL_END)
		{
			LOG_TRACE("EliminateGroupManager::AbdicateCheck:stage=%d:eliminate_group_type=%d:InvalidStage", stage, GLOBAL_CONFIG.eliminate_group_type);
			return;
		}
	}

	RoleInfo *ri = RoleMap::Instance().FindOnline(roleid);
	if (ri && ri->SNSReady())
	{
		ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(ri->friends.GetEliminateGroupID());
		if (it != m_eliminate_group_map.end())
		{
			(it->second).OnAbdicate(ri, target);
		}
	}
}

void EliminateGroupManager::Kick(ruid_t roleid, ruid_t target)
{
	LOG_TRACE("EliminateGroupManager::Kick::roleid=" PRINT64":target=" PRINT64":eliminate_group_type=%d", roleid, target, GLOBAL_CONFIG.eliminate_group_type);

	if (!m_debug_mode)
	{
		auto stage = EliminateBattleManager::GetInstance().GetState();
		if (stage < PB::EBS_ENROLL_BEGIN || stage > PB::EBS_ENROLL_END)
		{
			LOG_TRACE("EliminateGroupManager::AbdicateCheck:stage=%d:eliminate_group_type=%d:InvalidStage", stage, GLOBAL_CONFIG.eliminate_group_type);
			return;
		}
	}

	RoleInfo *ri = RoleMap::Instance().FindOnline(roleid);
	if (ri && ri->SNSReady())
	{
		ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(ri->friends.GetEliminateGroupID());
		if (it != m_eliminate_group_map.end())
		{
			(it->second).OnKick(ri, target);
		}
	}
}

void EliminateGroupManager::OnInviteAnswer(ruid_t eliminategroupid, ruid_t roleid, ruid_t invitee, int retcode)
{
	LOG_TRACE("EliminateGroupManager::OnInviteAnswer::groupid=%ld:roleid=" PRINT64":invitee=" PRINT64":retcode=%d:eliminate_group_type=%d", eliminategroupid, roleid, invitee, retcode, GLOBAL_CONFIG.eliminate_group_type);
	ELIMINATE_GROUP_MAP::iterator it = m_eliminate_group_map.find(eliminategroupid);
	if (it != m_eliminate_group_map.end())
	{
		(it->second).OnInviteAnswer(roleid, invitee, retcode);
	}
}
int EliminateGroupManager::GetRewardByStage(int stage)
{
	if (stage >= m_rewards.size())
	{
		return -1;
	}
	return m_rewards[stage];
}
void EliminateGroupManager::PlayerGetReward(ruid_t roleid, int stage)
{
	LOG_TRACE("EliminateGroupManager::PlayerGetReward::roleid=" PRINT64":stage=%d:eliminate_group_type=%d", roleid, stage, GLOBAL_CONFIG.eliminate_group_type);
	auto pInfo = RoleMap::Instance().FindOnline(roleid);
	if (!pInfo)
	{
		LOG_TRACE("EliminateGroupManager::PlayerGetReward::roleid=" PRINT64":stage=%d:eliminate_group_type=%d:playerOffLine", roleid, stage, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}
	if (!pInfo->SNSReady())
	{
		LOG_TRACE("EliminateGroupManager::PlayerGetReward::roleid=" PRINT64":stage=%d:eliminate_group_type=%d:SNSNotReady", roleid, stage, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}

	if (pInfo->friends.GetEliminateGroupID() == 0)
	{
		LOG_TRACE("EliminateGroupManager::PlayerGetReward::roleid=" PRINT64":stage=%d:eliminate_group_type=%d:NoGroupID", roleid, stage, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}

	auto it = m_eliminate_group_map.find(pInfo->friends.GetEliminateGroupID());
	if (it == m_eliminate_group_map.end())
	{
		LOG_TRACE("EliminateGroupManager::PlayerGetReward::roleid=" PRINT64":stage=%d:eliminate_group_type=%d:NoGroup", roleid, stage, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}
	it->second.OnGetReward(pInfo, stage);
}
void EliminateGroupManager::NotifyKnockoutBegin(ruid_t eliminategroupid, ruid_t roleid)
{
	LOG_TRACE("EliminateGroupManager::NotifyKnockoutBegin::eliminategroupid=%ld:roleid=" PRINT64":eliminate_group_type=%d", eliminategroupid, roleid, GLOBAL_CONFIG.eliminate_group_type);
	auto it = m_eliminate_group_map.find(eliminategroupid);
	if (it == m_eliminate_group_map.end())
	{
		LOG_TRACE("EliminateGroupManager::NotifyKnockoutBegin::eliminategroupid=%ld:eliminate_group_type=%d:NoGroup", eliminategroupid, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}
	it->second.OnNotifyKnockoutBegin(roleid);
}
bool EliminateGroupManager::CheckFighter(RoleInfo *pInfo) const
{
	LOG_TRACE("EliminateGroupManager::CheckFighter::roleid=" PRINT64":eliminate_group_id=%ld:eliminate_group_type=%d", pInfo->roleid, pInfo->friends.GetEliminateGroupID(), GLOBAL_CONFIG.eliminate_group_type);
	auto it = m_eliminate_group_map.find(pInfo->friends.GetEliminateGroupID());
	if (it == m_eliminate_group_map.end())
	{
		LOG_TRACE("EliminateGroupManager::CheckFighter::roleid=" PRINT64":eliminate_group_type=%d:GroupNotFound", pInfo->roleid, GLOBAL_CONFIG.eliminate_group_type);
		return false;
	}
	return it->second.OnCheckFighter(pInfo->roleid, pInfo->profession);
}
void EliminateGroupManager::CheckFighterProf(ruid_t eliminategroupid) const
{
	LOG_TRACE("EliminateGroupManager::CheckFighterProf:eliminategroupid=%ld:eliminate_group_type=%d", eliminategroupid, GLOBAL_CONFIG.eliminate_group_type);

	auto it = m_eliminate_group_map.find(eliminategroupid);
	if (it == m_eliminate_group_map.end())
	{
		return;
	}
	it->second.OnCheckFighterProf();
}
void EliminateGroupManager::SelectFighter(ruid_t roleid, const std::vector<ruid_t>& fighters)
{
	LOG_TRACE("EliminateGroupManager::SelectFighter::roleid=" PRINT64":fighters_size=%zu:eliminate_group_type=%d", roleid, fighters.size(), GLOBAL_CONFIG.eliminate_group_type);
	auto pInfo = RoleMap::Instance().FindOnline(roleid);
	if (!pInfo)
	{
		LOG_TRACE("EliminateGroupManager::SelectFighter::roleid=" PRINT64":eliminate_group_type=%d:RoleOffLine", roleid, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}
	if (!pInfo->SNSReady())
	{
		LOG_TRACE("EliminateGroupManager::SelectFighter::roleid=" PRINT64":eliminate_group_type=%d:RoleOffLine", roleid, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}

	if (pInfo->friends.GetEliminateGroupID() == 0)
	{
		LOG_TRACE("EliminateGroupManager::SelectFighter::roleid=" PRINT64":eliminate_group_type=%d:NoGroupID", roleid, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}

	auto it = m_eliminate_group_map.find(pInfo->friends.GetEliminateGroupID());
	if (it == m_eliminate_group_map.end())
	{
		LOG_TRACE("EliminateGroupManager::SelectFighter::roleid=" PRINT64":eliminate_group_type=%d:NoGroup", roleid, GLOBAL_CONFIG.eliminate_group_type);
		return;
	}
	it->second.OnSelectFighter(pInfo, fighters);
}
void EliminateGroupManager::ClearAllGrade(int eliminate_group_type)
{
	LOG_TRACE("EliminateGroupManager::ClearAllGrade:eliminate_group_type=%d", eliminate_group_type);
	for (auto it = m_eliminate_group_map.begin(), eit = m_eliminate_group_map.end(); it != eit; ++it)
	{
		auto& group = it->second;
		if (group.GetGroupType() != eliminate_group_type)
		{
			continue;
		}
		if (group.GetGroupGrade() != INIT_ELIMINATE_GROUP_GRADE)
		{
			group.SetGroupGrade(INIT_ELIMINATE_GROUP_GRADE);
			//TODO: 是否要设置flag
			group.SetFlag(FLAG_NONE);
			group.SetDirty(true);
		}
		group.ForEachMember([&group, this](GEliminateGroupPlayer & player)
		{
			if (!player.reward.empty())
			{
				player.reward.clear();
				player.reward.push_back(this->GetRewardByStage(PB::EA_STAGE_ENROLL));
				group.SetDirty(true);
			}
		});
	}
	int top_tid = GetEliminateGroupTopTid(eliminate_group_type);
	if (top_tid > 0)
	{
		DSTPManager::GetInstance().Debug_Clear(top_tid);
	}
}

void EliminateGroupManager::IdipResetEliminateScore(int eliminate_group_type)
{
	LOG_TRACE("EliminateGroupManager::IdipResetEliminateScore:eliminate_group_type=%d", eliminate_group_type);
	if (!CenterManager::GetInstance().IsCenter())
	{
		for (auto it = m_eliminate_group_map.begin(), eit = m_eliminate_group_map.end(); it != eit; ++it)
		{
			auto& group = it->second;
			if (group.GetGroupType() != eliminate_group_type)
			{
				continue;
			}
			if (group.GetGroupGrade() != INIT_ELIMINATE_GROUP_GRADE && group.GetFlag() == FLAG_CENTER_SCORE)
			{
				//TODO: 是否要设置flag
				group.SetFlag(FLAG_CENTER_SCORE);
				group.SetGroupGrade(ELIMINATE_GROUP_SCORE_BATTLE_INIT_SCORE);
				group.SetDirty(true);
				LOG_TRACE("EliminateGroupManager::IdipResetEliminateScore::groupid=%ld:eliminate_group_type=%d", group.GetGroupID(), eliminate_group_type);
				group.NotifyGSPlayerGrade();
			}
		}
	}

	auto *pEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(CORPS_BATTLE_STUB_TYPE_ELIMINATE);
	if (pEntry)
	{
		pEntry->ClearEliminateBattleScore();
	}
}

void EliminateGroupManager::LocalScoreEnd()
{
	int top_tid = GetEliminateGroupTopTid(GLOBAL_CONFIG.eliminate_group_type);
	if (top_tid <= 0)
	{
		return;
	}
	LOG_TRACE("EliminateGroupManager::LocalScoreEnd:top_tid=%d:eliminate_group_type=%d", top_tid, GLOBAL_CONFIG.eliminate_group_type);

	std::vector<DSTopListKey> topN;
	DSTPManager::GetInstance().GetTopN(top_tid, GetMatchCenterToplistCount(), topN);
	for (size_t i = 0; i < topN.size(); ++i)
	{
		auto pGroup = GetEliminateGroup(topN[i].id);
		if (!pGroup)
		{
			continue;
		}
		//TODO:设置战队flag
		pGroup->SetFlag(FLAG_CENTER_SCORE);
		pGroup->SetGroupGrade(ELIMINATE_GROUP_SCORE_BATTLE_INIT_SCORE);
		pGroup->SetDirty(true);
		LOG_TRACE("EliminateGroupManager::LocalScoreEnd::groupid=%ld:grade=%ld:changetime=%d:index=%zu:eliminate_group_type=%d", pGroup->GetGroupID(), topN[i].value, topN[i].changetime, i, GLOBAL_CONFIG.eliminate_group_type);
	}
}
void EliminateGroupManager::SetIsMatching(ruid_t eliminategroupid, bool is_matching)
{
	if (eliminategroupid <= 0)
	{
		return;
	}

	auto pGroup = GetEliminateGroup(eliminategroupid);
	if (!pGroup)
	{
		return;
	}

	pGroup->SetIsMatching(is_matching);
}
int EliminateGroupManager::GetCenterMatchSuccessTime(ruid_t eliminategroupid) const
{
	if (eliminategroupid <= 0)
	{
		return -1;
	}

	auto pGroup = GetEliminateGroup(eliminategroupid);
	if (!pGroup)
	{
		return -1;
	}

	return pGroup->GetCenterMatchSuccessTime();
}
void EliminateGroupManager::SetCenterMatchSuccessTime(ruid_t eliminategroupid, int center_match_success_time)
{
	if (eliminategroupid <= 0)
	{
		return;
	}

	auto pGroup = GetEliminateGroup(eliminategroupid);
	if (!pGroup)
	{
		return;
	}

	pGroup->SetCenterMatchSuccessTime(center_match_success_time);
}
void EliminateGroupManager::OnGenerateChampion(ruid_t eliminategroupid)
{
	LOG_TRACE("EliminateGroupManager::OnGenerateChampion::eliminategroupid=" PRINT64":eliminate_group_type=%d", eliminategroupid, GLOBAL_CONFIG.eliminate_group_type);
	auto it = m_eliminate_group_map.find(eliminategroupid);
	if (it == m_eliminate_group_map.end())
	{
		return;
	}
	auto& group = it->second;
	group.OnGenerateChampion();
}
void EliminateGroupManager::SendEliminateGroupInfo(RoleInfo *pInfo) const
{
	auto stage = EliminateBattleManager::GetInstance().GetState();
	LOG_TRACE("EliminateGroupManager::SendEliminateGroupInfo:roleid=" PRINT64":stage=%d:eliminate_group_type=%d", pInfo->roleid, (int)stage, GLOBAL_CONFIG.eliminate_group_type);
	auto it = m_eliminate_group_map.find(pInfo->friends.GetEliminateGroupID());
	if (it == m_eliminate_group_map.end())
	{
		PB::npt_eliminate_group_info info;
		info.set_battle_state((PB::ELIMINATE_BATTLE_STATE)stage);
		pInfo->SendMessage2Client(info);
		return;
	}

	const auto& group = it->second;
	PB::npt_eliminate_group_info info;
	group.Save2PB(*(info.mutable_group_info()), pInfo->roleid, true);
	info.set_battle_state((PB::ELIMINATE_BATTLE_STATE)stage);

	//写入对手信息以及比赛时间信息
	if (stage >= PB::EBS_CENTER_SCORE_BATTLE_END && stage < PB::EBS_CENTER_KNOCKOUT_BATTLE_2_END)
	{
		auto pSelf = CorpsBattleManager::GetInstance().GetCorpsBattleInfo(CORPS_BATTLE_STUB_TYPE_ELIMINATE, pInfo->friends.GetEliminateGroupID());
		auto pEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(CORPS_BATTLE_STUB_TYPE_ELIMINATE);
		if (pSelf && pEntry)
		{
			info.set_knockout_state(pSelf->battle_info.battle_state());

			auto pTarget = pEntry->GetOpponentCorpsBattleInfo(pInfo->friends.GetEliminateGroupID());
			auto pOrder = pEntry->GetCorpsBattleOrderByID(pInfo->friends.GetEliminateGroupID());
			if (pTarget && pOrder)
			{
				info.mutable_group_info()->set_opponent_name(pTarget->battle_info.name());
				info.mutable_group_info()->set_opponent_zoneid(pTarget->battle_info.zoneid());

				int timestamp = 0;	 // 被淘汰
				if (pSelf->battle_info.battle_state() % 3 == 1)
				{
					timestamp = pOrder->BattleTime(); // 未开始
				}
				else if (pSelf->battle_info.battle_state() % 3 == 0)
				{
				}

				auto battle_result = pOrder->Result();
				if (battle_result == -1)
				{
					timestamp = pOrder->BattleTime(); // 未开始
				}
				auto battle_index = battle_result == 1 ? pOrder->CorpsOneIndex() : (battle_result == 0 ? pOrder->CorpsTwoIndex() : -1);
				if ((battle_index != -1) && (pTarget->Index() != battle_index))
				{
					timestamp = -1;
				}
				info.mutable_group_info()->set_timestamp(timestamp);
			}
		}
	}
	info.set_flag(group.GetFlag());
	info.mutable_fighters()->CopyFrom(group.GetPBInfo().fighters());
	LOG_TRACE("EliminateGroupManager::SendEliminateGroupInfo:roleid=" PRINT64":stage=%d:group_id=%ld:fighters=%d:pbinfo=%p:eliminate_group_type=%d", pInfo->roleid, (int)stage, group.GetGroupID(), group.GetPBInfo().fighters_size(), &(group.GetPBInfo()), GLOBAL_CONFIG.eliminate_group_type);

	int battle_count = 0, win_count = 0, consecutive_win_count = 0;
	ARENA::ArenaManager::GetInstance().GetBattleInfo(pInfo->roleid, 6, battle_count, win_count, consecutive_win_count);
	info.set_battle_count(battle_count);
	info.set_win_count(win_count);
	info.set_consecutive_win(consecutive_win_count);

	pInfo->SendMessage2Client(info);
}
void EliminateGroupManager::OnTopListLoadFinish()
{
	int top_tid = GetEliminateGroupTopTid(GLOBAL_CONFIG.eliminate_group_type);
	if (top_tid <= 0)
	{
		return;
	}
	LOG_TRACE("EliminateGroupManager::OnTopListLoadFinish:top_tid=%d:eliminate_group_type=%d", top_tid, GLOBAL_CONFIG.eliminate_group_type);
	TPManager::GetInstance().DebugDump(top_tid);

	//加载排行榜前10
	std::vector<DSTopListKey> topN;
	DSTPManager::GetInstance().GetTopN(top_tid, GetMatchCenterToplistCount(), topN);
	for (size_t i = 0; i < topN.size(); ++i)
	{
		DBEliminateGroupGet *rpc = (DBEliminateGroupGet *) Rpc::Call(RPC_DBELIMINATEGROUPGET, DBEliminateGroupGetArg(topN[i].id));
		GameDBClient::GetInstance()->SendProtocol(rpc);
		LOG_TRACE("EliminateGroupManager::OnTopListLoadFinish::LoadTop10EliminateGroup::groupid=%ld:rank=%zu:eliminate_group_type=%d", topN[i].id, i, GLOBAL_CONFIG.eliminate_group_type);
	}

}
void EliminateGroupManager::UpdateTopList(ruid_t eliminategroupid, int grade, const Octets& name)
{
	//TODO:非本服战，不更新排行榜
	if (EliminateBattleManager::GetInstance().GetState() != PB::EBS_LOCAL_SCORE_BATTLE_BEGIN)
	{
		return;
	}
	int top_tid = GetEliminateGroupTopTid(GLOBAL_CONFIG.eliminate_group_type);
	if (top_tid <= 0)
	{
		return;
	}

	DSTPManager::GetInstance().UpdateItem(top_tid, eliminategroupid, (int64_t)grade, name);

	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}

	//获取一下排名
	//从DSTPManager到TPManager数据同步是异步进行的，此处取的排名是战队上一次的排行
	//先注掉，每次都取前十也还好
	/*
	int rank = TPManager::GetInstance().GetSelfRank(top_tid, eliminategroupid, NULL);
	LOG_TRACE("EliminateGroupManager::UpdateTopList::eliminategroupid=%ld:grade=%d:rank=%d", eliminategroupid, grade, rank);
	if (rank <= 0 || rank > GetMatchCenterToplistCount())
	{
		//不需要更新全局数据
		return;
	}
	*/
	//更新到PB数据中
	std::vector<DSTopListKey> topN;
	DSTPManager::GetInstance().GetTopN(top_tid, GetMatchCenterToplistCount(), topN);
	PB::db_eliminate_group_ranks m_local_rank_cache;
	for (size_t i = 0; i < topN.size(); ++i)
	{
		auto pRank = m_local_rank_cache.add_ranks();
		pRank->set_eliminate_group_id(topN[i].id);
		pRank->set_group_grade(topN[i].value);
		pRank->set_timestamp(topN[i].changetime);
	}
	Octets data = PB2Octets(m_local_rank_cache);
	if (!ipd->ip_global_data_manager.SetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_ELIMINATE_BATTLE_RANK), data))
	{
		Log::log(LOG_ERR, "EliminateGroupManager::UpdateTopList::SaveLocalRank m_local_rank_cache failed to setdata");
	}
}
void EliminateGroupManager::TryLoadEliminateGroupRanks()
{
	if (m_rank_init)
	{
		return;
	}

	if (!GameDBClient::GetInstance()->IsConnect())
	{
		return;
	}
	INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}
	if (!ipd->ip_global_data_manager.IsInit())
	{
		return;
	}

	PB::db_eliminate_group_ranks m_local_rank_cache;

	Octets value;
	if (ipd->ip_global_data_manager.GetData(IP_GLOBAL_DATA_KEY(IGKT_SERVER_DATA, ISGD_ELIMINATE_BATTLE_RANK), value) && value.size() > 0)
	{
		try
		{
			Octets2PB(value, m_local_rank_cache);
		}
		catch (...)
		{

		}
	}

	m_rank_init = true;
	LOG_TRACE("EliminateGroupManager::TryLoadEliminateGroupRanks::m_local_rank_cache=%d", m_local_rank_cache.ranks_size());
	for (int i = 0; i < m_local_rank_cache.ranks_size(); ++i)
	{
		auto& rank = m_local_rank_cache.ranks(i);
		LOG_TRACE("EliminateGroupManager::TryLoadEliminateGroupRanks::eliminate_group_id=%ld:grade=%d:timestamp=%d", rank.eliminate_group_id(), rank.group_grade(), rank.timestamp());

		//尝试加载排行榜前10的玩家
		//DBEliminateGroupGet *rpc = (DBEliminateGroupGet *) Rpc::Call(RPC_DBELIMINATEGROUPGET, DBEliminateGroupGetArg(rank.eliminate_group_id()));
		//GameDBClient::GetInstance()->SendProtocol(rpc);
	}
}

void EliminateGroup::OnDismiss()
{
	SetSaving(true);

	DBEliminateGroupSaveArg arg;
	GEliminateGroupSave ags;
	ags.eliminategroup.info.id = m_eliminate_group.info.id;
	ags.operation = AGST_DELETE;
	arg.eliminategroups.push_back(ags);
	DBEliminateGroupSave *rpc = (DBEliminateGroupSave *) Rpc::Call(RPC_DBELIMINATEGROUPSAVE, arg);
	GameDBClient::GetInstance()->SendProtocol(rpc);
}



}
}
