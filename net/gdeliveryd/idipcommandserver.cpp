#include "state.hxx"
#include "gprotoc/ipt_account_pursue.pb.h"
#include "gprotoc/ipt_query_task_status.pb.h"
#include "gprotoc/roam_friend_t.pb.h"
#include "gprotoc/db_secure_idip_command.pb.h"
#include "gprotoc/idip_role_info.pb.h"
#include "gprotoc/npt_text_msg.pb.h"
#include "gprotoc/npt_group_op.pb.h"
#include "gprotoc/ipt_eliminate_battle_notify.pb.h"
#include "gprotoc/ipt_gm_cmd.pb.h"
#include "gprotoc/mailtoalldata_pbinfo.pb.h"
#include "gprotoc/db_secure_idip.pb.h"
#include "gprotoc/eliminate_group.pb.h"
#include "gprotoc/npt_harmonious_notify.pb.h"
#include "gprotoc/reputation.pb.h"
#include "gprotoc/npt_secure_idip.pb.h"
#include "gprotoc/npt_notify_forbid_mask.pb.h"
#include "gprotoc/npt_server_group_open_time_change.pb.h"
#include "gprotoc/db_dyn_giftbag_data.pb.h"
#include "gprotoc/db_account_ds_data.pb.h"
#include "gprotoc/ipt_idip_modify_content.pb.h"
#include "gprotoc/face_book.pb.h"
#include "gprotoc/idip_info_t.pb.h"
#include "gprotoc/ipt_idip_set_face_book.pb.h"
#include "gprotoc/ipt_clear_card.pb.h"
#include "gprotoc/item_data.pb.h"
#include "gprotoc/ROLE_TRADE_STATUS.pb.h"
#include "gprotoc/ipt_role_trade_check.pb.h"
#include "gprotoc/ROLE_TRADE_REQ_TYPE.pb.h"

#include "conv_charset.h"
#include "idipcommandserver.hpp"
#include "gamedbclient.hpp"
#include "netplayer.h"
#include "getuser.hrp"
//#include "kickoutuser.hpp"
#include "d2lforbidchat.hpp"
#include "chatpublic.hpp"
#include "dbmailsend.hrp"
#include "dbmailsendaccount.hrp"
#include "dbforbiduser.hrp"
#include "dbputforbid.hrp"
#include "dbsecureidip.hrp"
#include "putmaildatarpc.hrp"
#include "listmailallrpc.hrp"
#include "deletemailallrpc.hrp"
#include "dbactiveuser.hrp"
#include "syncfuncswitch.hpp"
#include "campaignmanager.h"
#include "slog.h"
#include "groleinfo"
#include "special_id_config_manager.h"
#include "announcemanager.h"
#include "setupconnection.hpp"
#include "idip_data_manager.h"
#include "midas_recharge_manager.h"
#include "tpmanager.h"
#include "grcmanager.h"
#include "setrechargeversionrpc.hrp"
#include "gauction/gauction_manager.h"
#include "pre_create_server.h"
#include "precreatechat.hpp"
#include "fauction/fauction_manager.h"
#include "instancemanager.h"
#include "uniquenameclient.hpp"
#include "corps_auction_manager.h"
#include "allocname.hrp"
#include "allocrolerename.hrp"
#include "group_manager.h"
#include "gauanyclient.hpp"
#include "eliminategroupmanager.h"
#include "onlineinfoclient.hpp"
#include "queryrolefreecash.hrp"
#include "grcclient.hpp"
#include "usecashandsenditem.hrp"
#include "centerbattle.h"
#include "community_manager.h"
#include "corps_eliminate_battle.h"
#include "corps_season_manager.h"
#include "diaoxiang_manager.h"
#include "corps_manager.h"
#include "debugcommand.hpp"
#include "commonmacro.h"
#include "roam_community_manager.h"
#include "popfacemanager.h"

//req
#include "idipcmdreqcommon"
#include "idipcmdreqforbid"
#include "idipcmdrequnforbid"
#include "idipcmdreqsendmail"
#include "idipcmdreqaqmodifymoney"
#include "idipcmdreqchangeplayerlimit"
#include "idipcmdreqaqsendmessage"
#include "idipcmdreqaqforbidtoplist"
#include "idipcmdreqannounce"
#include "idipcmdreqqueryannounce"
#include "idipcmdreqdeleteannounce"
#include "idipcmdreqopenactivity"
#include "idipcmdreqdebug"
#include "idipcmdreqqueryrolebyname"
#include "idipcmdreqaqbangame"
#include "idipcmdreqchangedata"
#include "idipcmdreqswitchwhitelist"
#include "idipcmdreqactivateaccount"
#include "idipcmdreqqueryrolereputation"
#include "idipcmdreqtask"
#include "idipcmdreqquerymailall"
#include "idipcmdreqdeletemailall"
#include "idipcmdreqmailall"
#include "idipcmdreqchangedata2"
#include "idipcmdreqshareactivityinvoke"
#include "idipcmdreqsetscore"
#include "idipcmdreqcashpursue"
#include "idipcmdreqimagelist"
#include "idipcmdrequpdateimage"
#include "idipcmdreqaddimage"
#include "idipcmdreqzkremind"
#include "idipcmdreqzkban"
#include "idipcmdreqzkverify"
#include "idipcmdreqforbidtoplist"
#include "idipcmdsetcorpsmsg"
#include "idipcmdrequnban"
#include "idipcmdreqmonitorbanlogin"
#include "idipcmdaqdoallzonebanaccountreq"
#include "idipcmdaqdoallzonebanchatreq"
#include "idipcmddobandevicereq"
#include "idipcmdreqdelsocialspacemsg"
#include "idipcmdreqmodifysocialspace"
#include "idipcmdreqchangesocialspacesign"
#include "idipcmdreqbansocialspace"
#include "idipcmddimissgroupreq"
#include "idipcmdcorpsrename"
#include "idipcmdforbidplayername"
#include "idipcmdusecashandsenditem"
#include "idipcmdsspworkreq"
#include "idipcmdreqdelzspacebulletin"
#include "idipcmdreqbanzspaceuploadbulletin"

//rsp
#include "idipreqpacketcmd"
//#include "idipcmdrsp"
#include "idipcmdrspqueryusrinfo"
#include "idipcmdrspqueryroleinfo"
#include "idipcmdrspqueryopenidinfo"
#include "idipcmdrspaqlistrole"
#include "idipcmdrspqueryrolecorp"
#include "idipcmdrspqueryrolereputation"
#include "idipcmdrsptask"
#include "idipcmdrspqueryannounce"
#include "idipcmdrspquerymailall"
#include "idipcmdrspmailall"
#include "idipcmdrspqueryrolebriefinfo"
#include "idipcmdrspannounce"
#include "idipcmdrspqueryroleauction"
#include "idipcmdrspqueryscoreinfo"
#include "idipcmdrspquerypursue"
#include "idipcmdrspqueryrolefreecash"
#include "idipcmdrspqueryrolediamondcount"
#include "idipcmdusediamondandsenditem"
#include "idipcmdforbidmodifycontent"
#include "idipcmdmodifycontent"
#include "gtoplistrecord"
#include "idipcmdreqqueryrolemarriage"
#include "idipcmdrspqueryrolemarriage"
#include "idipcmdreqroletradequerybriefinfo"
#include "idipcmdrsproletradequerybriefinfo"
#include "idipcmdreqroletraderolelist"
#include "idipcmdrsproletraderolelist"
#include "idipcmdreqroletradedetail"
#include "idipcmdrsproletradedetail"
#include "idipcmdreqroletradequeryroletag"
#include "idipcmdrsproletradequeryroletag"
#include "idipcmdreqroletradequeryroleevaluation"
#include "idipcmdrsproletradequeryroleevaluation"
#include "idipcmdreqroletradequeryfilter"
#include "idipcmdrsproletradequeryfilter"
#include "idipcmdreqroletradesetstatus"
#include "idipcmdrsproletradesetstatus"
#include "idipcmdreqroletradetransfer"
#include "idipcmdrsproletradetransfer"
#include "idipcmdreqroletradetransferrollback"
#include "idipcmdrsproletradetransferrollback"
#include "idipcmdreqroletradegetstatus"
#include "idipcmdrsproletradegetstatus"
#include "idipcmdreqroletradechecksell"
#include "idipcmdrsproletradechecksell"
#include "idipcmdreqroletradecheckbuy"
#include "idipcmdrsproletradecheckbuy"
#include "idipcmdrsproletradecheckserver"
#include "idipcmdreqpopfaceadd"
#include "idipcmdreqpopfacequery"
#include "idipcmdpopfaceinfo"
#include "idipcmdrsppopfacequery"
#include "idipcmdreqpopfacedelete"
#include "roletradesummaryinfo"
#include "roletradebriefinfo"
#include "roletraderolelist"
#include "roletraderoledetail"
#include "roletradetaginfo"
#include "roletraderoletag"
#include "roletraderoleevaluation"
#include "roletradefilterinfo"
#include "roletraderolefilter"
#include "roletradecheckret"

#include "dbroletrade_setstate.hrp"
#include "dbroletrade_transfer.hrp"
#include "unamedroletrade_transfer.hrp"

#include "gantiwallowclient.hpp"
#include "antiwallow_manager.h"

#include "gantiwallowclient.hpp"
#include "antiwallow_manager.h"
#include "funcswitchmgr.h"
#include "centerarenateambattle.h"
#include "pve_arena_group_rank_manager.h"

#ifdef USE_IDIP_PROXY
#include "gidipproxyclient.hpp"
#include "databetweenidipandgamerep.hpp"
#endif

#include "dbidipmarriage.hrp"
#include "honey_garden_manager.h"
#include "zspace_manager.h"
#include "hundred_corps_battle_man.h"
#include "roletrademanager.h"
#include <zlib.h>
#include "base64.h"
#include "pbjson.hpp"

#ifdef USE_TCMALLOC
#include <gperftools/heap-profiler.h>
#include <gperftools/malloc_extension.h>
#include <gperftools/profiler.h>
#endif

namespace GNET
{
// ds没定义过这个错误码，这是zslib_classical的错误码，只好这里手动定义了
const int ERR_NOTFOUND = 43; 

#define IDIP_ACCOUNT(req) Openid2Account(Octets((char *)req.OpenId.c_str(), req.OpenId.size()), req.AreaId)

bool ip_whitelist_enabled = true;

class GetRoleListHelper
{
	std::set<int64_t> id_list;
	std::vector<int64_t> info_list;
public:
	GetRoleListHelper(std::vector<int64_t>& list) : id_list(list.begin(), list.end()) {}
	std::function<void (const std::vector<int64_t> & info_list)> call_back;
	void Prepare()
	{
		for (std::set<int64_t>::iterator id_it = id_list.begin(); id_it != id_list.end(); /*nothing*/)
		{
			std::set<int64_t>::iterator it_this = id_it++;
			RoleInfo *p_role = RoleMap::Instance().Find(*it_this);
			if (p_role != NULL)
			{
				info_list.push_back(*it_this);
				id_list.erase(it_this);
			}
		}
	}
	void Run()
	{
		std::set<int64_t>::iterator it = id_list.begin();
		if (it == id_list.end())
		{
			Finish();
			return;
		}
		getroleinfo_handle callback = std::bind(&GetRoleListHelper::OnGetRoleInfo, this, std::placeholders::_1,
		                                        std::placeholders::_2, std::placeholders::_3);
		RoleMap::GetRoleInfo(*it, Octets(), &callback);
	}
	void OnGetRoleInfo(int retcode, ruid_t roleid, RoleInfo *p_role)
	{
		info_list.push_back(roleid);
		id_list.erase(roleid);

		Run();
	}
	void Finish()
	{
		call_back(info_list);
		delete this;
	}
};

void MakeMailPBInfo(ProtocBuf& target, int user = MA_USER_BOTH, int level = 0, std::string version = std::string())
{
	PB::mailtoalldata_pbinfo pbinfo;
	pbinfo.set_level_limit(level);
	pbinfo.set_user_type(user);
	pbinfo.set_version(version);

	target.type = PB::GUT_MAILALL_DATA;
	try
	{
		target.value = PB2Octets(pbinfo);
		target.type = PB::GUT_MAILALL_DATA;
	}
	catch (...)
	{
		SLOG(ERR, "DS::MakeMailPBInfo parse err").P("user", user).P("level", level).P("version", version);
	}
}

std::string GetImageUserid(const std::string& openid, int areaid) //openid + 渠道号
{
	if (areaid > 90 && openid.size() > 0)
	{
		if (openid[0] == 'o')
		{
			areaid = 1;
		}
		else
		{
			areaid = 2;
		}
	}
	static const char *sub_fix[4] = {"$wechat", "$qq", "$qq", "$shadow"};
	--areaid;
	const char *sub = sub_fix[areaid % 4];
	return openid + sub;
}

void IDIPHandler::Send(const std::string& result)
{
#ifdef USE_IDIP_PROXY
	IdipProxyManager::Instance().SendResponse(xid, proxyid, result);
#else
	IDIPCommandServer::GetInstance()->SendResponse(web_sid, result);
#endif
}
void IDIPHandler::MakePBData(const IDIPCmdHead& req_head, PB::idip_info_t& idip)
{
	idip.set_cmdid(req_head.Cmdid);
	idip.set_seqid(req_head.Seqid);
	idip.set_servicename(req_head.ServiceName);
	idip.set_sendtime(req_head.SendTime);
	idip.set_version(req_head.Version);
	idip.set_authenticate(req_head.Authenticate);
	idip.set_xid(xid);
	idip.set_proxyid(proxyid);
}

void MakeSecureIDIPProcess(int64_t role_id, int op_type, int64_t para1 = 0, int64_t para2 = 0, const Octets& para3 = Octets(), DBSecureIDIP::callback *cb = nullptr)
{
	bool role_online = false;
	RoleInfo *p_role = RoleMap::Instance().FindOnline(role_id);
	if (p_role != NULL)
	{
		role_online = true;
	}
	SLOG(FORMAT, "MakeSecureIDIPProcess").P("roleid", role_id).P("role_online", role_online).P("op_type", op_type).P("para1", para1).P("para2", para2);

	DBSecureIDIPArg arg;
	arg.roleid = role_id;
	arg.role_online = role_online;
	arg.operate_type = op_type;
	arg.operate_param1 = para1;
	arg.operate_param2 = para2;
	arg.operate_param3 = para3;

	DBSecureIDIP *rpc = (DBSecureIDIP *) Rpc::Call(RPC_DBSECUREIDIP, arg);
	if (cb)
	{
		rpc->cbctx.cb = cb;
	}
	GameDBClient::GetInstance()->SendProtocol(rpc);
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//1.邮件赠送道具
struct idip_sendmail : public idip_req_body<IDIPCmdReqSendMail>
{
	idip_sendmail() : idip_req_body()
	{}
	virtual int Serve() override;

	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	void SendResponse(int retcode, std::multimap<int64_t, int>& item_list);
	static void CallBack_SendMail(idip_sendmail *p_request, int retcode, std::multimap<int64_t, int>& item_list);
};

int idip_sendmail::Serve()
{
	class SendMailList : public DBMailSend::callback, public DBMailSendAccount::callback
	{
		int64_t dst_roleid;
		Octets ucs2_title;
		Octets ucs2_content;
		Octets dst_account;
		std::multimap<int64_t, int> item_list;
	public:
		bool haveAttachment = true;
		std::function<void (int, std::multimap<int64_t, int>&)> call_back;

		SendMailList(int64_t roleid, Octets account) : dst_roleid(roleid), dst_account(account) { }
		virtual ~SendMailList() {}

		void SetMailContent(const std::string& title, const std::string& content)
		{
			std::string utf8_title = HttpProtocol::UrlDecode(title);
			std::string utf8_content = HttpProtocol::UrlDecode(content);

			CharsetConverter::conv_charset_t2u(Octets(utf8_title.c_str(), utf8_title.size()), ucs2_title);
			CharsetConverter::conv_charset_t2u(Octets(utf8_content.c_str(), utf8_content.size()), ucs2_content);
		}
		void AddMailItem(int64_t item_tid, int item_count)
		{
			if (item_tid != 0)
			{
				item_list.insert(std::make_pair(item_tid, item_count));
			}
		}
		void MakeMail(Mail& mail, int64_t to_roleid, int item_id, int item_count)
		{
			mail.header.category = CATEGORY_MAIL_SYSTEM | MAIL_CATEGORY_IDIPGIFT;
			mail.header.subject = ucs2_title;
			mail.header.from = ((int64_t)item_id << 32) | (item_count); //为了方便客户端，在header的from字段里填入物品id和数量
			mail.header.to = to_roleid;
			mail.header.date = Timer::GetTime();
			mail.header.status = MAIL_STATUS_ATTACHED;
			mail.header.msgid = 0;
			mail.context = ucs2_content;

			if (item_count <= 0)
			{
				mail.header.status = MAIL_STATUS_NEW;
				return;
			}

			GRoleInventory item;
			item.id = item_id;
			item.count = item_count;
			/*if (item_id == DIAMOND_BAG_ITEM_ID)
			{
				item.count = 1;
				//这两个是钻石袋
				Octets item_data;
				item_data.insert(item_data.end(), &item_count, sizeof(item_count));
				item.data = item_data;
			}*/
			mail.attachment.items.push_back(item);
		}
		void SendMail()
		{
			int item_tid = 0, item_count = 0;
			std::multimap<int64_t, int>::iterator it = item_list.begin();
			if (haveAttachment)
			{
				if (it == item_list.end())
				{
					Finish(0);
					return;
				}
				item_tid = it->first;
				item_count = it->second;
			}

			if (dst_roleid == 0)
			{
				int64_t online_roleid = 0;
				UserInfo *user = UserContainer::Instance().FindUser(dst_account);
				if (user != NULL && user->roleid != 0)
				{
					RoleInfo *p_role = RoleMap::Instance().Find(user->roleid);
					if (p_role != NULL && p_role->IsOnline())
					{
						online_roleid = user->roleid;
					}
				}

				DBMailSendAccountArg arg;
				arg.dst_account = dst_account;
				arg.inform_roleid = online_roleid;
				MakeMail(arg.mail, 0, item_tid, item_count);

				DBMailSendAccount *rpc = (DBMailSendAccount *) Rpc::Call(RPC_DBMAILSENDACCOUNT, arg);
				rpc->cbctx.cb = this;
				GameDBClient::GetInstance()->SendProtocol(rpc);
			}
			else
			{
				DBMailSendArg arg;
				arg.dst.id = dst_roleid;
				MakeMail(arg.mail, dst_roleid, item_tid, item_count);

				DBMailSend *rpc = (DBMailSend *) Rpc::Call(RPC_DBMAILSEND, arg);
				rpc->linksid = 0;
				rpc->cbctx.cb = this;
				GameDBClient::GetInstance()->SendProtocol(rpc);
			}

			if (haveAttachment)
			{
				item_list.erase(it);
			}
		}
		void OnSendBack(int retcode)//call next sendmail
		{
			if (retcode != 0 || !haveAttachment)
			{
				Finish(retcode);
				return;
			}
			else
			{
				SendMail();
			}
		}
		virtual void OnCallBack(const DBMailSendArg& arg, const DBMailSendRes& res)
		{
			LOG_TRACE("SendMailList. MailSendOnCallBack. retcode = %d", res.retcode);
			OnSendBack(res.retcode);
			return;
		}
		virtual void OnTimeout(const DBMailSendArg& arg)
		{
			LOG_TRACE("SendMailList. MailSendOnTimeout.");
			OnSendBack(-1);
			return;
		}
		virtual void OnCallBack(const DBMailSendAccountArg& arg, const DBMailSendAccountRes& res)
		{
			LOG_TRACE("SendMailList. MailSendAccountOnCallBack. retcode = %d", res.retcode);
			OnSendBack(res.retcode);
			return;
		}
		virtual void OnTimeout(const DBMailSendAccountArg& arg)
		{
			LOG_TRACE("SendMailList. MailSendAccountOnTimeout.");
			OnSendBack(-1);
			return;
		}
		void Finish(int finish_result)
		{
			call_back(finish_result, item_list);
			delete this;
		}
	};

	Octets open_account = IDIP_ACCOUNT(req);

	std::stringstream ss;

	SendMailList *send_list = new SendMailList(req.RoleId, open_account);
	send_list->SetMailContent(req.MailTitle, req.MailContent);
	for (size_t i = 0; i < req.ItemList.size(); ++i)
	{
		send_list->AddMailItem(req.ItemList[i].ItemId, req.ItemList[i].ItemNum);
		ss << req.ItemList[i].ItemId << "," << req.ItemList[i].ItemNum << ";";
		SLOG(FORMAT, "idip_command")
		.P("vopenid", req.OpenId)
		.P("iAttribute", req.ItemList[i].ItemId)
		.P("iValue", req.ItemList[i].ItemNum)
		.P("vSerial", req.Serial)
		.P("iSource", req.Source)
		.P("iCmdID", req_head.Cmdid);
	}
	SLOG(FORMAT, "idip_sendmail")
	.P("roleid", req.RoleId)
	.P("open_account", open_account)
	.P("items", ss.str());

	send_list->call_back = std::bind(&idip_sendmail::CallBack_SendMail, this, std::placeholders::_1, std::placeholders::_2);
	if (IDIP_AQ_DO_SEND_MAIL_REQ == req_head.Cmdid)
	{
		send_list->haveAttachment = false;
	}
	send_list->SendMail();
	return HANDLE_RET_PENDING;
}
void idip_sendmail::SendResponse(int retcode, std::multimap<int64_t, int>& item_list)
{
	string retmsg = "success";
	if (retcode == ERROR_MAIL_BOXFULL)
	{
		retcode = IDIP_ERR_MAILBOX_FULL;
		retmsg = "mail fulled";
	}
	else if (retcode == ERROR_ROLE_NOTFOUND || retcode == ERROR_DB_NOTFOUND)
	{
		retcode = IDIP_ERR_INVALID_ROLE;
		retmsg = "not exist";
	}
	IDIPSend(retcode, retmsg);
}
void idip_sendmail::CallBack_SendMail(idip_sendmail *p_request, int retcode, std::multimap<int64_t, int>& item_list)
{
	p_request->SendResponse(retcode, item_list);
	delete p_request;
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//2.封号
struct idip_forbid : public idip_req_body<IDIPCmdReqForbid>
{
public:
	idip_forbid() { }

	virtual int Serve() override;
	void SendResponse(int retcode);

	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	static void CallBack_ForbidUser(idip_forbid *p_req, ForbidUserArg *arg, DBForbidUserRes *res);
	static void CallBack_ForbidRole(idip_forbid *p_req, DBPutForbidArg *arg, DBPutForbidRes *res);

	void GetRoleData(int64_t role_id);

	static void CallBack_RoleList(idip_forbid *p_req, const std::vector<int64_t>& info_list);
};
void idip_forbid::SendResponse(int retcode)
{
	std::string ret_msg;
	if (retcode == 0 || retcode == 1)
	{
		ret_msg = "success";

		SLOG(FORMAT, "idip_command")
		.P("vopenid", req.OpenId)
		.P("iAttribute", req.RoleId)
		.P("iValue", req.BanTime)
		.P("vDesc", req.BanReason)
		.P("vSerial", req.Serial)
		.P("iSource", req.Source)
		.P("iCmdID", req_head.Cmdid);
	}
	else
	{
		ret_msg = "failed";
	}

	IDIPSend(retcode, ret_msg);
}
int idip_forbid::Serve()
{

	SLOG(FORMAT, "idip_forbid")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId)
	.P("time", req.BanTime);

	Octets user_account = IDIP_ACCOUNT(req);
	req.BanReason = HttpProtocol::UrlDecode(req.BanReason);

	Octets utf8_des(req.BanReason.c_str(), req.BanReason.size());
	Octets ucs2_des;
	CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

	if (req.RoleId == 0)
	{
		ForbidUserArg arg;
		arg.operation = (req.BanTime == 0) ? GNET_UNFORBID_LOGIN : GNET_FORBID_LOGIN; //时间为0表示解禁
		arg.account = user_account;
		arg.time = req.BanTime;
		arg.reason = ucs2_des;

		DBForbidUser *rpc = (DBForbidUser *)Rpc::Call(RPC_DBFORBIDUSER, arg);

		rpc->call_back = std::bind(&idip_forbid::CallBack_ForbidUser, this, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
		if (p_role != NULL)
		{
			if (p_role->account != user_account)
			{
				SendResponse(IDIP_ERR_INVALID_ROLE);
				return HANDLE_RET_FINISH;
			}
			else
			{
				GRoleForbid forbid(GNET_FORBID_LOGIN, req.BanTime, Timer::GetTime(), ucs2_des);
				DBPutForbidArg arg(req.RoleId, forbid);
				DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

				rpc->call_back = std::bind(&idip_forbid::CallBack_ForbidRole, this, std::placeholders::_1, std::placeholders::_2);

				GameDBClient::GetInstance()->SendProtocol(rpc);
				return HANDLE_RET_PENDING;
			}
		}
		else
		{
			GetRoleData(req.RoleId);
			return HANDLE_RET_PENDING;
		}
	}

	return HANDLE_RET_FINISH;
}
void idip_forbid::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_forbid::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_forbid::CallBack_RoleList(idip_forbid *p_req, const std::vector<int64_t>& info_list)
{
	Octets user_account = p_req->IDIP_ACCOUNT(p_req->req);
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL || p_role->account != user_account)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
		delete p_req;
	}
	else
	{
		Octets utf8_des(p_req->req.BanReason.c_str(), p_req->req.BanReason.size());
		Octets ucs2_des;
		CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

		GRoleForbid forbid(GNET_FORBID_LOGIN, p_req->req.BanTime, Timer::GetTime(), ucs2_des);
		DBPutForbidArg arg(roleid, forbid);
		DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

		rpc->call_back = std::bind(&idip_forbid::CallBack_ForbidRole, p_req, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
}
void idip_forbid::CallBack_ForbidUser(idip_forbid *p_req, ForbidUserArg *arg, DBForbidUserRes *res)
{
	if (res->retcode == ERR_NOTFOUND)
	{
		res->retcode = IDIP_ERR_INVALID_ACCOUNT;
	}
	if (res->retcode == ERROR_FORBID_IGNORE)
	{
		res->retcode = 0;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}
void idip_forbid::CallBack_ForbidRole(idip_forbid *p_req, DBPutForbidArg *arg, DBPutForbidRes *res)
{
	if (res->retcode == -1)
	{
		res->retcode = IDIP_ERR_INVALID_ROLE;
	}
	if (res->retcode == ERROR_FORBID_IGNORE)
	{
		res->retcode = 0;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//3.解封
struct idip_unforbid : public idip_req_body<IDIPCmdReqUnforbid>
{
public:
	idip_unforbid() : idip_req_body() {}

	virtual int Serve() override;
	void SendResponse(int retcode);

	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	static void CallBack_unForbidUser(idip_unforbid *p_req, ForbidUserArg *arg, DBForbidUserRes *res);
	static void CallBack_unForbidRole(idip_unforbid *p_req, DBPutForbidArg *arg, DBPutForbidRes *res);
};
void idip_unforbid::SendResponse(int retcode)
{
	std::string ret_msg;
	if (retcode == 0 || retcode == 1)
	{
		ret_msg = "success";
	}
	else
	{
		ret_msg = "failed";
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	IDIPSend(retcode, ret_msg);
}
int idip_unforbid::Serve()
{
	SLOG(FORMAT, "idip_unforbid")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId);

	Octets user_account = IDIP_ACCOUNT(req);
	if (req.RoleId == 0)
	{
		ForbidUserArg arg;
		arg.operation = GNET_UNFORBID_LOGIN;//解封
		arg.account = user_account;
		arg.time = 0;

		DBForbidUser *rpc = (DBForbidUser *)Rpc::Call(RPC_DBFORBIDUSER, arg);

		rpc->call_back = std::bind(&idip_unforbid::CallBack_unForbidUser, this, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		GRoleForbid forbid(GNET_FORBID_LOGIN, 0, Timer::GetTime());
		DBPutForbidArg arg(req.RoleId, forbid);
		DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

		rpc->call_back = std::bind(&idip_unforbid::CallBack_unForbidRole, this, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	return HANDLE_RET_FINISH;
}
void idip_unforbid::CallBack_unForbidUser(idip_unforbid *p_req, ForbidUserArg *arg, DBForbidUserRes *res)
{
	if (res->retcode == ERR_NOTFOUND)
	{
		res->retcode = IDIP_ERR_INVALID_ACCOUNT;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}
void idip_unforbid::CallBack_unForbidRole(idip_unforbid *p_req, DBPutForbidArg *arg, DBPutForbidRes *res)
{
	if (res->retcode == -1)
	{
		res->retcode = IDIP_ERR_INVALID_ROLE;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//4.查询角色列表
struct idip_listrole : public idip_req_body<IDIPCmdReqCommon>
{
public:
	idip_listrole() : idip_req_body() {}

	virtual int Serve() override;
	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);

	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	void GetRoleList(std::vector<int64_t>& role_list);

	static void CallBack_RoleList(idip_listrole *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_listrole *p_req, GetUserArg *arg, GetUserRes *res);
};
int idip_listrole::Serve()
{
	SLOG(FORMAT, "idip_listrole").P("openid", req.OpenId);

	Octets user_account = IDIP_ACCOUNT(req);
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_listrole::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		std::vector<int64_t> role_list = user->GetRoleList();
		GetRoleList(role_list);
		return HANDLE_RET_PENDING;
	}
	return HANDLE_RET_FINISH;
}
void idip_listrole::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = retcode;

	IDIPDataPacket<IDIPCmdRspQueryUsrInfo> rsp;
	rsp.head = req_head;

	int role_num = 0;
	auto& list_value = rsp.body.UsrInfoList;
	for (size_t i = 0; i < info_list.size(); ++i)
	{
		int64_t roleid = info_list[i];
		RoleInfo *p_role = RoleMap::Instance().Find(roleid);
		if (p_role == NULL)
		{
			continue;
		}

		PB::idip_role_info idip_value;
		idip_value.CopyFrom(p_role->show_property.data().idip_info());

		IDIPCmdUserRoleInfo role_value;
		role_value.RoleId = roleid;
#ifdef USE_UTF8
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
		role_value.Diamond = idip_value.cash_bind();
		role_value.GoldCoin = idip_value.money();
		role_value.Fight = p_role->fightingcapacity;
		role_value.Level = p_role->level;
		role_value.BodyType = p_role->body_size;
		role_value.Job = p_role->profession;
		role_value.Sex = p_role->gender;
		role_value.RegisterTime = p_role->create_time;
		role_value.TotalOnlineTime = idip_value.online_time();
		role_value.LastLogoutTime = p_role->logout_time;
		role_value.IsOnline = p_role->IsOnline() ? 0 : 1;
		role_value.TodayLogin = (p_role->IsOnline() || p_role->logout_time >= GetLocalDayBegin()) ? 1 : 0;
		std::string register_channel, login_channel;
		if (UserContainer::Instance().GetChannelInfo(p_role->account, register_channel, login_channel))
		{
			role_value.RegisterChannel = UrlEncode2(register_channel);
			role_value.LoginChannel = UrlEncode2(login_channel);
		}

		list_value.push_back(role_value);
		role_num ++;
	}

	rsp.body.UsrInfoList_count = role_num;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_listrole::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}
void idip_listrole::GetRoleList(std::vector<int64_t>& role_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_listrole::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_listrole::CallBack_RoleList(idip_listrole *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}
void idip_listrole::CallBack_GetUser(idip_listrole *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_listrole::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->SendResponse(1, std::vector<int64_t>());
		delete p_req;
	}
	else
	{
		std::vector<int64_t> role_list;
		User& u = res->value;
		for (size_t i = 0; i < u.rolelist.size(); ++i)
		{
			role_list.push_back(u.rolelist[i]);
		}
		p_req->GetRoleList(role_list);
	}
}

// AQ 查询账号下角色信息
struct idip_query_roles_of_account : public idip_listrole
{
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	void SendResponse(int retcode, const std::vector<int64_t>& info_list) override;
};

void idip_query_roles_of_account::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = retcode;

	IDIPDataPacket<IDIPCmdRspAQListRole> rsp;
	rsp.head = req_head;

	int role_num = 0;
	auto& list_value = rsp.body.RoleList;
	for (size_t i = 0; i < info_list.size(); ++i)
	{
		int64_t roleid = info_list[i];
		RoleInfo *p_role = RoleMap::Instance().Find(roleid);
		if (p_role == NULL)
		{
			continue;
		}

		PB::idip_role_info idip_value;
		idip_value.CopyFrom(p_role->show_property.data().idip_info());

		IDIPCmdAQRoleInfo role_value;
		role_value.RoleId = roleid;
#ifdef USE_UTF8
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
		GRoleForbid forbid;
		int State = p_role->IsForbidLogin(forbid);
		role_value.BanStatus = State;

		list_value.push_back(role_value);
		role_num ++;
	}

	rsp.body.RoleList_count = role_num;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_roles_of_account::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	Send(result);
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////
//5.修改钻石
/*
struct idip_modify_diamond : public idip_req_body
{
	int Type;		//0到角色，1到账户
	int Value;		//修改数量，- 减, + 加
	unsigned int Source;	//邮件发送来源，流水号
	std::string Serial;	//序列号
public:
	idip_modify_diamond() : idip_req_body()
	{}
	idip_modify_diamond(const idip_modify_diamond & rhs) : idip_req_body(rhs),
		Type(rhs.Type), Value(rhs.Value), Source(rhs.Source), Serial(rhs.Serial)
	{}
	idip_modify_diamond * Clone() { return new idip_modify_diamond(*this); }

	virtual bool Parse(const Json::Value & json_content);
	virtual int Serve();

	void SendResponse(int retcode);
};
bool idip_modify_diamond::Parse(const Json::Value & json_content)
{
	if (!idip_req_body::Parse(json_content))
		return false;

	if (!json_content.isMember("body"))
		return false;

	Json::Value json_value = json_content["body"];

	if (!json_parse_value(json_value, "Type", Type))
		return false;
	if (!json_parse_value(json_value, "Value", Value))
		return false;
	if (!json_parse_value(json_value, "Source", Source))
		return false;
	if (!json_parse_value(json_value, "Serial", Serial))
		return false;

	return true;
}
int idip_modify_diamond::Serve()
{
	SendResponse(  0);
	return 0;
}
void idip_modify_diamond::SendResponse(*  int retcode)
{
	try
	{
		Json::Value root;
		req_head.Cmdid = IDIP_CMD_RES_MOD_DIAMOND;
		req_head.WriteResponse(root);

		Json::Value body_value;
		json_set_value(body_value, "Result", (int)retcode);
		json_set_value(body_value, "RetMsg", "success");
		json_set_value(body_value, "BeginValue", 0);
		json_set_value(body_value, "EndValue", 0);

		json_set_value(root, "body", body_value);

		Json::FastWriter writer;
		std::string result = writer.write(root);

		Send(result);
	}
	catch(...)
	{
		LOG_TRACE("idip_modify_diamond::SendResponse. catch");
	}
}
*/
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//6.修改金币
/*
struct idip_modify_money : public idip_req_body
{
	int Value;		//修改数量，- 减, + 加
	unsigned int Source;	//邮件发送来源，流水号
	std::string Serial;	//序列号
public:
	idip_modify_money() : idip_req_body()
	{}
	idip_modify_money(const idip_modify_money & rhs) : idip_req_body(rhs),
		Type(rhs.Type), Value(rhs.Value), Source(rhs.Source), Serial(rhs.Serial)
	{}
	idip_modify_money * Clone() { return new idip_modify_money(*this); }

	virtual bool Parse(const Json::Value & json_content);
	virtual int Serve();

	void SendResponse(int retcode);
};
bool idip_modify_money::Parse(const Json::Value & json_content)
{
	if (!idip_req_body::Parse(json_content))
		return false;

	if (!json_content.isMember("body"))
		return false;

	Json::Value json_value = json_content["body"];

	if (!json_parse_value(json_value, "Type", Type))
		return false;
	if (!json_parse_value(json_value, "Value", Value))
		return false;
	if (!json_parse_value(json_value, "Source", Source))
		return false;
	if (!json_parse_value(json_value, "Serial", Serial))
		return false;

	return true;
}
int idip_modify_money::Serve()
{
	SendResponse(0);
	return 0;
}
void idip_modify_money::SendResponse(int retcode)
{
	try
	{
		Json::Value root;
		req_head.Cmdid = IDIP_CMD_RES_MOD_MONEY;
		req_head.WriteResponse(root);

		Json::Value body_value;
		json_set_value(body_value, "Result", (int)retcode);
		json_set_value(body_value, "RetMsg", "success");
		json_set_value(body_value, "BeginValue", 0);
		json_set_value(body_value, "EndValue", 0);

		json_set_value(root, "body", body_value);

		Json::FastWriter writer;
		std::string result = writer.write(root);

		Send(result);
	}
	catch(...)
	{
		LOG_TRACE("idip_modify_money::SendResponse. catch");
	}
}
*/
///////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//8.全服发邮件
struct idip_mail_all : public idip_req_body<IDIPCmdReqMailAll>
{
	idip_mail_all() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	static void CallBack_PutMailData(idip_mail_all *handler, GMailToAllData *arg, MailToAllRet *res);
};
int idip_mail_all::Serve()
{
	class SendMailToAll
	{
		Octets ucs2_title;
		Octets ucs2_content;
		std::multimap<std::string, int> item_list;
		Mail mail_t;

	public:
		SendMailToAll() { }
		void SetMailContent(const std::string& title, const std::string& content)
		{
#ifdef USE_IDIP_PROXY
			std::string utf8_title = HttpProtocol::UrlDecode(title);
			std::string utf8_content = HttpProtocol::UrlDecode(content);
#else
			std::string utf8_title = title;
			std::string utf8_content = content;
#endif

			CharsetConverter::conv_charset_t2u(Octets(utf8_title.c_str(), utf8_title.size()), ucs2_title);
			CharsetConverter::conv_charset_t2u(Octets(utf8_content.c_str(), utf8_content.size()), ucs2_content);
		}
		void AddMailItem(std::string _item_tid, int _item_count)
		{
			item_list.insert(std::make_pair(_item_tid, _item_count));
		}
		Mail MakeMail(int64_t serialid)
		{
			mail_t.header.category = CATEGORY_MAIL_SYSTEM | MAIL_CATEGORY_IDIPGIFT;
			mail_t.header.subject = ucs2_title;
			mail_t.header.to = 0;
			mail_t.header.date = Timer::GetTime();
			mail_t.header.msgid = 0;
			mail_t.context = ucs2_content;

			if (!item_list.size())
			{
				return mail_t;
			}
			mail_t.header.status = MAIL_STATUS_ATTACHED;
			for (auto& it : item_list)
			{
				GRoleInventory item;
				int item_id;
				std::stringstream(it.first) >> item_id;
				item.id = item_id;
				item.count = it.second;
				mail_t.attachment.items.push_back(item);
			}

			/*else
			{
				item.id = DYN_GIFTBAG_ITEMID;
				item.count = 1;
				PB::db_dyn_giftbag_data giftbag;
				for (auto itr = item_list.begin(); itr != item_list.end(); ++itr)
				{
					pos = itr->first.find('_', 0);
					if (pos == std::string::npos)
					{
						LOG_TRACE("web mail pos is parse item type failed '_' illegal.pos:%zu, serialid:%ld", pos, serialid);
						continue;
					}
					std::string item_type = itr->first.substr(0, pos);
					std::string item_sid   = itr->first.substr(pos + 1, itr->first.size() - pos);
					int64_t item_id;
					std::stringstream(item_sid) >> item_id;
					if (item_type == "item")
					{
						AddItemInfoToGiftBag(giftbag, item_id, itr->second, false, 0, NULL, 0);
					}
					else if (item_type == "money")
					{
						LOG_TRACE("web mail get money. num:%d serialid:%ld", itr->second, serialid);
						AddBindMoneyToGiftBag(giftbag, itr->second);
					}
					else if (item_type == "cash")
					{
						LOG_TRACE("web mail get cash. num:%d serialid:%ld", itr->second, serialid);
						AddBindCashToGiftBag(giftbag, itr->second);
					}
					else if (item_type == "repu")
					{
						LOG_TRACE("web mail get repu. id:%ld num:%d serialid:%ld", item_id, itr->second, serialid);
						AddRepuToGiftBag(giftbag, item_id, itr->second);
					}
					else if (item_type == "exp")
					{
						LOG_TRACE("web mail get exp. num:%d serialid:%ld", itr->second, serialid);
						AddExpToGiftBag(giftbag, itr->second);
					}
				}
				item.data = PB2Octets(giftbag);
			}

			mail_t.attachment.items.push_back(item);*/
			return mail_t;
		}
	};

	SendMailToAll  send_mail;
	send_mail.SetMailContent(req.MailTitle, req.MailContent);
	for (auto it_item = req.ItemList.begin(); it_item != req.ItemList.end(); ++it_item)
	{
		std::string item_id = std::to_string(it_item->ItemId);
		int64_t item_num = it_item->ItemNum;
		if (item_id.size() <= 0)
		{
			return HANDLE_RET_FINISH;
		}

		send_mail.AddMailItem(item_id, item_num);

		SLOG(FORMAT, "idip_command")
		//.P("vopenid", req.OpenId)
		.P("iAttribute", item_id)
		.P("iValue", item_num)
		.P("vSerial", req.Serial)
		.P("iSource", req.Source)
		.P("iCmdID", req_head.Cmdid);
	}

	GMailToAllData arg;
	arg.serialid = 0;//req.MailId;
	arg.expiry_time = req.EffectTime;
	arg.mail = send_mail.MakeMail(arg.serialid);
	//TODO
	//arg.begin_create_time = Timer::GetTime();
	//arg.end_create_time = Timer::GetTime();

	MakeMailPBInfo(arg.pbinfo, req.UserType, req.MinLevel, req.MinVersion);

	PutMailDataRpc *rpc = (PutMailDataRpc *) Rpc::Call(RPC_PUTMAILDATARPC, arg);
	rpc->call_back = std::bind(&CallBack_PutMailData, this, std::placeholders::_1, std::placeholders::_2);
	GameDBClient::GetInstance()->SendProtocol(rpc);

	return HANDLE_RET_PENDING;
}
void idip_mail_all::CallBack_PutMailData(idip_mail_all *handler, GMailToAllData *arg, MailToAllRet *res)
{
	int retcode = res->retcode;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_PARAM;
	}

	IDIPDataPacket<IDIPCmdRspMailAll> pck;
	pck.head = handler->req_head;
	++ pck.head.Cmdid;
	pck.body.Result = retcode;
	pck.body.MailId = res->serialid;

	std::string result;
	std::string err_msg = Json_Data2Str(result, pck);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("IDIPHandler::Mail to all. catch cmdid=%d", handler->req_head.Cmdid);
		return;
	}
	handler->Send(result);

	delete handler;
}

///////////////////////////////////
// 查看全服邮件
struct idip_query_mail_all : public idip_req_body<IDIPCmdReqQueryMailAll>
{
	idip_query_mail_all() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBack_QueryMailData(idip_query_mail_all *handler, GListMailToAllRet *res);
};
int idip_query_mail_all::Serve()
{
	ListMailAllRpc *rpc = (ListMailAllRpc *) Rpc::Call(RPC_LISTMAILALLRPC, GListMailToAllArg(req.BeginTime, req.EndTime));
	rpc->call_back = std::bind(&CallBack_QueryMailData, this, std::placeholders::_1);
	GameDBClient::GetInstance()->SendProtocol(rpc);

	return HANDLE_RET_PENDING;
}
void idip_query_mail_all::CallBack_QueryMailData(idip_query_mail_all *handler, GListMailToAllRet *res)
{
	static const int mail_num_per_page = 20;
	int retcode = res->retcode;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_DB_NOTFOUND;
	}

	IDIPDataPacket<IDIPCmdRspQueryMailAll> rsp;
	handler->req_head.Cmdid = handler->req_head.Cmdid + 1;
	handler->req_head.Result = retcode;
	rsp.head = handler->req_head;

	auto const& mails = res->mail_list;
	auto& list = rsp.body.AllzoneMailList;
	int index = 0;
	for (auto const& it : mails)
	{
		++ index;
		if (handler->req.PageNo <= 0 || index > handler->req.PageNo * mail_num_per_page)
		{
			break;
		}
		if (index <= (handler->req.PageNo - 1) * mail_num_per_page)
		{
			continue;
		}

		IDIPCmdMailAllInfo mail;
		mail.MailId = it.serialid;
		Octets utf8_title;
		CharsetConverter::conv_charset_u2t(it.mail.header.subject, utf8_title);
		mail.MailTitle = UrlEncode2(std::string((char *)utf8_title.begin(), utf8_title.size()));

		Octets utf8_content;
		CharsetConverter::conv_charset_u2t(it.mail.context, utf8_content);
		mail.MailContent = UrlEncode2(std::string((char *)utf8_content.begin(), utf8_content.size()));

		list.push_back(mail);
	}
	rsp.body.PageNo = handler->req.PageNo;
	rsp.body.AllzoneMailList_count = list.size();
	rsp.body.TotalPageNo = (mails.size() + mail_num_per_page - 1) / mail_num_per_page;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_listmailall::SendResponse. catch cmdid=%d", handler->req_head.Cmdid);
		delete handler;
		return;
	}

	handler->Send(result);
	delete handler;
}

///////////////////////////////////
//// 删除全服邮件
struct idip_delete_mail_all : public idip_req_body<IDIPCmdReqDeleteMailAll>
{
	idip_delete_mail_all() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBack_DeleteMailData(idip_delete_mail_all *handler, RpcRetcode *res);
};
int idip_delete_mail_all::Serve()
{
	DeleteMailAllRpc *rpc = (DeleteMailAllRpc *) Rpc::Call(RPC_DELETEMAILALLRPC, GDeleteMailToAllArg(req.MailId));
	rpc->call_back = std::bind(&CallBack_DeleteMailData, this, std::placeholders::_1);
	GameDBClient::GetInstance()->SendProtocol(rpc);

	return HANDLE_RET_PENDING;
}
void idip_delete_mail_all::CallBack_DeleteMailData(idip_delete_mail_all *handler, RpcRetcode *res)
{
	int retcode = res->retcode;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_PARAM;
	}
	handler->IDIPSend(retcode);
	delete handler;
}

////////////////////////////////////////////////////////////////////////////////////
//一些计时GM操作管理
typedef bool GMCMDResultCheck(const PB::ipt_gm_cmd& op);
class gm_cmd_info
{
	PB::ipt_gm_cmd m_op;
	GMCMDResultCheck *m_checker;
public:
	gm_cmd_info(const PB::ipt_gm_cmd& op, GMCMDResultCheck *checker = NULL) :
		m_op(op), m_checker(checker) {}
	bool Update()
	{
		if (!m_checker)
		{
			return true;
		}
		return m_checker(m_op);
	}
};

class GMCMDIncessancyObserver : public IntervalTimer::Observer
{
	std::vector<gm_cmd_info>  _cmd_vec;
public:

	GMCMDIncessancyObserver()
	{
		IntervalTimer::AddTimer(this, 10);
	}
	static GMCMDIncessancyObserver& GetInstance()
	{
		static GMCMDIncessancyObserver _instance;
		return _instance;
	}
	~GMCMDIncessancyObserver()
	{
		ReleaseTimer();
	}
	bool Update()
	{
		LOG_TRACE("GMCMDIncessancyObserver Left count:%zu", _cmd_vec.size());
		std::vector<gm_cmd_info> temp;
		temp.swap(_cmd_vec);
		for (auto it = temp.begin(); it != temp.end(); ++it)
		{
			if (!it->Update())
			{
				_cmd_vec.push_back(*it);
			}
		}
		return true;
	}

	void Insert(const PB::ipt_gm_cmd& op, GMCMDResultCheck *checker)
	{
		std::string out;
		::google::protobuf::log_message(out, &op);
		GLog::formatlog("insert_gm_cmd", "checker=%p:op=%.*s", checker, (int)out.size(), out.c_str());
		_cmd_vec.push_back(gm_cmd_info(op, checker));
	}
};

////////////////////////////////////////////////////////////////////////////////////
//zl_debug 活动相关操作
struct zl_campaign_op : public idip_req_body<IDIPCmdReqDebug>
{
	zl_campaign_op() : idip_req_body() {}
	virtual ~zl_campaign_op()
	{
		LOG_TRACE("~zl_campaign_op::zl_campaign_op, this=%p", this);
	}
	virtual int Serve() override;
	static void Callback_MergeCorps(zl_campaign_op *p_request, ruid_t merger, ruid_t merged, int retcode)
	{
		SLOG(FORMAT, "zl_campaign_op::Callback_MergeCorps").PS(retcode).PS(merger).PS(merged);
		p_request->IDIPSend(retcode, retcode == 0 ? "success" : "failed");
		delete p_request;
	}
	static void CallBack_SetRechargeVersion(zl_campaign_op *p_request, SetRechargeVersionData *arg, RpcRetcode *res);
	bool NeedFilterUnMasterRequest() const override
	{
		static std::set<int> types_need_filter
		{
			100, 171, 172, 185, 194, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269,
			270, 271, 272, 273, 274, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 320,
			321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 332, 333, 334, 335, 336, 337, 339, 340,
			341, 342, 343, 344, 350, 351, 352, 353, 354, 356, 357, 358, 359, 360, 361, 362, 363, 400,
			401, 403, 404, 405, 406, 407, 409, 410, 411, 412, 413, 414, 415, 416, 417, 419, 423, 424,
			425, 454,
		};
		return types_need_filter.find(req.OperType) != types_need_filter.end();
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
static void DumpSearchServerStatusInfo()
{
	char buf[128];
	memset(buf, 0, sizeof(buf));
	snprintf(buf, sizeof(buf), "<%d,%d,%d>", GLOBAL_CONFIG.total_register_users_limit, GLOBAL_CONFIG.today_register_users_limit, GLOBAL_CONFIG.strict_register_users_limit);
	std::string register_users_limit_str = buf;

	memset(buf, 0, sizeof(buf));
	snprintf(buf, sizeof(buf), "<%zu,%zu,%zu>", UserContainer::Instance().GetPlayerLimit(), UserContainer::Instance().GetWithQueuePlayerLimit(), UserContainer::Instance().GetMaxPassPlayerNumPerHeartbeat());
	std::string player_limit_str = buf;

	memset(buf, 0, sizeof(buf));
	snprintf(buf, sizeof(buf), "<%d,%d,%d,%d,%d>", ip_whitelist_enabled, GDeliveryServer::GetInstance()->RegisterAccountWhiteListEnabled(), GDeliveryServer::GetInstance()->IsStaticRegisterAccountWhiteListEnable(), GDeliveryServer::GetInstance()->AccountWhiteListEnabled(), GDeliveryServer::GetInstance()->StaticWhiteListEnabled());
	std::string white_list_str = buf;

	SLOG(FORMAT, "IdipSearchInfo::CommonInfo").P("zone_id", g_zoneid)
	.P("hub_size", GHubServerManager::GetInstance().GetConnHubSize())
	.P("gs_size", GProviderServer::GetInstance()->GetConnGsSize())
	.P("db_conn", GameDBClient::GetInstance()->IsConnect())
	.P("link_conn", GDeliveryServer::GetInstance()->GetConnLinkSize())
	.P("uname_conn", UniqueNameClient::GetInstance()->IsConnect())
	.P("auany_conn", GAUAnyClient::GetInstance()->IsConnect())
	.P("csp_conn", GCSProviderClient::GetInstance()->IsConnect())
	.P("grc_conn", GrcClient::GetInstance()->IsConnect())
	.P("idip_proxy_conn", GIdipProxyManager::GetInstance().GetConnIdipProxySize())
	.P("online_info_conn", OnlineinfoClient::GetInstance()->IsConnect())
	.P("debug", GDeliveryServer::GetInstance()->IsDebugging())
	.P("tlog_enable", GDeliveryServer::GetInstance()->IsTlogEnable())
	.P("register_user_limit<total,today,strict>", register_users_limit_str)
	.P("player_limit<pcu,queue,per_heart>", player_limit_str)
	.P("white_list<ip,register,static_register,account,static_account>", white_list_str);

	std::stringstream open_campain_info_str, close_campain_info_str;
	for (auto it = GLOBAL_CONFIG.idip_search_server_status_need_campaign_id.begin(); it != GLOBAL_CONFIG.idip_search_server_status_need_campaign_id.end(); ++it)
	{
		if (CampaignManager::GetInstance().IsActive(*it))
		{
			open_campain_info_str << *it << ",";
		}
		else
		{
			close_campain_info_str << *it << ",";
		}
	}
	SLOG(FORMAT, "IdipSearchInfo::CampaignInfo").P("zone_id", g_zoneid).P("open_campaign", open_campain_info_str.str()).P("close_campain", close_campain_info_str.str());

	std::stringstream open_func_code_info_str, close_func_code_info_str;
	for (auto it = GLOBAL_CONFIG.idip_search_server_status_need_func_code.begin(); it != GLOBAL_CONFIG.idip_search_server_status_need_func_code.end(); ++it)
	{
		if (*it > kFuncCodeMax || *it < kFuncCodeUnknown)
		{
			continue;
		}
		if (GET_FUNC_SWITCH(static_cast<FUNC_CODE>(*it)))
		{
			open_func_code_info_str << *it << ",";
		}
		else
		{
			close_func_code_info_str << *it << ",";
		}
	}
	SLOG(FORMAT, "IdipSearchInfo::FuncCodeInfo").P("zone_id", g_zoneid).P("open_func_code", open_func_code_info_str.str()).P("close_func_code", close_func_code_info_str.str());
}
int zl_campaign_op::Serve()
{
	bool is_ok = true;
	switch (req.OperType)
	{
	case 100:
	{
		int index = req.Param1;
		int value = req.Param2;
		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{
			break;
		}
		ipd->ip_global_data_manager.SetData(index, value);
	}
	break;
	case 171:
	{
		int campaignid = req.Param1;
		int is_forbid = req.Param2;
		CampaignManager::GetInstance().ForbidCampaign(campaignid, is_forbid);
	}
	break;
	case 172:
	{
		int campaignid = req.Param1;
		int lasttime = req.Param2;
		int starttime = Timer::GetTime();
		CampaignManager::GetInstance().GMOpenCampaign(campaignid, starttime, lasttime);
	}
	break;

	case 185:
	{
		// 功能开关，0为关
		int func_code = req.Param1;
		bool open = (req.Param2 != 0);

		if (!GNET::SetIdipFuncSwitch(func_code, open))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 194:
	{
		//设置服务器等级经验加成系数
		float factor = atof(req.Param4.c_str());
		if (factor > 5 || factor < 0)
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}
		SetRechargeVersionData arg;
		arg.version = short(factor * 50);
		arg.activity_type =  SERVER_LEVEL_EXP_FACTOR;
		SetRechargeVersionRpc *rpc = (SetRechargeVersionRpc *) Rpc::Call(RPC_SETRECHARGEVERSIONRPC, arg);
		//在回调中发送响应
		rpc->call_back = std::bind(&zl_campaign_op::CallBack_SetRechargeVersion, this, std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	break;

	case 195:
	{
		//设置服务器是否开启订单修复
		int check_orders = req.Param1;
		if (check_orders != 0 && check_orders != 1)
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}
		SetRechargeVersionData arg;
		arg.version = check_orders;
		arg.activity_type = CHECK_ORDERS;
		SetRechargeVersionRpc *rpc = (SetRechargeVersionRpc *) Rpc::Call(RPC_SETRECHARGEVERSIONRPC, arg);
		//在回调中发送响应
		rpc->call_back = std::bind(&zl_campaign_op::CallBack_SetRechargeVersion, this, std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	break;

	case 196:
	{
		// oper_param3: sn s_billno account
		int sn = 0, ignore_db = 0;
		char s_billno[128] = {0};
		char account[128] = {0};

		const char *szParamFmt = "%d%d%s%s";
		const int cntParamNeed = 4;
		int cntParam = sscanf(req.Param4.data(), szParamFmt, &ignore_db, &sn, s_billno, account);
		DEFER(SLOG(DEBUG, "zl_campaign_op::Serve").PS(cntParam).PS(account).PS(sn).PS(s_billno).PS(ignore_db););
		if (cntParamNeed != cntParam)
		{
			break;
		}
		std::string strAccount = account;
		GNET::Octets o_account(strAccount.data(), strAccount.length());
		MidasRechargeManager::GetInstance().DebugRemoveCheckingOrder(o_account, sn, s_billno, ignore_db);
	}
	break;

	case 197:
	{
		//设置服务器开启订单修复是否只针对特定角色
		int check_orders = req.Param1;
		if (check_orders != 0 && check_orders != 1)
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}
		SetRechargeVersionData arg;
		arg.version = check_orders;
		arg.activity_type = CHECK_ORDERS_SPECIAL;
		SetRechargeVersionRpc *rpc = (SetRechargeVersionRpc *) Rpc::Call(RPC_SETRECHARGEVERSIONRPC, arg);
		//在回调中发送响应
		rpc->call_back = std::bind(&zl_campaign_op::CallBack_SetRechargeVersion, this, std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	break;

	case 198:
	{
		//设置服务器消费订单修复版本号
		int version = req.Param1;
		if (version <= 0)
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}
		SetRechargeVersionData arg;
		arg.version = version;
		arg.activity_type = CHECK_ORDERS_COST_VERSION;
		SetRechargeVersionRpc *rpc = (SetRechargeVersionRpc *) Rpc::Call(RPC_SETRECHARGEVERSIONRPC, arg);
		//在回调中发送响应
		rpc->call_back = std::bind(&zl_campaign_op::CallBack_SetRechargeVersion, this, std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	break;

	case 199:
	{
		//设置服务器赠送订单修复版本号
		int version = req.Param1;
		if (version <= 0)
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}
		SetRechargeVersionData arg;
		arg.version = version;
		arg.activity_type = CHECK_ORDERS_PRESENT_VERSION;
		SetRechargeVersionRpc *rpc = (SetRechargeVersionRpc *) Rpc::Call(RPC_SETRECHARGEVERSIONRPC, arg);
		//在回调中发送响应
		rpc->call_back = std::bind(&zl_campaign_op::CallBack_SetRechargeVersion, this, std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	break;

	case 200:
	{
		/*int64_t roleid = atoll(req.Param4.c_str());
		if (roleid <= 0)
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}
		LOG_TRACE("IDIP_DEBUG_COMMAND. insert card clear. roleid=%ld", roleid);
		IDIPCommandServer::GetInstance()->card_forbid_role.insert(roleid);
		RoleInfo *p_role = RoleMap::Instance().FindOnline(roleid);
		if (p_role != NULL)
		{
			PB::ipt_clear_card pb;
			pb.set_role_id(roleid);
			p_role->SendMessage2GS(pb);
		}*/
	}
	break;
	/*case 201:
	{
		int64_t roleid = atoll(req.Param4.c_str());
		if (roleid <= 0)
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}

		LOG_TRACE("IDIP_DEBUG_COMMAND. erase card clear. roleid=%ld", roleid);
		IDIPCommandServer::GetInstance()->card_forbid_role.erase(roleid);
	}
	break;
	case 202:
	{
		LOG_TRACE("IDIP_DEBUG_COMMAND. set auction close.");
		IDIPCommandServer::GetInstance()->SetAuctionClose(true);
	}
	break;
	case 203:
	{
		LOG_TRACE("IDIP_DEBUG_COMMAND. unset auction close.");
		IDIPCommandServer::GetInstance()->SetAuctionClose(false);
	}
	break;*/
	case 206:
	{
		int diamond_bag_tid = req.Param1;
		int diamond_amount = req.Param2;
		int64_t to_roleid = atoll(req.Param4.c_str());

		int item_id = diamond_bag_tid;
		int item_count = 1;

		DBMailSendArg arg;
		arg.dst.id = to_roleid;
		Mail& mail = arg.mail;

		mail.header.category = CATEGORY_MAIL_SYSTEM | MAIL_CATEGORY_IDIPGIFT;
		mail.header.subject = Octets();
		mail.header.from = ((int64_t)item_id << 32) | (item_count); //为了方便客户端，在header的from字段里填入物品id和数量
		mail.header.to = to_roleid;
		mail.header.date = Timer::GetTime();
		mail.header.status = MAIL_STATUS_ATTACHED;
		mail.header.msgid = 0;
		mail.context = Octets();

		Octets item_data;
		item_data.insert(item_data.end(), &diamond_amount, sizeof(diamond_amount));

		GRoleInventory item;
		item.id = item_id;
		item.count = item_count;
		item.data = item_data;
		mail.attachment.items.push_back(item);

		DBMailSend *rpc = (DBMailSend *) Rpc::Call(RPC_DBMAILSEND, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
	break;
	case 207:
	{
		//	int param1 = oper_param1;
		//	int param2 = oper_param2;
		int64_t roleid = atoll(req.Param4.c_str());

		RoleInfo *pInfo = RoleMap::Instance().Find(roleid);
		if (pInfo && !pInfo->IsOnline())
		{
			pInfo->SetNeedGetRole();
			RoleMap::GetRoleInfo(roleid, Octets(), NULL);
		}
	}
	break;

	case 208:
	{
		int param1 = req.Param1;
		int param2 = req.Param2;

		LOG_TRACE("IDIP:ClearBattleOrderResult::param1=%d:param2=%d:zoneid=%d", param1, param2, g_zoneid);

		if (!CenterManager::GetInstance().IsCenter())
		{
			break;
		}

		auto *pCenterBattle = CenterManager::GetInstance().GetCenterBattle(CBT_KNOCKOUT_ELIMINATE);
		if (!pCenterBattle)
		{
			break;
		}
		if (pCenterBattle->GetCenterZoneID() != g_zoneid)
		{
			break;
		}

		auto pEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(CORPS_BATTLE_STUB_TYPE_ELIMINATE);
		if (pEntry)
		{
			pEntry->ClearBattleOrderResult(param1, param2);
		}
	}
	break;

	case 209:
	{
		// oper_param3: value account
		int value = 0;
		char account[128] = {0};

		const char *szParamFmt = "%d%s";
		const int cntParamNeed = 2;
		int cntParam = sscanf(req.Param4.data(), szParamFmt, &value, account);
		DEFER(SLOG(DEBUG, "zl_campaign_op::Serve").PS(cntParam).PS(account).PS(value););
		if (cntParamNeed != cntParam)
		{
			break;
		}
		std::string strAccount = account;
		GNET::Octets o_account(strAccount.data(), strAccount.length());
		MidasRechargeManager::GetInstance().DebugFixLostCostOrders(o_account, value);
	}
	break;

	case 210:
	{
		// oper_param3: value account
		int value = 0;
		char account[128] = {0};

		const char *szParamFmt = "%d%s";
		const int cntParamNeed = 2;
		int cntParam = sscanf(req.Param4.data(), szParamFmt, &value, account);
		DEFER(SLOG(DEBUG, "zl_campaign_op::Serve").PS(cntParam).PS(account).PS(value););
		if (cntParamNeed != cntParam)
		{
			break;
		}
		std::string strAccount = account;
		GNET::Octets o_account(strAccount.data(), strAccount.length());
		MidasRechargeManager::GetInstance().DebugFixLostPresentOrders(o_account, value);
	}
	break;

	case 240:
	{
		try
		{
			int forbid_time = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_EXP_TIME_FORBID, Timer::GetTime() + forbid_time);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM forbid exp time exception");
		}
	}
	break;

	case 241:
	{
		try
		{
			int64_t forbid_num = std::stol(req.Param5);
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_EXP_NUM_FORBID, forbid_num);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM forbid exp time exception");
		}
	}
	break;

	case 242:
	{
		try
		{
			int type = req.Param1;
			int id = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_SS_STYLE, type, id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del ss style exception");
		}
	}
	break;

	case 243:
	{
		try
		{
			int type = req.Param1;
			int id = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_CHATBOX, type, id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del chatbox exception");
		}
	}
	break;

	case 256:
	{
		// 禁止物品使用，1为禁止
		bool forbid = (req.Param1 != 0);
		int item_tid = req.Param2;

		if (!GNET::SetIdipForbidItemUse(item_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 257:
	{
		// 禁止物品增加，1为禁止
		bool forbid = (req.Param1 != 0);
		int item_tid = req.Param2;

		if (!GNET::SetIdipForbidItemInc(item_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 258:
	{
		// 禁止物品赠送，1为禁止
		bool forbid = (req.Param1 != 0);
		int item_tid = req.Param2;

		if (!GNET::SetIdipForbidItemGift(item_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 259:
	{
		// 禁止物品交易，1为禁止
		bool forbid = (req.Param1 != 0);
		int item_tid = req.Param2;

		if (!GNET::SetIdipForbidItemDeal(item_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 260:
	{
		// 禁止玩家属性，1为禁止
		bool forbid = (req.Param1 != 0);
		int addon_id = req.Param2;

		if (!GNET::SetIdipForbidAddon(addon_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 261:
	{
		// 禁止玩家属性组，1为禁止
		bool forbid = (req.Param1 != 0);
		int addon_group_id = req.Param2;

		if (!GNET::SetIdipForbidAddonGroup(addon_group_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 262:
	{
		// 禁止找回奖励，1为禁止
		bool forbid = (req.Param1 != 0);
		int activity_id = req.Param2;

		if (!GNET::SetIdipForbidRetrieve(activity_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 263:
	{
		// 禁止npc随身商店，1为禁止
		bool forbid = (req.Param1 != 0);
		int shop_id = req.Param2;

		if (!GNET::SetIdipForbidNpcShop(shop_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 264:
	{
		// 禁止npc随身商店出售某物品，1为禁止
		bool forbid = (req.Param1 != 0);
		int shop_id = req.Param2;
		int item_tid = req.Param3;

		if (!GNET::SetIdipForbidNpcShopItem(shop_id, forbid, item_tid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 265:
	{
		// 禁止玩家进入场景，1为禁止
		bool forbid = (req.Param1 != 0);
		int scene_tag = req.Param2;

		// 不能禁止默认场景
		if (scene_tag == GNET::SpecialIdConfigManager::Instance().SpecialIdConfig.default_player_scene_id)
		{
			is_ok = false;
			break;
		}

		if (!GNET::SetIdipForbidEnterScene(scene_tag, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 266:
	{
		// 禁止声望增加，1为禁止
		bool forbid = (req.Param1 != 0);
		int repu_id = req.Param2;

		if (!GNET::SetIdipForbidRepuInc(repu_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 267:
	{
		// 禁止声望减少，1为禁止
		bool forbid = (req.Param1 != 0);
		int repu_id = req.Param2;

		if (!GNET::SetIdipForbidRepuDec(repu_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 268:
	{
		// 禁止商城购买物品，1为禁用
		bool forbid = (req.Param1 != 0);
		int goods_id = req.Param2;

		if (!GNET::SetIdipForbidMallGoods(goods_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 269:
	{
		// 禁止通用限次模版(禁止=使用次数达上限)，1为禁用
		bool forbid = (req.Param1 != 0);
		int limit_id = req.Param2;

		if (!GNET::SetIdipForbidCommonUseLimit(limit_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 270:
	{
		// 禁止矿物刷出并杀掉已有，1为禁止
		bool forbid = (req.Param1 != 0);
		int matter_tid = req.Param2;

		if (!GNET::SetIdipForbidMatter(matter_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 271:
	{
		// 禁止技能，1为禁止
		bool forbid = (req.Param1 != 0);
		int skill_id = req.Param2;

		if (!GNET::SetIdipForbidSkill(skill_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 272:
	{
		// 禁止掉落table，1为禁止
		bool forbid = (req.Param1 != 0);
		int table_id = req.Param2;

		if (!GNET::SetIdipForbidDropData(table_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 273:
	{
		// 设置服务器等级
		int lv = req.Param1;

		if (!GNET::SetIdipDebugParaServerLevel(lv))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 274:
	{
		int scene_id = req.Param1;
		GrcManager::GetInstance().QQPushMsg(scene_id, Timer::GetTime());
	}
	break;

	case 301:
	{
		// 禁止任务，并不删除已经接的，1为禁止
		bool forbid = (req.Param1 != 0);
		int task_id = req.Param2;

		if (!GNET::SetIdipForbidTask(task_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 302:
	{
		// 禁止npc刷出并杀掉已有，1为禁止
		bool forbid = (req.Param1 != 0);
		int npc_tid = req.Param2;

		if (!GNET::SetIdipForbidNpc(npc_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 303:
	{
		// 禁止副本，1为禁止
		bool forbid = (req.Param1 != 0);
		int instance_tid = req.Param2;

		if (!GNET::SetIdipForbidInstance(instance_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 304:
	{
		// 开关控制器
		int enable = req.Param1;
		int spawn_index = req.Param2;
		int world_idx = req.Param3;
		int isglobal = world_idx ? 0 : 1;

		PB::ipt_gm_cmd pb;
		pb.set_cmd_type(PB::ipt_gm_cmd::GCT_TRIGGER_SPAWN);
		pb.add_params(spawn_index);
		pb.add_params(enable ? 1 : 0);
		pb.add_params(world_idx);
		pb.add_params(isglobal);
		GProviderServer::GetInstance()->BroadcastProtocol(pb);

		GLog::log(LOG_INFO, "DS:Operation_Cmd_Excutor::OnPtr_Trigger_Spawn: ctrl_idx=%d: world_idx=%u: enable=%d,global=%d",
		          spawn_index, world_idx, enable, isglobal);
	}
	break;

	case 305:
	{
		// 禁止npc服务，0为禁止
		bool forbid = (req.Param1 == 0);
		int service_id = req.Param2;
		int service_para = req.Param3;

		if (!GNET::SetIdipForbidNpcService(service_id, forbid, service_para))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 306:
	{
		// 禁止奖励模版，0为禁止
		bool forbid = (req.Param1 == 0);
		int reward_tid = req.Param2;

		if (!GNET::SetIdipForbidReward(reward_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 307:
	{
		// 禁止场景控制器，1为禁止
		bool forbid = (req.Param1 != 0);
		int spawn_id = req.Param2;
		int scene_tag = req.Param3;

		if (!GNET::SetIdipForbidSceneSpawn(spawn_id, scene_tag, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 308:
	{
		// 禁用协议，0为禁止
		bool forbid = (req.Param1 == 0);
		int protocol = req.Param2;
		if (forbid)
		{
			if (!GNET::SetIdipForbidC2SProtocol(protocol, forbid))
			{
				is_ok = false;
				break;
			}
		}
		else
		{
			std::string state = req.Param4;
			size_t pos = state.find('_', 0);
			if (pos == std::string::npos)
			{
				is_ok = false;
				break;
			}
			std::string valid_state = state.substr(0, pos);
			std::string invalid_state = state.substr(pos + 1, state.size() - pos);
			if (!GNET::SetIdipForbidC2SProtocol(protocol, forbid, valid_state, invalid_state))
			{
				is_ok = false;
				break;
			}
		}
	}
	break;

	case 309:
	{
		int is_forbid = req.Param1;
		CorpsManager::GetInstance().ForbidMerge(is_forbid == 1 ? true : false);
		GLog::log(LOG_INFO, "GMT ForbidCorpsMerge, is_forbid %d.", is_forbid);
	}
	break;
	case 310:
	{
		GLog::log(LOG_INFO, "GMT ClearCorpsReadyMergeInfo.");
		corps_handle  f = std::bind(&Corps::ClearReadyMergeInfo, std::placeholders::_1);
		CorpsManager::GetInstance().ForEachCorps(f);
	}
	break;

	// 防沉迷开关(大于0打开, <=0关闭)，参考 delcmd 10018
	case 311:
	{
		GAntiWallowClient::SetEnabled(req.Param1 > 0);
	}
	break;
	// 设置防沉迷是否踢人 idip 1/4127 312 <1/0> (参考delcmd 10019 )
	case 312:
	{
		bool kick_flag = (req.Param1 > 0);
		AntiWallowManager::GetInstance().DebugSetKickoutFlag(kick_flag);
	}
	break;

	// 处理玩家充值数据
	case 317: // 备份
	{
		GNET::Octets o_account(req.Param4.data(), req.Param4.length());
		MidasRechargeManager::GetInstance().DebugBackupRechargeData(o_account);
	}
	break;
	case 318: // 恢复
	{
		GNET::Octets o_account(req.Param4.data(), req.Param4.length());
		MidasRechargeManager::GetInstance().DebugRestoreRechargeData(o_account);
	}
	break;
	case 319: // 修正
	{
		int billtype = req.Param1;
		if (billtype < 1 || 2 < billtype)
		{
			break;
		}
		int dec_present = req.Param2;

		// oper_param3: sn s_billno account
		int sn = 0;
		char s_billno[128] = {0};
		char account[128] = {0};

		const char *szParamFmt = "%d%s%s";
		const int cntParamNeed = 3;
		int cntParam = sscanf(req.Param4.data(), szParamFmt, &sn, s_billno, account);
		DEFER(SLOG(DEBUG, "zl_campaign_op::Serve").P("oper_type", req.OperType).PS(cntParam).PS(account).PS(sn).PS(s_billno).PS(billtype).PS(dec_present););
		if (cntParamNeed != cntParam)
		{
			break;
		}
		std::string strAccount = account;
		GNET::Octets o_account(strAccount.data(), strAccount.length());
		MidasRechargeManager::GetInstance().DebugFixRechargeData(o_account, sn, s_billno, billtype, dec_present);
	}
	break;
	case 320: // 设置服务器卡级等级
	{
		DSTPManager::GetInstance().DebugSetServerLevel(req.Param1);
	}
	break;
	case 321: // 设置服务器卡级升级剩余天数
	{
		DSTPManager::GetInstance().DebugSetLevelupDays(req.Param1);
	}
	break;

	case 322: // 座驾解锁
	{
		bool forbid = (req.Param1 == 0); // 0表示关 1表示开
		int mount_id = req.Param2;

		if (!GNET::SetIdipForbidMountUnlock(mount_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 323: // 座驾制造
	{
		bool forbid = (req.Param1 == 0); // 0表示关 1表示开
		int mount_id = req.Param2;

		if (!GNET::SetIdipForbidMountProduce(mount_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 324: // 座驾喷涂
	{
		bool forbid = (req.Param1 == 0); // 0关 1开
		int mount_id = req.Param2;

		if (!GNET::SetIdipForbidMountPaintColor(mount_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 325: // 座驾解锁颜色
	{
		bool forbid = (req.Param1 == 0); // 0关 1开
		int mount_id = req.Param2;

		if (!GNET::SetIdipForbidMountActivateColor(mount_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 326: // 座驾改造
	{
		bool forbid = (req.Param1 == 0); // 0关 1开
		int mount_id = req.Param2;

		if (!GNET::SetIdipForbidMountSelectFashion(mount_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 327: // 解锁时装
	{
		bool forbid = (req.Param1 == 0); // 0关 1开
		int fashion_tid = req.Param2;

		if (!GNET::SetIdipForbidFashionUnlock(fashion_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 328: // 时装染色
	{
		bool forbid = (req.Param1 == 0); // 0关 1开
		int fashion_tid = req.Param2;

		if (!GNET::SetIdipForbidFashionPaint(fashion_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 329: // 时装解锁颜色
	{
		bool forbid = (req.Param1 == 0); // 0关 1开
		int fashion_tid = req.Param2;

		if (!GNET::SetIdipForbidFashionActivateColor(fashion_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 330: // 社团设置徽章
	{
		bool forbid = (req.Param1 == 0);
		int badge_id = req.Param2;

		if (!GNET::SetIdipForbidCorpsSetBadge(badge_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 331: // 禁止某社团执行某功能
	{
		bool forbid = (req.Param1 == 0);
		int func_code = req.Param2;
		int64_t corps_id = atoll(req.Param4.c_str());

		if (!GNET::SetIdipForbidCorpsFunc(corps_id, forbid, func_code))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 332: // 禁止将某物品放入仓库
	{
		bool forbid = (req.Param1 == 0);
		int item_tid = req.Param2;

		if (!GNET::SetIdipForbidPutInDepository(item_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 333: // 禁止将某物品从仓库中取出
	{
		bool forbid = (req.Param1 == 0);
		int item_tid = req.Param2;

		if (!GNET::SetIdipForbidTakeOutDepository(item_tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 334: // 禁止对指定ID的身份进行升级
	{
		bool forbid = (req.Param1 == 0);
		int career_id = req.Param2;

		if (!GNET::SetIdipForbidCareerLevelUp(career_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 335: // 禁止对指定食谱配方进行生产
	{
		bool forbid = (req.Param1 == 0);
		int cookbook_id = req.Param2;

		if (!GNET::SetIdipForbidCareerCookProduce(cookbook_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 336: // 禁止学习指定id的食谱
	{
		bool forbid = (req.Param1 == 0);
		int cookbook_id = req.Param2;

		if (!GNET::SetIdipForbidCareerLearnCookbook(cookbook_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 337: // 禁止解锁指定id的食谱
	{
		bool forbid = (req.Param1 == 0);
		int cookbook_id = req.Param2;

		if (!GNET::SetIdipForbidCareerUnlockCookbook(cookbook_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 338: // 禁止指定玩家执行某功能
	{
		bool forbid = (req.Param1 == 0);
		int func_code = req.Param2;
		int64_t roleid = atoll(req.Param4.c_str());

		if (!GNET::SetIdipForbidPlayerFunc(roleid, forbid, func_code))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 339: // 禁止指定id的小游戏
	{
		bool forbid = (req.Param1 == 0);
		int minigame_type = req.Param2;
		int minigame_id = req.Param3;

		if (!GNET::SetIdipForbidMiniGame(minigame_type, minigame_id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 340: // 禁止某个tid的交互模板
	{
		bool forbid = (req.Param1 == 0);
		int tid = req.Param2;

		if (!GNET::SetIdipForbidInteractTempl(tid, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 341: // 禁止客户端显示某个id的排行榜
	{
		bool forbid = (req.Param1 == 0);
		int id = req.Param2;

		if (!GNET::SetIdipForbidDisplayTopList(id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 342: // 禁止某个id的排行榜发奖
	{
		bool forbid = (req.Param1 == 0);
		int id = req.Param2;

		if (!GNET::SetIdipForbidRewardTopList(id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 343: // 禁止指定座驾进化
	{
		bool forbid = (req.Param1 == 0);
		int id = req.Param2;

		if (!GNET::SetIdipForbidMountTrain(id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 344: // 禁止指定身份冻结
	{
		bool forbid = (req.Param1 == 0);
		int id = req.Param2;

		if (!GNET::SetIdipForbidCareerFreeze(id, forbid))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 350:	// 设置服务器注册开关
	{
		GLog::log(LOG_INFO, "GMT SetRegisterAccountEnable %d.", req.Param1);
		GDeliveryServer::GetInstance()->SetRegisterAccountEnable((bool)req.Param1);
	}
	break;

	case 351:	// 设置服务器总注册人数限制
	{
		GLog::log(LOG_INFO, "GMT set total_register_users_limit %d.", req.Param1);
		GLOBAL_CONFIG.total_register_users_limit = req.Param1;
	}
	break;

	case 352:	// 设置服务器今日注册人数限制
	{
		GLog::log(LOG_INFO, "GMT set today_register_users_limit %d.", req.Param1);
		GLOBAL_CONFIG.today_register_users_limit = req.Param1;
	}
	break;

	case 353:	// 设置服务器注册账号白名单开关
	{
		GLog::log(LOG_INFO, "GMT SetRegisterEnableAccountWhiteList %d.", req.Param1);
		GDeliveryServer::GetInstance()->SetRegisterEnableAccountWhiteList((bool)req.Param1);
	}
	break;

	case 354:	// 拍卖行审批通过
	{
		int64_t aid = atoll(req.Param4.c_str());
		GAUCTION::GAuctionManager::GetInstance().IDIPOperation(0, aid, 0, 0, AUCTION_IDIP_OP_APPROVE);
	}
	break;

	case 355:	// 拍卖行下架
	{
		int64_t roleid = atoll(req.Param4.c_str());
		int64_t aid = atoll(req.Param5.c_str());
		GAUCTION::GAuctionManager::GetInstance().IDIPOperation(roleid, aid, 0, 0, AUCTION_IDIP_OP_CLOSE);
	}
	break;

	case 356: // 热更新场景
	{
		PB::ipt_gm_cmd pb;
		pb.set_cmd_type(PB::ipt_gm_cmd::GCT_RELOAD);
		pb.add_params(req.Param1);
		pb.add_params(req.Param2);
		pb.set_stringparams(req.Param4);
		GProviderServer::GetInstance()->BroadcastProtocol(pb);

		GLog::log(LOG_INFO, "DS:Operation_Cmd_Excutor::OnPtr_Reload: type=%d, value=%d", req.Param1, req.Param2);
	}
	break;

	case 357: // 通知指定玩家跳过指定cg 重启服务器失效
	{
		bool forbid = (req.Param1 == 1);
		int cg = req.Param2;

		IdipDataManager::Instance().SetForbidCG(cg, forbid);
	}
	break;

	case 358: // 指定玩家禁某任务 重启服务器失效
	{
		bool forbid = (req.Param1 != 0);
		int task_id = req.Param2;

		GNET::SetIdipForbidTask2(task_id, forbid);
	}
	break;

	case 359:	// 设置服务器静态注册账号白名单开关
	{
		GLog::log(LOG_INFO, "GMT SetStaticRegisterEnableAccountWhiteList %d.", req.Param1);
		GDeliveryServer::GetInstance()->SetStaticRegisterEnableAccountWhiteList((bool)req.Param1);
	}
	break;

	case 360: // 解除假拍卖行系统上架限制
	{
		is_ok = FAUCTION::FAuctionManager::Instance().IDIPAuctionOpen(req.Param1);
		LOG_TRACE("IDIPAuctionOpen itemid=%d, is_ok=%d", req.Param1, is_ok ? 1 : 0);
	}
	break;

	case 361:
	{
		// 替换附加属性
		int addon_id = req.Param1;
		int replace_addon_id = req.Param2;

		if (!GNET::SetIdipReplaceAddon(addon_id, replace_addon_id))
		{
			is_ok = false;
			break;
		}
	}
	break;

	case 362:
	{
		// 设置服务器极限注册人数限制
		GLog::log(LOG_INFO, "GMT set strict_register_users_limit %d.", req.Param1);
		GLOBAL_CONFIG.strict_register_users_limit = req.Param1;
	}
	break;

	case 363:
	{
		//重置好友互助
		GrcManager::GetInstance().AccountFriendHelp(3, Octets(), 0);
	}
	break;

	case 364:
	{
		//IDIP罢免社团团长
		try
		{
			int64_t corps_id = std::stol(req.Param4);
			ruid_t master_id = std::stol(req.Param5);
			LOG_TRACE("IDIP GM Abdicate corps master:corps_id=%ld:master=%ld", corps_id, master_id);
			CorpsManager::GetInstance().IDIPAbdicate(corps_id, master_id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM Abdicate corps master exception");
		}
	}
	break;

	case 365:
	{
		//IDIP合并帮派
		try
		{
			int64_t corps_merger = std::stol(req.Param4);
			int64_t corps_merged = std::stol(req.Param5);
			if (!corps_merger || !corps_merged)
			{
				LOG_TRACE("IDIP GM merge corps error, invalid corpsid! merger=%ld:merged=%ld", corps_merger, corps_merged);
				is_ok = false;
				break;
			}
			LOG_TRACE("IDIP GM merge corps merger=%ld:merged=%ld", corps_merger, corps_merged);
			auto callback = std::bind(&zl_campaign_op::Callback_MergeCorps, this, corps_merger, corps_merged, std::placeholders::_1);
			CorpsManager::GetInstance().IDIPMergeCorps(callback, corps_merger, corps_merged);
			return HANDLE_RET_PENDING;
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM merge corps exception");
		}

	}
	break;

	case 366:	// 设置回流服账号白名单开关
	{
		GLog::log(LOG_INFO, "GMT SetComebackAccountWhiteListEnable %d.", req.Param1);
		GDeliveryServer::GetInstance()->SetComebackAccountWhiteListEnable((bool)req.Param1);
	}
	break;

	//修复角色交易之后唯一名的异常状态
	case 367:
	{
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			char openId_seller[64] = "";
			char openId_buyer[64] = "";
			int cntParam = sscanf(req.Param5.data(), "%s %s", openId_seller, openId_buyer);
			SLOG(INFO, "IDIPCommand::Fix unamed data error after role_trade_transfer 1")
			.P("AreaId", req.AreaId)
			.P("PlatId", req.PlatId)
			.P("Partition", req.Partition)
			.P("roleid", req.Param4)
			.P("openids", req.Param5)
			.PS(openId_seller)
			.PS(openId_buyer);
			if (cntParam != 2)
			{
				is_ok = false;
				break;
			}
			DBRoleTrade_TransferArg arg;
			arg.account_seller = Openid2Account(Octets(openId_seller, strnlen(openId_seller, 64)), req.AreaId);
			arg.account_buyer = Openid2Account(Octets(openId_buyer, strnlen(openId_buyer, 64)), req.AreaId);
			arg.roleid = roleid;
			arg.is_rollback = 0;
			UnamedRoleTrade_Transfer *rpc = (UnamedRoleTrade_Transfer *)Rpc::Call(RPC_UNAMEDROLETRADE_TRANSFER, arg);
			if (!UniqueNameClient::GetInstance()->SendProtocol(rpc))
			{
				SLOG(WARNING, "DS::IDIPCommand::DBRoleTrade_Transfer::Client UniqueNameClient SendProtocol UnamedRoleTrade_Transfer failed").P("account_seller", arg.account_seller).P("account_buyer", arg.account_buyer).P("roleid", arg.roleid).P("is_rollback", arg.is_rollback);
				is_ok = false;
				break;
			}
		}
		catch (...)
		{
			is_ok = false;
		}
	}
	break;

	//设置小镇伙伴聊天事件状态
	case 368:
	{
		try
		{
			int event_id = req.Param1;
			int status = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_COMPLETE_TOWNLET_CHAT_EVENT, event_id, status);
		}
		catch (...)
		{
			is_ok = false;
			SLOG(ERR, "IDIP GM set townlet retinue chat event status exception");
		}
	}
	break;

	//完成新手引导
	case 369:
	{
		try
		{
			int guide_id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_FINISH_PLAYER_GUIDE, guide_id);
		}
		catch (...)
		{
			is_ok = false;
			SLOG(ERR, "IDIP GM set player guide exception");
		}
	}
	break;

	case 400:
	{
		int time = req.Param1;
		PreCreateServer::GetInstance().SetDebugServerOpenTime(time);
		LOG_TRACE("PreCreateServer::SetDebugServerOpenTime time=%d", time);
	}
	break;

	case 401:
	{
		if (!g_pre_create_server)
		{
			is_ok = false;
			break;
		}
		PreCreateChat chat;
		if (req.Param1 == 1)
		{
			chat.channel = CHAT_CHANNEL_MESSAGE; //弹窗
		}
		else if (req.Param1 == 2)
		{
			chat.channel = CHAT_CHANNEL_BROADCAST; //跑马灯
		}
		else
		{
			IDIPSend(1, "invalid param");
			return HANDLE_RET_FINISH;
		}

#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(req.Param4);
#else
		std::string content = req.Content;
#endif

		Octets ucs2_content;
		CharsetConverter::conv_charset_t2u(Octets(content.c_str(), content.size()), ucs2_content);
		chat.msg = ucs2_content;
		GDeliveryServer::GetInstance()->BroadcastToLS(chat);
	}
	break;

	case 402:
	{
		try
		{
			int sceneid = req.Param1;
			int x = req.Param2;
			int z = req.Param3;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_TRANSPORT, sceneid, (int64_t)x << 32 | z);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM transport role exception");
		}
	}
	break;

	case 403:
	{
		int limit = req.Param1;
		InstanceManager::Instance().GetQueueMan().SetSendLimitPerSecond(limit);
		LOG_TRACE("DS::GMT::SetSendLimitPerSecond limit=%d", limit);
	}
	break;

	case 404:
	{
		UniqueNameClient::GetInstance()->SetCorpsNameReservedEnable((bool)req.Param1);
		LOG_TRACE("GMT::SetCorpsNameReservedEnable enable=%d", req.Param1);
	}
	break;

	case 405:
	{
		UniqueNameClient::GetInstance()->SetRoleNameReservedEnable((bool)req.Param1);
		LOG_TRACE("GMT::SetRoleNameReservedEnable enable=%d", req.Param1);
	}
	break;

	case 406:
	{
		CorpsAuctionManager::Instance().ForceClose();
		LOG_TRACE("CorpsAuctionManager force close");
	}
	break;

	case 407:
	{
		GLog::log(LOG_INFO, "GMT set chat_channel_min_count = %d, chat_channel_max_count = %d", req.Param1, req.Param2);
		GLOBAL_CONFIG.chat_channel_min_count = req.Param1 ? req.Param1 : 600;
		GLOBAL_CONFIG.chat_channel_max_count = req.Param2 ? req.Param2 : 3000;
	}
	break;

	case 408:
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(req.Param4);
#else
		std::string content = req.Content;
#endif
		LOG_TRACE("GMT::set_server_group_open_time = %s", content.c_str());

		PB::npt_server_group_open_time_change pb;
		pb.set_open_time(content);

		GDeliveryServer::GetInstance()->BroadcastToLS(pb, true);
		GDeliveryServer::GetInstance()->BroadcastToCS(pb, true);
	}
	break;

	case 409:
	{
		GLog::log(LOG_INFO, "GMT set chat_channel_min_count = %d, chat_channel_max_count = %d", req.Param1, req.Param2);
		GLOBAL_CONFIG.chat_channel_min_count = req.Param1 ? req.Param1 : 600;
		GLOBAL_CONFIG.chat_channel_max_count = req.Param2 ? req.Param2 : 3000;
	}
	break;

	case 412:
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(req.Param4);
#else
		std::string content = req.Content;
#endif
		LOG_TRACE("GMT::set_forbid_mask = %s", content.c_str());

		PB::npt_notify_forbid_mask notify;
		notify.set_forbid_mask(atoll(content.c_str()));

		GDeliveryServer::GetInstance()->BroadcastToLS(notify, true);
		GDeliveryServer::GetInstance()->BroadcastToCS(notify, true);
	}
	break;

	case 411:// 一键屏蔽操作 0 关闭， 1 开启
	{
		bool open = (req.Param1 != 0);
		if (!GNET::SetIdipFuncSwitch(kFuncCodeCsp, open) // CSP 功能
		        || !GNET::SetIdipFuncSwitch(kFuncCodeSocialSpace, open) // 服务器与空间服务器的通讯 || 朋友圈功能
		        || !GNET::SetIdipFuncSwitch(kFuncCodeImageRoom, open) // 捏脸上传相关界面显示
		        || !GNET::SetIdipFuncSwitch(kFuncCodeBullet, open) // 弹幕
		        || !GNET::SetIdipFuncSwitch(kFuncCodeCredit, open) // 账号信用功能
		        || !GNET::SetIdipFuncSwitch(kFuncCodePlatMemberVip, open) // 平台会员vip
		        || !GNET::SetIdipFuncSwitch(kFuncCodeRewardLadderHistoryHighestRank, open) // 屠龙考核历史最高排名奖励
		        || !GNET::SetIdipFuncSwitch(kFuncCodeRewardLadderFirstClearance, open) // 屠龙考核首次通关奖励
		        || !GNET::SetIdipFuncSwitch(kFuncCodePlatVip, open) // 平台启动vip
		        || !GNET::SetIdipFuncSwitch(kFuncCodeFriendHelp, open) // 好友互助功能
		        || !GNET::SetIdipFuncSwitch(kFuncCodeShareLink, open) // 链接分享
		        || !GNET::SetIdipFuncSwitch(kFuncCodeGrc, open) // grc相关功能
		        || !GNET::SetIdipFuncSwitch(kFuncCodeQueueInviteFriend, open) // 排队邀请好友加速
		        || !GNET::SetIdipFuncSwitch(kFuncCodeGrcShareFriend, open) // 邀请未注册好友
		        || !GNET::SetIdipFuncSwitch(kFuncCodeGrcFriendGift, open) // grc好友礼物(客户端好友排行榜入口)
		        || !GNET::SetIdipFuncSwitch(kFuncCodeSensitiveInterface, open) // 腾讯敏感词
		        || !GNET::SetIdipFuncSwitch(kFuncCodeGRobot, open) // 游戏知己
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipQueryUserInfo, open) // 当前个人信息查询请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipSendMail, open) // 发送带附件系统邮件请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipQueryRoleRepu, open) // 查询角色声望请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipQueryByRoleid, open) // 根据角色id查询信息请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipQueryCorpsId, open) // 查询玩家公会id
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipQueryTaskComplete, open) // 任务完成查询请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipZk, open) // 中控防沉迷提醒请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipZkNotify, open) // 中控防沉迷提醒请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipZkBan, open) // 中控防沉迷禁玩请求
		        || !GNET::SetIdipFuncSwitch(kFuncCodeIdipZkVertify, open)) // 中控防沉迷强制验证请求
		{
			is_ok = false;
			break;
		}
		CampaignManager::GetInstance().ForbidCampaign(95, !open); // 朋友圈功能(客户端)
		CampaignManager::GetInstance().ForbidCampaign(171, !open); // 潘多拉录屏
		CampaignManager::GetInstance().ForbidCampaign(237, !open); // 手机绑定
		CampaignManager::GetInstance().ForbidCampaign(326, !open); // 潘多拉拍脸图
		CampaignManager::GetInstance().ForbidCampaign(386, !open); // 微社区
		CampaignManager::GetInstance().ForbidCampaign(422, !open); // 兴趣部落入口
		CampaignManager::GetInstance().ForbidCampaign(424, !open); // 心悦会员页
		CampaignManager::GetInstance().ForbidCampaign(423, !open); // 礼包中心入口
		CampaignManager::GetInstance().ForbidCampaign(432, !open); // 外发渠道专属活动 && 外发渠道福利页签
		CampaignManager::GetInstance().ForbidCampaign(76, !open); // Gvoice
		CampaignManager::GetInstance().ForbidCampaign(449, !open); // 微信详情页
		CampaignManager::GetInstance().ForbidCampaign(450, !open); // 游戏圈
		CampaignManager::GetInstance().ForbidCampaign(455, !open); // 直播电台
		CampaignManager::GetInstance().ForbidCampaign(462, !open); // 好友助力
		CampaignManager::GetInstance().ForbidCampaign(466, !open); // 超R管家
		CampaignManager::GetInstance().ForbidCampaign(463, !open); // 定向流量
	}
	break;

	case 413:
	{
		bool auto_inc = req.Param1 > 0;
		UserContainer::Instance().SetAutoIncMaxPlayer(auto_inc);
		LOG_TRACE("SetAutoIncMaxPlayer %d", req.Param1);
	}
	break;

	case 414:// 一键屏蔽封号禁言操作 0 关闭， 1 开启
	{
		bool open = (req.Param1 != 0);
		SLOG(FORMAT, "one_button ban_chat and ban_user").P("open", open);
		if (!GNET::SetIdipFuncSwitch(kFuncCodeIdipBanChat, open) || !GNET::SetIdipFuncSwitch(kFuncCodeIdipBanChatSilence, open) || !GNET::SetIdipFuncSwitch(kFuncCodeIdipAllzoneBanChat, open) || !GNET::SetIdipFuncSwitch(kFuncCodeIdipBanUser, open) || !GNET::SetIdipFuncSwitch(kFuncCodeIdipAllzoneBanUser, open))
		{
			break;
		}
	}
	break;

	case 415: // 禁止从假拍卖行购买指定id的物品
	{
		bool forbid = (req.Param1 == 0);
		int item_id = req.Param2;

		if (!GNET::SetIdipForbidFakeAuctionBuy(item_id, forbid))
		{
			is_ok = false;
			break;
		}
		LOG_TRACE("SetIdipForbidFakeAuctionBuy itemid=%d, forbid=%d", item_id, forbid ? 1 : 0);
	}
	break;

	case 416: // 拍卖行禁止物品或龙语交易（上架和购买）
	{
		bool forbid = (req.Param1 == 0);
		int item_id = req.Param2;
		int longyu_id = req.Param3;

		if (item_id > 0)
		{
			if (!GNET::SetIdipForbidAuctionTrade(item_id, forbid))
			{
				is_ok = false;
				break;
			}
		}
		if (longyu_id > 0)
		{
			if (!GNET::SetIdipForbidAuctionLongyuTrade(longyu_id, forbid))
			{
				is_ok = false;
				break;
			}
		}
		LOG_TRACE("SetIdipForbidAuctionTrade itemid=%d, longyuid=%d, forbid=%d", item_id, longyu_id, forbid ? 1 : 0);
	}
	break;

	case 417:	// 拍卖行对指定物品或龙语下架
	{
		int item_id = req.Param1;
		int longyu_id = req.Param2;
		GAUCTION::GAuctionManager::GetInstance().IDIPOperation(0, 0, item_id, longyu_id, AUCTION_IDIP_OP_CLOSE);
	}
	break;

	case 418: // 禁止指定id的身份
	{
		bool forbid = (req.Param1 == 0);
		int career_id = req.Param2;

		if (!GNET::SetIdipForbidCareer(career_id, forbid))
		{
			is_ok = false;
			break;
		}
		LOG_TRACE("SetIdipForbidCareer=%d, forbid=%d", career_id, forbid ? 1 : 0);
	}
	break;

	case 419:
	{
		bool forbid = (req.Param1 == 0);
		int item_id = req.Param2;
		int longyu_id = req.Param3;

		if (item_id > 0)
		{
			if (!GNET::SetIdipForbidAuctionRechargeLimit(item_id, forbid))
			{
				is_ok = false;
				break;
			}
		}
		if (longyu_id > 0)
		{
			if (!GNET::SetIdipForbidAuctionLongyuRechargeLimit(longyu_id, forbid))
			{
				is_ok = false;
				break;
			}
		}
		LOG_TRACE("SetIdipForbidAuctionTrade itemid=%d, longyuid=%d, forbid=%d", item_id, longyu_id, forbid ? 1 : 0);
	}
	break;

	case 420:
	{
#ifdef USE_IDIP_PROXY
		std::string account_str = HttpProtocol::UrlDecode(req.Param4);
#else
		std::string account_str = req.Content;
#endif
		Octets account(account_str.c_str(), account_str.size());
		int op_type = req.Param1;
		int value = req.Param2;

		if (0 == op_type)
		{
			MidasRechargeManager::GetInstance().DebugSetSaveAmtFix(account, value);
		}
		else if (1 == op_type)
		{
			MidasRechargeManager::GetInstance().DebugIncSaveAmtFix(account, value);
		}
		else if (2 == op_type)
		{
			MidasRechargeManager::GetInstance().DebugDecSaveAmtFix(account, value);
		}
		LOG_TRACE("GMT::DebugSaveAmtFix: account=%.*s|op_type=%d|value=%d", LOG_AC(account), op_type, value);
		GLog::formatlog("GMT::DebugSaveAmtFix", "account=%.*s|op_type=%d|value=%d", LOG_AC(account), op_type, value);
	}
	break;

	case 421:
	{
		try
		{
			int forbid = req.Param1;
			int time = req.Param2;
			ruid_t roleid = std::stol(req.Param4);

			RoleInfo *pInfo = RoleMap::Instance().FindOnline(roleid);
			if (pInfo)
			{
				pInfo->SetForbidName((bool)forbid, time);
			}
			else
			{
				getroleinfo_handle callback = [forbid, time](int retcode, ruid_t roleid, RoleInfo * pinfo)
				{
					if (0 == retcode)
					{
						pinfo->SetForbidName((bool)forbid, time);
						LOG_TRACE("GMT::SetForbidName delay. roleid=%ld forbid=%d time=%d", roleid, forbid, time);
					}
				};
				RoleMap::Instance().GetRoleInfo(roleid, Octets(), &callback);
			}
			LOG_TRACE("GMT::SetForbidName roleid=%ld forbid=%d time=%d", roleid, forbid, time);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM SetForbidName exception");
		}
	}
	break;

	case 422:
	{
		try
		{
			ruid_t corps_id = std::stol(req.Param4);
			auto pCorps = CorpsManager::GetInstance().GetCorp(corps_id);
			if (!pCorps)
			{
				break;
			}
#ifdef USE_IDIP_PROXY
			std::string name_str = HttpProtocol::UrlDecode(req.Param5);
#else
			std::string name_str = req.Content;
#endif
			Octets ucs2_name;
			CharsetConverter::conv_charset_t2u(Octets(name_str.c_str(), name_str.size()), ucs2_name);

			AllocName *rpc = (AllocName *)Rpc::Call(RPC_ALLOCNAME, AllocNameArg(g_zoneid, ALLOC_IDIP_FACTION_NAME, ucs2_name));
			rpc->corps_id = corps_id;
			rpc->callback = [this](ruid_t id, Octets & name, int ret)
			{
				int retcode = ret;
				if (retcode != 0)
				{
					retcode = IDIP_ERR_PARAM;
				}
				else
				{
					auto pCorps = CorpsManager::GetInstance().GetCorp(id);
					if (!pCorps)
					{
						return;
					}
					std::string rename((const char *)name.begin(), name.size());
					pCorps->IDIPRename(rename);
				}
				this->IDIPSend(retcode);
				delete this;
			};
			if (!UniqueNameClient::GetInstance()->SendProtocol(rpc))
			{
				onFailed(IDIP_ERR_PARAM);
				return HANDLE_RET_FINISH;
			}

			LOG_TRACE("GMT::IDIPRename id=%ld name=%.*s", corps_id, (int)name_str.size(), name_str.c_str());
			return HANDLE_RET_PENDING;
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPRename exception");
		}

	}
	break;

	case 423:// 服务器状态检查
	{
		DumpSearchServerStatusInfo();
	}
	break;

	case 424:// 防沉迷离线时间增加延迟功能
	{
		// 0为关
		bool delay = (req.Param1 != 0);
		UserContainer::Instance().SetZkDelay(delay);
	}
	break;

	case 425:// 断开L5连接
	{
		if (req.Param1 == 0 || req.Param1 == 1)
		{
			UniqueNameClient::GetInstance()->IDIPReConnect();
		}
		if (req.Param1 == 0 || req.Param1 == 2)
		{
			GrcClient::GetInstance()->IDIPReConnect();
		}
		if (req.Param1 == 0 || req.Param1 == 3)
		{
			GCSProviderClient::GetInstance()->IDIPReConnect();
		}
	}
	break;

	case 429:
	{
		int set_type = req.Param1;
		int set_level = req.Param2;
		ruid_t corps_id = atol(req.Param4.c_str());
		auto pCorps = CorpsManager::GetInstance().GetCorp(corps_id);
		if (!pCorps || !pCorps->IDIPSetLevel(set_type, set_level))
		{
			is_ok = false;
		}
	}
	break;

	case 430:
	{
		int sn = 0;
		char account[128] = {0};

		const char *szParamFmt = "%d%s";
		const int cntParamNeed = 2;
		int cntParam = sscanf(req.Param4.data(), szParamFmt, &sn, account);
		DEFER(SLOG(DEBUG, "zl_campaign_op::Serve").PS(req.OperType).PS(cntParam).PS(account).PS(sn););
		if (cntParamNeed != cntParam)
		{
			break;
		}
		std::string strAccount = account;
		GNET::Octets o_account(strAccount.data(), strAccount.length());
		MidasRechargeManager::GetInstance().DebugFixAuanyCheckStatus(o_account, 0, sn);
	}
	break;

	case 431:
	{
		try
		{
			int longyu_id = req.Param1;
			int equip_tid = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_LONGYU, longyu_id, equip_tid);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del longyu exception");
		}

	}
	break;

	case 432:
	{
		try
		{
			int longyu_id = req.Param1;
			int equip_idx = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_ADD_LONGYU, longyu_id, equip_idx);

		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM add longyu exception");
		}
	}
	break;

	case 433:
	{
		try
		{
			int longyu_id = req.Param1;
			int equip_idx = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_CHG_LONGYU, longyu_id, equip_idx, GNET::Octets(req.Param5.c_str(), req.Param5.size()));

		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM change longyu exception");
		}
	}
	break;

	case 434: // 本服强制上报社团竞赛数据到中心服
	{
		try
		{
			LOG_TRACE("IDIP::GM::CorpsBattleEntry::UploadBattleInfoToCenter");
			int day_begin = GetLocalDayBegin();
			CorpsBattleManager::GetInstance().UploadBattleInfoToCenter(GNET::CORPS_BATTLE_STUB_TYPE_COMMON, day_begin);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM upload corps battle exception");
		}
	}
	break;

	case 435:
	{
		int battle_type = req.Param1;
		if (IsCenterCoprsBattle(battle_type) && CenterManager::GetInstance().IsCenter())
		{
			LOG_TRACE("IDIP::GM::CorpsCenterBattleEntry::IDIPClearBattle:battle_type=%d", battle_type);
			CorpsBattleManager::GetInstance().IDIPClearBattle(battle_type);
		}
		else
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM clear corps battle exception:battle_type=%d", battle_type);
		}
	}
	break;

	//更新社团战场相关数据
	case 436:
	{
		int battle_type = req.Param1;
		int set_type = req.Param2;
		int set_value = req.Param3;
		ruid_t corps_id = atol(req.Param4.c_str());

		CorpsBattleManager::GetInstance().IDIPSetCorpsBattleInfoValue(battle_type, corps_id, set_type, set_value);
	}
	break;

	case 438:
	{
		try
		{
			int diaoxiang_id = req.Param1;
			ruid_t target_roleid = std::stol(req.Param4);


			GET_CENTER_BATTLE(centerbattle, CBT_ARENA_TEAM)
			centerbattle->TryGetPlayerStatus(diaoxiang_id, target_roleid);
			LOG_TRACE("GMT::TryGetPlayerStatus diaoxiang_id=%d roleid=%ld", diaoxiang_id, target_roleid);
			GET_CENTER_BATTLE_END
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM exception");
		}
	}
	break;

	case 439:
	{
		try
		{
			int equip_tid = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_ADD_EQUIP, equip_tid);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM add equip exception");
		}
	}
	break;

	case 441:
	{
		try
		{
			int value = req.Param1;
			ruid_t arena_group_id = std::stol(req.Param4);

			ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().SetGrade(arena_group_id, 0, value);
			LOG_TRACE("GMT::ArenaGroupManager::SetGrade arena_group_id=%ld value=%d", arena_group_id, value);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM exception");
		}
	}
	break;

	case 442:
	{
		bool forbid = (req.Param1 == 0);
		int item_id = req.Param2;

		if (!GNET::SetIdipForbidHometownFurniture(item_id, forbid))
		{
			is_ok = false;
			break;
		}
		LOG_TRACE("SetIdipForbidHometownFurniture itemid=%d, forbid=%d", item_id, forbid ? 1 : 0);
	}
	break;

	case 444:
	{
		int64_t roleid = atoll(req.Param4.c_str());
		int period = req.Param1;

		community_manager::GetInstance().ForbidHometownMsg(roleid, period);

		SLOG(DEBUG, "SetIdipForbidHometownMsg").P("roleid", roleid).P("period", period);
	}
	break;

	case 445:
	{
		int op_type = req.Param1;
		int function = req.Param2;

		community_manager::GetInstance().ForbidHometownCsp(op_type, function);

		SLOG(DEBUG, "SetIdipForbidHometownCsp").P("opType", op_type).P("function", function);
	}
	break;

	case 446: //清空社团竞赛天梯排行榜
	{
		LOG_TRACE("GMT::DSTPManager::Clear::CorpsBattleScore");
		DSTPManager::GetInstance().Clear(TPN_CORPS_BATTLE_SCORE, true);
		DSTPManager::GetInstance().Clear(TPN_CORPS_BATTLE_SCORE_OLD, true);
	}
	break;

	case 447:
	{
		CorpsManager::GetInstance().IDIPRefreshFightCapacityTopList();
	}
	break;

	case 448:
	{
		int64_t corps_id = atoll(req.Param4.c_str());
		CorpsManager::GetInstance().IDIPRefreshFightCapacityTopList(corps_id);
	}
	break;

	case 449:
	{
		try
		{
			if (CenterManager::GetInstance().IsCenter())
			{
				LOG_TRACE("IDIP::GM::center arena team single score::IDIP_rebuild_single_rank");
				GET_CENTER_BATTLE(centerbattle, CBT_ARENA_TEAM)
				CenterArenaTeamBattleServer *pCenterBattle = dynamic_cast<CenterArenaTeamBattleServer *>(centerbattle);
				if (pCenterBattle)
				{
					pCenterBattle->TryRebuildSingleRanks();
				}
				GET_CENTER_BATTLE_END
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM center arena team single score exception");
		}
	}
	break;

	case 450:
	{
		try
		{
			/*
			 * 将equip_index装备位的套装src_group_id 替换成 dst_group_id
			 * 如果src_group_id为零，表示新增套装
			 * 如果dst_group_id为零，表示删除套装
			 * 否则执行替换逻辑
			 */
			int equip_index = req.Param1;
			int src_group_id = req.Param2;
			int dst_group_id = req.Param3;
			ruid_t roleid = std::stol(req.Param4);
			GNET::Marshal::OctetsStream os;
			os << dst_group_id;
			MakeSecureIDIPProcess(roleid, SEC_IDIP_EXCHANGE_EQUIP_SUIT, equip_index, src_group_id, os);

		}
		catch (...)
		{
			GLog::log(LOG_ERR, "IDIP GM exchange equipment suit exception");
		}
	}
	break;

	case 451:
	{
		try
		{
			int gameid = req.Param1;
			ruid_t roleid = std::stol(req.Param4);

			PB::ipt_gm_cmd pb;
			pb.set_cmd_type(PB::ipt_gm_cmd::GCT_KICK_PLAYER);
			pb.add_params(roleid);

			GProviderServer::GetInstance()->DispatchProtocol(gameid, pb);
			GLog::log(LOG_INFO, "DS:Operation_Cmd_Excutor::kick player: gameid=%d, roleid=%ld", gameid, roleid);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM exception");
		}
	}
	break;

	case 452:
	{
		try
		{
			int title_id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_TITLE, title_id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del title exception");
		}
	}
	break;
	case 453:
	{
		try
		{
			int fashion_index = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_FASHION, fashion_index);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del fashion exception");
		}
	}
	break;

	case 454:
	{
		try
		{
			int64_t roleid = std::stoll(req.Param4);
			int period = req.Param1;
			community_manager::GetInstance().ForbidHometownDesignComment(roleid, period);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM disable hometown design");
		}
	}
	break;

	case 455:
	{
		try
		{
			int id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_PHOTO, id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del photo exception");
		}
	}
	break;

	case 456:
	{
		try
		{
			int op = req.Param1;
			int param = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_CHILD_MODIFY, op, param, GNET::Octets(req.Param5.c_str(), req.Param5.size()));
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM child modify exception");
		}
	}
	break;

	case 457:
	{
		try
		{
			int reward_id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_SEND_REWARD, reward_id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM send reward exception");
		}
	}
	break;

	case 458:
	{
		try
		{
			int id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_VEHICLE, id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM vehical del exception");
		}
	}
	break;

	case 460:
	{
		try
		{
			diaoxiang_manager::GetInstance().DebugClearDiaoxiangMap();
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM DebugClearDiaoxiangMap");
		}
	}
	break;

	case 461:
	{
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			diaoxiang_manager::GetInstance().IDIPCollectRoleID(roleid);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM IDIPCollectRoleID");
		}
	}
	break;

	case 462:
	{
		try
		{
			int count = req.Param1;
			diaoxiang_manager::GetInstance().IDIPConfirmCollect(count);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM IDIPConfirmCollect");
		}
	}
	break;

	case 463:
	{
		try
		{
			int eliminate_group_type = req.Param1;
			LOG_TRACE("IDIPCommand::EliminateGroupManager::ClearAllGrade:eliminate_group_type=%d", eliminate_group_type);
			if (IsValidEliminateGroupType(eliminate_group_type))
			{
				ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().ClearAllGrade(eliminate_group_type);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM EliminateGroupManager ClearAllGrade exception");
		}
	}
	break;

	case 464: // 禁止某社团执行某功能
	{
		int repu_id = req.Param1;
		int repu_val = req.Param2;
		int64_t corps_id = atoll(req.Param4.c_str());

		CorpsManager::GetInstance().IDIPIncSeasonRepu(corps_id, repu_id, repu_val);
	}
	break;

	case 465:   //清空社团赛季积分排行榜
	{
		CorpsSeasonManager::GetInstance().IDIPClearCorpsSeasonTopList();
	}
	break;

	case 466:   //清空社团赛季积分排行榜
	{
		CorpsManager::GetInstance().IDIPClearCorpsSeasonTopList();
	}
	break;

	case 467:
	{
		//设置大地图跨服时中心服数量
		GLOBAL_CONFIG.roam_global_world_center_count = req.Param1;
		GLog::log(LOG_INFO, "GMT set roam_global_world_center_count %d.", req.Param1);
	}
	break;
	case 468:
	{
		try
		{
			//结婚相关idip 现在只处理离婚
			ruid_t roleid = atoll(req.Param4.c_str());
			ruid_t spouseid = atoll(req.Param5.c_str());

			/*RoleInfo *pRole = RoleMap::Instance().Find(roleid);
			if (!pRole || !pRole->SNSReady())
			{
				is_ok = false;
				break;
			}
			if (pRole->friends.GetMaritalStatus() == MARRIAGE_ST_SINGLE)
			{
			  is_ok = false;
				break;
			}
			ruid_t spouseid = pRole->GetFacebook().spouse;
			//ruid_t sposueid = 0;
			//ds不判断这个人存不存在 在回来的时候判断
			//operation 0 默认是离婚
			*/
			LOG_TRACE("ds::dbidipdivorce: groom.id=%ld,bride.id=%ld", roleid, spouseid);
			const auto& call_back  = [this](DBIdipMarriageArg * arg, DBIdipMarriageRes * res)
			{
				if (res->retcode == 0)
				{
					ruid_t  roleid = arg->roleid;
					ruid_t  spouseid  = arg->spouseid;
					auto&& func  = [](RoleInfo * pInfo, ruid_t  spouseid)
					{
						DSTPManager::GetInstance().DeleteItem(TPN_CONTRACT_VALUE, pInfo->friends.GetFacebook().marriage_seq);

						pInfo->SetReputation(GNET::REPUID_HARMONIOUS_FLAG, 0);
						if (pInfo->friends.IsReady() && pInfo->friends.IsFriend(spouseid))
						{
							pInfo->friends.DeleteFromGroup(spouseid, FRIEND_GROUP_SPOUSE);
							pInfo->friends.SendFriendGroupMove(spouseid);
						}
						if (pInfo->IsOnline())
						{
							pInfo->friends.DivorceAbandonTask();
							PB::npt_harmonious_notify pb;
							pb.set_notify_type(PB::npt_harmonious_notify::NT_DIVORCE);
							//这里1代表成功
							pb.set_res(1);
							pInfo->SendMessage2Client(pb);
						}
						pInfo->friends.ClearSpouse();
					};
					RoleInfo *pRole = RoleMap::Instance().Find(roleid);
					if (pRole && pRole->SNSReady())
					{
						func(pRole, spouseid);
					}
					RoleInfo *pSpouse = RoleMap::Instance().Find(spouseid);
					if (pSpouse && pSpouse->SNSReady())
					{
						func(pSpouse, roleid);
					}
				}
				this->IDIPSend(res->retcode);
				delete this;
				LOG_TRACE("ds::dbidipdivorce: groom.id=%ld:retcode=%d", arg->roleid, res->retcode);
			};
			DBIdipMarriage *rpc = (DBIdipMarriage *)Rpc::Call(RPC_DBIDIPMARRIAGE, DBIdipMarriageArg(roleid, spouseid, 0));
			rpc->call_back = call_back;
			GameDBClient::GetInstance()->SendProtocol(rpc);
			return HANDLE_RET_PENDING;
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM dbidipdivorce exception");
		}
	}
	break;

	case 469:		//修改帮派id 和职位
	{
		ruid_t roleid = atoll(req.Param4.c_str());
		ruid_t corps_id = atoll(req.Param5.c_str());

		int   pos =  req.Param1;

		PB::ipt_idip_set_face_book pb;
		pb.set_set_type(PB::ipt_idip_set_face_book::ST_CORPS);
		pb.set_corps_id(corps_id);
		pb.set_roleid(roleid);
		pb.set_param(pos);  // 职位

		GameDBClient::GetInstance()->SendMessage(pb);
	}
	break;

	case 470:		//修改家园id
	{
		ruid_t roleid = atoll(req.Param4.c_str());
		ruid_t hometown_id = atoll(req.Param5.c_str());

		PB::ipt_idip_set_face_book pb;
		pb.set_set_type(PB::ipt_idip_set_face_book::ST_HOMETOWN);
		pb.set_hometown_id(hometown_id);
		pb.set_roleid(roleid);

		GameDBClient::GetInstance()->SendMessage(pb);
	}
	break;

	case 471:   //修改pdd_id
	{

		ruid_t roleid = atoll(req.Param4.c_str());
		ruid_t param_id = atoll(req.Param5.c_str());

		PB::ipt_idip_set_face_book pb;
		pb.set_set_type(PB::ipt_idip_set_face_book::ST_PDD_ID);
		pb.set_roleid(roleid);
		pb.set_param(param_id);

		GameDBClient::GetInstance()->SendMessage(pb);

	}
	break;

	case 472: //修改
	{
		int group_type = req.Param1;
		ruid_t roleid = atoll(req.Param4.c_str());
		ruid_t param_id = atoll(req.Param5.c_str());

		PB::ipt_idip_set_face_book pb;
		pb.set_set_type(PB::ipt_idip_set_face_book::ST_ELIMINATE_ID);
		pb.set_roleid(roleid);
		pb.set_param(param_id);
		pb.set_param2(group_type);

		GameDBClient::GetInstance()->SendMessage(pb);

	}
	break;
	case 473:
	{
		ruid_t roleid = atoll(req.Param4.c_str());
		ruid_t param_id = atoll(req.Param5.c_str());

		PB::ipt_idip_set_face_book pb;
		pb.set_set_type(PB::ipt_idip_set_face_book::ST_TEMPORARY_TEAM_ID);
		pb.set_roleid(roleid);
		pb.set_param(param_id);

		GameDBClient::GetInstance()->SendMessage(pb);
	}
	break;

	case 474:
	{
		int battle_type = req.Param1;
		if (!CenterManager::GetInstance().IsCenter())
		{
			LOG_TRACE("IDIPClearBattle:battle_type=%d", battle_type);
			CorpsBattleManager::GetInstance().IDIPClearBattle(battle_type);
		}
		else
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPClearBattle::exception:battle_type=%d", battle_type);
		}
	}
	break;

	case 475:
	{
		int battle_type = req.Param1;
		if (CenterManager::GetInstance().IsCenter())
		{
			LOG_TRACE("IDIPSyncBattleMatchResult:battle_type=%d", battle_type);
			CorpsBattleManager::GetInstance().IDIPSyncBattleMatchResult(battle_type);
		}
		else
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPSyncBattleMatchResult::exception:battle_type=%d", battle_type);
		}
	}
	break;

	case 476:
	{
		try
		{
			int part = req.Param1;
			int new_level = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_SET_ENHANCE_LEVEL, part, new_level);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM set enhance level exception");
		}
	}
	break;

	case 477:
	{
		try
		{
			int sword_id = req.Param1;
			int new_level = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_SET_SEVEN_CRIME_LEVEL, sword_id, new_level);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM set seven crime level exception");
		}
	}
	break;

	case 478:
	{
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_ERR_DRAGONBORN);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del err dragonborn exception");
		}
	}
	break;

	case 479:
	{
		try
		{
			int new_level = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_SET_GUARD_LEVEL, new_level);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM set guard level exception");
		}
	}
	break;

	case 480:
	{
		try
		{
			int diamond_pursue = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DIAMOND_PURSUE, diamond_pursue);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM set diamond pursue exception");
		}
	}
	break;
	case 481:
	{
		try
		{
			int need_reset_zero = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DIAMOND_PURSUE_CLEAN, need_reset_zero);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM set diamond pursue clean exception");
		}
	}
	break;
	case 482:
	{
		try
		{
			int add_exp = req.Param1;
			int add_money = req.Param2;
			int64_t garden_id = atoll(req.Param4.c_str());
			LOG_TRACE("idip_honeygarden_addexpandmoney: garden_id=%ju exp=%d money=%d", garden_id, add_exp, add_money);

			HoneyGardenManager::Instance().IDIPAddExpAndMoney(garden_id, add_exp, add_money);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM add honey garden exp and money exception");
		}
	}
	break;

	case 483:
	{
		try
		{
			int del_id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_PHOTO_DECO, del_id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del photo deco exception");
		}
	}
	break;

	case 484:
	{
		try
		{
			int type = req.Param1;
			int score = req.Param2;
			int64_t group_id = std::stol(req.Param4);
			if (type == 1)
			{
				ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().DebugSetGrade(group_id, score);
			}
			else if (type == 2)
			{
				if (CenterManager::GetInstance().IsCenter())
				{
					PB::ipt_eliminate_battle_notify notify;
					notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_IDIP_SET_SCORE);
					notify.set_group_id(group_id);
					notify.set_score(score);
					CenterManager::GetInstance().OnEliminateBattleNotify(notify);
				}
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del photo deco exception");
		}

	}
	break;

	case 485:
	{
		if (CenterManager::GetInstance().IsCenter())
		{
			PB::ipt_eliminate_battle_notify notify;
			notify.set_notify_type(PB::ipt_eliminate_battle_notify::EBNT_IDIP_RE_MATCH);
			CenterManager::GetInstance().OnEliminateBattleNotify(notify);
		}
	}
	break;

	case 486:
	{
		try
		{
			int repu_id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_MODIFY_TOP_STAR, repu_id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM set top star exception");
		}
	}
	break;

	case 487:
	{
		try
		{
			int isroam = req.Param1;
			int amity = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			ruid_t friendid = std::stol(req.Param5);

			RoleInfo *pRole = RoleMap::Instance().Find(roleid);
			if (pRole != NULL)
			{
				int ret = 0;
				if (pRole->friends.IsFriend(friendid))
				{
					ret = pRole->friends.AddAmity(friendid, amity, true, ADD_AMITY_TYPE_NONE, false);
				}
				else if (pRole->friends.IsRoamFriend(friendid))
				{
					ret = pRole->friends.AddRoamFriendAmity(friendid, amity, true, ADD_AMITY_TYPE_NONE, false);
				}
				LOG_TRACE("idip_add_friend_amity: isroam=%d amity=%d roleid=%ju friendid=%ju ret=%d", isroam, amity, roleid, friendid, ret);
			}
			else
			{
				LOG_TRACE("idip_add_friend_amity: isroam=%d amity=%d roleid=%ju friendid=%ju not find role", isroam, amity, roleid, friendid);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip_add_friend_amity exception");
		}
	}
	break;

	case 489:
	{
		try
		{
			int amity = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			ruid_t friendid = std::stol(req.Param5);

			//将对方加入自己的好友列表
			PB::roam_friend_t roam_friend;
			roam_friend.set_roleid(friendid);
			roam_friend.set_amity(amity);
			//roam_friend.mutable_info()->set_id(kv.first);
			//roam_friend.mutable_info()->set_name(srcname);
			//roam_friend.set_gender(msg.gender);
			//roam_friend.set_idphoto(msg.idphoto);
			//roam_friend.set_level(msg.level);
			//roam_friend.set_profession(msg.profession);

			std::string id2 = GetOssKeyWord(OSS_ROAM_FRIEND_LIST, roleid);
			OSSInterface::GetInstance().Lock(id2, [id2, srcroleid = roleid, dstroleid = friendid, roam_friend](int ret, const std::string & data)
			{
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != ret && (int)oss::OSSCode::OSSCODE_GET_DATA_INVALID != ret)
				{
					LOG_TRACE("idip_add_roam_friend oss Lock OSS_ROAM_FRIEND_LIST roleid=%ld friendid=%ld amity=%d, ret=%d", srcroleid, dstroleid, roam_friend.amity(), ret);
					return;
				}

				//函数结束自动释放远程id的锁
				OssUnlockShell shell(id2);

				std::string str_dstroleid = std::to_string(dstroleid);
				std::string friend_data;
				friend_data.resize(roam_friend.ByteSize());
				roam_friend.SerializeWithCachedSizesToArray((uint8_t *)friend_data.data());

				std::map<std::string, std::string> kv_map;
				if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID == ret)
				{
					std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
					kv_map[data_field] = ROAM_FRIEND_DATA_STR;
				}
				kv_map[str_dstroleid] = friend_data;
				OSSInterface::GetInstance().HMSet(id2, kv_map, [id2, srcroleid, dstroleid, roam_friend](int ret)
				{
					LOG_TRACE("idip_add_roam_friend oss HMSet roleid=%ld friendid=%ld amity=%d, ret=%d", srcroleid, dstroleid, roam_friend.amity(), ret);
					if ((int)oss::OSSCode::OSSCODE_SUCCESS != ret)
					{
						return;
					}

					RoleInfo *pself = RoleMap::Instance().Find(srcroleid);
					if (pself && pself->friends.IsRoamListReady())
					{
						auto roam_friends =  pself->friends.GetAllRoamFriends();
						auto iter = roam_friends.find(dstroleid);
						if (iter != roam_friends.end())
						{
							LOG_TRACE("idip_add_roam_friend oss set amity roleid=%ld friendid=%ld amity=%d", srcroleid, dstroleid, roam_friend.amity());
							iter->second.set_amity(roam_friend.amity());
						}
						else
						{
							LOG_TRACE("idip_add_roam_friend oss add friend roleid=%ld friendid=%ld amity=%d", srcroleid, dstroleid, roam_friend.amity());
							pself->friends.AddRoamFriend(roam_friend);
						}
					}

					//pself->friends.NotifyRoamFriendOnline(dstroleid);
				}, false);
			}, true);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip_add_friend exception");
		}
	}
	break;

	case 490:
	{
		ruid_t corps_id = std::stol(req.Param4);
		ruid_t target_corps_id = std::stol(req.Param5);

		auto corps = CorpsManager::GetInstance().GetCorp(corps_id);
		if (corps)
		{
			corps->AnswerUnion(NULL, target_corps_id, 1, true);
		}
	}
	break;

	case 491:
	{
		ruid_t corps_id = std::stol(req.Param4);
		ruid_t target_corps_id = std::stol(req.Param5);

		auto corps = CorpsManager::GetInstance().GetCorp(corps_id);
		if (corps)
		{
			corps->QuiteUnion(NULL, target_corps_id, true);
		}
	}
	break;
	case 493:
	{
		//修改座驾等级
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			int tid  = req.Param1;
			int level = req.Param2;
			MakeSecureIDIPProcess(roleid, SEC_IDIP_MODIFY_HORSE_LEVEL, tid, level);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip_modify_horse_level exception");
		}
	}
	break;

	case 494:
	{
		//修改伙伴等级 。 品质
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			int op = req.Param1;
			int tid  = req.Param2;
			int level = req.Param3;
			int64_t param2 = (int64_t)tid << 32 | level;
			MakeSecureIDIPProcess(roleid, SEC_IDIP_MODIFY_RETINUE_LEVEL, op, param2);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip_modify_retinue_level exception");
		}
	}
	break;

	case 495:
	{
		//删除圣核
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			int category = req.Param1;
			int level  = req.Param2;
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_DELTETRAHEDRON, category, level);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip del tetranhedrom exception");
		}

	}
	break;

	case 496:
	{
		//修改继承者天命层数，等级
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			int op = req.Param1;
			int index  = req.Param2;
			int level = req.Param3;
			int64_t param2 = (int64_t)index << 32 | level;
			MakeSecureIDIPProcess(roleid, SEC_IDIP_MODIFY_CHILD_LEVEL, op, param2);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip_modify_child_level exception");
		}
	}
	break;

	case 497:
	{
		//修改龙裔等级
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			int op = req.Param1;
			int index  = req.Param2;
			int level = req.Param3;
			int64_t param2 = (int64_t)index << 32 | level;
			MakeSecureIDIPProcess(roleid, SEC_IDIP_MODIFY_DRAGONBORN_LEVEL, op, param2);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip_modify_dragonborn_level exception");
		}
	}
	break;

	case 492:
	{
		/*增加同心值*/
		try
		{
			ruid_t roleid = std::stol(req.Param4);
			ruid_t roleid2 = std::stol(req.Param5);

			LOG_TRACE("DS::dbidipaddharmonious::roleid=%ld:roleid2=%ld:op=%d:value=%d", roleid, roleid2, req.Param1, req.Param2);
			const auto& call_back  = [this](DBIdipMarriageArg * arg, DBIdipMarriageRes * res)
			{
				if (res->retcode == 0)
				{
					ruid_t  roleid = arg->roleid;
					ruid_t  spouseid  = arg->spouseid;

					RoleInfo *role = RoleMap::Instance().Find(roleid);
					if (role)
					{
						int last_value = role->friends.GetHarmonious()->harmonious();
						int value = arg->suboperation ? arg->value : last_value + arg->value;
						role->friends.GetHarmonious()->set_harmonious(value);
						role->friends.UpdateToplist();
						LOG_TRACE("ds::dbidipAddHarmonious::roleid=%ld:last_value=%d:value=%d", roleid, last_value, value);
					}

					RoleInfo *role2 = RoleMap::Instance().Find(spouseid);
					if (role2)
					{
						int last_value = role2->friends.GetHarmonious()->harmonious();
						int value = arg->suboperation ? arg->value : last_value + arg->value;
						role2->friends.GetHarmonious()->set_harmonious(value);
						role2->friends.UpdateToplist();
						LOG_TRACE("ds::dbidipAddHarmonious::roleid=%ld:last_value=%d:value=%d", spouseid, last_value, value);
					}

				}
				this->IDIPSend(res->retcode);
				delete this;
				LOG_TRACE("ds::dbidipAddHarmonious::roleid=%ld:roleid2=%ld:retcode=%d", arg->roleid, arg->spouseid, res->retcode);
			};
			DBIdipMarriage *rpc = (DBIdipMarriage *)Rpc::Call(RPC_DBIDIPMARRIAGE, DBIdipMarriageArg(roleid, roleid2, 1, req.Param1, req.Param2));
			rpc->call_back = call_back;
			GameDBClient::GetInstance()->SendProtocol(rpc);
			return HANDLE_RET_PENDING;
		}
		catch (...)
		{

			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM idip_add_harmonious exception");
		}
	}
	break;

	case 498:
	{
		//通知gamedbd执行开服
		std::string _("3");
		Octets data((void *)_.data(), _.size());
		GNET::DebugCommand notify(0, 211, data);
		GameDBClient::GetInstance()->SendProtocol(notify);
	}
	break;

	case 499:
	{
		//将指定社团社团竞赛数据上报到中心服
		int battle_type = req.Param1;
		ruid_t corps_id = std::stol(req.Param4);

		if (!CenterManager::GetInstance().IsCenter())
		{
			GET_CENTER_BATTLE(centerbattle, CBT_FACTION)
			centerbattle->ServerApplyBattle(corps_id, battle_type);
			GET_CENTER_BATTLE_END
		}
	}
	break;

	/*新社团联赛数据上报*/
	case 500:
	{
		try
		{
			ruid_t corps_id = std::stol(req.Param4);
			if (!CenterManager::GetInstance().IsCenter())
			{
				HundredCorpsBattleManager::Instance().IDIPUploadCorpRanksInfo(corps_id);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPUploadCorpRanksInfo exception");
		}
	}
	break;
	/*新社团联赛本服分数*/
	case 501:
	{
		try
		{
			int modify_type = req.Param2; // 0:设置， 1：修改
			ruid_t corps_id = std::stol(req.Param4);
			HundredCorpsBattleManager::Instance().IDIPSetLocalScore(corps_id, req.Param1, modify_type);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPSetLocalScore exception");
		}
	}
	break;
	/*设置新社团联赛跨服阶段的分数*/
	case 502:
	{
		try
		{
			ruid_t corps_id = std::stol(req.Param4);
			int battle_type = req.Param1;
			int value = req.Param2;
			if (!CenterManager::GetInstance().IsCenter())
			{
				CorpsBattleManager::GetInstance().IDIPSetBattleScore(battle_type, corps_id, value);
				LOG_TRACE("IDIP::CorpsBattleManager::IDIPSetBattleScore::battle_type=%d:corps=%ld:value=%d", battle_type, corps_id, value);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPSetBattleScore exception");
		}

	}
	break;
	/*设置新社团联赛胜利次数*/
	case 503:
	{
		try
		{
			ruid_t corps_id = std::stol(req.Param4);
			int battle_type = req.Param1;
			int wincount = req.Param2;
			if (!CenterManager::GetInstance().IsCenter())
			{
				CorpsBattleManager::GetInstance().IDIPSetWinCount(battle_type, corps_id, wincount);
				LOG_TRACE("IDIP::CorpsBattleManager::IDIPSetWinCount::battle_type=%d:corps=%ld:wincount=%d", battle_type, corps_id, wincount);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPSetWinCount exception");
		}

	}
	break;
	/*新社团联赛重新生成某阶段的对战表*/
	case 504:
	{
		try
		{
			int battle_type = req.Param1;
			int state = req.Param2;	/*战场状态*/
			int offset_time = req.Param3; /*相对state开始时间的偏移*/
			if (!CenterManager::GetInstance().IsCenter())
			{
				CorpsBattleManager::GetInstance().IDIPCreateOrder(battle_type, state, offset_time);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPCreateOrder exception");
		}

	}
	break;

	/*新社团联赛生成一场对阵表*/
	case 505:
	{
		try
		{
			int battle_type = req.Param1;
			int state = req.Param2;
			int offset_time = req.Param3;
			ruid_t corps_id_1 = std::stol(req.Param4);
			ruid_t corps_id_2 = std::stol(req.Param5);
			if (!CenterManager::GetInstance().IsCenter())
			{
				CorpsBattleManager::GetInstance().IDIPCreateOneOrder(battle_type, corps_id_1, corps_id_2, state, offset_time);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPCreateOneOrder exception");
		}
	}
	break;

	case 506: //开关ds tcmalloc 内存相关操作 1 开始 0 结束
	{
#ifdef USE_TCMALLOC
		int start_flag = req.Param1;
		GDeliveryServer::GetInstance()->SetHeapProfileEnable(start_flag);
		SLOG(TRACE, "zl_campaign_op 506 ds tcmalloc heap profiler").PS(start_flag);
#endif
	}
	break;

	case 507: //开关db tcmalloc 内存相关操作 1 开始 0 结束
	{
#ifdef USE_TCMALLOC
		int start_flag = req.Param1;

		std::string str("6"); //结束
		if (start_flag == 1)
		{
			str = "5"; //开始
		}
		Octets data((void *)str.data(), str.size());
		GNET::DebugCommand notify(0, 5, data);
		GameDBClient::GetInstance()->SendProtocol(notify);
		SLOG(TRACE, "zl_campaign_op 507 db tcmalloc heap profiler").PS(start_flag);
#endif
	}
	break;

	case 508: //开关gs tcmalloc 内存相关操作 1 开始 0 结束
	{
#ifdef USE_TCMALLOC
		PB::ipt_gm_cmd pb;
		pb.set_cmd_type(PB::ipt_gm_cmd::GCT_TCMALLOC_HEAP_OP);
		pb.add_params(req.Param1);
		GProviderServer::GetInstance()->BroadcastProtocol(pb);

		SLOG(TRACE, "zl_campaign_op 508 gs tcmalloc heap profiler").P("flag", req.Param1);
#endif
	}
	break;

	case 509:
	{
		try
		{
			int longhun_id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_LONGHUN, longhun_id);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM del longhun exception");
		}
	}
	break;

	case 510:
	{
		try
		{
			int longhun_id = req.Param1;
			int validity_time = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_ADD_LONGHUN, longhun_id, validity_time);

		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM add longhun exception");
		}
	}
	break;

	case 511:
	{
		try
		{
			int longhun_id = req.Param1;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_CHG_LONGHUN, longhun_id, 0, GNET::Octets(req.Param5.c_str(), req.Param5.size()));

		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM change longyu exception");
		}
	}
	break;

	case 512:
	{
		int resource_num = req.Param1;
		ruid_t uniqueid = std::stol(req.Param4);
		LOG_TRACE("IDIP::zl_campaign_op 512 GMAddResourceNum uniqueid=%ld, resource_num=%d", uniqueid, resource_num);
		RoamCommunityManager::GetInstance().GMAddResourceNum(0, uniqueid, resource_num);
	}
	break;

	case 513:
	{
		try
		{
			int eliminate_group_type = req.Param1;
			LOG_TRACE("IDIPCommand::EliminateGroupManager::IdipResetEliminateScore:eliminate_group_type=%d", eliminate_group_type);
			if (IsValidEliminateGroupType(eliminate_group_type))
			{
				ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().IdipResetEliminateScore(eliminate_group_type);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM EliminateGroupManager IdipResetEliminateScore exception");
		}
	}
	break;
	/*新社团联赛清空某阶段对阵表*/
	case 514:
	{
		try
		{
			int battle_type = req.Param1;
			int state = req.Param2;	/*战场状态*/
			//if (!CenterManager::GetInstance().IsCenter())
			{
				CorpsBattleManager::GetInstance().IDIPClearOrder(battle_type, state);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPClearOrder exception");
		}

	}
	break;
	/*新社团联赛清空跨服排行榜*/
	case 515:
	{
		try
		{
			int battle_type = req.Param1;
			//if (!CenterManager::GetInstance().IsCenter())
			{
				CorpsBattleManager::GetInstance().IDIPClearCenterTop(battle_type);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPClearCenterTop exception");
		}

	}
	break;

	/*设置社团联赛金冠*/
	case 516:
	{
		try
		{
			int hundred_crown = req.Param1;
			int duration = req.Param2;
			ruid_t corps_id = std::stol(req.Param4);
			if (!CenterManager::GetInstance().IsCenter())
			{
				bool find = false;
				auto pCorps = CorpsManager::GetInstance().GetCorp(corps_id);
				if (pCorps)
				{
					find = true;
					pCorps->SetHundredCrown(hundred_crown, 0);
				}

				LOG_TRACE("IDIPCommand::SetHundredCrown corps_id=%ld, corwn=%d, duration=%d, find=%d", corps_id, hundred_crown, duration, (int)find);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPClearCenterTop exception");
		}

	}
	break;

	/*重设浩瀚天梯名人堂数据*/
	case 517:
	{
		try
		{
			bool test = req.Param1 == 0;
			PVEAGRMANAGER.IdipResetPersonalScore(test);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPResetPersonalScore exception");
		}
	}
	break;

	case 518:
	{
		int itemid = req.Param1;
		ruid_t roleid = std::stol(req.Param4);

		std::string __data;
		__data = std::string("7-") + std::to_string(itemid) + std::string("-") + std::to_string(roleid);
		Octets data((void *)__data.data(), __data.size());
		GNET::DebugCommand notify(0, 222, data);
		GameDBClient::GetInstance()->SendProtocol(notify);

		LOG_TRACE("IDIPQueryRoleItem: itemid=%d, roleid=%ld, data=%s", itemid, roleid, __data.c_str());

	}
	break;

	case 519:
	{
		try
		{
			int itemid = req.Param1;
			int itemcount = req.Param2;
			ruid_t roleid = std::stol(req.Param4);

			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_ITEM_MAIL, itemid, itemcount);
			LOG_TRACE("IDIPDelRoleMailItem: itemid=%d, roleid=%ld, item_count=%d", itemid, roleid, itemcount);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM forbid exp time exception");
		}
	}
	break;

	case 520:
	{
		try
		{
			int itemid = req.Param1;
			int itemcount = req.Param2;
			ruid_t roleid = std::stol(req.Param4);
			MakeSecureIDIPProcess(roleid, SEC_IDIP_DEL_ITEM_REWARD_BAG, itemid, itemcount);
			LOG_TRACE("IDIPDelRoleRewardBagItem: itemid=%d, roleid=%ld, item_count=%d", itemid, roleid, itemcount);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM forbid exp time exception");
		}
	}
	break;

	case 521:
	{
		try
		{
			int before_idx = req.Param1;
			ruid_t ag_id = std::stol(req.Param4);
			ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().IDIPReuploadArenaGroupScore(ag_id, before_idx);
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPReuploadArenaGroupScore exception");
		}
	}
	break;

	case 522:
	{
		try
		{
			char oss_field[128] = {0};
			char oss_value[128] = {0};

			const char *szParamFmt = "%s%s";
			const int cntParamNeed = 2;
			int cntParam = sscanf(req.Param5.data(), szParamFmt, oss_field, oss_value);
			if (cntParamNeed != cntParam)
			{
				break;
			}

			int can_override = req.Param1 != 0 ? 1 : 0;

			std::string str_key = req.Param4;
			std::string str_field = std::string(oss_field);
			std::string str_value = std::string(oss_value);
			OSSInterface::GetInstance().HSet(str_key, str_field, str_value, [str_key, str_field, str_value](int retcode)
			{
				LOG_TRACE("IDIP call HSet: key=%s, field=%s, value=%s, retcode=%d", str_key.c_str(), str_field.c_str(), str_value.c_str(), retcode);
			}, 0, 0, can_override, false);

		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP call HSet exception");
		}
	}
	break;

	case 523:
	{
		try
		{
			char oss_field[128] = {0};
			char oss_roleid[128] = {0};

			const char *szParamFmt = "%s%s";
			const int cntParamNeed = 2;
			int cntParam = sscanf(req.Param5.data(), szParamFmt, oss_field, oss_roleid);
			if (cntParamNeed != cntParam)
			{
				break;
			}

			std::string str_key = req.Param4;
			std::string str_field = std::string(oss_field);
			ruid_t roleid = atol(oss_roleid);
			OSSInterface::GetInstance().HGet(str_key, str_field, [str_key, str_field, roleid](int retcode, const string & data)
			{
				RoleInfo *pinfo = RoleMap::Instance().FindOnline(roleid);
				if (pinfo)
				{
					ChatPublic msg;
					msg.roleid = pinfo->roleid;
					msg.channel = 0;
					msg.emotion = 0;
					msg.nation = pinfo->nation;
					msg.msg = Octets("-1", strlen("-1"));
					if (retcode == 0)
					{
						msg.msg = Octets(data.data(), data.size());
					}
					CharsetConverter::conv_charset_t2u(msg.msg, msg.msg);
					msg.localsid = 0;
					GDeliveryServer::GetInstance()->SendMarshalData(pinfo->linksid, pinfo->localsid, msg);
				}
				LOG_TRACE("IDIP call HGet: key=%s, field=%s, value=%s, retcode=%d", str_key.c_str(), str_field.c_str(), data.c_str(), retcode);
			});

		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP call HSet exception");
		}
	}
	break;
	/*重置新社团联赛社团信息*/
	case 524:
	{
		try
		{
			int battle_type = req.Param1;
			if (!CenterManager::GetInstance().IsCenter())
			{
				CorpsBattleManager::GetInstance().IDIPClearHundredBattleInfo(battle_type);
				LOG_TRACE("IDIP::CorpsBattleManager::IDIPClearHundredBattleInfo::battle_type=%d", battle_type);
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIPClearHundredBattleInfo exception");
		}

	}
	break;
	/*输出上赛季浩瀚天梯内存排名数据*/
	case 525:
	{
		try
		{
			PVEAGRMANAGER.IdipPrintLastSeasonData();
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IdipPrintLastSeasonData exception");
		}
	}
	break;
	/*清理浩瀚天梯reids排名脏数据*/
	case 526:
	{
		try
		{
			PVEAGRMANAGER.IdipRefixSeasonData();
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IdipRefixSeasonData exception");
		}
	}
	break;

	case 527:
	{
		try
		{
			GET_CENTER_BATTLE(centerbattle, CBT_ARENA_ELIMINATE)
			if (centerbattle->GetCenterZoneID() == g_zoneid)
			{
				LOG_TRACE("IDIPCommand::EliminateGroupManager::IdipOnFinalsEnd");
				auto *pCorpsBattleEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(GNET::CORPS_BATTLE_STUB_TYPE_ELIMINATE);
				if (pCorpsBattleEntry)
				{
					pCorpsBattleEntry->OnFinalsEnd();
				}
			}
			GET_CENTER_BATTLE_END
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM EliminateGroupManager IdipOnFinalsEnd exception");
		}
	}
	break;

	case 528:
	{
		try
		{
			// 刷新中心服的battleinfo到普通服
			LOG_TRACE("IDIPCommand::RefreshCenterBattleInfoToNormal");

			auto *pCorpsBattleEntry = CorpsBattleManager::GetInstance().GetCorpsBattleEntry(GNET::CORPS_BATTLE_STUB_TYPE_ELIMINATE);
			if (pCorpsBattleEntry)
			{
				auto *pCorpsEliminateBattle = dynamic_cast<CorpsEliminateBattle*>(pCorpsBattleEntry);
				if (pCorpsEliminateBattle)
				{
					is_ok = pCorpsEliminateBattle->IDIPRefreshBattleInfoToNormal();
				}
				else
				{
					LOG_TRACE("IDIPCommand::RefreshCenterBattleInfoToNormal:cast_failed");
					is_ok = false;
				}
			}
			else
			{
				LOG_TRACE("IDIPCommand::RefreshCenterBattleInfoToNormal:no_corps_battle_entry");
				is_ok = false;
			}
		}
		catch (...)
		{
			is_ok = false;
			GLog::log(LOG_ERR, "IDIP GM RefreshCenterBattleInfoToNormal exception");
		}
	}
	break;

	default:
		is_ok = false;
		break;
	}

	if (is_ok)
	{
		onSucceeded();
	}
	else
	{
		onFailed(IDIP_ERR_PARAM);
	}
	return HANDLE_RET_FINISH;
}
void zl_campaign_op::CallBack_SetRechargeVersion(zl_campaign_op *p_request, SetRechargeVersionData *arg, RpcRetcode *res)
{
	std::string ret_msg;
	int retcode = res->retcode;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_PARAM;
	}
	else
	{
		if (SERVER_LEVEL_EXP_FACTOR == arg->activity_type)
		{
			DSTPManager::GetInstance().SetServerLevelExpFactor(arg->version);
			DSTPManager::GetInstance().SendServerConfig();
			DSTPManager::GetInstance().SaveServerConfig();
		}
	}
	p_request->IDIPSend(retcode, ret_msg);
	GLog::log(LOG_INFO, "zl_campaign_op::CallBack_SetRechargeVersion. type=%d ret=%d version=%d", arg->activity_type, res->retcode, arg->version);
	delete p_request;
}
//10:设置排队人数/////////////////////////////////////////////////////////////////////////////////////////
struct idip_change_player_limit : public idip_req_body<IDIPCmdReqChangePlayerLimit>
{
	idip_change_player_limit() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_change_player_limit::Serve()
{
	UserContainer::Instance().SetPlayerLimit(req.Param1, req.Param1 + req.Param2, req.Param3);
	UserContainer::Instance().SetAutoIncMaxPlayer(false);
	LOG_TRACE("idip_change_player_limit %d, %d, %d", req.Param1, req.Param2, req.Param3);
	onSucceeded();
	return HANDLE_RET_FINISH;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////
//IDIP_AQ_COMMAND
///////////////////////////////////////////////////////////////////////////////////////////////////////////

//查询openid基本信息
struct idip_aq_query_role : public idip_req_body<IDIPCmdReqCommon>
{
protected:
	PB::db_account_ds_data pb_data;
public:
	idip_aq_query_role() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	void SendResponse(int retcode, const std::vector<int64_t>& info_list);

	void GetRoleData(int64_t role_id);

	static void CallBack_RoleList(idip_aq_query_role *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_aq_query_role *p_req, GetUserArg *arg, GetUserRes *res);
};
int idip_aq_query_role::Serve()
{
	SLOG(FORMAT, "idip_aq_query_role")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId);

	Octets user_account = IDIP_ACCOUNT(req);
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_aq_query_role::CallBack_GetUser, this, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		if (!user->IsUserRole(req.RoleId))
		{
			SendResponse(1, std::vector<int64_t>());
			return HANDLE_RET_FINISH;
		}

		pb_data = user->GetDSProperty();
		GetRoleData(req.RoleId);
		return HANDLE_RET_PENDING;
	}
	return HANDLE_RET_FINISH;
}
void idip_aq_query_role::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	IDIPDataPacket<IDIPCmdRspQueryOpenidInfo> rsp;

	int find = 0;
	auto& body_value = rsp.body;
	if (retcode == 0)
	{
		RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
		if (p_role != NULL)
		{
			find = 1;
			auto& idip_value = p_role->show_property.data().idip_info();

			body_value.RoleId = req.RoleId;
#ifdef USE_UTF8
			role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
			body_value.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
			body_value.GoldCoin = idip_value.money();
			body_value.Fight = p_role->fightingcapacity;
			body_value.Level = p_role->level;
			body_value.Diamond = idip_value.cash_bind();
			body_value.Cash = GetRechargeInfoCanUseCash(*(pb_data.mutable_recharge_info()));
		}
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = (find == 1) ? 0 : 1;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_aq_query_role::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}
void idip_aq_query_role::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_query_role::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_aq_query_role::CallBack_RoleList(idip_aq_query_role *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}
void idip_aq_query_role::CallBack_GetUser(idip_aq_query_role *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_aq_query_role::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->SendResponse(1, std::vector<int64_t>());
		delete p_req;
	}
	else
	{
		Octets pb_ot = res->value.ds_data.value;
		if (pb_ot.size())
		{
			p_req->pb_data.ParseFromArray(pb_ot.begin(), pb_ot.size());
		}

		bool roleid_valid = false;
		ruid_t roleid = p_req->req.RoleId;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->SendResponse(1, std::vector<int64_t>());
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}

//修改游戏币数量
struct idip_aq_modify_money : public idip_req_body<IDIPCmdReqAQModifyMoney>
{
	int IsAQ;
public:
	idip_aq_modify_money(int is_aq) : idip_req_body(), IsAQ(is_aq) {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	void GetRoleData(int64_t role_id);

	void SendResponse(int retcode);
	static void CallBack_RoleList(idip_aq_modify_money *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_aq_modify_money *p_req, GetUserArg *arg, GetUserRes *res);
};
int idip_aq_modify_money::Serve()
{
	SLOG(FORMAT, "idip_aq_modify_money")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId)
	.P("Value", req.ModifyNum);

	Octets user_account = IDIP_ACCOUNT(req);
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_aq_modify_money::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}

	if (!user->IsUserRole(req.RoleId))
	{
		SendResponse(IDIP_ERR_INVALID_ROLE);
		return HANDLE_RET_FINISH;
	}

	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (p_role == NULL)
	{
		GetRoleData(req.RoleId);
		return HANDLE_RET_PENDING;
	}
	else
	{
		SendResponse(0);
		return HANDLE_RET_FINISH;
	}
	return HANDLE_RET_FINISH;
}
void idip_aq_modify_money::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_modify_money::CallBack_RoleList, this,   std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_aq_modify_money::CallBack_RoleList(idip_aq_modify_money *p_req, const std::vector<int64_t>& info_list)
{
	RoleInfo *p_role = RoleMap::Instance().Find(p_req->req.RoleId);
	if (p_role == NULL)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
	}
	else
	{
		p_req->SendResponse(0);
	}
	delete p_req;
}
void idip_aq_modify_money::CallBack_GetUser(idip_aq_modify_money *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_aq_modify_money::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ACCOUNT);
		delete p_req;
	}
	else
	{
		bool roleid_valid = false;
		ruid_t roleid = p_req->req.RoleId;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}
void idip_aq_modify_money::SendResponse(int retcode)
{
	if (retcode == 0)
	{
		MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_MONEY, req.ModifyNum);
	}

	IDIPSend(retcode);

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iValue", req.ModifyNum)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);
}
/*
//修改钻石
struct idip_aq_modify_diamond : public idip_req_body
{
	int IsAQ;		//是否安全IDIP
	int Type;		//钻石类型，0 绑定钻石 1 非绑定
	int Value;		//修改数量，- 减, + 加
	int IsLogin;		//是否需要重新登录，0 否 1 是
	unsigned int Source;	//邮件发送来源，流水号
	std::string Serial;	//序列号
public:
	idip_aq_modify_diamond(int is_aq) : idip_req_body(), IsAQ(is_aq), Type(0), Value(0), IsLogin(0), Source(0), Serial()
	{}
	idip_aq_modify_diamond(const idip_aq_modify_diamond & rhs) : idip_req_body(rhs), IsAQ(rhs.IsAQ),
		Type(rhs.Type), Value(rhs.Value), IsLogin(rhs.IsLogin), Source(rhs.Source), Serial(rhs.Serial)
	{}
	idip_aq_modify_diamond * Clone() { return new idip_aq_modify_diamond(*this); }

	virtual bool Parse(const Json::Value & json_content);
	virtual int Serve(* );

	void SendResponse(*  int retcode);
	static void CallBack_GetUser(idip_aq_modify_diamond * p_req, *  GetUserArg * arg, GetUserRes * res);
};
bool idip_aq_modify_diamond::Parse(const Json::Value & json_content)
{
	if (!idip_req_body::Parse(json_content))
		return false;

	if (!json_content.isMember("body"))
		return false;

	Json::Value json_value = json_content["body"];
	if(IsAQ==1)
	{
		if (!json_parse_value(json_value, "IsLogin", IsLogin))
			return false;
		if (!json_parse_value(json_value, "Type", Type))
			return false;
	}
	if (!json_parse_value(json_value, "Value", Value))
		return false;
	if (!json_parse_value(json_value, "Source", Source))
		return false;
	if (!json_parse_value(json_value, "Serial", Serial))
		return false;

	return true;
}
int idip_aq_modify_diamond::Serve(* )
{
	std::stringstream ss;
	ss << "openid=" << OpenId << ";" << "roleid=" << RoleId << ";"<<"Value"<<Value<<";";
	GLog::formatlog("idip_aq_modify_diamond::serve", "%s", ss.str().c_str());

	Octets user_account = IDIP_ACCOUNT(req);
	UserInfo* user = UserContainer::Instance().FindUser(user_account);

	if (user == NULL)
	{
		GetUser * rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		idip_aq_modify_diamond * clone_req = Clone();
		rpc->call_back = std::bind(&idip_aq_modify_diamond::CallBack_GetUser, clone_req,
				std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return 0;
	}
	else
	{
		SendResponse(  0);
		return 0;
	}
	return 0;
}
void idip_aq_modify_diamond::CallBack_GetUser(idip_aq_modify_diamond * p_req, *  GetUserArg * arg, GetUserRes * res)
{
	LOG_TRACE("idip_aq_modify_diamond::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->SendResponse(  1);
	}
	else
	{
		p_req->SendResponse(  0);
	}
	delete p_req;
}
void idip_aq_modify_diamond::SendResponse(*  int retcode)
{
	try
	{
		if (retcode == 0)
		{
			Octets user_account = IDIP_ACCOUNT(req);
			Octets midas_sn = CreateUUID(user_account);

			int midas_type = (Type == 0) ? MIDAS_DEC_CASH_TYPE_IDIP_BIND : MIDAS_DEC_CASH_TYPE_IDIP_UNBIND;

			if (Value < 0)
			{
				MidasRepair::SavePrePayFail(RoleId, user_account, midas_type, -1 * Value, midas_sn);
			}
			else
			{
				int bind = (Type == 0) ? 1 : 0;
				MidasRepair::SavePresentFail(RoleId, user_account, Value, bind, midas_sn);
			}
		}
		Json::Value root;
		req_head.Cmdid = (IsAQ) ? IDIP_AQ_DO_UPDATE_DIAMOND_RSP : IDIP_CMD_RES_MOD_DIAMOND;
		req_head.WriteResponse(root);

		Json::Value body_value;
		json_set_value(body_value, "Result", (int)retcode);
		json_set_value(body_value, "RetMsg", "success");

		json_set_value(root, "body", body_value);

		Json::FastWriter writer;
		std::string result = writer.write(root);

		Send(result);
	}
	catch(...)
	{
		LOG_TRACE("idip_aq_modify_diamond::SendResponse. catch");
	}
	if(retcode==0)
	{
		std::stringstream s1;
		s1 << "IDIPFLOW"
		<< "|" << TLOG::FT(Timer::GetTime()).c_str()
		<< "|" << AreaId
		<< "|" << OpenId
		<< "|" << 0
		<< "|" << Value
		<< "|" << Serial
		<< "|" << Source
		<< "|" << req_head.Cmdid
		;
		TLOG::log("%.*s", (int)s1.str().size(), s1.str().c_str());
		GLog::formatlog("","%s", s1.str().c_str());
	}
}*/
//禁止参与排行榜
struct idip_forbid_toplist : public idip_req_body<IDIPCmdReqForbidToplist>
{
public:
	idip_forbid_toplist() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleData(int64_t role_id);

	static void CallBack_RoleList(idip_forbid_toplist *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_forbid_toplist *p_req, GetUserArg *arg, GetUserRes *res);
};
void idip_forbid_toplist::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_forbid_toplist::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_forbid_toplist::CallBack_GetUser(idip_forbid_toplist *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_forbid_toplist::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ACCOUNT);
		delete p_req;
	}
	else
	{
		bool roleid_valid = false;
		ruid_t roleid = p_req->req.RoleId;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->onFailed(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}
int idip_forbid_toplist::Serve()
{
	SLOG(FORMAT, "idip_forbid_toplist")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId);

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iValue", req.BanTime)
	.P("vDesc", req.BanReason)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	Octets user_account = IDIP_ACCOUNT(req);
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	ruid_t roleid = req.RoleId;
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_forbid_toplist::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		if (!user->IsUserRole(roleid))
		{
			onFailed(IDIP_ERR_INVALID_ROLE);
			return HANDLE_RET_FINISH;
		}
	}

	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		GetRoleData(roleid);
		return HANDLE_RET_PENDING;
	}
	else
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(req.BanReason);
#else
		std::string content = req.BanReason;
#endif
		MakeSecureIDIPProcess(roleid, SEC_IDIP_TOPLIST, req.BanTime, (int)req.ToplistId, Octets(content.data(), content.size()));
		onSucceeded();
		return HANDLE_RET_FINISH;
	}
	return HANDLE_RET_FINISH;
}
void idip_forbid_toplist::CallBack_RoleList(idip_forbid_toplist *p_req, const std::vector<int64_t>& info_list)
{
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ROLE);
	}
	else
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(p_req->req.BanReason);
#else
		std::string content = p_req->req.BanReason;
#endif
		MakeSecureIDIPProcess(roleid, SEC_IDIP_TOPLIST, p_req->req.BanTime, (int)p_req->req.ToplistId, Octets(content.data(), content.size()));
		p_req->onSucceeded();
	}
	delete p_req;
}

//禁止加好友
struct idip_forbid_friend : public idip_req_body<IDIPCmdReqForbid>
{
public:
	idip_forbid_friend() { }

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void SendResponse(int retcode);

	static void CallBack_ForbidUser(idip_forbid_friend *p_req, ForbidUserArg *arg, DBForbidUserRes *res);
	static void CallBack_ForbidRole(idip_forbid_friend *p_req, DBPutForbidArg *arg, DBPutForbidRes *res);

	void GetRoleData(int64_t role_id);
	static void CallBack_RoleList(idip_forbid_friend *p_req, const std::vector<int64_t>& info_list);
};
void idip_forbid_friend::SendResponse(int retcode)
{
	std::string ret_msg;
	if (retcode == 0 || retcode == 1)
	{
		ret_msg = "success";

		SLOG(FORMAT, "idip_command")
		.P("vopenid", req.OpenId)
		.P("iAttribute", req.RoleId)
		.P("iValue", req.BanTime)
		.P("vDesc", req.BanReason)
		.P("vSerial", req.Serial)
		.P("iSource", req.Source)
		.P("iCmdID", req_head.Cmdid);
	}
	else
	{
		ret_msg = "failed";
	}

	IDIPSend(retcode, ret_msg);
}
int idip_forbid_friend::Serve()
{

	SLOG(FORMAT, "idip_forbid")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId)
	.P("time", req.BanTime);

	Octets user_account = IDIP_ACCOUNT(req);
	req.BanReason = HttpProtocol::UrlDecode(req.BanReason);

	Octets utf8_des(req.BanReason.c_str(), req.BanReason.size());
	Octets ucs2_des;
	CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

	if (req.RoleId == 0)
	{
		ForbidUserArg arg;
		arg.operation = GNET_FORBID_FRIEND; //禁止加好友
		arg.account = user_account;
		arg.time = req.BanTime;
		arg.reason = ucs2_des;

		DBForbidUser *rpc = (DBForbidUser *)Rpc::Call(RPC_DBFORBIDUSER, arg);

		rpc->call_back = std::bind(&idip_forbid_friend::CallBack_ForbidUser, this, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
		if (p_role != NULL)
		{
			if (p_role->account != user_account)
			{
				SendResponse(IDIP_ERR_INVALID_ROLE);
				return HANDLE_RET_FINISH;
			}
			else
			{
				GRoleForbid forbid(GNET_FORBID_FRIEND, req.BanTime, Timer::GetTime(), ucs2_des);
				DBPutForbidArg arg(req.RoleId, forbid);
				DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

				rpc->call_back = std::bind(&idip_forbid_friend::CallBack_ForbidRole, this, std::placeholders::_1, std::placeholders::_2);

				GameDBClient::GetInstance()->SendProtocol(rpc);
				return HANDLE_RET_PENDING;
			}
		}
		else
		{
			GetRoleData(req.RoleId);
			return HANDLE_RET_PENDING;
		}
	}

	return HANDLE_RET_FINISH;
}
void idip_forbid_friend::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_forbid_friend::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_forbid_friend::CallBack_RoleList(idip_forbid_friend *p_req, const std::vector<int64_t>& info_list)
{
	Octets user_account = p_req->IDIP_ACCOUNT(p_req->req);
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL || p_role->account != user_account)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
		delete p_req;
	}
	else
	{
		Octets utf8_des(p_req->req.BanReason.c_str(), p_req->req.BanReason.size());
		Octets ucs2_des;
		CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

		GRoleForbid forbid(GNET_FORBID_LOGIN, p_req->req.BanTime, Timer::GetTime(), ucs2_des);
		DBPutForbidArg arg(roleid, forbid);
		DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

		rpc->call_back = std::bind(&idip_forbid_friend::CallBack_ForbidRole, p_req, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
}
void idip_forbid_friend::CallBack_ForbidUser(idip_forbid_friend *p_req, ForbidUserArg *arg, DBForbidUserRes *res)
{
	if (res->retcode == ERR_NOTFOUND)
	{
		res->retcode = IDIP_ERR_INVALID_ACCOUNT;
	}
	if (res->retcode == ERROR_FORBID_IGNORE)
	{
		res->retcode = 0;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}
void idip_forbid_friend::CallBack_ForbidRole(idip_forbid_friend *p_req, DBPutForbidArg *arg, DBPutForbidRes *res)
{
	if (res->retcode == -1)
	{
		res->retcode = IDIP_ERR_INVALID_ROLE;
	}
	if (res->retcode == ERROR_FORBID_IGNORE)
	{
		res->retcode = 0;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}

//禁止上传社团招募图片
struct idip_forbid_corps_recruit_url : public idip_req_body<IDIPCmdReqForbid>
{
public:
	idip_forbid_corps_recruit_url() { }

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void SendResponse(int retcode);

	static void CallBack_ForbidRole(idip_forbid_corps_recruit_url *p_req, DBPutForbidArg *arg, DBPutForbidRes *res);

	void GetRoleData(int64_t role_id);
	static void CallBack_RoleList(idip_forbid_corps_recruit_url *p_req, const std::vector<int64_t>& info_list);
};
void idip_forbid_corps_recruit_url::SendResponse(int retcode)
{
	std::string ret_msg;
	if (retcode == 0 || retcode == 1)
	{
		ret_msg = "success";

		SLOG(FORMAT, "idip_command")
		.P("vopenid", req.OpenId)
		.P("iAttribute", req.RoleId)
		.P("iValue", req.BanTime)
		.P("vDesc", req.BanReason)
		.P("vSerial", req.Serial)
		.P("iSource", req.Source)
		.P("iCmdID", req_head.Cmdid);
	}
	else
	{
		ret_msg = "failed";
	}

	IDIPSend(retcode, ret_msg);
}
int idip_forbid_corps_recruit_url::Serve()
{

	SLOG(FORMAT, "idip_forbid")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId)
	.P("time", req.BanTime);

	Octets user_account = IDIP_ACCOUNT(req);
	req.BanReason = HttpProtocol::UrlDecode(req.BanReason);

	Octets utf8_des(req.BanReason.c_str(), req.BanReason.size());
	Octets ucs2_des;
	CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

	if (req.RoleId == 0)
	{
		SendResponse(IDIP_ERR_INVALID_ROLE);
		return HANDLE_RET_FINISH;
	}
	else
	{
		RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
		if (p_role != NULL)
		{
			if (p_role->account != user_account)
			{
				SendResponse(IDIP_ERR_INVALID_ROLE);
				return HANDLE_RET_FINISH;
			}
			else
			{
				GRoleForbid forbid(GNET_FORBID_CORP_RECRUIT_URL, req.BanTime, Timer::GetTime(), ucs2_des);
				DBPutForbidArg arg(req.RoleId, forbid);
				DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

				rpc->call_back = std::bind(&idip_forbid_corps_recruit_url::CallBack_ForbidRole, this, std::placeholders::_1, std::placeholders::_2);

				GameDBClient::GetInstance()->SendProtocol(rpc);
				return HANDLE_RET_PENDING;
			}
		}
		else
		{
			GetRoleData(req.RoleId);
			return HANDLE_RET_PENDING;
		}
	}

	return HANDLE_RET_FINISH;
}
void idip_forbid_corps_recruit_url::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_forbid_corps_recruit_url::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_forbid_corps_recruit_url::CallBack_RoleList(idip_forbid_corps_recruit_url *p_req, const std::vector<int64_t>& info_list)
{
	Octets user_account = p_req->IDIP_ACCOUNT(p_req->req);
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL || p_role->account != user_account)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
		delete p_req;
	}
	else
	{
		Octets utf8_des(p_req->req.BanReason.c_str(), p_req->req.BanReason.size());
		Octets ucs2_des;
		CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

		GRoleForbid forbid(GNET_FORBID_CORP_RECRUIT_URL, p_req->req.BanTime, Timer::GetTime(), ucs2_des);
		DBPutForbidArg arg(roleid, forbid);
		DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

		rpc->call_back = std::bind(&idip_forbid_corps_recruit_url::CallBack_ForbidRole, p_req, std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
}
void idip_forbid_corps_recruit_url::CallBack_ForbidRole(idip_forbid_corps_recruit_url *p_req, DBPutForbidArg *arg, DBPutForbidRes *res)
{
	if (res->retcode == -1)
	{
		res->retcode = IDIP_ERR_INVALID_ROLE;
	}
	if (res->retcode == ERROR_FORBID_IGNORE)
	{
		res->retcode = 0;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}

//解散聊天群组
struct idip_dismiss_group : public idip_req_body<IDIPCmdDimissGroupReq>
{
public:
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_dismiss_group::Serve()
{
	PB::npt_group_op cmd;
	cmd.set_op(PB::npt_group_op::GROUP_DISMISS);
	cmd.set_group_id(req.GroupId);
	GROUP::GroupManager::GetInstance().SystemOperation(req.GroupId, cmd);

	onSucceeded();

	SLOG(FORMAT, "idip_command")
	.P("iAttribute", req.GroupId)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	return HANDLE_RET_FINISH;
}

//初始化账号
struct idip_aq_reset_data : public idip_req_body<IDIPCmdReqCommon>
{
public:
	idip_aq_reset_data() : idip_req_body() {}

	virtual int Serve() override;
	void GetRoleData(int64_t role_id);

	void SendResponse(int retcode);
	static void CallBack_RoleList(idip_aq_reset_data *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_aq_reset_data *p_req, GetUserArg *arg, GetUserRes *res);
};
void idip_aq_reset_data::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_reset_data::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
int idip_aq_reset_data::Serve()
{
	std::stringstream ss;
	ss << "openid=" << req.OpenId << ";" << "roleid=" << req.RoleId << ";";
	SLOG(FORMAT, "idip_aq_reset_data").P("serve", ss.str());

	Octets user_account = IDIP_ACCOUNT(req);
	ruid_t roleid = req.RoleId;
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_aq_reset_data::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		if (!user->IsUserRole(roleid))
		{
			onFailed(IDIP_ERR_INVALID_ROLE);
			return HANDLE_RET_FINISH;
		}
	}

	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		GetRoleData(roleid);
		return HANDLE_RET_PENDING;
	}
	else
	{
		MakeSecureIDIPProcess(roleid, SEC_IDIP_RESET);
		onSucceeded();
		return HANDLE_RET_FINISH;
	}
	return 0;
}
void idip_aq_reset_data::CallBack_GetUser(idip_aq_reset_data *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_aq_reset_data::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ACCOUNT);
		delete p_req;
	}
	else
	{
		bool roleid_valid = false;
		ruid_t roleid = p_req->req.RoleId;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->onFailed(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}
void idip_aq_reset_data::CallBack_RoleList(idip_aq_reset_data *p_req, const std::vector<int64_t>& info_list)
{
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ROLE);
	}
	else
	{
		MakeSecureIDIPProcess(roleid, SEC_IDIP_RESET);
		p_req->onSucceeded();
	}
	delete p_req;
}
//发送消息
struct idip_aq_send_msg : public idip_req_body<IDIPCmdReqAQSendMessage>
{
	idip_aq_send_msg() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleData(int64_t role_id);

	static void CallBack_RoleList(idip_aq_send_msg *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_aq_send_msg *p_req, GetUserArg *arg, GetUserRes *res);
};
void idip_aq_send_msg::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_send_msg::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
int idip_aq_send_msg::Serve()
{
	std::stringstream ss;
	ss << "openid=" << req.OpenId << ";" << "roleid=" << req.RoleId << ";";
	GLog::formatlog("idip_aq_send_msg::serve", "%s", ss.str().c_str());

	Octets user_account = IDIP_ACCOUNT(req);
	ruid_t roleid = req.RoleId;
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_aq_send_msg::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		if (!user->IsUserRole(roleid))
		{
			onFailed(IDIP_ERR_INVALID_ROLE);
			return HANDLE_RET_FINISH;
		}
	}

	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		GetRoleData(roleid);
		return HANDLE_RET_PENDING;
	}
	else
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(req.MsgContent);
#else
		std::string content = req.MsgContent;
#endif
		MakeSecureIDIPProcess(roleid, SEC_IDIP_MESSAGE, 0, 0, Octets(content.data(), content.size()));
		onSucceeded();
		return HANDLE_RET_FINISH;
	}
}
void idip_aq_send_msg::CallBack_RoleList(idip_aq_send_msg *p_req, const std::vector<int64_t>& info_list)
{
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ROLE);
	}
	else
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(p_req->req.MsgContent);
#else
		std::string content = p_req->req.MsgContent;
#endif
		MakeSecureIDIPProcess(roleid, SEC_IDIP_MESSAGE, 0, 0, Octets(content.data(), content.size()));
		p_req->onSucceeded();
	}
	delete p_req;
}
void idip_aq_send_msg::CallBack_GetUser(idip_aq_send_msg *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_aq_send_msg::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ACCOUNT);
		delete p_req;
	}
	else
	{
		ruid_t roleid = p_req->req.RoleId;
		bool roleid_valid = false;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->onFailed(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}
//零收益
struct idip_aq_zero_benefit : public idip_req_body<IDIPCmdReqForbid>
{
public:
	idip_aq_zero_benefit() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleData(int64_t role_id);

	void SendResponse(int retcode);
	static void CallBack_RoleList(idip_aq_zero_benefit *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_aq_zero_benefit *p_req, GetUserArg *arg, GetUserRes *res);
};
void idip_aq_zero_benefit::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_zero_benefit::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
int idip_aq_zero_benefit::Serve()
{
	std::stringstream ss;
	ss << "openid=" << req.OpenId << ";" << "roleid=" << req.RoleId << ";";
	GLog::formatlog("idip_aq_zero_benefit::serve", "%s", ss.str().c_str());

	Octets user_account = IDIP_ACCOUNT(req);
	ruid_t roleid = req.RoleId;
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_aq_zero_benefit::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		if (!user->IsUserRole(roleid))
		{
			onFailed(IDIP_ERR_INVALID_ROLE);
			return HANDLE_RET_FINISH;
		}
	}

	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		GetRoleData(roleid);
		return HANDLE_RET_PENDING;
	}
	else
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(req.BanReason);
#else
		std::string content = req.BanReason;
#endif
		MakeSecureIDIPProcess(roleid, SEC_IDIP_ZERO_BENEFIT, req.BanTime, 0, Octets(content.data(), content.size()));
		onSucceeded();
		return HANDLE_RET_FINISH;
	}
	return HANDLE_RET_FINISH;
}
void idip_aq_zero_benefit::CallBack_GetUser(idip_aq_zero_benefit *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_aq_zero_benefit::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ACCOUNT);
		delete p_req;
	}
	else
	{
		ruid_t roleid = p_req->req.RoleId;
		bool roleid_valid = false;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->onFailed(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}
void idip_aq_zero_benefit::CallBack_RoleList(idip_aq_zero_benefit *p_req, const std::vector<int64_t>& info_list)
{
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ROLE);
	}
	else
	{
#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(p_req->req.BanReason);
#else
		std::string content = p_req->req.BanReason;
#endif
		MakeSecureIDIPProcess(roleid, SEC_IDIP_ZERO_BENEFIT, p_req->req.BanTime, 0, Octets(content.data(), content.size()));
		p_req->onSucceeded();
	}
	delete p_req;
}
//禁止指定玩法
struct idip_aq_ban_game : public idip_req_body<IDIPCmdReqAQBanGame>
{
public:
	idip_aq_ban_game() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleData(int64_t role_id);

	virtual void onSucceeded() override;
	static void CallBack_RoleList(idip_aq_ban_game *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_aq_ban_game *p_req, GetUserArg *arg, GetUserRes *res);
};
void idip_aq_ban_game::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_ban_game::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
int idip_aq_ban_game::Serve()
{
	std::stringstream ss;
	ss << "openid=" << req.OpenId << ";" << "roleid=" << req.RoleId << ";";
	GLog::formatlog("idip_aq_ban_game::serve", "%s", ss.str().c_str());

	Octets user_account = IDIP_ACCOUNT(req);
	ruid_t roleid = req.RoleId;
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_aq_ban_game::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		if (!user->IsUserRole(roleid))
		{
			onFailed(IDIP_ERR_INVALID_ROLE);
			return HANDLE_RET_FINISH;
		}
	}

	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		GetRoleData(roleid);
		return HANDLE_RET_PENDING;
	}
	else
	{
		onSucceeded();
		return HANDLE_RET_FINISH;
	}
	return HANDLE_RET_FINISH;
}
void idip_aq_ban_game::onSucceeded()
{
	ruid_t roleid = req.RoleId;
#ifdef USE_IDIP_PROXY
	std::string content = HttpProtocol::UrlDecode(req.Tip);
#else
	std::string content = req.Tip;
#endif
	MakeSecureIDIPProcess(roleid, SEC_IDIP_BAN_GAME, req.Type, req.Time, Octets(content.data(), content.size()));
	IDIPSend(0, "success");
}
void idip_aq_ban_game::CallBack_RoleList(idip_aq_ban_game *p_req, const std::vector<int64_t>& info_list)
{
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ROLE);
	}
	else
	{
		p_req->onSucceeded();
	}
	delete p_req;
}
void idip_aq_ban_game::CallBack_GetUser(idip_aq_ban_game *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_aq_ban_game::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ACCOUNT);
		delete p_req;
	}
	else
	{
		ruid_t roleid = p_req->req.RoleId;
		bool roleid_valid = false;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->onFailed(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}
//禁止指定玩法
struct idip_aq_ban_game_all : public idip_aq_ban_game
{
	virtual void onSucceeded();
};
void idip_aq_ban_game_all::onSucceeded()
{
#ifdef USE_IDIP_PROXY
	std::string content = HttpProtocol::UrlDecode(req.Tip);
#else
	std::string content = req.Tip;
#endif
	MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_BAN_GAME_ALL, req.Time, 0, Octets(content.data(), content.size()));
	IDIPSend(0, "success");
}

// 禁言类型
enum AQ_TALK_TYPE
{
	ATT_COMMON		= 0,	// 普通禁言 / 普通禁言解禁
	ATT_SILENCE		= 1,	// 无感知禁言 / 无感知禁言解禁
};
// 禁言、无感知禁言
struct idip_aq_ban_talk : public idip_req_body<IDIPCmdReqForbid>
{
	AQ_TALK_TYPE att;

	explicit idip_aq_ban_talk(AQ_TALK_TYPE _att) : idip_req_body(), att(_att) {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return req.RoleId == 0;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void SendResponse(int retcode);
	void GetRoleData(int64_t role_id);

	static void CallBack_RoleList(idip_aq_ban_talk *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_ban_talk_user(idip_aq_ban_talk *p_req, ForbidUserArg *arg, DBForbidUserRes *res);
	static void CallBack_ban_talk_role(idip_aq_ban_talk *p_req, DBPutForbidArg *arg, DBPutForbidRes *res);
};
void idip_aq_ban_talk::SendResponse(int retcode)
{
	std::string ret_msg;
	if (retcode == 0 || retcode == 1)
	{
		ret_msg = "success";
	}
	else
	{
		ret_msg = "failed";
	}

	IDIPSend(retcode, ret_msg);
}
int idip_aq_ban_talk::Serve()
{
	std::stringstream ss;
	ss << "openid=" << req.OpenId << ";" << "roleid" << req.RoleId << ";";
	GLog::formatlog("idip_aq_ban_talk::serve", "%s", ss.str().c_str());

	Octets user_account = IDIP_ACCOUNT(req);
	Octets ucs2_des;
	if (att == ATT_COMMON)
	{
#ifdef USE_IDIP_PROXY
		std::string utf8_des_str = HttpProtocol::UrlDecode(req.BanReason);
#else
		std::string utf8_des_str = req.BanReason;
#endif
		Octets utf8_des(utf8_des_str.c_str(), utf8_des_str.size());
		CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);
	}

	ruid_t roleid = req.RoleId;
	if (roleid == 0) //禁止所有角色
	{
		ForbidUserArg arg;
		arg.operation = (att == ATT_COMMON ? GNET_FORBID_TALK : GNET_FORBID_TALK_SILENCE);
		arg.account = user_account;
		arg.time = req.BanTime;
		arg.reason = ucs2_des;
		DBForbidUser *rpc = (DBForbidUser *)Rpc::Call(RPC_DBFORBIDUSER, arg);

		rpc->call_back = std::bind(&idip_aq_ban_talk::CallBack_ban_talk_user, this,
		                           std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		RoleInfo *p_role = RoleMap::Instance().Find(roleid);
		if (p_role == NULL)
		{
			GetRoleData(roleid);
			return HANDLE_RET_PENDING;
		}
		else
		{
			if (p_role->account != user_account)
			{
				SendResponse(IDIP_ERR_INVALID_ROLE);
				return HANDLE_RET_FINISH;
			}
			else
			{
				DBPutForbidArg arg;
				arg.roleid = roleid;
				GRoleForbid forbid((att == ATT_COMMON ? GNET_FORBID_TALK : GNET_FORBID_TALK_SILENCE), req.BanTime, Timer::GetTime(), ucs2_des);
				arg.forbid = forbid;
				DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);
				rpc->call_back = std::bind(&idip_aq_ban_talk::CallBack_ban_talk_role, this,
				                           std::placeholders::_1, std::placeholders::_2);
				GameDBClient::GetInstance()->SendProtocol(rpc);

				//SendResponse(  0);
				return HANDLE_RET_PENDING;
			}
		}
	}
	return HANDLE_RET_FINISH;
}
void idip_aq_ban_talk::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_ban_talk::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_aq_ban_talk::CallBack_ban_talk_user(idip_aq_ban_talk *p_req, ForbidUserArg *arg, DBForbidUserRes *res)
{
	if (res->retcode == ERR_NOTFOUND)
	{
		res->retcode = IDIP_ERR_INVALID_ACCOUNT;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}

void idip_aq_ban_talk::CallBack_ban_talk_role(idip_aq_ban_talk *p_req, DBPutForbidArg *arg, DBPutForbidRes *res)
{
	if (res->retcode == ERR_NOTFOUND)
	{
		res->retcode = IDIP_ERR_INVALID_ROLE;
	}
	p_req->SendResponse(res->retcode);
	delete p_req;
}
void idip_aq_ban_talk::CallBack_RoleList(idip_aq_ban_talk *p_req, const std::vector<int64_t>& info_list)
{
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
		delete p_req;
	}
	else
	{
		Octets user_account = p_req->IDIP_ACCOUNT(p_req->req);
		if (p_role->account != user_account)
		{
			p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
		}
		else
		{
			Octets ucs2_des;
			if (p_req->att == ATT_COMMON)
			{
#ifdef USE_IDIP_PROXY
				std::string utf8_des_str = HttpProtocol::UrlDecode(p_req->req.BanReason);
#else
				std::string utf8_des_str = p_req->req.BanReasonp_req->req.BanReason;
#endif
				Octets utf8_des(utf8_des_str.c_str(), utf8_des_str.size());
				CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);
			}

			DBPutForbidArg arg;
			arg.roleid = roleid;
			GRoleForbid forbid((p_req->att == ATT_COMMON ? GNET_FORBID_TALK : GNET_FORBID_TALK_SILENCE), p_req->req.BanTime, Timer::GetTime(), ucs2_des);
			arg.forbid = forbid;
			DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);
			rpc->call_back = std::bind(&idip_aq_ban_talk::CallBack_ban_talk_role, p_req,
			                           std::placeholders::_1, std::placeholders::_2);
			GameDBClient::GetInstance()->SendProtocol(rpc);
		}
	}
}
// 解除禁言、解除无感知禁言
struct idip_aq_unban_talk : public idip_unforbid
{
	AQ_TALK_TYPE att;

	explicit idip_aq_unban_talk(AQ_TALK_TYPE _att) : att(_att) {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return req.RoleId == 0;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_aq_unban_talk::Serve()
{
	SLOG(FORMAT, "idip_unban_talk")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId);

	Octets user_account = IDIP_ACCOUNT(req);
	if (req.RoleId == 0)
	{
		ForbidUserArg arg;
		arg.operation = (att == ATT_COMMON ? GNET_FORBID_TALK : GNET_FORBID_TALK_SILENCE);
		arg.account = user_account;
		arg.time = 0;

		DBForbidUser *rpc = (DBForbidUser *)Rpc::Call(RPC_DBFORBIDUSER, arg);

		rpc->call_back = std::bind(&idip_unforbid::CallBack_unForbidUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		GRoleForbid forbid((att == ATT_COMMON ? GNET_FORBID_TALK : GNET_FORBID_TALK_SILENCE), 0, Timer::GetTime());
		DBPutForbidArg arg(req.RoleId, forbid);
		DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);

		rpc->call_back = std::bind(&idip_unforbid::CallBack_unForbidRole, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	return HANDLE_RET_FINISH;
}

//解除处罚
struct idip_aq_unban_game : public idip_req_body<IDIPCmdReqUnban>
{
public:
	idip_aq_unban_game() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleData(int64_t role_id);

	virtual void onSucceeded() override;
	static void CallBack_RoleList(idip_aq_unban_game *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_aq_unban_game *p_req, GetUserArg *arg, GetUserRes *res);
};
void idip_aq_unban_game::GetRoleData(int64_t role_id)
{
	std::vector<int64_t> role_list;
	role_list.push_back(role_id);

	GetRoleListHelper *helper = new GetRoleListHelper(role_list);
	helper->call_back = std::bind(&idip_aq_unban_game::CallBack_RoleList, this,   std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
int idip_aq_unban_game::Serve()
{
	std::stringstream ss;
	ss << "openid=" << req.OpenId << ";" << "roleid=" << req.RoleId << ";";
	GLog::formatlog("idip_aq_unban_game::serve", "%s", ss.str().c_str());

	Octets user_account = IDIP_ACCOUNT(req);
	ruid_t roleid = req.RoleId;
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		rpc->call_back = std::bind(&idip_aq_unban_game::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		if (!user->IsUserRole(roleid))
		{
			onFailed(IDIP_ERR_INVALID_ROLE);
			return HANDLE_RET_FINISH;
		}
	}

	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		GetRoleData(roleid);
		return HANDLE_RET_PENDING;
	}
	else
	{
		onSucceeded();
		return HANDLE_RET_FINISH;
	}
	return HANDLE_RET_FINISH;
}
void idip_aq_unban_game::CallBack_RoleList(idip_aq_unban_game *p_req, const std::vector<int64_t>& info_list)
{
	ruid_t roleid = p_req->req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(roleid);
	if (p_role == NULL)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ROLE);
	}
	else
	{
		p_req->onSucceeded();
	}
	delete p_req;
}
void idip_aq_unban_game::CallBack_GetUser(idip_aq_unban_game *p_req, GetUserArg *arg, GetUserRes *res)
{
	LOG_TRACE("idip_aq_unban_game::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->onFailed(IDIP_ERR_INVALID_ACCOUNT);
		delete p_req;
	}
	else
	{
		bool roleid_valid = false;
		ruid_t roleid = p_req->req.RoleId;
		for (size_t i = 0; i < res->value.rolelist.size(); ++i)
		{
			if (roleid == res->value.rolelist[i])
			{
				roleid_valid = true;
				break;
			}
		}
		if (!roleid_valid)
		{
			p_req->onFailed(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->GetRoleData(roleid);
		}
	}
}
void idip_aq_unban_game::onSucceeded()
{
	if (req.RelieveBanUser)//解除登录
	{
		GRoleForbid forbid(GNET_FORBID_LOGIN, 0, 0, Octets());
		DBPutForbidArg arg(req.RoleId, forbid);
		DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}
	if (req.RelieveBanChat)
	{
		GRoleForbid forbid(GNET_FORBID_TALK, 0, 0, Octets());
		DBPutForbidArg arg(req.RoleId, forbid);
		DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);
		GameDBClient::GetInstance()->SendProtocol(rpc);
	}

	Octets param3 = (req.RelieveRanking) ? Octets("1", 1) : Octets();
	MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_UNBAN_GAME, req.RelieveZeroProfit, req.RelieveAllPlay, param3);

	IDIPSend(0);
}
//跑马灯
struct idip_announce : public idip_req_body<IDIPCmdReqAnnounce>
{
	idip_announce() : idip_req_body() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_announce::Serve()
{
	std::stringstream ss;
	GLog::formatlog("idip_announce::serve", "%s", ss.str().c_str());

	int NoticeId = 0;
#ifdef USE_IDIP_PROXY
	std::string utf8_content = HttpProtocol::UrlDecode(req.NoticeContent);
#else
	std::string utf8_content = req.NoticeContent;
#endif
	int ret = AnnounceManager::GetInstance().AddBroadcastChatInfo(NoticeId, req.BeginTime, req.Interval, req.EndTime, utf8_content, req.ShowCount);
	if (ret != 0)
	{
		ret = IDIP_ERR_INVALID_ID;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = ret;

	IDIPDataPacket<IDIPCmdRspAnnounce> rsp;
	rsp.head = req_head;
	rsp.body.Result = ret;
	rsp.body.RetMsg = (ret == 0) ? "success" : "failed";
	rsp.body.NoticeId = NoticeId;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("IDIPHandler::IDIPSend. catch cmdid=%d", req_head.Cmdid);
		return HANDLE_RET_FINISH;
	}
	Send(result);

	return HANDLE_RET_FINISH;
}
//查询跑马灯
struct idip_query_announce : public idip_req_body<IDIPCmdReqQueryAnnounce>
{
	idip_query_announce() : idip_req_body() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_query_announce::Serve()
{
	static const int roll_num_per_page = 30;
	std::stringstream ss;
	GLog::formatlog("idip_query_announce::serve", "%s", ss.str().c_str());

	IDIPDataPacket<IDIPCmdRspQueryAnnounce> rsp;
	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = 0;
	rsp.head = req_head;

	auto const& chats = AnnounceManager::GetInstance().QueryBroadcastChatInfo();
	auto& roll_list = rsp.body.MarqueeNoticeList;
	int index = 0;
	for (auto const& it : chats)
	{
		auto const& info = it.second;
		if (info.end_time < req.BeginTime || req.EndTime < info.begin_time)
		{
			//过滤掉时间以外的
			continue;
		}
		++ index;
		if (req.PageNo <= 0 || index > req.PageNo * roll_num_per_page)
		{
			continue;
		}
		if (index <= (req.PageNo - 1) * roll_num_per_page)
		{
			continue;
		}

		IDIPCmdNoticeInfo roll;
		roll.NoticeId = it.first;
		roll.SendTime = info.send_time;
		roll.EffectTime = info.begin_time;
		roll.MsgEndTime = info.end_time;
		roll.Type = 1; // 0弹出, 1滚动
		Octets utf8_msg;
		CharsetConverter::conv_charset_u2t(info.msg.msg, utf8_msg);
		roll.NoticeContent = UrlEncode2(std::string((char *)utf8_msg.begin(), utf8_msg.size()));

		roll_list.push_back(roll);
	}
	rsp.body.PageNo = req.PageNo;
	rsp.body.MarqueeNoticeList_count = roll_list.size();
	rsp.body.TotalPageNo = (index + roll_num_per_page - 1) / roll_num_per_page;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_listannounce::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return HANDLE_RET_FINISH;
	}

	Send(result);
	return HANDLE_RET_FINISH;
}
//删除跑马灯
struct idip_delete_announce : public idip_req_body<IDIPCmdReqDeleteAnnounce>
{
	idip_delete_announce() : idip_req_body() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_delete_announce::Serve()
{
	std::stringstream ss;
	GLog::formatlog("idip_announce::serve", "%s", ss.str().c_str());

	int ret = AnnounceManager::GetInstance().DeleteBroadcastChatInfo(req.NoticeId);
	if (ret != 0)
	{
		ret = IDIP_ERR_INVALID_ID;
	}
	IDIPSend(ret);
	return HANDLE_RET_FINISH;
}

/*
//修改非绑定钻石
struct idip_modify_unbind_diamond : public idip_req_body
{
	int Value;		//修改数量，- 减, + 加
	unsigned int Source;	//邮件发送来源，流水号
	std::string Serial;	//序列号
public:
	idip_modify_unbind_diamond() : idip_req_body(), Value(0), Source(0), Serial()
	{}
	idip_modify_unbind_diamond(const idip_modify_unbind_diamond & rhs) : idip_req_body(rhs),
		Value(rhs.Value), Source(rhs.Source), Serial(rhs.Serial)
	{}
	idip_modify_unbind_diamond * Clone() { return new idip_modify_unbind_diamond(*this); }

	virtual bool Parse(const Json::Value & json_content);
	virtual int Serve(* );

	void SendResponse(*  int retcode);
	static void CallBack_GetUser(idip_modify_unbind_diamond * p_req, *  GetUserArg * arg, GetUserRes * res);
};
bool idip_modify_unbind_diamond::Parse(const Json::Value & json_content)
{
	if (!idip_req_body::Parse(json_content))
		return false;

	if (!json_content.isMember("body"))
		return false;

	Json::Value json_value = json_content["body"];
	if (!json_parse_value(json_value, "Value", Value))
		return false;
	if (!json_parse_value(json_value, "Source", Source))
		return false;
	if (!json_parse_value(json_value, "Serial", Serial))
		return false;

	return true;
}
int idip_modify_unbind_diamond::Serve(* )
{
	std::stringstream ss;
	ss << "openid=" << OpenId << ";" << "roleid=" << RoleId << ";" << "Value" << Value << ";";
	GLog::formatlog("idip_modify_unbind_diamond::serve", "%s", ss.str().c_str());

	Octets user_account = IDIP_ACCOUNT(req);
	UserInfo* user = UserContainer::Instance().FindUser(user_account);

	if (user == NULL)
	{
		GetUser * rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));

		idip_modify_unbind_diamond * clone_req = Clone();
		rpc->call_back = std::bind(&idip_modify_unbind_diamond::CallBack_GetUser, clone_req,
				std::placeholders::_1, std::placeholders::_2);

		GameDBClient::GetInstance()->SendProtocol(rpc);
		return 0;
	}
	else
	{
		SendResponse(  0);
		return 0;
	}
	return 0;
}
void idip_modify_unbind_diamond::CallBack_GetUser(idip_modify_unbind_diamond * p_req, *  GetUserArg * arg, GetUserRes * res)
{
	LOG_TRACE("idip_modify_unbind_diamond::OnGetUser.account=%.*s. ret=%d", (int)arg->account.size(), (char *)arg->account.begin(), res->retcode);
	if (res->retcode != 0)
	{
		p_req->SendResponse(  1);
	}
	else
	{
		p_req->SendResponse(  0);
	}
	delete p_req;
}
void idip_modify_unbind_diamond::SendResponse(*  int retcode)
{
	try
	{
		if (retcode == 0)
		{
			Octets user_account = IDIP_ACCOUNT(req);
			Octets midas_sn = CreateUUID(user_account);

			int midas_type = MIDAS_DEC_CASH_TYPE_IDIP_UNBIND;

			if (Value < 0)
			{
				MidasRepair::SavePrePayFail(RoleId, user_account, midas_type, -1 * Value, midas_sn);
			}
			else
			{
				int bind = 0;
				MidasRepair::SavePresentFail(RoleId, user_account, Value, bind, midas_sn);
			}
		}
		Json::Value root;
		req_head.Cmdid = IDIP_DO_MOD_UNBIND_DIAMOND_RSP;
		req_head.WriteResponse(root);

		Json::Value body_value;
		json_set_value(body_value, "Result", (int)retcode);
		json_set_value(body_value, "RetMsg", "success");

		json_set_value(root, "body", body_value);

		Json::FastWriter writer;
		std::string result = writer.write(root);

		Send(result);
	}
	catch(...)
	{
		LOG_TRACE("idip_modify_unbind_diamond::SendResponse. catch");
	}
	if(retcode==0)
	{
		std::stringstream s1;
		s1 << "IDIPFLOW"
		<< "|" << TLOG::FT(Timer::GetTime()).c_str()
		<< "|" << AreaId
		<< "|" << OpenId
		<< "|" << 0
		<< "|" << Value
		<< "|" << Serial
		<< "|" << Source
		<< "|" << req_head.Cmdid
		;
		TLOG::log("%.*s", (int)s1.str().size(), s1.str().c_str());
		GLog::formatlog("","%s", s1.str().c_str());
	}
}*/
//////////////////////////////////////////////////////////////////////////////////////////////////////////
struct idip_open_activity : public idip_req_body<IDIPCmdReqOpenActivity>
{
	idip_open_activity() : idip_req_body() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_open_activity::Serve()
{
	CampaignManager::GetInstance().GMOpenCampaign(req.ActivityId, req.BeginTime, req.EndTime - req.BeginTime);
	onSucceeded();
	SLOG(FORMAT, "idip_open_activity")
	.P("activityid", req.ActivityId)
	.P("begintime", req.BeginTime)
	.P("endtime", req.EndTime);
	SLOG(FORMAT, "idip_command")
	.P("iAttribute", req.ActivityId)
	.P("iValue", req.BeginTime)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);
	return HANDLE_RET_FINISH;
}

//按roleid查询角色
struct idip_query_role_by_roleid : public idip_req_body<IDIPCmdReqCommon>
{
public:
	idip_query_role_by_roleid() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_query_role_by_roleid *p_req, const std::vector<int64_t>& info_list);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};
int idip_query_role_by_roleid::Serve()
{
	SLOG(FORMAT, "idip_query_role_by_roleid").P("role_id", req.RoleId);
	std::vector<int64_t> role_list;
	role_list.push_back(req.RoleId);
	RoleInfo *pinfo = RoleMap::Instance().Find(req.RoleId);
	if (pinfo)
	{
		SendResponse(0, role_list);
		return HANDLE_RET_FINISH;
	}
	else
	{
		GetRoleList(role_list);
		return HANDLE_RET_PENDING;
	}
}
void idip_query_role_by_roleid::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	IDIPDataPacket<IDIPCmdRspQueryRoleInfo> rsp;
	int count = 0;
	for (size_t i = 0; i < info_list.size(); i++)
	{
		int64_t roleid = info_list[i];
		RoleInfo *p_role = RoleMap::Instance().Find(roleid);
		if (!p_role)
		{
			continue;
		}
		PB::idip_role_info idip_value;
		idip_value.CopyFrom(p_role->show_property.data().idip_info());

		auto& role_value = rsp.body;
		role_value.RoleId = roleid;
#ifdef USE_UTF8
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
		role_value.Diamond = idip_value.cash_bind();
		role_value.GoldCoin = idip_value.money();
		role_value.Fight = p_role->fightingcapacity;
		role_value.Level = p_role->level;
		role_value.BodyType = p_role->body_size;
		role_value.Job = p_role->profession;
		role_value.Sex = p_role->gender;
		role_value.RegisterTime = p_role->create_time;
		role_value.TotalOnlineTime = idip_value.online_time();
		role_value.LastLogoutTime = p_role->logout_time;
		role_value.IsOnline = p_role->IsOnline() ? 0 : 1;
		role_value.OpenId = UrlEncode2(GetOpenid(std::string((char *)p_role->account.begin(), p_role->account.size())));
		role_value.PlatId = GetAccountPlat(p_role->account);
		++ count;
		break;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = (count > 0) ? 0 : IDIP_ERR_INVALID_ROLE;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_role_by_roleid::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}
	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}
void idip_query_role_by_roleid::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_query_role_by_roleid::CallBack_RoleList, this,   std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_query_role_by_roleid::CallBack_RoleList(idip_query_role_by_roleid *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}

//根据名字查询角色
struct idip_query_role_by_name : public idip_req_body<IDIPCmdReqQueryRoleByName>
{
	std::vector<int64_t> role_list;
public:
	idip_query_role_by_name() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	static void CallBack_RoleId(idip_query_role_by_name *p_req, int retcode, int64_t roleid, RoleInfo *p_role);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};
int idip_query_role_by_name::Serve()
{
	SLOG(FORMAT, "idip_query_role_by_name")
	.P("name", req.RoleName);

	GNET::Octets role_name(req.RoleName.c_str(), req.RoleName.size());
	GNET::Octets ucs2_name;
	CharsetConverter::conv_charset_t2u(role_name, ucs2_name);

	int64_t roleid = UserContainer::Instance().FindRoleId(ucs2_name);

	if (roleid)
	{
		RoleInfo *p_role = RoleMap::Instance().Find(roleid);
		if (p_role)
		{
			role_list.push_back(roleid);
			SendResponse(0, role_list);
			return HANDLE_RET_FINISH;
		}
	}

	getroleinfo_handle callback = std::bind(&idip_query_role_by_name::CallBack_RoleId, this,   std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
	RoleMap::GetRoleInfo(roleid, ucs2_name, &callback);

	return HANDLE_RET_PENDING;
}
void idip_query_role_by_name::CallBack_RoleId(idip_query_role_by_name *p_req, int retcode, int64_t roleid, RoleInfo *p_role)
{
	if (retcode == 0)
	{
		p_req->role_list.push_back(roleid);
		if (p_role)
		{
			p_req->SendResponse(0, p_req->role_list);
		}
		else
		{
			p_req->SendResponse(IDIP_ERR_INVALID_ROLE, p_req->role_list);;

		}
	}
	else
	{
		p_req->SendResponse(-3, std::vector<int64_t>());
	}
	delete p_req;
}
void idip_query_role_by_name::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	IDIPDataPacket<IDIPCmdRspQueryRoleInfo> rsp;
	int count = 0;
	for (size_t i = 0; i < info_list.size(); i++)
	{
		int64_t roleid = info_list[i];
		RoleInfo *p_role = RoleMap::Instance().Find(roleid);
		if (!p_role)
		{
			continue;
		}
		PB::idip_role_info idip_value;
		idip_value.CopyFrom(p_role->show_property.data().idip_info());

		auto& role_value = rsp.body;
		role_value.RoleId = roleid;
#ifdef USE_UTF8
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
		role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
		role_value.Diamond = idip_value.cash_bind();
		role_value.GoldCoin = idip_value.money();
		role_value.Fight = p_role->fightingcapacity;
		role_value.Level = p_role->level;
		role_value.BodyType = p_role->body_size;
		role_value.Job = p_role->profession;
		role_value.Sex = p_role->gender;
		role_value.RegisterTime = p_role->create_time;
		role_value.TotalOnlineTime = idip_value.online_time();
		role_value.LastLogoutTime = p_role->logout_time;
		role_value.IsOnline = p_role->IsOnline() ? 0 : 1;
		role_value.OpenId = UrlEncode2(GetOpenid(std::string((char *)p_role->account.begin(), p_role->account.size())));
		role_value.PlatId = GetAccountPlat(p_role->account);

		++ count;
		break;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = (count > 0) ? 0 : IDIP_ERR_INVALID_ROLE;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_role_by_roleid::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vDesc", req.RoleName)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//idip更改玩家数据
//如果是全服玩家，则发全服邮件，在玩家收全服邮件时候再存储再玩家身上
//如果是部分玩家，玩家在线则直接发给gs处理，玩家不在线则存储在玩家身上
enum IDIP_CHANGE_DATA_OP_TYPE
{
	IDIP_CHANGE_DATA_OP_TYPE_TO_PARTIAL = 1,	// 部分玩家
	IDIP_CHANGE_DATA_OP_TYPE_TO_ALL		= 2,	// 全服玩家
};

struct idip_change_data : public idip_req_body<IDIPCmdReqChangeData>
{
	std::string _tlog_info;
	int _type;
	idip_change_data(int type) : idip_req_body(), _type(type) {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return _type == IDIP_CHANGE_DATA_OP_TYPE_TO_ALL;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	static void CallBackChangeDataTypeToPartial(idip_change_data *p_request, int retcode);
	static void CallBackChangeDataTypeToAll(idip_change_data *p_request, GMailToAllData *arg, MailToAllRet *res);
};

int idip_change_data::Serve()
{
	std::stringstream ss;
	ss << "[";
	ss << req.Id << "," << req.Num << ",";
	//for (int64_t roleid : req.RoleIdList)
	{
		ss << req.RoleId;
	}
	ss << "]";
	_tlog_info = ss.str();
	class ChangeDataOpTypeToPartial : public DBSecureIDIP::callback
	{
	private:
		std::unordered_set<int64_t> role_id_list;
		int c_type;
		int64_t c_para1;
		int64_t c_para2;
	public:
		std::function<void (int)> call_back;

		explicit ChangeDataOpTypeToPartial(int data_type, int64_t id, int64_t num, const std::vector<int64_t>& role_id_vec)
		{
			c_type = data_type;
			c_para1 = id;
			c_para2 = num;

			std::string log;
			for (auto it = role_id_vec.begin(); it != role_id_vec.end(); ++it)
			{
				if (*it > 0)
				{
					role_id_list.insert(*it);
					log.append(",").append(std::to_string(*it));
				}
			}
			SLOG(FORMAT, "ChangeDataOpTypeToPartial::ChangeDataOpTypeToPartial").P("data_type", c_type).P("id", c_para1).P("num", c_para2).P("role_id_list", log);
		}
		virtual ~ChangeDataOpTypeToPartial() {}
		void ChangeDataOp()
		{
			auto it = role_id_list.begin();
			if (it == role_id_list.end())
			{
				Finish(0);
				return;
			}
			ruid_t role_id = *it;
			role_id_list.erase(it);

			MakeSecureIDIPProcess(role_id, c_type, c_para1, c_para2, Octets(), this);
		}
		virtual void OnCallBack(const DBSecureIDIPArg& arg, const DBSecureIDIPRes& res)
		{
			if (res.retcode == 0)
			{
				SLOG(FORMAT, "ChangeDataOpTypeToPartial::OnCallBack").P("role_id", arg.roleid).P("data_type", c_type).P("id", c_para1).P("num", c_para2);
			}
			else
			{
				SLOG(ERR, "ChangeDataOpTypeToPartial::OnCallBack err").P("role_id", arg.roleid).P("data_type", c_type).P("id", c_para1).P("num", c_para2).P("ret", res.retcode);
			}
			ChangeDataOp();
		}
		virtual void OnTimeout(const DBSecureIDIPArg& arg)
		{
			SLOG(ERR, "ChangeDataOpTypeToPartial::OnTimeout").P("role_id", arg.roleid).P("data_type", c_type).P("id", c_para1).P("num", c_para2);
			ChangeDataOp();
		}
		void Finish(int finish_result)
		{
			call_back(finish_result);
			delete this;
		}
	};
	class ChangeDataOpTypeToAll
	{
	private:
		int64_t serial;
		Octets mail_context;

	public:
		explicit ChangeDataOpTypeToAll(int data_type, int64_t id, int64_t num, int64_t mailid)
		{
			PB::db_secure_idip_command proto;
			proto.set_c_type(data_type);
			proto.set_c_para1(id);
			proto.set_c_para2(num);
			mail_context = PB2Octets(proto);
			serial = mailid;//strtol(serial_str.c_str(), nullptr, 10);
			SLOG(FORMAT, "ChangeDataOpTypeToAll::ChangeDataOpTypeToAll").P("data_type", data_type).P("id", id).P("num", num).P("serial", serial);
		}
		void MakeMail(Mail& mail)
		{
			mail.header.category = MAIL_CATEGORY_IDIP_CHANGE_DATA;
			mail.header.msgid = FORMAT_MAIL_IDIP_CHANGE_DATA;
			mail.header.date = Timer::GetTime();
			mail.context = mail_context;
		}
		void ChangeDataOp(idip_change_data *iu)
		{
			GMailToAllData arg;
			time_t now_time = Timer::GetTime();
			arg.expiry_time = 0; //改全服玩家数据的邮件不会过期
			arg.end_create_time = now_time;
			arg.serialid = serial;
			MakeMail(arg.mail);

			MakeMailPBInfo(arg.pbinfo, MA_USER_OLD);  //更改全服玩家数据 只对邮件发送前注册的玩家有效

			PutMailDataRpc *rpc = (PutMailDataRpc *) Rpc::Call(RPC_PUTMAILDATARPC, arg);
			rpc->call_back = std::bind(&idip_change_data::CallBackChangeDataTypeToAll, iu,   std::placeholders::_1, std::placeholders::_2);
			GameDBClient::GetInstance()->SendProtocol(rpc);
		}
	};

	if (req.DataType != SEC_IDIP_UNFREEZE
	        && req.DataType != SEC_IDIP_COMPLETE_TOWNLET_CHAT_EVENT
	        && req.DataType != SEC_IDIP_CHANGE_REPU
	        && req.DataType != SEC_IDIP_DEL_TASK
	        && req.DataType != SEC_IDIP_DEL_ITEM
	        && req.DataType != SEC_IDIP_DISCARD_FREEZE
	        && req.DataType != SEC_IDIP_DEL_LONGYU
	        && req.DataType != SEC_IDIP_ADD_LONGYU
	        && req.DataType != SEC_IDIP_CHG_LONGYU
	        && req.DataType != SEC_IDIP_ADD_EQUIP
	        && req.DataType != SEC_IDIP_ADD_FURNITURE
	        && req.DataType != SEC_IDIP_DEL_FURNITURE
	        && req.DataType != SEC_IDIP_UNLEARN_KOTODAMA
	        && req.DataType != SEC_IDIP_EXCHANGE_EQUIP_SUIT
	        && req.DataType != SEC_IDIP_DEL_TITLE
	        && req.DataType != SEC_IDIP_ADD_TASK
	        && req.DataType != SEC_IDIP_DEL_LONGHUN
	        && req.DataType != SEC_IDIP_ADD_LONGHUN
	        && req.DataType != SEC_IDIP_CHG_LONGHUN
	        && req.DataType != SEC_IDIP_FINISH_PLAYER_GUIDE
	   )
	{
		SLOG(ERR, "idip_change_data::Serve").P("DataType", req.DataType);
		onFailed(IDIP_ERR_PARAM);
		return HANDLE_RET_FINISH;
	}

	if (_type == IDIP_CHANGE_DATA_OP_TYPE_TO_PARTIAL)
	{
		std::vector<int64_t> role_list;
		role_list.push_back(req.RoleId);
		ChangeDataOpTypeToPartial *op = new ChangeDataOpTypeToPartial(req.DataType, req.Id, req.Num, role_list);
		op->call_back = std::bind(&idip_change_data::CallBackChangeDataTypeToPartial, this,   std::placeholders::_1);
		op->ChangeDataOp();
	}
	else if (_type == IDIP_CHANGE_DATA_OP_TYPE_TO_ALL)
	{
		ChangeDataOpTypeToAll op(req.DataType, req.Id, req.Num, 0);
		op.ChangeDataOp(this);
	}
	else
	{
		SLOG(ERR, "idip_change_data::Serve").P("OpType", _type);
		onFailed(IDIP_ERR_PARAM);
		return HANDLE_RET_FINISH;
	}

	return HANDLE_RET_PENDING;
}
void idip_change_data::CallBackChangeDataTypeToPartial(idip_change_data *p_request, int retcode)
{
	std::string ret_msg;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_DB_NOTFOUND;
	}
	p_request->IDIPSend(retcode, ret_msg);
	if (retcode == 0)
	{
		SLOG(FORMAT, "idip_command")
		.P("iAttribute", p_request->_type)
		.P("iValue", p_request->req.DataType)
		.P("vDesc", p_request->_tlog_info)
		.P("vSerial", p_request->req.Serial)
		.P("iCmdID", p_request->req_head.Cmdid);
	}
	delete p_request;
}
void idip_change_data::CallBackChangeDataTypeToAll(idip_change_data *p_request, GMailToAllData *arg, MailToAllRet *res)
{
	std::string ret_msg;
	int retcode = res->retcode;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_DB_NOTFOUND;
	}
	p_request->IDIPSend(retcode, ret_msg);
	if (res->retcode == 0)
	{
		SLOG(FORMAT, "idip_command")
		.P("iAttribute", p_request->_type)
		.P("iValue", p_request->req.DataType)
		.P("vDesc", p_request->_tlog_info)
		.P("vSerial", p_request->req.Serial)
		.P("iCmdID", p_request->req_head.Cmdid);
	}
	delete p_request;
}
// 开关白名单
// // IDIP_DO_OPEN_CLOSE_WHITE_LIST_REQ
// // IDIP_DO_OPEN_CLOSE_WHITE_LIST_RSP
struct idip_switch_white_list : public idip_req_body<IDIPCmdReqSwitchWhiteList>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_switch_white_list::Serve()
{
	SLOG(FORMAT, "idip_switch_white_list::Serve").P("optype", req.OperType);
	switch (req.OperType)
	{
	case 0: //全部关闭
		ip_whitelist_enabled = false;
		GDeliveryServer::GetInstance()->BroadcastToCS(SetupConnection(LSCT_WHITE, 0, false));
		GDeliveryServer::GetInstance()->SetEnableAccountWhiteList(false);
		GDeliveryServer::GetInstance()->SetEnableStaticWhiteList(false);
		break;
	case 1: //全部开启
		ip_whitelist_enabled = true;
		GDeliveryServer::GetInstance()->BroadcastToCS(SetupConnection(LSCT_WHITE, 0, true));
		GDeliveryServer::GetInstance()->SetEnableAccountWhiteList(true);
		GDeliveryServer::GetInstance()->SetEnableStaticWhiteList(true);
		break;
	case 2: //账号白名单开启
		GDeliveryServer::GetInstance()->SetEnableAccountWhiteList(true);
		break;
	case 3: //IP白名单开启
		ip_whitelist_enabled = true;
		GDeliveryServer::GetInstance()->BroadcastToCS(SetupConnection(LSCT_WHITE, 0, true));
		break;
	case 4: //账号白名单关闭
		GDeliveryServer::GetInstance()->SetEnableAccountWhiteList(false);
		break;
	case 5: //IP白名单关闭
		ip_whitelist_enabled = false;
		GDeliveryServer::GetInstance()->BroadcastToCS(SetupConnection(LSCT_WHITE, 0, false));
		break;
	case 6: //静态白名单开启
		GDeliveryServer::GetInstance()->SetEnableStaticWhiteList(true);
		break;
	case 7: //静态白名单关闭
		GDeliveryServer::GetInstance()->SetEnableStaticWhiteList(false);
		break;
	default:
		onFailed(IDIP_ERR_PARAM);
		return HANDLE_RET_FINISH;
	}

	onSucceeded();
	return HANDLE_RET_FINISH;
}
//激活账号
struct idip_activate_user : public idip_req_body<IDIPCmdReqActivateAccount>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_activate_user::Serve()
{
	SLOG(FORMAT, "idip_active_user").P("openid", req.OpenId);

	Octets account = Openid2Account(Octets(req.OpenId.c_str(), req.OpenId.size()), req.AreaId);
	UserInfo *user = UserContainer::Instance().FindUser(account);
	if (user)
	{
		user->SetActived(true);
	}
	DBActiveUser *rpc = (DBActiveUser *)Rpc::Call(RPC_DBACTIVEUSER, DBActiveUserArg(account));
	if (!GameDBClient::GetInstance()->SendProtocol(rpc))
	{
		SLOG(ERR, "DS::idip_activate_user ActiveUser err").P("account", account);
	}
	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	onSucceeded();
	return HANDLE_RET_FINISH;
}
//查询角色声望
struct idip_query_role_reputation : public idip_req_body<IDIPCmdReqQueryRoleReputation>
{
public:
	idip_query_role_reputation() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_query_role_reputation *p_req, const std::vector<int64_t>& info_list);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};
int idip_query_role_reputation::Serve()
{
	SLOG(FORMAT, "idip_forbid")
	.P("openid", req.OpenId)
	.P("roleid", req.RoleId)
	.P("repuid", req.RepuIndex);

	std::vector<int64_t> role_list;
	role_list.push_back(req.RoleId);
	RoleInfo *pinfo = RoleMap::Instance().Find(req.RoleId);
	if (pinfo)
	{
		SendResponse(0, role_list);
		return HANDLE_RET_FINISH;
	}
	else
	{
		GetRoleList(role_list);
		return HANDLE_RET_PENDING;
	}
}
void idip_query_role_reputation::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	IDIPDataPacket<IDIPCmdRspQueryRoleReputation> rsp;

	auto& body = rsp.body;
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (!p_role)
	{
		req_head.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		// 今天未登录的角色，活跃度为0
		if (req.RepuIndex == REPUID_DAY_ACTIVITY
		        && p_role->login_time < p_role->logout_time
		        && p_role->logout_time < GetLocalDayBegin())
		{
			body.RepuValue = 0;
		}
		else
		{
			body.RepuValue = p_role->GetReputation(req.RepuIndex);
		}
		req_head.Result = 0;
	}
	req_head.Cmdid = req_head.Cmdid + 1;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(ERR, "idip_query_role_reputation::SendResponse err for Json_Data2Str").P("rold_id", req.RoleId);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iValue", req.RepuIndex)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}
void idip_query_role_reputation::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_query_role_reputation::CallBack_RoleList, this,   std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}
void idip_query_role_reputation::CallBack_RoleList(idip_query_role_reputation *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}
//查询玩家工会数据
struct idip_query_role_corp : public idip_query_role_by_roleid
{
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list) override;
};
void idip_query_role_corp::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	IDIPDataPacket<IDIPCmdRspQueryRoleCorp> rsp;

	auto& body = rsp.body;
	body.RoleId = req.RoleId;
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (!p_role)
	{
		req_head.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		auto pCorps = CorpsManager::GetInstance().GetCorp(p_role->GPSFacebook().corps().id());
		if (pCorps)
		{
			body.UnionId = pCorps->ID();
			auto& name = pCorps->Data().data().name();
			auto& announce = pCorps->Data().data().announce();
#ifdef USE_UTF8
			body.UnionName = UrlEncode2(name);
			body.UnionSign = UrlEncode2(announce);
#else
			Octets utf8_name;
			CharsetConverter::conv_charset_u2t(Octets(name.c_str(), name.size()), utf8_name);
			body.UnionName = UrlEncode2(std::string((char *)utf8_name.begin(), utf8_name.size()));
			Octets utf8_announce;
			CharsetConverter::conv_charset_u2t(Octets(announce.c_str(), announce.size()), utf8_announce);
			body.UnionSign = UrlEncode2(std::string((char *)utf8_announce.begin(), utf8_announce.size()));
#endif
			body.UnionLevel = pCorps->Data().data().level();
			body.UnionMemberNum = pCorps->MemberCount();
			body.UnionLeaderId =  pCorps->GetMaster().ID();

			/*auto& memberMap = pCorps->_member_map;
			auto it = memberMap.find(req.RoleId);
			if ( it != memberMap.end() && it->second )
			{
				PartyRole = it->second->_m_data.info().pos();
			}*/
		}
		req_head.Result = 0;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_role_corp::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}

//设置工会签名
struct idip_set_corps_msg : idip_req_body<IDIPCmdSetCorpsMsg>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_set_corps_msg::Serve()
{
	auto pCorps = CorpsManager::GetInstance().GetCorp(req.UnionId);
	if (!pCorps)
	{
		IDIPSend(1, "corps not found");
		return HANDLE_RET_FINISH;
	}
	auto *data = pCorps->Data().mutable_data();
#ifdef USE_IDIP_PROXY
	std::string content = HttpProtocol::UrlDecode(req.Content);
#else
	std::string content = req.Content;
#endif

	if (req.SetType == 1)
	{
		Octets ucs2_content;
		CharsetConverter::conv_charset_t2u(Octets(content.c_str(), content.size()), ucs2_content);
		ucs2_content.tostr(*data->mutable_announce());
	}
	else if (req.SetType == 2)
	{
		Octets ucs2_content;
		CharsetConverter::conv_charset_t2u(Octets(content.c_str(), content.size()), ucs2_content);
		ucs2_content.tostr(*data->mutable_recruitment());
	}
	else if (req.SetType == 3)
	{
		data->set_recruitment_url(content);
	}
	else
	{
		IDIPSend(1000, "invalid type");
		return HANDLE_RET_FINISH;
	}
	pCorps->SetDirty();

	SLOG(FORMAT, "idip_command")
	.P("iAttribute", req.UnionId)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	onSucceeded();
	return HANDLE_RET_FINISH;
}

//设置工会名字
struct idip_corps_rename : idip_req_body<IDIPCmdCorpsRename>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_corps_rename::Serve()
{
	auto pCorps = CorpsManager::GetInstance().GetCorp(req.UnionId);
	if (!pCorps)
	{
		IDIPSend(1);
		return HANDLE_RET_FINISH;
	}
#ifdef USE_IDIP_PROXY
	std::string content = HttpProtocol::UrlDecode(req.UnionRename);
#else
	std::string content = req.Content;
#endif

	Octets ucs2_content;
	CharsetConverter::conv_charset_t2u(Octets(content.c_str(), content.size()), ucs2_content);

	std::string rename((const char *)ucs2_content.begin(), ucs2_content.size());
	pCorps->IDIPRename(rename);


	SLOG(FORMAT, "idip_command")
	.P("iAttribute", req.UnionId)
	.P("vDesc", StripName(content.c_str(), content.size(), "."))
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	onSucceeded();
	return HANDLE_RET_FINISH;
}

//屏蔽玩家名字
struct idip_forbid_player_name: public idip_req_body<IDIPCmdForbidPlayerName>
{
	std::vector<int64_t> role_list;
public:
	idip_forbid_player_name() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

	static void CallBack_RoleId(idip_forbid_player_name *p_req, int retcode, int64_t roleid, RoleInfo *p_role);
};
int idip_forbid_player_name::Serve()
{
	SLOG(FORMAT, "idip_forbid_player_name")
	.P("roleid", req.RoleId);

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iValue", req.Type)
	.P("iDuration", req.Duration)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (p_role)
	{
		p_role->SetForbidName((bool)req.Type, req.Duration);
		onSucceeded();
		return HANDLE_RET_FINISH;
	}

	getroleinfo_handle callback = std::bind(&idip_forbid_player_name::CallBack_RoleId, this,   std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
	RoleMap::GetRoleInfo(req.RoleId, Octets(), &callback);

	return HANDLE_RET_PENDING;
}
void idip_forbid_player_name::CallBack_RoleId(idip_forbid_player_name *p_req, int retcode, int64_t roleid, RoleInfo *p_role)
{
	if (retcode == 0 && p_role)
	{
		p_role->SetForbidName((bool)p_req->req.Type, p_req->req.Duration);
		p_req->onSucceeded();
		delete p_req;
		return;
	}
	p_req->onFailed(-1);
	delete p_req;
}

//通知玩家扣点券并同时发送物品
struct idip_use_cash_and_send_item: public idip_req_body<IDIPCmdUseCashAndSendItem>
{
public:
	idip_use_cash_and_send_item() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_use_cash_and_send_item::Serve()
{
	SLOG(INFO, "idip_use_cash_and_send_item")
	.P("roleid", req.RoleId).P("UseCashAmount", req.UseCashAmount).P("MailBoxSlotLimit", req.MailBoxSlotLimit)
	.P("ItemListSize", req.ItemList.size());

	if (!GET_FUNC_SWITCH(kFuncCodeIdipUseCashSendItem))
	{
		onFailed(IDIP_ERR_INVALID);
		return HANDLE_RET_FINISH;
	}

	if (0 == req.UseCashAmount && 0 == req.MailBoxSlotLimit && req.ItemList.empty())
	{
		//什么参数都没填写，不允许
		onFailed(IDIP_ERR_PARAM);
		return HANDLE_RET_FINISH;
	}

	//要求玩家必须在线
	RoleInfo *p_role = RoleMap::Instance().FindOnline(req.RoleId);
	if (!p_role)
	{
		onFailed(IDIP_ERR_ROLE_OFFLINE);
		return HANDLE_RET_FINISH;
	}

	UseCashAndSendItemArg arg;
	arg.roleid = req.RoleId;
	arg.use_cash_amount = req.UseCashAmount;
	arg.mailbox_slot_limit = req.MailBoxSlotLimit;
	arg.money_type = EXPSHOPSELLMODE_MUSTBE_TRADEMONEY;

	std::stringstream contentSs;
	contentSs << req.MailBoxSlotLimit;
	if (!req.ItemList.empty())
	{
#ifdef USE_IDIP_PROXY
		std::string utf8_title = HttpProtocol::UrlDecode(req.MailTitle);
		std::string utf8_content = HttpProtocol::UrlDecode(req.MailContent);
#else
		std::string utf8_title = title;
		std::string utf8_content = content;
#endif
		contentSs << "#" << utf8_title << "#" << utf8_content;
		Octets ucs2_title, ucs2_content;
		CharsetConverter::conv_charset_t2u(Octets(utf8_title.c_str(), utf8_title.size()), ucs2_title);
		CharsetConverter::conv_charset_t2u(Octets(utf8_content.c_str(), utf8_content.size()), ucs2_content);

		Mail& mail = arg.mail;
		mail.header.category = CATEGORY_MAIL_SYSTEM | MAIL_CATEGORY_IDIP_USE_CASH_AND_SEND_ITEM;
		mail.header.subject = ucs2_title;
		mail.header.from = 0;
		mail.header.to = req.RoleId;
		mail.header.date = Timer::GetTime();
		mail.header.status = MAIL_STATUS_ATTACHED;
		mail.header.msgid = 0;
		mail.context = ucs2_content;

		for (size_t i = 0; i < req.ItemList.size(); ++ i)
		{
			GRoleInventory item;
			item.id = req.ItemList[i].ItemId;
			item.count = req.ItemList[i].ItemNum;
			mail.attachment.items.push_back(item);
			if (i == 0)
			{
				contentSs << "#";
			}
			else
			{
				contentSs << ",";
			}
			contentSs << "[" << item.id << "," << item.count << "]";
		}
	}

	UseCashAndSendItem *rpc = (UseCashAndSendItem *)Rpc::Call(RPC_USECASHANDSENDITEM, arg);
	rpc->callback = [this](int retcode)
	{
		if (0 == retcode)
		{
			this->onSucceeded();
		}
		else
		{
			this->onFailed(retcode);
		}
		delete this;
	};

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iValue", req.UseCashAmount)
	.P("vDesc", StripName(contentSs.str().c_str(), contentSs.str().size(), "."))
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid)
	.P("AreaID", GetAreaID(p_role->account))
	.P("PlatID", GetAccountPlat(p_role->account));

	if (!p_role->SendProtocol2GS(rpc))
	{
		onFailed(IDIP_ERR_INVALID_ID);
		return HANDLE_RET_FINISH;
	}

	return HANDLE_RET_PENDING;
}

// ************************************************************
// // 发言清除
struct idip_broadcast_remove_chat : public idip_query_role_by_roleid
{
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list) override;
};
void idip_broadcast_remove_chat::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	bool isValidRole = false;
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (p_role && p_role->account == IDIP_ACCOUNT(req))
	{
		isValidRole = true;
	}

	if (!isValidRole)
	{
		IDIPSend(IDIP_ERR_INVALID_ROLE, "role invalid");
		return;
	}

	PB::npt_secure_idip msg;
	msg.set_cmd(PB::npt_secure_idip::REMOVE_CHAT);
	msg.set_roleid(req.RoleId);
	GDeliveryServer::GetInstance()->BroadcastToLS(msg);

	onSucceeded();
};

//任务查询
struct idip_query_role_task : idip_req_body<IDIPCmdReqTask>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_query_role_task::Serve()
{
	RoleInfo *pInfo = RoleMap::Instance().FindOnline(req.RoleId);
	if (!pInfo)
	{
		onFailed(IDIP_ERR_ROLE_OFFLINE);
		return HANDLE_RET_FINISH;
	}

	PB::ipt_query_task_status msg;
	auto *idip = msg.mutable_idip();
	idip->set_cmdid(req_head.Cmdid);
	idip->set_seqid(req_head.Seqid);
	idip->set_servicename(req_head.ServiceName);
	idip->set_sendtime(req_head.SendTime);
	idip->set_version(req_head.Version);
	idip->set_authenticate(req_head.Authenticate);
#ifdef USE_IDIP_PROXY
	idip->set_web_sid(xid << 16 | proxyid);
#else
	idip->set_web_sid(web_sid);
#endif
	msg.set_task_id(req.TaskId);

	pInfo->SendMessage2GS(msg);
	return HANDLE_RET_FINISH;
}

enum IdipScoreType
{
	IST_1 = 1, //小黄鸭奖章
	IST_2 = 2, //助力积分
	IST_3 = 3, //竞技积分
	IST_4 = 4, //羁绊
	IST_5 = 5, //信息片段
	IST_6 = 6, //身份积分
	IST_7 = 7, //完美回忆
	IST_8 = 8, //社团贡献(非声望

	IST_MAX = IST_8,
};

const static int Score2RepuId[IST_MAX] = {42, 83, 71, 225, 53, 309, 290, 0};
//查询角色积分数据
struct idip_query_role_score : public idip_query_role_by_roleid
{
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list) override;
};
void idip_query_role_score::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	bool isValidRole = false;
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (p_role)
	{
		isValidRole = true;
	}

	if (!isValidRole)
	{
		IDIPSend(IDIP_ERR_INVALID_ROLE, "role invalid");
		return;
	}

	IDIPDataPacket<IDIPCmdRspQueryScoreInfo> rsp;
	auto& body = rsp.body;
#ifdef USE_UTF8
	body.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
	body.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
	body.Level = p_role->level;
	auto pCorps = CorpsManager::GetInstance().GetCorp(p_role->GPSFacebook().corps().id());
	if (pCorps)
	{
		body.CommunityId = pCorps->ID();
		auto& name = pCorps->Data().data().name();
#ifdef USE_UTF8
		body.CommunityName = UrlEncode2(name);
#else
		Octets utf8_name;
		CharsetConverter::conv_charset_u2t(Octets(name.c_str(), name.size()), utf8_name);
		body.CommunityName = UrlEncode2(std::string((char *)utf8_name.begin(), utf8_name.size()));
#endif
	}
	auto& idip_value = p_role->show_property.data().idip_info();
	body.GoldCoin = idip_value.money();
	body.Diamond = idip_value.cash_bind();
	body.Cash = 0;

	for (int i = 0; i < IST_MAX; ++i)
	{
		int score_id = i + 1;
		if (score_id == IST_8)
		{
			int value = 0;
			if (pCorps)
			{
				auto pMember = pCorps->GetMember(p_role->roleid);
				if (pMember)
				{
					value = pMember->Data().contri_cumulate();
				}
			}
			body.ScoreList.emplace_back(score_id, value);
			continue;
		}
		body.ScoreList.emplace_back(score_id, p_role->GetReputation(Score2RepuId[i]));
	}
	body.ScoreList_count = body.ScoreList.size();

	req_head.Result = 0;

	req_head.Cmdid = req_head.Cmdid + 1;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_role_score::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}

//设置角色积分数据
struct idip_modify_role_score : public idip_req_body <IDIPCmdReqSetScore>
{
	idip_modify_role_score() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_modify_role_score *p_req, const std::vector<int64_t>& info_list);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};

void idip_modify_role_score::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	if (info_list.empty())
	{
		return;
	}

	RoleInfo *p_role = RoleMap::Instance().Find(info_list[0]);
	if (!p_role)
	{
		IDIPSend(IDIP_ERR_INVALID_ROLE, "invalid role");
		return;
	}
	auto pCorps = CorpsManager::GetInstance().GetCorp(p_role->GPSFacebook().corps().id());
	if (pCorps)
	{
		auto pMember = pCorps->GetMember(p_role->roleid);
		if (pMember)
		{
			pMember->Data().set_contri_cumulate(req.SetValue);
			pMember->SetDirty();
			onSucceeded();
			return;
		}
	}
	IDIPSend(1, "invalid corps");
	return;
}

int idip_modify_role_score::Serve()
{
	int score_id = req.ScoreType;
	if (score_id < 1 || score_id > IST_MAX)
	{
		IDIPSend(1, "invalid param");
		return HANDLE_RET_FINISH;
	}
	if (score_id == IST_8)
	{
		RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
		if (p_role)
		{
			auto pCorps = CorpsManager::GetInstance().GetCorp(p_role->GPSFacebook().corps().id());
			if (pCorps)
			{
				auto pMember = pCorps->GetMember(p_role->roleid);
				if (pMember)
				{
					pMember->Data().set_contri_cumulate(req.SetValue);
					pMember->SetDirty();
					onSucceeded();
					return HANDLE_RET_FINISH;
				}
			}

			IDIPSend(1, "invalid corps");
			return HANDLE_RET_FINISH;
		}
		std::vector<int64_t> role_list;
		role_list.push_back(req.RoleId);
		GetRoleList(role_list);
		return HANDLE_RET_PENDING;
	}

	int repu_id = Score2RepuId[score_id - 1];
	MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_CHANGE_REPU, repu_id, req.SetValue);
	onSucceeded();
	return HANDLE_RET_FINISH;
}

void idip_modify_role_score::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_modify_role_score::CallBack_RoleList, this,   std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_modify_role_score::CallBack_RoleList(idip_modify_role_score *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}

// ************************************************************
// 根据roleid查询openid
struct idip_query_openid_by_roleid : public idip_query_role_by_roleid
{
	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list) override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
void idip_query_openid_by_roleid::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (!p_role)
	{
		IDIPSend(IDIP_ERR_INVALID_ROLE, "role invalid");
		return;
	}

	IDIPDataPacket<IDIPCmdRspQueryRoleBriefInfo> rsp;
	req_head.Cmdid = req_head.Cmdid + 1;
	rsp.head = req_head;

	auto& role_value = rsp.body;
	role_value.RoleId = req.RoleId;
#ifdef USE_UTF8
	role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
	role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
	role_value.OpenId = UrlEncode2(GetOpenid(std::string((char *)p_role->account.begin(), p_role->account.size())));
	role_value.PlatId = GetAccountPlat(p_role->account);

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_openid_by_roleid::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
};

// ************************************************************
// 根据rolename查询openid
struct idip_query_openid_by_name : public idip_query_role_by_name
{
	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list) override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
void idip_query_openid_by_name::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	RoleInfo *p_role = NULL;

	if (info_list.size() > 0)
	{
		p_role = RoleMap::Instance().Find(info_list[0]);
	}
	if (!p_role)
	{
		IDIPSend(IDIP_ERR_INVALID_ROLE, "role invalid");
		return;
	}

	IDIPDataPacket<IDIPCmdRspQueryRoleBriefInfo> rsp;
	req_head.Cmdid = req_head.Cmdid + 1;
	rsp.head = req_head;

	auto& role_value = rsp.body;
	role_value.RoleId = p_role->roleid;
#ifdef USE_UTF8
	role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetShowName().begin(), p_role->GetShowName().size()));
#else
	role_value.RoleName = UrlEncode2(std::string((char *)p_role->GetUtf8ShowName().begin(), p_role->GetUtf8ShowName().size()));
#endif
	role_value.OpenId = UrlEncode2(GetOpenid(std::string((char *)p_role->account.begin(), p_role->account.size())));
	role_value.PlatId = GetAccountPlat(p_role->account);

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_openid_by_name::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	Send(result);
};

struct idip_share_activity_invoke : idip_req_body<IDIPCmdReqShareActivityInvoke>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return 0;
	}
};
int idip_share_activity_invoke::Serve()
{
	req.IconUrl = HttpProtocol::UrlDecode(req.IconUrl);
	req.NickName = HttpProtocol::UrlDecode(req.NickName);
	LOG_TRACE("idip_share_activity_invoke::Serve activity_id=%ld:host=%s:guest=%s:type=%d:name=%s:icon=%s", req.ActivityId, req.OpenidHost.c_str(),
	          req.OpenidGuest.c_str(), req.Parameter1, req.NickName.c_str(), req.IconUrl.c_str());
	if (req.ActivityId == GST_SHARE_FRIEND || req.ActivityId == GST_SHARE_ALL)
	{
		Octets host((char *)req.OpenidHost.c_str(), req.OpenidHost.size());
		Octets quest((char *)req.OpenidGuest.c_str(), req.OpenidGuest.size());
		Octets name((char *)req.NickName.c_str(), req.NickName.size());
		Octets url((char *)req.IconUrl.c_str(), req.IconUrl.size());
		GrcManager::GetInstance().AddAccountShare(req.ActivityId, host, quest, req.Parameter1, name, url);
	}
	else if (req.ActivityId == GST_FRIEND_HELP)
	{
		Octets host((char *)req.OpenidHost.c_str(), req.OpenidHost.size());
		int help_type = req.Parameter1;
		GrcManager::GetInstance().AccountFriendHelp(2, host, help_type);
	}
	else
	{
		IDIPSend(1, "invalid ActivityId");
		return HANDLE_RET_FINISH;
	}
	onSucceeded();
	return HANDLE_RET_FINISH;
}

struct idip_change_data2 : public idip_req_body<IDIPCmdReqChangeData2>
{
	int type;
	int64 para1;
	int64 para2;

	idip_change_data2() : type(0), para1(0), para2(0) {}
	virtual int Serve() override;
	virtual bool Init() = 0;
	static void CallBackChangeData(idip_change_data2 *p_request, int retcode);

	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_change_data2::Serve()
{
	class ChangeData : public DBSecureIDIP::callback
	{
		int c_type;
		int64_t c_para1;
		int64_t c_para2;
		int64_t c_roleid;
	public:
		std::function<void (int)> call_back;

		explicit ChangeData(int data_type, int64_t id, int64_t num, int64_t role_id)
		{
			c_type = data_type;
			c_para1 = id;
			c_para2 = num;
			c_roleid = role_id;
		}
		virtual ~ChangeData() {}
		void ChangeDataOp()
		{
			MakeSecureIDIPProcess(c_roleid, c_type, c_para1, c_para2, Octets(), this);
		}
		void Finish(int finish_result)
		{
			call_back(finish_result);
			delete this;
		}
		virtual void OnCallBack(const DBSecureIDIPArg& arg, const DBSecureIDIPRes& res)
		{
			Finish(res.retcode);
		}
		virtual void OnTimeout(const DBSecureIDIPArg& arg)
		{
			Finish(-1);
		}
	};

	if (!Init())
	{
		onFailed(IDIP_ERR_PARAM);
		return HANDLE_RET_FINISH;
	}
	ChangeData *op = new ChangeData(type, para1, para2, req.RoleId);
	op->call_back = std::bind(&idip_change_data2::CallBackChangeData, this,   std::placeholders::_1);
	op->ChangeDataOp();

	return HANDLE_RET_PENDING;
}
void idip_change_data2::CallBackChangeData(idip_change_data2 *p_request, int retcode)
{
	std::string ret_msg;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_DB_NOTFOUND;
	}
	p_request->IDIPSend(retcode, ret_msg);
	if (retcode == 0)
	{
		SLOG(FORMAT, "idip_command")
		.P("vopenid", p_request->req.OpenId)
		.P("vDesc", p_request->req.RoleId)
		.P("iAttribute", p_request->type)
		.P("iValue", p_request->para1)
		.P("vSerial", p_request->req.Serial)
		.P("iCmdID", p_request->req_head.Cmdid)
		.P("AreaID", GetAreaID(GDeliveryServer::GetInstance()->GetAppid()))
		.P("PlatID", GDeliveryServer::GetInstance()->GetOS());
	}
	delete p_request;
}

struct idip_delete_item : public idip_change_data2
{
	virtual bool Init() override
	{
		type = SEC_IDIP_DEL_ITEM;
		para1 = req.ItemId;
		para2 = req.ItemNum;
		return true;
	}
};

struct idip_send_exp : public idip_change_data2
{
	virtual bool Init() override
	{
		type = SEC_IDIP_SEND_EXP;
		para1 = req.Num;
		return true;
	}
};

struct idip_send_money : public idip_change_data2
{
	virtual bool Init() override
	{
		if (req.MoneyType == 0) //金币
		{
			type = SEC_IDIP_MONEY;
			para1 = req.Num;
		}
		else if (req.MoneyType == 1) //点券
		{
			type = SEC_IDIP_CASH;
			para1 = req.Num;
		}
		else if (req.MoneyType == 2) //钻石
		{
			type = SEC_IDIP_DIAMOND;
			para1 = req.Num;
		}
		else if (req.MoneyType == 3) //充值额度
		{
			Octets account = IDIP_ACCOUNT(req);
			if (req.Num > 0)
			{
				MidasRechargeManager::GetInstance().DebugIncSaveAmtFix(account, req.Num);
			}
			else
			{
				MidasRechargeManager::GetInstance().DebugDecSaveAmtFix(account, - req.Num);
			}

			LOG_TRACE("GMT::idip_send_money: account=%.*s|value=%d", LOG_AC(account), req.Num);
			GLog::formatlog("GMT::idip_send_money", "account=%.*s|value=%d", LOG_AC(account), req.Num);
		}
		else
		{
			return false;
		}
		return true;
	}
};

struct idip_finish_task : public idip_change_data2
{
	virtual bool Init() override
	{
		type = SEC_IDIP_FINISH_TASK;
		para1 = req.TaskId;
		return true;
	}
};

struct idip_add_equip : public idip_change_data2
{
	virtual bool Init() override
	{
		type = SEC_IDIP_ADD_EQUIP;
		para1 = req.ItemId;
		return true;
	}
};

//追缴状态操作
struct idip_cash_pursue : public idip_req_body<IDIPCmdReqCashPursue>
{
	int type; //0清除 1增加 2查询
	idip_cash_pursue(int _type) : type(_type) {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBackChangeData(idip_cash_pursue *p_request, const DBSecureIDIPRes& res);
};

int idip_cash_pursue::Serve()
{
	class ChangeData : public DBSecureIDIP::callback
	{
		int c_type;
		int64_t c_time;
		int64_t c_amount;
		int64_t c_roleid;
		Octets c_msg;
	public:
		std::function<void (const DBSecureIDIPRes&)> call_back;

		explicit ChangeData(int data_type, int64_t p1, int64_t p2, const Octets& msg, int64_t role_id)
		{
			c_type = data_type;
			c_time = p1;
			c_amount = p2;
			c_roleid = role_id;
			c_msg = msg;
		}
		virtual ~ChangeData() {}
		void ChangeDataOp()
		{
			MakeSecureIDIPProcess(c_roleid, c_type, c_time, c_amount, c_msg, this);
		}
		void Finish(const DBSecureIDIPRes& res)
		{
			call_back(res);
			delete this;
		}
		virtual void OnCallBack(const DBSecureIDIPArg& arg, const DBSecureIDIPRes& res)
		{
			Finish(res);
		}
		virtual void OnTimeout(const DBSecureIDIPArg& arg)
		{
			DBSecureIDIPRes res;
			res.retcode = -1;
			Finish(res);
		}
	};

	if (req.RoleId == 0) //针对账号操作
	{
		if (type == 1 && req.MoneyType != 1)
		{
			IDIPSend(1, "invalid money type(need 1 for cash)");
			return HANDLE_RET_FINISH;
		}

		Octets account = IDIP_ACCOUNT(req);

		PB::ipt_account_pursue idip;
		MakePBData(req_head, *idip.mutable_idip());
		account.tostr(*idip.mutable_account());
		idip.set_op_type(type);
		if (type == 1)
		{
			idip.set_count(req.Amount);
			idip.set_tm(req.Time);
		}

		UserInfo *user = UserContainer::Instance().FindUser(account);
		if (user && user->IsLogined() && user->role.IsOnline())
		{
			user->role.info->SendMessage2GS(idip);
		}
		else
		{
			GameDBClient::GetInstance()->SendMessage(idip);
		}
		return HANDLE_RET_PENDING;
	}

	if (type == 0) //0表示清除追缴状态
	{
		ChangeData *op = new ChangeData(SEC_IDIP_CASH_PURSUE_CLEAN, 0, 0, Octets(), req.RoleId);
		op->call_back = std::bind(&idip_cash_pursue::CallBackChangeData, this, std::placeholders::_1);
		op->ChangeDataOp();
	}
	else if (type == 1)
	{
		if (req.MoneyType < 1 || req.MoneyType > 3)
		{
			IDIPSend(1, "invalid money type");
			return HANDLE_RET_FINISH;
		}

#ifdef USE_IDIP_PROXY
		std::string content = HttpProtocol::UrlDecode(req.Reason);
#else
		std::string content = req.Reason;
#endif

		Octets msg(content.data(), content.size());
		ChangeData *op = new ChangeData(SEC_IDIP_CASH_PURSUE, req.Time, req.Amount << 8 | req.MoneyType, msg, req.RoleId);
		op->call_back = std::bind(&idip_cash_pursue::CallBackChangeData, this, std::placeholders::_1);
		op->ChangeDataOp();
	}
	else
	{
		ChangeData *op = new ChangeData(SEC_IDIP_QUERY_PURSUE, 0, 0, Octets(), req.RoleId);
		op->call_back = std::bind(&idip_cash_pursue::CallBackChangeData, this, std::placeholders::_1);
		op->ChangeDataOp();
	}

	return HANDLE_RET_PENDING;
}
void idip_cash_pursue::CallBackChangeData(idip_cash_pursue *p_request, const DBSecureIDIPRes& res)
{
	int retcode = res.retcode;
	if (p_request->type == 2)
	{
		IDIPDataPacket<IDIPCmdRspQueryPursue> rsp;

		p_request->req_head.Cmdid ++;
		p_request->req_head.Result = retcode;
		rsp.head = p_request->req_head;
		rsp.body.Result = retcode;

		if (retcode == 0)
		{
			PB::db_secure_idip_command unit;
			try
			{
				Octets2PB(res.new_data, unit);
			}
			catch (Marshal::Exception&)
			{
				Log::log(LOG_ERR, "idip_cash_pursue::CallBackChangeData, error unmarshal");
				std::string ret_msg;
				p_request->IDIPSend(-1, ret_msg);

				delete p_request;
				return;
			}

			rsp.body.MoneyType = unit.c_para2() & 0xFF;
			rsp.body.Amount = unit.c_para2() >> 8;
			rsp.body.Time = unit.c_para1();
			rsp.body.Reason = std::string(unit.c_para3());
		}

		std::string str;
		std::string err_msg = Json_Data2Str(str, rsp);
		if (err_msg.size() > 0)
		{
			LOG_TRACE("idip_cash_pursue::CallBackChangeData. catch cmdid=%d", p_request->req_head.Cmdid);
		}
		else
		{
			p_request->Send(str);
		}
		delete p_request;
		return;
	}

	std::string ret_msg;
	if (retcode != 0)
	{
		retcode = IDIP_ERR_DB_NOTFOUND;
	}
	p_request->IDIPSend(retcode, ret_msg);
	if (retcode == 0)
	{
		SLOG(FORMAT, "idip_command")
		.P("iAttribute", p_request->type)
		.P("iValue", p_request->req.Amount)
		.P("vSerial", p_request->req.Serial)
		.P("iCmdID", p_request->req_head.Cmdid);
	}
	delete p_request;
}

//查询角色拍卖行数据
struct idip_query_role_auction : public idip_query_role_by_roleid
{
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list) override;
};
void idip_query_role_auction::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	IDIPDataPacket<IDIPCmdRspQueryRoleAuction> rsp;

	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (!p_role)
	{
		req_head.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		auto& body = rsp.body;
		GAUCTION::GAuctionManager::GetInstance().OnIDIPQuery(req.RoleId, body);
		req_head.Result = 0;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		LOG_TRACE("idip_query_role_auction::SendResponse. catch cmdid=%d", req_head.Cmdid);
		return;
	}

	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("iCmdID", req_head.Cmdid);

	Send(result);
}

//云捏脸平台接口
struct idip_query_image_list : public idip_req_body<IDIPCmdReqImageList>
{
	idip_query_image_list() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_query_image_list::Serve()
{
	PB::idip_info_t idip;
	MakePBData(req_head, idip);

	std::string userid = GetImageUserid(req.OpenId, req.AreaId);
	SOCIALSPACE::SocialSpaceManager::GetInstance().QueryImageList(idip, userid);

	return HANDLE_RET_FINISH;
}

struct idip_update_default_image : public idip_req_body<IDIPCmdReqUpdateImage>
{
	idip_update_default_image() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_update_default_image::Serve()
{
	PB::idip_info_t idip;
	MakePBData(req_head, idip);
	std::string userid = GetImageUserid(req.OpenId, req.AreaId);
	SOCIALSPACE::SocialSpaceManager::GetInstance().UpdateDefaultImage(idip, userid, req.ImageId);
	return HANDLE_RET_FINISH;
}

struct idip_add_image_list : public idip_req_body<IDIPCmdReqAddImage>
{
	idip_add_image_list() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};
int idip_add_image_list::Serve()
{
	PB::idip_info_t idip;
	MakePBData(req_head, idip);
	std::string userid = GetImageUserid(req.TargetOpenId, req.AreaId);
	SOCIALSPACE::SocialSpaceManager::GetInstance().AddImageList(idip, req.OpenId, userid, req.ImageId, req.ImageName, req.ImageDescription);
	return HANDLE_RET_FINISH;
}

//中控防沉迷提醒
struct idip_zk_remind : public idip_req_body<IDIPCmdReqZkRemind>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_zk_remind::Serve()
{
	auto account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	UserInfo *user = UserContainer::Instance().FindUser(account);
	if (NULL == user)
	{
		onFailed(IDIP_ERR_INVALID_ACCOUNT);
	}
	else
	{
		PB::npt_text_msg msg;
		msg.set_msg_type(PB::npt_text_msg::ZK_REMIND);
		Octets dst;
		if (req.Title.size())
		{
			CharsetConverter::conv_charset_t2u(Octets(req.Title.data(), req.Title.size()), dst);
			msg.set_title(dst.tostr());
		}
		if (req.Msg.size())
		{
			CharsetConverter::conv_charset_t2u(Octets(req.Msg.data(), req.Msg.size()), dst);
			msg.set_content(dst.tostr());
		}
		if (AntiWallowManager::GetInstance().ZkRemind(account, msg))
		{
			onSucceeded();
			SLOG(FORMAT, "idip_command")
			.P("vopenid", req.OpenId)
			.P("iAttribute", req.RoleId)
			.P("vDesc", req.Msg)
			.P("vSerial", req.Serial)
			.P("iSource", req.Source)
			.P("iCmdID", req_head.Cmdid);
		}
		else
		{
			onFailed(IDIP_ERR_PARAM);
		}
	}
	return HANDLE_RET_FINISH;
}

//中控防沉迷禁玩
struct idip_zk_ban : public idip_req_body<IDIPCmdReqZkBan>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_zk_ban::Serve()
{
	auto account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	PB::npt_text_msg msg;
	msg.set_msg_type(PB::npt_text_msg::ZK_REMIND);
	Octets dst;
	if (req.Title.size())
	{
		CharsetConverter::conv_charset_t2u(Octets(req.Title.data(), req.Title.size()), dst);
		msg.set_title(dst.tostr());
	}
	if (req.Msg.size())
	{
		CharsetConverter::conv_charset_t2u(Octets(req.Msg.data(), req.Msg.size()), dst);
		msg.set_content(dst.tostr());
	}
	AntiWallowManager::GetInstance().ZkBan(account, req.BeginTime, req.EndTime, msg);
	onSucceeded();
	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("vDesc", req.Msg)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);
	return HANDLE_RET_FINISH;
}

//中控防沉迷强制验证
struct idip_zk_verify : public idip_req_body<IDIPCmdReqZkVerify>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_zk_verify::Serve()
{
	auto account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	std::string url_str = HttpProtocol::UrlDecode(req.JsonStr);

	// 发送人脸识别验证请求给客户端
	if (url_str.length())
	{
		Octets verify(url_str.data(), url_str.length());
		Octets trace_id(req.TraceId.data(), req.TraceId.length());
		AntiWallowManager::GetInstance().SendNotify(account, AWMT_FORCE_VERIFY, 0, 0, 0, &verify, &trace_id);
	}

	onSucceeded();
	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.RoleId)
	.P("vDesc", url_str)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);
	return HANDLE_RET_FINISH;
}

//中控防沉迷强制验证
struct idip_monitor_ban_login : public idip_req_body<IDIPCmdReqMonitorBanLogin>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_monitor_ban_login::Serve()
{
	int now = Timer::GetTime();
	std::string msg = HttpProtocol::UrlDecode(req.Msg);
	auto kickOpenidFunc = [this, now, &msg](UserInfo * user)
	{
		if (req.Type == 1) //1、踢人并封号 2、解处罚
		{
			// 实时踢下线
			if (now >= req.StartTime && (req.EndTime == 0 || now < req.EndTime))
			{
				// 踢下线
				Octets utf8_des(req.Msg.c_str(), req.Msg.size());
				Octets ucs2_des;
				CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

				int now = Timer::GetTime();
				int starttime = req.StartTime;
				if (starttime == 0)
				{
					starttime = now;
				}
				int left_time = 0;
				if (req.EndTime == 0)
				{
					left_time = 20 * 365 * 86400;
				}
				else
				{
					left_time = req.EndTime - now;
				}
				GRoleForbid forbid(GNET_FORBID_LOGIN_MONITOR, left_time, starttime, ucs2_des);
				GDeliveryServer::GetInstance()->Send(user->linksid, AnnounceForbidInfo(user->account, user->localsid, forbid));
				UserContainer::Instance().UserLogoutComplete(user, ERROR_ACCOUNT_MONITOR_FORBID, Octets(msg.c_str(), msg.size()));
			}
			if (now < req.StartTime)
			{
				user->SetPreKick(req.StartTime, req.EndTime, msg);
				UserContainer::Instance().SetPreKickInfo(user->account, req.StartTime, req.EndTime, msg);
			}
		}
		if (req.Type == 2)
		{
			user->CancelPreKick();
			UserContainer::Instance().RemovePreKickInfo(user->account);
		}
	};
	UserContainer::Instance().ForEachOpenIdUser(Octets(req.OpenId.c_str(), req.OpenId.size()), kickOpenidFunc);
	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.Type)
	.P("vDesc", req.Msg)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	//onSucceeded();
	return HANDLE_RET_FINISH;
}

//全区全服封号（AQ）请求
struct idip_openid_ban_login : public idip_req_body<IDIPCmdAqDoAllzoneBanAccountReq>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_openid_ban_login::Serve()
{
	auto kickOpenidFunc = [](UserInfo * user)
	{
		// 账号封禁 不考虑开始和结束时间
		UserContainer::Instance().UserLogoutComplete(user, ERROR_ACCOUNT_FORBID, Octets());
	};
	UserContainer::Instance().ForEachOpenIdUser(Octets(req.OpenId.c_str(), req.OpenId.size()), kickOpenidFunc);
	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.Type)
	.P("vDesc", req.Msg)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	//onSucceeded();
	return HANDLE_RET_FINISH;
}

//封禁设备请求
struct idip_deviceid_ban_login : public idip_req_body<IDIPCmdDoBanDeviceReq>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return true;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_deviceid_ban_login::Serve()
{
	auto kickDeviceFunc = [](UserInfo * user)
	{
		// 踢下线
		UserContainer::Instance().UserLogoutComplete(user, ERROR_ACCOUNT_FORBID, Octets());
	};
	UserContainer::Instance().ForEachDeviceIdUser(req.DeviceId, kickDeviceFunc);
	SLOG(FORMAT, "idip_command")
	.P("vDesc", req.DeviceId)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);
	return HANDLE_RET_FINISH;
}

static void __ModifyType2FuncCode(int ModifyType, std::vector<int>& func_codes)
{
	static std::map<int, int> type_code(
	{
		{1, 	kFuncCodeIdipForbidPlayerName},
		{2, 	kFuncCodeIdipForbidGanEnXiangCeJiYu},
		{31, 	kFuncCodeIdipForbidLittleShopName},
		{32, 	kFuncCodeIdipForbidLittleShopSlogan},
		{4, 	kFuncCodeIdipForbidProfessionPetName},
		{5, 	kFuncCodeIdipForbidHeirName},
		{6,		kFuncCodeIdipForbidCorpsPurpose},
		{7,		kFuncCodeIdipForbidCorpsPoster},
		{8,		kFuncCodeIdipForbidCorpsNotice},
		{9,		kFuncCodeIdipForbidHometownDesignPlanName},
		{10,	kFuncCodeIdipForbidPetName},
		{11,	kFuncCodeIdipForbidArenaTeamName},
		{12,	kFuncCodeIdipForbidEliminateTeamName},
		{13,	kFuncCodeIdipForbidHoneyGardenName},
		{14,	kFuncCodeIdipForbidAnonymousChatRoomName},
		{15,	kFuncCodeIdipForbidAnonymousChatRoomTag},
		{16,	kFuncCodeIdipForbidIntimateFashionDressName},
		{17,	kFuncCodeIdipForbidFriendGroupName},
		{18,	kFuncCodeIdipForbidSkillPlanName},
		{19,	kFuncCodeIdipForbidBloodlinesPlanName},
		{20,	kFuncCodeIdipForbidYiChuFashionDressName},
		{211,	kFuncCodeIdipForbidJiBanChengWei},
		{212,	kFuncCodeIdipForbidJiBanJiYu},
		{221,	kFuncCodeIdipForbidLingHunJiBanName},
		{222,	kFuncCodeIdipForbidLingHunJiBanShiYan},
		{23,	kFuncCodeIdipForbidLingHunShiYueShuName},
		{241,	kFuncCodeIdipForbidJieHunBanLvChengWei},
		{242,	kFuncCodeIdipForbidJieHunShiYan},
		{25,	kFuncCodeIdipForbidPengYouQuanShangChuan},
		{26,	kFuncCodeIdipForbidHaoYouQun},
		{272,	kFuncCodeIdipForbidShiTu},
		{28,	kFuncCodeIdipForbidPengYouQuanLiuYan},
		{29,	kFuncCodeIdipForbidPengYouQuanLiWuJiYu},
		{30,	kFuncCodeIdipForbidJiaYuanLiuYan},
		{33,	kFuncCodeIdipForbidSongHuaLiuYan},
		{34,	kFuncCodeIdipForbidJiaYuanHuanYingYu},
		{35,	kFuncCodeIdipForbidJiaYuanLiWuJiYu},
		{36,	kFuncCodeIdipForbidCorpsName},
	});

	func_codes.clear();
	if (ModifyType == 99)
	{
		for (auto iter = type_code.begin(); iter != type_code.end(); ++iter)
		{
			func_codes.push_back(iter->second);
		}
	}
	else
	{
		auto iter = type_code.find(ModifyType);
		if (iter != type_code.end())
		{
			func_codes.push_back(iter->second);
		}
	}
}

//禁止修改游戏内文字
struct idip_forbid_modify_content : public idip_req_body<IDIPCmdForbidModifyContent>
{
	virtual int Serve() override
	{
		int err_code = 0;
		bool forbid = false;
		std::vector<int> func_codes;
		do
		{
			if (req.BanType != 1 && req.BanType != 2)
			{
				err_code = 1;
				break;
			}

			__ModifyType2FuncCode(req.ModifyType, func_codes);
			if (func_codes.empty())
			{
				err_code = 2;
				break;
			}

			forbid = (req.BanType == 1);

#ifdef USE_IDIP_PROXY
			std::string ban_reason = HttpProtocol::UrlDecode(req.BanReason);
#else
			std::string ban_reason = req.BanReason;
#endif
			Octets utf8_des(ban_reason.c_str(), ban_reason.size());
			Octets ucs2_des;
			CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);
			std::string str_reason((const char *)ucs2_des.begin(), ucs2_des.size());

			for (auto iter = func_codes.begin(); iter != func_codes.end(); ++iter)
			{
				GNET::SetIdipForbidPlayerFunc(req.RoleId, forbid, *iter, str_reason, req.BanTime);
			}
		}
		while (false);

		LOG_TRACE("idip_forbid_modify_content::Serve roleid=%ld, modify_type=%d, func_codes.size=%d, ban_type=%d, ban_time=%d, err_code=%d",
		          req.RoleId, req.ModifyType, (int)func_codes.size(), req.BanType, req.BanTime, err_code);

		if (err_code == 0)
		{
			onSucceeded();
		}
		else
		{
			onFailed(IDIP_ERR_PARAM);
		}
		return HANDLE_RET_FINISH;
	}
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}

};

//修改游戏内文字
struct idip_modify_content : public idip_req_body<IDIPCmdModifyContent>
{
	enum SERVER_ERR_CODE
	{
		SERVER_ERR_CODE_TARGET_NULL					= 1,
		SERVER_ERR_CODE_CONTENT_EMPTY				= 2,
		SERVER_ERR_CODE_INVALID_MODIFYTYPE			= 3,
		SERVER_ERR_CODE_OCTETS2PB_CAREER_SHOP		= 4,
		SERVER_ERR_CODE_OCTETS2PB_CHILD				= 5,
		SERVER_ERR_CODE_HONEY_GARDEN_IPD_NOT_INIT	= 6,
		SERVER_ERR_CODE_NO_HONEY_GARDEN1			= 7,
		SERVER_ERR_CODE_NO_HONEY_GARDEN2			= 8,
		SERVER_ERR_CODE_NO_ANONYMOUS_ROOM			= 9,
		SERVER_ERR_CODE_NO_CORPS					= 10,
		SERVER_ERR_CODE_SEND_PROTOC_FAILED			= 11,
		SERVER_ERR_CODE_EXCEPTION					= 12,
	};
	virtual int Serve() override
	{
		int err_code = 0;
		std::vector<int> func_codes;
		do
		{
			__ModifyType2FuncCode(req.ModifyType, func_codes);
			if (func_codes.size() != 1)
			{
				err_code = static_cast<int>(SERVER_ERR_CODE_INVALID_MODIFYTYPE);
				break;
			}

#ifdef USE_IDIP_PROXY
			std::string utf8_content = HttpProtocol::UrlDecode(req.ModifyContent);
#else
			std::string utf8_content = req.ModifyContent;
#endif

			Octets oct_content((const void *)utf8_content.data(), utf8_content.size());
			CharsetConverter::conv_charset_t2u(oct_content, oct_content);
			std::string content((const char *)oct_content.begin(), oct_content.size());

#define CHECK_TARGET		\
	if (0 == req.TargetID)	\
	{						\
		err_code = static_cast<int>(SERVER_ERR_CODE_TARGET_NULL);	\
		break;				\
	}
			switch (func_codes[0])
			{
			case kFuncCodeIdipForbidPlayerName:
			{
				CHECK_TARGET
				std::string name_str = utf8_content + std::to_string(req.TargetID) + std::to_string(Timer::GetTime() % 100);
				if (utf8_content.empty())
				{
					std::stringstream stream;
					stream << std::hex << req.TargetID << (Timer::GetTime() % 100);
					name_str = "小怪兽" + stream.str();
				}

				Octets uname(name_str.c_str(), name_str.size());
				CharsetConverter::conv_charset_t2u(uname, uname);

				auto account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
				AllocRoleRename *rpc = (AllocRoleRename *)Rpc::Call(RPC_ALLOCROLERENAME, AllocRoleRenameArg(MERGE_ZONE(req.TargetID), account, req.TargetID, uname));
				rpc->debug = true;
				rpc->del_oldest = true;
				UniqueNameClient::GetInstance()->SendProtocol(rpc);
			}
			break;

			case kFuncCodeIdipForbidArenaTeamName:
			{
				CHECK_TARGET
				std::string name_str = utf8_content + std::to_string(req.TargetID);
				Octets uname(name_str.c_str(), name_str.size());
				CharsetConverter::conv_charset_t2u(uname, uname);

				ARENA_GROUP_MANAGER::ArenaGroupManager::GetInstance().IDIPRename(req.TargetID, uname);
			}
			break;

			case kFuncCodeIdipForbidEliminateTeamName:
			{
				CHECK_TARGET
				std::string name_str = utf8_content + std::to_string(req.TargetID);
				Octets uname(name_str.c_str(), name_str.size());
				CharsetConverter::conv_charset_t2u(uname, uname);

				ELIMINATE_GROUP_MANAGER::EliminateGroupManager::GetInstance().IDIPRename(req.TargetID, uname);
			}
			break;

			case kFuncCodeIdipForbidGanEnXiangCeJiYu:
			{
				PB::ipt_idip_modify_content db_request;
				db_request.set_op(PB::IMCO_MODIFY_GANENXIANGCEJIYU);
				db_request.set_roleid(req.RoleId);
				db_request.set_content(content);
				GameDBClient::GetInstance()->SendMessage(db_request);
			}
			break;

			case kFuncCodeIdipForbidLittleShopName:
			case kFuncCodeIdipForbidLittleShopSlogan:
			{
				RoleMap::Instance().IdipModifyContentCareerShopNameOrSlogan(req.RoleId, content, func_codes[0]);
			}
			break;

			case kFuncCodeIdipForbidProfessionPetName:
			{
				RoleMap::Instance().IdipModifyContentCutePetName(req.RoleId, content);
			}
			break;

			case kFuncCodeIdipForbidHeirName:
			{
				RoleMap::Instance().IdipModifyContentChildName(req.RoleId, content);
			}
			break;

			case kFuncCodeIdipForbidCorpsPurpose:
			case kFuncCodeIdipForbidCorpsPoster:
			case kFuncCodeIdipForbidCorpsNotice:
			{
				CHECK_TARGET
				CorpsManager::GetInstance().IDIPModifyContentCorps(req.TargetID, func_codes[0], content);
			}
			break;

			case kFuncCodeIdipForbidHoneyGardenName:
			{
				HoneyGardenManager::Instance().IDIPModifyContentHoneyGardenName(req.RoleId, content);
			}
			break;

			case kFuncCodeIdipForbidAnonymousChatRoomName:
			case kFuncCodeIdipForbidAnonymousChatRoomTag:
			{
				CHECK_TARGET
				AnonymousChatRoomManager::GetInstance().IDIPModifyContentAcrNameOrTags(req.TargetID, content, func_codes[0]);
			}
			break;

			case kFuncCodeIdipForbidIntimateFashionDressName:
			{
				CHECK_TARGET
				RoleMap::Instance().IdipModifyContentIntimateFashionDressName(req.RoleId, req.TargetID, content);
			}
			break;

			case kFuncCodeIdipForbidFriendGroupName:
			{
				RoleInfo *pRole = RoleMap::Instance().Find(req.RoleId);
				if (pRole != NULL)
				{
					pRole->friends.IDIPModifyContentChangeGroupRename(content);
				}
				else
				{
					PB::ipt_idip_modify_content db_request;
					db_request.set_op(PB::IMCO_MODIFY_FRIENDGROUPNAME);
					db_request.set_roleid(req.RoleId);
					db_request.set_content(content);
					GameDBClient::GetInstance()->SendMessage(db_request);
				}
			}
			break;
			case kFuncCodeIdipForbidJiBanChengWei:
			case kFuncCodeIdipForbidJiBanJiYu:
			{
				CHECK_TARGET
				GoldenIntimateManager::GetInstance().IdipModifyContentNameOrMsg(func_codes[0], req.RoleId, req.TargetID, content);
			}
			break;

			case kFuncCodeIdipForbidSkillPlanName:
			{
				MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_MODIFY_SKILL_PLAN_NAME, 0, 0, GNET::Octets(content.data(), content.size()));
			}
			break;

			case kFuncCodeIdipForbidBloodlinesPlanName:
			{
				MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_MODIFY_BLOOD_PLAN_NAME, 0, 0, GNET::Octets(content.data(), content.size()));
			}
			break;

			case kFuncCodeIdipForbidYiChuFashionDressName:
			{
				MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_MODIFY_YICHU_FASHION_DRESS_NAME, 0, 0, GNET::Octets(utf8_content.data(), utf8_content.size()));
			}
			break;

			case kFuncCodeIdipForbidLingHunJiBanName:
			case kFuncCodeIdipForbidLingHunJiBanShiYan:
			case kFuncCodeIdipForbidLingHunShiYueShuName:
			{
				CHECK_TARGET
				GoldenIntimateManager::GetInstance().IdipModifyContentSoulNameOrMsg(func_codes[0], req.RoleId, req.TargetID, content);
			}
			break;

			case kFuncCodeIdipForbidJieHunBanLvChengWei:
			{
				MakeSecureIDIPProcess(req.RoleId, SEC_IDIP_MODIFY_HARMOUIOUS_NAME, 0, 0, GNET::Octets(content.data(), content.size()));
			}
			break;
			case kFuncCodeIdipForbidJieHunShiYan:
			{
				Octets oct_content(utf8_content.data(), utf8_content.size());
				RoleInfo *pInfo = RoleMap::Instance().Find(req.RoleId);
				if (pInfo)
				{
					pInfo->friends.SetMarriageMsg(oct_content, 0, false);
				}
			}
			break;

			case kFuncCodeIdipForbidPetName:
			{
				RoleMap::Instance().IdipModifyContentGuardName(req.RoleId, content);
			}
			break;

			case kFuncCodeIdipForbidHometownDesignPlanName:
			{
				RoleMap::Instance().IdipModifyContentHometownSchemeName(req.RoleId, content);
			}
			break;

			//---------------------------------------------------------------
			case kFuncCodeIdipForbidPengYouQuanShangChuan:
			{
				RoleMap::Instance().IdipModifyContentPengYouQuanDongTai(req.RoleId, req.TargetID, utf8_content);
			}
			break;
			case kFuncCodeIdipForbidHaoYouQun:
			{
				GROUP::GroupManager::GetInstance().IdipModifyName(req.TargetID, content);
			}
			break;
			case kFuncCodeIdipForbidPengYouQuanLiuYan:
			{
				RoleMap::Instance().IdipModifyContentPengYouQuanLiuYan(req.RoleId, req.TargetID, utf8_content);
			}
			break;
			case kFuncCodeIdipForbidPengYouQuanLiWuJiYu:
			{
				//RoleMap::Instance().IdipModifyContentPengYouQuanLiWuJiYu(req.RoleId, req.TargetID, utf8_content);
			}
			break;
			case kFuncCodeIdipForbidJiaYuanLiuYan:
			{
				RoleMap::Instance().IdipModifyContentJiaYuanLiuYan(req.RoleId, req.TargetID, utf8_content);
			}
			break;
			case kFuncCodeIdipForbidJiaYuanHuanYingYu:
			{
				// 还差个db改数据
				RoleMap::Instance().IdipModifyContentHometownWelcomeTip(req.RoleId, req.TargetID, content);
			}
			break;
			case kFuncCodeIdipForbidJiaYuanLiWuJiYu:
			{
				//RoleMap::Instance().IdipModifyContentJiaYuanLiWuJiYu(req.RoleId, req.TargetID, utf8_content);
			}
			break;
			case kFuncCodeIdipForbidSongHuaLiuYan:
			{
				RoleMap::Instance().IdipModifyContentSongHuaLiuYan(req.RoleId, req.TargetID, content);
			}
			break;
			case kFuncCodeIdipForbidCorpsName:
			{
				try
				{
					ruid_t corps_id = req.TargetID;
					auto pCorps = CorpsManager::GetInstance().GetCorp(corps_id);
					if (!pCorps)
					{
						err_code = static_cast<int>(SERVER_ERR_CODE_NO_CORPS);
						break;
					}

					AllocName *rpc = (AllocName *)Rpc::Call(RPC_ALLOCNAME, AllocNameArg(g_zoneid, ALLOC_FACTION_NAME, oct_content));
					rpc->corps_id = corps_id;
					rpc->callback = [this](ruid_t id, Octets & name, int ret)
					{
						int retcode = ret;
						if (retcode == 0)
						{
							auto pCorps = CorpsManager::GetInstance().GetCorp(id);
							if (!pCorps)
							{
								return;
							}
							std::string rename((const char *)name.begin(), name.size());
							pCorps->IDIPRename(rename);
						}
					};
					if (!UniqueNameClient::GetInstance()->SendProtocol(rpc))
					{
						err_code = static_cast<int>(SERVER_ERR_CODE_SEND_PROTOC_FAILED);
						break;
					}
				}
				catch (...)
				{
					err_code = static_cast<int>(SERVER_ERR_CODE_EXCEPTION);
				}
			}
			break;
			default:
				break;
			}
		}
		while (false);

		LOG_TRACE("idip_modify_content::Serve IdipModifyContent roleid=%ld, targetid=%ld, modify_type=%d, content=%s, content_size=%zu, err_code=%d",
		          req.RoleId, req.TargetID, req.ModifyType, req.ModifyContent.c_str(), req.ModifyContent.size(), err_code);

		if (err_code == 0)
		{
			onSucceeded();
		}
		else
		{
			onFailed(IDIP_ERR_PARAM);
		}
		return HANDLE_RET_FINISH;
	}
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

//全区全服禁言（AQ）请求
struct idip_opendi_ban_chat : public idip_req_body<IDIPCmdAqDoAllzoneBanChatReq>
{
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_opendi_ban_chat::Serve()
{
	auto BanOpenIdFunc = [this](UserInfo * user)
	{
		if (user)
		{
#ifdef USE_IDIP_PROXY
			req.Msg = HttpProtocol::UrlDecode(req.Msg);
#endif
			Octets utf8_des(req.Msg.c_str(), req.Msg.size());
			Octets ucs2_des;
			CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);

			GRoleForbid forbid(GNET_FORBID_TALK_GLOBAL, 0, 0, ucs2_des);
			if (req.Type == 1)
			{
				forbid.createtime = req.StartTime > 0 ? req.StartTime : Timer::GetTime();
				forbid.time = req.EndTime;
				UserContainer::Instance().AddAllzoneForbidChatAccount(user->account, forbid);
			}
			else
			{
				UserContainer::Instance().DelAllzoneForbidChatAccount(user->account);
			}
			D2LForbidChat notify(user->roleid, user->localsid, forbid);
			GDeliveryServer::GetInstance()->Send(user->linksid, notify);
			if (user->status == PLAYER_STATUS_INGAME)
			{
				forbid.type = GNET_FORBID_TALK; //兼容客户端原协议
				if (req.Type == 1)
				{
					forbid.time = forbid.time - Timer::GetTime(); //兼容客户端原协议
				}
				GDeliveryServer::GetInstance()->Send(user->linksid, AnnounceForbidInfo(user->account, user->localsid, forbid));
			}
		}
	};

	UserContainer::Instance().ForEachOpenIdUser(Octets(req.OpenId.c_str(), req.OpenId.size()), BanOpenIdFunc);
	SLOG(FORMAT, "idip_command")
	.P("vopenid", req.OpenId)
	.P("iAttribute", req.Type)
	.P("vDesc", req.Msg)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid);

	return HANDLE_RET_FINISH;
}

template <typename RPCDATA_REQ>
struct idip_socialspace_role_op : public idip_req_body<RPCDATA_REQ>
{
	virtual int Serve() override
	{
		Octets user_account = idip_req_body<RPCDATA_REQ>::IDIP_ACCOUNT(idip_req_body<RPCDATA_REQ>::req);
		RoleInfo *p_role = RoleMap::Instance().Find(idip_req_body<RPCDATA_REQ>::req.RoleId);
		if (p_role != NULL)
		{
			if (p_role->account != user_account)
			{
				idip_req_body<RPCDATA_REQ>::onFailed(IDIP_ERR_INVALID_ROLE);
				return idip_req_body<RPCDATA_REQ>::HANDLE_RET_FINISH;
			}
			else
			{
				SendToSocialSpace(p_role);
				return idip_req_body<RPCDATA_REQ>::HANDLE_RET_PENDING;
			}
		}
		else
		{
			getroleinfo_handle callback = std::bind(&idip_socialspace_role_op::CallBack_GetRoleData, this,
			                                        std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
			RoleMap::GetRoleInfo(idip_req_body<RPCDATA_REQ>::req.RoleId, Octets(), &callback);
			return idip_req_body<RPCDATA_REQ>::HANDLE_RET_PENDING;
		}
	}
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return idip_req_body<RPCDATA_REQ>::req.Partition;
	}
	virtual void SendToSocialSpace(RoleInfo *p_role) {}
	virtual int DeliverydOperation(RoleInfo *p_role)
	{
		return 0;
	}
	static void CallBack_CspFinish(idip_socialspace_role_op *p_req, int retcode)
	{
		if (retcode == 0)
		{
			p_req->onSucceeded();
		}
		else
		{
			p_req->onFailed(retcode);
		}
		delete p_req;
	}
	static void CallBack_GetRoleData(idip_socialspace_role_op *p_req, int retcode, int64_t roleid, RoleInfo *p_role)
	{
		Octets user_account = p_req->IDIP_ACCOUNT(p_req->req);
		if (retcode != 0 || p_role->account != user_account)
		{
			p_req->onFailed(IDIP_ERR_INVALID_ROLE);
			delete p_req;
		}
		else
		{
			p_req->SendToSocialSpace(p_role);
		}
	}
};

//删除朋友圈(socialspace)留言及评论
template <>
void idip_socialspace_role_op<IDIPCmdReqDelSocialspaceMsg>::SendToSocialSpace(RoleInfo *p_role)
{
	SOCIALSPACE::ssresult_handle callback = std::bind(&idip_socialspace_role_op::CallBack_CspFinish, this, std::placeholders::_1);
	SOCIALSPACE::SocialSpaceManager::GetInstance().IdipDelPost(p_role, req.Type, req.PostId, req.StartTime, req.EndTime, &callback);
}

//修改朋友圈照片
template <>
void idip_socialspace_role_op<IDIPCmdReqModifySocialspace>::SendToSocialSpace(RoleInfo *p_role)
{
	SOCIALSPACE::ssresult_handle callback = std::bind(&idip_socialspace_role_op::CallBack_CspFinish, this, std::placeholders::_1);
	SOCIALSPACE::SocialSpaceManager::GetInstance().IdipModifyImage(p_role, req.Type, req.PostId, HttpProtocol::UrlDecode(req.ModifyPicUrl), &callback);
}

//封禁游戏内朋友圈
template <>
void idip_socialspace_role_op<IDIPCmdReqBanSocialSpace>::SendToSocialSpace(RoleInfo *p_role)
{
	Octets user_account = IDIP_ACCOUNT(req);
	SOCIALSPACE::ssresult_handle callback = std::bind(&idip_socialspace_role_op::CallBack_CspFinish, this, std::placeholders::_1);
	SOCIALSPACE::SocialSpaceManager::GetInstance().IdipBanSocialSpace(p_role, req.Type, std::string((char *)user_account.begin(), user_account.size()), req.BanTime, &callback);
}

//替换玩家签名
template <>
void idip_socialspace_role_op<IDIPCmdReqChangeSocialSpaceSign>::SendToSocialSpace(RoleInfo *p_role)
{
	SOCIALSPACE::ssresult_handle callback = std::bind(&idip_socialspace_role_op::CallBack_CspFinish, this, std::placeholders::_1);
	SOCIALSPACE::SocialSpaceManager::GetInstance().IdipModifySign(p_role, req.Type, HttpProtocol::UrlDecode(req.Content), &callback);
}

template <>
void idip_socialspace_role_op<IDIPCmdReqDelZspaceBulletin>::SendToSocialSpace(RoleInfo *p_role)
{
	SOCIALSPACE::ssresult_handle callback = std::bind(&idip_socialspace_role_op::CallBack_CspFinish, this, std::placeholders::_1);
	ZSpaceManager::Instance().IDIPDelBulletin(p_role, &callback);
}

template <>
int idip_socialspace_role_op<IDIPCmdReqBanZspaceUploadBulletin>::DeliverydOperation(RoleInfo *p_role)
{
	return ZSpaceManager::Instance().IDIPBanUploadBulletin(p_role, req.Operation, req.BanTime, req.Serial);
}
template <>
int idip_socialspace_role_op<IDIPCmdReqBanZspaceUploadBulletin>::Serve()
{
	Octets user_account = IDIP_ACCOUNT(req);
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	if (p_role != NULL)
	{
		if (p_role->account != user_account)
		{
			onFailed(IDIP_ERR_INVALID_ROLE);

		}
		else
		{
			int ret = DeliverydOperation(p_role);
			if (ret == 0)
			{
				onSucceeded();
			}
			else
			{
				onFailed(ret);
			}
		}
		return HANDLE_RET_FINISH;
	}
	else
	{
		getroleinfo_handle callback = [p_req = this](int retcode, ruid_t roleid, RoleInfo * p_role)->void
		{
			if (p_role)
			{
				return;
			}
			Octets user_account = p_req->IDIP_ACCOUNT(p_req->req);
			if (retcode != 0 || p_role->account != user_account)
			{
				p_req->onFailed(IDIP_ERR_INVALID_ROLE);

			}
			else
			{
				int ret = p_req->DeliverydOperation(p_role);
				if (ret == 0)
				{
					p_req->onSucceeded();
				}
				else
				{
					p_req->onFailed(ret);
				}
			}
			delete p_req;
		};
		RoleMap::GetRoleInfo(req.RoleId, Octets(), &callback);
		return HANDLE_RET_PENDING;
	}
}
//封禁游戏内朋友圈
template <>
int idip_socialspace_role_op<IDIPCmdReqBanSocialSpace>::Serve()
{
	if (req.Type == 1 || req.Type == 2 || req.Type == 101 || req.Type == 301 || req.Type == 500) //1 封角色使用空间, 2 封openid 使用空间, 101 封禁角色CG评论 301 禁止参与比赛 500通用留言板发布和评论留言
	{
		Octets user_account = IDIP_ACCOUNT(req);
		RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
		if (p_role != NULL)
		{
			if (p_role->account != user_account)
			{
				onFailed(IDIP_ERR_INVALID_ROLE);
				return HANDLE_RET_FINISH;
			}
			else
			{
				SendToSocialSpace(p_role);
				return HANDLE_RET_PENDING;
			}
		}
		else
		{
			getroleinfo_handle callback = std::bind(&idip_socialspace_role_op::CallBack_GetRoleData, this,
			                                        std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
			RoleMap::GetRoleInfo(req.RoleId, Octets(), &callback);
			return HANDLE_RET_PENDING;
		}
	}
	else
	{
		SendToSocialSpace(NULL);
		return HANDLE_RET_PENDING;
	}
}

class idip_socialspace_ssp_work_op : public idip_socialspace_role_op<IDIPCmdSSPWorkReq>
{
	int operation_type;
public:
	idip_socialspace_ssp_work_op(int type) : operation_type(type) {}

	void SendToSocialSpace(RoleInfo *p_role)
	{
		SOCIALSPACE::ssresult_handle callback = std::bind(&idip_socialspace_role_op::CallBack_CspFinish, this, std::placeholders::_1);
		SOCIALSPACE::SocialSpaceManager::GetInstance().IdipDelPost(p_role, operation_type, req.WorksID, 0, 0, &callback);
	}
};

//按roleid查询充值余额
struct idip_query_role_free_cash : public idip_req_body<IDIPCmdReqCommon>
{
public:
	idip_query_role_free_cash() : idip_req_body() {}

	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBack_QueryRoleFreeCash(idip_query_role_free_cash *p_req, int retcode, int64_t free_cash);
	virtual void SendResponse(int retcode, int64_t free_cash);
};
int idip_query_role_free_cash::Serve()
{
	RoleInfo *pInfo = RoleMap::Instance().FindOnline(req.RoleId);
	SLOG(FORMAT, "idip_query_role_free_cash").P("role_id", req.RoleId).P("is_online", (pInfo != nullptr));
	if (pInfo)
	{
		QueryRoleFreeCashArg arg;
		arg.role_id = req.RoleId;
		arg.money_type = EXPSHOPSELLMODE_MUSTBE_TRADEMONEY;
		QueryRoleFreeCash *rpc = (QueryRoleFreeCash *)Rpc::Call(RPC_QUERYROLEFREECASH, arg);
		rpc->cb = std::bind(&CallBack_QueryRoleFreeCash, this, std::placeholders::_1, std::placeholders::_2);
		if (pInfo->SendProtocol2GS(rpc))
		{
			return HANDLE_RET_PENDING;
		}
	}
	SendResponse(IDIP_ERR_ROLE_OFFLINE, 0);
	return HANDLE_RET_FINISH;
}
void idip_query_role_free_cash::CallBack_QueryRoleFreeCash(idip_query_role_free_cash *p_req, int retcode, int64_t free_cash)
{
	p_req->SendResponse(retcode, free_cash);
	delete p_req;
}
void idip_query_role_free_cash::SendResponse(int retcode, int64_t free_cash)
{
	IDIPDataPacket<IDIPCmdRspQueryRoleFreeCash> rsp;
	rsp.body.FreeCash = free_cash;
	rsp.body.Result = retcode;
	req_head.Cmdid = req_head.Cmdid + 1;
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		return;
	}
	Send(result);
}

// 按roleid查询玩家钻石数量
struct idip_query_role_diamond_count: public idip_req_body<IDIPCmdReqCommon>
{
public:
	idip_query_role_diamond_count(): idip_req_body() {}
	int Serve() override
	{
		RoleInfo *pinfo = RoleMap::Instance().FindOnline(req.RoleId);
		SLOG(FORMAT, "idip_query_role_diamond_count")
		.P("roleid", req.RoleId)
		.P("is_online", (pinfo != nullptr));

		if (!pinfo)
		{
			SendResponse(IDIP_ERR_ROLE_OFFLINE, 0);
			return HANDLE_RET_FINISH;
		}

		QueryRoleFreeCashArg arg;
		arg.role_id = req.RoleId;
		arg.money_type = EXPSHOPSELLMODE_MUSTBE_BOUNDCASH;
		QueryRoleFreeCash *rpc = (QueryRoleFreeCash *)Rpc::Call(RPC_QUERYROLEFREECASH, arg);
		rpc->cb = std::bind(&CallBack_QueryRoleDiamondCount, this,
		                    std::placeholders::_1, std::placeholders::_2);
		if (!pinfo->SendProtocol2GS(rpc))
		{
			SendResponse(IDIP_ERR_ROLE_OFFLINE, 0);
			return HANDLE_RET_FINISH;
		}

		return HANDLE_RET_PENDING;
	}
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBack_QueryRoleDiamondCount(idip_query_role_diamond_count *p_req,
	        int retcode, int64_t count)
	{
		p_req->SendResponse(retcode, count);
		delete p_req;
	}
	void SendResponse(int retcode, int64_t diamond_count)
	{
		IDIPDataPacket<IDIPCmdRspQueryRoleDiamondCount> rsp;
		rsp.body.DiamondNum = diamond_count;
		rsp.body.Result = retcode;
		rsp.body.RetMsg = retcode == 0 ? "success" : "failed";
		req_head.Cmdid = req_head.Cmdid + 1;
		rsp.head = req_head;

		std::string result;
		std::string err_msg = Json_Data2Str(result, rsp);
		if (err_msg.size() > 0)
		{
			return;
		}
		Send(result);
	}
};

// 扣钻石并发放物品
struct idip_use_diamond_and_send_item: public idip_req_body<IDIPCmdUseDiamondAndSendItem>
{
	idip_use_diamond_and_send_item(): idip_req_body() {}
	int Serve() override
	{
		SLOG(INFO, "idip_use_diamond_and_send_item")
		.P("roleid", req.RoleId)
		.P("DeductDiamondAmount", req.DeductDiamondAmount)
		.P("MailBoxSlotLimit", req.MailBoxSlotLimit)
		.P("ItemListSize", req.ItemList.size());

		if (!GET_FUNC_SWITCH(kFuncCodeIdipUseDiamondSendItem))
		{
			onFailed(IDIP_ERR_INVALID);
			return HANDLE_RET_FINISH;
		}

		if (0 == req.DeductDiamondAmount && 0 == req.MailBoxSlotLimit && req.ItemList.empty())
		{
			onFailed(IDIP_ERR_PARAM);
			return HANDLE_RET_FINISH;
		}

		RoleInfo *pinfo = RoleMap::Instance().FindOnline(req.RoleId);
		if (!pinfo)
		{
			onFailed(IDIP_ERR_ROLE_OFFLINE);
			return HANDLE_RET_FINISH;
		}

		UseCashAndSendItemArg arg;
		arg.roleid = req.RoleId;
		arg.use_cash_amount = req.DeductDiamondAmount;
		arg.mailbox_slot_limit = req.MailBoxSlotLimit;
		arg.money_type = EXPSHOPSELLMODE_MUSTBE_BOUNDCASH;

		std::stringstream contentss;
		contentss << req.MailBoxSlotLimit;
		if (!req.ItemList.empty())
		{
#ifdef USE_IDIP_PROXY
			std::string utf8_title = HttpProtocol::UrlDecode(req.MailTitle);
			std::string utf8_content = HttpProtocol::UrlDecode(req.MailContent);
#else
			std::string utf8_title = title;
			std::string utf8_content = content;
#endif

			contentss << "#" << utf8_title << "#" << utf8_content;
			Octets ucs2_title, ucs2_content;
			CharsetConverter::conv_charset_t2u(Octets(utf8_title.c_str(), utf8_title.size()), ucs2_title);
			CharsetConverter::conv_charset_t2u(Octets(utf8_content.c_str(), utf8_content.size()), ucs2_content);

			Mail& mail = arg.mail;
			mail.header.category = CATEGORY_MAIL_SYSTEM | MAIL_CATEGORY_IDIP_USE_CASH_AND_SEND_ITEM; // 和点券一样
			mail.header.subject = ucs2_title;
			mail.header.from = 0;
			mail.header.to = req.RoleId;
			mail.header.date = Timer::GetTime();
			mail.header.status = MAIL_STATUS_ATTACHED;
			mail.header.msgid = 0;
			mail.context = ucs2_content;

			for (size_t i = 0; i < req.ItemList.size(); ++i)
			{
				GRoleInventory item;
				item.id = req.ItemList[i].ItemId;
				item.count = req.ItemList[i].ItemNum;
				mail.attachment.items.push_back(item);
				contentss << (i == 0 ? "#" : ",");
				contentss << "[" << item.id << "," << item.count << "]";
			}
		}

		UseCashAndSendItem *rpc = (UseCashAndSendItem *)Rpc::Call(RPC_USECASHANDSENDITEM, arg);
		rpc->callback = [this](int retcode)
		{
			if (0 == retcode)
			{
				onSucceeded();
			}
			else
			{
				onFailed(retcode);
			}
			delete this;
		};

		SLOG(FORMAT, "idip_command")
		.P("vopenid", req.OpenId)
		.P("iAttribute", req.RoleId)
		.P("iValue", req.DeductDiamondAmount)
		.P("vDesc", StripName(contentss.str().c_str(), contentss.str().size(), "."))
		.P("vSerial", req.Serial)
		.P("iSource", req.Source)
		.P("iCmdID", req_head.Cmdid)
		.P("AreaID", GetAreaID(pinfo->account))
		.P("PlatID", GetAccountPlat(pinfo->account));

		if (!pinfo->SendProtocol2GS(rpc))
		{
			onFailed(IDIP_ERR_INVALID_ID);
			return HANDLE_RET_FINISH;
		}

		return HANDLE_RET_PENDING;
	}
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

//查询角色结婚状态
struct idip_query_role_marriage: public idip_req_body<IDIPCmdReqQueryRoleMarriage>
{
	idip_query_role_marriage() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_query_role_marriage *p_req, const std::vector<int64_t>& info_list);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};

void idip_query_role_marriage::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_query_role_marriage::SendResponse").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).P("info_list_size", info_list.size()).PS(req_account);
	if (info_list.empty())
	{
		return;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspQueryRoleMarriage> rsp;
	RoleInfo *p_role = RoleMap::Instance().Find(info_list[0]);
	if (!p_role || p_role->account != req_account)
	{
		rsp.body.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		rsp.body.Result = 0;
		rsp.body.SpouseRoleId = p_role->friends.GetSpouse();
		rsp.body.MarritalStatus = p_role->friends.GetMaritalStatus();
	}
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_query_role_by_roleid::SendResponse  Json_Data2Str failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

int idip_query_role_marriage::Serve()
{
	SLOG(INFO, "idip_query_role_marriage::Serve").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid);
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	std::vector<int64_t> role_list;
	role_list.push_back(req.RoleId);
	if (p_role)
	{
		SendResponse(0, role_list);
		return HANDLE_RET_FINISH;
	}
	GetRoleList(role_list);
	return HANDLE_RET_PENDING;
}

void idip_query_role_marriage::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_query_role_marriage::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_query_role_marriage::CallBack_RoleList(idip_query_role_marriage *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}

//(IDIP拍脸)新增
struct idip_pop_face_add: public idip_req_body<IDIPCmdReqPopFaceAdd>
{
	idip_pop_face_add() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_pop_face_add::Serve()
{
	req.HrefUrl = HttpProtocol::UrlDecode(req.HrefUrl);
	req.PictureUrl = HttpProtocol::UrlDecode(req.PictureUrl);

	// 调用PopFaceManager的OnAdd方法
	bool result = PopFaceManager::GetInstance().OnAdd(req);

	int retcode = result ? 0 : IDIP_ERR_PARAM;
	std::string retmsg = result ? "success" : "failed";

	// 构造响应数据
	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = retcode;

	IDIPDataPacket<IDIPCmdRsp> rsp;
	rsp.head = req_head;
	rsp.body.Result = retcode;
	rsp.body.RetMsg = retmsg;

	// 序列化响应
	std::string json_result;
	std::string err_msg = Json_Data2Str(json_result, rsp);

	if (err_msg.size() > 0)
	{
		retcode = IDIP_ERR_PARAM;
		retmsg = "json parse error";
	}
	else
	{
		Send(json_result);
	}

	// 统一的日志输出
	SLOG(FORMAT, "idip_pop_face_add")
	.P("NoticeId", req.NoticeId)
	.P("PopFaceTitle", req.PopFaceTitle)
	.P("PictureUrl", req.PictureUrl)
	.P("StartTime", req.StartTime)
	.P("EndTime", req.EndTime)
	.P("Partition", req.Partition)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid)
	.P("result", retcode)
	.P("retmsg", retmsg)
	.P("json_error", err_msg);

	return HANDLE_RET_FINISH;
}

//(IDIP拍脸)查询
struct idip_pop_face_query: public idip_req_body<IDIPCmdReqPopFaceQuery>
{
	idip_pop_face_query() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_pop_face_query::Serve()
{
	// 构造响应数据
	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = 0;

	IDIPDataPacket<IDIPCmdRspPopFaceQuery> rsp;
	rsp.head = req_head;

	// 调用PopFaceManager的OnQuery方法获取查询结果
	bool query_result = PopFaceManager::GetInstance().OnQuery(req, rsp.body);

	int final_result = 0;
	std::string final_retmsg = "success";
	std::string json_error = "";

	if (!query_result)
	{
		final_result = IDIP_ERR_PARAM;
		final_retmsg = "query failed";
		// 设置错误响应
		rsp.body.Result = final_result;
		rsp.body.RetMsg = final_retmsg;
		rsp.body.PopFaceList_count = 0;
		rsp.body.PopFaceList.clear();
		rsp.body.TotalPageNo = 0;
	}
	else
	{
		for (int i = 0; i < rsp.body.PopFaceList.size(); ++i)
		{
			rsp.body.PopFaceList[i].HrefUrl = UrlEncode2(rsp.body.PopFaceList[i].HrefUrl);
			rsp.body.PopFaceList[i].PictureUrl = UrlEncode2(rsp.body.PopFaceList[i].PictureUrl);
		}

		final_result = rsp.body.Result;
		final_retmsg = rsp.body.RetMsg;
	}

	// 统一进行JSON序列化
	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);

	if (err_msg.size() > 0)
	{
		final_result = IDIP_ERR_PARAM;
		final_retmsg = "json parse error";
		json_error = err_msg;
	}
	else
	{
		Send(result);
	}

	// 统一的日志输出
	SLOG(FORMAT, "idip_pop_face_query")
	.P("AreaId", req.AreaId)
	.P("PlatId", req.PlatId)
	.P("Partition", req.Partition)
	.P("StartTime", req.StartTime)
	.P("EndTime", req.EndTime)
	.P("PageNo", req.PageNo)
	.P("iCmdID", req_head.Cmdid)
	.P("result", final_result)
	.P("retmsg", final_retmsg)
	.P("total_records", rsp.body.PopFaceList_count)
	.P("total_pages", rsp.body.TotalPageNo)
	.P("json_error", err_msg);

	return HANDLE_RET_FINISH;
}

//(IDIP拍脸)删除
struct idip_pop_face_delete: public idip_req_body<IDIPCmdReqPopFaceDelete>
{
	idip_pop_face_delete() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
};

int idip_pop_face_delete::Serve()
{
	// 调用PopFaceManager的OnDelete方法
	bool result = PopFaceManager::GetInstance().OnDelete(req);

	int retcode = result ? 0 : IDIP_ERR_PARAM;
	std::string retmsg = result ? "success" : "failed";

	// 构造响应数据
	req_head.Cmdid = req_head.Cmdid + 1;
	req_head.Result = retcode;

	IDIPDataPacket<IDIPCmdRsp> rsp;
	rsp.head = req_head;
	rsp.body.Result = retcode;
	rsp.body.RetMsg = retmsg;

	// 序列化响应
	std::string json_result;
	std::string err_msg = Json_Data2Str(json_result, rsp);

	if (err_msg.size() > 0)
	{
		retcode = IDIP_ERR_PARAM;
		retmsg = "json parse error";
	}
	else
	{
		Send(json_result);
	}

	// 统一的日志输出
	SLOG(FORMAT, "idip_pop_face_delete")
	.P("AreaId", req.AreaId)
	.P("PlatId", req.PlatId)
	.P("Partition", req.Partition)
	.P("NoticeId", req.NoticeId)
	.P("vSerial", req.Serial)
	.P("iSource", req.Source)
	.P("iCmdID", req_head.Cmdid)
	.P("result", retcode)
	.P("retmsg", retmsg)
	.P("json_error", err_msg);

	return HANDLE_RET_FINISH;
}

//(角色交易)获取用户角色列表
struct idip_role_trade_query_role_list: public idip_req_body<IDIPCmdReqRoleTradeRoleList>
{
	bool _get_one = false;
	PB::ROLE_TRADE_REQ_TYPE _op_type = PB::RTRT_REQ_BRIEF_INFO;
	int _is_wait_delete = 0;
	idip_role_trade_query_role_list(bool get_one, PB::ROLE_TRADE_REQ_TYPE op_type) : _get_one(get_one), _op_type(op_type) {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	bool GetRoleGSInfo(const std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_role_trade_query_role_list *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_role_trade_query_role_list *p_req, GetUserArg *arg, GetUserRes *res);
	static void CallBack_Get_GS_Info(idip_role_trade_query_role_list *p_req, int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map);

	void FillCheckData(RoleInfo *p_role, PB::ipt_role_trade_check *p_pb, RoleTradeCheckRet& cr);
	void FillBriefInfoData(RoleInfo *p_role, PB::ipt_role_trade_check *p_pb, RoleTradeBriefInfo& bf);
	void FillDetailInfoData(RoleInfo *p_role, PB::ipt_role_trade_check *p_pb, std::string& result);
	virtual void SendResponse(int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map);
	virtual void SendResponseOne(int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map);
	virtual void SendResponseAll(int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map);
};

void idip_role_trade_query_role_list::FillCheckData(RoleInfo *p_role, PB::ipt_role_trade_check *p_pb, RoleTradeCheckRet& cr)
{
	if (!p_role || !p_pb)
	{
		return;
	}
	p_role->CheckTrade(*p_pb);
	cr.isTradable = true;
	if (!p_pb->cw_list().empty())
	{
		cr.isTradable = false;
		for (int i = 0; i < p_pb->cw_list().size(); i++)
		{
			cr.cwList.push_back(p_pb->cw_list(i));
		}
	}
	if (_is_wait_delete)
	{
		cr.isTradable = false;
		cr.cwList.push_back(IDIP_ERR_ROLE_TRADE_USER_WAIT_DETELE);
	}
}

void idip_role_trade_query_role_list::FillBriefInfoData(RoleInfo *p_role, PB::ipt_role_trade_check *p_pb, RoleTradeBriefInfo& bf)
{
	if (!p_role || !p_pb)
	{
		return;
	}

	p_role->CheckTrade(*p_pb);
	bf.isTradable = true;
	if (!p_pb->cw_list().empty())
	{
		bf.isTradable = false;
		for (int i = 0; i < p_pb->cw_list().size(); i++)
		{
			bf.cwList.push_back(p_pb->cw_list(i));
		}
	}
	if (_is_wait_delete)
	{
		bf.isTradable = false;
		bf.cwList.push_back(IDIP_ERR_ROLE_TRADE_USER_WAIT_DETELE);
	}
	if (p_pb->has_json_data() && !p_pb->json_data().empty())
	{
		Marshal::OctetsStream os;
		os.push_byte(p_pb->json_data().c_str(), p_pb->json_data().size());
		bf.gameSummary.unmarshal(os);
	}
	p_role->TradeGetBriefInfo(bf);
}

void idip_role_trade_query_role_list::FillDetailInfoData(RoleInfo *p_role, PB::ipt_role_trade_check *p_pb, std::string& result)
{
	if (!p_role || !p_pb)
	{
		return;
	}
	//第一part合并ds缓存的需要存库的信息
	if (req.PartId == 1)
	{
		p_pb->mutable_detail_info()->MergeFrom(p_role->complicated_data.role_trade_detail_cache());
	}
	pbjson::pb2json(p_pb->mutable_detail_info(), result); //pb序列化成json串
}

void idip_role_trade_query_role_list::SendResponseOne(int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map)
{
	if (retcode || !p_pb_map)
	{
		return;
	}
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_query_role_list::SendResponseOne").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).PS(req_account).P("pb_map_size", p_pb_map->size()).P("op_type", (int)_op_type);

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeQueryBriefInfo> rsp;
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	auto pb_iter = p_pb_map->find(req.RoleId);
	if (!p_role || p_role->account != req_account || pb_iter == p_pb_map->end())
	{
		rsp.body.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		std::string result = "";
		std::string err_msg = "";
		switch (_op_type)
		{
		case (PB::RTRT_REQ_BRIEF_INFO):
		{
			RoleTradeBriefInfo bf;
			FillBriefInfoData(p_role, &(pb_iter->second), bf);
			err_msg = Json_Data2Str(result, bf);
		}
		break;
		case (PB::RTRT_REQ_DETAIL_INFO):
		{
			FillDetailInfoData(p_role, &(pb_iter->second), result);
			if (result.empty())
			{
				SLOG(WARNING, "idip_role_trade_query_role_list::SendResponseOne Json_Data2Str failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
				return;
			}
			result = "{\"gameDetail\":" + result + "}";
			if (GDeliveryServer::GetInstance()->IsDebugging())
			{
				//debug模式下, 输出到文件
				std::string tmp_file = "role_trade_detail_json_" + std::to_string(pb_iter->second.part()) + ".json";
				ofstream file(tmp_file);   // 创建一个文件输出流
				if (file.is_open())
				{
					file << result;    // 将字符串写入文件中
					file.close();   // 关闭文件输出流
				}
			}
			SLOG(INFO, "idip_role_trade_query_role_list::SendResponseOne detail json result").P("openid", req.OpenId).P("roleid", req.RoleId).P("r_size", result.size());
			//详细信息需要zlib压缩
			Bytef buf_dst[result.size()];
			uLongf len_dst = result.size();
			if (Z_OK != compress(buf_dst, &len_dst, (const Bytef *)result.c_str(), result.size()))
			{
				SLOG(WARNING, "idip_role_trade_query_role_list::SendResponseOne zlib compress failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid);
				return;
			}
			result = std::string((char *)buf_dst, len_dst);
		}
		break;
		case (PB::RTRT_REQ_CHECK_SELL):
		{
			RoleTradeCheckRet cr;
			FillCheckData(p_role, &(pb_iter->second), cr);
			err_msg = Json_Data2Str(result, cr);
		}
		break;
		default:
			break;
		}
		if (err_msg.size() > 0 || result.empty())
		{
			SLOG(WARNING, "idip_role_trade_query_role_list::SendResponseOne Json_Data2Str failed 1").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
			return;
		}
		std::string str_base64(result.size() * 2, '\0');
		size_t base_64_len = base64_encode((unsigned char *)result.c_str(), result.size(), (char *)str_base64.c_str());
		rsp.body.Result = 0;
		rsp.body.Data = UrlEncode2(std::string(str_base64.c_str(), base_64_len));

		if (GDeliveryServer::GetInstance()->IsDebugging())
		{
			//debug模式下, 存一下
			std::string tmp_file = "detail_encode_ret.json";
			ofstream file(tmp_file);   // 创建一个文件输出流
			if (file.is_open())
			{
				file << rsp.body.Data;    // 将字符串写入文件中
				file.close();   // 关闭文件输出流
			}
		}
	}
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_query_role_list::SendResponseOne  Json_Data2Str failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

void idip_role_trade_query_role_list::SendResponseAll(int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map)
{
	if (retcode || !p_pb_map)
	{
		return;
	}
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_query_role_list::SendResponseAll").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).PS(req_account).P("pb_map_size", p_pb_map->size());

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeRoleList> rsp;
	do
	{
		if (retcode || !p_pb_map)
		{
			rsp.body.Result = IDIP_ERR_INVALID_ROLE;
			break;
		}
		RoleTradeRoleList role_list_data;
		for (auto& kv : *p_pb_map)
		{
			RoleInfo *p_role = RoleMap::Instance().Find(kv.first);
			if (!p_role || p_role->account != req_account)
			{
				rsp.body.Result = IDIP_ERR_INVALID_ROLE;
				break;
			}
			else
			{
				RoleTradeBriefInfo bf;
				FillBriefInfoData(p_role, &kv.second, bf);
				role_list_data.roleList.push_back(std::move(bf));
			}
			if (rsp.body.Result != 0)
			{
				break;
			}
		}
		std::string result = "";
		std::string err_msg = Json_Data2Str(result, role_list_data);
		if (err_msg.size() > 0 || result.empty())
		{
			SLOG(WARNING, "idip_role_trade_query_role_list::SendResponseAll Json_Data2Str failed 1").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
			return;
		}
		std::string str_base64(result.size() * 2, '\0');
		size_t base_64_len = base64_encode((unsigned char *)result.c_str(), result.size(), (char *)str_base64.c_str());
		rsp.body.Result = 0;
		rsp.body.Data = UrlEncode2(std::string(str_base64.c_str(), base_64_len));
	}
	while (false);

	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_query_role_list::SendResponseAll  Json_Data2Str failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;

}

void idip_role_trade_query_role_list::SendResponse(int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map)
{
	if (retcode || !p_pb_map)
	{
		req_head.Cmdid = req_head.Cmdid + 1;
		IDIPDataPacket<IDIPCmdRspRoleTradeRoleList> rsp;
		rsp.body.Result = retcode ? retcode : IDIP_ERR_INVALID;
		rsp.body.RetMsg = "failed";
		std::string result;
		std::string err_msg = Json_Data2Str(result, rsp);
		if (err_msg.size() > 0)
		{
			SLOG(WARNING, "idip_role_trade_query_role_list::SendResponse Json_Data2Str failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
			return;
		}
		Send(result);
		return;
	}
	if (_get_one)
	{
		SendResponseOne(retcode, p_pb_map);
	}
	else
	{
		SendResponseAll(retcode, p_pb_map);
	}
}

int idip_role_trade_query_role_list::Serve()
{
	SLOG(INFO, "idip_role_trade_query_role_list::Serve").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).P("get_one", _get_one);
	if (_get_one && !req.RoleId)
	{
		return HANDLE_RET_FINISH;
	}
	Octets user_account = IDIP_ACCOUNT(req);
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL || _op_type == PB::RTRT_REQ_CHECK_SELL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));
		rpc->call_back = std::bind(&idip_role_trade_query_role_list::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		std::vector<int64_t> role_list = user->GetRoleList();
		if (_get_one)
		{
			role_list.clear();
			role_list.push_back(req.RoleId);
		}
		GetRoleList(role_list);
		return HANDLE_RET_PENDING;
	}
}
void idip_role_trade_query_role_list::CallBack_GetUser(idip_role_trade_query_role_list *p_req, GetUserArg *arg, GetUserRes *res)
{
	SLOG(INFO, "idip_role_trade_query_role_list::CallBack_GetUser").P("account", arg->account).P("retcode", res->retcode);
	if (res->retcode != 0)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE, NULL);
		delete p_req;
	}
	else
	{
		p_req->_is_wait_delete = res->is_wait_delete;
		std::vector<int64_t> role_list;
		if (p_req->_get_one)
		{
			role_list.push_back(p_req->req.RoleId);
		}
		else
		{
			User& u = res->value;
			for (size_t i = 0; i < u.rolelist.size(); ++i)
			{
				role_list.push_back(u.rolelist[i]);
			}
		}
		p_req->GetRoleList(role_list);
	}
}

void idip_role_trade_query_role_list::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_role_trade_query_role_list::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_role_trade_query_role_list::CallBack_RoleList(idip_role_trade_query_role_list *p_req, const std::vector<int64_t>& info_list)
{
	auto account = p_req->Openid2Account(Octets(p_req->req.OpenId.data(), p_req->req.OpenId.size()), p_req->req.AreaId);
	SLOG(INFO, "idip_role_trade_query_role_list::CallBack_RoleList").PS(account).P("get_one", p_req->_get_one);
	if (!p_req->GetRoleGSInfo(info_list))
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE, NULL);
		delete p_req;
	}
}

bool idip_role_trade_query_role_list::GetRoleGSInfo(const std::vector<int64_t>& info_list)
{
	if (info_list.empty())
	{
		return false;
	}
	auto account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_query_role_list::GetRoleGSInfo").PS(account).P("roleids", info_list).P("get_one", _get_one);
	//存回调
	int serial_id = RoleTradeManager::GetInstance().AllocSerialId();
	RoleTradeManager::GetInstance().Insert(serial_id, info_list, std::bind(&idip_role_trade_query_role_list::CallBack_Get_GS_Info, this, std::placeholders::_1, std::placeholders::_2));
	for (auto roleid : info_list)
	{
		//去gs检查
		PB::ipt_role_trade_check pb;
		pb.set_op_type(_op_type);
		pb.set_serial_id(serial_id);
		pb.set_roleid(roleid);
		pb.set_seller_account(account.tostr());
		pb.set_part(req.PartId);
		GProviderServer::GetInstance()->RandomDispatchProtocol(pb);
	}
	return true;
}

//gs协议回调回来
void idip_role_trade_query_role_list::CallBack_Get_GS_Info(idip_role_trade_query_role_list *p_req, int retcode, std::map<int64_t, PB::ipt_role_trade_check> *p_pb_map)
{
	if (retcode || !p_pb_map)
	{
		p_req->SendResponse(IDIP_ERR_INVALID, NULL);
	}
	else
	{
		p_req->SendResponse(0, p_pb_map);
	}
	delete p_req;
}

// (角色交易)获取用户角色标签信息
struct idip_role_trade_query_role_tag: public idip_req_body<IDIPCmdReqRoleTradeQueryRoleTag>
{
	idip_role_trade_query_role_tag() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_role_trade_query_role_tag *p_req, const std::vector<int64_t>& info_list);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};

void idip_role_trade_query_role_tag::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_query_role_tag::SendResponse").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).P("info_list_size", info_list.size()).PS(req_account);
	if (info_list.empty())
	{
		return;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeQueryRoleTag> rsp;
	RoleInfo *p_role = RoleMap::Instance().Find(info_list[0]);
	if (!p_role || p_role->account != req_account)
	{
		rsp.body.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		rsp.body.Result = 0;
		std::vector<RoleTradeTagInfo> t_infos;
		p_role->TradeGetTagInfo(t_infos);
		std::stringstream ss;
		ss << "{\"gameTags\":{\"allTags\":[";
		for (int i = 0; i < t_infos.size(); i++)
		{
			std::string tmp;
			Json_Data2Str(tmp, t_infos[i]);
			if (!tmp.size())
			{
				return;
			}
			ss << tmp;
			if (i != t_infos.size() - 1)
			{
				ss << ",";
			}
		}
		ss << "]}}";
		std::string result = ss.str();
		if (result.empty())
		{
			SLOG(WARNING, "idip_role_trade_query_role_tag::SendResponseAll Json_Data2Str failed 1").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid);
			return;
		}
		std::string str_base64(result.size() * 2, '\0');
		size_t base_64_len = base64_encode((unsigned char *)result.c_str(), result.size(), (char *)str_base64.c_str());
		rsp.body.Result = 0;
		rsp.body.Data = UrlEncode2(std::string(str_base64.c_str(), base_64_len));

	}
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_query_role_tag::SendResponse  Json_Data2Str failed 2").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_query_role_tag::Serve()
{
	SLOG(INFO, "idip_role_trade_query_role_tag::Serve").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid);
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	std::vector<int64_t> role_list;
	role_list.push_back(req.RoleId);
	if (p_role)
	{
		SendResponse(0, role_list);
		return HANDLE_RET_FINISH;
	}
	GetRoleList(role_list);
	return HANDLE_RET_PENDING;
}

void idip_role_trade_query_role_tag::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_role_trade_query_role_tag::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_role_trade_query_role_tag::CallBack_RoleList(idip_role_trade_query_role_tag *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}


//(角色交易)获取角色筛选字段
struct idip_role_trade_query_filter: public idip_req_body<IDIPCmdReqRoleTradeQueryFilter>
{
	idip_role_trade_query_filter() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_role_trade_query_filter *p_req, const std::vector<int64_t>& info_list);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};

void idip_role_trade_query_filter::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_query_filter::SendResponse").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).P("info_list_size", info_list.size()).PS(req_account);
	if (info_list.empty())
	{
		return;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeQueryFilter> rsp;

	RoleInfo *p_role = RoleMap::Instance().Find(info_list[0]);
	if (!p_role || p_role->account != req_account)
	{
		rsp.body.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		rsp.body.Result = 0;
		RoleTradeRoleFilter rf;
		p_role->TradeGetFilterInfo(rf);
		std::string result = "";
		std::string err_msg = Json_Data2Str(result, rf);
		if (err_msg.size() > 0 || result.empty())
		{
			SLOG(WARNING, "idip_role_trade_query_filter::SendResponseAll Json_Data2Str failed 1").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
			return;
		}
		std::string str_base64(result.size() * 2, '\0');
		size_t base_64_len = base64_encode((unsigned char *)result.c_str(), result.size(), (char *)str_base64.c_str());
		rsp.body.Result = 0;
		rsp.body.Data = UrlEncode2(std::string(str_base64.c_str(), base_64_len));
	}
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_query_filter::SendResponse  Json_Data2Str failed 2").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_query_filter::Serve()
{
	SLOG(INFO, "idip_role_trade_query_filter::Serve").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid);
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	std::vector<int64_t> role_list;
	role_list.push_back(req.RoleId);
	if (p_role)
	{
		SendResponse(0, role_list);
		return HANDLE_RET_FINISH;
	}
	GetRoleList(role_list);
	return HANDLE_RET_PENDING;
}

void idip_role_trade_query_filter::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_role_trade_query_filter::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_role_trade_query_filter::CallBack_RoleList(idip_role_trade_query_filter *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}

// (角色交易)修改角色交易状态
struct idip_role_trade_set_status: public idip_req_body<IDIPCmdReqRoleTradeSetStatus>
{
	idip_role_trade_set_status() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBack_DBSetState(idip_role_trade_set_status *p_req, int retcode);

	virtual void SendResponse(int retcode);
};

void idip_role_trade_set_status::SendResponse(int retcode)
{
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_set_status::SendResponse").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).PS(req_account);
	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeSetStatus> rsp;
	rsp.body.Result = retcode;
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_set_status::SendResponse  Json_Data2Str failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_set_status::Serve()
{
	SLOG(INFO, "idip_role_trade_set_status::Serve").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).P("next_status", req.NextStatus);
	DBRoleTrade_SetStateArg arg;
	arg.account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	arg.roleid = req.RoleId;
	arg.next_status = req.NextStatus;
	DBRoleTrade_SetState *rpc = (DBRoleTrade_SetState *) Rpc::Call(RPC_DBROLETRADE_SETSTATE, arg);
	rpc->_call_back = std::bind(&CallBack_DBSetState, this, std::placeholders::_1);
	GameDBClient::GetInstance()->SendProtocol(rpc);
	return HANDLE_RET_PENDING;
}

void idip_role_trade_set_status::CallBack_DBSetState(idip_role_trade_set_status *p_req, int retcode)
{
	if (!p_req)
	{
		return;
	}
	Octets account = p_req->Openid2Account(Octets(p_req->req.OpenId.data(), p_req->req.OpenId.size()), p_req->req.AreaId);
	SLOG(INFO, "idip_role_trade_set_status::CallBack_DBSetState").PS(account).P("roleid", p_req->req.RoleId).P("status", (int)p_req->req.NextStatus).PS(retcode);
	if (!retcode)
	{
		if (p_req->req.NextStatus == PB::RTS_PUBLIC_NOTICE || p_req->req.NextStatus == PB::RTS_ON_SHELF)
		{
			//公示期, 上架期 踢下线, 清缓存
			UserInfo *user = UserContainer::Instance().FindUser(account);
			if (user != NULL && user->roleid != 0 && user->roleid == p_req->req.RoleId)
			{
				UserContainer::Instance().UserLogoutComplete(user, ERROR_BANNED_ACCOUNT, Octets());
			}
		}
		//交易完成给买家补一个改名道具邮件
		else if (p_req->req.NextStatus == PB::RTS_TRADE_SUCCESS)
		{
			DBMailSendArg arg;
			uint64_t to_roleid = p_req->req.RoleId;
			int item_id = 15116;
			int item_count = 1;
			std::string utf8_title = "角色交易成功";
			std::string utf8_content = "亲爱的玩家：\n感谢您使用角色交易功能，附件是改名申请卡，祝您游戏愉快！";
			Octets ucs2_title, ucs2_content;
			CharsetConverter::conv_charset_t2u(Octets(utf8_title.c_str(), utf8_title.size()), ucs2_title);
			CharsetConverter::conv_charset_t2u(Octets(utf8_content.c_str(), utf8_content.size()), ucs2_content);

			arg.dst.id = to_roleid;
			arg.mail.header.category = CATEGORY_MAIL_SYSTEM | MAIL_CATEGORY_IDIPGIFT;
			arg.mail.header.subject = ucs2_title;
			arg.mail.header.from = ((int64_t)item_id << 32) | (item_count); //为了方便客户端，在header的from字段里填入物品id和数量
			arg.mail.header.to = to_roleid;
			arg.mail.header.date = Timer::GetTime();
			arg.mail.header.status = MAIL_STATUS_ATTACHED;
			arg.mail.header.msgid = 0;
			arg.mail.context = ucs2_content;
			GRoleInventory item;
			item.id = item_id;
			item.count = item_count;
			arg.mail.attachment.items.push_back(item);

			DBMailSend *rpc = (DBMailSend *) Rpc::Call(RPC_DBMAILSEND, arg);
			rpc->linksid = 0;
			GameDBClient::GetInstance()->SendProtocol(rpc);
		}
	}

	p_req->SendResponse(retcode);
	delete p_req;
}

// (角色交易)角色转移
struct idip_role_trade_transfer: public idip_req_body<IDIPCmdReqRoleTradeTransfer>
{
	bool _is_roll_back = false;
	idip_role_trade_transfer(bool is_role_back) : _is_roll_back(is_role_back) {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBack_DBTransfer(idip_role_trade_transfer *p_req, int retcode);
	static void CallBack_ForbidRole(idip_role_trade_transfer *p_req, DBPutForbidArg *arg, DBPutForbidRes *res);

	virtual void SendResponse(int retcode);
};

void idip_role_trade_transfer::SendResponse(int retcode)
{
	SLOG(INFO, "idip_role_trade_transfer::SendResponse").P("iCmdID", req_head.Cmdid).P("AreaId", req.AreaId).P("PlatId", req.PlatId).P("Partition", req.Partition).P("SellerOpenId", req.SellerOpenId).P("BuyerOpenId", req.BuyerOpenId).P("roleid", req.RoleId).PS(retcode).PS(_is_roll_back);

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeTransfer> rsp;
	rsp.body.Result = retcode;
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_transfer::SendResponse Json_Data2Str failed").P("iCmdID", req_head.Cmdid).P("AreaId", req.AreaId).P("PlatId", req.PlatId).P("Partition", req.Partition).P("SellerOpenId", req.SellerOpenId).P("BuyerOpenId", req.BuyerOpenId).P("roleid", req.RoleId).PS(_is_roll_back);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_transfer::Serve()
{
	auto account_seller = Openid2Account(Octets(req.SellerOpenId.data(), req.SellerOpenId.size()), req.AreaId);
	auto account_buyer = Openid2Account(Octets(req.BuyerOpenId.data(), req.BuyerOpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_transfer::Serve").P("iCmdID", req_head.Cmdid).P("AreaId", req.AreaId).P("PlatId", req.PlatId).P("Partition", req.Partition).PS(account_seller).PS(account_buyer).P("roleid", req.RoleId).PS(_is_roll_back);
	//先将角色封禁30s
	std::string ban_reason = "角色正在交易流程中, 请稍后重试";
	Octets utf8_des(ban_reason.c_str(), ban_reason.size());
	Octets ucs2_des;
	CharsetConverter::conv_charset_t2u(utf8_des, ucs2_des);
	GRoleForbid forbid(GNET_FORBID_LOGIN, 30, Timer::GetTime(), ucs2_des);
	DBPutForbidArg arg(req.RoleId, forbid);
	DBPutForbid *rpc = (DBPutForbid *)Rpc::Call(RPC_DBPUTFORBID, arg);
	rpc->call_back = std::bind(&idip_role_trade_transfer::CallBack_ForbidRole, this, std::placeholders::_1, std::placeholders::_2);
	GameDBClient::GetInstance()->SendProtocol(rpc);
	return HANDLE_RET_PENDING;
}

void idip_role_trade_transfer::CallBack_DBTransfer(idip_role_trade_transfer *p_req, int retcode)
{
	SLOG(INFO, "idip_role_trade_transfer::CallBack_DBTransfer").P("iCmdID", p_req->req_head.Cmdid).P("AreaId", p_req->req.AreaId).P("PlatId", p_req->req.PlatId).P("Partition", p_req->req.Partition).P("roleid", p_req->req.RoleId).PS(retcode).P("is_roll_back", p_req->_is_roll_back);
	p_req->SendResponse(retcode);
	delete p_req;
}

void idip_role_trade_transfer::CallBack_ForbidRole(idip_role_trade_transfer *p_req, DBPutForbidArg *arg, DBPutForbidRes *res)
{
	SLOG(INFO, "idip_role_trade_transfer::CallBack_ForbidRole").P("iCmdID", p_req->req_head.Cmdid).P("AreaId", p_req->req.AreaId).P("PlatId", p_req->req.PlatId).P("Partition", p_req->req.Partition).P("roleid", p_req->req.RoleId).P("retcode", res->retcode).P("is_roll_back", p_req->_is_roll_back);
	if (res->retcode == ERROR_FORBID_IGNORE)
	{
		res->retcode = 0;
	}
	if (res->retcode != 0)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE);
		delete p_req;
		return;
	}
	//如果role还有缓存，设置下needgetrole
	RoleInfo *p_role = RoleMap::Instance().Find(p_req->req.RoleId);
	if (p_role)
	{
		p_role->SetNeedGetRole();
	}
	auto account_seller = p_req->Openid2Account(Octets(p_req->req.SellerOpenId.data(), p_req->req.SellerOpenId.size()), p_req->req.AreaId);
	auto account_buyer = p_req->Openid2Account(Octets(p_req->req.BuyerOpenId.data(), p_req->req.BuyerOpenId.size()), p_req->req.AreaId);
	//双方先踢下线, 2s之后再transfer
	UserInfo *user_seller = UserContainer::Instance().FindUser(account_seller);
	if (user_seller)
	{
		UserContainer::Instance().UserLogoutComplete(user_seller, ERROR_ROLE_TRADE_TRANSFERING, Octets());
	}
	UserInfo *user_buyer = UserContainer::Instance().FindUser(account_buyer);
	if (user_buyer)
	{
		UserContainer::Instance().UserLogoutComplete(user_buyer, ERROR_ROLE_TRADE_TRANSFERING, Octets());
	}

	DBRoleTrade_TransferArg t_arg;
	t_arg.account_seller = account_seller;
	t_arg.account_buyer = account_buyer;
	t_arg.roleid = p_req->req.RoleId;
	t_arg.is_rollback = p_req->_is_roll_back;
	DBRoleTrade_Transfer *rpc = (DBRoleTrade_Transfer *) Rpc::Call(RPC_DBROLETRADE_TRANSFER, t_arg);
	rpc->_call_back = std::bind(&idip_role_trade_transfer::CallBack_DBTransfer, p_req, std::placeholders::_1);
	GameDBClient::GetInstance()->SendProtocol(rpc);
}

// (角色交易)角色转移回滚
struct idip_role_trade_transfer_rollback: public idip_req_body<IDIPCmdReqRoleTradeTransferRollBack>
{
	idip_role_trade_transfer_rollback() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	static void CallBack_DBTransfer(idip_role_trade_transfer_rollback *p_req, int retcode);

	virtual void SendResponse(int retcode);
};

void idip_role_trade_transfer_rollback::SendResponse(int retcode)
{
	SLOG(INFO, "idip_role_trade_transfer_rollback::SendResponse").P("iCmdID", req_head.Cmdid).P("AreaId", req.AreaId).P("PlatId", req.PlatId).P("Partition", req.Partition).P("SellerOpenId", req.SellerOpenId).P("BuyerOpenId", req.BuyerOpenId).P("roleid", req.RoleId).PS(retcode);

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeTransferRollBack> rsp;
	rsp.body.Result = retcode;
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_transfer_rollback::SendResponse Json_Data2Str failed").P("iCmdID", req_head.Cmdid).P("AreaId", req.AreaId).P("PlatId", req.PlatId).P("Partition", req.Partition).P("SellerOpenId", req.SellerOpenId).P("BuyerOpenId", req.BuyerOpenId).P("roleid", req.RoleId);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_transfer_rollback::Serve()
{
	SLOG(INFO, "idip_role_trade_transfer_rollback::Serve").P("iCmdID", req_head.Cmdid).P("AreaId", req.AreaId).P("PlatId", req.PlatId).P("Partition", req.Partition).P("SellerOpenId", req.SellerOpenId).P("BuyerOpenId", req.BuyerOpenId).P("roleid", req.RoleId);
	DBRoleTrade_TransferArg arg;
	arg.account_seller = Openid2Account(Octets(req.SellerOpenId.data(), req.SellerOpenId.size()), req.AreaId);
	arg.account_buyer = Openid2Account(Octets(req.BuyerOpenId.data(), req.BuyerOpenId.size()), req.AreaId);
	arg.roleid = req.RoleId;
	arg.is_rollback = true;
	DBRoleTrade_Transfer *rpc = (DBRoleTrade_Transfer *) Rpc::Call(RPC_DBROLETRADE_TRANSFER, arg);
	rpc->_call_back = std::bind(&CallBack_DBTransfer, this, std::placeholders::_1);
	GameDBClient::GetInstance()->SendProtocol(rpc);
	return HANDLE_RET_PENDING;
}

void idip_role_trade_transfer_rollback::CallBack_DBTransfer(idip_role_trade_transfer_rollback *p_req, int retcode)
{
	p_req->SendResponse(retcode);
	delete p_req;
}

// (角色交易)查询角色交易状态
struct idip_role_trade_get_status: public idip_req_body<IDIPCmdReqRoleTradeGetStatus>
{
	idip_role_trade_get_status() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_role_trade_get_status *p_req, const std::vector<int64_t>& info_list);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list);
};

void idip_role_trade_get_status::SendResponse(int retcode, const std::vector<int64_t>& info_list)
{
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_get_status::SendResponse").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).P("info_list_size", info_list.size()).PS(req_account);
	if (info_list.empty())
	{
		return;
	}

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeGetStatus> rsp;
	RoleInfo *p_role = RoleMap::Instance().Find(info_list[0]);
	if (!p_role || p_role->account != req_account)
	{
		rsp.body.Result = IDIP_ERR_INVALID_ROLE;
	}
	else
	{
		rsp.body.Result = 0;
		rsp.body.Status = p_role->trade_status;
	}
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_get_status::SendResponse  Json_Data2Str failed").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_get_status::Serve()
{
	SLOG(INFO, "idip_role_trade_get_status::Serve").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid);
	RoleInfo *p_role = RoleMap::Instance().Find(req.RoleId);
	std::vector<int64_t> role_list;
	role_list.push_back(req.RoleId);
	if (p_role)
	{
		SendResponse(0, role_list);
		return HANDLE_RET_FINISH;
	}
	GetRoleList(role_list);
	return HANDLE_RET_PENDING;
}

void idip_role_trade_get_status::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_role_trade_get_status::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_role_trade_get_status::CallBack_RoleList(idip_role_trade_get_status *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}

// (角色交易)查询角色是否可售出
struct idip_role_trade_check_sell: public idip_req_body<IDIPCmdReqRoleTradeCheckSell>
{
	idip_role_trade_check_sell() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_RoleList(idip_role_trade_check_sell *p_req, const std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_role_trade_check_sell *p_req, GetUserArg *arg, GetUserRes *res);

	virtual void SendResponse(int retcode, const std::vector<int64_t>& info_list, bool is_wait_delete = false);
};

int idip_role_trade_check_sell::Serve()
{
	SLOG(INFO, "idip_role_trade_check_sell::Serve").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid);
	Octets user_account = IDIP_ACCOUNT(req);
	GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));
	rpc->call_back = std::bind(&idip_role_trade_check_sell::CallBack_GetUser, this,
	                           std::placeholders::_1, std::placeholders::_2);
	GameDBClient::GetInstance()->SendProtocol(rpc);
	return HANDLE_RET_PENDING;
}

void idip_role_trade_check_sell::CallBack_GetUser(idip_role_trade_check_sell *p_req, GetUserArg *arg, GetUserRes *res)
{
	SLOG(INFO, "idip_role_trade_check_sell::CallBack_GetUser").P("account", arg->account).P("retcode", res->retcode);
	std::vector<int64_t> role_list = {};
	if (res->retcode != 0)
	{
		p_req->SendResponse(IDIP_ERR_INVALID_ROLE, role_list);
		delete p_req;
	}
	else
	{
		if (res->is_wait_delete == 1)
		{
			p_req->SendResponse(IDIP_ERR_INVALID_ROLE, role_list, true);
			delete p_req;
		}
		else
		{
			role_list.push_back(p_req->req.RoleId);
			p_req->GetRoleList(role_list);
		}
	}
}

void idip_role_trade_check_sell::GetRoleList(std::vector<int64_t>& info_list)
{
	GetRoleListHelper *helper = new GetRoleListHelper(info_list);
	helper->call_back = std::bind(&idip_role_trade_check_sell::CallBack_RoleList, this, std::placeholders::_1);
	helper->Prepare();
	helper->Run();
}

void idip_role_trade_check_sell::SendResponse(int retcode, const std::vector<int64_t>& info_list, bool is_wait_delete)
{
	auto req_account = Openid2Account(Octets(req.OpenId.data(), req.OpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_check_sell::SendResponse").P("openid", req.OpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).P("info_list_size", info_list.size()).PS(req_account).PS(is_wait_delete);

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeCheckSell> rsp;
	rsp.body.Result = 0;
	RoleTradeCheckRet ret_data;
	if (retcode)
	{
		rsp.body.Result = IDIP_ERR_INVALID_ROLE;
	}
	else if (is_wait_delete)
	{
		ret_data.isTradable = false;
		ret_data.cwList.push_back(IDIP_ERR_ROLE_TRADE_USER_WAIT_DETELE);
	}
	else
	{
		if (info_list.empty())
		{
			return;
		}
		RoleInfo *p_role = RoleMap::Instance().Find(info_list[0]);
		if (!p_role || p_role->account != req_account)
		{
			rsp.body.Result = IDIP_ERR_INVALID_ROLE;
		}
		else
		{
			PB::ipt_role_trade_check check_ret;
			p_role->CheckTrade(check_ret);
			ret_data.isTradable = true;
			if (!check_ret.cw_list().empty())
			{
				ret_data.isTradable = false;
				for (int i = 0; i < check_ret.cw_list().size(); i++)
				{
					ret_data.cwList.push_back(check_ret.cw_list(i));
				}
			}
		}
	}
	if (rsp.body.Result == 0)
	{
		std::string result;
		std::string err_msg = Json_Data2Str(result, ret_data);
		if (err_msg.size() > 0 || result.empty())
		{
			SLOG(WARNING, "idip_role_trade_check_sell::SendResponse Json_Data2Str failed 1").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
			return;
		}
		std::string str_base64(result.size() * 2, '\0');
		size_t base_64_len = base64_encode((unsigned char *)result.c_str(), result.size(), (char *)str_base64.c_str());
		rsp.body.Data = UrlEncode2(std::string(str_base64.c_str(), base_64_len));

	}
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_check_sell::SendResponse  Json_Data2Str failed 2").P("openid", req.OpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

void idip_role_trade_check_sell::CallBack_RoleList(idip_role_trade_check_sell *p_req, const std::vector<int64_t>& info_list)
{
	p_req->SendResponse(0, info_list);
	delete p_req;
}

// (角色交易)查询角色是否可购买
struct idip_role_trade_check_buy: public idip_req_body<IDIPCmdReqRoleTradeCheckBuy>
{
	idip_role_trade_check_buy() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	void GetRoleList(std::vector<int64_t>& info_list);
	static void CallBack_GetUser(idip_role_trade_check_buy *p_req, GetUserArg *arg, GetUserRes *res);

	virtual void SendResponse(GetUserRes *res);
};

void idip_role_trade_check_buy::SendResponse(GetUserRes *res)
{
	auto req_account = Openid2Account(Octets(req.BuyerOpenId.data(), req.BuyerOpenId.size()), req.AreaId);
	SLOG(INFO, "idip_role_trade_check_buy::SendResponse").P("openid", req.BuyerOpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid).PS(req_account);

	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeCheckBuy> rsp;
	UserInfo *user = UserContainer::Instance().FindUser(req_account);
	int roles_size = 0;
	bool flag = true;
	if (user)
	{
		roles_size = user->GetRoleCount();
	}
	else if (res)
	{
		if (res->retcode == 0)
		{
			roles_size = res->value.rolelist.size();
		}
		else if (res->retcode != ERROR_DB_NOTFOUND)
		{
			rsp.body.Result = IDIP_ERR_INVALID_ROLE;
			flag = false;
		}
	}
	else
	{
		rsp.body.Result = IDIP_ERR_INVALID_ROLE;
		flag = false;
	}
	if (flag)
	{
		RoleTradeCheckRet ret_data;
		rsp.body.Result = 0;
		ret_data.isTradable = true;
		//只有角色数量的限制
		if (roles_size >= 4)
		{
			ret_data.isTradable = false;
			ret_data.cwList.push_back(GNET::IDIP_ERR_ROLE_TRADE_FULL_ROLE);
		}
		std::string result;
		std::string err_msg = Json_Data2Str(result, ret_data);
		if (err_msg.size() > 0 || result.empty())
		{
			SLOG(WARNING, "idip_role_trade_check_buy::SendResponse Json_Data2Str failed 1").P("openid", req.BuyerOpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
			return;
		}
		std::string str_base64(result.size() * 2, '\0');
		size_t base_64_len = base64_encode((unsigned char *)result.c_str(), result.size(), (char *)str_base64.c_str());
		rsp.body.Data = UrlEncode2(std::string(str_base64.c_str(), base_64_len));
	}
	rsp.body.RetMsg = (rsp.body.Result == 0) ? "success" : "failed";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_check_buy::SendResponse  Json_Data2Str failed 2").P("openid", req.BuyerOpenId).P("roleid", req.RoleId).P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_check_buy::Serve()
{
	SLOG(INFO, "idip_role_trade_check_buy::Serve").P("BuyerOpenId", req.BuyerOpenId).P("roleid", req.RoleId).P("iCmdID", req_head.Cmdid);
	auto user_account = Openid2Account(Octets(req.BuyerOpenId.data(), req.BuyerOpenId.size()), req.AreaId);
	UserInfo *user = UserContainer::Instance().FindUser(user_account);
	if (user == NULL)
	{
		GetUser *rpc = (GetUser *)Rpc::Call(RPC_GETUSER, GetUserArg(user_account));
		rpc->call_back = std::bind(&idip_role_trade_check_buy::CallBack_GetUser, this,
		                           std::placeholders::_1, std::placeholders::_2);
		GameDBClient::GetInstance()->SendProtocol(rpc);
		return HANDLE_RET_PENDING;
	}
	else
	{
		SendResponse(NULL);
		return HANDLE_RET_FINISH;
	}
}

void idip_role_trade_check_buy::CallBack_GetUser(idip_role_trade_check_buy *p_req, GetUserArg *arg, GetUserRes *res)
{
	p_req->SendResponse(res);
	delete p_req;
}

// (角色交易)查询服务器状态
struct idip_role_trade_check_server: public idip_req_body<IDIPCmdReqRoleTradeCheckBuy>
{
	idip_role_trade_check_server() {}
	virtual int Serve() override;
	bool NeedFilterUnMasterRequest() const override
	{
		return false;
	}
	unsigned int GetRequestPartition() const override
	{
		return req.Partition;
	}
	virtual void SendResponse();
};

void idip_role_trade_check_server::SendResponse()
{
	req_head.Cmdid = req_head.Cmdid + 1;
	IDIPDataPacket<IDIPCmdRspRoleTradeCheckServer> rsp;
	rsp.body.ServerStop = 0;
	rsp.body.Result = 0;
	rsp.body.RetMsg = "success";
	rsp.head = req_head;

	std::string result;
	std::string err_msg = Json_Data2Str(result, rsp);
	if (err_msg.size() > 0)
	{
		SLOG(WARNING, "idip_role_trade_check_server::SendResponse  Json_Data2Str failed ").P("cmdid", req_head.Cmdid).PS(err_msg);
		return;
	}
	Send(result);
	return;
}

int idip_role_trade_check_server::Serve()
{
	SendResponse();
	return HANDLE_RET_FINISH;
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////
IDIPHandler *idip_request::Create(const std::string& json_content)
{
	IDIPReqPacketCmd CmdPkt;
	std::string err_msg = Json_Str2Data(CmdPkt, json_content);
	if (err_msg.size() > 0)
	{
		return NULL;
	}

	unsigned int cmd_id = CmdPkt.head.Cmdid;

	IDIPHandler *p_body = NULL;
	switch (cmd_id)
	{
	case IDIP_DO_ACTIVITY_ETC_OPERATE_REQ:
		p_body = new zl_campaign_op();
		break;
	case IDIP_DO_SEND_ITEM_MAIL_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeIdipSendMail))
		{
			p_body = new idip_sendmail();
		}
		break;
	case IDIP_AQ_DO_SEND_MAIL_REQ:
		p_body = new idip_sendmail();
		break;
	case IDIP_DO_BAN_USR_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeIdipBanUser))
		{
			p_body = new idip_forbid();
		}
		break;
	case IDIP_DO_UNBAN_USR_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeIdipBanUser))
		{
			p_body = new idip_unforbid();
		}
		break;
	case IDIP_QUERY_USR_INFO_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeIdipQueryUserInfo))
		{
			p_body = new idip_listrole();
		}
		break;
	case IDIP_DO_SEND_ALLAREA_MAIL_REQ:
		p_body = new idip_mail_all();
		break;
	case IDIP_QUERY_ALLZONE_MAIL_REQ:
		p_body = new idip_query_mail_all();
		break;
	case IDIP_DO_DELETE_ALLZONE_MAIL_REQ:
		p_body = new idip_delete_mail_all();
		break;
	case IDIP_DO_SET_MAX_NUMBER_REQ:
		p_body = new idip_change_player_limit();
		break;
	case IDIP_AQ_QUERY_ROLE_INFO_REQ:
		p_body = new idip_query_roles_of_account(); //查询帐号下角色信息（AQ）
		break;
	case IDIP_AQ_QUERY_OPENID_INFO_REQ:	//查询openid基本信息
		p_body = new idip_aq_query_role();
		break;
	case IDIP_AQ_DO_SEND_MSG_REQ:		//发送消息
		p_body = new idip_aq_send_msg();
		break;
	case IDIP_AQ_DO_MODIFY_GOLDCOIN_REQ:	//修改游戏币数量
		p_body = new idip_aq_modify_money(1);
		break;
	/*case IDIP_AQ_DO_MODIFY_DIAMOND_REQ:	//修改钻石
		p_body = new idip_aq_modify_diamond(1);
		break;*/
	/*case IDIP_AQ_DO_INIT_ACCOUNT_REQ	:	//初始化账号
		p_body = new idip_aq_reset_data();
		break;*/
	case IDIP_AQ_DO_ZERO_PROFIT_REQ:	//零收益
		p_body = new idip_aq_zero_benefit();
		break;
	case IDIP_AQ_DO_BAN_SPECIFY_PLAY_REQ:	//禁止指定玩法
		p_body = new idip_aq_ban_game();
		break;
	/*case IDIP_AQ_DO_BAN_PLAY_ALL_REQ	:	//禁止所有玩法
		p_body = new idip_aq_ban_game_all();
		break;*/
	case IDIP_AQ_DO_BAN_USR_REQ:	//封号
		if (GET_FUNC_SWITCH(kFuncCodeIdipBanUser))
		{
			p_body = new idip_forbid();
		}
		break;
	case IDIP_DO_BAN_CHAT_REQ:	//禁言
		if (GET_FUNC_SWITCH(kFuncCodeIdipBanChat))
		{
			p_body = new idip_aq_ban_talk(ATT_COMMON);
		}
		break;
	case IDIP_DO_UNBAN_CHAT_REQ:	//解除禁言
		if (GET_FUNC_SWITCH(kFuncCodeIdipBanChat))
		{
			p_body = new idip_aq_unban_talk(ATT_COMMON);
		}
		break;
	case IDIP_AQ_DO_RELIEVE_PUNISH_REQ:	//解除处罚
		p_body = new idip_aq_unban_game();
		break;
	case IDIP_DO_SEND_MARQUEE_NOTICE_REQ:	//跑马灯
		p_body = new idip_announce();
		break;
	case IDIP_QUERY_MARQUEE_NOTICE_REQ: 	//查询跑马灯
		p_body = new idip_query_announce();
		break;
	case IDIP_DO_DELETE_MARQUEE_NOTICE_REQ:	//删除跑马灯
		p_body = new idip_delete_announce();
		break;
	/*case IDIP_DO_MOD_UNBIND_DIAMOND_REQ	:	//修改非绑定钻石
		p_body = new idip_modify_unbind_diamond();
		break;*/
	case IDIP_DO_OPEN_CLOSE_ACTIVITY_REQ	:	//开启GM活动
		p_body = new idip_open_activity();
		break;
	case IDIP_QUERY_INFO_BY_ROLEID_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeIdipQueryByRoleid))
		{
			p_body = new idip_query_role_by_roleid();
		}
		break;
	case IDIP_QUERY_INFO_BY_ROLENAME_REQ:
		p_body = new idip_query_role_by_name();
		break;
	case IDIP_DO_CHANGE_DATA_REQ:
		p_body = new idip_change_data(IDIP_CHANGE_DATA_OP_TYPE_TO_PARTIAL);
		break;
	case IDIP_DO_ACTIVATE_ACCOUNT_REQ:
		p_body = new idip_activate_user();
		break;
	case IDIP_DO_OPEN_CLOSE_WHITE_LIST_REQ:
		p_body = new idip_switch_white_list();
		break;
	case IDIP_QUERY_ROLE_REPUTATION_REQ://查询玩家声望
		if (GET_FUNC_SWITCH(kFuncCodeIdipQueryRoleRepu))
		{
			p_body = new idip_query_role_reputation();
		}
		break;
	case IDIP_QUERY_USER_UNION_ID_REQ: //查询玩家公会
		if (GET_FUNC_SWITCH(kFuncCodeIdipQueryCorpsId))
		{
			p_body = new idip_query_role_corp();
		}
		break;
	case IDIP_AQ_DO_REMOVE_CHAT_REQ: // 发言清除
		p_body = new idip_broadcast_remove_chat();
		break;
	case IDIP_DO_ALLZONE_CHANGE_DATA_REQ: //更改全服玩家数据
		p_body = new idip_change_data(IDIP_CHANGE_DATA_OP_TYPE_TO_ALL);
		break;
	case IDIP_QUERY_TASK_COMPLETE_INFO_REQ: //任务完成查询
		if (GET_FUNC_SWITCH(kFuncCodeIdipQueryTaskComplete))
		{
			p_body = new idip_query_role_task();
		}
		break;
	case IDIP_QUERY_OPENID_ACCORDING_ROLEID_REQ: //根据roleid查openid
		p_body = new idip_query_openid_by_roleid();
		break;
	case IDIP_QUERY_OPENID_ACCORDING_ROLENAME_REQ: //角色名称查询openid和角色ID请求
		p_body = new idip_query_openid_by_name();
		break;
	case IDIP_AQ_DO_SET_PURSUING_REQ: //使目标进入追缴状态（AQ）请求
		p_body = new idip_cash_pursue(1);
		break;
	case IDIP_AQ_DO_RELIEVE_PURSUING_REQ: //追缴状态解除（AQ）请求
		p_body = new idip_cash_pursue(0);
		break;
	case IDIP_AQ_DO_NOSENSE_BAN_CHAT_REQ:	//无感知禁言
		if (GET_FUNC_SWITCH(kFuncCodeIdipBanChatSilence))
		{
			p_body = new idip_aq_ban_talk(ATT_SILENCE);
		}
		break;
	case IDIP_AQ_DO_NOSENSE_UNBAN_CHAT_REQ:	//无感知禁言解禁
		if (GET_FUNC_SWITCH(kFuncCodeIdipBanChatSilence))
		{
			p_body = new idip_aq_unban_talk(ATT_SILENCE);
		}
		break;
	case IDIP_DO_COMPLETE_TASK_REQ: //完成任务请求
		p_body = new idip_finish_task();
		break;
	case IDIP_DO_DEL_ITEM_REQ: //道具扣除请求
		p_body = new idip_delete_item();
		break;
	case IDIP_DO_SHARE_ACTIVITY_INVOKE_REQ:
		p_body = new idip_share_activity_invoke();
		break;
	case IDIP_DO_SEND_EXP_REQ: //发送角色经验请求
		p_body = new idip_send_exp();
		break;
	case IDIP_DO_SEND_MONEY_REQ: //发送货币请求
		p_body = new idip_send_money();
		break;
	case IDIP_AQ_QUERY_SCORE_INFO_REQ: //查询玩家分数
		p_body = new idip_query_role_score();
		break;
	case IDIP_AQ_DO_SET_SCORE_REQ: //修改玩家分数
		p_body = new idip_modify_role_score();
		break;
	case IDIP_QUERY_ONSHELF_GOODS_REQ : //查询玩家拍卖行
		p_body = new idip_query_role_auction();
		break;
	case IDIP_AQ_QUERY_PURSUING_STATUS_REQ: //查询追缴状态
		p_body = new idip_cash_pursue(2);
		break;
	case IDIP_QUERY_IMAGE_LIST_REQ: //查询形象列表请求
		p_body = new idip_query_image_list();
		break;
	case IDIP_DO_UPDATA_DEFAULT_IMAGE_REQ: //更新默认形象请求
		p_body = new idip_update_default_image();
		break;
	case IDIP_DO_COPY_IMAGE_TO_ALBUM_REQ: //复制形象至玩家账号相册请求
		p_body = new idip_add_image_list();
		break;
	case IDIP_DO_ZK_PREVENT_ADDICTION_REMIND_REQ : //中控防沉迷提醒
		if (GET_FUNC_SWITCH(kFuncCodeIdipZk) && GET_FUNC_SWITCH(kFuncCodeIdipZkNotify))
		{
			p_body = new idip_zk_remind();
		}
		break;
	case IDIP_DO_ZK_PREVENT_ADDICTION_BAN_REQ : //中控防沉迷禁玩
		if (GET_FUNC_SWITCH(kFuncCodeIdipZk) && GET_FUNC_SWITCH(kFuncCodeIdipZkBan))
		{
			p_body = new idip_zk_ban();
		}
		break;
	case IDIP_DO_ZK_PREVENT_ADDICTION_FORCE_VERTIFY_REQ : //中控防沉迷强制验证
		if (GET_FUNC_SWITCH(kFuncCodeIdipZk) && GET_FUNC_SWITCH(kFuncCodeIdipZkVertify))
		{
			p_body = new idip_zk_verify();
		}
		break;
	case IDIP_AQ_DO_SET_UNION_NOTICE_REQ://设置工会公告（AQ）请求
		p_body = new idip_set_corps_msg();
		break;
	case IDIP_AQ_DO_BAN_ADD_FRIEND_REQ: //禁止添加好友（AQ）请求
		p_body = new idip_forbid_friend();
		break;
	case IDIP_AQ_DO_BAN_RANKING_REQ: //禁止参与排行榜（AQ）请求
		p_body = new idip_forbid_toplist();
		break;
	case IDIP_DO_MONITOR_BAN_LOGIN_REQ : //家长监控平台禁止登录游戏接口(成长守护平台专用)请求
		p_body = new idip_monitor_ban_login();
		break;
	case IDIP_AQ_DO_ALLZONE_BAN_ACCOUNT_REQ: //全区全服封号（AQ）请求
		if (GET_FUNC_SWITCH(kFuncCodeIdipAllzoneBanUser))
		{
			p_body = new idip_openid_ban_login();
		}
		break;
	case IDIP_AQ_DO_ALLZONE_BAN_CHAT_REQ: //全区全服禁言（AQ）请求
		if (GET_FUNC_SWITCH(kFuncCodeIdipAllzoneBanChat))
		{
			p_body = new idip_opendi_ban_chat();
		}
		break;
	case IDIP_DO_BAN_DEVICE_REQ: //封禁设备请求
		p_body = new idip_deviceid_ban_login();
		break;
	case IDIP_AQ_DO_DEL_SOCIALSPACE_MSG_REQ: //删除朋友圈留言及评论
		p_body = new idip_socialspace_role_op<IDIPCmdReqDelSocialspaceMsg>();
		break;
	case IDIP_AQ_DO_MODIFY_PHOTO_REQ: //修改朋友圈照片
		p_body = new idip_socialspace_role_op<IDIPCmdReqModifySocialspace>();
		break;
	case IDIP_AQ_DO_BAN_SOCIAL_SPACE_REQ: //替换玩家签名
		p_body = new idip_socialspace_role_op<IDIPCmdReqBanSocialSpace>();
		break;
	case IDIP_AQ_DO_REPLACE_ROLE_SIGN_REQ: //替换玩家签名
		p_body = new idip_socialspace_role_op<IDIPCmdReqChangeSocialSpaceSign>();
		break;
	case IDIP_AQ_DO_BAN_UPLOAD_REQ: //禁止上传工会招募图片（AQ）请求
		p_body = new idip_forbid_corps_recruit_url();
		break;
	case IDIP_AQ_DO_DISSOLVE_REQ: //解散聊天群组（AQ）请求
		p_body = new idip_dismiss_group();
		break;
	case IDIP_AQ_DO_UNION_RENAME_REQ:	//设置社团名字
		p_body = new idip_corps_rename();
		break;
	case IDIP_AQ_DO_FORBID_PLAYER_NAME_REQ:	//屏蔽玩家名字
		p_body = new idip_forbid_player_name();
		break;
	case IDIP_DO_USE_CASH_AND_SEND_ITEM_REQ:	//扣点券并发送物品
		p_body = new idip_use_cash_and_send_item();
		break;
	case IDIP_DO_QUERY_ROLE_FREE_CASH_REQ:	//查询玩家可用点券
		p_body = new idip_query_role_free_cash();
		break;
	case IDIP_DO_QUERY_ROLE_DIAMOND_COUNT_REQ: // 查询玩家钻石数量
		p_body = new idip_query_role_diamond_count();
		break;
	case IDIP_DO_USE_DIAMOND_AND_SEND_ITEM_REQ: // 扣钻石并发物品
		p_body = new idip_use_diamond_and_send_item();
		break;
	case IDIP_DO_ADD_EQUIP_REQ: // 扣钻石并发物品
		p_body = new idip_add_equip();
		break;
	case IDIP_AQ_DO_WORKS_OFF_SHELF_REQ: //朋友圈比赛作品下架
		p_body = new idip_socialspace_ssp_work_op(631);
		break;
	case IDIP_AQ_DO_WORKS_RESTORE_REQ: //朋友圈比赛作品恢复
		p_body = new idip_socialspace_ssp_work_op(641);
		break;
	case IDIP_AQ_DO_FORBID_MODIFY_CONTENT_REQ: //禁止修改游戏内文字
		p_body = new idip_forbid_modify_content();
		break;
	case IDIP_AQ_DO_MODIFY_CONTENT_REQ: //修改游戏内文字
		p_body = new idip_modify_content();
		break;
	case IDIP_BAN_UPLOAD_ZSPACE_BULLETIN_REQ:
		p_body = new idip_socialspace_role_op<IDIPCmdReqBanZspaceUploadBulletin>();
		break;
	case IDIP_DEL_ZSPACE_BULLETIN_REQ:
		p_body = new idip_socialspace_role_op<IDIPCmdReqDelZspaceBulletin>();
		break;
	case IDIP_QUERY_ROLE_MARRIAGE_REQ:
		p_body = new idip_query_role_marriage();
		break;
	case IDIP_ROLE_TRADE_QUERY_BRIEF_INFO_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_query_role_list(true, PB::RTRT_REQ_BRIEF_INFO);
		}
		break;
	case IDIP_ROLE_TRADE_QUERY_ROLELIST_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_query_role_list(false, PB::RTRT_REQ_BRIEF_INFO);
		}
		break;
	case IDIP_ROLE_TRADE_QUERY_ROLE_DETAIL_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_query_role_list(true, PB::RTRT_REQ_DETAIL_INFO);
		}
		break;
	case IDIP_ROLE_TRADE_QUERY_ROLE_TAG_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_query_role_tag();
		}
		break;
	case IDIP_ROLE_TRADE_QUERY_FILTER_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_query_filter();
		}
		break;
	case IDIP_ROLE_TRADE_SET_STATUS_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_set_status();
		}
		break;
	case IDIP_ROLE_TRADE_TRANSFER_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_transfer(false);
		}
		break;
	case IDIP_ROLE_TRADE_TRANSFER_ROLLBACK_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_transfer(true);
		}
		break;
	case IDIP_ROLE_TRADE_GET_STATUS_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_get_status();
		}
		break;
	case IDIP_ROLE_TRADE_CHECK_SELL_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			//p_body = new idip_role_trade_check_sell();
			p_body = new idip_role_trade_query_role_list(true, PB::RTRT_REQ_CHECK_SELL);
		}
		break;
	case IDIP_ROLE_TRADE_CHECK_BUY_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_check_buy();
		}
		break;
	case IDIP_ROLE_TRADE_CHECK_SERVER_REQ:
		if (GET_FUNC_SWITCH(kFuncCodeRoleTrade))
		{
			p_body = new idip_role_trade_check_server();
		}
		break;

	case IDIP_POP_FACE_ADD_REQ:
		p_body = new idip_pop_face_add();
		break;
	case IDIP_POP_FACE_QUERY_REQ:
		p_body = new idip_pop_face_query();
		break;
	case IDIP_POP_FACE_DELETE_REQ:
		p_body = new idip_pop_face_delete();
		break;
	default:
		break;
	}
	if (p_body == NULL)
	{
		SLOG(ERR, "idip_request::Create invalid command").PS(cmd_id);
		return NULL;
	}

	if (p_body->Parse(json_content))
	{
		return p_body;
	}
	else
	{
		delete p_body;
		return NULL;
	}
}
bool idip_request::Serve(PManager *manager, SESSION_ID iweb_sid, const std::string& json_request)
{
	IDIPHandler *p_body = Create(json_request);
	if (p_body)
	{
		p_body->web_sid = iweb_sid;

		if (p_body->NeedFilterUnMasterRequest() && p_body->GetRequestPartition() != g_zoneid)
		{
			SLOG(TRACE, "DS::idip_request::Serve: 过滤非主服IDIP命令")
			.P("data_name", p_body->GetDataName())
			.P("req_partition", p_body->GetRequestPartition())
			.P("master_zoneid", g_zoneid);

			p_body->IDIPSend(0, "success");
			delete p_body;
			return true;
		}

		if (p_body->Serve() == IDIPHandler::HANDLE_RET_FINISH)
		{
			delete p_body;
		}
		return true;
	}
	return false;
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#ifdef USE_IDIP_PROXY
void IdipProxyManager::processRequest(int xid, int proxyid, const Octets& reqdata)
{
	std::string json_data((char *)reqdata.begin(), reqdata.size());
	std::string::size_type pos = json_data.find("{");
	if (pos == std::string::npos)
	{
		return;
	}
	json_data.erase(0, pos);
	LOG_TRACE("IdipProxyManager::processRequest. body=%s", json_data.c_str());
	IDIPHandler *p_body = idip_request::Create(json_data);
	if (p_body)
	{
		p_body->xid = xid;
		p_body->proxyid = proxyid;

		if (p_body->NeedFilterUnMasterRequest() && p_body->GetRequestPartition() != g_zoneid)
		{
			SLOG(TRACE, "DS::IdipProxyManager::processRequest: 过滤非主服IDIP命令")
			.P("data_name", p_body->GetDataName())
			.P("req_partition", p_body->GetRequestPartition())
			.P("master_zoneid", g_zoneid);

			p_body->IDIPSend(0, "success");
			delete p_body;
			return;
		}

		if (p_body->Serve() == IDIPHandler::HANDLE_RET_FINISH)
		{
			delete p_body;
		}
	}
	else
	{
		DataBetweenIDIPAndGameRep rep;
		rep.direction = 0; //TODO
		rep.xid = xid;
		rep.proxyid = proxyid;
		rep.zoneid = g_zoneid;
		rep.retcode = -1;

		GIdipProxyManager::GetInstance().SendProtocol(proxyid, rep);
	}
}

void IdipProxyManager::SendResponse(int xid, int proxyid, const std::string& result)
{
	DataBetweenIDIPAndGameRep rep;
	rep.direction = 0; //TODO
	rep.xid = xid;
	rep.proxyid = proxyid;
	rep.zoneid = g_zoneid;
	rep.retcode = 0;
	rep.repdata = Octets(result.c_str(), result.size());

	GIdipProxyManager::GetInstance().SendProtocol(proxyid, rep);
}

#else
////////////////////////////////////////////////////////////////////////////////////////////////
//IDIPCommandServer IDIPCommandServer::instance;
const Protocol::Manager::Session::State *IDIPCommandServer::GetInitState() const
{
	return &state_IDIPCommandServer;
}

void IDIPCommandServer::OnAddSession(Session::ID sid)
{
}

void IDIPCommandServer::OnDelSession(Session::ID sid)
{
}

HttpProtocol *IDIPCommandServer::CreateHttpProtocol() const
{
	return new IDIPCommandRequest();
}

void IDIPCommandServer::SendResponse(SESSION_ID sid, const std::string& content)
{
	//LOG_TRACE("IDIPCommandServer::SendResponse.content=%s", content.c_str());
	IDIPCommandResponse re;
	re.SetResponse(content);
	//LOG_TRACE("IDIPCommandServer::SendResponse.body=%.*s", (int)re.body.size(), (char *)re.body.begin());
	GLog::formatlog("IDIPCommandServer::SendResponse", "%.*s", (int)re.body.size(), (char *)re.body.begin());
	Send(sid, re);
}

void IDIPCommandRequest::Process(PManager *manager, SESSION_ID sid)
{
	InitRequest();
	LOG_TRACE("IDIPCommandRequest. body=%s", _post_data.c_str());

	std::string json_data = _post_data;
	std::string::size_type pos = json_data.find("{");
	if (pos == std::string::npos)
	{
		return;
	}
	json_data.erase(0, pos);

	//LOG_TRACE("IDIPCommandRequest. json=%s", json_data.c_str());

	try
	{
		if (!idip_request::Serve(manager, sid, json_data))
		{
			SendResponse(sid, "204");
		}
	}
	catch (int value)
	{
		LOG_TRACE("IDIPCommandRequest. throw =%d", value);
	}
	catch (...)
	{
		LOG_TRACE("IDIPCommandRequest. try catch...");
		return;
	}

};
#endif
#undef IDIP_ACCOUNT
};
