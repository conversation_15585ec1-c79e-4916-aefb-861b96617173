#include "roam_community_manager.h"
#include "funcswitchmgr.h"
#include "func_info.h"
#include "rolemap.h"
#include "global_config.h"
#include "sensitive_interface.h"
#include "nats_message.h"
#include "natsinterface.h"
#include "global_config.h"
#include "roam_community_config.h"
#include "chatpublic.hpp"
#include "brief_manager.h"
#include "centerroamcommunitybattle.h"
#include "instancemanager.h"
#include "campaignmanager.h"

#include <iomanip>

#include "gprotoc/ipt_roam_community_op_re.pb.h"
#include "gprotoc/npt_roam_community_notify.pb.h"
#include "gprotoc/npt_roam_community_op.pb.h"
#include "gprotoc/ipt_roam_community_op.pb.h"
#include "gprotoc/ROAM_COMMUNITY_OP.pb.h"
#include "gprotoc/npt_roam_community_op_re.pb.h"
#include "gprotoc/roam_community_brief_data.pb.h"
#include "gprotoc/roam_community_data.pb.h"
#include "gprotoc/roam_community_battle_data.pb.h"
#include "gprotoc/roam_community_point_data.pb.h"
#include "gprotoc/roam_community_against_plan.pb.h"
#include "gprotoc/ipt_center_battle_transfer_player.pb.h"
#include "gprotoc/ipt_send_center_battle_result.pb.h"
#include "gprotoc/npt_player_search_brief_re.pb.h"
#include "gprotoc/ipt_center_battle_roam_player_apply_re.pb.h"
#include "gprotoc/roam_community_battle_result.pb.h"
#include "gprotoc/ipt_center_battle_roam_player_quit.pb.h"
#include "gprotoc/roam_community_rank_data.pb.h"

//#include "oss_element_templates.h"

#include <time.h>
#include <list>

#define ERR_FINISH_RETURN(msg, code) 	\
	_err_msg = (msg);			\
	_retcode = code;			\
	Finish();					\
	return;

#define FOO_ERR_FINISH_RETURN(msg, code) 	\
	foo->_err_msg = (msg);			\
	foo->_retcode = code;			\
	foo->Finish();					\
	return;

#define DECLARE_RET_VAR				\
	std::string _err_msg;			\
	int _redis_ret;					\
	int _retcode;

#define INIT_RET_VAR				\
	_err_msg(""), _redis_ret(0), _retcode(0)

#define RC_UPDATE_TASK_INTERVAL	5
#define RC_PAGE_SIZE			30
#define RC_MAX_BATTLE_NUM_PER_UPDATE	10
#define MAKE_SERVICE_ID(zoneid) std::string("ds.") + std::to_string(zoneid)


namespace GNET
{
static std::string _test = std::string(".New");
static std::string _service_group_op = std::string("RC.OP") + _test;

enum UPDATE_LIST_TYPE
{
	ULT_NORMAL		= 1,
	ULT_INSERT		= 2,
	ULT_DELETE		= 3,
};

static void update_list_data(int64_t uniqueid, PB::roam_community_data& data, UPDATE_LIST_TYPE type)
{
	if (type == ULT_DELETE)
	{
		for (auto iter = RCMANAGER._brief_list.begin(); iter != RCMANAGER._brief_list.end(); ++iter)
		{
			if (iter->base().id() == data.base().id())
			{
				RCMANAGER._brief_list.erase(iter);
				break;
			}
		}
		return;
	}

	PB::roam_community_brief_data brief_data;
	auto base_ptr = brief_data.mutable_base();
	base_ptr->CopyFrom(data.base());

	brief_data.set_member_num(data.members_size());
	for (int i = 0; i < data.applyers_size(); ++i)
	{
		brief_data.add_applyers(data.applyers(i).roleid());
	}

	std::string list_key = GetOssKeyWord(OSS_RC_LIST, 0, "TEST");
	std::string str_brief;
	PB_2_STR(brief_data, str_brief);
	OSSInterface::GetInstance().HSet(list_key, std::to_string(uniqueid), str_brief, [](int retcode)
	{
	}, 0, 0, 1, false);

	if (type == ULT_INSERT)
	{
		RCMANAGER._brief_list.push_back(brief_data);
	}
	else
	{
		for (auto iter = RCMANAGER._brief_list.begin(); iter != RCMANAGER._brief_list.end(); ++iter)
		{
			if (iter->base().id() == data.base().id())
			{
				iter->CopyFrom(brief_data);
				break;
			}
		}
	}
}

static void update_base_data(int64_t uniqueid, const PB::roam_community_data& data)
{
	std::string data_key = GetOssKeyWord(OSS_RC_DATA, uniqueid, "TEST");

	PB::roam_community_data tmp_data;
	tmp_data.CopyFrom(data);
	tmp_data.clear_members();
	std::string str_data;
	PB_2_STR(tmp_data, str_data);
	std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
	OSSInterface::GetInstance().HSet(data_key, data_field, str_data, [](int retcode)
	{
	}, 0, 0, 1, false);
}

static void update_full_data(int64_t uniqueid, const PB::roam_community_data& data)
{
	update_base_data(uniqueid, data);

	std::string data_key = GetOssKeyWord(OSS_RC_DATA, uniqueid, "TEST");
	std::map<std::string, std::string> kv_map;
	for (int i = 0; i < data.members_size(); ++i)
	{
		auto& member = data.members(i);
		std::string str_member;
		PB_2_STR(member, str_member);
		kv_map[std::to_string(member.roleid())] = str_member;
		if (kv_map.size() >= 10 || i == data.members_size() - 1)
		{
			OSSInterface::GetInstance().HMSet(data_key, kv_map, [](int retcode)
			{
			}, false);
			kv_map.clear();
		}
	}
}

static void add_history_data(PB::roam_community_data& data, PB::roam_community_data::history_record::HISTORY_TYPE type, int64_t roleid, int64_t param, PB::roam_community_data::history_record& history, int64_t param2 = 0)
{
	auto history_ptr = data.add_historys();
	history_ptr->set_type(type);
	history_ptr->set_roleid(roleid);
	history_ptr->set_param1(param);
	history_ptr->set_param2(param2);
	history_ptr->set_timestamp(Timer::GetTime());
	history.CopyFrom(*history_ptr);

	int historys_size = data.historys_size();
	if (historys_size > 1)
	{
		for (int i = 0; i < historys_size - 1; ++i)
		{
			data.mutable_historys()->SwapElements(i, historys_size - 1);
		}

		if (historys_size > ROAM_COMMUNITY_CONFIG.max_history)
		{
			data.mutable_historys()->RemoveLast();
		}
	}
}

static void get_community_data(const std::map<std::string, std::string>& data_map, std::vector<int64_t>& roleids, PB::roam_community_data& _data, int64_t roleid, bool& isjoin)
{
	PB::roam_community_data tmp_data;
	for (auto memb_iter = data_map.begin(); memb_iter != data_map.end(); ++memb_iter)
	{
		if (memb_iter->first == REDIS_UUID_KEY_DATA_FIELD_NAME)
		{
			STR_2_PB(memb_iter->second, _data);
			continue;
		}

		auto member_ptr = tmp_data.add_members();
		STR_2_PB(memb_iter->second, *member_ptr);

		roleids.push_back(member_ptr->roleid());
		if (roleid > 0 && member_ptr->roleid() == roleid)
		{
			isjoin = true;
		}
	}

	for (int i = 0; i < tmp_data.members_size(); ++i)
	{
		auto member_ptr = _data.add_members();
		member_ptr->CopyFrom(tmp_data.members(i));
	}
}

static void update_battle_data(const std::map<std::string, std::string>& data_map)
{
	PB::roam_community_battle_data battle_data;
	std::map<int64_t, std::set<int> > _winner_map;
	std::map<int64_t, std::set<int> > _failed_map;

	auto iter_data = data_map.find(REDIS_UUID_KEY_DATA_FIELD_NAME);
	if (iter_data != data_map.end())
	{
		STR_2_PB(iter_data->second, battle_data);

		for (int i = 0; i < battle_data.winners_size(); ++i)
		{
			auto tmp_ptr = battle_data.mutable_winners(i);
			auto& index_set = _winner_map[tmp_ptr->uniqueid()];
			for (int j = 0; j < tmp_ptr->indexes_size(); ++j)
			{
				index_set.insert(tmp_ptr->indexes(j));
			}
		}
		for (int i = 0; i < battle_data.faileds_size(); ++i)
		{
			auto tmp_ptr = battle_data.mutable_faileds(i);
			auto& index_set = _failed_map[tmp_ptr->uniqueid()];
			for (int j = 0; j < tmp_ptr->indexes_size(); ++j)
			{
				index_set.insert(tmp_ptr->indexes(j));
			}
		}
	}

	std::map<int, PB::roam_community_point_data> points;
	for (auto iter = data_map.begin(); iter != data_map.end(); ++iter)
	{
		if (iter->first == REDIS_UUID_KEY_DATA_FIELD_NAME)
		{
			continue;
		}

		PB::roam_community_point_data point;
		STR_2_PB(iter->second, point);
		points.insert(std::make_pair(point.index(), point));
	}

	LOG_TRACE("DS::RoamCommunityManager update_battle_data old_battle_state=%d, new_battle_state=%d, old_version=%ld, new_version=%ld, old_season_state=%d, new_season_state=%d", RCMANAGER._battle_data.state(), battle_data.state(), RCMANAGER._battle_data.data_version(), battle_data.data_version(), RCMANAGER._battle_data.season_state(), battle_data.season_state());
	RCMANAGER._battle_data.CopyFrom(battle_data);
	RCMANAGER._points.swap(points);
	RCMANAGER._winner_map.swap(_winner_map);
	RCMANAGER._failed_map.swap(_failed_map);
}

static int calc_defense(int resource)
{
	int defense = resource * ROAM_COMMUNITY_CONFIG.resource_to_defense / 100.0f;
	return defense;
}


void RoamCommunityManager::SendNotifyUpdateData(const std::vector<int64_t>& uniqueids)
{
	PB::npt_roam_community_notify notify;
	notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_UPDATE_DATA);
	for (int i = 0; i < uniqueids.size(); ++i)
	{
		notify.add_targetid(uniqueids[i]);
	}
	NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
}

void RoamCommunityManager::OnNotifyUpdateData(const PB::npt_roam_community_notify *notify)
{
	for (int i = 0; i < notify->targetid_size(); ++i)
	{
		std::string _data_key = GetOssKeyWord(OSS_RC_DATA, notify->targetid(i), "TEST");
		OSSInterface::GetInstance().HGetAll(_data_key, [](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
		{
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				return;
			}

			PB::roam_community_data _data;
			std::vector<int64_t> roleids;
			bool isjoin = false;
			get_community_data(data_map, roleids, _data, 0, isjoin);

			PB::npt_roam_community_notify update_nf;
			update_nf.set_op(PB::ROAM_COMMUNITY_OP::RCO_UPDATE_DATA);
			update_nf.set_resource((int)_data.base().resource_num());

			for (auto iter = roleids.begin(); iter != roleids.end(); ++iter)
			{
				RoleInfo *pRole = RoleMap::Instance().FindOnline(*iter);
				if (pRole)
				{
					pRole->SendMessage2Client(update_nf);
				}
			}
		}, 0, false, false);
	}
}


struct RCUpdateResourceProcess: public std::enable_shared_from_this<RCUpdateResourceProcess>
{
	DECLARE_RET_VAR

	std::map<int64_t, int> _resource;
	std::function<void(const std::map<int64_t, int>&)> _cb;

	std::map<int64_t, int> _final_resource;
	bool _reset;
	RCUpdateResourceProcess(const std::map<int64_t, int>& resource, std::function<void(const std::map<int64_t, int>&)>&& cb)
		: INIT_RET_VAR, _resource(resource), _cb(cb), _reset(false)
	{}

	void Begin()
	{
		// 初始就没传值，认为是要重置
		if (_resource.size() == 0)
		{
			_reset = true;
			std::string list_key = GetOssKeyWord(OSS_RC_LIST, 0, "TEST");
			OSSInterface::GetInstance().HGetAll(list_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					LOG_TRACE("DS::RoamCommunityManager::RCUpdateResourceProcess get brief_list failed, retcode=%d", retcode);
					foo->Finish();
					return;
				}

				foo->_resource.clear();
				std::vector<PB::roam_community_brief_data> _brief_list;
				for (auto iter = data_map.begin(); iter != data_map.end(); ++iter)
				{
					if (iter->first == REDIS_UUID_KEY_DATA_FIELD_NAME)
					{
						continue;
					}

					PB::roam_community_brief_data brief;
					STR_2_PB(iter->second, brief);

					_brief_list.push_back(brief);
					foo->_resource.insert(std::make_pair(brief.base().id(), ROAM_COMMUNITY_CONFIG.start_resource_num));
				}
				RCMANAGER._brief_list.swap(_brief_list);

				foo->Check1();
			}, 0, false, false);
		}
		else
		{
			Check1();
		}
	}

	void Check1()
	{
		if (_resource.size() == 0)
		{
			Finish();
			return;
		}

		auto iter_resource = _resource.begin();
		int64_t uniqueid = iter_resource->first;
		int resource_num = iter_resource->second;
		_resource.erase(iter_resource);

		std::string data_key = GetOssKeyWord(OSS_RC_DATA, uniqueid, "TEST");
		OSSInterface::GetInstance().Lock(data_key, [data_key, uniqueid, resource_num, foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				LOG_TRACE("DS::RoamCommunityManager::RCUpdateResourceProcess1 add resource, uniqueid=%ld, resource_num=%d, retcode=%d", uniqueid, resource_num, retcode);
				foo->Check1();
			}
			else
			{
				PB::roam_community_data _data;
				STR_2_PB(data, _data);

				if (foo->_reset)
				{
					_data.mutable_base()->set_resource_num(resource_num);
					_data.mutable_base()->set_day_activity_resource(0);
				}
				else
				{
					_data.mutable_base()->set_resource_num(_data.base().resource_num() + resource_num);
				}
				foo->_final_resource[uniqueid] = _data.base().resource_num();

				std::string str_data;
				PB_2_STR(_data, str_data);
				std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
				OSSInterface::GetInstance().HSet(data_key, data_field, str_data, [uniqueid, resource_num, cur_resource_num = _data.base().resource_num(), reset = foo->_reset](int retcode)
				{
					LOG_TRACE("DS::RoamCommunityManager::RCUpdateResourceProcess2 add resource, uniqueid=%ld, resource_num=%d, cur_resource_num=%ld, reset=%d, retcode=%d", uniqueid, resource_num, cur_resource_num, reset, retcode);
				}, 0, 0, 1, false);

				update_list_data(uniqueid, _data, ULT_NORMAL);
				{
					OssUnlockShell shell(data_key);
				}

				foo->Check1();
			}
		});
	}

	void Finish()
	{
		_cb(_final_resource);
		LOG_TRACE("DS::RoamCommunityManager::RCUpdateResourceProcess finish");
	}
};

struct RCInitBattleDataProcess: public std::enable_shared_from_this<RCInitBattleDataProcess>
{
	DECLARE_RET_VAR

	bool _reset;
	bool _reset_all_community_resource;

	std::string _battle_data_key;
	RCInitBattleDataProcess(bool reset)
		: INIT_RET_VAR, _reset(reset), _reset_all_community_resource(false)
	{
		_battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
	}

	void Begin()
	{
		OSSInterface::GetInstance().Lock(_battle_data_key, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode && (int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode && (int)oss::OSSCode::OSSCODE_GET_DATA_INVALID != retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_INIT_FAILED);
			}


			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
			{
				OssUnlockShell shell(foo->_battle_data_key);

				PB::roam_community_battle_data battle_data;
				STR_2_PB(data, battle_data);
				if (!foo->_reset && battle_data.activity_index() == ROAM_COMMUNITY_CONFIG.activity_index)
				{
					foo->Check3();
				}
				else
				{
					oss::Del(foo->_battle_data_key, [](int retcode) { });
					foo->Check1();
				}
			}
			else
			{
				foo->Check1();
			}
		}, false);
	}

	void Check1()
	{
		OSSInterface::GetInstance().Lock(_battle_data_key, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
			{
				{
					OssUnlockShell shell(foo->_battle_data_key);
				}
				foo->Check3();
			}
			else if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_INIT_FAILED);
			}
			else
			{
				foo->Check2();
			}
		}, true);
	}

	void Check2()
	{
		_reset_all_community_resource = true;

		PB::roam_community_battle_data battle_data;
		battle_data.set_state(PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_INVALID);
		battle_data.set_data_version(1);
		battle_data.set_season_start_time(ROAM_COMMUNITY_CONFIG.season_start_time);
		battle_data.set_season_state(RCSS_CLOSE);
		battle_data.set_battle_id(1);
		battle_data.set_activity_index(ROAM_COMMUNITY_CONFIG.activity_index);

		std::string str_data;
		PB_2_STR(battle_data, str_data);
		std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
		OSSInterface::GetInstance().HSet(_battle_data_key, data_field, str_data, [](int retcode)
		{
		}, 0, 0, 1, false);

		int index = 0;
		std::map<std::string, std::string> kv_map;
		for (auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.begin(); iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.end(); ++iter_point_cfg)
		{
			++index;
			if (iter_point_cfg->second.ispve == 0)
			{
				PB::roam_community_point_data point;
				point.set_index(iter_point_cfg->first);
				point.set_defense(iter_point_cfg->second.defense);
				point.set_belong(0);

				std::string str_point;
				PB_2_STR(point, str_point);
				kv_map.insert(std::make_pair(std::to_string(point.index()), str_point));
			}

			if (kv_map.size() >= 10 || (kv_map.size() > 0 && index == ROAM_COMMUNITY_CONFIG.points.size()))
			{
				OSSInterface::GetInstance().HMSet(_battle_data_key, kv_map, [](int retcode)
				{
				}, false);
				kv_map.clear();
			}
		}

		{
			OssUnlockShell shell(_battle_data_key);
		}
		Check3();
	}

	void Check3()
	{
		OSSInterface::GetInstance().HGetAll(_battle_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check3 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			update_battle_data(data_map);

			foo->Check4();
		}, 0, false, false);
	}

	void Check4()
	{
		if (_reset_all_community_resource)
		{
			std::map<int64_t, int> resource;
			std::shared_ptr<RCUpdateResourceProcess> process = make_shared<RCUpdateResourceProcess>(resource, [](const std::map<int64_t, int>& _) {});
			process->Begin();
		}
		Finish();
	}

	void Finish()
	{
		RCMANAGER._init_state = _retcode == 0 ? RC_INIT_STATE_SUCCESS : RC_INIT_STATE_UNLOAD;
		LOG_TRACE("DS::RoamCommunityManager::RCInitBattleDataProcess resest=%d, _reset_resource=%d, errmsg=%s, reids_retcode=%d, retcode=%d", _reset, _reset_all_community_resource, _err_msg.c_str(), _redis_ret, _retcode);
	}
};

struct RCInitProcess: public std::enable_shared_from_this<RCInitProcess>
{
	DECLARE_RET_VAR

	std::string _unique_name_key;
	std::string _list_key;
	std::string _ruid_key;

	RCInitProcess() : INIT_RET_VAR
	{
		_unique_name_key = GetOssKeyWord(OSS_RC_UNIQUENAME, 0, "TEST");
		_list_key = GetOssKeyWord(OSS_RC_LIST, 0, "TEST");
		_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
	}

	void Begin()
	{
		OSSInterface::GetInstance().Lock(_list_key, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
			{
				OssUnlockShell shell(foo->_list_key);
				foo->Check1();
			}
			else if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID ==  retcode)
			{
				std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
				OSSInterface::GetInstance().HSet(foo->_list_key, data_field, data_field, [](int retcode)
				{
				}, 0, 0, 1, false);
				OssUnlockShell shell(foo->_list_key);
				foo->Check1();
			}
			else
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_INIT_FAILED);
			}
		}, true);
	}

	void Check1()
	{
		OSSInterface::GetInstance().Lock(_ruid_key, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
			{
				OssUnlockShell shell(foo->_ruid_key);
				foo->Check2();
			}
			else if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID ==  retcode)
			{
				std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
				OSSInterface::GetInstance().HSet(foo->_ruid_key, data_field, data_field, [](int retcode)
				{
				}, 0, 0, 1, false);
				OssUnlockShell shell(foo->_ruid_key);
				foo->Check2();
			}
			else
			{
				FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_INIT_FAILED);
			}
		}, true);
	}

	void Check2()
	{
		OSSInterface::GetInstance().Lock(_unique_name_key, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
			{
				OssUnlockShell shell(foo->_unique_name_key);
				foo->Finish();
			}
			else if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID ==  retcode)
			{
				std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
				OSSInterface::GetInstance().HSet(foo->_unique_name_key, data_field, data_field, [](int retcode)
				{
				}, 0, 0, 1, false);
				OssUnlockShell shell(foo->_unique_name_key);
				foo->Finish();
			}
			else
			{
				FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_INIT_FAILED);
			}
		}, true);
	}

	void Finish()
	{
		LOG_TRACE("DS::RoamCommunityManager::Initialize errmsg=%s, reids_retcode=%d, retcode=%d", _err_msg.c_str(), _redis_ret, _retcode);
	}
};

struct RCUpdateBattleData: public std::enable_shared_from_this<RCUpdateBattleData>
{
	DECLARE_RET_VAR

	std::string _battle_data_key;
	bool _force;

	RCUpdateBattleData(bool force)
		: INIT_RET_VAR, _force(force)
	{
		_battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
	}

	void Begin()
	{
		if (!_force)
		{
			Check1();
		}
		else
		{
			Check2();
		}
	}

	void Check1()
	{
		OSSInterface::GetInstance().HGet(_battle_data_key, REDIS_UUID_KEY_DATA_FIELD_NAME, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			PB::roam_community_battle_data battle_data;
			STR_2_PB(data, battle_data);

			if (battle_data.data_version() != RCMANAGER._battle_data.data_version())
			{
				foo->Check2();
			}
			else
			{
				foo->Finish();
			}
		});
	}

	void Check2()
	{
		OSSInterface::GetInstance().HGetAll(_battle_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			update_battle_data(data_map);
			foo->Finish();
		}, 0, false, false);
	}

	void Finish()
	{
	}
};

struct RCCheckActiveProcess: public std::enable_shared_from_this<RCCheckActiveProcess>
{
	DECLARE_RET_VAR

	std::string _data_key;
	int64_t _uniqueid;

	std::vector<int64_t> _roleids;

	PB::roam_community_data _data;
	RCCheckActiveProcess(int64_t uniqueid)
		: INIT_RET_VAR, _uniqueid(uniqueid)
	{
		_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
	}

	void Begin()
	{
		OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID ==  retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
			}
			else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			STR_2_PB(data, foo->_data);

			int64_t month_begin = GetLocalMonthBegin();
			if (foo->_data.base().reset_time() == month_begin)
			{
				OssUnlockShell shell(foo->_data_key);
				return;
			}

			foo->Check1();
		}, false);
	}

	void Check1()
	{
		OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				OssUnlockShell shell(foo->_data_key);
				FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			bool isjoin = false;
			get_community_data(data_map, foo->_roleids, foo->_data, 0, isjoin);

			foo->Do();
		}, 0, false, false);
	}

	void Do()
	{
		int64_t month_begin = GetLocalMonthBegin();
		_data.mutable_base()->set_reset_time(month_begin);
		_data.mutable_base()->set_active(0);

		update_base_data(_uniqueid, _data);
		update_list_data(_uniqueid, _data, ULT_NORMAL);

		OssUnlockShell shell(_data_key);
		Finish();
	}

	void Finish()
	{
		if (_retcode == 0)
		{
			PB::npt_roam_community_notify notify;
			notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_ACTIVE);
			notify.set_active(0);

			for (int i = 0; i < _roleids.size(); ++i)
			{
				notify.add_targetid(_roleids[i]);
			}
			NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
		}

		LOG_TRACE("DS::RoamCommunityManager::RCCheckActiveProcess id=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _uniqueid, _err_msg.c_str(), _redis_ret, _retcode);
	}
};

struct RCUpdateProcess: public std::enable_shared_from_this<RCUpdateProcess>
{
	DECLARE_RET_VAR

	std::string _list_key;

	std::vector<PB::roam_community_brief_data> _brief_list;

	RCUpdateProcess() : INIT_RET_VAR
	{
		_list_key = GetOssKeyWord(OSS_RC_LIST, 0, "TEST");
	}

	void Begin()
	{
		OSSInterface::GetInstance().HGetAll(_list_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			for (auto iter = data_map.begin(); iter != data_map.end(); ++iter)
			{
				if (iter->first == REDIS_UUID_KEY_DATA_FIELD_NAME)
				{
					continue;
				}

				PB::roam_community_brief_data brief;
				STR_2_PB(iter->second, brief);

				foo->_brief_list.push_back(brief);
			}

			foo->Do();
		}, 0, false, false);
	}

	void Do()
	{
		// 这边做点啥：玩家活跃度重置？社团活跃度重置？
		int64_t month_begin = GetLocalMonthBegin();
		for (auto iter = _brief_list.begin(); iter != _brief_list.end(); ++iter)
		{
			int zoneid = RoamCommunityManager::GetZoneidFromUniqueId(iter->base().id());
			if (g_zoneid == MERGE_ZONE(zoneid) && iter->base().reset_time() != month_begin)
			{
				std::shared_ptr<RCCheckActiveProcess> process = make_shared<RCCheckActiveProcess>(iter->base().id());
				process->Begin();
			}
		}

		std::sort(_brief_list.begin(), _brief_list.end(), [](const PB::roam_community_brief_data & a, const PB::roam_community_brief_data & b)
		{
			return a.base().active() > b.base().active();
		});

		RCMANAGER._brief_list.swap(_brief_list);

		Finish();
	}

	void Finish()
	{
		if (_retcode)
		{
			LOG_TRACE("DS::RoamCommunityManager::Update errmsg=%s, reids_retcode=%d, retcode=%d", _err_msg.c_str(), _redis_ret, _retcode);
		}
	}
};

bool RoamCommunityManager::Initialize()
{
	IntervalTimer::Attach(this, (1000000 * RC_UPDATE_TASK_INTERVAL) / IntervalTimer::Resolution());

	std::shared_ptr<RCInitProcess> process = make_shared<RCInitProcess>();
	process->Begin();
	return true;
}

bool RoamCommunityManager::Update()
{
	++_update_times;
	if (_update_times % 12 == 0)
	{
		std::shared_ptr<RCUpdateProcess> process = make_shared<RCUpdateProcess>();
		process->Begin();
	}

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END

	if (manager_zoneid != g_zoneid)
	{
		if (FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
		{
			std::shared_ptr<RCUpdateBattleData> process = make_shared<RCUpdateBattleData>(_update_times % 60 == 0);
			process->Begin();
		}
	}
	else
	{
		do
		{
			if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
			{
				break;
			}

			if (_init_state != RC_INIT_STATE_SUCCESS)
			{
				if (_init_state == RC_INIT_STATE_UNLOAD)
				{
					_init_state = RC_INIT_STATE_LOADING;

					std::shared_ptr<RCInitBattleDataProcess> process1 = make_shared<RCInitBattleDataProcess>(false);
					process1->Begin();
				}
				break;
			}

			static bool has_check_repeated_name = false;
			if (!has_check_repeated_name)
			{
				if (_brief_list.size() > 0)
				{
					has_check_repeated_name = true;

					std::map<std::string, int64_t> name_uids;
					for (auto iter = _brief_list.begin(); iter != _brief_list.end(); ++iter)
					{
						auto iter_nuid = name_uids.find(iter->base().name());
						if (iter_nuid == name_uids.end())
						{
							name_uids.insert(std::make_pair(iter->base().name(), iter->base().id()));
						}
						else
						{
							std::ostringstream ss;
							ss << std::setiosflags(std::ios::uppercase) << std::hex << iter->base().id();
							GNET::Octets oct_name;
							CharsetConverter::conv_charset_t2u(GNET::Octets(ss.str().c_str(), ss.str().size()), oct_name);
							std::string new_name = iter->base().name() + std::string((const char *)oct_name.begin(), oct_name.size());

							std::string _name_data_key = GetOssKeyWord(OSS_RC_DATA, iter->base().id(), "TEST");
							OSSInterface::GetInstance().Lock(_name_data_key, [_name_data_key, id = iter->base().id(), old_name = iter->base().name(), new_name](int retcode, const string & data)
							{
								if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
								{
									PB::roam_community_data _data;
									STR_2_PB(data, _data);
									_data.mutable_base()->set_name(new_name);
									std::string str_data;
									PB_2_STR(_data, str_data);

									std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
									OSSInterface::GetInstance().HSet(_name_data_key, data_field, str_data, [id, old_name, new_name](int retcode)
									{
										LOG_TRACE("DS::RoamCommunityManager::Update check repeated name, id=%ju name=%s, new_name=%s", id, old_name.c_str(), new_name.c_str());
									}, 0, 0, 1, false);

									update_list_data(id, _data, ULT_NORMAL);

									std::string _unique_name_key = GetOssKeyWord(OSS_RC_UNIQUENAME, 0, "TEST");
									OSSInterface::GetInstance().HSet(_unique_name_key, new_name, std::to_string(id), [](int retcode)
									{
									}, 0, 0, 0, false);
									OssUnlockShell shell(_name_data_key);
								}
							}, false);
						}
					}
				}
			}

			time_t now = Timer::GetTime();
			if (now >= ROAM_COMMUNITY_CONFIG.season_end_time && _battle_data.season_state() == RCSS_OPEN)
			{
				if (!_debug_model)
				{
					OnSeasonEnd();
				}
			}
			else if (now >= ROAM_COMMUNITY_CONFIG.season_start_time && now < ROAM_COMMUNITY_CONFIG.season_end_time)
			{
				if (!_debug_model)
				{
					if (_battle_data.season_state() != RCSS_OPEN)
					{
						OnSeasonStart();
						break;
					}

					time_t week_begin = GetLocalWeekBegin();
					time_t vote_to_wait_time = week_begin + ROAM_COMMUNITY_CONFIG.vote_to_wait_time;
					time_t wait_to_battle_time = week_begin + ROAM_COMMUNITY_CONFIG.wait_to_battle_time;
					time_t battle_to_end_time = week_begin + ROAM_COMMUNITY_CONFIG.battle_to_end_time;
					time_t end_to_vote_time = week_begin + ROAM_COMMUNITY_CONFIG.end_to_vote_time;

					if (ROAM_COMMUNITY_CONFIG.split_stage == (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_VOTE
					        && (now >= end_to_vote_time || now < vote_to_wait_time)
					        && _battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_VOTE)
					{
						OnChange2Vote();
					}
					else if (ROAM_COMMUNITY_CONFIG.split_stage == (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_WAIT
					         && (now >= vote_to_wait_time || now < wait_to_battle_time)
					         && _battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_WAIT)
					{
						OnChange2Wait();
					}
					else if (ROAM_COMMUNITY_CONFIG.split_stage == (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE
					         && (now >= wait_to_battle_time || now < battle_to_end_time)
					         && _battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE)
					{
						OnChange2Battle();
					}
					else if (ROAM_COMMUNITY_CONFIG.split_stage == (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_END
					         && (now >= battle_to_end_time || now < end_to_vote_time)
					         && _battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_END)
					{
						OnChange2End();
					}

					if (ROAM_COMMUNITY_CONFIG.split_stage != (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_VOTE
					        && (now >= end_to_vote_time && now < vote_to_wait_time)
					        && _battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_VOTE)
					{
						OnChange2Vote();
					}
					else if (ROAM_COMMUNITY_CONFIG.split_stage != (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_WAIT
					         && (now >= vote_to_wait_time && now < wait_to_battle_time)
					         && _battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_WAIT)
					{
						OnChange2Wait();
					}
					else if (ROAM_COMMUNITY_CONFIG.split_stage != (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE
					         && (now >= wait_to_battle_time && now < battle_to_end_time)
					         && _battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE)
					{
						OnChange2Battle();
					}
					else if (ROAM_COMMUNITY_CONFIG.split_stage != (int)PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_END
					         && (now >= battle_to_end_time && now < end_to_vote_time)
					         && (_battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_END))
					{
						OnChange2End();
					}
				}

				if (_battle_data.state() == PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE)
				{
					//创建战场
					if (_will_create_battle_points.size() > 0)
					{
						int create_num = 0;
						for (auto iter_create = _will_create_battle_points.begin(); iter_create != _will_create_battle_points.end() && create_num < RC_MAX_BATTLE_NUM_PER_UPDATE; ++create_num)
						{
							auto iter_battle = _battle_info.find(iter_create->index());
							if (iter_battle == _battle_info.end())
							{
								auto iter_point = RCMANAGER._points.find(iter_create->index());
								if (iter_point != RCMANAGER._points.end())
								{
									auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(iter_create->index());
									if (iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.end())
									{
										int occupy_times = iter_point->second.occupy_times();
										if (iter_point->second.belong() == 0)
										{
											occupy_times = 0;
										}
										else if (occupy_times == 0)
										{
											occupy_times = 1;
										}
										TryCreateBattle(iter_create->index(), iter_create->attacker(), iter_create->defender(), 0, 0, iter_point_cfg->second.level, iter_point->second.defense(), 0, occupy_times);
									}
								}
							}
							iter_create = _will_create_battle_points.erase(iter_create);
						}
					}
					// 完了尝试传入
					//else
					{
						int enter_num = 0;
						for (auto iter_index = _battle_info.begin(); iter_index != _battle_info.end(); ++iter_index)
						{
							for (auto iter_battle = iter_index->second.begin(); iter_battle != iter_index->second.end(); ++iter_battle)
							{
								if (!iter_battle->create_finish || iter_battle->battle_finish)
								{
									continue;
								}
								for (auto iter_player = iter_battle->wait_players.begin(); iter_player != iter_battle->wait_players.end();)
								{
									TryTeleportPlayer(iter_player->first, iter_battle->inst_tid, iter_battle->inst_id, iter_battle->zoneid, iter_index->first, iter_player->second.second);
									iter_player = iter_battle->wait_players.erase(iter_player);
									++enter_num;
								}
							}
						}
					}
				}

			}
		}
		while (false);
	}


	return true;
}

void RoamCommunityManager::IptOp(RoleInfo *pRole, PB::ipt_roam_community_op *request)
{
	PB::ipt_roam_community_op_re request_re;
	request_re.set_op(request->op());
	request_re.set_txnid(request->txnid());
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunity))
	{
		request_re.set_retcode(GNET::ERROR_FUNC_CLOSED_TEMPORARY);
		pRole->SendMessage2GS(request_re);
		return;
	}

	switch (request->op())
	{
	case PB::ROAM_COMMUNITY_OP::RCO_CREATE:
		RequestCreate(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_EVENT:
		RequestAddHistory(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_TRY_ADD_RESOURCE:
		RequestAddResource(pRole, request);
		break;
	default:
		break;
	}
}

void OnCallBack(RoleInfo *pRole, PB::ipt_roam_community_op *request, int retcode)
{
	PB::ipt_roam_community_op_re request_re;
	request_re.set_op(request->op());
	request_re.set_retcode(retcode);
	request_re.set_txnid(request->txnid());
	request_re.set_name(request->name());
	request_re.set_icon(request->icon());
	request_re.set_announce(request->announce());
	pRole->SendMessage2GS(request_re);
	LOG_TRACE("DS::RoamCommunityManager::RequestCreate invalid name roleid=%ju name=%s", pRole->roleid, request->name().c_str());
	return;
}
void RoamCommunityManager::RequestCreate(RoleInfo *pRole, PB::ipt_roam_community_op *request)
{
	struct RCCreateProcess: public std::enable_shared_from_this<RCCreateProcess>
	{
		DECLARE_RET_VAR

		std::string _unique_name_key;
		std::string _list_key;
		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;
		int _fightcapacity;

		std::string _name;
		std::string _announce;
		int _icon;
		int _txnid;

		PB::roam_community_data _data;
		RCCreateProcess(ruid_t roleid, int fp, int64_t uniqueid, const std::string& name, const std::string& announce, int icon, int txnid)
			: INIT_RET_VAR, _uniqueid(uniqueid), _roleid(roleid), _fightcapacity(fp), _name(name), _announce(announce), _icon(icon), _txnid(txnid)
		{
			_unique_name_key = GetOssKeyWord(OSS_RC_UNIQUENAME, 0, "TEST");
			_list_key = GetOssKeyWord(OSS_RC_LIST, 0, "TEST");
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: already in roam community=" + data, GNET::ERROR_ROAM_COMMUNITY_SELF_HAS_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->Check1();
			});
		}

		void Check1()
		{
			OSSInterface::GetInstance().HSet(_unique_name_key, _name, std::to_string(_uniqueid), [foo = shared_from_this()](int retcode)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: uniquename not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else if ((int)oss::OSSCode::OSSCODE_FIELD_EXIST == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: already used name = " + foo->_name, GNET::ERROR_ROAM_COMMUNITY_NAME_HAS_USED);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->Check2();
			}, 0, 0, 0, false);
		}

		void Check2()
		{
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID != retcode)
				{
					if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
					{
						OssUnlockShell shell(foo->_data_key);
						OSSInterface::GetInstance().HDel(foo->_unique_name_key, foo->_name, [](int retcode) {});
						FOO_ERR_FINISH_RETURN("Check2 failed: already exists key=" + foo->_data_key, GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
					}
					else
					{
						OSSInterface::GetInstance().HDel(foo->_unique_name_key, foo->_name, [](int retcode) {});
						FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
					}
				}

				foo->Do();
			}, true);
		}

		void Do()
		{
			OSSInterface::GetInstance().HSet(_ruid_key, std::to_string(_roleid), std::to_string(_uniqueid), [foo = shared_from_this()](int retcode)
			{
			}, 0, 0, 1, false);

			int64_t month_begin = GetLocalMonthBegin();
			auto base_ptr = _data.mutable_base();
			base_ptr->set_id(_uniqueid);
			base_ptr->set_name(_name);
			base_ptr->set_icon(_icon);
			base_ptr->set_leader(_roleid);
			base_ptr->set_active(0);
			base_ptr->set_announce(_announce);
			base_ptr->set_reset_time(month_begin);
			base_ptr->set_create_time(Timer::GetTime());
			base_ptr->set_resource_num(ROAM_COMMUNITY_CONFIG.start_resource_num);

			auto member_ptr = _data.add_members();
			member_ptr->set_roleid(_roleid);
			member_ptr->set_active(0);
			member_ptr->set_join_time(Timer::GetTime());
			member_ptr->set_isonline(1);
			member_ptr->set_zoneid(g_zoneid);
			member_ptr->set_fightcapacity(_fightcapacity);

			update_list_data(_uniqueid, _data, ULT_INSERT);

			std::string str_member;
			PB_2_STR(*member_ptr, str_member);
			PB::roam_community_data tmp_data;
			tmp_data.CopyFrom(_data);
			tmp_data.clear_members();
			std::string str_data;
			PB_2_STR(tmp_data, str_data);

			std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
			std::map<std::string, std::string> kv_map;
			kv_map[data_field] = str_data;
			kv_map[std::to_string(_roleid)] = str_member;
			OSSInterface::GetInstance().HMSet(_data_key, kv_map, [foo = shared_from_this()](int retcode)
			{
			}, false);

			OssUnlockShell shell(_data_key);
			Finish();
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::ipt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_CREATE);
				request_re.set_retcode(_retcode);
				request_re.set_txnid(_txnid);
				request_re.set_name(_name);
				request_re.set_icon(_icon);
				request_re.set_announce(_announce);
				if (_retcode == 0)
				{
					request_re.mutable_data()->CopyFrom(_data);
				}
				pRole->SendMessage2GS(request_re);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestCreate roleid=%ld, id=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};
	int retcode = 0;
	do
	{
		if (!request->has_name() || request->name() == "")
		{
			retcode = GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS;
			break;
		}
		/*
		if (SensitiveInterface::Match(request->name().data(), request->name().size(), SensitiveInterface::MATCH_NAME))
		{
			retcode = GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS;
			break;
		}
		if (request->has_announce() && SensitiveInterface::Match(request->announce().data(), request->announce().size(), SensitiveInterface::MATCH_NAME))
		{
			retcode = GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS;
			break;
		}
		*/
		SensitiveInterface::Match(pRole, kFuncCodeRoamCommunity, request->name().data(), request->name().size(), SensitiveInterface::MATCH_NAME,
		                          [roleid = pRole->roleid, request = std::make_shared<PB::ipt_roam_community_op>(*request)](bool ret, const GNET::Octets & data)->void
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(roleid);
			if (!pRole) return;
			if (!ret)
			{
				OnCallBack(pRole, request.get(), GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS);
				return;
			}
			else if (request->has_announce())
			{
				SensitiveInterface::Match(pRole, kFuncCodeRoamCommunity, request->announce().data(), request->announce().size(), SensitiveInterface::MATCH_NAME,
				[roleid = pRole->roleid, request = request](bool ret, const GNET::Octets & data)->void
				{
					RoleInfo *pRole = RoleMap::Instance().FindOnline(roleid);
					if (!pRole) return;
					if (!ret)
					{
						OnCallBack(pRole, request.get(), GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS);
						return;
					}
					else
					{
						int64_t uniqueid = AllocUniqueId(g_zoneid);
						std::shared_ptr<RCCreateProcess> process = make_shared<RCCreateProcess>(pRole->roleid, pRole->show_property.data().fightcapacity(), uniqueid, request->name(), request->announce(), request->icon(), request->txnid());
						process->Begin();
					}
				});
			}
			else
			{
				int64_t uniqueid = AllocUniqueId(g_zoneid);
				std::shared_ptr<RCCreateProcess> process = make_shared<RCCreateProcess>(pRole->roleid, pRole->show_property.data().fightcapacity(), uniqueid, request->name(), request->announce(), request->icon(), request->txnid());
				process->Begin();
			}
		});
		return;
	}
	while (false);

	if (retcode)
	{
		OnCallBack(pRole, request, retcode);
		return;
	}
}

void RoamCommunityManager::RequestAddHistory(RoleInfo *pRole, PB::ipt_roam_community_op *request)
{
	struct RCAddHistoryProcess: public std::enable_shared_from_this<RCAddHistoryProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;
		int _his_type;
		int64_t _param1;
		int64_t _param2;

		std::vector<int64_t> _roleids;

		PB::roam_community_data _data;
		PB::roam_community_data::history_record _history;
		RCAddHistoryProcess(ruid_t roleid, int his_type, int64_t param1, int64_t param2)
			: INIT_RET_VAR, _uniqueid(0), _roleid(roleid), _his_type(his_type), _param1(param1), _param2(param2)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->_uniqueid = atoll(data.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->Check2();
			}, false);
		}

		void Check2()
		{
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_roleid, isjoin);

				if (!isjoin)
				{
					// 这边要不要把ruid里面的值给清了
					OSSInterface::GetInstance().HDel(foo->_ruid_key, std::to_string(foo->_roleid), [](int retcode)
					{
					});
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			add_history_data(_data, (PB::roam_community_data::history_record::HISTORY_TYPE)_his_type, _roleid, _param1, _history, _param2);
			update_base_data(_uniqueid, _data);

			OssUnlockShell shell(_data_key);
			Finish();
		}

		void Finish()
		{
			if (_retcode == 0)
			{
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_EVENT);
				notify.set_roleid(_roleid);
				notify.mutable_history()->CopyFrom(_history);
				for (int i = 0; i < _roleids.size(); ++i)
				{
					notify.add_targetid(_roleids[i]);
				}
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
			}

			LOG_TRACE("DS::RoamCommunityManager::RCAddHistoryProcess roleid=%ld, id=%ld, his_type=%d, param1=%ld, param2=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _his_type, _param1, _param2, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCAddHistoryProcess> process = make_shared<RCAddHistoryProcess>(pRole->roleid, request->his_type(), request->param1(), request->param2());
	process->Begin();
}

void RoamCommunityManager::RequestAddResource(RoleInfo *pRole, PB::ipt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	if (!CampaignManager::GetInstance().IsActive(ROAM_COMMUNITY_CONFIG.activity_index_add_resource))
	{
		return;
	}

	struct RCDayActivityAddResourceProcess: public std::enable_shared_from_this<RCDayActivityAddResourceProcess>
	{
		DECLARE_RET_VAR

		int64_t _roleid;
		int64_t _uniqueid;
		int _real_add;

		std::string _ruid_key;
		std::string _data_key;

		PB::roam_community_data _data;
		PB::roam_community_data::member_info _self;
		PB::roam_community_data::history_record _history;
		std::vector<int64_t> _roleids;
		RCDayActivityAddResourceProcess(int64_t roleid)
			: INIT_RET_VAR, _roleid(roleid), _uniqueid(0), _real_add(0)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->_uniqueid = atoll(data.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);

				if (foo->_data.base().day_activity_resource() >= ROAM_COMMUNITY_CONFIG.max_add_num)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: max add num limit", GNET::ERROR_ROAM_COMMUNITY_MAX_ADD_NUM_LIMIT);
				}

				foo->Check2();
			});
		}

		void Check2()
		{
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_roleid, isjoin);

				if (!isjoin)
				{
					// 这边要不要把ruid里面的值给清了
					OSSInterface::GetInstance().HDel(foo->_ruid_key, std::to_string(foo->_roleid), [](int retcode)
					{
					});
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				for (int i = 0; i < foo->_data.members_size(); ++i)
				{
					if (foo->_data.members(i).roleid() == foo->_roleid)
					{
						foo->_self.CopyFrom(foo->_data.members(i));
						break;
					}
				}

				time_t now = Timer::GetTime();
				if (now - foo->_self.join_time() < RCMANAGER.GetJoinTimeLimit())
				{
					{
						OssUnlockShell shell(foo->_data_key);
					}
					FOO_ERR_FINISH_RETURN("Check2 failed: join time limit", GNET::ERROR_ROAM_COMMUNITY_JOIN_TIME_LIMIT);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			_real_add = _data.base().day_activity_resource() + ROAM_COMMUNITY_CONFIG.add_resource_num <= ROAM_COMMUNITY_CONFIG.max_add_num
			            ? ROAM_COMMUNITY_CONFIG.add_resource_num
			            : ROAM_COMMUNITY_CONFIG.max_add_num - _data.base().day_activity_resource();

			_data.mutable_base()->set_resource_num(_data.base().resource_num() + _real_add);
			_data.mutable_base()->set_day_activity_resource(_data.base().day_activity_resource() + _real_add);

			add_history_data(_data, PB::roam_community_data::history_record::RCHT_ADD_RESOURCE, _roleid, _real_add, _history);

			std::string str_data;
			PB_2_STR(_data, str_data);
			std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
			OSSInterface::GetInstance().HSet(_data_key, data_field, str_data, [uniqueid = _uniqueid, real_add = _real_add, cur_resource_num = _data.base().resource_num()](int retcode)
			{
				LOG_TRACE("DS::RoamCommunityManager::RCDayActivityAddResourceProcess add resource, uniqueid=%ld, resource_num=%d, cur_resource_num=%ld, retcode=%d", uniqueid, real_add, cur_resource_num, retcode);
			}, 0, 0, 1, false);

			update_list_data(_uniqueid, _data, ULT_NORMAL);
			{
				OssUnlockShell shell(_data_key);
			}

			Finish();
		}

		void Finish()
		{
			if (_retcode == 0)
			{
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_EVENT);
				notify.mutable_history()->CopyFrom(_history);
				for (int i = 0; i < _roleids.size(); ++i)
				{
					notify.add_targetid(_roleids[i]);
				}
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);

				std::vector<int64_t> uniqueids;
				uniqueids.push_back(_uniqueid);
				RCMANAGER.SendNotifyUpdateData(uniqueids);
			}
			else
			{
				LOG_TRACE("DS::RoamCommunityManager::RCDayActivityAddResourceProcess failed, roleid=%ld, add_num=%d, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _real_add, _err_msg.c_str(), _redis_ret, _retcode);
			}
		}
	};

	std::shared_ptr<RCDayActivityAddResourceProcess> process = make_shared<RCDayActivityAddResourceProcess>(pRole->roleid);
	process->Begin();
}

void RoamCommunityManager::NptOp(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!pRole || !request)
	{
		return;
	}

	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunity))
	{
		PB::npt_roam_community_op_re request_re;
		request_re.set_op(request->op());
		request_re.set_retcode(GNET::ERROR_FUNC_CLOSED_TEMPORARY);
		pRole->SendMessage2Client(request_re);
		return;
	}

	switch (request->op())
	{
	case PB::ROAM_COMMUNITY_OP::RCO_INVITE:
		RequestInvite(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_KICK:
		RequestKick(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_LEAVE:
		RequestLeave(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_TRANSFER:
		RequestTransfer(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_DISMISS:
		RequestDismiss(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_APPLY:
		RequestApply(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_APPROVE:
		RequestApprove(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_RENAME:
		RequestRename(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_ICON:
		RequestIcon(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_ANNOUNCE:
		RequestAnnounce(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_CLEAR_APPLY:
		RequestClearApply(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_SEARCH:
		RequestSearch(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_VOTE:
		RequestVote(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_ENHANCE_DEFENSE:
		RequestEnhanceDefense(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_REQUEST_POINT_DATA:
		RequestPointData(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_APPLY_BATTLE:
		RequestApplyBattle(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_REQUEST_POINT_DETAIL:
		RequestPointDetail(pRole, request);
		break;
	//case PB::ROAM_COMMUNITY_OP::RCO_GET_RANK:
	//	RequestRank(pRole, request);
	//	break;
	case PB::ROAM_COMMUNITY_OP::RCO_ASK_RANK_REWARD_FLAG:
		RequestAskRankRewardFlag(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_GET_BATTLE_REWARD:
		RequestGetBattleReward(pRole, request);
		break;
	case PB::ROAM_COMMUNITY_OP::RCO_GET_SEASON_REWARD:
		RequestGetSeasonReward(pRole, request);
		break;
	default:
		break;
	}
}

void RoamCommunityManager::RequestInvite(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	struct RCInviteProcess: public std::enable_shared_from_this<RCInviteProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		int64_t _uniqueid;
		ruid_t _roleid;
		std::string _rolename;
		ruid_t _target_roleid;

		PB::roam_community_data _data;
		RCInviteProcess(ruid_t roleid, const std::string& rolename, ruid_t target_roleid)
			: INIT_RET_VAR, _uniqueid(0), _roleid(roleid), _rolename(rolename), _target_roleid(target_roleid)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			std::vector<std::string> role_fields;
			role_fields.push_back(std::to_string(_roleid));
			role_fields.push_back(std::to_string(_target_roleid));
			OSSInterface::GetInstance().HMGet(_ruid_key, role_fields, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				auto iter = data_map.find(std::to_string(foo->_roleid));
				if (iter == data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community2", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				auto iter_target = data_map.find(std::to_string(foo->_target_roleid));
				if (iter_target != data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: target has in roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_HAS_JOIN);
				}

				foo->_uniqueid = atoll(iter->second.c_str());
				foo->Finish();
			});
		}

		void Finish()
		{
			do
			{
				if (_retcode != 0)
				{
					break;
				}

				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_INVITE);
				notify.set_roleid(_roleid);
				notify.set_content(_rolename);
				notify.set_communityid(_uniqueid);
				notify.add_targetid(_target_roleid);

				int zoneid = MERGE_ZONE(_target_roleid);
				if (zoneid != g_zoneid)
				{
					NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
					break;
				}

				// 只剩本服的情况了
				RoleInfo *pTarget = RoleMap::Instance().FindOnline(_target_roleid);
				if (!pTarget)
				{
					_err_msg = "Check2 failed: target not online";
					_retcode = GNET::ERROR_ROAM_COMMUNITY_TARGET_OFFLINE;
					break;
				}

				CommonInviteArg arg;
				arg.roleid = _roleid;
				arg.target = _target_roleid;
				arg.isinvite = 0;
				arg.localsid = pTarget->localsid;
				arg.dtype = (unsigned short)notify.type();
				arg.datainfo.resize(notify.ByteSize());
				notify.SerializeWithCachedSizesToArray((uint8_t *)arg.datainfo.begin());
				pTarget->SendInvite2Client(arg);
			}
			while (false);

			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_INVITE);
				request_re.set_retcode(_retcode);
				pRole->SendMessage2Client(request_re);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestInvite roleid=%ld, id=%ld, target_roleid=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _target_roleid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::string name((const char *)pRole->GetShowName().begin(), pRole->GetShowName().size());
	std::shared_ptr<RCInviteProcess> process = make_shared<RCInviteProcess>(pRole->roleid, name, request->targetid());
	process->Begin();
}

void RoamCommunityManager::RequestKick(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	struct RCKickProcess: public std::enable_shared_from_this<RCKickProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;
		ruid_t _targetid;

		std::vector<int64_t> _roleids;

		PB::roam_community_data _data;
		PB::roam_community_data::history_record _history;
		RCKickProcess(ruid_t roleid, ruid_t target_roleid)
			: INIT_RET_VAR, _uniqueid(0), _roleid(roleid), _targetid(target_roleid)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			std::vector<std::string> role_fields;
			role_fields.push_back(std::to_string(_roleid));
			role_fields.push_back(std::to_string(_targetid));
			OSSInterface::GetInstance().HMGet(_ruid_key, role_fields, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				auto iter = data_map.find(std::to_string(foo->_roleid));
				if (iter == data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community2", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				auto iter_target = data_map.find(std::to_string(foo->_targetid));
				if (iter_target == data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: target not in roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN);
				}

				if (iter_target->second != iter->second)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in same roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN_THIS);
				}

				foo->_uniqueid = atoll(iter->second.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_targetid, isjoin);

				if (foo->_data.base().leader() != foo->_roleid)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (!isjoin)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: target not in roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			OSSInterface::GetInstance().HDel(_data_key, std::to_string(_targetid), [](int retcode)
			{
			});
			OSSInterface::GetInstance().HDel(_ruid_key, std::to_string(_targetid), [](int retcode)
			{
			});

			int members_size = _data.members_size();
			for (int i = 0; i < members_size; ++i)
			{
				auto member_ptr = _data.mutable_members(i);
				if (member_ptr->roleid() == _targetid)
				{
					if (i != members_size - 1)
					{
						_data.mutable_members()->SwapElements(i, members_size - 1);
					}
					_data.mutable_members()->RemoveLast();

					RoleInfo *pRole = RoleMap::Instance().Find(_targetid);
					if (pRole)
					{
						pRole->SetReputation(ROAM_COMMUNITY_CONFIG.battle_reward_repu, 0);
						pRole->SetReputation(ROAM_COMMUNITY_CONFIG.season_reward_repu, 0);
					}
					break;
				}
			}

			add_history_data(_data, PB::roam_community_data::history_record::RCHT_KICK, _targetid, 0, _history);
			update_base_data(_uniqueid, _data);
			update_list_data(_uniqueid, _data, ULT_NORMAL);

			Finish();
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_KICK);
				request_re.set_retcode(_retcode);
				request_re.set_targetid(_targetid);
				pRole->SendMessage2Client(request_re);
			}

			if (_retcode == 0)
			{
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_KICK);
				notify.set_roleid(_targetid);
				notify.mutable_history()->CopyFrom(_history);
				for (int i = 0; i < _roleids.size(); ++i)
				{
					notify.add_targetid(_roleids[i]);
				}
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
				RCMANAGER.SyncUniqueid(_targetid, 0);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestKick roleid=%ld, id=%ld, target_roleid=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _targetid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCKickProcess> process = make_shared<RCKickProcess>(pRole->roleid, request->targetid());
	process->Begin();
}

void RoamCommunityManager::RequestLeave(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	struct RCLeaveProcess: public std::enable_shared_from_this<RCLeaveProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;

		std::vector<int64_t> _roleids;

		PB::roam_community_data _data;
		PB::roam_community_data::history_record _history;
		RCLeaveProcess(ruid_t roleid)
			: INIT_RET_VAR, _uniqueid(0), _roleid(roleid)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			std::vector<std::string> role_fields;
			role_fields.push_back(std::to_string(_roleid));
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->_uniqueid = atoll(data.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_roleid, isjoin);

				if (foo->_data.base().leader() == foo->_roleid)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: is leader", GNET::ERROR_ROAM_COMMUNITY_LEADER_CANNOT_LEAVE);
				}

				if (!isjoin)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			OSSInterface::GetInstance().HDel(_data_key, std::to_string(_roleid), [](int retcode)
			{
			});
			OSSInterface::GetInstance().HDel(_ruid_key, std::to_string(_roleid), [](int retcode)
			{
			});

			int members_size = _data.members_size();
			for (int i = 0; i < members_size; ++i)
			{
				auto member_ptr = _data.mutable_members(i);
				if (member_ptr->roleid() == _roleid)
				{
					if (i != members_size - 1)
					{
						_data.mutable_members()->SwapElements(i, members_size - 1);
					}
					_data.mutable_members()->RemoveLast();
					RoleInfo *pRole = RoleMap::Instance().Find(_roleid);
					if (pRole)
					{
						pRole->SetReputation(ROAM_COMMUNITY_CONFIG.battle_reward_repu, 0);
						pRole->SetReputation(ROAM_COMMUNITY_CONFIG.season_reward_repu, 0);
					}
					break;
				}
			}

			add_history_data(_data, PB::roam_community_data::history_record::RCHT_LEAVE, _roleid, 0, _history);
			update_base_data(_uniqueid, _data);
			update_list_data(_uniqueid, _data, ULT_NORMAL);

			Finish();
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_LEAVE);
				request_re.set_retcode(_retcode);
				pRole->SendMessage2Client(request_re);
			}

			if (_retcode == 0)
			{
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_LEAVE);
				notify.set_roleid(_roleid);
				notify.mutable_history()->CopyFrom(_history);
				for (int i = 0; i < _roleids.size(); ++i)
				{
					notify.add_targetid(_roleids[i]);
				}
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
				RCMANAGER.SyncUniqueid(_roleid, 0);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestLeave roleid=%ld, id=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCLeaveProcess> process = make_shared<RCLeaveProcess>(pRole->roleid);
	process->Begin();
}

void RoamCommunityManager::RequestTransfer(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	struct RCTransferProcess: public std::enable_shared_from_this<RCTransferProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;
		ruid_t _targetid;

		std::vector<int64_t> _roleids;

		PB::roam_community_data _data;
		PB::roam_community_data::history_record _history;
		RCTransferProcess(ruid_t roleid, ruid_t target_roleid)
			: INIT_RET_VAR, _uniqueid(0), _roleid(roleid), _targetid(target_roleid)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			std::vector<std::string> role_fields;
			role_fields.push_back(std::to_string(_roleid));
			role_fields.push_back(std::to_string(_targetid));
			OSSInterface::GetInstance().HMGet(_ruid_key, role_fields, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				auto iter = data_map.find(std::to_string(foo->_roleid));
				if (iter == data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community2", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				auto iter_target = data_map.find(std::to_string(foo->_targetid));
				if (iter_target == data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: target not in roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN);
				}

				if (iter_target->second != iter->second)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in same roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN_THIS);
				}

				foo->_uniqueid = atoll(iter->second.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID ==  retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);

				foo->Check2();
			}, false);
		}

		void Check2()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_targetid, isjoin);

				if (foo->_data.base().leader() != foo->_roleid)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (!isjoin)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: target not in roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			_data.mutable_base()->set_leader(_targetid);

			add_history_data(_data, PB::roam_community_data::history_record::RCHT_TRANSFER, _targetid, _roleid, _history);
			update_base_data(_uniqueid, _data);
			update_list_data(_uniqueid, _data, ULT_NORMAL);

			OssUnlockShell shell(_data_key);
			Finish();
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_TRANSFER);
				request_re.set_retcode(_retcode);
				request_re.set_targetid(_targetid);
				pRole->SendMessage2Client(request_re);
			}

			if (_retcode == 0)
			{
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_TRANSFER);
				notify.set_roleid(_targetid);
				notify.mutable_history()->CopyFrom(_history);
				for (int i = 0; i < _roleids.size(); ++i)
				{
					notify.add_targetid(_roleids[i]);
				}
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestTransfer roleid=%ld, id=%ld, target_roleid=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _targetid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCTransferProcess> process = make_shared<RCTransferProcess>(pRole->roleid, request->targetid());
	process->Begin();
}

void RoamCommunityManager::RequestDismiss(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	struct RCDismissProcess: public std::enable_shared_from_this<RCDismissProcess>
	{
		DECLARE_RET_VAR

		std::string _unique_name_key;
		std::string _list_key;
		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;

		PB::roam_community_data _data;
		RCDismissProcess(ruid_t roleid)
			: INIT_RET_VAR, _uniqueid(0), _roleid(roleid)
		{
			_unique_name_key = GetOssKeyWord(OSS_RC_UNIQUENAME, 0, "TEST");
			_list_key = GetOssKeyWord(OSS_RC_LIST, 0, "TEST");
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			std::vector<std::string> role_fields;
			role_fields.push_back(std::to_string(_roleid));
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->_uniqueid = atoll(data.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				std::vector<int64_t> roleids;
				bool isjoin = false;
				get_community_data(data_map, roleids, foo->_data, 0, isjoin);

				if (foo->_data.base().leader() != foo->_roleid)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (foo->_data.members_size() != 1)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: has other member", GNET::ERROR_ROAM_COMMUNITY_HAS_OTHER_MEMBER);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			OSSInterface::GetInstance().HDel(_list_key, std::to_string(_uniqueid), [](int retcode)
			{
			});

			oss::Del(_data_key, [](int retcode)
			{
			});

			OSSInterface::GetInstance().HDel(_ruid_key, std::to_string(_roleid), [](int retcode)
			{
			});

			OSSInterface::GetInstance().HDel(_unique_name_key, _data.base().name(), [](int retcode)
			{
			});

			RoleInfo *pRole = RoleMap::Instance().Find(_roleid);
			if (pRole)
			{
				pRole->SetReputation(ROAM_COMMUNITY_CONFIG.battle_reward_repu, 0);
				pRole->SetReputation(ROAM_COMMUNITY_CONFIG.season_reward_repu, 0);
			}

			update_list_data(_uniqueid, _data, ULT_DELETE);

			Finish();
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_DISMISS);
				request_re.set_retcode(_retcode);
				pRole->SendMessage2Client(request_re);
			}
			if (_retcode == 0)
			{
				RCMANAGER.SyncUniqueid(_roleid, 0);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestDismiss roleid=%ld, id=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCDismissProcess> process = make_shared<RCDismissProcess>(pRole->roleid);
	process->Begin();
}

void RoamCommunityManager::RequestApply(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	struct RCApplyProcess: public std::enable_shared_from_this<RCApplyProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		int64_t _uniqueid;
		uint32_t _fightcapacity;

		PB::roam_community_data::applyer_info _apply;
		PB::roam_community_data _data;
		RCApplyProcess(ruid_t roleid, int64_t uniqueid, uint32_t fp)
			: INIT_RET_VAR, _roleid(roleid), _uniqueid(uniqueid), _fightcapacity(fp)
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: already in roam community=" + data, GNET::ERROR_ROAM_COMMUNITY_SELF_HAS_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->Check1();
			});
		}

		void Check1()
		{
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID ==  retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);

				foo->Check2();
			}, false);
		}

		void Check2()
		{
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				bool isjoin = false;
				std::vector<int64_t> roleids;
				get_community_data(data_map, roleids, foo->_data, 0, isjoin);

				//if (foo->_data.applyers_size() >= ROAM_COMMUNITY_CONFIG.max_apply)
				//{
				//	OssUnlockShell shell(foo->_data_key);
				//	FOO_ERR_FINISH_RETURN("Check2 failed: member full", GNET::ERROR_ROAM_COMMUNITY_MEMBER_FULL);
				//}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			bool has_apply = false;
			int applyers_size = _data.applyers_size();
			for (int i = 0; i < applyers_size; ++i)
			{
				auto apply_ptr = _data.mutable_applyers(i);
				if (apply_ptr->roleid() == _roleid)
				{
					has_apply = true;
					break;
				}
			}

			if (!has_apply)
			{
				// 加入申请列表
				auto applyer_ptr = _data.add_applyers();
				applyer_ptr->set_roleid(_roleid);
				applyer_ptr->set_fightcapacity(_fightcapacity);
				_apply.CopyFrom(*applyer_ptr);

				int applyers_size = _data.applyers_size();
				if (applyers_size > ROAM_COMMUNITY_CONFIG.max_apply)
				{
					for (int i = 0; i < applyers_size - 1; ++i)
					{
						_data.mutable_applyers()->SwapElements(i, i + 1);
					}
					_data.mutable_applyers()->RemoveLast();
				}
			}

			update_base_data(_uniqueid, _data);
			update_list_data(_uniqueid, _data, ULT_NORMAL);

			OssUnlockShell shell(_data_key);

			Finish();
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_APPLY);
				request_re.set_retcode(_retcode);
				pRole->SendMessage2Client(request_re);
			}

			if (_retcode == 0 && _apply.has_roleid())
			{
				// 通知leader有申请？
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_APPLY);
				notify.set_roleid(_roleid);
				notify.mutable_apply()->CopyFrom(_apply);
				notify.add_targetid(_data.base().leader());

				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestApply roleid=%ld, id=%ld, errmsg=%s, reids_ret=%d", _roleid, _uniqueid, _err_msg.c_str(), _redis_ret);
		}
	};

	std::shared_ptr<RCApplyProcess> process = make_shared<RCApplyProcess>(pRole->roleid, request->communityid(), pRole->show_property.data().fightcapacity());
	process->Begin();
}

void RoamCommunityManager::RequestApprove(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	struct RCApproveProcess: public std::enable_shared_from_this<RCApproveProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		ruid_t _targetid;
		int64_t _uniqueid;
		int _isagree;

		std::vector<int64_t> _roleids;

		PB::roam_community_data::member_info _member;
		PB::roam_community_data _data;
		PB::roam_community_data::history_record _history;
		RCApproveProcess(ruid_t roleid, int64_t targetid, int isagree)
			: INIT_RET_VAR, _roleid(roleid), _targetid(targetid), _uniqueid(0), _isagree(isagree)
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			std::vector<std::string> role_fields;
			role_fields.push_back(std::to_string(_roleid));
			role_fields.push_back(std::to_string(_targetid));
			OSSInterface::GetInstance().HMGet(_ruid_key, role_fields, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				auto iter = data_map.find(std::to_string(foo->_roleid));
				if (iter == data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community2", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				auto iter_target = data_map.find(std::to_string(foo->_targetid));
				if (iter_target != data_map.end())
				{
					FOO_ERR_FINISH_RETURN("Begin failed: target has in roam community", GNET::ERROR_ROAM_COMMUNITY_TARGET_HAS_JOIN);
				}

				foo->_uniqueid = atoll(iter->second.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID ==  retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);

				foo->Check2();
			}, false);
		}

		void Check2()
		{
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_targetid, isjoin);

				if (foo->_data.base().leader() != foo->_roleid)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (foo->_isagree == 0)
				{
					foo->_err_msg = "Check2 failed: leader refuse";
					foo->_retcode = GNET::ERROR_ROAM_COMMUNITY_TARGET_REFUSE_APPLY;
					foo->Do();
				}
				else if (foo->_data.members_size() >= ROAM_COMMUNITY_CONFIG.max_member)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: member full", GNET::ERROR_ROAM_COMMUNITY_MEMBER_FULL);
				}
				else
				{
					foo->Do();
				}
			}, 0, false, false);
		}

		void Do()
		{
			PB::roam_community_data::applyer_info apply;
			int applyers_size = _data.applyers_size();
			for (int i = 0; i < applyers_size; ++i)
			{
				auto apply_ptr = _data.mutable_applyers(i);
				if (apply_ptr->roleid() == _targetid)
				{
					apply.CopyFrom(*apply_ptr);
					if (i != applyers_size - 1)
					{
						_data.mutable_applyers()->SwapElements(i, applyers_size - 1);
					}
					_data.mutable_applyers()->RemoveLast();
					break;
				}
			}
			if (!apply.has_roleid())
			{
				ERR_FINISH_RETURN("Do failed: no this player apply info", GNET::ERROR_ROAM_COMMUNITY_NO_THIS_APPLY_INFO);
			}

			if (_retcode == 0)
			{
				OSSInterface::GetInstance().HSet(_ruid_key, std::to_string(_targetid), std::to_string(_uniqueid), [foo = shared_from_this()](int retcode)
				{
				}, 0, 0, 1, false);

				auto member_ptr = _data.add_members();
				member_ptr->set_roleid(_targetid);
				member_ptr->set_active(0);
				member_ptr->set_join_time(Timer::GetTime());
				member_ptr->set_isonline(0);
				member_ptr->set_zoneid(g_zoneid);
				member_ptr->set_fightcapacity(apply.fightcapacity());

				_member.CopyFrom(*member_ptr);
				_roleids.push_back(_targetid);

				std::string str_member;
				PB_2_STR(_member, str_member);
				OSSInterface::GetInstance().HSet(_data_key, std::to_string(_targetid), str_member, [foo = shared_from_this()](int retcode)
				{
				}, 0, 0, 1, false);

				add_history_data(_data, PB::roam_community_data::history_record::RCHT_ENTER, _targetid, 0, _history);
			}

			update_base_data(_uniqueid, _data);
			update_list_data(_uniqueid, _data, ULT_NORMAL);

			OssUnlockShell shell(_data_key);

			Finish();
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_APPROVE);
				//request_re.set_agree(_isagree);
				request_re.set_targetid(_targetid);
				request_re.set_retcode(_retcode == GNET::ERROR_ROAM_COMMUNITY_TARGET_REFUSE_APPLY ? 0 : _retcode);
				pRole->SendMessage2Client(request_re);
			}

			if (_retcode == 0)
			{
				// 这边通知自己和会员都是什么字段
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_ENTER);
				notify.set_roleid(_targetid);
				notify.mutable_data()->CopyFrom(_data);
				notify.mutable_member()->CopyFrom(_member);
				notify.mutable_history()->CopyFrom(_history);
				for (int i = 0; i < _roleids.size(); ++i)
				{
					notify.add_targetid(_roleids[i]);
				}
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
				RCMANAGER.SyncUniqueid(_targetid, _data.base().id());
			}
			else if (_retcode == GNET::ERROR_ROAM_COMMUNITY_TARGET_REFUSE_APPLY)
			{
				// 这边通知自己和会员都是什么字段
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_REFUSE);
				notify.add_targetid(_targetid);
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestApprove roleid=%ld, id=%ld, targetid=%ld, errmsg=%s, reids_ret=%d", _roleid, _uniqueid, _targetid, _err_msg.c_str(), _redis_ret);
		}
	};

	std::shared_ptr<RCApproveProcess> process = make_shared<RCApproveProcess>(pRole->roleid, request->targetid(), 1);
	//std::shared_ptr<RCApproveProcess> process = make_shared<RCApproveProcess>(pRole->roleid, request->targetid(), request->agree());
	process->Begin();
}

struct RCModifyDataProcess: public std::enable_shared_from_this<RCModifyDataProcess>
{
	DECLARE_RET_VAR

	std::string _ruid_key;
	std::string _data_key;
	int64_t _uniqueid;
	ruid_t _roleid;
	std::string _old_name;
	std::string _unique_name_key;

	struct DataParam
	{
		int op;
		std::string name;
		int icon;
		std::string announce;
	};

	DataParam _param;

	std::vector<int64_t> _roleids;

	PB::roam_community_data _data;
	PB::roam_community_data::history_record _history;
	RCModifyDataProcess(ruid_t roleid, const DataParam& param)
		: INIT_RET_VAR, _uniqueid(0), _roleid(roleid), _param(param)
	{
		_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		_unique_name_key = GetOssKeyWord(OSS_RC_UNIQUENAME, 0, "TEST");
	}

	void Begin()
	{
		OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID ==  retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
			}
			else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			foo->_uniqueid = atoll(data.c_str());
			foo->Check1();
		});
	}

	void Check1()
	{
		_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
		OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID ==  retcode)
			{
				FOO_ERR_FINISH_RETURN("Check1 failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
			}
			else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			STR_2_PB(data, foo->_data);

			if (foo->_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_ICON)
			{
				if (Timer::GetTime() - foo->_data.base().modify_icon_time() < ROAM_COMMUNITY_CONFIG.modify_icon_cd)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: modify cooldown", GNET::ERROR_ROAM_COMMUNITY_MODIFY_ICON_CD);
				}
			}
			else if (foo->_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_ANNOUNCE)
			{
				if (Timer::GetTime() - foo->_data.base().modify_announce_time() < ROAM_COMMUNITY_CONFIG.modify_announce_cd)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: modify cooldown", GNET::ERROR_ROAM_COMMUNITY_MODIFY_ANNOUNCE_CD);
				}
			}
			foo->Check2();
		}, false);
	}

	void Check2()
	{
		OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				OssUnlockShell shell(foo->_data_key);
				FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			bool isjoin = false;
			get_community_data(data_map, foo->_roleids, foo->_data, 0, isjoin);

			if (foo->_data.base().leader() != foo->_roleid)
			{
				OssUnlockShell shell(foo->_data_key);
				FOO_ERR_FINISH_RETURN("Check2 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
			}

			if (foo->_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_RENAME)
			{
				foo->Check3();
			}
			else
			{
				foo->Do();
			}
		}, 0, false, false);
	}

	void Check3()
	{
		OSSInterface::GetInstance().HSet(_unique_name_key, _param.name, std::to_string(_uniqueid), [foo = shared_from_this()](int retcode)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_GET_DATA_INVALID == retcode)
			{
				FOO_ERR_FINISH_RETURN("Check3 failed: uniquename not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}
			else if ((int)oss::OSSCode::OSSCODE_FIELD_EXIST == retcode)
			{
				FOO_ERR_FINISH_RETURN("Check3 failed: already used name = " + foo->_param.name, GNET::ERROR_ROAM_COMMUNITY_NAME_HAS_USED);
			}
			else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check3 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			foo->Do();
		}, 0, 0, 0, false);
	}

	void Do()
	{
		if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_RENAME)
		{
			_old_name =  _data.mutable_base()->name();
			_data.mutable_base()->set_name(_param.name);
		}
		else if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_ICON)
		{
			_data.mutable_base()->set_icon(_param.icon);
			_data.mutable_base()->set_modify_icon_time(Timer::GetTime());
		}
		else if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_ANNOUNCE)
		{
			_data.mutable_base()->set_announce(_param.announce);
			_data.mutable_base()->set_modify_announce_time(Timer::GetTime());
			add_history_data(_data, PB::roam_community_data::history_record::RCHT_ANNOUNCE, _roleid, 0, _history);
		}
		else if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_CLEAR_APPLY)
		{
			_data.clear_applyers();
		}

		update_base_data(_uniqueid, _data);
		update_list_data(_uniqueid, _data, ULT_NORMAL);

		if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_RENAME)
		{
			if (_old_name != _param.name)
			{
				OSSInterface::GetInstance().HDel(_unique_name_key, _old_name, [](int retcode) {});
			}
		}

		OssUnlockShell shell(_data_key);
		Finish();
	}

	void Finish()
	{
		RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
		if (pRole)
		{
			PB::npt_roam_community_op_re request_re;
			request_re.set_op((PB::ROAM_COMMUNITY_OP)_param.op);
			request_re.set_retcode(_retcode);
			pRole->SendMessage2Client(request_re);
		}

		if (_retcode == 0 && _param.op != (int)PB::ROAM_COMMUNITY_OP::RCO_CLEAR_APPLY)
		{
			PB::npt_roam_community_notify notify;
			notify.set_op((PB::ROAM_COMMUNITY_OP)_param.op);
			if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_RENAME)
			{
				notify.set_content(_param.name);
			}
			else if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_ICON)
			{
				notify.set_icon(_param.icon);
			}
			else if (_param.op == (int)PB::ROAM_COMMUNITY_OP::RCO_ANNOUNCE)
			{
				notify.set_content(_param.announce);
				notify.mutable_history()->CopyFrom(_history);
			}

			for (int i = 0; i < _roleids.size(); ++i)
			{
				notify.add_targetid(_roleids[i]);
			}
			NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
		}

		LOG_TRACE("DS::RoamCommunityManager::RCModifyDataProcess roleid=%ld, id=%ld, op=%d, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _param.op, _err_msg.c_str(), _redis_ret, _retcode);
	}
};

void RoamCommunityManager::RequestRename(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	/*
	int retcode = 0;
	do
	{
		if (SensitiveInterface::Match(request->content().data(), request->content().size(), SensitiveInterface::MATCH_NAME))
		{
			retcode = GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS;
			break;
		}
	}
	while (false);
	*/

	SensitiveInterface::Match(pRole, kFuncCodeRoamCommunity, request->content().data(), request->content().size(), SensitiveInterface::MATCH_NAME,
	                          [roleid = pRole->roleid, request = std::make_shared<PB::npt_roam_community_op>(*request)](bool ret, const GNET::Octets & data)->void
	{
		RoleInfo *pRole = RoleMap::Instance().FindOnline(roleid);
		if (!pRole) return;
		if (!ret)
		{
			PB::npt_roam_community_op_re request_re;
			request_re.set_op(request->op());
			request_re.set_retcode(GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS);
			pRole->SendMessage2Client(request_re);
			LOG_TRACE("DS::RoamCommunityManager::RequestRename invalid name roleid=%ju name=%s", pRole->roleid, request->content().c_str());
			return;
		}

		RCModifyDataProcess::DataParam param;
		param.op = (int)request->op();
		param.name = request->content();
		std::shared_ptr<RCModifyDataProcess> process = make_shared<RCModifyDataProcess>(pRole->roleid, param);
		process->Begin();
	});
}

void RoamCommunityManager::RequestIcon(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	RCModifyDataProcess::DataParam param;
	param.op = (int)request->op();
	param.icon = request->icon();
	std::shared_ptr<RCModifyDataProcess> process = make_shared<RCModifyDataProcess>(pRole->roleid, param);
	process->Begin();
}

void RoamCommunityManager::RequestAnnounce(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	/*
	int retcode = 0;
	do
	{
		if (SensitiveInterface::Match(request->content().data(), request->content().size(), SensitiveInterface::MATCH_NAME))
		{
			retcode = GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS;
			break;
		}
	}
	while (false);
	*/
	SensitiveInterface::Match(pRole, kFuncCodeRoamCommunity, request->content().data(), request->content().size(), SensitiveInterface::MATCH_NAME,
	                          [roleid = pRole->roleid, request = std::make_shared<PB::npt_roam_community_op>(*request)](bool ret, const GNET::Octets & data)->void
	{
		RoleInfo *pRole = RoleMap::Instance().FindOnline(roleid);
		if (!pRole) return;
		if (!ret)
		{
			PB::npt_roam_community_op_re request_re;
			request_re.set_op(request->op());
			request_re.set_retcode(GNET::ERROR_ROAM_COMMUNITY_BAD_WARDS);
			pRole->SendMessage2Client(request_re);
			LOG_TRACE("DS::RoamCommunityManager::RequestAnnounce invalid announce roleid=%ju name=%s", pRole->roleid, request->content().c_str());
			return;
		}

		RCModifyDataProcess::DataParam param;
		param.op = (int)request->op();
		param.announce = request->content();
		std::shared_ptr<RCModifyDataProcess> process = make_shared<RCModifyDataProcess>(pRole->roleid, param);
		process->Begin();
	});
}

void RoamCommunityManager::RequestClearApply(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	RCModifyDataProcess::DataParam param;
	param.op = (int)request->op();
	std::shared_ptr<RCModifyDataProcess> process = make_shared<RCModifyDataProcess>(pRole->roleid, param);
	process->Begin();
}

void RoamCommunityManager::RequestSearch(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	bool has_content = (request->has_content() && request->content() != "");

	std::vector<PB::roam_community_brief_data> tmp_list;
	for (auto iter = _brief_list.begin(); iter != _brief_list.end(); ++iter)
	{
		if ((!has_content && iter->member_num() < ROAM_COMMUNITY_CONFIG.max_member) || (has_content && iter->base().name() == request->content()))
		{
			tmp_list.push_back(*iter);
		}
	}

	PB::npt_roam_community_op_re request_re;
	request_re.set_op(request->op());
	request_re.set_page(request->page());
	request_re.set_content(request->content());
	request_re.set_total((int)tmp_list.size());

	int start = request->page() * RC_PAGE_SIZE;
	int end = (request->page() + 1) * RC_PAGE_SIZE;
	for (int i = start; i < end && i < tmp_list.size(); ++i)
	{
		auto brief_ptr = request_re.add_list();
		brief_ptr->CopyFrom(tmp_list[i]);
	}

	pRole->SendMessage2Client(request_re);

}

struct RCModifyMemberDataProcess: public std::enable_shared_from_this<RCModifyMemberDataProcess>
{
	DECLARE_RET_VAR

	std::string _ruid_key;
	std::string _data_key;
	int64_t _uniqueid;
	ruid_t _roleid;
	int _fightcapacity;
	int _active;

	enum MODIFY_TYPE
	{
		MT_LOGIN		= 1,
		MT_LOGOUT		= 2,
		MT_UPDATE		= 3,
	};
	MODIFY_TYPE _type;

	std::vector<int64_t> _roleids;

	PB::roam_community_data _data;
	PB::roam_community_data::member_info _self;
	RCModifyMemberDataProcess(ruid_t roleid, int fp, int active, int type)
		: INIT_RET_VAR, _uniqueid(0), _roleid(roleid), _fightcapacity(fp), _active(active), _type((MODIFY_TYPE)type)
	{
		_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
	}

	void Begin()
	{
		OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID ==  retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
			}
			else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			foo->_uniqueid = atoll(data.c_str());
			foo->Check1();
		});
	}

	void Check1()
	{
		_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
		OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
			}

			bool isjoin = false;
			get_community_data(data_map, foo->_roleids, foo->_data, foo->_roleid, isjoin);

			if (!isjoin)
			{
				// 这边要不要把ruid里面的值给清了
				OSSInterface::GetInstance().HDel(foo->_ruid_key, std::to_string(foo->_roleid), [](int retcode)
				{
				});
				FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
			}

			foo->Do();
		}, 0, false, false);
	}

	void Do()
	{
		for (int i = 0; i < _data.members_size(); ++i)
		{
			if (_data.members(i).roleid() == _roleid)
			{
				int isonline = _type != MT_LOGOUT;
				auto member_ptr = _data.mutable_members(i);
				member_ptr->set_isonline(isonline);
				member_ptr->set_zoneid(g_zoneid);
				member_ptr->set_fightcapacity(_fightcapacity);
				member_ptr->set_active(_active);

				_self.CopyFrom(*member_ptr);

				std::string str_member;
				PB_2_STR(*member_ptr, str_member);
				OSSInterface::GetInstance().HSet(_data_key, std::to_string(_roleid), str_member, [foo = shared_from_this()](int retcode)
				{
				}, 0, 0, 1, false);
				break;
			}
		}
		Finish();
	}

	void Finish()
	{
		if (_retcode == 0)
		{
			if (_retcode == 0 && _type == MT_LOGIN)
			{
				RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
				if (pRole)
				{
					PB::npt_roam_community_notify notify;
					notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_SYNC);
					notify.mutable_data()->CopyFrom(_data);
					pRole->SendMessage2Client(notify);
				}
				RCMANAGER.SyncUniqueid(_roleid, _data.base().id());
			}

			PB::npt_roam_community_notify notify;
			notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_MEMBER);
			notify.set_roleid(_roleid);
			notify.mutable_member()->CopyFrom(_self);
			for (int i = 0; i < _roleids.size(); ++i)
			{
				notify.add_targetid(_roleids[i]);
			}
			NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
		}

		LOG_TRACE("DS::RoamCommunityManager::ModifyMemberData roleid=%ld, id=%ld, type=%d, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _type, _err_msg.c_str(), _redis_ret, _retcode);
	}
};

void RoamCommunityManager::OnLogin(RoleInfo *pRole)
{
	int fp = pRole->show_property.data().fightcapacity();
	int active = pRole->GetReputation(REPUID_ROAM_COMMUNITY_ACTIVE);
	std::shared_ptr<RCModifyMemberDataProcess> process = make_shared<RCModifyMemberDataProcess>(pRole->roleid, fp, active, 1);
	process->Begin();
}

void RoamCommunityManager::OnPlayerLogout(RoleInfo *pRole)
{
	int fp = pRole->show_property.data().fightcapacity();
	int active = pRole->GetReputation(REPUID_ROAM_COMMUNITY_ACTIVE);
	std::shared_ptr<RCModifyMemberDataProcess> process = make_shared<RCModifyMemberDataProcess>(pRole->roleid, fp, active, 2);
	process->Begin();
}

void RoamCommunityManager::UploadPlayerData(RoleInfo *pRole)
{
	int fp = pRole->show_property.data().fightcapacity();
	int active = pRole->GetReputation(REPUID_ROAM_COMMUNITY_ACTIVE);
	std::shared_ptr<RCModifyMemberDataProcess> process = make_shared<RCModifyMemberDataProcess>(pRole->roleid, fp, active, 3);
	process->Begin();
}

void RoamCommunityManager::OnWeeklyUpdate(RoleInfo *pRole, PB::ipt_player_weekly_update *request)
{
}

void RoamCommunityManager::OnActiveRepuChange(RoleInfo *pRole)
{
	struct RCUpdateActiveProcess: public std::enable_shared_from_this<RCUpdateActiveProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;
		int _fightcapacity;
		int _active;
		int _old_active;

		std::vector<int64_t> _roleids;

		PB::roam_community_data _data;
		PB::roam_community_data::history_record _history;
		PB::roam_community_data::member_info _self;
		RCUpdateActiveProcess(ruid_t roleid, int fp, int active)
			: INIT_RET_VAR, _uniqueid(0), _roleid(roleid), _fightcapacity(fp), _active(active), _old_active(0)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->_uniqueid = atoll(data.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->Check2();
			}, false);
		}

		void Check2()
		{
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_roleid, isjoin);

				if (!isjoin)
				{
					// 这边要不要把ruid里面的值给清了
					OSSInterface::GetInstance().HDel(foo->_ruid_key, std::to_string(foo->_roleid), [](int retcode)
					{
					});
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			for (int i = 0; i < _data.members_size(); ++i)
			{
				if (_data.members(i).roleid() == _roleid)
				{
					auto member_ptr = _data.mutable_members(i);
					_old_active = member_ptr->active();
					member_ptr->set_isonline(1);
					member_ptr->set_zoneid(g_zoneid);
					member_ptr->set_fightcapacity(_fightcapacity);
					member_ptr->set_active(_active);
					_self.CopyFrom(*member_ptr);

					std::string str_member;
					PB_2_STR(*member_ptr, str_member);
					OSSInterface::GetInstance().HSet(_data_key, std::to_string(_roleid), str_member, [foo = shared_from_this()](int retcode)
					{
					}, 0, 0, 1, false);

					if (_active > _old_active)
					{
						int old_community_active = _data.base().active();
						_data.mutable_base()->set_active(old_community_active + _active - _old_active);

						for (auto iter = ROAM_COMMUNITY_CONFIG.active_award.begin(); iter != ROAM_COMMUNITY_CONFIG.active_award.end(); ++iter)
						{
							if (_active >= iter->first && _old_active < iter->first)
							{
								add_history_data(_data, PB::roam_community_data::history_record::RCHT_ACTIVE, _roleid, iter->first, _history);
							}
						}

						update_base_data(_uniqueid, _data);
						update_list_data(_uniqueid, _data, ULT_NORMAL);
					}

					break;
				}
			}
			OssUnlockShell shell(_data_key);
			Finish();
		}

		void Finish()
		{
			if (_retcode == 0)
			{
				if (_active != _old_active)
				{
					PB::npt_roam_community_notify notify;
					notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_MEMBER);
					notify.set_roleid(_roleid);
					notify.mutable_member()->CopyFrom(_self);
					notify.set_active(_data.mutable_base()->active());
					if (_history.has_type())
					{
						notify.mutable_history()->CopyFrom(_history);
					}
					for (int i = 0; i < _roleids.size(); ++i)
					{
						notify.add_targetid(_roleids[i]);
					}
					NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
				}

			}

			LOG_TRACE("DS::RoamCommunityManager::OnActiveRepuChange roleid=%ld, id=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	int fp = pRole->show_property.data().fightcapacity();
	int active = pRole->GetReputation(GNET::REPUID_ROAM_COMMUNITY_ACTIVE);
	std::shared_ptr<RCUpdateActiveProcess> process = make_shared<RCUpdateActiveProcess>(pRole->roleid, fp, active);
	process->Begin();
}

void RoamCommunityManager::OnCommonInviteReply(int64_t roleid, int64_t targetid, int64_t uniqueid, char isinvite, short retcode, uint32_t fp)
{
	struct RCInviteReplyProcess: public std::enable_shared_from_this<RCInviteReplyProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		ruid_t _targetid;
		int64_t _uniqueid;
		int _isagree;
		uint32_t _fightcapacity;

		PB::roam_community_data::member_info _self;
		PB::roam_community_data::applyer_info _apply;

		std::vector<int64_t> _roleids;

		PB::roam_community_data _data;
		PB::roam_community_data::history_record _history;
		RCInviteReplyProcess(ruid_t roleid, int64_t targetid, int64_t uniqueid, int isagree, uint32_t fp)
			: INIT_RET_VAR, _roleid(roleid), _targetid(targetid), _uniqueid(uniqueid), _isagree(isagree), _fightcapacity(fp)
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			if (!_isagree)
			{
				ERR_FINISH_RETURN("Begin failed: target refuse", GNET::ERROR_ROAM_COMMUNITY_REFUSE_INVITE);
			}

			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_targetid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: already in roam community=" + data, GNET::ERROR_ROAM_COMMUNITY_SELF_HAS_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->Check1();
			});
		}

		void Check1()
		{
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID ==  retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: community not exists", GNET::ERROR_ROAM_COMMUNITY_NOT_EXIST);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);

				foo->Check2();
			}, false);
		}

		void Check2()
		{
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: get community data failed", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_targetid, isjoin);

				if (foo->_data.base().leader() == foo->_roleid && foo->_data.members_size() >= ROAM_COMMUNITY_CONFIG.max_member)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check2 failed: member full", GNET::ERROR_ROAM_COMMUNITY_MEMBER_FULL);
				}

				foo->Do();
			}, 0, false, false);
		}

		void Do()
		{
			if (_data.base().leader() == _roleid)
			{
				OSSInterface::GetInstance().HSet(_ruid_key, std::to_string(_targetid), std::to_string(_uniqueid), [foo = shared_from_this()](int retcode)
				{
				}, 0, 0, 1, false);

				auto member_ptr = _data.add_members();
				member_ptr->set_roleid(_targetid);
				member_ptr->set_active(0);
				member_ptr->set_join_time(Timer::GetTime());
				member_ptr->set_isonline(1);
				member_ptr->set_zoneid(g_zoneid);
				member_ptr->set_fightcapacity(_fightcapacity);

				_self.CopyFrom(*member_ptr);
				_roleids.push_back(_targetid);

				std::string str_member;
				PB_2_STR(_self, str_member);
				OSSInterface::GetInstance().HSet(_data_key, std::to_string(_targetid), str_member, [foo = shared_from_this()](int retcode)
				{
				}, 0, 0, 1, false);

				add_history_data(_data, PB::roam_community_data::history_record::RCHT_ENTER, _targetid, 0, _history);
				int applyers_size = _data.applyers_size();
				for (int i = 0; i < applyers_size; ++i)
				{
					auto apply_ptr = _data.mutable_applyers(i);
					if (apply_ptr->roleid() == _targetid)
					{
						if (i != applyers_size - 1)
						{
							_data.mutable_applyers()->SwapElements(i, applyers_size - 1);
						}
						_data.mutable_applyers()->RemoveLast();
						break;
					}
				}
			}
			else
			{
				bool has_apply = false;
				int applyers_size = _data.applyers_size();
				for (int i = 0; i < applyers_size; ++i)
				{
					auto apply_ptr = _data.mutable_applyers(i);
					if (apply_ptr->roleid() == _targetid)
					{
						has_apply = true;
						break;
					}
				}

				if (!has_apply)
				{
					// 加入申请列表
					auto applyer_ptr = _data.add_applyers();
					applyer_ptr->set_roleid(_targetid);
					applyer_ptr->set_fightcapacity(_fightcapacity);
					_apply.CopyFrom(*applyer_ptr);

					int applyers_size = _data.applyers_size();
					if (applyers_size > ROAM_COMMUNITY_CONFIG.max_apply)
					{
						for (int i = 0; i < applyers_size - 1; ++i)
						{
							_data.mutable_applyers()->SwapElements(i, i + 1);
						}
						_data.mutable_applyers()->RemoveLast();
					}
				}
			}

			update_base_data(_uniqueid, _data);
			update_list_data(_uniqueid, _data, ULT_NORMAL);

			OssUnlockShell shell(_data_key);

			Finish();
		}

		void Finish()
		{
			if (_retcode == 0)
			{
				if (_data.base().leader() == _roleid)
				{
					// 这边通知自己和会员都是什么字段
					PB::npt_roam_community_notify notify;
					notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_ENTER);
					notify.set_roleid(_targetid);
					notify.mutable_data()->CopyFrom(_data);
					notify.mutable_member()->CopyFrom(_self);
					notify.mutable_history()->CopyFrom(_history);
					for (int i = 0; i < _roleids.size(); ++i)
					{
						notify.add_targetid(_roleids[i]);
					}
					NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
					RCMANAGER.SyncUniqueid(_targetid, _data.base().id());
				}
				else if (_apply.has_roleid())
				{
					// 通知leader有申请？
					PB::npt_roam_community_notify notify;
					notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_APPLY);
					notify.set_roleid(_targetid);
					notify.mutable_apply()->CopyFrom(_apply);
					notify.add_targetid(_data.base().leader());

					NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
				}
			}
			else if (_retcode == GNET::ERROR_ROAM_COMMUNITY_REFUSE_INVITE)
			{
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_REFUSE);
				notify.set_roleid(_targetid);
				notify.add_targetid(_roleid);
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
			}
			else
			{
				RoleInfo *pRole = RoleMap::Instance().FindOnline(_targetid);
				if (pRole)
				{
					PB::npt_roam_community_op_re request_re;
					request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_APPLY);
					request_re.set_retcode(_retcode);
					pRole->SendMessage2Client(request_re);
				}
			}

			LOG_TRACE("DS::RoamCommunityManager::OnCommonInviteReply roleid=%ld, id=%ld, errmsg=%s, reids_ret=%d", _roleid, _uniqueid, _err_msg.c_str(), _redis_ret);
		}
	};

	std::shared_ptr<RCInviteReplyProcess> process = make_shared<RCInviteReplyProcess>(roleid, targetid, uniqueid, retcode == 0, fp);
	process->Begin();
}

void RoamCommunityManager::SyncUniqueid(int64_t roleid, int64_t uniqueid)
{
	RoleInfo *pRole = RoleMap::Instance().FindOnline(roleid);
	if (pRole)
	{
		PB::ipt_roam_community_op_re request_re;
		request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_SYNC);
		request_re.set_uniqueid(uniqueid);
		pRole->SendMessage2GS(request_re);
	}
}

void RoamCommunityManager::Chat(ruid_t roleid, ChatPublic *pchat)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunity))
	{
		return;
	}

	RoleInfo *pRole = RoleMap::Instance().FindOnline(roleid);
	if (!pRole)
	{
		return;
	}

	struct RCChatProcess: public std::enable_shared_from_this<RCChatProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;
		ruid_t _roleid;
		GNET::ChatPublic _chat;

		std::vector<int64_t> _roleids;

		PB::roam_community_data _data;
		RCChatProcess(ruid_t roleid, GNET::ChatPublic& chat)
			: INIT_RET_VAR, _data_key(""), _uniqueid(0), _roleid(roleid)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
			_chat = chat;
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				foo->_uniqueid = atoll(data.c_str());
				foo->Check1();
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGetAll(_data_key, [foo = shared_from_this()](int retcode, int data_version, const std::map<std::string, std::string>& data_map)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				bool isjoin = false;
				get_community_data(data_map, foo->_roleids, foo->_data, foo->_roleid, isjoin);

				if (!isjoin)
				{
					// 这边要不要把ruid里面的值给清了
					OSSInterface::GetInstance().HDel(foo->_ruid_key, std::to_string(foo->_roleid), [](int retcode)
					{
					});
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}

				foo->Finish();
			}, 0, false, false);
		}

		void Finish()
		{
			if (_retcode == 0)
			{
				std::vector<int64_t> roam_roles;
				for (int i = 0; i < _roleids.size(); ++i)
				{
					RoleInfo *pTarget = RoleMap::Instance().FindOnline(_roleids[i]);
					if (pTarget)
					{
						_chat.localsid = pTarget->localsid;
						GDeliveryServer::GetInstance()->Send(pTarget->linksid, &_chat);
						continue;
					}

					roam_roles.push_back(_roleids[i]);
				}


				Octets o = Marshal::OctetsStream() << _chat;
				PB::npt_roam_community_notify notify;
				notify.set_op(PB::ROAM_COMMUNITY_OP::RCO_CHAT);
				notify.set_content(o.begin(), o.size());
				for (int i = 0; i < roam_roles.size(); ++i)
				{
					notify.add_targetid(roam_roles[i]);
				}
				NatsMessageManager::PublicNatsMessage(ROAM_COMMUNITY_SERVICE_GROUP, (int)NPT_ROAM_COMMUNITY_NOTIFY, notify);
			}

			LOG_TRACE("DS::RoamCommunityManager::RCChatProcess roleid=%ld, id=%ld, errmsg=%s, reids_retcode=%d, retcode=%d", _roleid, _uniqueid, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCChatProcess> process = make_shared<RCChatProcess>(pRole->roleid, *pchat);
	process->Begin();
}

void RoamCommunityManager::RequestVote(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid == manager_zoneid)
	{
		PB::ipt_roam_community_op ipt_request;
		ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_VOTE);
		ipt_request.set_roleid(pRole->roleid);
		ipt_request.set_resource_num(request->resource_num());
		ipt_request.set_point_index(request->point_index());
		std::string service_id = MAKE_SERVICE_ID(manager_zoneid);
		RequestVote_Center(&ipt_request, service_id);
		return;
	}

	struct RCVoteProcess: public std::enable_shared_from_this<RCVoteProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		int _resource_num;
		int _point_index;
		int64_t _uniqueid;

		PB::roam_community_data _data;
		RCVoteProcess(ruid_t roleid, int resource_num, int point_index)
			: INIT_RET_VAR, _roleid(roleid), _resource_num(resource_num), _point_index(point_index)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			if (_resource_num <= 0)
			{
				ERR_FINISH_RETURN("Begin failed: invalid resource_num", GNET::ERROR_ROAM_COMMUNITY_INVALID_RESOURCE_NUM);
			}

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_point_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				ERR_FINISH_RETURN("Begin failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}
			if (iter_point_cfg->second.ispve == 1)
			{
				ERR_FINISH_RETURN("Begin failed: point is pve", GNET::ERROR_ROAM_COMMUNITY_CAN_NOT_VOTE_PVE);
			}

			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGet(_data_key, REDIS_UUID_KEY_DATA_FIELD_NAME, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);
				update_list_data(foo->_uniqueid, foo->_data, ULT_NORMAL);

				time_t now = Timer::GetTime();
				if (now - foo->_data.base().create_time() < RCMANAGER.GetJoinTimeLimit())
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: create time limit", GNET::ERROR_ROAM_COMMUNITY_CREATE_TIME_LIMIT);
				}

				if (foo->_data.base().leader() != foo->_roleid)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (foo->_data.base().resource_num() < foo->_resource_num)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: resource not enough", GNET::ERROR_ROAM_COMMUNITY_RESOURCE_NOT_ENOUGH);
				}

				foo->Check4();
			});
		}

		void Check4()
		{
			if (RCMANAGER._battle_data.season_state() != RCSS_OPEN)
			{
				ERR_FINISH_RETURN("Check4 failed: season close", GNET::ERROR_ROAM_COMMUNITY_SEASON_NOT_OPEN);
			}

			if (RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_VOTE)
			{
				ERR_FINISH_RETURN("Check4 failed: not vote state", GNET::ERROR_ROAM_COMMUNITY_NOT_VOTE_STATE);
			}

			auto iter_point = RCMANAGER._points.find(_point_index);
			if (iter_point == RCMANAGER._points.end())
			{
				ERR_FINISH_RETURN("Check4 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}

			if (iter_point->second.belong() == _uniqueid)
			{
				ERR_FINISH_RETURN("Check4 failed: vote self point", GNET::ERROR_ROAM_COMMUNITY_CANNOT_VOTE_SELF_POINT);
			}

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_point_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				ERR_FINISH_RETURN("Check4 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}

			int vote_point_num = 0;
			int belong_point_num = 0;
			std::map<int, std::set<int>> level_belong_map;
			std::map<int, std::set<int>> level_vote_map;
			for (auto iter_point = RCMANAGER._points.begin(); iter_point != RCMANAGER._points.end(); ++iter_point)
			{
				auto iter_point_cfg_tmp = ROAM_COMMUNITY_CONFIG.points.find(iter_point->first);
				if (iter_point_cfg_tmp == ROAM_COMMUNITY_CONFIG.points.end())
				{
					ERR_FINISH_RETURN("Check4 failed: invalid index 2", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
				}

				if (iter_point->first == _point_index)
				{
					bool already_vote = false;
					int point_max_vote_num = 0;
					for (int i = 0; i < iter_point->second.votes_size(); ++i)
					{
						if (point_max_vote_num < iter_point->second.votes(i).resource())
						{
							point_max_vote_num = iter_point->second.votes(i).resource();
						}
						if (iter_point->second.votes(i).id() == _uniqueid)
						{
							already_vote = true;
						}
					}

					if (!already_vote && _resource_num <= point_max_vote_num)
					{
						ERR_FINISH_RETURN("Check4 failed: vote less than other", GNET::ERROR_ROAM_COMMUNITY_CAN_VOTE_LESS_THAN_OTHER);
					}

					// 当前据点肯定不是已占领的据点；当前据点如果是已投据点，那么依然可投。
					// 所以该处不处理level_belong_map和level_vote_map
					continue;
				}

				for (int i = 0; i < iter_point->second.votes_size(); ++i)
				{
					if (iter_point->second.votes(i).id() == _uniqueid)
					{
						++vote_point_num;
						if (vote_point_num >= ROAM_COMMUNITY_CONFIG.max_vote_point_num)
						{
							ERR_FINISH_RETURN("Check4 failed: already vote point num max", GNET::ERROR_ROAM_COMMUNITY_VOTE_POINT_LIMIT);
						}

						level_vote_map[iter_point_cfg_tmp->second.level].insert(iter_point->first);
						break;
					}
				}

				if (iter_point->second.belong() == _uniqueid)
				{
					++belong_point_num;
					level_belong_map[iter_point_cfg_tmp->second.level].insert(iter_point->first);
				}
			}

			if (belong_point_num + vote_point_num >= ROAM_COMMUNITY_CONFIG.max_occupy_num)
			{
				ERR_FINISH_RETURN("Check4 failed: occupy + vote num limit", GNET::ERROR_ROAM_COMMUNITY_OCCUPY_NUM_LIMIT);
			}

			if (iter_point_cfg->second.level >= ROAM_COMMUNITY_CONFIG.min_occupy_limit_level)
			{
				auto iter_tmp1 = level_belong_map.find(iter_point_cfg->second.level);
				if (iter_tmp1 != level_belong_map.end())
				{
					ERR_FINISH_RETURN("Check4 failed: this level occupy num limit", GNET::ERROR_ROAM_COMMUNITY_THIS_LEVEL_OCCUPY_NUM_LIMIT);
				}
				auto iter_tmp2 = level_vote_map.find(iter_point_cfg->second.level);
				if (iter_tmp2 != level_vote_map.end())
				{
					ERR_FINISH_RETURN("Check4 failed: this level vote num limit", GNET::ERROR_ROAM_COMMUNITY_THIS_LEVEL_VOTE_NUM_LIMIT);
				}
			}


			if (iter_point_cfg->second.isborder == 0)
			{
				bool can_vote = false;
				for (int i = 0; i < iter_point_cfg->second.link.size(); ++i)
				{
					auto iter_point_link = RCMANAGER._points.find(iter_point_cfg->second.link[i]);
					if (iter_point_link != RCMANAGER._points.end() && iter_point_link->second.belong() == _uniqueid)
					{
						can_vote = true;
						break;
					}
				}

				if (!can_vote)
				{
					ERR_FINISH_RETURN("Check4 failed: can't vote for no link", GNET::ERROR_ROAM_COMMUNITY_CANNOT_VOTE_FOR_NO_LINK);
				}
			}

			Finish();
		}

		void Finish()
		{
			if (_retcode == 0)
			{
				// 这边往中心服中去处理
				PB::ipt_roam_community_op ipt_request;
				ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_VOTE);
				ipt_request.set_roleid(_roleid);
				ipt_request.set_resource_num(_resource_num);
				ipt_request.set_point_index(_point_index);

				int manager_zoneid = 0;
				GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
				manager_zoneid = battle_ptr->GetCenterZoneID();
				std::string service_id = MAKE_SERVICE_ID(manager_zoneid);
				NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, service_id, &ipt_request);
				GET_CENTER_BATTLE_END
			}
			else
			{
				RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
				if (pRole)
				{
					PB::npt_roam_community_op_re request_re;
					request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_VOTE);
					request_re.set_resource_num(_resource_num);
					request_re.set_point_index(_point_index);
					request_re.set_retcode(_retcode);
					pRole->SendMessage2Client(request_re);
				}
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestVote roleid=%ld, id=%ld, resouce_num=%d, _point_index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _resource_num, _point_index, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCVoteProcess> process = make_shared<RCVoteProcess>(pRole->roleid, request->resource_num(), request->point_index());
	process->Begin();
}

void RoamCommunityManager::RequestEnhanceDefense(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid == manager_zoneid)
	{
		PB::ipt_roam_community_op ipt_request;
		ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_ENHANCE_DEFENSE);
		ipt_request.set_roleid(pRole->roleid);
		ipt_request.set_resource_num(request->resource_num());
		ipt_request.set_point_index(request->point_index());
		std::string service_id = MAKE_SERVICE_ID(manager_zoneid);
		RequestEnhanceDefense_Center(&ipt_request, service_id);
		return;
	}

	struct RCEnhanceDefenseProcess: public std::enable_shared_from_this<RCEnhanceDefenseProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		int _resource_num;
		int _point_index;
		int64_t _uniqueid;

		PB::roam_community_data _data;
		RCEnhanceDefenseProcess(ruid_t roleid, int resource_num, int point_index)
			: INIT_RET_VAR, _roleid(roleid), _resource_num(resource_num), _point_index(point_index)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			if (_resource_num <= 0)
			{
				ERR_FINISH_RETURN("Begin failed: invalid resource_num", GNET::ERROR_ROAM_COMMUNITY_INVALID_RESOURCE_NUM);
			}

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_point_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				ERR_FINISH_RETURN("Begin failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}
			if (iter_point_cfg->second.ispve == 1)
			{
				ERR_FINISH_RETURN("Begin failed: point is pve", GNET::ERROR_ROAM_COMMUNITY_CAN_NOT_ENHANCE_PVE);
			}

			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGet(_data_key, REDIS_UUID_KEY_DATA_FIELD_NAME, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);
				update_list_data(foo->_uniqueid, foo->_data, ULT_NORMAL);

				time_t now = Timer::GetTime();
				if (now - foo->_data.base().create_time() < RCMANAGER.GetJoinTimeLimit())
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: create time limit", GNET::ERROR_ROAM_COMMUNITY_CREATE_TIME_LIMIT);
				}

				if (foo->_data.base().leader() != foo->_roleid)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (foo->_data.base().resource_num() < foo->_resource_num)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: resource not enough", GNET::ERROR_ROAM_COMMUNITY_RESOURCE_NOT_ENOUGH);
				}

				foo->Check4();
			});
		}

		void Check4()
		{
			if (RCMANAGER._battle_data.season_state() != RCSS_OPEN)
			{
				ERR_FINISH_RETURN("Begin failed: season close", GNET::ERROR_ROAM_COMMUNITY_SEASON_NOT_OPEN);
			}

			if ((RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_WAIT) && (RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE))
			{
				ERR_FINISH_RETURN("Begin failed: battle state can't enhance", GNET::ERROR_ROAM_COMMUNITY_BATTLE_STATE_CAN_NOT_ENHANCE);
			}

			auto iter_point = RCMANAGER._points.find(_point_index);
			if (iter_point == RCMANAGER._points.end())
			{
				ERR_FINISH_RETURN("Check4 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}

			if (iter_point->second.belong() != _uniqueid)
			{
				ERR_FINISH_RETURN("Check4 failed: can't enhance other point", GNET::ERROR_ROAM_COMMUNITY_CAN_NOT_ENHANCE_OTHER_POINT);
			}

			Finish();
		}

		void Finish()
		{
			if (_retcode == 0)
			{
				// 这边往中心服中去处理
				PB::ipt_roam_community_op ipt_request;
				ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_ENHANCE_DEFENSE);
				ipt_request.set_roleid(_roleid);
				ipt_request.set_resource_num(_resource_num);
				ipt_request.set_point_index(_point_index);
				int manager_zoneid = 0;
				GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
				manager_zoneid = battle_ptr->GetCenterZoneID();
				std::string service_id = MAKE_SERVICE_ID(manager_zoneid);
				NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, service_id, &ipt_request);
				GET_CENTER_BATTLE_END
			}
			else
			{
				RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
				if (pRole)
				{
					PB::npt_roam_community_op_re request_re;
					request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_ENHANCE_DEFENSE);
					request_re.set_resource_num(_resource_num);
					request_re.set_point_index(_point_index);
					request_re.set_retcode(_retcode);
					pRole->SendMessage2Client(request_re);
				}
			}

			LOG_TRACE("DS::RoamCommunityManager::RequestEnhanceDefense roleid=%ld, id=%ld, resouce_num=%d, _point_index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _resource_num, _point_index, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCEnhanceDefenseProcess> process = make_shared<RCEnhanceDefenseProcess>(pRole->roleid, request->resource_num(), request->point_index());
	process->Begin();
}

void RoamCommunityManager::RequestPointData(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	struct RCPointDataProcess: public std::enable_shared_from_this<RCPointDataProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		ruid_t _roleid;
		int64_t _uniqueid;

		RCPointDataProcess(ruid_t roleid)
			: INIT_RET_VAR, _roleid(roleid)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			// 仅获取uniqueid，不做错误处理
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
				}
				foo->Finish();
			});
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.mutable_battle_data()->CopyFrom(RCMANAGER._battle_data);
				request_re.mutable_battle_data()->clear_winners();
				request_re.mutable_battle_data()->clear_faileds();

				for (auto iter_point = RCMANAGER._points.begin(); iter_point != RCMANAGER._points.end(); ++iter_point)
				{
					auto pt_ptr = request_re.add_points_data();
					pt_ptr->set_index(iter_point->second.index());
					pt_ptr->set_belong(iter_point->second.belong());
					pt_ptr->set_defense(iter_point->second.defense());
					for (int i = 0; i < iter_point->second.votes_size() && _uniqueid != 0; ++i)
					{
						if (iter_point->second.votes(i).id() == _uniqueid)
						{
							auto vote_ptr = pt_ptr->add_votes();
							vote_ptr->CopyFrom(iter_point->second.votes(i));
							break;
						}
					}
				}
				for (auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.begin(); iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.end(); ++iter_point_cfg)
				{
					if (iter_point_cfg->second.ispve == 1)
					{
						auto pt_ptr = request_re.add_points_data();
						pt_ptr->set_index(iter_point_cfg->first);
						pt_ptr->set_belong(0);
						pt_ptr->set_defense(iter_point_cfg->second.defense);
					}
				}

				for (int i = 0; i < request_re.battle_data().against_plan_size(); ++i)
				{
					int64_t tmp_attacker = request_re.battle_data().against_plan(i).attacker();
					if (tmp_attacker > 0)
					{
						for (auto iter_brief = RCMANAGER._brief_list.begin(); iter_brief != RCMANAGER._brief_list.end(); ++iter_brief)
						{
							if (tmp_attacker == iter_brief->base().id())
							{
								request_re.mutable_battle_data()->mutable_against_plan(i)->set_attacker_name(iter_brief->base().name());
								break;
							}
						}

					}
				}
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_REQUEST_POINT_DATA);
				request_re.set_retcode(_retcode);
				pRole->SendMessage2Client(request_re);
			}
		}
	};

	std::shared_ptr<RCPointDataProcess> process = make_shared<RCPointDataProcess>(pRole->roleid);
	process->Begin();
}

void RoamCommunityManager::RequestPointDetail(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	struct RCPointDetailProcess: public std::enable_shared_from_this<RCPointDetailProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		ruid_t _roleid;
		int64_t _uniqueid;
		int _index;

		RCPointDetailProcess(ruid_t roleid, int index)
			: INIT_RET_VAR, _roleid(roleid), _index(index)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			// 仅获取uniqueid，不做错误处理
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
				}
				foo->Finish();
			});
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				auto iter_point = RCMANAGER._points.find(_index);
				if (iter_point != RCMANAGER._points.end())
				{
					auto pt_ptr = request_re.mutable_points_detail();
					pt_ptr->CopyFrom(iter_point->second);
					for (int i = 0; i < pt_ptr->votes_size(); ++i)
					{
						auto vote_ptr = pt_ptr->mutable_votes(i);
						int64_t uniqueid = vote_ptr->id();
						for (auto iter_brief = RCMANAGER._brief_list.begin(); iter_brief != RCMANAGER._brief_list.end(); ++iter_brief)
						{
							if (uniqueid == iter_brief->base().id())
							{
								vote_ptr->set_name(iter_brief->base().name());
								break;
							}
						}
					}
				}
				else
				{
					auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_index);
					if (iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.find(_index) && iter_point_cfg->second.ispve == 1)
					{
						auto pt_ptr = request_re.mutable_points_detail();
						pt_ptr->set_index(iter_point_cfg->first);
						pt_ptr->set_belong(0);
						pt_ptr->set_defense(iter_point_cfg->second.defense);
					}
				}
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_REQUEST_POINT_DETAIL);
				request_re.set_point_index(_index);
				request_re.set_retcode(_retcode);
				pRole->SendMessage2Client(request_re);
			}
		}
	};

	std::shared_ptr<RCPointDetailProcess> process = make_shared<RCPointDetailProcess>(pRole->roleid, request->point_index());
	process->Begin();
}

void RoamCommunityManager::RequestRank(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	//if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	//{
	//	return;
	//}

	//struct RCGetRankProcess: public std::enable_shared_from_this<RCGetRankProcess>
	//{
	//	DECLARE_RET_VAR

	//	ruid_t _roleid;
	//	int _sequence;
	//	std::string _battle_rank_key;

	//	PB::roam_community_rank_data _rank_data;
	//	RCGetRankProcess(ruid_t roleid, int sequence)
	//		: INIT_RET_VAR, _roleid(roleid), _sequence(sequence)
	//	{
	//		_battle_rank_key = GetOssKeyWord(OSS_RC_BATTLE_RANK_DATA, 0, "TEST");
	//	}

	//	void Begin()
	//	{
	//		std::string field_name = std::to_string(_sequence);
	//		OSSInterface::GetInstance().HGet(_battle_rank_key, field_name, [foo = shared_from_this()](int retcode, const string & data)
	//		{
	//			foo->_redis_ret = retcode;
	//			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
	//			{
	//				STR_2_PB(data, foo->_rank_data);

	//				foo->Finish();
	//			}
	//			else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
	//			{
	//				FOO_ERR_FINISH_RETURN("Begin failed: ", GNET::ERROR_ROAM_COMMUNITY_NOT_FIND_RANK_DATA);
	//			}
	//			else
	//			{
	//				FOO_ERR_FINISH_RETURN("Begin failed: ", GNET::ERROR_ROAM_COMMUNITY_NOT_FIND_RANK_DATA);
	//			}
	//		});
	//	}

	//	void Finish()
	//	{
	//		RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
	//		if (pRole)
	//		{
	//			PB::npt_roam_community_op_re request_re;
	//			request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_GET_RANK);
	//			request_re.set_season_sequence(_sequence);
	//			request_re.mutable_rank()->CopyFrom(_rank_data);
	//			request_re.set_retcode(_retcode);
	//			pRole->SendMessage2Client(request_re);
	//		}
	//		LOG_TRACE("DS::RoamCommunityManager::RequestRank roleid=%ld, sequence=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _sequence, _err_msg.c_str(), _redis_ret, _retcode);
	//	}
	//};

	//std::shared_ptr<RCGetRankProcess> process = make_shared<RCGetRankProcess>(pRole->roleid, request->season_sequence());
	//process->Begin();
}

void RoamCommunityManager::RequestAskRankRewardFlag(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	struct RCAskRankRewardProcess: public std::enable_shared_from_this<RCAskRankRewardProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		int64_t _uniqueid;

		PB::roam_community_data::member_info _self;
		RCAskRankRewardProcess(ruid_t roleid)
			: INIT_RET_VAR, _roleid(roleid)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGet(_data_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_self);

				foo->Finish();
			});
		}

		void Finish()
		{
			std::stringstream battle_pick_ss;
			std::stringstream season_pick_ss;
			battle_pick_ss << "{";
			season_pick_ss << "{";
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				PB::npt_roam_community_op_re request_re;
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_ASK_RANK_REWARD_FLAG);
				request_re.set_retcode(_retcode);
				if (_retcode == 0)
				{
					bool upload = false;
					time_t week_begin = GetLocalWeekBegin();
					if (_self.battle_pick().last_value() != week_begin)
					{
						_self.mutable_battle_pick()->set_last_value(week_begin);
						_self.mutable_battle_pick()->clear_indexes();
						upload = true;
					}
					if (_self.season_pick().last_value() != ROAM_COMMUNITY_CONFIG.activity_index)
					{
						_self.mutable_season_pick()->set_last_value(ROAM_COMMUNITY_CONFIG.activity_index);
						_self.mutable_season_pick()->clear_indexes();
						upload = true;
					}
					if (upload)
					{
						std::string str_member;
						PB_2_STR(_self, str_member);
						OSSInterface::GetInstance().HSet(_data_key, std::to_string(_roleid), str_member, [foo = shared_from_this()](int retcode)
						{
						}, 0, 0, 1, false);
					}

					for (int i = 0; i < _self.battle_pick().indexes_size(); ++i)
					{
						request_re.add_picked_battle_indexes(_self.battle_pick().indexes(i));
						battle_pick_ss << _self.battle_pick().indexes(i) << "|";
					}
					for (int i = 0; i < _self.season_pick().indexes_size(); ++i)
					{
						request_re.add_picked_season_indexes(_self.season_pick().indexes(i));
						season_pick_ss << _self.season_pick().indexes(i) << "|";
					}
				}
				pRole->SendMessage2Client(request_re);
			}
			battle_pick_ss << "}";
			season_pick_ss << "}";

			LOG_TRACE("DS::RoamCommunityManager::RequestAskRankRewardFlag roleid=%ld, id=%ld, battle_pick=%s, season_pick=%s, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, battle_pick_ss.str().c_str(), season_pick_ss.str().c_str(), _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCAskRankRewardProcess> process = make_shared<RCAskRankRewardProcess>(pRole->roleid);
	process->Begin();
}

enum RC_GET_REWARD_TYPE
{
	RC_GRT_BATTLE	= 1,
	RC_GRT_SEASON	= 2,
};

struct RCGetPointRewardProcess: public std::enable_shared_from_this<RCGetPointRewardProcess>
{
	DECLARE_RET_VAR

	ruid_t _roleid;
	int _index;
	int _type;

	std::string _ruid_key;
	std::string _data_key;
	int64_t _uniqueid;

	PB::roam_community_data::member_info _self;
	RCGetPointRewardProcess(ruid_t roleid, int index, int type)
		: INIT_RET_VAR, _roleid(roleid), _index(index), _type(type), _data_key(""), _uniqueid(0)
	{
		_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
	}

	void Begin()
	{
		RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
		if (!pRole)
		{
			ERR_FINISH_RETURN("Begin failed: not find role", GNET::ERROR_ROAM_COMMUNITY_TARGET_OFFLINE);
		}

		if (_type == RC_GRT_BATTLE)
		{
			int repu_value = pRole->GetReputation(ROAM_COMMUNITY_CONFIG.battle_reward_repu);
			if (repu_value < ROAM_COMMUNITY_CONFIG.battle_reward_repu_value)
			{
				ERR_FINISH_RETURN("Begin failed: not find role", GNET::ERROR_ROAM_COMMUNITY_BATTLE_REPU_NOT_ENOUGH);
			}
		}
		else if (_type == RC_GRT_SEASON)
		{
			int repu_value = pRole->GetReputation(ROAM_COMMUNITY_CONFIG.season_reward_repu);
			if (repu_value < ROAM_COMMUNITY_CONFIG.season_reward_repu_value)
			{
				ERR_FINISH_RETURN("Begin failed: not find role", GNET::ERROR_ROAM_COMMUNITY_SEASON_REPU_NOT_ENOUGH);
			}
		}

		OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
			{
				foo->_uniqueid = atoll(data.c_str());
				foo->Check1();
			}
			else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}
			else
			{
				FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
			}
		});
	}

	void Check1()
	{
		if (_type == RC_GRT_BATTLE)
		{
			//if (RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_END)
			//{
			//	ERR_FINISH_RETURN("Check1 failed: cur battle state can't get reward", GNET::ERROR_ROAM_COMMUNITY_CUR_STATE_CAN_NOT_GET_REWARD);
			//}
		}
		else if (_type == RC_GRT_SEASON)
		{
			if (RCMANAGER._battle_data.season_state() != RCSS_CLOSE)
			{
				ERR_FINISH_RETURN("Check1 failed: cur season state can't get reward", GNET::ERROR_ROAM_COMMUNITY_CUR_STATE_CAN_NOT_GET_REWARD);
			}
		}

		auto iter_point = RCMANAGER._points.find(_index);
		if (iter_point == RCMANAGER._points.end())
		{
			ERR_FINISH_RETURN("Check1 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
		}

		if (iter_point->second.belong() != _uniqueid)
		{
			ERR_FINISH_RETURN("Check1 failed: not belong community", GNET::ERROR_ROAM_COMMUNITY_NOT_SELF_RC_BATTLE);
		}

		Check2();
	}

	void Check2()
	{
		_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
		OSSInterface::GetInstance().HGet(_data_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
		{
			foo->_redis_ret = retcode;
			if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
			{
				FOO_ERR_FINISH_RETURN("Check2 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
			}
			else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
			{
				FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
			}

			STR_2_PB(data, foo->_self);

			bool has_pick = false;
			if (foo->_type == RC_GRT_BATTLE)
			{
				time_t week_begin = GetLocalWeekBegin();
				if (foo->_self.battle_pick().last_value() != week_begin)
				{
					foo->_self.mutable_battle_pick()->set_last_value(week_begin);
					foo->_self.mutable_battle_pick()->clear_indexes();
					std::string str_member;
					PB_2_STR(foo->_self, str_member);
					OSSInterface::GetInstance().HSet(foo->_data_key, std::to_string(foo->_roleid), str_member, [](int retcode)
					{
					}, 0, 0, 1, false);
				}
				for (int i = 0; i < foo->_self.battle_pick().indexes_size(); ++i)
				{
					if (foo->_self.battle_pick().indexes(i) == foo->_index)
					{
						has_pick = true;
						break;
					}
				}
			}
			else if (foo->_type == RC_GRT_SEASON)
			{
				if (foo->_self.season_pick().last_value() != ROAM_COMMUNITY_CONFIG.activity_index)
				{
					foo->_self.mutable_season_pick()->set_last_value(ROAM_COMMUNITY_CONFIG.activity_index);
					foo->_self.mutable_season_pick()->clear_indexes();
					std::string str_member;
					PB_2_STR(foo->_self, str_member);
					OSSInterface::GetInstance().HSet(foo->_data_key, std::to_string(foo->_roleid), str_member, [](int retcode)
					{
					}, 0, 0, 1, false);
				}
				for (int i = 0; i < foo->_self.season_pick().indexes_size(); ++i)
				{
					if (foo->_self.season_pick().indexes(i) == foo->_index)
					{
						has_pick = true;
						break;
					}
				}
			}

			if (has_pick)
			{
				FOO_ERR_FINISH_RETURN("Check2 reward already picked", GNET::ERROR_ROAM_COMMUNITY_REWARD_ARREADY_PICK);
			}

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(foo->_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				FOO_ERR_FINISH_RETURN("Check2 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}

			if (foo->_type == RC_GRT_BATTLE)
			{
				foo->_self.mutable_battle_pick()->add_indexes(foo->_index);
			}
			else if (foo->_type == RC_GRT_SEASON)
			{
				foo->_self.mutable_season_pick()->add_indexes(foo->_index);
			}

			RoleInfo *pRole = RoleMap::Instance().FindOnline(foo->_roleid);
			if (pRole)
			{
				int reward_tid = 0;
				if (foo->_type == RC_GRT_BATTLE)
				{
					reward_tid = iter_point_cfg->second.battle_reward;
				}
				else if (foo->_type == RC_GRT_SEASON)
				{
					reward_tid = iter_point_cfg->second.season_reward;
				}
				FuncInfo fi = {kFuncCodeRoamCommunityBattle};
				pRole->DeliverGeneralReward(fi, reward_tid, GRANT_REWARD_TYPE_NO);
			}

			std::string str_member;
			PB_2_STR(foo->_self, str_member);
			OSSInterface::GetInstance().HSet(foo->_data_key, std::to_string(foo->_roleid), str_member, [](int retcode)
			{
			}, 0, 0, 1, false);

			foo->Finish();
		});
	}

	void Finish()
	{
		RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
		if (pRole)
		{
			PB::npt_roam_community_op_re request_re;
			if (_type == RC_GRT_BATTLE)
			{
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_GET_BATTLE_REWARD);
			}
			else if (_type == RC_GRT_SEASON)
			{
				request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_GET_SEASON_REWARD);
			}
			request_re.set_retcode(_retcode);
			if (_retcode == 0)
			{
				for (int i = 0; i < _self.battle_pick().indexes_size(); ++i)
				{
					request_re.add_picked_battle_indexes(_self.battle_pick().indexes(i));
				}
				for (int i = 0; i < _self.season_pick().indexes_size(); ++i)
				{
					request_re.add_picked_season_indexes(_self.season_pick().indexes(i));
				}
			}
			pRole->SendMessage2Client(request_re);
		}

		LOG_TRACE("DS::RoamCommunityManager::RCGetPointRewardProcess roleid=%ld, id=%ld, type=%d, index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _type, _index, _err_msg.c_str(), _redis_ret, _retcode);
	}
};

void RoamCommunityManager::RequestGetBattleReward(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	if (pRole->IsInCooldown(COOLDOWN_ID_ROAM_COMMUNITY_GET_REWARD))
	{
		return ;
	}
	pRole->SetCooldown(COOLDOWN_ID_ROAM_COMMUNITY_GET_REWARD, COOLDOWN_ROAM_COMMUNITY_GET_REWARD_TIMES);

	std::shared_ptr<RCGetPointRewardProcess> process = make_shared<RCGetPointRewardProcess>(pRole->roleid, request->point_index(), RC_GRT_BATTLE);
	process->Begin();
}

void RoamCommunityManager::RequestGetSeasonReward(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	if (pRole->IsInCooldown(COOLDOWN_ID_ROAM_COMMUNITY_GET_REWARD))
	{
		return ;
	}
	pRole->SetCooldown(COOLDOWN_ID_ROAM_COMMUNITY_GET_REWARD, COOLDOWN_ROAM_COMMUNITY_GET_REWARD_TIMES);

	std::shared_ptr<RCGetPointRewardProcess> process = make_shared<RCGetPointRewardProcess>(pRole->roleid, request->point_index(), RC_GRT_SEASON);
	process->Begin();
}

void RoamCommunityManager::RequestVote_Center(PB::ipt_roam_community_op *request, const std::string& from)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid != manager_zoneid)
	{
		return;
	}

	struct RCVoteCenterProcess: public std::enable_shared_from_this<RCVoteCenterProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		std::string _battle_data_key;
		ruid_t _roleid;
		int _resource_num;
		int _point_index;
		int64_t _uniqueid;
		int _total_vote;
		std::string _from;
		std::map<int64_t, int> _need_return;

		PB::roam_community_data _data;
		RCVoteCenterProcess(ruid_t roleid, int resource_num, int point_index, const std::string& from)
			: INIT_RET_VAR, _roleid(roleid), _resource_num(resource_num), _point_index(point_index), _total_vote(0), _from(from)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
			_battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
		}

		void Begin()
		{
			if (_resource_num <= 0)
			{
				ERR_FINISH_RETURN("Begin failed: invalid resource_num", GNET::ERROR_ROAM_COMMUNITY_INVALID_RESOURCE_NUM);
			}

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_point_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				ERR_FINISH_RETURN("Begin failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}
			if (iter_point_cfg->second.ispve == 1)
			{
				ERR_FINISH_RETURN("Begin failed: point is pve", GNET::ERROR_ROAM_COMMUNITY_CAN_NOT_VOTE_PVE);
			}

			if (RCMANAGER._battle_data.season_state() != RCSS_OPEN)
			{
				ERR_FINISH_RETURN("Begin failed: season close", GNET::ERROR_ROAM_COMMUNITY_SEASON_NOT_OPEN);
			}

			if (RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_VOTE)
			{
				ERR_FINISH_RETURN("Begin failed: not vote state", GNET::ERROR_ROAM_COMMUNITY_NOT_VOTE_STATE);
			}

			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);

				time_t now = Timer::GetTime();
				if (now - foo->_data.base().create_time() < RCMANAGER.GetJoinTimeLimit())
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: create time limit", GNET::ERROR_ROAM_COMMUNITY_CREATE_TIME_LIMIT);
				}

				if (foo->_data.base().leader() != foo->_roleid)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (foo->_data.base().resource_num() < foo->_resource_num)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: resource not enough", GNET::ERROR_ROAM_COMMUNITY_RESOURCE_NOT_ENOUGH);
				}

				int vote_point_num = 0;
				for (auto iter_point = RCMANAGER._points.begin(); iter_point != RCMANAGER._points.end(); ++iter_point)
				{
					if (iter_point->first == foo->_point_index)
					{
						bool already_vote = false;
						int point_max_vote_num = 0;
						for (int i = 0; i < iter_point->second.votes_size(); ++i)
						{
							if (point_max_vote_num < iter_point->second.votes(i).resource())
							{
								point_max_vote_num = iter_point->second.votes(i).resource();
							}
							if (iter_point->second.votes(i).id() == foo->_uniqueid)
							{
								already_vote = true;
							}
						}

						if (!already_vote && foo->_resource_num <= point_max_vote_num)
						{
							FOO_ERR_FINISH_RETURN("Check1 failed: vote less than other", GNET::ERROR_ROAM_COMMUNITY_CAN_VOTE_LESS_THAN_OTHER);
						}
						continue;
					}

					for (int i = 0; i < iter_point->second.votes_size(); ++i)
					{
						if (iter_point->second.votes(i).id() == foo->_uniqueid)
						{
							++vote_point_num;
							if (vote_point_num >= ROAM_COMMUNITY_CONFIG.max_vote_point_num)
							{
								OssUnlockShell shell(foo->_data_key);
								FOO_ERR_FINISH_RETURN("Check1 failed: already vote point num max", GNET::ERROR_ROAM_COMMUNITY_VOTE_POINT_LIMIT);
							}
						}
					}
				}

				auto iter_point = RCMANAGER._points.find(foo->_point_index);
				if (iter_point == RCMANAGER._points.end())
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
				}

				if (iter_point->second.belong() == foo->_uniqueid)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: vote self point", GNET::ERROR_ROAM_COMMUNITY_CANNOT_VOTE_SELF_POINT);
				}


				auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(foo->_point_index);
				if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
				}

				if (iter_point_cfg->second.isborder == 0)
				{
					bool can_vote = false;
					for (int i = 0; i < iter_point_cfg->second.link.size(); ++i)
					{
						auto iter_point_link = RCMANAGER._points.find(iter_point_cfg->second.link[i]);
						if (iter_point_link != RCMANAGER._points.end() && iter_point_link->second.belong() == foo->_uniqueid)
						{
							can_vote = true;
							break;
						}
					}

					if (!can_vote)
					{
						OssUnlockShell shell(foo->_data_key);
						FOO_ERR_FINISH_RETURN("Check1 failed: can't vote for no link", GNET::ERROR_ROAM_COMMUNITY_CANNOT_VOTE_FOR_NO_LINK);
					}
				}

				std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
				int resource_num = foo->_data.base().resource_num();
				foo->_data.mutable_base()->set_resource_num(resource_num - foo->_resource_num);
				std::string str_rc_data;
				PB_2_STR(foo->_data, str_rc_data);
				update_list_data(foo->_uniqueid, foo->_data, ULT_NORMAL);
				OSSInterface::GetInstance().HSet(foo->_data_key, data_field, str_rc_data, [](int retcode)
				{
				}, 0, 0, 1, false);


				int already_votes = 0;
				foo->_need_return.clear();
				for (int i = 0; i < iter_point->second.votes_size(); ++i)
				{
					auto vote_ptr = iter_point->second.mutable_votes(i);
					if (vote_ptr->id() == foo->_uniqueid)
					{
						already_votes = vote_ptr->resource();
					}
					else
					{
						int ret_resource = vote_ptr->resource() * ROAM_COMMUNITY_CONFIG.return_resource_percent / 100.0f;
						foo->_need_return.insert(std::make_pair(vote_ptr->id(), ret_resource));
					}
				}

				iter_point->second.clear_votes();
				auto vote_ptr = iter_point->second.add_votes();
				vote_ptr->set_id(foo->_uniqueid);
				vote_ptr->set_resource(already_votes + foo->_resource_num);
				vote_ptr->set_timestamp(Timer::GetTime());
				foo->_total_vote = vote_ptr->resource();

				int64_t data_version = RCMANAGER._battle_data.data_version();
				RCMANAGER._battle_data.set_data_version(data_version + 1);

				std::map<std::string, std::string> kv_map;
				std::string str_pt_data;
				PB_2_STR(iter_point->second, str_pt_data);
				kv_map.insert(std::make_pair(std::to_string(iter_point->first), str_pt_data));
				std::string str_bt_data;
				PB_2_STR(RCMANAGER._battle_data, str_bt_data);
				kv_map.insert(std::make_pair(data_field, str_bt_data));

				OSSInterface::GetInstance().HMSet(foo->_battle_data_key, kv_map, [](int retcode)
				{
				}, false);

				OssUnlockShell shell(foo->_data_key);

				foo->Finish();
			});
		}

		void Finish()
		{
			PB::ipt_roam_community_op_re ipt_request_re;
			ipt_request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_VOTE);
			ipt_request_re.set_roleid(_roleid);
			ipt_request_re.set_resource_num(_resource_num);
			ipt_request_re.set_point_index(_point_index);
			ipt_request_re.set_total_vote(_total_vote);
			ipt_request_re.set_retcode(_retcode);
			NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, _from, &ipt_request_re);

			LOG_TRACE("DS::RoamCommunityManager::RequestVote_Center roleid=%ld, id=%ld, resource_num=%d, cur_resource_num=%ld, _point_index=%d, from=%s, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _resource_num, _data.base().resource_num(), _point_index, _from.c_str(), _err_msg.c_str(), _redis_ret, _retcode);

			if (_need_return.size() > 0)
			{
				std::shared_ptr<RCUpdateResourceProcess> process = make_shared<RCUpdateResourceProcess>(_need_return, [uniqueid = _uniqueid](const std::map<int64_t, int>& _)
				{
					std::vector<int64_t> uniqueids;
					uniqueids.push_back(uniqueid);
					for (auto iter = _.begin(); iter != _.end(); ++iter)
					{
						uniqueids.push_back(iter->first);
					}
					RCMANAGER.SendNotifyUpdateData(uniqueids);
				});
				process->Begin();
			}
			else
			{
				std::vector<int64_t> uniqueids;
				uniqueids.push_back(_uniqueid);
				RCMANAGER.SendNotifyUpdateData(uniqueids);
			}
		}
	};

	std::shared_ptr<RCVoteCenterProcess> process = make_shared<RCVoteCenterProcess>(request->roleid(), request->resource_num(), request->point_index(), from);
	process->Begin();
}

void RoamCommunityManager::RequestEnhanceDefense_Center(PB::ipt_roam_community_op *request, const std::string& from)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid != manager_zoneid)
	{
		return;
	}

	struct RCEnhanceDefenseCenterProcess: public std::enable_shared_from_this<RCEnhanceDefenseCenterProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		std::string _battle_data_key;
		ruid_t _roleid;
		int _resource_num;
		int _point_index;
		int64_t _uniqueid;
		int _old_defense;
		int _new_defense;
		std::string _from;

		PB::roam_community_data _data;
		RCEnhanceDefenseCenterProcess(ruid_t roleid, int resource_num, int point_index, const std::string& from)
			: INIT_RET_VAR, _roleid(roleid), _resource_num(resource_num), _point_index(point_index), _old_defense(0), _new_defense(0), _from(from)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
			_battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
		}

		void Begin()
		{
			if (_resource_num <= 0)
			{
				ERR_FINISH_RETURN("Begin failed: invalid resource_num", GNET::ERROR_ROAM_COMMUNITY_INVALID_RESOURCE_NUM);
			}

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_point_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				ERR_FINISH_RETURN("Begin failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}
			if (iter_point_cfg->second.ispve == 1)
			{
				ERR_FINISH_RETURN("Begin failed: point is pve", GNET::ERROR_ROAM_COMMUNITY_CAN_NOT_ENHANCE_PVE);
			}

			if (RCMANAGER._battle_data.season_state() != RCSS_OPEN)
			{
				ERR_FINISH_RETURN("Begin failed: season close", GNET::ERROR_ROAM_COMMUNITY_SEASON_NOT_OPEN);
			}

			if ((RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_WAIT) && (RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE))
			{
				ERR_FINISH_RETURN("Begin failed: battle state can't enhance", GNET::ERROR_ROAM_COMMUNITY_BATTLE_STATE_CAN_NOT_ENHANCE);
			}

			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().Lock(_data_key, [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_data);

				time_t now = Timer::GetTime();
				if (now - foo->_data.base().create_time() < RCMANAGER.GetJoinTimeLimit())
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: create time limit", GNET::ERROR_ROAM_COMMUNITY_CREATE_TIME_LIMIT);
				}

				if (foo->_data.base().leader() != foo->_roleid)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: not leader", GNET::ERROR_ROAM_COMMUNITY_NOT_LEADER);
				}

				if (foo->_data.base().resource_num() < foo->_resource_num)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: resource not enough", GNET::ERROR_ROAM_COMMUNITY_RESOURCE_NOT_ENOUGH);
				}

				auto iter_point = RCMANAGER._points.find(foo->_point_index);
				if (iter_point == RCMANAGER._points.end())
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
				}

				if (iter_point->second.belong() != foo->_uniqueid)
				{
					OssUnlockShell shell(foo->_data_key);
					FOO_ERR_FINISH_RETURN("Check1 failed: can't enhance other point", GNET::ERROR_ROAM_COMMUNITY_CAN_NOT_ENHANCE_OTHER_POINT);
				}

				std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
				int resource_num = foo->_data.base().resource_num();
				foo->_data.mutable_base()->set_resource_num(resource_num - foo->_resource_num);
				std::string str_rc_data;
				PB_2_STR(foo->_data, str_rc_data);
				update_list_data(foo->_uniqueid, foo->_data, ULT_NORMAL);
				OSSInterface::GetInstance().HSet(foo->_data_key, data_field, str_rc_data, [](int retcode)
				{
				}, 0, 0, 1, false);

				foo->_old_defense = iter_point->second.defense();
				int add_defense = calc_defense(foo->_resource_num);
				foo->_new_defense = foo->_old_defense + add_defense;
				iter_point->second.set_defense(foo->_new_defense);

				int64_t data_version = RCMANAGER._battle_data.data_version();
				RCMANAGER._battle_data.set_data_version(data_version + 1);

				std::map<std::string, std::string> kv_map;
				std::string str_pt_data;
				PB_2_STR(iter_point->second, str_pt_data);
				kv_map.insert(std::make_pair(std::to_string(iter_point->first), str_pt_data));
				std::string str_bt_data;
				PB_2_STR(RCMANAGER._battle_data, str_bt_data);
				kv_map.insert(std::make_pair(data_field, str_bt_data));

				OSSInterface::GetInstance().HMSet(foo->_battle_data_key, kv_map, [](int retcode)
				{
				}, false);

				OssUnlockShell shell(foo->_data_key);

				foo->Finish();
			});
		}

		void Finish()
		{
			PB::ipt_roam_community_op_re ipt_request_re;
			ipt_request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_ENHANCE_DEFENSE);
			ipt_request_re.set_roleid(_roleid);
			ipt_request_re.set_resource_num(_resource_num);
			ipt_request_re.set_point_index(_point_index);
			ipt_request_re.set_retcode(_retcode);
			ipt_request_re.set_total_defense(_new_defense);
			NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, &ipt_request_re);

			std::vector<int64_t> uniqueids;
			uniqueids.push_back(_uniqueid);
			RCMANAGER.SendNotifyUpdateData(uniqueids);

			LOG_TRACE("DS::RoamCommunityManager::RequestEnhanceDefense_Center roleid=%ld, id=%ld, resource_num=%d, cur_resource_num=%ld, _point_index=%d, old_defense=%d, new_defense=%d, from=%s, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _resource_num, _data.base().resource_num(), _point_index, _old_defense, _new_defense, _from.c_str(), _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCEnhanceDefenseCenterProcess> process = make_shared<RCEnhanceDefenseCenterProcess>(request->roleid(), request->resource_num(), request->point_index(), from);
	process->Begin();
}

void RoamCommunityManager::GetBriefInfos(RoleInfo *pInfo, const std::vector< std::pair<ruid_t, std::string> >& id_names)
{
	if (!pInfo)
	{
		return;
	}

	//if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	//{
	//	return;
	//}

	PB::npt_player_search_brief_re re;
	re.set_search_type(BRIEF_STRUCT_TYPE_ROAM_COMMUNITY);
	for (auto& ruid_name : id_names)
	{
		PB::roam_community_base_info base_info;
		for (auto iter_brief = _brief_list.begin(); iter_brief != _brief_list.end(); ++iter_brief)
		{
			if ((ruid_name.first > 0 && ruid_name.first == iter_brief->base().id()) || (ruid_name.second.size() > 0 && ruid_name.second == iter_brief->base().name()))
			{
				base_info.CopyFrom(iter_brief->base());
				break;
			}
		}

		auto base_ptr = re.add_rc_bases();
		base_ptr->CopyFrom(base_info);
	}
	pInfo->SendMessage2Client(re);
}

void RoamCommunityManager::UploadBattleData()
{
	int64_t data_version = _battle_data.data_version();
	_battle_data.set_data_version(data_version + 1);

	std::string data_field = REDIS_UUID_KEY_DATA_FIELD_NAME;
	std::string str_bt_data;
	PB_2_STR(_battle_data, str_bt_data);

	std::string battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
	OSSInterface::GetInstance().HSet(battle_data_key, data_field, str_bt_data, [](int retcode)
	{
	}, 0, 0, 1, false);
	LOG_TRACE("DS::RoamCommunityManager::UploadBattleData new_battle_state=%d, old_version=%ld, new_version=%ld", _battle_data.state(), data_version, _battle_data.data_version());
}

int RoamCommunityManager::GetNextBattleId()
{
	int battle_id = _battle_data.battle_id();
	++battle_id;
	_battle_data.set_battle_id(battle_id);
	return battle_id;
}

void RoamCommunityManager::OnSeasonStart()
{
	_battle_data.set_state(PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_INVALID);
	//_battle_data.set_data_version(1);
	_battle_data.set_season_state(RCSS_OPEN);
	_battle_data.set_season_start_time(ROAM_COMMUNITY_CONFIG.season_start_time);
	_battle_data.set_battle_id(1);
	_battle_data.clear_winners();
	_battle_data.clear_faileds();
	_battle_data.clear_against_plan();
	_battle_data.set_activity_index(ROAM_COMMUNITY_CONFIG.activity_index);
	UploadBattleData();

	_winner_map.clear();
	_failed_map.clear();
	_will_create_battle_points.clear();
	_battle_info.clear();
	_player_quit_time.clear();
	LOG_TRACE("DS::RoamCommunityManager::OnSeasonStart");
}

void RoamCommunityManager::OnSeasonEnd()
{
	_battle_data.set_season_state(RCSS_CLOSE);
	UploadBattleData();

	LOG_TRACE("DS::RoamCommunityManager::OnSeasonEnd");
}


void RoamCommunityManager::OnChange2Vote()
{
	int old_battle_state = _battle_data.state();
	_battle_data.set_state(PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_VOTE);
	//_battle_data.set_data_version(1);
	_battle_data.set_battle_id(1);
	_battle_data.clear_winners();
	_battle_data.clear_faileds();
	_battle_data.clear_against_plan();
	_winner_map.clear();
	_failed_map.clear();
	_will_create_battle_points.clear();
	_battle_info.clear();
	_player_quit_time.clear();

	_battle_creators.clear();
	_create_battle_info.clear();
	_battleid_2_instid.clear();

	UploadBattleData();

	LOG_TRACE("DS::RoamCommunityManager::OnChange2Vote old_battle_state=%d, new_battle_state=%d", old_battle_state, _battle_data.state());
}

void RoamCommunityManager::OnChange2Wait()
{
	int old_battle_state = _battle_data.state();
	std::stringstream against_ss;
	against_ss << "against_list={";

	_battle_data.set_state(PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_WAIT);
	_battle_data.clear_against_plan();

	std::map<int64_t, int> need_return;
	std::map<int, std::map<std::string, std::string>> kv_map;
	int point_num = 0;
	for (auto iter_point = _points.begin(); iter_point != _points.end(); ++iter_point)
	{
		std::stringstream vote_ss;
		vote_ss << "[point" << iter_point->first << "]={vote:{";

		// 找到中标者，并计算所有需要退还的资源数
		int64_t attack_uniqueid = 0;
		int attack_resource = 0;
		time_t attack_timestamp = 0;
		for (int i = 0; i < iter_point->second.votes_size(); ++i)
		{
			auto vote_ptr = iter_point->second.mutable_votes(i);
			if ((attack_uniqueid == 0) || (vote_ptr->resource() > attack_resource) || (vote_ptr->resource() == attack_resource && vote_ptr->timestamp() < attack_timestamp))
			{
				attack_uniqueid = vote_ptr->id();
				attack_resource = vote_ptr->resource();
				attack_timestamp = vote_ptr->timestamp();
			}
			vote_ss << "(" << vote_ptr->id() << "|" << vote_ptr->resource() << ")";

			int ret_resource = vote_ptr->resource() * ROAM_COMMUNITY_CONFIG.return_resource_percent / 100.0f;
			need_return.insert(std::make_pair(vote_ptr->id(), ret_resource));
		}
		// 生成对阵表
		auto against_ptr = _battle_data.add_against_plan();
		against_ptr->set_index(iter_point->first);
		against_ptr->set_attacker(attack_uniqueid);
		against_ptr->set_defender(iter_point->second.belong());
		// 从退还列表中移除中标者
		auto iter_attack = need_return.find(attack_uniqueid);
		if (iter_attack != need_return.end())
		{
			need_return.erase(iter_attack);
		}

		vote_ss << "} against={" << attack_uniqueid << "|" << iter_point->second.belong() << "}},";
		LOG_TRACE("DS::RoamCommunityManager::OnChange2Wait point info %s", vote_ss.str().c_str());

		against_ss << "(" << iter_point->first << "|" << attack_uniqueid << "|" << iter_point->second.belong() << ")";

		iter_point->second.clear_votes();
		std::string str_point;
		PB_2_STR(iter_point->second, str_point);
		++point_num;
		kv_map[point_num / 10].insert(std::make_pair(std::to_string(iter_point->first), str_point));
	}
	against_ss << "}";
	LOG_TRACE("DS::RoamCommunityManager::OnChange2Wait against list %s", against_ss.str().c_str());

	UploadBattleData();
	std::string battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
	for (auto iter_kv = kv_map.begin(); iter_kv != kv_map.end(); ++iter_kv)
	{
		OSSInterface::GetInstance().HMSet(battle_data_key, iter_kv->second, [](int retcode)
		{
		}, false);
	}

	if (need_return.size() > 0)
	{
		std::shared_ptr<RCUpdateResourceProcess> process = make_shared<RCUpdateResourceProcess>(need_return, [](const std::map<int64_t, int>& _)
		{
			std::vector<int64_t> uniqueids;
			for (auto iter = _.begin(); iter != _.end(); ++iter)
			{
				uniqueids.push_back(iter->first);
			}
			RCMANAGER.SendNotifyUpdateData(uniqueids);
		});
		process->Begin();
	}
	LOG_TRACE("DS::RoamCommunityManager::OnChange2Wait old_battle_state=%d, new_battle_state=%d", old_battle_state, _battle_data.state());
}

void RoamCommunityManager::OnChange2Battle()
{
	int old_battle_state = _battle_data.state();
	_battle_data.set_state(PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE);
	_battle_data.set_battle_id(1);
	_battle_data.clear_winners();
	_battle_data.clear_faileds();
	_will_create_battle_points.clear();
	_winner_map.clear();
	_failed_map.clear();
	_battle_info.clear();
	_player_quit_time.clear();

	UploadBattleData();

	for (int i = 0; i < _battle_data.against_plan_size(); ++i)
	{
		_will_create_battle_points.push_back(_battle_data.against_plan(i));
	}
	LOG_TRACE("DS::RoamCommunityManager::OnChange2Battle old_battle_state=%d, new_battle_state=%d", old_battle_state, _battle_data.state());
}

class BattleEndRewardTimer : public Thread::Runnable
{
public:
	BattleEndRewardTimer() {}
	virtual void Run()
	{
		RCMANAGER.SendBattleEndReward();
		delete this;
	}
};

void RoamCommunityManager::OnChange2End()
{
	_battle_creators.clear();
	_create_battle_info.clear();
	_battleid_2_instid.clear();

	int old_battle_state = _battle_data.state();
	_battle_data.set_state(PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_END);
	UploadBattleData();

	if (ROAM_COMMUNITY_CONFIG.end_stage_send_reward_delay > 0)
	{
		IntervalTimer::Schedule(new BattleEndRewardTimer(), (1000000 * ROAM_COMMUNITY_CONFIG.end_stage_send_reward_delay) / IntervalTimer::Resolution());
	}
	else
	{
		SendBattleEndReward();
	}
	LOG_TRACE("DS::RoamCommunityManager::OnChange2End old_battle_state=%d, new_battle_state=%d", old_battle_state, _battle_data.state());

	int count = 1;
	std::stringstream belong_ss;
	belong_ss << "{";
	for (auto iter_point = _points.begin(); iter_point != _points.end(); ++iter_point, ++count)
	{
		belong_ss << "(" << iter_point->first << "|" << iter_point->second.belong() << "),";
		if (count % 5 == 0)
		{
			belong_ss << "}";
			LOG_TRACE("DS::RoamCommunityManager::OnChange2End belong=%s", belong_ss.str().c_str());
			belong_ss.str("");
			belong_ss.clear();
			belong_ss << "{";
		}
	}
	belong_ss << "}";
	LOG_TRACE("DS::RoamCommunityManager::OnChange2End belong=%s", belong_ss.str().c_str());
}

void RoamCommunityManager::SendBattleEndReward()
{
	// 发奖
	std::map<int64_t, int> rewards;
	for (auto iter_winner = _winner_map.begin(); iter_winner != _winner_map.end(); ++iter_winner)
	{
		int total_res = 0;
		int64_t uniqueid = iter_winner->first;
		for (auto iter_index = iter_winner->second.begin(); iter_index != iter_winner->second.end(); ++iter_index)
		{

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(*iter_index);
			if (iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.end())
			{
				total_res += iter_point_cfg->second.production;
			}
		}
		rewards[uniqueid] += total_res;
	}
	for (auto iter_failed = _failed_map.begin(); iter_failed != _failed_map.end(); ++iter_failed)
	{
		int total_res = 0;
		int64_t uniqueid = iter_failed->first;
		for (auto iter_index = iter_failed->second.begin(); iter_index != iter_failed->second.end(); ++iter_index)
		{

			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(*iter_index);
			if (iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.end())
			{
				total_res += iter_point_cfg->second.production_failed;
			}
		}
		rewards[uniqueid] += total_res;
	}
	_winner_map.clear();
	_failed_map.clear();

	if (rewards.size() > 0)
	{
		std::shared_ptr<RCUpdateResourceProcess> process = make_shared<RCUpdateResourceProcess>(rewards, [](const std::map<int64_t, int>& _)
		{
			std::vector<int64_t> uniqueids;
			for (auto iter = _.begin(); iter != _.end(); ++iter)
			{
				uniqueids.push_back(iter->first);
			}
			RCMANAGER.SendNotifyUpdateData(uniqueids);
		});
		process->Begin();
	}
}

void RoamCommunityManager::TryCreateBattle(int index, int64_t attacker, int64_t defender, int64_t roleid, int role_zoneid, int level, int defense, int64_t uniqueid, int occupy_times)
{
	if (attacker == 0 && defender == 0)
	{
		LOG_TRACE("DS::RoamCommunityManager::TryCreateBattle no creator, index=%d, attacker=%ld, defender=%ld, roleid=%ld, role_zoneid=%d, uniqueid=%ld\n", index, attacker, defender, roleid, role_zoneid, uniqueid);
		return;
	}

	auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(index);
	if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
	{
		LOG_TRACE("DS::RoamCommunityManager::TryCreateBattle not find index, index=%d, attacker=%ld, defender=%ld, roleid=%ld, role_zoneid=%d, uniqueid=%ld\n", index, attacker, defender, roleid, role_zoneid, uniqueid);
		return;
	}

	int battle_id = GetNextBattleId();
	int param = (index << 16) | battle_id;
	zone_id_t center_battle_zoneid = GNET::DistributedCenter::Instance().GetRandomCenter(true);
	if (DistributedCenter::Instance().IsRandomCenterMasterEnable())
	{
		center_battle_zoneid = GNET::DistributedCenter::Instance().GetRandomCenter(false);
	}

	LOG_TRACE("DS::RoamCommunityManager::TryCreateBattle battle_id=%d, battle_type=%d, index=%d, center_zoneid=%d, attacker=%ld, defender=%ld, roleid=%ld, role_zoneid=%d, uniqueid=%ld\n", battle_id, iter_point_cfg->second.instance_id, index, center_battle_zoneid, attacker, defender, roleid, role_zoneid, uniqueid);

	if (center_battle_zoneid == g_zoneid)
	{
		GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
		std::vector<ruid_t> uniqueids;
		uniqueids.push_back(attacker);
		uniqueids.push_back(defender);
		((CenterRoamCommunityBattleServer *)battle_ptr)->CreateBattle(iter_point_cfg->second.instance_id, uniqueids, 0, param, level, defense, occupy_times);
		GET_CENTER_BATTLE_END
	}
	else
	{
		PB::ipt_center_battle_transfer_player transfer_player;
		transfer_player.set_center_battle_type(CBT_ROAM_COMMUNITY);
		transfer_player.set_battle_type(iter_point_cfg->second.instance_id);
		transfer_player.set_param4(param);
		auto attacker_player_ptr = transfer_player.add_players();
		attacker_player_ptr->set_roleid(attacker);
		auto defender_player_ptr = transfer_player.add_players();
		defender_player_ptr->set_roleid(defender);
		transfer_player.add_params(level);
		transfer_player.add_params(defense);
		transfer_player.add_params(occupy_times);

		HUB_CLIENT.TransferProtocol(center_battle_zoneid, transfer_player, 0);
	}

	battle_info info;
	info.attacker = attacker;
	info.defender = defender;
	info.create_finish = false;
	info.battle_finish = false;
	info.inst_id = 0;
	info.inst_tid = iter_point_cfg->second.instance_id;
	info.zoneid = center_battle_zoneid;
	info.battle_id = battle_id;
	if (roleid > 0)
	{
		info.wait_players.insert(std::make_pair(roleid, std::make_pair(role_zoneid, uniqueid)));
	}
	_battle_info[index].push_back(info);
}

void RoamCommunityManager::TryTeleportPlayer(ruid_t roleid, int battle_type, int inst_id, int center_zoneid, int index, int64_t uniqueid)
{
	PB::ipt_center_battle_roam_teleport_player proto;
	proto.set_roleid(roleid);
	proto.set_center_battle_type(CBT_ROAM_COMMUNITY);
	proto.set_center_zoneid(center_zoneid);
	proto.set_world_tid(battle_type);
	proto.set_world_id(inst_id);
	proto.mutable_param()->set_point_index(index);
	proto.mutable_param()->set_roam_communityid(uniqueid);

	HUB_CLIENT.TransferProtocol(MERGE_ZONE(roleid), proto, roleid);
	LOG_TRACE("DS::RoamCommunityManager::TryTeleportPlayer roleid=%ld, battle_type=%d, inst_id=%d, center_zoneid=%d, index=%d, uniqueid=%ld\n", roleid, battle_type, inst_id, center_zoneid, index, uniqueid);
}

bool RoamCommunityManager::CheckRoamBattle(ruid_t roleid, int& inst_id)
{
	bool find_role = false;
	bool has_param = false;
	bool find_index = false;
	bool find_instid = false;
	int index = -1;
	int64_t uniqueid = 0;
	RoleInfo *pRole = RoleMap::Instance().Find(roleid);
	if (pRole)
	{
		find_role = true;
		if (pRole->roam_teleport_param.has_point_index() && pRole->roam_teleport_param.has_roam_communityid())
		{
			has_param = true;
			index = pRole->roam_teleport_param.point_index();
			uniqueid = pRole->roam_teleport_param.roam_communityid();

			auto iter_index = _create_battle_info.find(index);
			if (iter_index != _create_battle_info.end())
			{
				find_index = true;
				auto iter_uniqueid = iter_index->second.find(uniqueid);
				if (iter_uniqueid != iter_index->second.end())
				{
					find_instid = true;
					inst_id = iter_uniqueid->second;
				}
			}
		}
	}
	LOG_TRACE("DS::RoamCommunityManager::CheckRoamBattle roleid=%ld, find_role=%d, has_param=%d, find_index=%d, index=%d, find_instid=%d, uniqueid=%ld\n", roleid, find_role, has_param, find_index, index, find_instid, uniqueid);
	return find_instid;
}

void RoamCommunityManager::OnTransferPlayer(const PB::ipt_center_battle_transfer_player& transfer)
{
	std::vector<ruid_t> uniqueids;
	ruid_t attacker = 0;
	ruid_t defender = 0;
	if (transfer.players_size() > 0)
	{
		attacker = transfer.players(0).roleid();
		uniqueids.push_back(attacker);
	}
	if (transfer.players_size() > 1)
	{
		defender = transfer.players(1).roleid();
		uniqueids.push_back(defender);
	}

	int index = (transfer.param4() >> 16) & 0xffff;
	int battle_id = transfer.param4() & 0xffff;

	int level = 0;
	int defense = 0;
	int occupy_times = 0;
	if (transfer.params_size() > 0)
	{
		level = transfer.params(0);
	}
	if (transfer.params_size() > 1)
	{
		defense = transfer.params(1);
	}
	if (transfer.params_size() > 2)
	{
		occupy_times = transfer.params(2);
	}


	LOG_TRACE("DS::RoamCommunityManager::OnTransferPlayer battle_type=%d, battle_id=%d, index=%d, attacker=%ld, defender=%ld, level=%d, defense=%d, occupy_times=%d\n", transfer.battle_type(), battle_id, index, attacker, defender, level, defense, occupy_times);

	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	((CenterRoamCommunityBattleServer *)battle_ptr)->CreateBattle(transfer.battle_type(), uniqueids, 0, transfer.param4(), level, defense, occupy_times);
	GET_CENTER_BATTLE_END
}

void RoamCommunityManager::PreCreateBattle(int instid, int battle_id, int64_t attacker, int64_t defender)
{
	_battle_creators[instid].attacker = attacker;
	_battle_creators[instid].defender = defender;
	_battleid_2_instid[battle_id] = instid;
	LOG_TRACE("DS::RoamCommunityManager::PreCreateBattle instid=%d, battle_id=%d, attacker=%ld, defender=%ld\n", instid, battle_id, attacker, defender);
}

void RoamCommunityManager::OnCreateBattle(int instid, int battle_type, int battle_id, int param, int ret)
{
	int index = (param >> 16) & 0xffff;
	auto iter_creators = _battle_creators.find(instid);
	int64_t attacker = 0;
	int64_t defender = 0;
	if (iter_creators != _battle_creators.end())
	{
		attacker = iter_creators->second.attacker;
		defender = iter_creators->second.defender;
		if (!ret)
		{
			auto& uinqueid_inst_map = _create_battle_info[index];
			if (attacker > 0)
			{
				uinqueid_inst_map[iter_creators->second.attacker] = instid;
			}
			if (defender > 0)
			{
				uinqueid_inst_map[iter_creators->second.defender] = instid;
			}
		}
		_battle_creators.erase(iter_creators);
	}

	LOG_TRACE("DS::RoamCommunityManager::OnCreateBattle instid=%d, battle_type=%d, battle_id=%d, index=%d, attaker=%ld, defender=%ld, ret=%d\n", instid, battle_type, battle_id, index, attacker, defender, ret);

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid == manager_zoneid)
	{
		auto iter_index = _battle_info.find(index);
		if (iter_index == _battle_info.end())
		{
			return;
		}

		if (ret)
		{
			_battle_info.erase(iter_index);
			return;
		}

		int enter_num = 0;
		for (auto iter_battle = iter_index->second.begin(); iter_battle != iter_index->second.end(); ++iter_battle)
		{
			if (iter_battle->battle_id == battle_id)
			{
				iter_battle->create_finish = true;
				iter_battle->inst_id = instid;
				for (auto iter_player = iter_battle->wait_players.begin(); iter_player != iter_battle->wait_players.end();)
				{
					TryTeleportPlayer(iter_player->first, iter_battle->inst_tid, iter_battle->inst_id, iter_battle->zoneid, iter_index->first, iter_player->second.second);
					iter_player = iter_battle->wait_players.erase(iter_player);
					++enter_num;
				}
				break;
			}
		}
	}
	else
	{
		GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
		((CenterRoamCommunityBattleServer *)battle_ptr)->SendCenterBattleCreateFinishNotify(instid, battle_type, battle_id, GNET::g_zoneid, param, ret);
		GET_CENTER_BATTLE_END
	}
}

void RoamCommunityManager::RequestApplyBattle(RoleInfo *pRole, PB::npt_roam_community_op *request)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunity))
	{
		return;
	}

	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	if (DistributedCenter::Instance().IsCenter(GNET::g_zoneid))
	{
		return;
	}

	struct RCApplyBattleProcess: public std::enable_shared_from_this<RCApplyBattleProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		int _point_index;
		int64_t _uniqueid;
		bool _ispve;
		int _inst_tid;

		PB::roam_community_data::member_info _self;
		RCApplyBattleProcess(ruid_t roleid, int point_index)
			: INIT_RET_VAR, _roleid(roleid), _point_index(point_index), _uniqueid(0), _ispve(0), _inst_tid(0)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_point_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				ERR_FINISH_RETURN("Begin failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}
			_ispve = iter_point_cfg->second.ispve;
			_inst_tid = iter_point_cfg->second.instance_id;

			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			if (RCMANAGER._battle_data.season_state() != RCSS_OPEN)
			{
				ERR_FINISH_RETURN("Check3 failed: season close", GNET::ERROR_ROAM_COMMUNITY_SEASON_NOT_OPEN);
			}

			if (RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE)
			{
				ERR_FINISH_RETURN("Check3 failed: not vote state", GNET::ERROR_ROAM_COMMUNITY_NOT_BATTLE_STATE);
			}

			bool can_enter = false;
			if (_ispve == 0)
			{
				for (int i = 0; i < RCMANAGER._battle_data.against_plan_size(); ++i)
				{
					auto against_ptr = RCMANAGER._battle_data.mutable_against_plan(i);
					if (against_ptr->index() == _point_index)
					{
						if (_uniqueid == against_ptr->attacker() || _uniqueid == against_ptr->defender())
						{
							can_enter = true;
						}
						break;
					}
				}
			}
			else
			{
				can_enter = true;
				for (int i = 0; i < RCMANAGER._battle_data.against_plan_size(); ++i)
				{
					auto against_ptr = RCMANAGER._battle_data.mutable_against_plan(i);
					if (_uniqueid == against_ptr->attacker() || _uniqueid == against_ptr->defender())
					{
						can_enter = false;
						break;
					}
				}
			}

			if (!can_enter)
			{
				ERR_FINISH_RETURN("Check3 failed: can not enter", GNET::ERROR_ROAM_COMMUNITY_NOT_SELF_RC_BATTLE);
			}

			Check4();
		}

		void Check4()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGet(_data_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check4 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check4 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_self);

				time_t now = Timer::GetTime();
				if (now - foo->_self.join_time() < RCMANAGER.GetJoinTimeLimit())
				{
					FOO_ERR_FINISH_RETURN("Check4 failed: join time limit", GNET::ERROR_ROAM_COMMUNITY_JOIN_TIME_LIMIT);
				}

				foo->Finish();
			});
		}

		void Finish()
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
			if (pRole)
			{
				if (_retcode)
				{
					PB::npt_roam_community_op_re request_re;
					request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_APPLY_BATTLE);
					request_re.set_point_index(_point_index);
					request_re.set_retcode(_retcode);
					pRole->SendMessage2Client(request_re);
				}
				else
				{
					GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
					((CenterRoamCommunityBattleClient *)battle_ptr)->SendRoamPlayerApplyCenterBattle(_roleid, _inst_tid, GNET::g_zoneid, _point_index);
					GET_CENTER_BATTLE_END
				}
			}

			LOG_TRACE("DS::RoamCommunityManager::RCApplyBattleProcess roleid=%ld, id=%ld, inst_tid=%d, _point_index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _inst_tid, _point_index, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCApplyBattleProcess> process = make_shared<RCApplyBattleProcess>(pRole->roleid, request->point_index());
	process->Begin();
}

void RoamCommunityManager::OnPlayerApplyBattle(ruid_t roleid, int inst_tid, int index, int param2, zone_id_t zoneid)
{
	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunity))
	{
		return;
	}

	if (!FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRoamCommunityBattle))
	{
		return;
	}

	struct RCOnApplyBattleProcess: public std::enable_shared_from_this<RCOnApplyBattleProcess>
	{
		DECLARE_RET_VAR

		std::string _ruid_key;
		std::string _data_key;
		ruid_t _roleid;
		int _role_zoneid;
		int _point_index;
		int64_t _uniqueid;
		bool _ispve;
		int _inst_tid;
		int64_t _attacker;
		int64_t _defender;
		int _level;
		int _defense;
		int _occupy_times;

		PB::roam_community_data::member_info _self;
		RCOnApplyBattleProcess(ruid_t roleid, int role_zoneid, int point_index)
			: INIT_RET_VAR, _roleid(roleid), _role_zoneid(role_zoneid), _point_index(point_index), _uniqueid(0), _ispve(0), _inst_tid(0), _attacker(0), _defender(0), _level(0), _defense(0), _occupy_times(0)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(_point_index);
			if (iter_point_cfg == ROAM_COMMUNITY_CONFIG.points.end())
			{
				ERR_FINISH_RETURN("Begin failed: invalid index", GNET::ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX);
			}
			_ispve = iter_point_cfg->second.ispve;
			_inst_tid = iter_point_cfg->second.instance_id;
			_level = iter_point_cfg->second.level;
			_defense = iter_point_cfg->second.defense;
			if (_ispve == 0)
			{
				auto iter_point = RCMANAGER._points.find(_point_index);
				if (iter_point != RCMANAGER._points.end())
				{
					_defense = iter_point->second.defense();
					_occupy_times = iter_point->second.occupy_times();
					if (iter_point->second.belong() == 0)
					{
						_occupy_times = 0;
					}
					else if (_occupy_times == 0)
					{
						_occupy_times = 1;
					}
				}
			}

			time_t now = Timer::GetTime();
			if (now - RCMANAGER._player_quit_time[_roleid] < ROAM_COMMUNITY_CONFIG.enter_interval_limit)
			{
				ERR_FINISH_RETURN("Begin failed: enter interval limit", GNET::ERROR_ROAM_COMMUNITY_ENTER_INTERVAL_LIMIT);
			}

			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			if (RCMANAGER._battle_data.season_state() != RCSS_OPEN)
			{
				ERR_FINISH_RETURN("Check1 failed: season close", GNET::ERROR_ROAM_COMMUNITY_SEASON_NOT_OPEN);
			}

			if (RCMANAGER._battle_data.state() != PB::ROAM_COMMUNITY_BATTLE_STATE::RCBS_BATTLE)
			{
				ERR_FINISH_RETURN("Check1 failed: not vote state", GNET::ERROR_ROAM_COMMUNITY_NOT_BATTLE_STATE);
			}

			bool can_enter = false;
			if (_ispve == 0)
			{
				for (int i = 0; i < RCMANAGER._battle_data.against_plan_size(); ++i)
				{
					auto against_ptr = RCMANAGER._battle_data.mutable_against_plan(i);
					if (against_ptr->index() == _point_index)
					{
						if (_uniqueid == against_ptr->attacker() || _uniqueid == against_ptr->defender())
						{
							can_enter = true;
							_attacker = against_ptr->attacker();
							_defender = against_ptr->defender();
						}
						break;
					}
				}
				if (!can_enter)
				{
					ERR_FINISH_RETURN("Check1 failed: can not enter", GNET::ERROR_ROAM_COMMUNITY_NOT_SELF_RC_BATTLE);
				}
			}
			else
			{
				can_enter = true;
				_attacker = _uniqueid;
				_defender = 0;
				// 不能有PVP战场
				for (int i = 0; i < RCMANAGER._battle_data.against_plan_size(); ++i)
				{
					auto against_ptr = RCMANAGER._battle_data.mutable_against_plan(i);
					if (_uniqueid == against_ptr->attacker() || _uniqueid == against_ptr->defender())
					{
						can_enter = false;
						break;
					}
				}
				if (!can_enter)
				{
					ERR_FINISH_RETURN("Check1 failed: can not enter", GNET::ERROR_ROAM_COMMUNITY_NOT_SELF_RC_BATTLE);
				}
				// 并且不能有其他PVE战场
				for (auto iter = RCMANAGER._battle_info.begin(); iter != RCMANAGER._battle_info.end(); ++iter)
				{
					for (auto iter_battle = iter->second.begin(); iter_battle != iter->second.end(); ++iter_battle)
					{
						if (iter->first != _point_index)
						{
							if (iter_battle->attacker == _uniqueid || iter_battle->defender == _uniqueid)
							{
								can_enter = false;
								break;
							}
						}
					}
				}
				if (!can_enter)
				{
					ERR_FINISH_RETURN("Check1 failed: can not enter", GNET::ERROR_ROAM_COMMUNITY_HAS_JOIN_OTHER_PVE_BATTLE);
				}
			}


			Check2();
		}

		void Check2()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGet(_data_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check2 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check2 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_self);

				time_t now = Timer::GetTime();
				if (now - foo->_self.join_time() < RCMANAGER.GetJoinTimeLimit())
				{
					FOO_ERR_FINISH_RETURN("Check2 failed: join time limit", GNET::ERROR_ROAM_COMMUNITY_JOIN_TIME_LIMIT);
				}

				foo->Do();
			});
		}

		void Do()
		{
			auto iter = RCMANAGER._battle_info.find(_point_index);
			if (iter == RCMANAGER._battle_info.end())
			{
				time_t now = Timer::GetTime();
				time_t week_begin = GetLocalWeekBegin();
				time_t wait_to_battle_time = week_begin + ROAM_COMMUNITY_CONFIG.wait_to_battle_time;
				if (!RCMANAGER._debug_model && now >= ROAM_COMMUNITY_CONFIG.allow_create_instance_time + wait_to_battle_time)
				{
					ERR_FINISH_RETURN("Do failed: create time limit", GNET::ERROR_ROAM_COMMUNITY_CREATE_INSTANCE_TIME_LIMIT);
				}
				LOG_TRACE("DS::RoamCommunityManager::RCOnApplyBattleProcess roleid=%ld, id=%ld, inst_tid=%d, _point_index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _inst_tid, _point_index, _err_msg.c_str(), _redis_ret, _retcode);
				RCMANAGER.TryCreateBattle(_point_index, _attacker, _defender, _roleid, _role_zoneid, _level, _defense, _uniqueid, _occupy_times);
			}
			else
			{
				bool has_create = false;
				for (auto iter_battle = iter->second.begin(); iter_battle != iter->second.end(); ++iter_battle)
				{
					if (iter_battle->attacker == _uniqueid || iter_battle->defender == _uniqueid)
					{
						if (iter_battle->battle_finish)
						{
							ERR_FINISH_RETURN("Do failed: battle finished", GNET::ERROR_ROAM_COMMUNITY_BATTLE_FINISHED);
							return;
						}
						if (!iter_battle->create_finish)
						{
							iter_battle->wait_players.insert(std::make_pair(_roleid, std::make_pair(_role_zoneid, _uniqueid)));
						}
						else
						{
							LOG_TRACE("DS::RoamCommunityManager::RCOnApplyBattleProcess roleid=%ld, id=%ld, inst_tid=%d, _point_index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _inst_tid, _point_index, _err_msg.c_str(), _redis_ret, _retcode);
							RCMANAGER.TryTeleportPlayer(_roleid, iter_battle->inst_tid, iter_battle->inst_id, iter_battle->zoneid, _point_index, _uniqueid);
						}

						has_create = true;
						break;
					}
				}
				if (!has_create)
				{
					LOG_TRACE("DS::RoamCommunityManager::RCOnApplyBattleProcess roleid=%ld, id=%ld, inst_tid=%d, _point_index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _inst_tid, _point_index, _err_msg.c_str(), _redis_ret, _retcode);
					RCMANAGER.TryCreateBattle(_point_index, _attacker, _defender, _roleid, _role_zoneid, _level, _defense, _uniqueid, _occupy_times);
				}
			}
		}

		void Finish()
		{
			if (_retcode)
			{
				RoleInfo *pRole = RoleMap::Instance().FindOnline(_roleid);
				if (pRole)
				{
					PB::npt_roam_community_op_re request_re;
					request_re.set_op(PB::ROAM_COMMUNITY_OP::RCO_APPLY_BATTLE);
					request_re.set_point_index(_point_index);
					request_re.set_retcode(_retcode);
					pRole->SendMessage2Client(request_re);
				}
				else if (_role_zoneid != g_zoneid)
				{
					PB::ipt_center_battle_roam_player_apply_re proto;
					proto.set_roleid(_roleid);
					proto.set_center_battle_type(CBT_ROAM_COMMUNITY);
					proto.set_battle_type(_inst_tid);
					proto.set_ret(_retcode);
					HUB_CLIENT.TransferProtocol(_role_zoneid, proto, _roleid);
				}
				LOG_TRACE("DS::RoamCommunityManager::RCOnApplyBattleProcess roleid=%ld, id=%ld, inst_tid=%d, _point_index=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _inst_tid, _point_index, _err_msg.c_str(), _redis_ret, _retcode);
			}
		}
	};

	std::shared_ptr<RCOnApplyBattleProcess> process = make_shared<RCOnApplyBattleProcess>(roleid, zoneid, index);
	process->Begin();
}


void RoamCommunityManager::OnPlayerChangeScene(ruid_t roleid, int inst_tid)
{
	bool enter = false;
	RoleInfo *pRole = RoleMap::Instance().Find(roleid);
	if (pRole)
	{
		if (pRole->roam_teleport_param.has_point_index() && pRole->roam_teleport_param.has_roam_communityid())
		{
			int index = pRole->roam_teleport_param.point_index();
			int64_t uniqueid = pRole->roam_teleport_param.roam_communityid();

			auto iter_index = _create_battle_info.find(index);
			if (iter_index != _create_battle_info.end())
			{
				auto iter_uniqueid = iter_index->second.find(uniqueid);
				if (iter_uniqueid != iter_index->second.end())
				{
					int inst_id = iter_uniqueid->second;
					InstanceData *pInstance = InstanceManager::Instance().Find(inst_id);
					if (pInstance && pInstance->world_tid == inst_tid)
					{
						_inst_players[inst_id].insert(roleid);
						enter = true;
						LOG_TRACE("DS::RoamCommunityManager::OnPlayerChagneZone roleid=%ld, inst_tid=%d, enter_battle=%d", roleid, inst_tid, enter);
					}
				}
			}
		}
	}
}

void RoamCommunityManager::OnPlayerLogout(ruid_t roleid)
{
	time_t now = Timer::GetTime();
	bool leave = false;
	int world_tid = 0;
	for (auto iter_inst = _inst_players.begin(); iter_inst != _inst_players.end(); ++iter_inst)
	{
		auto iter_player = iter_inst->second.find(roleid);
		if (iter_player != iter_inst->second.end())
		{
			iter_inst->second.erase(iter_player);
			InstanceData *pInstance = InstanceManager::Instance().Find(iter_inst->first);
			if (pInstance && pInstance->world_tid == iter_inst->first)
			{
				world_tid = pInstance->world_tid;
			}
			leave = true;
		}
	}

	if (leave)
	{
		LOG_TRACE("DS::RoamCommunityManager::OnPlayerLogout roleid=%ld, leave_battle=%d, now=%ld", roleid, leave, now);
		GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
		if (GNET::g_zoneid == battle_ptr->GetCenterZoneID())
		{
			battle_ptr->OnPlayerQuit(roleid, world_tid, 0, 0);
		}
		else
		{
			PB::ipt_center_battle_roam_player_quit proto;
			proto.set_roleid(roleid);
			proto.set_center_battle_type(CBT_ROAM_COMMUNITY);
			proto.set_battle_type(world_tid);
			HUB_CLIENT.TransferProtocol(battle_ptr->GetCenterZoneID(), proto, roleid);
		}
		GET_CENTER_BATTLE_END
	}
}

void RoamCommunityManager::OnPlayerQuitBattle(ruid_t roleid, time_t timestamp)
{
	_player_quit_time[roleid] = timestamp;
}

void RoamCommunityManager::OnCenterBattleResult(const ipt_send_center_battle_result& msg)
{
	int battle_id = msg.param() & 0xffff;
	int index = (msg.param() >> 16) & 0xffff;

	auto iter_b2i = _battleid_2_instid.find(battle_id);
	if (iter_b2i != _battleid_2_instid.end())
	{
		_inst_players.erase(iter_b2i->second);
		_battleid_2_instid.erase(iter_b2i);
	}
	auto& uinqueid_inst_map = _create_battle_info[index];
	for (auto iter_inst = uinqueid_inst_map.begin(); iter_inst != uinqueid_inst_map.end();)
	{
		if (iter_inst->second == msg.instid())
		{
			iter_inst = uinqueid_inst_map.erase(iter_inst);
		}
		else
		{
			++iter_inst;
		}
	}

	LOG_TRACE("DS::RoamCommunityManager::OnCenterBattleResult instid=%d, winner=%ld, loser=%ld, battle_id=%d, index=%d\n", msg.instid(), msg.winner(), msg.loser(), battle_id, index);

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid != manager_zoneid)
	{
		HUB_CLIENT.TransferProtocol(manager_zoneid, msg, 0);
		return;
	}

	auto iter_index = _battle_info.find(index);
	if (iter_index == _battle_info.end())
	{
		return;
	}

	for (auto iter_battle = iter_index->second.begin(); iter_battle != iter_index->second.end(); ++iter_battle)
	{
		if (iter_battle->battle_id == battle_id)
		{
			if (iter_battle->battle_finish)
			{
				return;
			}
			iter_battle->battle_finish = true;
			break;
		}
	}

	if (msg.winner() > 0)
	{
		_winner_map[msg.winner()].insert(index);
		PB::roam_community_battle_result *winner_ptr = NULL;
		for (int i = 0; i < _battle_data.winners_size(); ++i)
		{
			auto tmp_ptr = _battle_data.mutable_winners(i);
			if (tmp_ptr->uniqueid() == msg.winner())
			{
				winner_ptr = tmp_ptr;
				bool find_index = false;
				for (int j = 0; j < winner_ptr->indexes_size(); ++j)
				{
					if (winner_ptr->indexes(j) == index)
					{
						find_index = true;
						break;
					}
				}
				if (!find_index)
				{
					winner_ptr->add_indexes(index);
				}
				break;
			}
		}
		if (!winner_ptr)
		{
			winner_ptr = _battle_data.add_winners();
			winner_ptr->set_uniqueid(msg.winner());
			winner_ptr->add_indexes(index);
		}
	}
	if (msg.loser() > 0)
	{
		_failed_map[msg.loser()].insert(index);
		PB::roam_community_battle_result *failed_ptr = NULL;
		for (int i = 0; i < _battle_data.faileds_size(); ++i)
		{
			auto tmp_ptr = _battle_data.mutable_faileds(i);
			if (tmp_ptr->uniqueid() == msg.loser())
			{
				failed_ptr = tmp_ptr;
				bool find_index = false;
				for (int j = 0; j < failed_ptr->indexes_size(); ++j)
				{
					if (failed_ptr->indexes(j) == index)
					{
						find_index = true;
						break;
					}
				}
				if (!find_index)
				{
					failed_ptr->add_indexes(index);
				}
				break;
			}
		}
		if (!failed_ptr)
		{
			failed_ptr = _battle_data.add_faileds();
			failed_ptr->set_uniqueid(msg.loser());
			failed_ptr->add_indexes(index);
		}
	}
	UploadBattleData();

	// 找个合适的机会重置belong和据点防御
	auto iter_point = RCMANAGER._points.find(index);
	if (iter_point != RCMANAGER._points.end())
	{
		//if (msg.winner() > 0 && iter_point->second.belong() != msg.winner())
		if (msg.winner() > 0)
		{
			int occupy_times = iter_point->second.occupy_times() < 1 ? 1 : iter_point->second.occupy_times();
			int new_occupy_times = iter_point->second.belong() != msg.winner() ? 1 : occupy_times + 1;
			iter_point->second.set_occupy_times(new_occupy_times);
			iter_point->second.set_belong(msg.winner());
			if (iter_point->second.belong() != msg.winner())
			{
				auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(index);
				if (iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.end())
				{
					iter_point->second.set_defense(iter_point_cfg->second.defense);
				}
			}

			std::string str_pt_data;
			PB_2_STR(iter_point->second, str_pt_data);
			std::string battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
			OSSInterface::GetInstance().HSet(battle_data_key, std::to_string(index), str_pt_data, [](int retcode)
			{
			}, 0, 0, 1, false);
		}
	}
}

void RoamCommunityManager::OnCenterBattleClose(int battle_type, int battle_id, int param)
{
	int instid = 0;
	int index = (param >> 16) & 0xffff;
	auto iter_b2i = _battleid_2_instid.find(battle_id);
	if (iter_b2i != _battleid_2_instid.end())
	{
		instid = iter_b2i->second;
		_battleid_2_instid.erase(iter_b2i);

		auto& uinqueid_inst_map = _create_battle_info[index];
		for (auto iter_inst = uinqueid_inst_map.begin(); iter_inst != uinqueid_inst_map.end();)
		{
			if (iter_inst->second == instid)
			{
				iter_inst = uinqueid_inst_map.erase(iter_inst);
			}
			else
			{
				++iter_inst;
			}
		}

		_inst_players.erase(instid);
	}

	LOG_TRACE("DS::RoamCommunityManager::OnCenterBattleClose instid=%d, battle_type=%d, battle_id=%d, index=%d\n", instid, battle_type, battle_id, index);

	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid != manager_zoneid)
	{
		return;
	}

	auto iter_index = _battle_info.find(index);
	if (iter_index == _battle_info.end())
	{
		return;
	}

	for (auto iter_battle = iter_index->second.begin(); iter_battle != iter_index->second.end(); ++iter_battle)
	{
		if (iter_battle->battle_id == battle_id)
		{
			if (iter_battle->battle_finish)
			{
				return;
			}
			iter_battle->battle_finish = true;
			break;
		}
	}
}

void RoamCommunityManager::GMAddResourceNum(int64_t roleid, int64_t uniqueid, int resource_num)
{
	if (uniqueid > 0)
	{
		std::map<int64_t, int> resource;
		resource.insert(std::make_pair(uniqueid, resource_num));
		std::shared_ptr<RCUpdateResourceProcess> process = make_shared<RCUpdateResourceProcess>(resource, [](const std::map<int64_t, int>& _)
		{
			std::vector<int64_t> uniqueids;
			for (auto iter = _.begin(); iter != _.end(); ++iter)
			{
				uniqueids.push_back(iter->first);
			}
			RCMANAGER.SendNotifyUpdateData(uniqueids);
		});
		process->Begin();
		LOG_TRACE("DS::RoamCommunityManager::GMAddResourceNum uniqueid=%ld, resource_num=%d", uniqueid, resource_num);
	}
	else
	{

		std::string ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		OSSInterface::GetInstance().HGet(ruid_key, std::to_string(roleid), [roleid, resource_num](int retcode, const string & data)
		{
			if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
			{
				int64_t uniqueid = atoll(data.c_str());
				std::map<int64_t, int> resource;
				resource.insert(std::make_pair(uniqueid, resource_num));
				std::shared_ptr<RCUpdateResourceProcess> process = make_shared<RCUpdateResourceProcess>(resource, [](const std::map<int64_t, int>& _)
				{
					std::vector<int64_t> uniqueids;
					for (auto iter = _.begin(); iter != _.end(); ++iter)
					{
						uniqueids.push_back(iter->first);
					}
					RCMANAGER.SendNotifyUpdateData(uniqueids);
				});
				process->Begin();
			}
		});
	}
}

void RoamCommunityManager::GMGetUniqueid(int64_t roleid)
{
	std::string ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
	OSSInterface::GetInstance().HGet(ruid_key, std::to_string(roleid), [roleid](int retcode, const string & data)
	{
		if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
		{
			RoleInfo *pRole = RoleMap::Instance().FindOnline(roleid);
			if (pRole)
			{
				std::stringstream ss;
				ss << "role:" << roleid << " roam_communityid:" << data.c_str();

				ChatPublic msg;
				msg.roleid = pRole->roleid;
				msg.channel = 0;
				msg.emotion = 0;
				msg.nation = pRole->nation;
				msg.msg = Octets(ss.str().c_str(), strlen(ss.str().c_str()));
				CharsetConverter::conv_charset_t2u(msg.msg, msg.msg);
				msg.localsid = 0;
				GDeliveryServer::GetInstance()->SendMarshalData(pRole->linksid, pRole->localsid, msg);
			}
		}
	});
}

void RoamCommunityManager::GMSetPointBelong(int index, int64_t uniqueid, bool transfer)
{
	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid == manager_zoneid)
	{
		auto iter_point = RCMANAGER._points.find(index);
		if (iter_point != RCMANAGER._points.end())
		{
			if (iter_point->second.belong() != uniqueid)
			{
				iter_point->second.set_belong(uniqueid);
				auto iter_point_cfg = ROAM_COMMUNITY_CONFIG.points.find(index);
				if (iter_point_cfg != ROAM_COMMUNITY_CONFIG.points.end())
				{
					iter_point->second.set_defense(iter_point_cfg->second.defense);
				}

				std::string str_pt_data;
				PB_2_STR(iter_point->second, str_pt_data);
				std::string battle_data_key = GetOssKeyWord(OSS_RC_BATTLE_DATA, 0, "TEST");
				OSSInterface::GetInstance().HSet(battle_data_key, std::to_string(index), str_pt_data, [](int retcode)
				{
				}, 0, 0, 1, false);
			}
		}
	}
	else if (transfer)
	{
		PB::ipt_roam_community_op ipt_request;
		ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_GM_SET_BELONG);
		ipt_request.set_point_index(index);
		ipt_request.set_communityid(uniqueid);
		std::string service_id = MAKE_SERVICE_ID(manager_zoneid);
		NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, service_id, &ipt_request);
	}
	LOG_TRACE("DS::RoamCommunityManager::GMSetPointBelong index=%d, uniqueid=%ld", index, uniqueid);
}

void RoamCommunityManager::GMResetSeason(int stage, bool transfer)
{
	int manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	manager_zoneid = battle_ptr->GetCenterZoneID();
	GET_CENTER_BATTLE_END
	if (GNET::g_zoneid == manager_zoneid)
	{
		switch (stage)
		{
		case 0:
		{
			_debug_model = true;
			std::shared_ptr<RCInitBattleDataProcess> process1 = make_shared<RCInitBattleDataProcess>(true);
			process1->Begin();
		}
		break;
		case 1:
		{
			_debug_model = true;
			OnChange2Vote();
		}
		break;
		case 2:
		{
			_debug_model = true;
			OnChange2Wait();
		}
		break;
		case 3:
		{
			_debug_model = true;
			OnChange2Battle();
		}
		break;
		case 4:
		{
			_debug_model = true;
			OnChange2End();
		}
		break;
		case 5:
		{
			_debug_model = true;
			OnSeasonEnd();
		}
		break;
		case 6:	// 临时加一个模拟初始化失败的情况
		{
			RCMANAGER._battle_data.set_season_state(0);
		}
		break;
		default:
		{
			_debug_model = false;
		}
		break;
		}
	}
	else if (transfer)
	{
		PB::ipt_roam_community_op ipt_request;
		ipt_request.set_param1((int64_t)stage);
		ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_GM_RESET_SEASON);
		std::string service_id = MAKE_SERVICE_ID(manager_zoneid);
		NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, service_id, &ipt_request);
	}
	LOG_TRACE("DS::RoamCommunityManager::GMResetSeason stage=%d, debug_model=%d", stage, _debug_model);
}

void RoamCommunityManager::GMResetSetJoinTimeLimit(int64_t roleid, int join_time_limit)
{
	PB::ipt_roam_community_op ipt_request;
	ipt_request.set_param1((int64_t)join_time_limit);
	ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_GM_SET_JOIN_LIMIT_TIME);
	NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, &ipt_request);
	LOG_TRACE("DS::RoamCommunityManager::GMResetSetJoinTimeLimit roleid=%ld, join_time_limit=%d", roleid, join_time_limit);
}

int RoamCommunityManager::GetJoinTimeLimit()
{
	if (_debug_join_time_limit > 0)
	{
		return _debug_join_time_limit;
	}
	return ROAM_COMMUNITY_CONFIG.join_time_limit;
}

void RoamCommunityManager::SetJoinTimeLimit(int join_time_limit)
{
	_debug_join_time_limit = join_time_limit;
	LOG_TRACE("DS::RoamCommunityManager::SetJoinTimeLimit join_time_limit=%d", join_time_limit);
}

void RoamCommunityManager::GMSetManagerZoneid(int64_t roleid, int manager_zoneid)
{
	PB::ipt_roam_community_op ipt_request;
	ipt_request.set_param1((int64_t)manager_zoneid);
	ipt_request.set_op(PB::ROAM_COMMUNITY_OP::RCO_GM_SET_MANAGER_ZONEID);
	NatsInterface::GetInstance().SendMessage(ROAM_COMMUNITY_SERVICE_GROUP, &ipt_request);
	LOG_TRACE("DS::RoamCommunityManager::GMSetManagerZoneid roleid=%ld, GMGMSetManagerZoneid=%d", roleid, manager_zoneid);
}

void RoamCommunityManager::SetManagerZoneid(int manager_zoneid)
{
	int old_manager_zoneid = 0;
	GET_CENTER_BATTLE(battle_ptr, CBT_ROAM_COMMUNITY)
	old_manager_zoneid = battle_ptr->GetCenterZoneID();
	((CenterRoamCommunityBattleServer *)battle_ptr)->SetCenterZoneID(manager_zoneid);
	GET_CENTER_BATTLE_END


	if (GNET::g_zoneid == manager_zoneid || GNET::g_zoneid == old_manager_zoneid)
	{
		std::shared_ptr<RCInitBattleDataProcess> process1 = make_shared<RCInitBattleDataProcess>(false);
		process1->Begin();
	}
	LOG_TRACE("DS::RoamCommunityManager::SetManagerZoneid manager_zoneid=%d", manager_zoneid);
}

void RoamCommunityManager::GMResetGetPointRewards(int64_t roleid, int type)
{
	struct RCResetGetPointRewardProcess: public std::enable_shared_from_this<RCResetGetPointRewardProcess>
	{
		DECLARE_RET_VAR

		ruid_t _roleid;
		int _type;

		std::string _ruid_key;
		std::string _data_key;
		int64_t _uniqueid;

		PB::roam_community_data::member_info _self;
		RCResetGetPointRewardProcess(ruid_t roleid, int type)
			: INIT_RET_VAR, _roleid(roleid), _type(type), _data_key(""), _uniqueid(0)
		{
			_ruid_key = GetOssKeyWord(OSS_RC_RUID, 0, "TEST");
		}

		void Begin()
		{
			OSSInterface::GetInstance().HGet(_ruid_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_SUCCESS == retcode)
				{
					foo->_uniqueid = atoll(data.c_str());
					foo->Check1();
				}
				else if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID != retcode)
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}
				else
				{
					FOO_ERR_FINISH_RETURN("Begin failed: redis error", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
			});
		}

		void Check1()
		{
			_data_key = GetOssKeyWord(OSS_RC_DATA, _uniqueid, "TEST");
			OSSInterface::GetInstance().HGet(_data_key, std::to_string(_roleid), [foo = shared_from_this()](int retcode, const string & data)
			{
				foo->_redis_ret = retcode;
				if ((int)oss::OSSCode::OSSCODE_DATA_FIELD_INVALID == retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: not in roam community1", GNET::ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN);
				}
				else if ((int)oss::OSSCode::OSSCODE_SUCCESS != retcode)
				{
					FOO_ERR_FINISH_RETURN("Check1 failed: redis error", GNET::ERROR_ROAM_COMMUNITY_NOT_SERVICE);
				}

				STR_2_PB(data, foo->_self);

				if (foo->_type == RC_GRT_BATTLE)
				{
					foo->_self.mutable_battle_pick()->clear_indexes();
				}
				else if (foo->_type == RC_GRT_SEASON)
				{
					foo->_self.mutable_season_pick()->clear_indexes();
				}

				std::string str_member;
				PB_2_STR(foo->_self, str_member);
				OSSInterface::GetInstance().HSet(foo->_data_key, std::to_string(foo->_roleid), str_member, [](int retcode)
				{
				}, 0, 0, 1, false);

				foo->Finish();
			});
		}

		void Finish()
		{
			LOG_TRACE("DS::RoamCommunityManager::RCResetGetPointRewardProcess roleid=%ld, id=%ld, type=%d, errmsg=%s, redis_ret=%d, retcode=%d", _roleid, _uniqueid, _type, _err_msg.c_str(), _redis_ret, _retcode);
		}
	};

	std::shared_ptr<RCResetGetPointRewardProcess> process = make_shared<RCResetGetPointRewardProcess>(roleid, type);
	process->Begin();
}

}
