#ifndef __GNET_COMMONMACRO_H
#define __GNET_COMMONMACRO_H
// Define macros shared by SwordNet and SwordGame
#include "octets.h"
#include "thread.h"
#include "marshal.h"
#include <sys/types.h>
#include <sys/socket.h>
#include <netdb.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <stdint.h>
#include <stdint.h>
#include <string>
#include <map>
#include <unordered_map>
#include <set>
#include <unordered_set>
#include <time.h>
#include <functional>
#include <atomic>
#include <sstream>
#include "conv_charset.h"

#ifndef RUID_TYPE
#define RUID_TYPE
typedef int64_t ruid_t;
#endif

const int SECOND_PER_DAY_IN_GAME	= 3600 * 4;
const int DAY_PER_WEEK				= 7;
const int HOUR_PER_DAY				= 24;
const int SECOND_PER_WEEK			= 86400 * 7;
const int SECOND_PER_DAY			= 86400;
const int SECOND_PER_HOUR			= 3600;
const int SECOND_PER_MIN			= 60;
const int TOTAL_HOURS_OF_WEEK		= 24 * 7;

namespace GNET
{
#define MAILBOX_LEAVE_MSG_MAX	(100)		//最大离线消息数量
#define MAX_MAIL_COUNT		99
#define MAX_UN_RECYCLE_MAIL_COUNT		999
#define MAILBOX_AUCTION_MAIL_MAX (100)
#define MAILBOX_CORPS_AUCTION_MAIL_MAX (300)
#define MAX_SYSTEM_MAIL_COUNT	99
#define MAX_PLAYER_MAIL_COUNT	99
#define MAX_MAIL_ATTACHED_ITEM_COUNT (6)
#define DEFAULT_SYSTEM_MAIL_LIFETIME (7*24*60*60)
#define DEFAULT_PLAYER_MAIL_LIFETIME (7*24*60*60)
#define DEFAULT_LEAVE_MSG_LIFETIME  (14*24*60*60)
#define WEB_SYSTEM_MAIL_LIFETIME    1209600//14*24*60*60
#define ERROR_SUCCESS           0
#define AC_STATUS_ONGAME        5
#define AC_DELIVERY_CLIENT      1
#define INVALID_MIRROR_ID	0xFF
#define YJ_GAME_ID				208
#define MAX_INTIMATE_COUNT 		10    //玩家最多拥有羁绊数量
#define MAX_SOUL_INTIMATE_COUNT         3    //玩家最多拥有灵魂伴侣数量
#define NEW_PLAYER_INST_ID		321	 //创建选人副本ID
#define MAX_MAIL_SUBJECT_SIZE		40
#define MAX_MAIL_CONTEXT_SIZE		400
#define INGAME_HOURS_TO_REAL(gameHours) (gameHours * 3600 / 6) //游戏里显示的时间对应现实时间(秒)
#define SIMPLE_STOCK_PRICE_DECIMAL	100
#define SIMPLE_STOCK_PRICE_INTERVAL	300
#define DEFAULT_CREDIT_VALUE 400
#define ROLE_OLD_NAME_COUNT 5 // 曾用名保有数量
#define FAIR_BLANCE_VALUE 10000 // 公平战平衡值

#define SAFE_DELETE(p)              { if(p) { delete (p); (p) = nullptr;}  }

#define BOOLEN(b) ((b) ? "true" : "false")

#define PB_2_STR(PB, STR) \
do{\
		(STR).resize((PB).ByteSize());\
		(PB).SerializeWithCachedSizesToArray((uint8_t *)(STR).data());\
}while(false);

#define STR_2_PB(STR, PB) \
do{\
	try{\
		if(!(PB).ParseFromArray((STR).data(), (STR).size())) throw "error";\
	}																\
	catch(...){\
		SLOG(DEBUG,"STR_2_PB_ERROR") \
		.P("param1",#STR)\
		.P("param2",#PB);\
		return;\
	}\
}while(false);


/*
 *其中日常文字答题与每日图片答题，没有限制，周末图片答题需要周末文字答题达到一定正确率后，方可以进行答题
 *使用答题声望对是否答过对应的答题进行区分：
 */
enum CONTEST_SUBTYPE
{
	CONTEST_SUBTYPE_INVALID		= 0,
	CONTEST_SUBTYPE_TEXT_EVERYDAY	= 1,	//每日文字答题
	CONTEST_SUBTYPE_TEXT_SATURDAY	= 2,	//周六文字答题
	CONTEST_SUBTYPE_TEXT_SUNDAY	= 3,	//周日文字答题
	CONTEST_SUBTYPE_GRAPH_EVERYDAY	= 4,	//每日图片答题，实际上周一到周四进行
	CONTEST_SUBTYPE_GRAPH_WEEKEND	= 5,	//周末答题，实际上是周五，周六，周日进行
	CONTEST_SUBTYPE_MAX,
};

enum
{
	MAX_DISCIPLE_COUNT	= 5,
	DISCIPLE_REQUIRE_LEVEL	= 30,
	SECT_REQUIRE_LEVEL_DIFF	= 0,
};

const int CRID_CORPS_BOSS_SCORE_ACTIVITY = 2787;

const int BLESS_WALL_RECENTLY_NUM_LIMIT = 40;
const int BLESS_WALL_SEND_NUM_LIMIT = 40;
const int BLESS_WALL_RECEIVE_NUM_LIMIT = 40;
const int BLESS_WALL_OP_NUM_LIMIT = 256;
// 祝福墙操作
enum BLESS_WALL_OP_TYPE
{
	BWOT_NONE				= 0,
	BWOT_SAVE_DATAS			= 1,
	BWOT_LOAD_IDS			= 2,
	BWOT_LOAD_DATAS			= 3,
	BWOT_DELETE_DATAS		= 4,
};

const int DIAMOND_BAG_ITEM_ID = 4463;
const int THANKS_GIVING_RECORD_MAX_SIZE = 99;
const int THANKS_GIVING_MSG_MIN_SIZE = 10;
const int THANKS_GIVING_MSG_MAX_SIZE = 30;

// 白帝神宫
const int BDSG_MATCH_TEAM_MAX_SIZE = 16;
const int BDSG_MATCH_TEAM_MAX_THRESHOLD = 32;
const int BDSG_BATTLE_CREATE_PER_TICK = 5;
const int BDSG_JOIN_LEVEL_LIMIT = 40;

enum SPECIAL_TITLE
{
	TITLE_SECT_MENTOR 	= 2920,
	TITLE_SECT_DISCIPLE	= 2925,
	TITLE_INTIMATE		= 625,
	TITLE_INLAWS		= 29054,
	TITLE_CORPS_SERVER_BATTLE_MEMBER = 7686,
	TITLE_NEW_SECT_MASTER	= 5996,		//新师徒师父称号
	TTILE_NEW_SECT_DISCIPLE	= 6111,		//新师徒徒弟称号
	TITLE_NEW_SECT_PARTNER	= 6068,		//新师徒助教称号
	TITLE_SOUL_INTIMATE     = 17678,	//灵魂伴侣称号
	TITLE_MARRIAGE			= 38770,       //(永恒契约)结婚良缘称号
	TITLE_PVEINTIMATE       =  54677, //跨服羁绊
	TITLE_PVEHARMONIOUS       =  55426, //跨服羁绊
};

enum DATA_BETWEEN_AUANY_DIRECTION
{
	AUANY_TO_GAMESERVER = 1,            // Auany到游戏服务器
	AUANY_TO_GAMECLIENT = 2,            // Auany到游戏客户端
	GAMESERVER_TO_AUANY = 3,            // 游戏服务器到Auany
	GAMECLIENT_TO_AUANY = 4,            // 游戏客户端到Auany
};

enum DATA_BETWEEN_AUANY_REQTYPE
{
	REQ_UPDAE_LOGIN_STATE = 2,          // 更新登陆状态(client -> au)
	REQ_NOIFY_RECHARGE = 50,            // 通知充值成功(client -> au)
	REQ_GET_BALANCE_CONFIRM = 90,       // 获取余额确认(au -> server)
	REQ_COST = 100,                     // 花费点券(server -> au)
	REQ_COST_CONFIRM = 102,             // 花费点券确认(au -> server)
	REQ_PRESENT = 105,                  // 赠送点券(server -> au)
	REQ_PRESENT_CONFIRM = 106,          // 赠送点券确认(au -> server)
	REQ_GO_ON = 110,                    // 通知游戏服继续下一单(au -> server)
	REQ_TRY_CHECK_NEXT_ORDER = 112,     // 尝试下一单(server -> au)
	REQ_RECHARGE_SIMULATOR = 115,       // 模拟充值(server -> au)
	REQ_RESET_AUANY_CHECK_STATUS = 116, // 重置auany的check_status，避免玩家一直陷入状态4
	REQ_DELETE_AUANY_CHECKING_ORDER = 117, // 删除正在对账的auany账单
	REQ_TYPE_ROLE_LOGIN = 512,          // 角色登陆(server -> au) 2 << 8
	REQ_TYPE_ROLE_LOGOFF = 768,         // 角色登出(server -> au) 3 << 8

	// 只是利用DBDataBetweenAu的rpc同db交互，并未向auany进行交互
	REQ_INC_SAVE_AMT_FIX    = 1024,     // 增加玩家的累计充值修正值(ds -> db) 4 << 8
	REQ_DEC_SAVE_AMT_FIX    = 1025,     // 减少玩家的累计充值修正值(ds -> db)
	REQ_SET_SAVE_AMT_FIX    = 1026,     // 设置玩家的累计充值修正值(ds -> db)
	REQ_BACKUP_PLAERY_RECHARGE_DATA = 1027,     // 请求备份玩家充值数据(ds->db)
	REQ_RESTORE_PLAYER_RECHARGE_DATA = 1028,    // 请求恢复玩家充值数据(ds->db)
	REQ_FIX_PLAYER_RECHARGE_DATA    = 1029,     // 请求修正玩家充值数据(ds->db)
	REQ_DEL_CHECKING_ORDER  = 1030,     // 删除正在对账的订单(ds->db)
	REQ_FIX_LOST_COST_ORDERS = 1031,	// 尝试修复丢失的消费订单
	REQ_FIX_LOST_PRESENT_ORDERS = 1032,	// 尝试修复丢失的赠送订单
};

enum MIDAS_AUANY_ERR_CODE
{
	MIDAS_AUERR_SUCCEED = 0, // 成功
	MIDAS_AUERR_INVALID = 1, // 失败，token/sessionid无效
	MIDAS_AUERR_PLAT_ARGS = 2, // 与平台的访问的参数异常，服务器请求的参数不标准
	MIDAS_AUERR_PLAT_TRANS = 3, // 与平台的访问异常
	MIDAS_AUERR_DB_STORE = 4, // db存储异常
	MIDAS_AUERR_INVALID_ACCOUNT = 5, // 账号错误，账号不存在
	MIDAS_AUERR_INVALID_PASSWORD = 6, // 密码错误
	MIDAS_AUERR_SDK_BUSY = 7, // sdk 平台网络繁忙
	MIDAS_AUERR_NETWORK_TIMEOUT = 8, // 网络超时
	MIDAS_AUERR_OTHERS = 9, // 其他错误
	MIDAS_AUERR_APPID_INVALID = 10, // APPID无效
	MIDAS_AUERR_ORDER_INVALID = 11, // 订单无效
	MIDAS_AUERR_ORDER_STATUS_INVALID = 12, // 订单状态错误
	MIDAS_AUERR_ORDER_INFO_INVALID = 13, // 订单信息不一致
	MIDAS_AUERR_TENCENT_TOKEN_INVALID = 14, // 应用宝——token失效需要更新登陆状态;
	MIDAS_AUERR_TENCENT_PAY_FAILED_AND_DELTE_UNISDK_ORDER_INFO = 15, // 应用宝——失败并删除unisdk记录的订单相关信息;
	MIDAS_AUERR_TENCENT_GET_BALANCE_FAILED = 16, // 应用宝——获取余额失败;
	MIDAS_AUERR_TENCENT_PAY_FAILED = 17, // 应用宝——支付失败
	MIDAS_AUERR_TENCENT_ORDER_PENDING = 18, // 应用宝——订单处于PENDING状态;
	MIDAS_AUERR_ZONE_ID_NOT_MATCH = 19, // ZONEID不匹配;
	MIDAS_AUERR_SN_NOT_MATCH = 20, // SN不匹配;
	MIDAS_AUERR_USER_NOT_FOUND = 1000, // 用户不存在;
	MIDAS_AUERR_CHECK_STATUS_INVALID = 1001, // 订单状态非法;
	MIDAS_AUERR_BILLNO_DUPLICATE = 1002, // 订单号重复;
	MIDAS_AUERR_BALANCE_NOT_ENOUGH = 1003, // 余额不足;
	MIDAS_AUERR_BAN_LOGIN = 1004, // 禁止登陆;
	MIDAS_AUERR_SKIP_SANDBOX_ORDER = 65536, // 跳过沙箱订单;
};

// grc礼物类型
enum GrcGiftType
{
	GRC_GT_VIGOUR           =       1,      // 体力
	GRC_GT_BLESSING         =       2,      // 祝福值
	GRC_GT_GOLD				= 		3,		// 金币

	GRC_GT_COUNT,           // grc 礼物类型数量限制
};

enum INSTANCE_TYPE
{
	INSTANCE_SOLO       = 0,	// 个人副本
	INSTANCE_ROUTINE    = 1,	// 活动副本
	INSTANCE_TEAM       = 2,	// 常规副本
	INSTANCE_GM         = 3,	// GM尝试跟踪进入
	INSTANCE_BATTLE     = 4,	// 战场副本
	INSTANCE_BASE       = 5,	// 帮派基地
	INSTANCE_FACTION    = 6,	// 帮派副本
	INSTANCE_FACTION_TEAM = 7,	// 帮派组队副本
	INSTANCE_TOURNAMENT = 8,	// 团体竞赛副本
	INSTANCE_DUEL       = 9,	// 切磋副本
	INSTANCE_MELEE	    = 10,	// 混战副本
	INSTANCE_ARENA		= 11,	// 竞技场
	INSTANCE_CORPS_BATTLE = 12,	// 帮派竞赛
	INSTANCE_HOMETOWN	= 13,	// 家园
	INSTANCE_REVENGE_BATTLE	= 14,	// 仇杀约战
	INSTANCE_CENTER_BATTLE = 15,	// 跨服战场
	INSTANCE_CENTER_SINGLE_ARENA_BATTLE = 16,	// 跨服单人竞技场
	INSTANCE_CENTER_TEAM_ARENA_BATTLE = 17,	// 跨服组队竞技场
	INSTANCE_CENTER_FACTION_BATTLE = 18,	// 跨服帮派竞赛
	INSTANCE_SECOND_HOMETOWN = 19,	//第二家园
	INSTANCE_CENTER_SERVER_BATTLE = 20,	//跨服服务器对战
	INSTANCE_CARRACE = 21,	//赛车副本
	INSTANCE_CAREER_SHOP = 22,	// 商店副本
	INSTANCE_CENTER_SINGLE_PUBG_BATTLE = 23,	// 跨服单人吃鸡战场
	INSTANCE_CENTER_TEAM_PUBG_BATTLE = 24,	// 跨服组队吃鸡战场
	INSTANCE_HOMETOWN_DESIGN = 25, // 家园设计模式
	INSTANCE_CENTER_ELIMINATE_SCORE_BATTLE = 26,
	INSTANCE_CENTER_ELIMINATE_KNOCKOUT_BATTLE = 27,
	INSTANCE_CENTER_FAIR = 28, // 公平1V1
	INSTANCE_CENTER_CHESS = 29, // 自走棋
	INSTANCE_OVERCOOK = 30, //胡闹厨房
	INSTANCE_CENTER_CORPS_DUEL = 31, //社团约战
	INSTANCE_HOMETOWN_CONTRACT	= 32,	// 契约家园
	INSTANCE_HOMETOWN_CONTRACT_DESIGN	= 33,	// 契约家园设计模式
	INSTANCE_CENTER_WOLF = 34, //狼人杀
	INSTANCE_BDSG = 35, //白帝神宫
	INSTANCE_HONEYGARDEN = 36, //甜蜜花园
	INSTANCE_SKATEBOARD_RACE = 37, //彩虹航线(滑板)
	INSTANCE_ZSPACE   = 38,
	INSTANCE_HUNDRED_CORPS_BATTLE = 39,
	INSTANCE_ROAM_COMMUNITY_BATTLE = 40,
	INSTANCE_MOUNT_SPACE   = 41,
	INSTANCE_CROSS_MULTI_PVP = 42,
	INSTANCE_CENTER_TEAM_ARENA_BATTLE_NEW = 43,	// 新跨服组队竞技场
	INSTANCE_TOWNLET		= 44,	//小镇
	INSTANCE_ROUGE	= 45,	//rouge本
	INSTANCE_RAID_TEAM = 46,	//10人团队本
	INSTANCE_TYPE_COUNT,
};

enum CUSTOME_SCENE_TYPE
{
	CUSTOME_SCENE_CAREER_SHOP	= 1,
};

enum SPEC_TYPE			//副本分类
{
	SPEC_CELEB		= 1,		//名人挑战
	SPEC_BATTLE		= 2,		//战场
	SPEC_TYPE_COUNT,
};
enum CORPS_SUPPORT_SIDE		//社团拥护组织
{
	CSS_INVALID		= 0,	//错误的拥护组织
	CSS_STUDENT_UNION	= 1,	//学生会
	CSS_LION_HEART		= 2,	//狮心会
};
enum CORPS_MOMENT_ACHIEVE_TYPE
{
	CMAT_TOTAL_COUNT,
	CMAT_TOTAL_VOTE,
	CMAT_SINGLE_VOTE,
	CMAT_COUNT,
};
enum CHANGEWORD_MODE
{
	MODE_CHANGELINE		= 0,
	MODE_ENTERINSTANCE	= 1,
	MODE_LEAVEINSTANCE	= 2,
	MODE_INSTANCE2INSTANCE	= 3,
	MODE_CHANGE_ZONE	= 4,
	MODE_TEAM_FOLLOW_JUMP = 5,
};

enum JOIN_INSTANCE_TYPE
{
	JIT_PATICIPATE	= 0,	//参与副本
	JIT_GM		= 1,	//GM跟随进入副本
	JIT_LOGIN	= 2,	//直接登录参与副本
};

enum PLAYER_LOST_CONNECTION_MODE
{
	PLCM_RECONNECT		= 0,	//普通逻辑,副本中使用断线重连
	PLCM_NO_RECONEC		= 1,	//副本中也不再使用断线重连
	PLCM_KEEP_CONNECT	= 2,	//断线后保持一直连接，直到模式改变

	PLCM_COUNT,
};

enum TITLE_MASK
{
	TITLE_FACTIONMASTER    = 0x0004,
	TITLE_TEAMLEADER       = 0x0040,
	TITLE_SECTMENTOR       = 0x0080,
};
enum MAIL_STATUS
{
	MAIL_STATUS_NEW        = 0,      // 新邮件(其他状态未设置时，默认为未读邮件)
	MAIL_STATUS_ATTACHED   = 0x01,   // 有附件
	MAIL_STATUS_READ       = 0x02,   // 已读
	MAIL_STATUS_RESERVED   = 0x04,   // 保留
	MAIL_STATUS_PROCESSED  = 0x08,   // 已处理的邮件，比如加好友请求，通知等
	MAIL_STATUS_DELETING   = 0x10,   // 待删除
};

enum MAIL_CATEGORY
{
	MAIL_CATEGORY_PLAYER		= 0,    // 普通玩家邮件
	MAIL_CATEGORY_TASK		= 1,    // 任务发送邮件
	MAIL_CATEGORY_MESSAGE		= 2,    // 玩家留言
	MAIL_CATEGORY_FRIEND_ADD	= 3,    // 加好友相关邮件(有一方不在线 或 点击忽略)
	MAIL_CATEGORY_INFORM		= 4,    // 格式邮件－通知
	MAIL_CATEGORY_GIFT		= 5,    // 礼物邮件
	MAIL_CATEGORY_STOCK		= 6,    // 股票结算邮件
	MAIL_CATEGORY_HOME		= 7,    // 家园系统取物品邮件
	MAIL_CATEGORY_LOSTFOUND		= 8,    // 副本中没来得及发的东西
	MAIL_CATEGORY_MALL_BUY		= 9,    // 商城购物邮件
	MAIL_CATEGORY_MALL_PRESENT	= 10,	// 商城赠送邮件
	MAIL_CATEGORY_AUCTION		= 11,	// 拍卖系统邮件
	MAIL_CATEGORY_WEBMAIL		= 12,   // 页面发奖工具
	MAIL_CATEGORY_AUCTION_SELL	= 13,	// 新拍卖系统拍卖获得钱
	MAIL_CATEGORY_AUCTION_CANCEL	= 14,	// 新拍卖系统取消挂单
	MAIL_CATEGORY_AUCTION_SUCCESS	= 15,	// 新拍卖系统买到物品
	MAIL_CATEGORY_AUCTION_TIMEOUT	= 16,	// 新拍卖系统流拍
	MAIL_CATEGORY_IDIPGIFT		= 17,   // 腾讯IDIP账号赠送物品和元宝
	MAIL_CATEGORY_DIVORCE		= 18,   // 离婚通知邮件
	MAIL_CATEGORY_GIFT_CARD		= 19,	// 礼包卡邮件
	MAIL_CATEGORY_ACTIVITY_AWARD	= 20,	// 活动奖励
	MAIL_CATEGORY_FIRST_RECHARGE	= 21,	// 首冲奖励
	MAIL_CATEGORY_TIME_LIMIT_AWARD	= 22,	// 限时奖励
	MAIL_CATEGORY_MERGE_AWARD	= 23,	// 合服奖励
	MAIL_CATEGORY_MERGE_KICK_AWARD	= 24,	// 合服退帮奖励
	MAIL_CATEGORY_REVENGE		= 25,	// 约战玩家下线时的结果通知
	MAIL_CATEGORY_TENCENT_GIFT	= 26,	// 腾讯应用宝礼包
	MAIL_CATEGORY_ARENA_TOPLIST	= 27,	// 单人竞技场排行榜发奖
	MAIL_CATEGORY_ARENATEAM_TOPLIST	= 28,	// 多人竞技场排行榜发奖
	MAIL_CATEGORY_EVATAR_GIFT	= 29,	// evatar直冲赠送
	MAIL_CATEGORY_TPAWARD		= 30,	// 排行榜发奖
	MAIL_CATEGORY_LEVELUP		= 31,	// 升级系统邮件
	MAIL_CATEGORY_CHILD_DIVORCE	= 32,	// 孩子离婚邮件
	MAIL_CATEGORY_CHILD_TOUR	= 33,	// 孩子出游邮件
	MAIL_CATEGORY_NEWBIE_GIFT	= 34,	// 新手礼包
	MAIL_CATEGORY_HIGH_RANK		= 35,	// 排行榜历史最高排名提示邮件
	MAIL_CATEGORY_CORPS_TEMP	= 36,	// 社团成员转成休假状态提示邮件
	MAIL_CATEGORY_SOCIALSPACE_TXN	= 37,	// 空间事务处理
	MAIL_CATEGORY_SECRET_LOVE_MSG	= 38,	// 暗恋消息
	MAIL_CATEGORY_IDIP_UNFREEZE	= 39,	// idip解冻(经验、周期性经验、金币、钻石)
	MAIL_CATEGORY_AUCTION_VERIFY_BEGIN_SELL	= 40,	// 新拍卖系统开始审核（卖方）
	MAIL_CATEGORY_AUCTION_VERIFY_BEGIN_BUY	= 41,	// 新拍卖系统开始审核（买方）
	MAIL_CATEGORY_AUCTION_VERIFY_FAIL_SELL	= 42,	// 新拍卖系统审核失败（卖方）
	MAIL_CATEGORY_AUCTION_VERIFY_FAIL_BUY	= 43,	// 新拍卖系统审核失败（买方）
	MAIL_CATEGORY_AUCTION_VERIFY_FAIL_BUY_LIMIT	= 44,	// 新拍卖系统审核失败（买方）,审核次数达上限
	MAIL_CATEGORY_IDIP_CHANGE_DATA = 45,// idip更改玩家数据(解冻[经验、周期性经验、金币、钻石]、声望、任务、物品)
	MAIL_CATEGORY_CORPS_SPECIAL_POS	= 46,	// 社团特殊身份
	//MAIL_CATEGORY_CORPS_MOMENT_AWARD = 47,	// 社团空间奖励邮件
	MAIL_CATEGORY_CORPS		= 48,	// 社团邮件
	MAIL_CATEGORY_TASK_SYS_MAIL	= 49,	// 任务系统邮件
	MAIL_CATEGORY_AUCTION_OPEN_VERIFY	= 50,	// 新拍卖系统上架审核
	MAIL_CATEGORY_AUCTION_VERIFY_APPROVE	= 51,	// 新拍卖系统上架审核批准
	MAIL_CATEGORY_AUCTION_IDIP_CLOSE	= 52,	// 新拍卖系统IDIP下架
	MAIL_CATEGORY_PURSUE_CLEAR		= 53,	// 追缴状态清除
	MAIL_CATEGORY_CORPS_ORGANIZE		= 54,	// 社团活动组织奖励
	MAIL_CATEGORY_CORPS_AUCTION_BUY = 55,	// 社团拍卖购买成功(物品邮件)
	MAIL_CATEGORY_CORPS_AUCTION_BUY_ORDER = 56,	// 社团拍卖购买成功(生成订单)
	MAIL_CATEGORY_CORPS_AUCTION_WITHDRAW = 57,	// 社团拍卖返还
	MAIL_CATEGORY_CORPS_AUCTION_BONUS = 58,	// 社团拍卖分红
	MAIL_CATEGORY_CORPS_IDIP_OP		= 59,	// 社团IDIP操作
	MAIL_CATEGORY_TOPSTAR_REPORT	= 60,	// 人气巨星战报
	MAIL_CATEGORY_IDIP_USE_CASH_AND_SEND_ITEM = 61,	// idip扣点券并发物品
	MAIL_CATEGORY_CORPS_CENTER_BATTLE_WIN	= 62,	//跨服帮派战胜利
	MAIL_CATEGORY_SS_RECOMMAND_REWARD = 63, // 朋友圈普通状态热点奖励
	MAIL_CATEGORY_IDIP_USE_DIAMOND_AND_SEND_ITEM = 64, // idip扣钻石并发物品
	MAIL_CATEGORY_CORPS_BATTLE2_WIN_AWARD	= 65,	// 社团竞赛2胜利奖励
	MAIL_CATEGORY_CORPS_BATTLE2_LOSE_AWARD	= 66,	// 社团竞赛2失败/平局奖励
	MAIL_CATEGORY_SELL_HOMETOWN           = 67, // 卖家园
	MAIL_CATEGORY_MOVE_HOMETOWN	          = 68, // 搬家园
	MAIL_CATEGORY_ARENA_MINIGAME_TOPLIST	= 69,	// 多人竞技场排行榜发奖
	MAIL_CATEGORY_GUARD_BREED				= 70,	// 守护灵培育
	MAIL_CATEGORY_CORPS_AUTO_MERGE          = 71,   //系统自动合并社团
	MAIL_CATEGORY_HOMETOWN_PARTY_AWARD      = 72,   //家园派对奖励
	MAIL_CATEGORY_SOUL_CHILD_DELETE         = 73,   //伴侣解除 非携带方删除孩子
	MAIL_CATEGORY_HOMETOWN_DESIGN_RECOMMEND_CHANCE_NOTIFY = 74, // 通知领取家园设计推荐券
	MAIL_CATEGORY_HOMETOWN_DESIGN_RECOMMEND_REWARD_PLAYER = 75, // 家园设计推荐奖度奖励-个人
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_DAILY_SALES		= 76,	// 排行榜奖励邮件，莱茵拍卖
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_DRAGON_HOUSE		= 77,	// 排行榜奖励邮件，巨龙宝库
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_VIDEO_GAME		= 78,	// 排行榜奖励邮件，绘梨衣的游戏机
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_TOP_STAR			= 79,	// 排行榜奖励邮件，超级明星
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_HEAVEN_DICE		= 80,	// 排行榜奖励邮件，夏妮尔的游乐场
	MAIL_CATEGORY_TOP_LIST_MAIL_SSP          = 81,   // 排行榜奖励邮件，朋友圈比赛
	//MAIL_CATEGORY_SSP_CORPS_MAIL		= 82,	//朋友圈比赛排行榜奖励社团邮件
	MAIL_CATEGORY_CORPS_BOSS_MAIL		= 83,	//社团BOSS
	MAIL_CATEGORY_BREED_CUTE_PET_PRESENT				= 84,	// 育宠达人萌宠赠送
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_PLEASURE_DICE    = 85,   // 排行榜奖励邮件，趣味夺宝
	MAIL_CATEGORY_HOMETOWN_PLEASURE_AWARD               = 86,   // 趣味夺宝
	MAIL_CATEGORY_PDD_MAIL                           = 87,   // 拼团邮件
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_SPRING_DICE    = 88,   // 春节彩票
	MAIL_CATEGORY_HAR_TASK_FINSH   = 89, //永恒契约（良缘）任务完成邮件
	MAIL_CATEGORY_SELL_CONTRACT_HOMETOWN    = 90,   //出售契约家园
	MAIL_CATEGORY_HAR_IDIP_DIVORCE         = 91, //	idip离婚邮件
	MAIL_CATEGORY_COMMON_PB_MAIL	= 92,	//公共的PB邮件 对应 -> message common_pb_mail_info
	MAIL_CATEGORY_SELL_CONTRACT_HOMETOWN_OBJECT = 93,   //出售契约家园家具
	MAIL_CATEGORY_CONTRACT_HOMETOWN_DEMOLISH = 94,      //契约家园全部拆除
	MAIL_CATEGORY_CONTRACT_TARGET_SELL_OBJECT   = 95,
	MAIL_CATEGORY_CONTRACT_MOVE_HOMETOWN	    = 96,   //契约家园搬家园
	MAIL_CATEGORY_CONTRACT_GRID_DEMOLISH        = 97,      //契约家园全部拆除
	MAIL_CATEGORY_CONTRACT_SELL_HOMETOWN        = 98,   //出售家园通知
	MAIL_CATEGORY_TOP_LIST_MAIL_CORPS_BOSS_SCORE	= 99,	// 排行榜奖励邮件，社团boss积分
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_GOD_EXPLORE = 100, // 神迹探索排行榜奖励
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_TEAM_RECHARGE = 101, // 组队充值队伍排行榜奖励
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_TEAM_RECHARGE_ROLE = 102, // 组队充值个人排行榜奖励
	MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_PERSONAL_TARGET = 103, // 个人目标排行榜奖励
	MAIL_CATEGORY_FRIEND_ROAM2LOCAL	= 104, // 跨服好友转本服
	MAIL_CATEGORY_TOP_LIST_MAIL_RECHARGE_CONSUME = 107,	// 充值消费排行榜奖励
	MAIL_CATEGORY_PVE_REWARD = 108,	// PVE服务器活动奖励
	MAIL_CATEGORY_LOTTERY_SEA = 109,	// 星海密藏
	MAIL_CATEGORY_HUNDRED_CORPS_BATTLE = 110, //百团大战社团排行榜邮件
	MAIL_CATEGORY_HUNDRED_CORPS_BATTLE_PERSONA_SCORE = 111, //百团大战社团排行榜邮件个人积分
	MAIL_CATEGORY_LOTTERY_SHIP = 112,	// 冰海行动
	MAIL_CATEGORY_HUNDRED_CENTER_CORPS_BATTLE = 113, //百团大战社团跨服排行榜邮件
	MAIL_CATEGORY_HUNDRED_CENTER_CORPS_BATTLE_PERSONA_SCORE = 114, //百团大战社团跨服排行榜邮件个人积分
	MAIL_CATEGORY_COMMON_PB_MAIL_FOR_DICE_TOP_LIST	= 115,	//彩票排行榜——公共的PB邮件


	CATEGORY_MAIL_SYSTEM   = 0x80,  // 来自系统的邮件最高位置为1
};

// 邮件ID高4位用于区分邮件产生的模块位置
enum MAIL_IDSCOPE
{
	MAIL_IDSCOPE_DBUSER   = 0x1,    // DB中发送给账号的邮件
	MAIL_IDSCOPE_DBPLAYER = 0x2,    // DB中发送给角色的邮件
	MAIL_IDSCOPE_GSPLAYER = 0x4,    // GS中发送给角色的邮件
	MAIL_IDSCOPE_ROAM_GSPLAYER = 0x8,	// 跨服GS中发给角色的邮件
};

enum MAILBOX_IDX
{
	MAILBOX_IDX_NORMAL  = 0,
	MAILBOX_IDX_MESSAGE = 1,
	MAILBOX_IDX_SERVER  = 2,
	MAILBOX_IDX_AUCTION = 3,
	MAILBOX_IDX_CORPS_AUCTION = 4,
	MAILBOX_IDX_UN_RECYCLE = 5,
	MAILBOX_IDX_BOUQUET = 6,
	MAILBOX_IDX_PRAY	= 7,
};

enum DELETE_ACCOUNT_STAGE
{
	DAS_CHECK = 1,
	DAS_PRE_VERIFY = 2,
	DAS_POST_VERIFY = 3,
	DAS_PS_SAVE = 4,
};

static bool IS_SYSTEM_MAIL(unsigned char category)
{
	return ((category & GNET::CATEGORY_MAIL_SYSTEM) != 0);
}
static bool IS_PLAYER_MAIL(unsigned char category)
{
	return ((category) == GNET::MAIL_CATEGORY_PLAYER || (category) == GNET::MAIL_CATEGORY_GIFT || (category) == GNET::MAIL_CATEGORY_FRIEND_ADD);
}
static bool IS_MSG_MAIL(unsigned char category)
{
	return (!IS_SYSTEM_MAIL(category) && !IS_PLAYER_MAIL(category));
}
static bool CMP_CATEGORY(unsigned char self, unsigned char other)
{
	return ((self & 0x7f) == (other & 0x7f));
}

enum DB_DATA_MASK
{
	DBDATA_BASIC           = 0x01,
	DBDATA_CASH            = 0x02,
	DBDATA_POCKET          = 0x04,
	DBDATA_STORE           = 0x08,
	DBDATA_TASK            = 0x10,
	DBDATA_ALL             = (DBDATA_BASIC | DBDATA_CASH | DBDATA_POCKET | DBDATA_STORE | DBDATA_TASK),

	DBDATA_ROAM            = 0x20,
};

enum
{
	CHAT_SYSTEM_TASK       = 0,  //任务系统
	CHAT_SYSTEM_MARRIAGE   = 1,  //婚姻系统
	CHAT_SYSTEM_DROP       = 2,  //掉落物品通知
	CHAT_SYSTEM_BLESSING   = 3,  //送花
};

enum
{
	CHAT_PUBLIC_WORLD_MSG_MAX_LEN	= 240,	//世界msg最大长度
};


enum SPEAK_ID_TYPE
{
	SIT_ROLEID	= 0,	//玩家角色id
	SIT_FACTIONID	= 1,	//帮派id
	SIT_NATION	= 2,	//国家id
};

enum PRIVATE_CHANNEL
{
	WHISPER_NORMAL	= 0,	//非好友
	WHISPER_NORMALRE,	//非好友自动回复
	WHISPER_FRIEND,		//好友
	WHISPER_FRIEND_RE,	//好友自动回复
	WHISPER_USERINFO,	//好友相关信息
	WHISPER_GM, 		//在线客服
	WHISPER_MAX
};

// FORMAT_MAIL用于MailHeader.msgid定义，因为会存到数据库中，因此不能随便修改，一般只能新增，不能修改或删除已有定义
enum FORMAT_MAIL
{
	FORMAT_MAIL_FRIENDINVITE  		= 1,	// 邀请成为好友
	FORMAT_MAIL_FRIENDAGREE   		= 2,	// 同意好友申请
	FORMAT_MAIL_RENEGE        		= 3,	// 被悔婚
	FORMAT_MAIL_DIVORCE       		= 4,	// 被离婚
	FORMAT_MAIL_FAMILY_EXPEL  		= 5, 	// 被开除出结义
	FORMAT_MAIL_FAMILY_DISMISS		= 6, 	// 所在结义解散
	FORMAT_MAIL_SECT_EXPEL    		= 7, 	// 被师门开除
	FORMAT_MAIL_SECT_QUIT     		= 8, 	// 叛离师门
	FORMAT_MAIL_FACTION_INVITE		= 9, 	// 邀请加入帮派
	FORMAT_MAIL_SECT_GRADUATE_AWARD		= 10, 	// 徒弟出师对师父的奖励
	FORMAT_MAIL_TIZI_ERASE			= 11,	// 题字被擦出通知
	FORMAT_MAIL_BLESSING_THANKS		= 12,	// 鲜花感谢消息
	FORMAT_MAIL_IFRIEND_REMOVE 		= 13,	// 情缘事件
	FORMAT_MAIL_SECT_GRADUATE 		= 14,	// 通知玩家可以出师
	FORMAT_MAIL_DISCIPLE_GRADUATE		= 15,	// 玩家出师通知师傅邮件
	FORMAT_MAIL_CHILD_DIVORCE		= 16,	// 孩子离婚
	FORMAT_MAIL_CHILD_TOUR			= 17,	// 孩子出游
	FORMAT_MAIL_CORPS_TEMP			= 18,	// 社团成员转为休假状态通知邮件
	FORMAT_MAIL_SECRET_LOVE_MSG		= 19,	// 暗恋消息
	FORMAT_MAIL_SECRET_LOVE_BOTH		= 20,	// 相互暗恋
	FORMAT_MAIL_SOCIALSPACE_TXN		= 21,	// 空间送礼返回结果不在线时处理
	FORMAT_MAIL_IDIP_CHANGE_DATA		= 22,	// idip更改玩家数据(解冻[经验、周期性经验、金币、钻石]、声望、任务、物品)
	FORMAT_MAIL_CORPS_SPECIAL_POS		= 23,	// 社团特殊身份
	FORMAT_MAIL_CORPS			= 30,	// 社团邮件
	FORMAT_MAIL_STOCK_RESULT		= 31,	// 股票结算
	FORMAT_MAIL_SECT_HIRE_PARTNER		= 32,	// 雇佣助教
	FORMAT_MAIL_SECT_FIRE_PARTNER		= 33,	// 雇佣助教
	FORMAT_MAIL_CORPS_IDIP_APPOINT		= 34,	// IDIP任命社长
	FORMAT_MAIL_CORPS_IDIP_DEPOSE		= 35,	// IDIP罢免社长
	FORMAT_MAIL_SECT_MASTER_RENAME		= 36,   // 师傅改名
	FORMAT_MAIL_SECT_DISCIPLE_RENAME	= 37,   // 徒弟改名
	FORMAT_MAIL_FRIEND_RENAME			= 38,   // 好友改名
	FORMAT_MAIL_INTIMATE_RENAME			= 39,   // 羁绊改名
	FORMAT_MAIL_GUARD_BREED				= 40,	// 守护灵培育
	FORMAT_MAIL_SOUL_DELETE                         = 41,   // 强制删除灵魂伴侣
	FORMAT_MAIL_CORPS_AUTO_MERGER                   = 42,   //
	FORMAT_MAIL_CORPS_AUTO_MERGED                   = 43,   //
	FORMAT_MAIL_CORPS_AUTO_REFUSE                   = 44,   //
	FORMAT_MAIL_SOUL_MISS			= 44,	// 伴侣miss离线消息
	FORMAT_MAIL_SOUL_TREE_STATUS	= 45,	// 誓约树特殊状态
	FORMAT_MAIL_SOUL_TREE_HARVEST	= 46,	// 誓约树可收获
	FORMAT_MAIL_SHOP_PARTNER_QUIT   = 47,   // 小店合伙人退出
	FORMAT_MAIL_SOUL_CHILD_DELETE	= 48,	//原型期继承者删除
	FORMAT_MAIL_HOMETOWN_ROOMMATE_EXPEL = 49, // 驱离舍友
	FORMAT_MAIL_HOMETOWN_ROOMMATE_ESCAPE = 50, // 主动离开
	FORMAT_MAIL_HOMETOWN_ROOMMATE_CANCEL_ROOM = 51, // 专属房间被被取消
	FORMAT_MAIL_SSP_VOTE_REFRESH		= 52, //获得朋友圈比赛票
	FORMAT_MAIL_SSP_RECOMMEND_MAIL	= 53, //朋友圈比赛推荐上榜邮件
	FORMAT_MAIL_PDD_SUCCESS				= 54,
	FORMAT_MAIL_PDD_FAIL				= 55,
	FORMAT_MAIL_LEAVE_MSG_NEW			= 56,	// 新的离线消息
	FORMAT_MAIL_FRIENDROAM2LOCAL		= 57,	// 好友由跨服转本服
	FORMAT_MAIL_CLOSE_DIARY				= 58,	// 亲密日记
};

enum FORMAT_MAIL_RESPONSE
{
	MAIL_REQUEST_ACCEPT = 0,
	MAIL_REQUEST_REFUSE = 1,
};

// 拍卖行的审核状态
enum AUCTION_VERIFY_STATE
{
	AUCTION_VERIFY_NONE				= 0,
	AUCTION_TRADE_VERIFY_BEGIN		= 1,
	AUCTION_VERIFY_SAME_IPMAC		= AUCTION_TRADE_VERIFY_BEGIN,
	AUCTION_TRADE_VERIFY_END		= AUCTION_VERIFY_SAME_IPMAC,

	AUCTION_OPEN_VERIFY_BEGIN		= 10,
	AUCTION_VERIFY_ITEM_LIMIT		= AUCTION_OPEN_VERIFY_BEGIN,	//特定ID物品在时间段内超过数量限制
	AUCTION_VERIFY_MONEY_LIMIT,		//总售价在时间段内超过限制
	AUCTION_OPEN_VERIFY_END			= AUCTION_VERIFY_MONEY_LIMIT,

	AUCTION_IDIP_CLOSE			= 50,	//审核下架
};

inline bool AUCTION_NOT_VERIFY(int verify_state)
{
	return verify_state == AUCTION_VERIFY_NONE;
}
inline bool AUCTION_TRADE_VERIFY(int verify_state)
{
	return verify_state >= AUCTION_TRADE_VERIFY_BEGIN && verify_state <= AUCTION_TRADE_VERIFY_END;
}
inline bool AUCTION_OPEN_VERIFY(int verify_state)
{
	return verify_state >= AUCTION_OPEN_VERIFY_BEGIN && verify_state <= AUCTION_OPEN_VERIFY_END;
}
inline bool AUCTION_IS_IDIP_CLOSE(int verify_state)
{
	return verify_state == AUCTION_IDIP_CLOSE;
}

enum AUCTION_CLOSE_TYPE
{
	AUCTION_CLOSE_NORMAL					= 0,	//下架
	AUCTION_CLOSE_REPO						= 1,	//系统回购
	AUCTION_CLOSE_TRADE_VERIFY_FAIL			= 2,	//交易审核失败
	AUCTION_CLOSE_IDIP						= 3,	//IDIP下架
	AUCTION_CLOSE_SERVER					= 4,	//普通服处理，完成后ds发往PVE了
	AUCTION_CLOSE_PVE						= 5,	//中心服处理，完成后ds发往普通服
};

enum AUCTION_OPEN_TYPE
{
	AUCTION_OPEN_NORMAL						= 0,	//不涉及跨服，玩家正常上架
	AUCTION_OPEN_PRE_TO_PVE					= 1,	//普通服处理，玩家跨服定向出售
	AUCTION_OPEN_ROAM_PVE					= 2,	//中心服处理，到期转存和主动上架
};

enum AUCTION_BUY_TYPE
{
	AUCTION_BUY_NORMAL						= 0,	//不涉及跨服，玩家正常购买
	AUCTION_BUY_PRE_TO_PVE					= 1,	//普通服处理，玩家跨服购买
	AUCTION_BUY_ROAM_PVE					= 2,	//中心服处理，玩家跨服购买
};

enum AUCTION_IDIP_OP
{
	AUCTION_IDIP_OP_APPROVE			= 0,	//批准
	AUCTION_IDIP_OP_CLOSE			= 1,	//下架
};

enum PERSONALITY_MODIFY_TYPE
{
	PERSONALITY_MODIFY_CORP_MASTER	= 0,
	PERSONALITY_MODIFY_BLESS		= 1,
	PERSONALITY_MODIFY_ZHULI		= 2,
	PERSONALITY_MODIFY_BASKETBALL	= 3,
	PERSONALITY_MODIFY_KILLWHITE	= 4,
	PERSONALITY_MODIFY_KILLRED		= 5,

	PERSONALITY_MODIFY_MAX,
};

#define DBMASK_PUT_SYNC  (DBDATA_BASIC|DBDATA_CASH|DBDATA_POCKET|DBDATA_STORE)
#define DBMASK_PUT_ALL   (DBDATA_BASIC|DBDATA_CASH|DBDATA_POCKET|DBDATA_STORE|DBDATA_TASK)
#define DBMASK_PUT_SYNC_TIMEOUT (DBMASK_PUT_ALL&(~DBDATA_POCKET))

enum COMMON_DATA
{
	COMMON_DATA_WEATHER	= 7,       //场景天气种子
	COMMON_DATA_WEDDING	= 8,       //婚礼预约
	COMMON_DATA_PROSPERITY	= 9,       //繁荣度
	COMMON_DATA_RANDOMSEED	= 10,      //随机种子
	COMMON_DATA_ALLIANCEWAR	= 11,      //盟主战
	COMMON_DATA_WOO		= 12,      //求爱
	COMMON_DATA_NATION	= 13,      //国家
	COMMON_DATA_NATION_WAR	= 14,      //国家
	COMMON_DATA_GLOBAL = 15,        //全局数据

	COMMON_DATA_MASK_BITS	= 16,      //前缀左移位数
	COMMON_DATA_MASK	= 0xFFFF,  //数据蒙板
};

enum COMMON_DATA_VERSION
{
	WEATHER_VERSION		= 0x01,	//场景天气版本号
	WEDDING_VERSION		= 0x01,	//婚礼预约版本号
	PROSPERITY_VERSION	= 0x01,	//繁荣度版本号
	RANDOMSEED_VERSION	= 0x01, //随机种子版本号
	ALLIANCEWAR_VERSION	= 0x02,	//盟主战版本号
	WOO_VERSION		= 0x02, //求爱版本号
	NATION_VERSION		= 0x01, //国家版本号
};

enum AUTHD_ERROR
{
	AUERR_INVALID_ACCOUNT      = 2,   //帐号不存在
	AUERR_INVALID_PASSWORD     = 3,   //密码错误
	AUERR_LOGOUT_FAIL          = 12,  //AUTH登出失败
	AUERR_PHONE_LOCK           = 130, //电话密保处于锁定中
	AUERR_NOT_ACTIVED          = 131, //本服务器需经激活方可登入，该帐号未激活。
	AUERR_ZONGHENG_ACCOUNT     = 132, //纵横中文网帐号未经激活不能登录游戏。
	AUERR_STOPPED_ACCOUNT      = 133, //为了优化服务器负载，因该帐号长时间未登录游戏，已被封禁，请与客服联系。
	AUERR_LOGIN_FREQUENT	   = 134, //您登录频繁，请稍后重新登录
};

enum UNIQUE_NAME_ERROR
{
	// general
	UNAME_ERR_SUCCESS         = 0,
	UNAME_ERR_UNKNOWN         = -1,
	UNAME_ERR_MARSHAL         = -2,
	UNAME_ERR_NOTFOUND        = -3,

	// db
	UNAME_ERR_DB_NOTFOUND     = -4,
	UNAME_ERR_DB_UNKNOWN      = -5,

	// rolename/name
	UNAME_ERR_NOFREENAMESPACE = -6,
	UNAME_ERR_DUPLICATENAME   = -7,
	UNAME_ERR_INCONSISTENT    = -8,
	UNAME_ERR_HASNAME		  = -9,
};

enum TRANSACTION_RESULT
{
	TRANSACTION_CLOSED	= 0,   // 交易已关闭
	TRANSACTION_SUCCESS	= 1,   // 交易成功完成
	TRANSACTION_FAILED	= 2,   // 交易失败
	TRANSACTION_TIMEOUT	= 3,   // GS在预定时间内未确认交易结果，归还物品
	TRANSACTION_UNKNOWN	= 4,   // GS未收到交易的执行结果
};

enum SWORD_ERRCODE
{
	ERROR_PLAYER_LOGOUT            = 1,     // 角色正常退出游戏
	ERROR_GENERAL                  = 9,     // 通用错误，偷懒不想加错误码用这个
	ERROR_INVALID_PASSWORD         = 10,    // 帐号或者密码错误
	ERROR_MULTILOGIN               = 11,    // 帐号已经登录
	ERROR_PHONE_LOCK               = 12,    // 电话密保处于锁定中
	ERROR_NOT_ACTIVATED            = 13,    // 本服务器需经激活方可登入，该帐号未激活。
	ERROR_ZONGHENG_ACCOUNT         = 14,    // 纵横中文网帐号未经激活不能登录游戏。
	ERROR_FROZEN_ACCOUNT           = 15,    // 为了优化服务器负载，因该帐号长时间未登录游戏，已被封禁，请与客服联系。
	ERROR_AUTHD_UNKNOWN            = 16,    // 未知AUTHD错误
	ERROR_SERVER_CLOSED            = 17,    // 服务器正在维护中
	ERROR_SERVER_OVERLOAD          = 18,    // 服务器人数达到上限
	ERROR_BANNED_ACCOUNT           = 19,    // 帐号被禁止登录
	ERROR_AUTHD_TIMEOUT            = 20,    // 帐号服务器认证超时
	ERROR_PROXY_SEND               = 21,    // ProxyRpc转发失败
	ERROR_GM_KICKOUT               = 22,    // 客服踢人
	ERROR_FORCE_LOGIN              = 23,    // 帐号从其他地方登录
	ERROR_AUTHD_KICKOUT            = 24,    // 帐号服务器踢人
	ERROR_ACCOUNT_FORBID           = 25,    // 帐号被封禁
	ERROR_INVLAID_ACCOUNT          = 26,    // 帐号数据错误
	ERROR_DB_LISTROLE              = 27,    // 从数据库读取角色信息失败
	ERROR_LOGIN_PENDING            = 28,    // 等待上次登录退出
	ERROR_LOGIN_STATE              = 29,    // 帐号状态不正确，登录失败
	ERROR_FORBID_IGNORE            = 30,    // 已经存在更长的同类封禁记录
	ERROR_INVALID_SCENE            = 31,    // scene不存在
	ERROR_LOGINFREQUENT_USBKEY2    = 32,    // 绑定二代神盾的通行证在32秒之内只能登陆一次。
	ERROR_GACD_KICKOUT             = 33,    // 反外挂系统踢人
	ERROR_MATRIX_FAILURE           = 34,    // 密保验证失败
	ERROR_NOT_IN_WHITELIST 	       = 35, 	// 不在glinkd的白名单中
	ERROR_IWEB_VERSION             = 36,	// IWEB版本不一致
	ERROR_GM_FORBID	               = 39,	// GM关闭此项服务
	ERROR_SERVER_BUSY              = 40,    // 您的请求发送过于频繁，请稍候再试
	ERROR_TOKEN_ABATE              = 41,	// TOKEN/SESSION失效
	ERROR_PLAT_ERROR               = 42,	// 平台错误、服务器内部错误
	ERROR_NEW_PLAYER_SCENE         = 44,	// 进入创建选人场景失败
	ERROR_CSP_DISCONNECT           = 45,	// 服务器内部错误
	ERROR_LOGIN_ACTIVE_CODE_TIME_OUT = 46,	// 输入激活码超时
	ERROR_REGISTER_ACCOUNT_FORBID  = 47,	// 禁止注册用户
	ERROR_TOTAL_REGISTER_ACCOUNT_FULL = 48,	// 总注册人数达到上限
	ERROR_TODAY_REGISTER_ACCOUNT_FULL = 49,	// 今日注册人数达到上限
	ERROR_STRICT_REGISTER_ACCOUNT_FULL = 50,// 极限注册人数达到上限
	ERROR_ANTIWALLOW			   = 51,	// 防沉迷
	ERROR_ACCOUNT_MONITOR_FORBID   = 52,    // 成长守护帐号被封禁
	ERROR_NOT_COMEBACK_ACCOUNT     = 53,	// 不是回流服白名单账号
	ERROR_NOT_COMEBACK_OR_NEW_ACCOUNT    = 54,	// 不是回流服白名单账号或新账号
	ERROR_ROLE_TRADE_TRANSFERING	= 55,    // 帐号交易角色转移流程中

	ERROR_TEST_CODE                = 70,    // 测试错误码
	ERROR_TEST_CODE_2               = 71,    // 测试错误码

	ERROR_DB_NOTFOUND              = 100,   // 记录未找到
	ERROR_DB_OVERWRITE             = 101,   // 不能覆盖已有记录
	ERROR_DB_NULLKEY               = 102,   // 错误的key长度
	ERROR_DB_DECODE                = 103,   // 记录数据解码错误
	ERROR_DB_UNKNOWN               = 104,   // 未知数据库错误
	ERROR_DB_INVALIDINPUT          = 105,   // 请求参数校验失败
	ERROR_DB_CREATEROLE            = 106,   // 创建角色失败
	ERROR_DB_DISCONNECT            = 107,   // 服务器内部错误
	ERROR_DB_TIMEOUT               = 108,   // 服务器内部错误
	ERROR_DB_NOSPACE               = 109,   // 服务器上没有剩余空间
	ERROR_DB_VERIFYFAILED          = 110,   // 数据校验失败
	ERROR_DB_CASHOVERFLOW          = 111,   // 元宝金额已达上限
	ERROR_DB_EXCEPTION             = 112,   // 数据库异常
	ERROR_DB_DEADLOCK              = 113,   // mysql死锁或链接异常
	ERROR_DB_TXN_DEADLOCK	       = 114,	// 事务中出现了死锁
	ERROR_DB_RECORD_DEADLOCK       = 115,	// 记录死锁
	ERROR_DB_LOCK_SEQ	           = 116,	// 事务中加锁顺序出现问题
	ERROR_DB_OUT_OF_RANGE	       = 117,	// 数据库记录过长
	ERROR_DB_PBDECODE	           = 118,	// pb协议解析错误

	ERROR_ROLELIST_FULL            = 150,   // 本帐号不能创建更多角色
	ERROR_INVALID_NAME             = 151,   // 名字中含有非法字符
	ERROR_UNAMED_DISCONNECT        = 152,   // 不能连接到名字服务器，请稍侯
	ERROR_UNAMED_NAMEUSED          = 153,   // 该名字已经被使用
	ERROR_GAMEDBD_NAMEUSED         = 154,   // 该名字已经被使用
	ERROR_ROLELIST_TIMEOUT         = 155,   // 获得角色列表超时
	ERROR_NAME_WRONG_LEN           = 156,   // 名字太长或太短
	ERROR_INVALID_REFERRAL         = 157,   // 错误的推广码
	ERROR_REFERRAL_FORBID          = 158,   // 已有角色不能使用推广码
	ERROR_REFERRAL_CLOSE           = 159,   // 推广码功能没有开启

	ERROR_ELIMINATE_GUESS_ALREADY       = 160,  // 玩家已经对该场比赛竞猜
	ERROR_ELIMINATE_GUESS_COUNT_LIMIT   = 161,  // 玩家当前赛季已经达到竞猜上线
	ERROR_ELIMINATE_GUESS_WRONG_STATE   = 162,  // 玩家在错误的状态进行竞猜
	ERROR_ELIMINATE_GUESS_WRONG_TIME    = 163,  //
	ERROR_ELIMINATE_GUESS_WRONG_MONEY   = 164,  //
	ERROR_ELIMINATE_GUESS_NO_ORDER      = 165,  //
	ERROR_ELIMINATE_GUESS_WRONG_ID      = 166,  //
	ERROR_ELIMINATE_GUESS_NO_GUESS      = 167,  //
	ERROR_ELIMINATE_GUESS_ALREADY_AWARD = 168,  //
	ERROR_ELIMINATE_GUESS_AWARD_WRONG_STATE = 169,  //
	ERROR_ELIMINATE_GUESS_NO_RESULT     = 170,
	ERROR_ELIMINATE_GUESS_NO_AWARD      = 171,

	ERROR_CORPS_SEASON_INVALID_POS      = 180,
	ERROR_CORPS_SEASON_INVALID_JOIN_TIME = 181,
	ERROR_CORPS_SEASON_NO_HISTORY       = 182,

	ERROR_CMD_COOLING              = 200,   // 命令处于冷却中
	ERROR_CMD_INVALID              = 201,   // 角色状态错误
	ERROR_DATA_EXCEPTION           = 202,   // 数据异常
	ERROR_DATA_LOADING             = 203,   // 正在读取数据
	ERROR_LINE_UNAVAILABLE         = 204,   // 没有可用的线路
	ERROR_LINE_NOTFOUND            = 205,   // 选择的线路不存在
	ERROR_LINE_FULL                = 206,   // 该线路玩家数已经达到上限
	ERROR_SERVER_NETWORK           = 207,   // 网络通信错误
	ERROR_ROLE_BANNED              = 208,   // 角色被禁止登录
	ERROR_ROLE_UNAVAILABLE         = 209,   // 角色不能登录
	ERROR_ROLE_LOGINFAILED         = 210,   // 登录游戏服务器失败
	ERROR_ROLE_MULTILOGIN          = 211,   // 角色已经在游戏服务器中
	ERROR_ROLE_NOTFOUND            = 212,   // 角色不存在
	ERROR_INVALID_DATA             = 213,   // 收到客户端发送的错误数据
	ERROR_GS_DISCONNECTED          = 214,   // 服务器内部错误
	ERROR_GS_DROPPLAYER            = 215,   // 游戏服务器断开用户连接
	ERROR_CLIENT_SEND              = 216,   // 客户端接收数据出错
	ERROR_CLIENT_RECV              = 217,   // 客户端发送数据出错
	ERROR_CLIENT_CLOSE             = 218,   // 客户端主动关闭连接
	ERROR_CLIENT_TIMEOUT           = 219,   // 客户端连接超时
	ERROR_CLIENT_INVALIDDATA       = 220,   // 客户端收到不正确的协议
	ERROR_CLIENT_DECODE            = 221,   // 客户端收到错误的协议数据
	ERROR_ROLE_DELETED	           = 222,	// 角色已经被删除
	ERROR_SERVER_CLOSING	       = 223,	// 服务器即将关闭
	ERROR_WAIT_CONNECTION          = 224,	// 断线重连等待
	ERROR_SERVER_SHUTDOWN          = 225,   // 服务器关闭

	ERROR_PLAYER_OFFLINE           = 301,   // 玩家不在线
	ERROR_TEAM_FULL                = 302,   // 队伍已满
	ERROR_TEAM_PLAYERINTEAM        = 303,   // 玩家已经加入队伍
	ERROR_TEAM_REFUSED             = 304,   // 对方拒绝组队邀请
	ERROR_TEAM_NOTFOUND            = 305,   // 队伍不存在
	ERROR_TEAM_DENIED              = 306,   // 没有队长权限
	ERROR_TEAM_LEADEROFFLINE       = 307,   // 队长没有在线
	ERROR_TEAM_NOTONLINE           = 308,   // 不在线队员不能成为队长
	ERROR_TEAM_DUPLICATE           = 309,   // 重复发布队伍招人信息
	ERROR_MAIL_BOXFROZEN           = 310,   // 对方邮箱冻结
	ERROR_MAIL_BOXFULL             = 311,   // 对方邮箱已满
	ERROR_MAIL_NOTFOUND            = 312,   // 邮件没有找到
	ERROR_MAIL_NOATTACHMENT        = 313,   // 附件没有找到
	ERROR_FRIEND_LISTFULL          = 320,   // 好友数量达到上限
	ERROR_FRIEND_REFUSED           = 321,   // 对方拒绝好友邀请
	ERROR_FRIEND_LOADING           = 322,   // 好友数据暂时不可用
	ERROR_FRIEND_BLACKLISTFULL     = 323,   // 黑名单人数达到上限
	ERROR_SECT_OFFLINE             = 330,   // 玩家不在线
	ERROR_SECT_UNAVAILABLE         = 331,   // 对方已经拜师
	ERROR_SECT_FULL                = 332,   // 徒弟数量已经达到上限
	ERROR_SECT_REFUSE              = 333,   // 对方拒绝了你的收徒邀请
	ERROR_SECT_INVALIDLEVEL        = 334,   // 对方级别不满足要求
	ERROR_SECT_COOLING             = 335,   // 一天只能招收一弟子
	ERROR_SECT_DBERROR             = 336,   // 保存数据失败
	ERROR_SECT_NOTFOUND            = 337,   // 查找不到师门信息
	ERROR_SECT_NONINSIDER          = 338,   // 玩家不属于本师门
	ERROR_TRANSACTION_PENDING      = 342,   // 角色数据处于事务状态中，暂时不能发起新的事务
	ERROR_TEAM_CLIENT_REFUSEED     = 343,   // 对方拒绝(对方客户端原因)
	ERROR_TEAM_CANT_BE_LEADER      = 344,   // 自己没队伍，但对方有队伍，所以无法建新队伍成为队长
	ERROR_TEAM_RECRUIT_MIN_LEVEL   = 345,   // 不满足招募等级设定
	ERROR_TEAM_RECRUIT_NO_EXIST    = 346,   // 招募已取消
	ERROR_NO_CORP				   = 347,   // 没有加入帮派
	ERROR_PLAYER_ROAMOUT           = 348,   // 玩家跨服中
	ERROR_FRIEND_ADD_LEVEL_LIMIT   = 349,	// 玩家等级不足，无法添加好友

	ERROR_MAILPACK_FRIEND	       = 350,	// 只有好友可以赠送邮件
	ERROR_TEAM_APPLY_LEVEL_LIMIT   = 351,	// 玩家等级不足，无法邀请组队or申请加入队伍

	ERROR_FRIEND_GROUP_COUNT_LIMIT = 352,	// 分组数量达到上限
	ERROR_TEAM_HAS_MORE_THEN_ONE   = 353,	// 申请加入队伍时自己拥有队伍且队伍中不止一个人
	ERROR_TEAM_APPLY_APPLY_SUCCESS	   = 354,	// 队伍申请成功
	ERROR_SIMPLIFIED_CLIENT_RESOURCE_WITHOUT_DOWNLOAD = 355, // 小包资源未下载
	ERROR_TEAM_APPLY_INVITE_SUCCESS	   = 356,	// 队伍邀请成功

	ERROR_TOP_LIST_ACTIVE          = 370,	// 比武排名系统未开启
	ERROR_MAX_FIGHT_RANK           = 371,	// 玩家排名超出最高排名
	ERROR_WRONG_ADVERSARY          = 372,	// 选择了错误的对手
	ERROR_ROLE_IN_BATTLE           = 373,	// 玩家正在接受挑战
	ERROR_BATTLE_TIME_OUT          = 374,	// 挑战超时
	ERROR_TOP_MIN_LEVEL            = 375,	// 玩家没有到达挑战等级
	ERROR_GET_REWARD               = 376,	// 玩家已经领了奖励
	ERROR_MAIL_FORCEDELETE         = 377,   // 不能删除有附件的邮件
	ERROR_FRIEND_INBLACK		   = 378,	// 对方在你的黑名单内

	ERROR_GS_LOADTIMEOUT           = 400,   // 数据库读取超时
	ERROR_GS_LOADEXCEPTION         = 401,   // 数据库读取失败
	ERROR_GS_INVALIDDATA           = 402,   // 非法的角色数据
	ERROR_GS_INVALIDPOSITION       = 403,   // 角色处在错误的位置
	ERROR_GS_INVALIDWORLD          = 404,   // 世界类型错误
	ERROR_GS_MULTILOGIN            = 405,   // 玩家已经处于登入状态
	ERROR_GS_LOADFAILED            = 406,   // 加载玩家数据失败
	ERROR_GS_OVERLOADED            = 407,   // 本线达到人数上限
	ERROR_GS_INVALIDSTATE          = 408,   // 玩家状态错误
	ERROR_GS_DROPDELIVERY          = 409,   // GS与DS断开连接，连接恢复中
	ERROR_MARRY_GENDER             = 410,   // 性别错误
	ERROR_MARRY_NOT_SINGLE         = 411,   // 婚姻状态错误
	ERROR_MARRY_COOLTIME           = 412,   // 婚姻冷却中
	ERROR_MARRY_WRONG_LEVEL        = 413,   // 人物级别不够
	ERROR_MARRY_REJECTED           = 414,   // 对方拒绝
	ERROR_MARRY_ITEM               = 415,   // 缺少物品
	ERROR_VOTE_VOTING              = 416,   // 已经在投票中
	ERROR_MARRY_NOT_2PERSON        = 417,   // 组成员不是2个人
	ERROR_MARRY_NOT_ENGAGED        = 418,   // 未订婚
	ERROR_MARRY_AMITY              = 419,   // 好感度不够
	ERROR_MARRY_POSITION           = 420,   // 组成员不在一块
	ERROR_MARRY_NOT_TEAMLEADER     = 421,   // 申请者不是组长
	ERROR_VOTE_FAILED              = 422,   // 投票结果未通过
	//ERROR_FAMILY_LACK_OF_MONEY     = 423,   // 缺少操作所需要的金钱
	ERROR_TEAM_OFFLINE             = 424,   // 组队中有成员不在线
	ERROR_IDIOM_SOLITAIRE_ALREADY_BEGIN = 425, //成语接龙已经开始
	ERROR_IDIOM_SOLITAIRE_NOT_BEGIN = 426,	//成语接龙还未开始
	ERROR_IDIOM_SOLITAIRE_INVALID_IDIOM = 427, //非法的成语输入
	ERROR_IDIOM_SOLITAIRE_DUP_IDIOM	= 428,	//重复的成语
	ERROR_IDIOM_SOLITAIRE_COOLDOWN	= 429,	//开启答题过于频繁
	ERROR_IDIOM_SOLITAIRE_OPEN_BLACKLIST	= 430,	//开启成语接龙黑名单
	ERROR_IDIOM_SOLITAIRE_ANSWER_BALCKLIST	= 431,	//回答成语接龙黑名单
	ERROR_THANKS_GIVING_IN_LOADING		= 432,	//
	ERROR_THANKS_GIVING_IN_PROCRSSING	= 433,	//
	ERROR_THANKS_GIVING_NOT_FRIEND		= 434,	//
	ERROR_THANKS_GIVING_MSG_INVALID_SIZE	= 435,	//
	ERROR_THANKS_GIVING_MSG_INVALID_CONTENT	= 436,	//
	ERROR_THANKS_GIVING_TODAY		= 437,	//
	ERROR_THANKS_GIVING_COOLDOWN		= 438,	//
	ERROR_THANKS_GIVING_LEVEL		= 439,	//
	ERROR_THANKS_GIVING_2			= 440,	//
	ERROR_BREAK_CONNECTION	       = 441, 	// 断开与客户端的连接
	ERROR_INSTANCE_NOTFOUND        = 442,   // 副本没有找到
	ERROR_VOTE_TIMEOUT             = 443,   // 投票超时
	ERROR_LOST_CONNECTION          = 444,   // 失去与客户端的连接
	ERROR_TRUSTEE_DUPLICATE        = 445,   // 重复的受托人
	ERROR_TRUSTEE_COUNT_LIMIT      = 446,   // 受托人数超过上限
	ERROR_TRUSTEE_NOTFOUND         = 447,   // 托管关系不存在
	ERROR_TRUSTEE_SELF             = 448,   // 不能指定自己帐号下的角色为受托人
	ERROR_TRUSTEE_PERMISSION       = 449,   // 受托人没有操作权限
	ERROR_TRUSTOR_ONLINE           = 450,   // 委托人正在游戏中
	ERROR_FRIEND_BUFF_INVALID      = 451,   // 发送人没有这个技能或者级别不对
	ERROR_FRIEND_BUFF_SEND1_COOL   = 452,   // 发送冷却中
	ERROR_FRIEND_BUFF_SEND2_COOL   = 453,   // 已达到日发送上限
	ERROR_FRIEND_BUFF_RECV_COOL    = 454,   // 接收冷却中
	ERROR_FRIEND_BUFF_NOT_REMOTE   = 455,   // 非远程技能
	/*
	ERROR_FAMILY_VOTE_ERROR        = 456,   // 发起结义内投票失败
	ERROR_FAMILY_VOTE_VOTING       = 457,   // 有同样的结义内投票正在进行
	ERROR_FAMILY_VOTE_VOTING_MAX   = 458,   // 达到同时允许进行的结义内投票上限了
	ERROR_FAMILY_VOTE_VOTED        = 459,   // 个人已经投过票了
	ERROR_FAMILY_POSITION          = 460,   // 组员没有在一块
	*/
	ERROR_MARRY_NOTINONETEAM       = 461,   // 夫妻不在同一组
	ERROR_MARRY_NOT_SPOUSE         = 462,   // 一方不是另一方的配偶
	ERROR_MARRY_DIVORCE_HOMETOWN   = 463,   // 因某操作而开始投票了，实际上不是错误，是一个中间状态
	ERROR_MARRY_NOT_MARRIED        = 464,   // 未结婚
	ERROR_STOCK_CLOSED             = 465,   // 元宝交易账户已关闭
	ERROR_STOCK_ACCOUNTBUSY        = 466,   // 元宝账户忙
	ERROR_STOCK_INVALIDINPUT       = 467,   // 非法输入
	ERROR_STOCK_OVERFLOW           = 468,   // 元宝或金钱数值溢出
	ERROR_STOCK_DATABASE           = 469,   // 数据库错误
	ERROR_STOCK_NOTENOUGHCASH      = 470,   // 元宝不足
	ERROR_STOCK_MAXCOMMISSION      = 471,   // 超过最大挂单数
	ERROR_STOCK_NOTFOUND           = 472,   // 未找到相关记录
	ERROR_STOCK_CASHLOCKED         = 473,   // 元宝交易已锁定
	ERROR_STOCK_CASHUNLOCKFAILED   = 474,   // 元宝交易解锁失败
	ERROR_STOCK_NOFREEMONEY        = 475,   // 无可取出金钱
	ERROR_STOCK_NOTENOUGHMONEY     = 476,   // 包裹金钱不足
	ERROR_SECT_QUIT_COOLING        = 477,   // 叛师冷却
	ERROR_SECT_EXPEL_COOLING       = 478,   // 开除徒弟冷却
	ERROR_SECT_RECOMMENDED         = 479,   // 已经是该师父的记名弟子了
	ERROR_SECT_TEACH_COOLING       = 480,   // 今天已经教过了
	ERROR_SECT_NOCONSULT           = 481,	// 请教的机会用光了
	ERROR_SECT_NOT_VICE_MENTOR     = 482,	// 被请教者不是记名师父
	ERROR_SECT_UPGRADE_LIMIT       = 483,	// 师德不够，无法升级宗师等级
	ERROR_FRIEND_CANNOT_BLACK      = 484,	// 特殊组中的好友无法加入到黑名单
	ERROR_SECT_RELATION            = 485,	// 无亲友关系，无法推荐徒弟
	ERROR_SECT_NOT_DISCIPLE        = 486,	// 只能鼓励未出师徒弟
	ERROR_HOME_NOTLOADED           = 487,   // 家园数据未加载
	ERROR_HOME_COOLING             = 488,   // 命令冷却中
	ERROR_HOME_TIMEOUT             = 489,   // 超时
	ERROR_HOME_LOCKED              = 490,   // 锁定状态，操作进行中
	ERROR_HOME_UNMARSHAL           = 491,   // 解码数据出错
	ERROR_HOME_INVALIDINPUT        = 492,   // 输入数据非法
	ERROR_HOME_INVALIDSTATE        = 493,   // 非法状态
	ERROR_HOME_PERMISSION          = 494,   // 无操作权限
	ERROR_HOME_OFFLINE             = 495,   // 玩家不在线
	ERROR_HOME_NOTFRIEND           = 496,   // 不是好友
	ERROR_HOME_NOSEED              = 497,   // 种子或幼兽不存在
	ERROR_HOME_NOENOUGHPRODUCEPOINT = 498,  // 生产点不足
	ERROR_HOME_AMBUSH_FULL         = 499,   // 埋伏人数已满
	ERROR_HOME_STOREHOUSE_FULL     = 500,   // 仓库已满
	ERROR_HOME_AMBUSHING           = 501,   // 已处于埋伏状态
	ERROR_HOME_NOFREEPRODUCTS      = 502,   // 没有可收获/偷窃的产物（偷窃时有保留个数限制）
	ERROR_HOME_STEALSELF           = 503,   // 不能偷自已
	ERROR_HOME_STEALAGAIN          = 504,   // 再次偷窃
	ERROR_HOME_STEALCAUGHT         = 505,   // 偷窃被抓
	ERROR_HOME_FRUITPROTECTED      = 506,   // 果实处于采摘保护期
	ERROR_HOME_PRODUCESKILL        = 507,   // 所需生产技能或技能等级不符合要求
	ERROR_HOME_NOTENOUGHPACKSPACE  = 508,   // 非安全区或包裹空间不足，物品已经存入系统邮件中，请自行取出
	ERROR_HOME_NO_ENOUGH_FORAGE    = 509,   // 饲料不足
	ERROR_HOME_TOO_MANY_FORAGE     = 510,   // 饲料过多
	ERROR_HOME_INVALID_ACTION      = 511,   // 非法操作
	ERROR_HOME_PLOT_NOT_FREE       = 512,   // 地块非空
	ERROR_HOME_CAPACITY            = 513,   // 超过容量限制
	ERROR_HOME_PLOT_INACTIVE       = 514,   // 地块未开放
	ERROR_HOME_PLOT_BLESSED        = 515,   // 地块已被祈福
	ERROR_FACTION_BAD_NAME         = 516,   // 非法名
	ERROR_FACTION_DUP_NAME         = 517,   // 重名
	ERROR_FACTION_MONEY            = 518,   // 钱不够
	ERROR_FACTION_SERVER           = 519,   // 服务器内部错误
	ERROR_FACTION_FULL             = 520,   // 帮派人数达到上限
	ERROR_FACTION_PERMISSION       = 521,   // 没有权限
	ERROR_FACTION_REFUSED          = 522,   // 对方拒绝
	ERROR_FACTION_LEVEL_MAX        = 523,   // 已经升到最高级
	ERROR_FACTION_COST             = 524,   // 升级所需资源不足
	ERROR_FACTION_TMP_MEMBER       = 525,   // 挂名成员不能任免
	ERROR_FACTION_UNAVAILABLE      = 526,   // 职位有人
	ERROR_FACTION_SUBFACTION       = 527,   // 分舵状态不对
	ERROR_FACTION_SPOUSE           = 528,   // 因配偶有职务而不能任免
	ERROR_FACTION_WRONG_POSITION   = 529,   // 无效职位
	ERROR_FACTION_EXPEL_COOLING    = 530,   // 踢人冷却
	ERROR_FACTION_HAS_FACTION      = 531,   // 被加者已经有帮派
	ERROR_GRADE_INVALIDLEVEL       = 532,   // 玩家不在任何同等级频道中
	ERROR_SHARE_EXPIRE             = 533,   // 祝福过期失效
	ERROR_SHARE_FULL               = 534,   // 非好友祝福已满
	ERROR_SHARE_GRADE              = 535,   // 等级区间不符
	ERROR_SHARE_AGAIN              = 536,   // 已经祝福过了
	ERROR_SHARE_SELF               = 537,   // 不能祝福自己
	ERROR_SHARE_INVALID            = 538,   // 无效的祝福
	ERROR_DEL_ROLE_FAMILY          = 539,   // 已结义的角色不能删除
	ERROR_DEL_ROLE_FACTION         = 540,   // 加入帮派的角色不能删除
	ERROR_DEL_ROLE_DISCIPLE        = 541,   // 未出师的角色不能删除
	ERROR_DEL_ROLE_MENTOR          = 542,   // 已收徒的角色不能删除
	ERROR_DEL_ROLE_MARRIAGE        = 543,   // 已结婚的角色不能删除
	ERROR_INVENTORY_FULL           = 544,   // 包裹已满
	ERROR_INVENTORY_BIND_MONEY_FULL = 545,  // 银票携带数已达上限
	ERROR_INVENTORY_TRADE_MONEY_FULL = 546, // 银子携带数已达上限
	ERROR_HOME_BLESS_NO_CHANCES    = 547,   // 无祈福机会
	ERROR_HOME_CLOSED              = 548,   // 家园模块未开启
	ERROR_SECT_FAMILY              = 549,   // 结义关系不能拜师
	ERROR_SNS_QUALITY              = 550,   // 没有资格发此征友信息
	ERROR_SNS_EXISTED              = 551,   // 已经发过此征友信息了
	ERROR_SNS_NOTFOUND             = 552,   // 找不到征友信息
	ERROR_FACTION_DEL_ACTIVITY     = 553,   // 会长踢人活力不足
	ERROR_LESS_LEVEL               = 555,	// 玩家等级太低
	ERROR_GREATER_LEVEL            = 556,	// 玩家等级过高
	ERROR_NO_FAMILY                = 557,	// 玩家不属于任何结义
	ERROR_IN_ALLIANCE_WAR          = 558,	// 盟主战进行中
	ERROR_ALLIANCE_MONEY           = 559,	// 盟主金不足
	ERROR_CANNOT_SPECTATE          = 560,	// 不能进行观战
	ERROR_ALLIANCE_CANNT_APPLY     = 561,	// 现在不能申请盟主战
	ERROR_ALLIANCE_MAX_FAMILY      = 562,	// 申请参战的结义达到上限
	//ERROR_FAMILY_LEAGUE            = 563,	// 盟主所在结义
	ERROR_ALLIANCE_CANNT_START     = 564,	// 盟主战尚未开始
	ERROR_ALLIANCE_WAR_NO_APPLY    = 565,	// 没有申请盟主战
	//ERROR_FAMILY_IN_ALLIANCE_WAR   = 566,	// 结义正在盟主战中，不能投票
	ERROR_NOT_LEAGUE               = 567,	// 没有盟主权限
	ERROR_LEAGUE_MONEY_LIMIT       = 568,	// 盟主取钱数达到上限
	ERROR_LEAGUE_COOLDOWN          = 569,	// 盟主权限冷却时间内
	ERROR_FACTION_LEVEL_MIN        = 570,	// 贵帮规模太小，不足以开宗立派。
	ERROR_FACTION_BASE_MONEY       = 571,	// 这可是近百亩的一整块地呢，区区**元宝，可真不算贵
	ERROR_FACTION_BASE_ERROR       = 572,	// 不好意思，这周围的地皮都被其他武林玩家购买了
	ERROR_FACTION_TEAM             = 573,	// 在江湖里没点朋友，建立帮会可不是好玩的
	ERROR_FACTION_MEMBER_LESS_LEVEL = 574,	// 创建队伍中其他玩家等级过低
	ERROR_FACTION_MEMBER_FRIEND    = 575,	// 创建帮派互为好友，且好友度达到要求
	ERROR_FACTION_NOT_READY        = 576,	// 帮派数据未准备好
	ERROR_FACTION_MERGEREQ_INVALID = 577,	// 帮派合并请求失败
	ERROR_FACTION_STATUS_CANNOTDO  = 578,	// 帮派当前状态不能进行此操作
	ERROR_FACTION_MERGEREQ_AGREE   = 579,	// 对方同意合帮
	ERROR_FACTION_MERGEREQ_DISAGREE = 580,	// 对方不同意合帮
	ERROR_FACTION_MERGEVOTE_FAILED = 581,   // 发起投票失败
	ERROR_FACTION_DB_WAIT          = 582,	// 数据库忙，暂时无法服务
	ERROR_FACTION_STATE            = 583,	// 帮派状态不错错误
	ERROR_FACTION_MAX_COUNT        = 584,	// 帮派数已满，不能创建新帮派
	ERROR_FACTION_TIMEOUT          = 585,	// 帮派建立超时
	ERROR_FACTION_INVALID_DATA     = 586,	// 帮派数据错误
	ERROR_FACTION_ITEM             = 587,   // 物品不齐
	ERROR_FACTION_NOTFOUND         = 588,	// 没有找到相应的帮派
	ERROR_FACTION_NO_BASE          = 589,	// 帮派没有基地
	ERROR_FACTION_MAX_SUB          = 590,	// 帮派分舵数已满
	ERROR_FACTION_GETPARA	       = 591,	// 获取帮派协议参数错误
	ERROR_FACTION_DOWORKCOOLDOWN   = 592,	// 还未冷却，不能打工
	ERROR_FACTION_DOWORKSTATUS     = 593,	// 当前状态不能打工
	ERROR_ROLE_NOFACTION           = 594,	// 玩家没有加入帮派
	ERROR_FACTION_APPLIED          = 595,	// 已经申请
	ERROR_FACTION_INMERGE	       = 596,	// 帮派已经在合并过程中
	ERROR_FACTION_MERGEVOTE_NOTPASS = 597,	// 合并投票未通过
	ERROR_FACTION_MERGEVOTE_WAIT   = 598,	// 合并投票通过，待对方帮派投票确认
	ERROR_FACTION_VOTE_OPEN        = 599,	// 投票开通失败
	ERROR_FACTION_MERGE_MAX        = 600,	// 合并人数过多
	ERROR_FACTION_SUB_COOLDOWN     = 601,	// 解除分舵冷却时间
	ERROR_FACTION_CONTRIBUTAION    = 602,	// 帮派建设度不足
	ERROR_FACTION_APPLIER_FULL     = 603,	// 帮派申请人数过多
	ERROR_FACTION_INVALIDHIREREQ   = 604,	// 非法打工请求
	ERROR_FACTION_APPLYCOOLDOWN    = 605,	// 申请加入帮派冷却
	ERROR_FACTION_STOREFULL	       = 606,	// 帮派仓库已满
	ERROR_FACTION_REBELTIMEFAILED  = 607,	// 篡权时间不符合条件
	ERROR_FACTION_SUB_FACTION      = 608,	// 已经在这个帮建立了分舵
	ERROR_FACTION_TEMPLATE	       = 609,	// 帮派模板数据不正确
	ERROR_FACTION_BASE_ACTIVITY    = 610,	// 帮派基地将因为活跃度不足关闭
	ERROR_FACTION_BASE_CLOSED      = 611,	// 帮派基地关闭
	ERROR_FACTION_BASE_MEMBERS     = 612,	// 帮派基地将因为成员不足关闭
	ERROR_FACTION_ACTIVITYOPEN     = 613,   // 帮派有活动已经开启
	ERROR_FACTION_ACTIVITYEND      = 614,   // 帮派有活动已经结束
	//ERROR_FAMILY_CALL_MISS         = 615,	// 有成员在特殊区域无法响应结义召集
	ERROR_STOCK_TXNCOOLING         = 616,   // 事务操作冷却中
	ERROR_FACTION_IN_TIME	       = 617,	// 帮派成员在加入帮派冷却中
	ERROR_FACTION_MAXAPPLIED       = 618,	// 已经申请满
	ERROR_FACTION_HOLYBOSS_GROWTH  = 619,	// 圣兽经验值不足
	ERROR_FACTION_IN_FACTION       = 620,	// 玩家已经加入帮派
	ERROR_FACTION_SUBSTRUCT_LIMIT  = 621,	// 附属建筑级别不够, 不能升级主建筑
	ERROR_SECT_POSITION            = 622,   // 组成员不在一块
	ERROR_SECT_AMITY	       = 623,	// 带徒拜师友好度不足
	ERROR_MARRY_TEAM	       = 624,	// 结婚请求队伍错误
	ERROR_DIVORCE_TEAM	       = 625,	// 结婚请求队伍错误
	ERROR_MARRY_FRIEND	       = 626,	// 结婚对象错误的好友关系
	ERROR_MAIL_ATTACHMENT_LIMIT    = 627,	// 邮件收取物品达到上限

	ERROR_TEAM_IN_INSTANCE         = 630,	// 队伍已经开启了一个副本
	ERROR_NOT_ENOUGH_MEMBER        = 631,	// 队伍人数不足
	ERROR_CREATE_INSTANCE          = 632,	// 创建副本失败
	ERROR_INSTANCE_TIME            = 633,   // 副本返回时间限制
	ERROR_INSTANCE_BOARD           = 634,	// 副本版面限制
	ERROR_INSTANCE_CLOSE           = 635,	// 副本关闭
	ERROR_INSTANCE_PLAYER_CANCEL   = 636,	// 玩家自己关闭管理面板
	ERROR_INSTANCE_MAX_TIMES       = 637,	// 玩家超过进入副本次数上限

	ERROR_HOME_NO_FREE_SPACE       = 640,	// 家园地图已满
	ERROR_HOME_INTERNAL            = 641,   // 家园内部错误
	ERROR_HOME_EXIST               = 642,	// 已有家园
	ERROR_HOME_NO_HOME             = 643,	// 没有家园
	ERROR_HOME_SCENE               = 644,	// 家园地图错误
	ERROR_HOME_BUILD_POINT         = 645,	// 建设度不足
	ERROR_HOME_RESOURCE            = 646,	// 资源不足
	ERROR_HOME_DUPLICATE           = 647,	// 重复
	ERROR_HOME_LEVEL               = 648,	// 家园或者建筑等级错误

	ERROR_WEDDING_BUSY	       = 649,	// 婚礼正在进行

	ERROR_FCITY_NOKINGAPPLYSUCCESS = 659,	//无龙头地图申请分舵成功
	ERROR_FCITY_DB_CORRUPT	       = 660,	//数据库一致性错误
	ERROR_FCITY_DB_SUBADD_EXIST    = 661,	//该帮在本地图已经有分舵或总舵
	ERROR_FCITY_SERVER	       = 662,	//帮派势力服务器错误
	ERROR_FCITY_APPLYEXIST	       = 663,	//该帮派的分舵申请已经存在
	ERROR_FCITY_APPLYFULL          = 664,	//势力地图分舵信息已满
	ERROR_FCITY_NOTFOUND           = 665,	//帮派势力地图不存在
	ERROR_FCITY_SUBEXIST           = 666,	//该分舵已经存在
	ERROR_FCITY_SUBNOTEXIST        = 667,	//该地图不存在该分舵
	ERROR_FCITY_MAINEXIST          = 668,	//该总舵已经存在
	ERROR_FCITY_MAINNOTEXIST       = 669,	//该地图不存在该总舵
	ERROR_FACTION_CITYEXIST        = 670,	//该帮已有此势力地图
	ERROR_FACTION_CITYNOTEXIST     = 671,	//该帮没有该势力地图
	ERROR_FACTION_CITYFULL         = 672,	//该帮势力地图数量已满
	ERROR_FACTION_INVALIDCITYNUM   = 673,	//帮派已有的势力地图数目不合法
	ERROR_FACTION_INITCITY         = 674,	//初始势力地图
	ERROR_FCITY_AUCPRICE_LESS      = 675,	//出价太少
	ERROR_FCITY_AUCTIONCLOSE       = 676,	//拍卖结束
	ERROR_FCITY_DB_RELOAD	       = 677,	//需要重导数据
	ERROR_FACTION_AUCTIONPOINT_LESS = 678,	//竞拍点太少了
	ERROR_FACTION_APDONATED	       = 679,	//每天只能捐一次
	ERROR_FACTION_APMASTERNAME     = 680,	//受益方帮主名不相符合
	ERROR_FCITY_SUBFULL	       = 681,	//势力地图分舵已满
	ERROR_FCITY_MAINFULL	       = 682,	//势力地图总舵已满
	ERROR_FCITY_NOTOPEN	       = 683,	//势力地图未开放
	ERROR_FCITY_NOTINAUC	       = 684,	//势力地图当前没有处于拍卖状态中
	ERROR_FCITY_WEIGHTVALID	       = 685,	//势力地图设置权重数据不合法
	ERROR_FCITY_SUBINPROTECT       = 686,	//该分舵处于保护期不能删除
	ERROR_FCITY_AUCPRICE_TWICE     = 687,	//势力地图竞拍帮派重复出价
	ERROR_FACTION_CITYCONSLESS     = 688,	//开拓势力时建设度不够
	ERROR_FCITY_FACTIONFULL	       = 689,	//势力地图可容纳的帮派数已满
	ERROR_FCITY_POWERLESS	       = 690,	//势力地图开放所需要的新势力不够
	ERROR_FACTION_NOMAIN	       = 691,	//总舵搬迁时，源势力地图没有总舵
	ERROR_FACTION_NOSUB	       = 692,	//总舵搬迁时，目的势力地图没有分舵
	ERROR_FACTION_AUCNOBASE        = 693,	//没有基地的帮派不允许竞拍
	ERROR_TEAM_MEMBER_TOO_FAR      = 694,	//队伍成员离的太远
	ERROR_FACTION_NOTNEARCITY      = 695,	//非临近势力地图，不能申请
	ERROR_FACTION_APPLER_OFFLINE   = 696,	//社团申请者不在线

	ERROR_AUCTION_PRICE            = 700,	//拍卖价格不满足条件
	ERROR_AUCTION_TYPE             = 701,	//拍卖类型不满足条件
	ERROR_AUCTION_NONE             = 702,	//当前没有拍卖
	ERROR_AUCTION_NOT_START        = 703,	//拍卖未开启
	ERROR_AUCTION_BID_TWICE        = 704,	//重复出价

	ERROR_MINGXING_EMPTY           = 711,	//没有该明星或者明星没有形象备份
	ERROR_MINGXING_NO_CHANGE       = 712,	//明星形象没有变化

	ERROR_LIST_END			= 721,	//到达列表结尾
	ERROR_LIST_WRONG_TYPE		= 722,	//列表类型错误
	ERROR_LIST_ABATE_DATA		= 723,	//失效数据
	ERROR_WRONG_KEY			= 724,	//索引查询错误

	ERROR_ENOUGH_REPU		= 730,	//没有足够的残页
	ERROR_WRONG_ENEMY		= 731,	//选择的对手不存在

	ERROR_DST_ZONE_DISCONNECT	= 740,	//漫游目标服务器无法连接
	ERROR_ZONE_NOT_REGISTER		= 741,	//当前服务器没有注册
	ERROR_HUB_SERVER_DISCONNECT	= 742,	//漫游目标服务器无法连接HUB服务器
	ERROR_WRONG_ZONE_ROAM		= 743,	//目标服务器错误
	ERROR_ROAM_TIMEOUT		= 744,	//漫游超时
	ERROR_ROAM_ROLE_NOTFOUND	= 745,	//漫游角色数据查找失败
	ERROR_ROAM_WRONG_STATUS		= 746,	//错误的角色状态
	ERROR_ROAM_DECODE		= 747,	//漫游角色数据加载错误
	ERROR_ROAM_GENERAL		= 748,	//漫游通用错误
	ERROR_ROAM_KICKOUT		= 749,	//漫游状态错误，踢掉
	ERROR_ROAM_SPECIAL_CMD		= 750,	//通知GS下线，且不存盘
	ERROR_ROAM_SAVE_STATUS		= 751,	//漫游角色正在下线
	ERROR_ROAM_NETWORK		= 752,	//网络错误，可能需要重新存盘
	ERROR_ROAM_STATUS		= 753,	//玩家正在漫游状态
	ERROR_WRONG_DST_ZONE		= 754,	//选择了错误的服务器
	ERROR_WRONG_HUB_PROTOCOL	= 755,	//错误的跨服协议
	ERROR_ROLEID_STATUS		= 756,	//玩家数据错误
	ERROR_HUB_TIMEOUT		= 757,	//跨服转发数据超时
	ERROR_ROAM_RECONNECT		= 758,	//跨服连接断开重连
	ERROR_ROAM_OTHER_ROLE		= 759,	//登陆了其他角色
	ERROR_QUEUE_NOT_READY		= 760,	//排队尚未完成
	ERROR_ROAM_MULTILOGIN       	= 761,  //跨服角色重复登录

	ERROR_NIGHT_REWARD_END		= 765,	//晚间领奖活动已经结束
	ERROR_NIGHT_REWARD_EMPTY_POOL	= 766,	//晚间领奖，奖池为空
	ERROR_NIGHT_REWARD_TIME_LIMIT	= 767,	//超过领奖时间
	ERROR_NIGHT_REWARD_LIMIT	= 768,	//重复领奖
	ERROR_NIGHT_REWARD_EMPTY_REWARD	= 769,	//奖品被领光

	ERROR_ROAM_SAVE_ERROR       = 770,
	ERROR_ROLE_UNCOMP_ERROR     = 769,

	ERROR_TIGUAN_ALREADY		= 780,	//今天已经踢过了
	ERROR_TIGUAN_ING		= 781,	//目标帮派正在被踢中
	ERROR_TIGUAN_NOT_KING		= 782,	//目标帮派非龙头
	ERROR_TIGUAN_NOT_FULL		= 783,	//目标城市总舵未满
	ERROR_TIGUAN_CANT_ENTER		= 784,	//目标基地正在踢馆，无法进入
	ERROR_TIGUAN_SRCNOMAIN		= 785,	//踢馆方在源势力地图没有总舵
	ERROR_TIGUANED_SRCNOMAIN	= 786,	//被踢馆方在源势力地图没有总舵
	ERROR_TIGUAN_DSTNOSUB		= 787,	//踢馆方在目的势力地图没有分舵
	ERROR_TIGUANED_DSTNOSUB		= 788,	//被踢馆方在目的势力地图没有分舵
	ERROR_TIGUAN_SUB_NOTNEARCITY	= 789,	//踢馆方势力地图同申请通行的势力地图不接壤
	ERROR_TIGUANED_SUB_DSTNOTKING	= 790,	//被踢馆方不是申请通行的势力地图的龙头
	ERROR_TIZI_HASEXIST		= 791,	//涂鸦板上已经有内容
	ERROR_TIZI_NOTEXIST		= 792,	//涂鸦板上没有内容
	ERROR_FACTION_TIZINOTTIGUAN	= 793,	//贵帮不是踢馆发起者，不能题字
	ERROR_TIGUANED_NODST		= 794,	//被踢馆方的总舵无处可搬了
	ERROR_FACTION_TIZICOOLDOWN	= 795,	//题字冷却
	ERROR_REPUTATION_TRANSFER_TARGET_OFFLINE	= 796,	// 转移声望目标不在线
	ERROR_REPUTATION_TRANSFER_MAX	= 797,	// 转移声望超过上限
	ERROR_REPUTATION_TRANSFER_WRONG	= 798,	// 转移声望参数错误
	ERROR_REPUTATION_TRANSFER_NOT_ENOUGH	= 799,	// 转移声望不足

	ERROR_MATCH_DISABLE             = 811,  //匹配功能已关闭
	ERROR_MATCH_ING                 = 812,  //[队伍有]玩家已经在匹配中了
	ERROR_MATCH_NO_TEAM             = 813,  //组队匹配但没有队伍
	ERROR_MATCH_NOT_TEAM_LEADER     = 814,  //组队匹配需要队长发起
	ERROR_MATCH_WRONG_POS           = 815,  //必须在 大世界+安全区 才能匹配
	ERROR_MATCH_CANT_ENTER          = 816,  //不满足进副本的条件
	ERROR_MATCH_OFFLINE             = 817,  //队伍中有玩家不在线
	ERROR_MATCH_TEAM_PROF           = 818,  //队伍中已有重复职业
	ERROR_MATCH_FULL_TEAM           = 819,  //已满员队伍就别来搀和了

	ERROR_ADD_CASH_REPEAT		= 831,  //重复的AddCash命令
	ERROR_MUCH_PENDING_ORDER	= 832,  //未完成充值太多

	ERROR_USER_NEED_ACTIVE		= 843,  //帐号需要激活

	ERROR_INVALID_COMMAND		= 850,	//客户端请求不合法
	ERROR_PRIVILEGE			= 851,	//权限不符
	ERROR_PEER_CONDITION		= 852,	//对方条件不满足
	ERROR_MONEY_NOT_ENOUGH		= 853,	//钱不够
	ERROR_PEER_OFFLINE		= 854,	//对方不在线
	ERROR_APPLY_ALREADY		= 855,	//已经申请
	ERROR_IN_COOLDOWN_PEER		= 856,	//对方冷却
	ERROR_NATION_IN_WAR		= 857,	//指定地图正在国战中，无关国家不能进入
	ERROR_DIFF_NATION		= 858,	//国家不同
	ERROR_AUCTION_FULL		= 859,	//拍卖数量带到上限
	ERROR_AUCTION_MAILBOX_FULL = 860,	//拍卖行邮箱已满
	ERROR_AUCTION_INVALID_ID	= 861,	//拍卖行ID不存在
	ERROR_AUCTION_VERIFY		= 862,	//拍卖行正在审核中
	ERROR_AUCTION_INVALID_COUNT	= 863,	//拍卖行数量不足
	ERROR_AUCTION_TRIGGER_VERIFY = 864,	//拍卖行购买成功但是触发审核

	ERROR_COMMON_TIMEOUT		= 865,	//操作超时

	ERROR_MERGE_CORPS_REQ_CANCEL_CORPS_ID	= 867,	//取消请求，没有找到对应的
	ERROR_MERGE_CORPS_REQ_DUP	= 868,	//重复的合并社团请求
	ERROR_MERGE_CORPS_REQ_NEED_PROC	= 869,	//有没有处理的合并申请
	ERROR_MERGE_CORPS_SELF_ACTIVITY = 870,	//本帮活跃度不达标，不能合帮
	ERROR_MERGE_CORPS_DST_ACTIVITY 	= 871,	//目标帮活跃度不达标，不能合帮
	ERROR_MERGE_CORPS_REQ_LIMIT	= 872,	//合帮请求过多
	ERROR_MERGE_CORPS_SELF_IN_MERGE	= 873,	//已经同意和其他帮派合帮
	ERROR_ALIANCE_COOLDOWN		= 874,	//结盟操作冷却
	ERROR_DIVORCE_NEED_COST         = 875,  //单方强制离婚
	ERROR_CHARIOST_CAMP_LV_NEED     = 876,  //打造/升级战车，战车营等级不足
	ERROR_CHARIOST_MONEY_NOT_ENOUGH = 877,  //升级战车帮会资金不足
	ERROR_CHARIOST_NOT_FIND		= 878,  //没有找到要升级的战车
	ERROR_RENT_CHARIOT_FAILD        = 879,  //领取战车失败
	ERROR_CHARIOT_COUNT_ENOUGH      = 880,  //战车数量已达上限

	ERROR_MERGE_CORPS_DST_IN_MERGE	= 881,	//目标已经同意和其他帮派合帮
	ERROR_MERGE_CORPS_DUP_REQ	= 882,	//重复的合帮请求
	ERROR_MERGE_CORPS_CAPACITY	= 883,	//合并方帮派容量不足
	ERROR_MERGE_CORPS_REQ_TIMEOUT	= 884,	//合并请求超时
	ERROR_MERGE_CORPS_REQ_REJECT	= 885,	//合并请求被拒绝
	ERROR_MERGE_CORPS_REQ_CANCEL	= 886,	//合并请求被取消
	ERROR_MERGE_CORPS_IN_PROTECT	= 887,	//在帮派保护时间，不能合帮
	ERROR_MERGE_CORPS_DST_IN_PROTECT = 888,	//目标帮派在帮派保护时间，不能合帮

	ERROR_CORPS_CREATE_RESERVED_NAME	= 898,	//创建帮派时使用了被占用的名字
	ERROR_SETNAME_RESERVED_NAME		= 899,	//起名时使用了被占用的名字

	ERROR_PLATFORM_INVALID_PLAYER	= 900,	//组队平台非法玩家
	ERROR_PLATFORM_REGISTER_DUP	= 901,	//重复注册组队平台
	ERROR_PLATFORM_INVALID_REQUEST	= 902,	//组队平台非法的
	ERROR_PLATFORM_ALREADY_WAITING	= 903,	//队员已经进入确认状态
	ERROR_PLATFORM_FULL		= 904,	//组队平台已经满了
	ERROR_PLATFORM_INVALID_LEVEL	= 905,	//组队平台等级错误
	ERROR_PLATFORM_INVALID_TEAM	= 906,	//组队平台错误的队伍
	ERROR_PLATFORM_FORBID		= 907,	//禁止排队
	ERROR_PLATFORM_TEAMMEMBER_OFFLINE	= 908,	//有队员离线
	ERROR_PLATFORM_TASK_FINISH_COUNT = 909,	//组队平台任务达到上限
	ERROR_PLATFORM_INSTANCE_FINISH_COUNT	= 910,	//组队平台副本达到上限
	ERROR_PLATFORM_PLAYER_TOO_MORE	= 911,	//组队平台队伍人数过多
	ERROR_PLATFORM_OCCUPATION	= 912,	//组队职业出错
	ERROR_PLATFORM_TIMEOUT		= 913,	//组队平台超时
	ERROR_PLATFORM_TEAM_NOTFOUNT	= 914,	//加入组队平台指定队伍无法找到该队伍
	ERROR_PLATFORM_TEAM_CANNOT_JOIN	= 915,	//无法加入指定队伍
	ERROR_PLATFORM_TEAM_LEADER	= 916,	//不是队长，无法进行组队平台操作
	ERROR_PLATFORM_INSTANCE_PRE_TASK = 917,	//组队平台副本前置任务未完成
	ERROR_PLATFORM_SCENE_LIMIT	= 918,	//当前场景禁止排组队平台
	ERROR_PLATFORM_INVALID_TEAM_TYPE = 919,	//队伍类型和组队平台队伍类型不一致

	ERROR_DISTRIBUTE_CENTER     = 920,  //跨服中心服不可用
	ERROR_DISTRIBUTE_FUNCTION   = 921,  //跨服中心服
	ERROR_ARENA_EXCEPTION_RESET		= 928,	//竞技场出现异常重置
	ERROR_ARENA_EXCEPTION_WIN		= 929,	//竞技场对面异常引起的直接获胜
	ERROR_ARENA_ACTIVITY_NOT_BEGIN = 930,	//竞技场活动没有开启
	ERROR_ARENA_ALREADY_APPLY	= 931,		//竞技场已经报名
	ERROR_ARENA_LEVEL_SECTION_WRONG	= 932,		//竞技场错误的等级段
	ERROR_ARENA_NOT_MATCHED		= 933,		//竞技场没有匹配上
	ERROR_ARENA_NOT_TEAM_LEADER		= 934,		//竞技场不是队长
	ERROR_ARENA_MEMBER_NOT_AROUND = 935,		//竞技场有队员没在附近
	ERROR_ARENA_MEMBER_LEVEL_NOT_FIT = 936,		//竞技场有队员等级不匹配
	ERROR_ARENA_MEMBER_STATE_WRONG = 937,		//竞技场有队员不能匹配
	ERROR_ARENA_CANT_QUIT 		= 938,		//竞技场处于不能退出的状态
	ERROR_ARENA_TOTAL_COUNT_MAX 	= 939,		//竞技场参与次数达到上限
	ERROR_ARENA_NOT_IN_APPLY_SCENE 	= 940,		//竞技场没有处于报名场景
	ERROR_ARENA_TEAM_MEMBER_MAX_LIMIT 	= 941,		//竞技场组队报名人数达到上限
	ERROR_ARENA_BE_PUNISHED			= 942,		//竞技场处于惩罚时间
	ERROR_ARENA_DUP_PROFESSION		= 943,		//有重复的职业

	ERROR_IFRIEND_LEVEL_LIMIT  	= 945,  	//等级不满足，不能申请情缘
	ERROR_IFRIEND_BOTH_INTEAM_NEED  = 946,  	//需要两人都在队中
	ERROR_IFRIEND_FULL              = 947,  	//情缘好友人数到达上限
	ERROR_IFRIEND_DST_FULL          = 948,  	//对方情缘好友人数到达上限
	ERROR_IFRIEND_NOTFOUND          = 949,  	//没有找到情缘好友
	ERROR_IFRIEND_NOTFRIEND         = 950,  	//不是好友 不能申请 情缘好友
	ERROR_IFRIEND_ALREADY           = 951,  	//已经是情缘好友
	ERROR_IFRIEND_REFUSED           = 952,  	//对方拒绝成为情缘好友
	ERROR_IFRIEND_AMITY_LIMIT       = 953,  	//亲密度不足
	ERROR_IFRIEND_INTEAM_NEED       = 954,  	//需组队申请
	ERROR_IFRIEND_TEAM_LEADER_NEED  = 955,  	//需队长申请
	ERROR_IFRIEND_REQ_SEND          = 956,  	//添加情缘好友请求已发送，等待对方回应

	ERROR_PLAYER_CONTEST_GRAPH_FINISHED = 957,	//个人答题图片答题已经完成
	ERROR_PLAYER_CONTEST_TEST_SUNDAY_FINISHED = 958,	//个人答题周日文字答题已经完成
	ERROR_PLAYER_CONTEST_CONFILICT	= 959,		//个人答题发生冲突
	ERROR_PLAYER_CONTEST_NOT_BEGIN	= 960,		//个人答题没有开始
	ERROR_PLAYER_CONTEST_NO_FACTION = 961,		//没有帮派
	ERROR_PLAYER_CONTEST_HELP_LIMIT = 962,		//求助达到上限
	ERROR_LEAVEMSG_NOT_FRIEND = 963,          	//非好友不能发送离线消息
	ERROR_LEAVEMSG_NOT_ITEM = 964,                //离线消息不能发送道具
	ERROR_ROAM_NOT_FRIEND = 965,				//非好友不能 跨服聊天
	ERROR_PLAYER_CONTEST_WRONG_LEVEL = 966,		//个人答题等级不足
	ERROR_PLAYER_CONTEST_CAMP_CLOSE	= 967,		//个人答题对应活动没有开启
	ERROR_PLAYER_CONTEST_TEST_EVERYDAY_FINISHED = 968,	//个人答题每日文字答题已经完成
	ERROR_PLAYER_CONTEST_TEST_SATURDAY_FINISHED = 969,	//个人答题周六文字答题已经完成

	ERROR_CORPS_BATTLE_ALREADY_IN_BATTLE = 970,	//帮派竞赛已经处于战场中了
	ERROR_CORPS_BATTLE_LEVEL_LOW	= 971,	//帮派竞赛参与等级不够
	ERROR_CORPS_BATTLE_NOT_MATCHED	= 972,	//帮派竞赛没有匹配上
	ERROR_CORPS_BATTLE_NOT_CREATE_BATTLE = 973,	//帮派竞赛战场没有开启
	ERROR_CORPS_BATTLE_FULL	= 974,		//帮派竞赛战场人数已经满了
	ERROR_CORPS_BATTLE_CANT_JOIN_BATTLE = 975,	//实习生、休假成员无法参加该活动
	ERROR_CORPS_BATTLE_JOIN_TIME_LIMIT	= 976,	//进入次数达到上限
	ERROR_CORPS_BATTLE_NOT_BEGIN		= 977,	//社团战场没有开启
	ERROR_CORPS_BATTLE_PUNISH		= 978,	//玩家处于惩罚时间
	ERROR_CORPS_BATTLE_DAY_LIMIE	 	= 979,	//加入社团天数小于2天，无法参加该活动

	ERROR_CORPS_CITY_BATTLE_STAR_CHANGED = 980,	//这条模拟战星级发生了变化
	ERROR_CORPS_CITY_BATTLE_MAX_TIMES = 981,	//社团模拟战参与次数达到上限
	ERROR_CORPS_CITY_BATTLE_IN_CHALLENGE = 982,	//社团模拟战正在被挑战
	ERROR_CORPS_CITY_BATTLE_CREATE_INSTANCE_FAILED = 983,	//社团模拟战开启副本失败
	ERROR_CORPS_CITY_BATTLE_NOT_BEGIN = 984,	//社团模拟城战没有处于战场开启阶段
	ERROR_CORPS_CITY_BATTLE_DIFFCULT_DUMP_CHALLENGE	= 985,	//社团模拟战，苦难模式重复挑战
	ERROR_CORPS_BATTLE_IN_CLOSING		= 986,	//战场处于关闭中，禁止进入
	ERROR_CORPS_BATTLE_IN_CORPS_BASE	= 987,	//社团基地，不得进入战场

	ERROR_NOT_MARRIED = 989,			//没有结婚
	ERROR_RED_ENVELOPE_NET_FOUND = 990, //找不到红包
	ERROR_RED_ENVELOPE_EMPTY = 991, //红包已经领完了
	ERROR_RED_ENVELOPE_UNKNOWN = 992,   //红包未知错误
	ERROR_RED_ENVELOPE_MONEY_FULL = 993,    //身上钱已经满了
	ERROR_RED_ENVELOPE_NO_RIGHT = 994,  //没有操作红包权限
	ERROR_RED_ENVELOPE_ALLOC = 995, //创建红包ID失败
	ERROR_RED_ENVELOPE_SEND_SELF = 996, //不能发红包给自己
	ERROR_RED_ENVELOPE_ALREADY_DRAW = 997,  //玩家已经领过红包了
	ERROR_RED_ENVELOPE_REACH_LIMIT = 998,   //玩家达到领取上限了

	ERROR_SCENE_FORBIDDEN_JOIN_TEAM = 999, //当前地图禁止组队

	ERROR_CENTER_BATTLE_ROAM_PLAYER = 1001,  //跨服玩家不能使用中心战场
	ERROR_CENTER_BATTLE_PUNISHED = 1002,     //玩家处于被惩罚状态
	ERROR_CENTER_BATTLE_ALREADY_MATCHED = 1003,  //已经处于战场匹配状态
	ERROR_CENTER_BATTLE_DUPLICATE_APPLY = 1004,  //战场重复报名
	ERROR_CENTER_BATTLE_NO_ENTRY = 1005, //找不到玩家报名信息
	ERROR_CENTER_BATTLE_STAT_WRONG = 1006,   //玩家状态错误
	ERROR_CENTER_BATTLE_NO_ACTIVITY = 1007,  //活动没有开启
	ERROR_CENTER_BATTLE_NOT_APPLY   = 1008,  //活动没有报名
	ERROR_CENTER_BATTLE_SERVER_FULL	= 1009,	//中心服务器已经满了
	ERROR_CENTER_BATTLE_SERVER_LEVEL_NOT_ENOUGH = 1010,	//服务器等级不够
	ERROR_CENTER_BATTLE_CORPS_BATTLE_NOT_ENOUGH = 1011,	//帮派等级不够
	ERROR_CENTER_BATTLE_CORPS_ACTIVITY_NOT_ENOUGH = 1012,	//帮派活跃度不够
	ERROR_CENTER_BATTLE_CORPS_NOT_CREATE = 1013,	//战斗没有开启
	ERROR_CENTER_BATTLE_TEAM_ZERO_FACTION = 1014,	//没有分阵营前不能创建队伍
	ERROR_CENTER_BATTLE_TEAM_DIFF_FACTION = 1015,	//不同的阵营不能组队

	ERROR_CORPS_GAG_LESS_ACTIVITY 		= 1030,	//社团社长禁言，活跃度不足
	ERROR_CORPS_GAG_TARGET_ALREADY_GAGED	= 1031,	//社团禁言，对方已经禁言
	ERROR_CORPS_GAG_TARGET_NOT_GAGED	= 1032,	//社团解除禁言，对方不在禁言状态
	ERROR_CORPS_GAG_TARGET_NOT_IN_CORPS	= 1033,	//社团禁言，对方不是社团成员
	ERROR_CORPS_CHAT_IN_GAG			= 1034,	//社团发言，禁言中
	ERROR_CORPS_TARGET_RECEIVE_TASK_TIME_LIMIT		= 1035,	// 社团目标领取任务时间限制
	ERROR_CORPS_TARGET_NO_TASK						= 1036,	// 社团目标无此任务

	ERROR_CORPS_PLAYER_NO_CORPS							= 1040,	//玩家没有社团
	ERROR_CORPS_BATTLE2_SUB_BATTLE_FULL					= 1041,	//帮派竞赛2子战场已经满了
	ERROR_CORPS_BATTLE2_SAME_SUB_BATTLE_TYPE			= 1042,	//帮派竞赛2一样的子战场类型
	ERROR_CORPS_BATTLE2_ERR_SUB_BATTLE_TYPE				= 1043,	//帮派竞赛2错误的子战场类型
	ERROR_CORPS_BATTLE2_SELECT_SUB_BATTLE_TYPE_ERR_TIME	= 1044,	//帮派竞赛2错误的时间不能选择子战场
	ERROR_CORPS_BATTLE_COMMON_ERR						= 1045,	//帮派竞赛通用错误
	ERROR_CORPS_BATTLE_ENTER_BATTLE_ERR_TIME			= 1046,	//帮派竞赛错误的时间无法进入战场

	ERROR_NEW_SECT_NONE_NODE		= 1050,	//新师徒系统，没有找到对应的玩家
	ERROR_NEW_SECT_NOT_MASTER		= 1051,	//新师徒系统，不是师徒关系
	ERROR_NEW_SECT_INVALID_HOMEWORK		= 1052,	//新师徒系统，作业不合法
	ERROR_NEW_SECT_DISCIPLE_LEVEL		= 1053,	//新师徒系统，徒弟等级不符合
	ERROR_NEW_SECT_NOT_IN_TEAM		= 1054,	//新师徒系统，双方没有在同一队伍内
	ERROR_NEW_SECT_WRONG_TARGET		= 1055,	//新师徒系统，错误的目标
	ERROR_NEW_SECT_BREAK_COOLDOWN		= 1056,	//新师徒系统，逐出师门/判师冷却中
	ERROR_NEW_SECT_HAS_MASTER		= 1057,	//新师徒系统，已经有师傅了
	ERROR_NEW_SECT_DISCIPLE_FULL		= 1058,	//新师徒系统，徒弟人数已经满
	ERROR_NEW_SECT_ALREADY_GRADUATED	= 1059,	//新师徒系统，已经出师
	ERROR_NEW_SECT_LESS_TIME_GRADUATE	= 1060,	//新师徒系统，拜师时间太短，无法出师
	ERROR_NEW_SECT_WRONG_TIME		= 1061,	//
	ERROR_NEW_SECT_NOT_GRADUATED		= 1062,	//

	ERROR_TOWNLET_NOT_CREATE		= 1070,	// 小镇还没有创建
	ERROR_TOWNLET_CHAT_ERROR		= 1071,	// 小镇聊天错误

	ERROR_RUNE_NEW_COMMON           = 1080,         // 新符文通用错误
	ERROR_RUNE_NEW_HAS_UNLOCK       = 1081,         // 新符文已经解锁
	ERROR_RUNE_NEW_ERR_CONTINUE		= 1082,         // 新符文解锁类型继续

	ERROR_RUNE_NEW_WORD_COMMON		= 1085,         // 新符文之语通用错误
	ERROR_RUNE_NEW_WORD_NOT_UNLOCK	= 1086,         // 新符文之语有符文未解锁
	ERROR_RUNE_NEW_WORD_ERR_INFO	= 1087,         // 新符文之语错误的符文
	ERROR_RUNE_NEW_WORD_NO_WORD		= 1088,         // 新符文之语没有对应的符文之语
	ERROR_RUNE_NEW_WORD_ALREADY		= 1089,         // 已经激活过

	ERROR_RENAME_PLAYER_NOT_FOUND 	= 1100,			// 改名时玩家不在线
	ERROR_RENAME_PLAYER_NOT_READY 	= 1101,			// 改名时玩家数据尚未准备好
	ERROR_RENAME_NO_CHANCE			= 1102,			// 改名次数已经用完
	ERROR_RENAME_CD					= 1103,			// 改名CD中
	ERROR_RENAME_INVALID_NAME		= 1104,			// 改名时使用的名字不合法
	ERROR_RENAME_INVALID_ROLE		= 1105,			// 非法角色改名
	ERROR_RENAME_FAILED				= 1106,			// 改名一般错误
	ERROR_RENAME_DUPLICATE_NAME 	= 1107,			// 改名新名字重复使用
	ERROR_RENAME_ACTIVITY_CLOSED 	= 1108,			// 改名活动未开启
	ERROR_FRIEND_DST_CREDIT_LOW    = 1109,   // 对方信用值过低 不能加好友

	ERROR_FORBID_STRANGER_CHAT	= 1110,		//禁止陌生人私聊
	ERROR_CHAT_PRIVATE_LEVEL_LIMIT	= 1111,		//等级不足禁止私聊

	ERROR_CENTER_ARENA_NO_TEAM = 1117,  //中心服组队竞技场没有组队
	ERROR_CENTER_ARENA_NOT_LEADER = 1118,   //中心服组队竞技场不是队长
	ERROR_CENTER_ARENA_NOT_ENOUGH_MEMBER = 1119,    //中心服组队竞技场人数不符
	ERROR_CENTER_ARENA_MEMBER_NOT_AROUND = 1120,    //中心服组队竞技场队友不在附近
	ERROR_CENTER_ARENA_NOT_IN_ONE_GROUP = 1121,     //不是一个战队的
	ERROR_CENTER_ARENA_HEAL_TOO_MUCH = 1122,    //中心服组队竞技场治疗职业过多
	ERROR_CENTER_ARENA_PROF_10_LIMIT = 1123,	// 中心服组队竞技场职业10限制
	ERROR_CENTER_ARENA_PROF_12_LIMIT = 1124,	// 中心服组队竞技场职业12限制
	ERROR_ARENA_GROUP_INVITE_COOLDOWN = 1128,	//战队邀请冷却
	ERROR_ARENA_GROUP_NO_ARENA_ZONE	= 1129,	//服务器没有战区
	ERROR_ARENA_GROUP_BAD_NAME = 1130,  //竞技场战队名字不合法
	ERROR_ARENA_GROUP_DUP_NAME = 1131,  //竞技场战队名字重复
	ERROR_ARENA_GROUP_MONEY_NOT_ENOUGH = 1132,  //竞技场战队创建的钱不够
	ERROR_ARENA_GROUP_MAX_COUNT = 1133, //竞技场战队数量已经达到上限
	ERROR_ARENA_GROUP_ALREADY_IN = 1134,    //已经处于战队中了
	ERROR_ARENA_GROUP_INVITE_TIMEOUT = 1135,    //战队邀请超时
	ERROR_ARENA_GROUP_MEMBER_MAX_COUNT = 1136,  //战队成员已经满了
	ERROR_ARENA_GROUP_LEADER_CANT_QUIT = 1137,  //战队队长不能退出
	ERROR_ARENA_GROUP_NOT_IN_GROUP = 1138,  //没有处于战队中
	ERROR_ARENA_GROUP_NO_RIGHT = 1139,  //战队操作没有权限
	ERROR_ARENA_GROUP_PLAYER_LEVEL_WRONG = 1140,    //等级太低
	ERROR_ARENA_GROUP_PLAYER_NOT_ONLINE = 1141, //对方不在线
	ERROR_ARENA_GROUP_TARGET_ALREADY_IN = 1142,	//对方已经处于战队中了
	ERROR_ARENA_GROUP_INVITE_REFUSED = 1143,	//拒绝加入战队

	ERROR_MARRIAGE_VOWS_IN_COOLDOWN		= 1144,	//婚礼宣誓冷却中
	ERROR_FRIEND_BLESS_NOT_FRIEND		= 1145,	//非好友不能点赞祝福
	ERROR_FRIEND_BLESS_LIMIT		= 1146,	//已经点赞，无法重复

	ERROR_CORPS_RACE_GUESS_CLOSED		= 1147, //下注功能关闭
	ERROR_CORPS_RACE_BOOS_CLOSED		= 1148, //倒彩功能关闭
	ERROR_CORPS_RACE_CHEER_CLOSED		= 1149, //喝彩功能关闭
	ERROR_CORPS_RACE_CHEER_COOLDOWN		= 1150,	//喝彩冷却中
	ERROR_CORPS_RACE_BOOS_COOLDOWN		= 1151,	//倒彩冷却中
	ERROR_CORPS_RACE_NOT_BEGIN		= 1152,	//比赛未开始
	ERROR_CORPS_RACE_CHEER_WRONG_TARGET	= 1153,	//为错误的目标喝彩
	ERROR_CORPS_RACE_BOOS_WRONG_TARGET	= 1154,	//为错误的目标喝倒彩
	ERROR_CORPS_RACE_WRONG_PARAM		= 1155,	//错误的参数
	ERROR_CORPS_RACE_CAMPAIGN_CLOSE		= 1156,	//帮派赛跑对应活动没有开启
	ERROR_CORPS_RACE_OPEN_DUMP		= 1157,	//已经开启过帮派赛跑
	ERROR_CORPS_RACE_WRONG_BET_TYPE		= 1158,	//错误的下注的类型
	ERROR_CORPS_RACE_BET_MONEY		= 1159,	//下注钱不够
	ERROR_CORPS_RACE_BET_DUMP		= 1160,	//重复下注

	ERROR_INVALID_PROF		= 1161,	//不可用的职业
	ERROR_SOCIAL_SPACE_TIMEOUT		= 1162,

	ERROR_CORPS_RACE_AWARD_CLOSED	= 1163, //领奖功能关闭

	ERROR_SEND_MSG_FAIL     = 1165, //发送数据失败
	ERROR_INVALIDE_SUB_STATUS   = 1166, //订阅状态非法
	ERROR_CREATE_REMOTE_INSTANCE_TIMEOUT = 1167, //远程创建副本超时
	ERROR_CREATE_DIS_INSTANCE_SUCCESS = 1168,//远程创建副本成功
	ERROR_CREATE_DIS_INSTANCE_SUCCESS_RE = 1169,//远程创建副本成功ACK
	ERROR_TOP_ALREADY_EXIST			= 1170,	//主题已存在
	ERROR_INVALIDE_TOPIC        = 1171, //非法的主题
	ERROR_INVALIDE_MSG_TYPE     = 1172, //错误的数据类型
	ERROR_TOP_NOT_EXIST     = 1173, //主题不存在
	ERROR_WRONG_DS          = 1174, //错误的ds
	ERROR_INVALID_SHARE_MEMORY	= 1175,	//共享内存没有初始化
	ERROR_TEAM_COUNT_MAX	= 1176,	//队伍数量达到上限

	ERR_FRIEND_GROUP_CHANGE_INVALID_CHANGE_TYPE		= 1178,			// 无效的更改类型
	ERR_FRIEND_GROUP_CHANGE_GROUP_COUNT_LIMIT		= 1179,			// 分组数量达到上限
	ERR_FRIEND_GROUP_CHANGE_GROUP_NAME_LIMIT		= 1180,			// 分组名最多6个汉字
	ERR_FRIEND_GROUP_CHANGE_INVALID_GROUP_ID		= 1181,			// 无效的分组id
	ERR_FRIEND_GROUP_CHANGE_INVALID_GROUP_NAME		= 1182,			// 无效的分组名
	ERR_FRIEND_GROUP_CHANGE_INVALID_SORT_GROUP_IDS		= 1183,			// 无效的分组排序ids
	ERR_ALREADY_FRIEND					= 1184,			// 已经是好友了
	ERR_FRIEND_GROUP_CHANGE_GROUP_NAME_INVALID		= 1185,			// 分组名含有非法字符

	ERROR_GROUP_NOT_FOUND		= 1186,         //无法找到要操作的群组
	ERROR_GROUP_CREATE_LIMIT	= 1187,         //创建的群太多
	ERROR_GROUP_JOIN_LIMIT		= 1188,         //加入的群太多
	ERROR_GROUP_DST_JOIN_LIMIT	= 1189,         //对方加入的群太多
	ERROR_GROUP_PERMISSION		= 1190,		//群操作权限不足
	ERROR_GROUP_MEMBER_OFFLINE	= 1191,		//群操作玩家不在线
	ERROR_GROUP_TARGET_NOT_FRIEND	= 1192,		//群操作对方不是你的好友
	ERROR_GROUP_MEMBER_CAN_NOT_JOIN	= 1193,		//群操作玩家无法加入
	ERROR_GROUP_CAPACITY_NOT_ENOUGH	= 1194,		//群空间不足，无法添加更多的人

	ERROR_SETNAME_INVALID_NAME			= 1195,	//起名时名字非法
	ERROR_SETNAME_HAS_NAME				= 1196,	//起名时已经有名字了
	ERROR_SETNAME_DUPLICATE_NAME			= 1197, //起名时名字重复使用
	ERROR_SETNAME_FAILED				= 1198, //起名一般错误
	ERROR_SETNAME_COOLDOWN				= 1199, //起名冷却中
	ERROR_SETNAME_COND				= 1200, //起名条件不满足

	ERROR_CORPS_SET_BADGE_COOLDOWN			= 1201,	//修改社团徽章冷却中
	ERROR_CORPS_SET_BADGE_LESS_MONEY		= 1202,	//修改社团徽章帮派资金不足
	ERROR_CORPS_SET_BADGE_WRONG_BADGE		= 1203,	//修改社团徽章错误的数据
	ERROR_CORPS_SET_SUPPORT_SIDE_COOLDOWN		= 1204,	//修改社团拥护组织冷却中
	ERROR_CORPS_SET_SUPPORT_SIDE_LESS_MONEY		= 1205,	//修改社团拥护组织社团资金不足
	ERROR_CORPS_SET_SUPPORT_SIDE_WRONG		= 1206,	//修改社团拥护组织错误的数据
	ERROR_CORPS_RECRUITMENT_SIZE			= 1207,	//社团招聘宣言设置出错
	ERROR_CORPS_SET_POS_TYPE_LEVEL			= 1208,	//社团自定义职务，社团等级不足
	ERROR_CORPS_SET_POS_TYPE_POS			= 1209,	//社团自定义职务，权限不足
	ERROR_CORPS_SET_POS_TYPE_COOLDOWN		= 1210,	//社团自定义职务，冷却中
	ERROR_CORPS_SET_POS_TYPE_WRONG_TYPE		= 1211,	//社团自定义职务，错误的类型
	ERROR_CORPS_GET_MANAGER_BONUS_MASTER_LIMIT	= 1212,	//社团管理工资会长领取次数达到上线
	ERROR_CORPS_GET_MANAGER_BONUS_VICE_MASTER_LIMIT	= 1213,	//社团管理工资副会长领取次数达到上线
	ERROR_CORPS_GET_MANAGER_BONUS_NORMAL_LIMIT	= 1214,	//社团管理工资非会长不得领取
	ERROR_CORPS_INVALID_BADGE			= 1215,	//社团无法使用该徽章
	ERROR_SECRET_LOVE_LEVEL_LIMIT			= 1216,	//等级不足，无法暗恋
	ERROR_SECRET_LOVE_COUNT_LIMIT			= 1217,	//只能同时暗恋一个人
	ERROR_SECRET_LOVE_MARRY_LIMIT			= 1218,	//已经结婚，无法暗恋
	ERROR_SECRET_LOVE_GENDER_LIMIT			= 1219,	//不能同性恋
	ERROR_SECRET_LOVE_COOLDOWN_LIMIT		= 1220,	//冷却中，不能频繁暗恋
	ERROR_SECRET_LOVE_NO_LOVER			= 1221,	//没有暗恋对象
	ERROR_SECRET_LOVE_MSG_COOLDOWN_LIMIT		= 1222,	//暗恋消息冷却中，不能频繁发送暗恋消息

	ERROR_GFRIEND_NOT_TEAMFOLLOW            = 1223, //不在组队跟随状态
	ERROR_GFRIEND_NAME_WRONG                = 1224, //名字长度不对
	ERROR_GFRIEND_DELETE_TIME_WRONG         = 1225, //强制删除羁绊对方不满足离线条件
	ERROR_GFRIEND_AMITY_LOW                 = 1226, //好友度不够
	ERROR_GFRIEND_FULL                      = 1227,  //情缘好友人数到达上限
	ERROR_GFRIEND_DST_FULL                  = 1228,  //对方情缘好友人数到达上限
	ERROR_GFRIEND_NOTFOUND                  = 1229,  //没有找到情缘好友
	ERROR_GFRIEND_NOT_FRIEND                = 1230,  //不是好友 不能申请 情缘好友
	ERROR_GFRIEND_ALREADY                   = 1231,  //已经是情缘好友
	ERROR_GFRIEND_TEAM_LEADER_NEED          = 1232,  //需队长申请
	ERROR_GFRIEND_LEVEL_LIMIT               = 1233,  //等级不满足，不能申请金鱼情缘
	ERROR_GFRIEND_BOTH_INTEAM_NEED          = 1234,  //需要两人都在队中
	ERROR_GFRIEND_RED_LEVEL                 = 1235, //金兰发送全服红包错误
	ERROR_GFRIEND_REQ_SEND                  = 1236, //添加金兰请求已发送，等待对方回应
	ERROR_GFRIEND_REQ_FAILED                = 1237, //对方拒绝金兰结义
	ERROR_GFRIEND_APPLY_COUNT_LIMIT         = 1238, //结义次数限制
	ERROR_GFRIEND_APPLY_COUNT_DST_LIMIT     = 1239, //对方金兰结义次数限制
	ERROR_GFRIEND_DST_AMITY_LOW             = 1240, //对方好友度不够
	ERROR_FUNC_CLOSED_TEMPORARY		= 1241, //功能暂未开放
	ERROR_GFRIEND_DELETE_REQ_FAILED		= 1242, //对方拒绝解除羁绊请求
	ERROR_GFRIEND_CHANGENAME_REQ_FAILED	= 1243,	//对方拒绝改羁绊名字请求
	ERROR_CORPS_RECRUITMENT_COOLDOWN        = 1244, //社团设置招聘冷却中
	ERROR_CORPS_RECRUITMENT_ACTIVITY        = 1245, //社团设置招聘活力不足
	ERROR_CORPS_RECRUITMENT_URL_NULL        = 1246, //社团设置招聘没有图片url
	ERROR_INTIMATE_PARTY_APPLY              = 1247, //已经有人在申请派对
	ERROR_INTIMATE_PARTY_BEGIN              = 1248, //派对已经开始
	ERROR_INTIMATE_PARTY_REQ_FAILED      	= 1249,	//对方拒绝羁绊派对请求


	ERROR_CORPS_INVALID_RECRUITMENT			= 1250,	//非法的社团招募宣言
	ERROR_CORPS_INVALID_ANNOUNCE			= 1251,	//非法的社团Announce
	ERROR_CORPS_INVALID_MANIFESTO			= 1252,	//非法的社团Manifesto
	ERROR_GROUP_INVALID_NAME			= 1253,	//非法的群聊名字
	ERROR_GROUP_INVALID_MANIFESTPO			= 1254,	//非法的群聊宣言
	ERROR_SECRET_LOVER_INVALID_MSG			= 1255,	//非法的暗恋消息
	ERROR_GIO_INVALID_MSG				= 1256,	//非法的羁绊消息
	ERROR_MAIL_INVALID_CONTEXT			= 1257,	//非法的道具赠送留言

	ERROR_FACTION_REPU_LIMIT			= 1258,	//社团声望没有开启
	ERROR_FACTION_CAMP_OPEN				= 1259,	//关联活动开启中，无法特殊任命

	ERROR_CORPS_TOWER_SINGLE_NO_AWARD		= 1260,	//社团爬塔落单战场没有排名奖励以及胜负奖励
	ERROR_FACTION_SELF_REPU_LIMIT			= 1261,	//自己的社团声望没有开启，与1258进行区分

	ERROR_SOCIAL_SPACE_CORPS_COUNT_LIMIT		= 1262, //今日已发送过状态
	ERROR_SOCIAL_SPACE_CORPS_ACTIVITY		= 1263, //会长活力值不足
	ERROR_SOCIAL_SPACE_CORPS_EDITOR_LIMIT		= 1264,	//无权限(职位不对)
	ERROR_SOCIAL_SPACE_CORPS_CONTENT_LIMIT		= 1265, //发送内容超长
	ERROR_SOCIAL_SPACE_CORPS_CONTENT_CHECK		= 1266, //内容包含敏感字
	ERROR_SOCIAL_SPACE_CORPS_CONTENT_REMIND_MAX     = 1267, //提醒人数超过上限
	ERROR_SOCIAL_SPACE_CORPS_CONTENT_REMIND_INVALID = 1268, //提醒人不在帮派中

	ERROR_GFRIEND_ANNI_END				= 1269, //纪念日已全部结束
	ERROR_GFRIEND_ANNI_TIME				= 1270, //不在羁绊纪念日时间范围
	ERROR_GFRIEND_ANNI_TASK				= 1271, //已经接取任务
	ERROR_GFRIEND_ANNI_NO_TASK			= 1272, //还没有接取任务
	ERROR_GFRIEND_ANNI_DATA				= 1273, //已经存在纪念日数据
	ERROR_GFRIEND_ANNI_NO_DATA			= 1274, //没有纪念日数据
	ERROR_PLAYER_CARRACE_DS_CHECK_ERR		= 1275, //DS检查不通过
	ERROR_PLAYER_CARRACE_NOT_ONLINE			= 1276, //玩家不在线
	ERROR_PLAYER_CARRACE_STATE_ERROR		= 1277, //玩家状态错误 gs检查不通过
	ERROR_PLAYER_CARRACE_MATCH_ROOM_TIMEOUT	= 1278, //赛车匹配，操作时房间已超时
	ERRPR_PLAYER_CARRACE_MATCH_ROLEID_INVALID = 1279, //赛车匹配，玩家id不正确

	ERROR_PLAYER_CARRACE_ALREADY_IN_MATCH = 1280, //赛车匹配 已经在匹配中 重复申请
	ERROR_THIS_CORPS_BADGE_CANNOT_SET_TEMPORARY	= 1281, // 此社团徽章暂时不可设置

	ERROR_TEAM_APPLY_ROLE_COOLDOWN = 1282,//队伍申请/邀请冷却中
	ERROR_FRIEND_ADD_ROLE_COOLDOWN = 1283,//好友申请冷却中

	ERROR_CORPS_AUCTION_INVALID_REQUEST = 1284,			//社团拍卖错误的请求
	ERROR_CORPS_AUCTION_LESS_CASH		= 1285,			//社团拍卖点券不足
	ERROR_CORPS_AUCTION_WRONG_STATE		= 1286,			//社团拍卖状态错误
	ERROR_CORPS_AUCTION_NOT_IN_CORPS	= 1287,			//社团拍卖不在社团
	ERROR_CORPS_AUCTION_LOW_CORPS_POS	= 1288,			//社团拍卖职位过低
	ERROR_CORPS_AUCTION_LESS_JOINTIME	= 1289,			//社团拍卖加入社团时间过短
	ERROR_CORPS_AUCTION_LESS_LEVEL		= 1290,			//社团拍卖世界拍卖等级不足
	ERROR_CORPS_AUCTION_SELLOUT			= 1291,			//社团拍卖已经卖出
	ERROR_CORPS_AUCTION_PRICE_LOW		= 1292,			//社团拍卖出价过低
	ERROR_CORPS_AUCTION_NO_WITHDRAW		= 1293,			//社团拍卖没有可以领取的返还
	ERROR_CORPS_AUCTION_HAS_WITHDRAW	= 1294,			//社团拍卖必须领取返还才能竞价
	ERROR_CORPS_AUCTION_CURRENT_BID		= 1295,			//社团拍卖已经是当前最高价
	ERROR_CORPS_AUCTION_UNKNOWN			= 1296,			//社团拍卖服务器未知错误
	ERROR_ITEM_CANNOT_TRADE_TEMPORARY	= 1297,			//该商品已经暂时被禁止交易

	ERROR_CORPS_REPORT_MASTER_COOLDOWN = 1299, // 社团举报会长冷却中
	ERROR_CORPS_INVITE_COOLDOWN = 1300, // 社团邀请处于冷却中
	ERROR_CREATE_ROLE_CREDIT_LIMIT	= 1301, //信用值过低禁止创建角色
	ERROR_AUCTION_RECHARGE_LIMIT        = 1302,			// 拍卖行购买充值限制：本次交易疑似违规，无法进行
	ERROR_CORPS_AUCTION_MAILBOX_FULL	= 1303,	//社团拍卖行邮箱已满

	ERROR_CORPS_GROUP_FULL                  = 1304,         //小组已满
	ERROR_NOT_CORPS_MEMBER                  = 1305,         //不是本社团成员
	ERROR_CORPS_GROUP_NOT_MATCH             = 1306,         //不在本行动小组中
	ERROR_CORPS_GROUP_ALREADY_LEADER        = 1307,         //已经是组长
	ERROR_CORPS_GROUP_NOT_LEADER            = 1308,         //不是行动组长
	ERROR_CORPS_DST_IN_GROUP                = 1309,         //对方已在小组中
	ERROR_ARENA_GROUP_NOT_SAME_ARENA_ZONE	= 1310,		//玩家和战队不是同一个战区的
	ERROR_MERGE_CORPS_LEVEL_LIMIT		= 1311,		//1级社团不能合并比1级高的社团
	ERROR_RENAME_DEL_OLD_NAME				= 1312,		//曾用名满了需要移除最早的名字, 合并代码是务必保留这个错误码
	ERROR_CORPS_GROUP_DST_TIME		= 1313,		//对方加入不足两天
	ERROR_CORPS_GROUP_DST_POS		= 1314,		//对方是实习或休假
	ERROR_CORPS_GROUP_DST_LEVEL		= 1315,		//对方等级不足50

	ERROR_CORPS_GARDEN_LESS_ACTIVITY		= 1320,			//社团花坛操作，活力不足
	ERROR_CORPS_GARDEN_INVALID_NAME			= 1321,			//社团花坛改名，名字无效
	ERROR_CORPS_GARDEN_INVALID_REQUEST		= 1322,			//社团花坛操作，错误的请求
	ERROR_CORPS_GARDEN_HAS_FLOWER			= 1323,			//社团花坛操作，已经有正在培育的花
	ERROR_CORPS_GARDEN_LESS_LEVEL			= 1324,			//社团花坛操作，等级不足
	ERROR_CORPS_GARDEN_HAS_BREED			= 1325,			//社团花坛操作，鲜花已经被培育
	ERROR_CORPS_GARDEN_NO_FLOWER			= 1326,			//社团花坛操作，当前位置没有鲜花
	ERROR_CORPS_GARDEN_BREEDING				= 1327,			//社团花坛操作，培育尚未结束
	ERROR_CORPS_GARDEN_NOT_BREED			= 1328,			//社团花坛操作，鲜花还没有培育
	ERROR_CORPS_GARDEN_NO_STATE				= 1329,			//社团花坛操作，没有指定的负面状态
	ERROR_CAREER_SHOP_NO_FREE_SPACE			= 1330,	// 小店地图已满

	ERROR_CORPS_CHAT_ROOM_ALREADY_OPEN      	= 1331, // 社团实时语音聊天室已经开启
	ERROR_CORPS_CHAT_ROOM_NOT_OPEN          	= 1332, // 社团实时语音聊天室没有开启
	ERROR_CORPS_CHAT_ROOM_ALREADY_MICROPHONE	= 1333, // 社团实时语音已经开麦
	ERROR_CORPS_CHAT_ROOM_MICROPHONE_FULL   	= 1334, // 社团实时语音已经开麦
	ERROR_CORPS_CHAT_ROOM_NOTIFY_COOLDOWN		= 1335,	// 社团实时语音邀请收听冷却中

	ERROR_HOMETOWN_HAS_OWNER			= 1340,			//家园已经有主人了
	ERROR_HOMETOWN_ALREADY_HAS			= 1341,			//已经有家园了
	ERROR_HOMETOWN_NONE					= 1342,	// 没有家园
	ERROR_HOMETOWN_IPD_FAIL				= 1343,	// 共享内存操作失败
	ERROR_HOMETOWN_FAIL					= 1344,	// 失败
	ERROR_HOMETOWN_FULL					= 1345, // 家园人数已满
	ERROR_HOMETOWN_FURNITURE_LOCKED     = 1346, // 家具需要解锁
	ERROR_HOMETOWN_TEMPLATE_NOT_ALL_BOUND_MONEY = 1347, // 家园样板间中使用了非绑定金币的家具

	ERROR_CENTER_PUBG_NO_TEAM		= 1360,		//吃鸡战场：队伍不存在
	ERROR_CENTER_PUBG_NOT_LEADER	= 1361,		//吃鸡战场：不是队长
	ERROR_CENTER_PUBG_MEMBER_COUNT  = 1362,		//吃鸡战场：队员人数不能报名
	ERROR_CENTER_PUBG_MEMBER_NOT_AROUND = 1363, //吃鸡战场：有队员不再附近
	ERROR_CENTER_PUBG_HAS_TEAM		= 1364,		//吃鸡战场：在队伍中不能报名单人战场
	ERROR_CENTER_PUBG_NO_PROF		= 1365,		//吃鸡战场：没有选择职业倾向
	ERROR_CENTER_BATTLE_OTHER_ALREADY_MATCHED  = 1366,  //其他战场已经处于匹配状态

	ERROR_PROXY_DRAW_DST_OFFLINE			= 1380,	// 代抽，对方不在线
	ERROR_PROXY_DRAW_DST_IS_NOT_FRIEND		= 1381,	// 代抽，对方不是你好友
	ERROR_PROXY_DRAW_SRC_IS_NOT_FRIEND		= 1382,	// 代抽，你不是对方好友
	ERROR_PROXY_DRAW_SRC_COOLDOWN			= 1383,	// 代抽，发起冷却
	ERROR_PROXY_DRAW_DST_COOLDOWN			= 1384,	// 代抽，对方正在代抽冷却
	ERROR_PROXY_DRAW_REFUSE					= 1385,	// 代抽，拒绝
	ERROR_GFRIEND_ANNI_RED_WRONG_LEVEL              = 1386, // 羁绊纪念日红包等级不对
	ERROR_GFRIEND_ANNI_RED_LEVEL_LIMIT              = 1387, // 羁绊纪念日红包等级不满足条件
	ERROR_WEDDING_INVITE_FAIL                       = 1388, // 拒绝了婚礼邀请

	ERROR_SOUL_INVALID_ICON				= 1390,	// 灵魂伴侣图标错误
	ERROR_SOUL_NOT_CREATE				= 1391,	// 对方不是灵魂伴侣
	ERROR_SOUL_INVALID_PARAM			= 1392,	// 灵魂伴侣参数错误
	ERROR_SOUL_LEVEL_LIMIT				= 1393,	// 灵魂伴侣等级不够
	ERROR_SOUL_INTIMATE_ERR				= 1394,	// 双方还不是羁绊或者羁绊值不够
	ERROR_SOUL_ALREADY				= 1395,	// 双方已经是灵魂伴侣
	ERROR_SOUL_CREARE_COOLDOWN			= 1396,	// 结成伴侣还未到冷却时间
	ERROR_SOUL_DST_LEVEL_LIMIT			= 1397,	// 对方等级不足
	ERROR_SOUL_COUNT_LIMIT                      	= 1398, // 伴侣数量限制
	ERROR_SOUL_DST_COUNT_LIMIT			= 1399,	// 对方伴侣数量限制
	ERROR_SOUL_REQ_SEND				= 1400,	// 已发送请求 等待对方回应
	ERROR_SOUL_REQ_FAILED				= 1401,	// 请求被拒绝
	ERROR_SOUL_VALUE_LIMIT				= 1402,	// 羁绊值不足
	ERROR_SOUL_RED_WRONG_LEVEL			= 1403,	// 伴侣发放红包等级错误
	ERROR_SOUL_RED_LEVEL_LIMIT			= 1404,	// 伴侣发放红包等级不足
	ERROR_SOUL_TEAM_GFX_LIMIT			= 1405,	// 组队特效未解锁
	ERROR_SOUL_CHAT_BACKGROUND_LIMIT		= 1406,	// 聊天背景未解锁

	ERROR_SOUL_MSG_INVALID_ITEM                     = 1409, // 物品参数检查错误
	ERROR_SOUL_TELEPORT_TARGET_OFFLINE		= 1410,	// 传送目标不在线
	ERROR_SOUL_TELEPORT_TARGET_INVALID		= 1411,	// 传送目标无法传送
	ERROR_SOUL_TELEPORT_COOLDOWM			= 1412,	// 传送冷却中
	ERROR_SOUL_INVALID_MSG_ID			= 1413,	// 消息id未找到
	ERROR_SOUL_MSG_ALREADY_REPLY			= 1414,	// 消息已经回复过
	ERROR_SOUL_MSG_COUNT_LIMIT			= 1415, // 消息超过最大数量

	// 1416-1429 注销账号占用，如果合并冲突，请保留这些
	ERROR_DELETE_ACCOUNT_MAIL_ATTACH		= 1416, // 有邮件未领取
	ERROR_DELETE_ACCOUNT_AUCTION			= 1417, // 还有物品在拍卖行出售
	ERROR_DELETE_ACCOUNT_AUCTION_MAIL		= 1418, // 有拍卖行收益未领取
	ERROR_DELETE_ACCOUNT_TMP_REWARD			= 1419, // 临时包裹内还有道具未领取
	ERROR_DELETE_ACCOUNT_SHOP_INCOME		= 1420, // 小店收益还未领取
	ERROR_DELETE_ACCOUNT_INTIMATE			= 1421, // 羁绊关系还未解除
	ERROR_DELETE_ACCOUNT_SECRET_LOVE		= 1422, // 好感目标还未解除
	ERROR_DELETE_ACCOUNT_CORPS_MASTER		= 1423, // 仍担任社团会长
	ERROR_DELETE_ACCOUNT_ARENAGROUP_LEADER	= 1424, // 仍担任战队队长
	ERROR_DELETE_ACCOUNT_SECT_RELATION		= 1425, // 师徒关系还未解除
	ERROR_DELETE_ACCOUNT_SECT_PARTNER		= 1426, // 助教关系还未解除
	ERROR_DELETE_ACCOUNT_VERIFY_FAILED       = 1427, // 身份验证失败
	ERROR_DELETE_ACCOUNT_VERIFY_FAILED2       = 1428, // 身份证不符合格式
	ERROR_DELETE_ACCOUNT_VERIFY_FAILED3       = 1429, // 身份证公安接口返回不一致

	// 1430-1440灵魂伴侣占用，如果合并冲突，请保留这些
	ERROR_SOUL_TREE_NEED_NO_WATER			= 1430,	// 誓约树无需浇水
	ERROR_SOUL_TREE_NEED_NO_MANURE			= 1431,	// 誓约树无需施肥
	ERROR_SOUL_TREE_NEED_NO_DEBUG			= 1432,	// 誓约树无需除虫

	ERROR_SHOP_PARTNER_INVITE_SEND			= 1439,	// 邀请已发送
	ERROR_SHOP_PARTNER_OFFLINE				= 1440,	// 离线
	ERROR_SHOP_PARTNER_LEVEL_LIMIT			= 1441,	// 等级不足
	ERROR_SHOP_PARTNER_LESS_ADVENTURE		= 1442,	// 异闻点数不足
	ERROR_SHOP_PARTNER_COOK_UNACTIVE		= 1443,	// 料理未激活
	ERROR_SHOP_PARTNER_STAR_UNACTIVE		= 1444,	// 明星未激活
	ERROR_SHOP_PARTNER_JOB_NONEMPTY			= 1445,	// 合伙人身份已经分配
	ERROR_SHOP_PARTNER_NOT_FRIEND			= 1446,	// 不是好友
	ERROR_SHOP_PARTNER_IN_INSTANCE			= 1447,	// 在副本中
	ERROR_SHOP_PARTNER_REFUSE				= 1448,	// 对方拒绝
	ERROR_SHOP_PARTNER_OPENED				= 1449,	// 今天已开店
	ERROR_SHOP_PARTNER_SHOP_UNACTIVE		= 1450,	// 小店身份未激活
	ERROR_SHOP_PARTNER_NO_JOB				= 1451,	// 不是合伙人
	ERROR_SHOP_PARTNER_NO_BONUS				= 1452,	// 没有分红可以领
	ERROR_SHOP_PARTNER_FULL					= 1453,	// 被邀请人合伙人身份的数量已满
	ERROR_SHOP_PARTNER_COOLDOWN				= 1454,	// 合伙人打卡冷却
	ERROR_SHOP_PARTNER_BONUS_ALREADY		= 1455,	// 已经领过分红了
	ERROR_SHOP_PARTNER_JOIN_TOO_LATE		= 1456,	// 入伙时间过晚

	//天梯群组聊天
	ERROR_ARENA_GROUP_CHAT_ARENA_NOT_FOUNT  = 1470, // 天梯战队不存在
	ERROR_ARENA_GROUP_CHAT_NEED_LEADER      = 1471, // 需要队长进行操作
	ERROR_ARENA_GROUP_CHAT_ALREADY_HAS_GROUP = 1472, // 天梯战队已经有群聊

	//限时抽奖
	ERROR_LIMIT_LOTTERY_STATE_ERROR = 1490, // 领奖阶段错误
	ERROR_LIMIT_LOTTERY_REWARD_ERROR = 1491, // 奖品错误
	ERROR_LIMIT_LOTTERY_NO_COUNT_ERROR = 1492, // 外圈次数不够
	ERROR_LIMIT_LOTTERY_ALREADY_ERROR = 1493, // 已经领过奖

	//家园派对
	ERROR_HOMETOWN_PARTY_NO_HOME = 1500, // 没有家园
	ERROR_HOMETOWN_PARTY_NO_INVITE_SELF = 1501, // 不能邀请自己
	ERROR_HOMETOWN_PARTY_INVITE_TYPE_WRONG = 1502, // 邀请类型错误
	ERROR_HOMETOWN_PARTY_PARTY_TYPE_WRONG = 1503, // 派对类型错误
	ERROR_HOMETOWN_PARTY_AGENT_INDEX = 1504, // 委托索引错误
	ERROR_HOMETOWN_PARTY_NOT_ONLINE = 1505, // 目标不在线
	ERROR_HOMETOWN_PARTY_TARGET_IS_AGENT = 1506, // 目标已经是委托人了
	ERROR_HOMETOWN_PARTY_CAREER_LEVEL_WRONG = 1507, // 目标身份等级不足
	ERROR_HOMETOWN_PARTY_NO_RELIATON =  1508, // 不满足社团好友关系
	ERROR_HOMETOWN_PARTY_INVITE_CD = 1509, // 邀请CD中
	ERROR_HOMETOWN_PARTY_END = 1510, // 派对已经结束啦
	ERROR_HOMETOWN_PARTY_HAS_AGENT = 1511, // 该委托职位已经有人啦
	ERROR_HOMETOWN_PARTY_INVITE_REFUSE = 1512, // 对方拒绝你的邀请
	ERROR_HOMETOWN_PARTY_INVITE_ACK = 1513, // 给邀请委托人回复客户端提示用
	ERROR_HOMETOWN_PARTY_INVITE_FRIEND_ACK = 1515, // 给邀请好友回复客户端提示用
	ERROR_HOMETOWN_PARTY_INVITE_CAN_ENTER = 1516, // 仅邀请人可进入

	ERROR_VOW_ALREADY	= 1520, // 已经开始过宣誓
	ERROR_VOW_COOLDOWN	= 1521, // 宣誓cd
	ERROR_VOW_LIST_END	= 1522, // 宣誓列表已到底
	ERROR_VOW_INVALID	= 1523,	// 宣誓内容非法

	// 拍卖行关注
	ERROR_NEW_AUCTION_ATTENTION_COMMON			= 1530,		// 通用错误
	ERROR_NEW_AUCTION_ATTENTION_NO_AUCTION		= 1531,		// 没有这个上架商品
	ERROR_NEW_AUCTION_ATTENTION_HAS_ATTENTION	= 1532,		// 已经关注这个上架商品
	ERROR_NEW_AUCTION_ATTENTION_NO_ATTENTION	= 1533,		// 没有关注这个上架商品
	ERROR_NEW_AUCTION_ATTENTION_SELF_SELL		= 1534,		// 自己售卖的物品不能关注

	// 家园装修方案 设计方案
	ERROR_HOMETOWN_SCHEME_RETRY = 1550, // 重试
	ERROR_HOMETOWN_SCHEME_INVALID_INDEX = 1551, // 无效的装修方案序号
	ERROR_HOMETOWN_SCHEME_NAME_INVALID = 1552, // 非法的装修方案名称
	ERROR_HOMETOWN_DESIGN_NAME_OR_DESC_INVALID = 1553, // 家园设计名称或描述包含敏感词
	ERROR_HOMETOWN_DESIGN_UPLOADING = 1554, // 设计方案上传中
	ERROR_HOMETOWN_DESIGN_CSP_PARAM_INVALID = 1555, // csp检查参数错误
	ERROR_HOMETOWN_DESIGN_CSP_UPLOAD_FAIL = 1556, // csp上传失败

	//淘汰赛战队1560-1620
	ERROR_ELIMINATE_GROUP_INVITE_COOLDOWN = 1560,	//战队邀请冷却
	ERROR_ELIMINATE_GROUP_BAD_NAME = 1561,  //竞技场战队名字不合法
	ERROR_ELIMINATE_GROUP_DUP_NAME = 1562,  //竞技场战队名字重复
	ERROR_ELIMINATE_GROUP_MONEY_NOT_ENOUGH = 1563,  //竞技场战队创建的钱不够
	ERROR_ELIMINATE_GROUP_MAX_COUNT = 1564, //竞技场战队数量已经达到上限
	ERROR_ELIMINATE_GROUP_ALREADY_IN = 1565,    //已经处于战队中了
	ERROR_ELIMINATE_GROUP_INVITE_TIMEOUT = 1566,    //战队邀请超时
	ERROR_ELIMINATE_GROUP_MEMBER_MAX_COUNT = 1567,  //战队成员已经满了
	ERROR_ELIMINATE_GROUP_LEADER_CANT_QUIT = 1568,  //战队队长不能退出
	ERROR_ELIMINATE_GROUP_NOT_IN_GROUP = 1569,  //没有处于战队中
	ERROR_ELIMINATE_GROUP_NO_RIGHT = 1570,  //战队操作没有权限
	ERROR_ELIMINATE_GROUP_PLAYER_LEVEL_WRONG = 1571,    //等级太低
	ERROR_ELIMINATE_GROUP_PLAYER_NOT_ONLINE = 1572, //对方不在线
	ERROR_ELIMINATE_GROUP_TARGET_ALREADY_IN = 1573,	//对方已经处于战队中了
	ERROR_ELIMINATE_GROUP_INVITE_REFUSED = 1574,	//拒绝加入战队
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_TEAM = 1575,  //玩家没有队伍
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_GROUP = 1576, //玩家没有战队
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NO_CENTER = 1577, //
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NOT_LEADER = 1578, //玩家不是队长
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NOT_ENOUGH_MEMBER = 1579, //队伍人数不足
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_MEMBER_OFFLINE = 1580, //队员离线
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NOT_SAME_GROUP = 1581, //队员不是同一个战队
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_PUNISHED = 1582, //处于惩罚期
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_ALREADY_MATCHED = 1583, //战队已经匹配中
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_DUP_PROFESSION   = 1584,//重复的职业
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_APPLY_INVALID_SCENE = 1585,//战队处于非法的报名场景
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_IN_PUNISHED = 1586,//战队成员处于惩罚时间
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_SELECT_FIGHTER_NO_LEADER = 1587, //非队长不能选择参战成员
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_NOT_FIGHTER              = 1588, //不是参战成员不能进战场
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF     = 1589, //选择参战成员职业不合法
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_FIGHTER_TIME             = 1590, //非选择参战成员时间
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_FIGHTER_PROF_NOT_MATCH   = 1591, //参战成员职业与报名时不匹配
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_WATCH_SAME_GROUP         = 1592, //禁止观战自身战队的比赛
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_WATCH_TOO_MANY_PLAYER    = 1593, //观战人数过多
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_3V3_DUP_PROFESSION       = 1594, //重复的职业3v3
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_IS_MATCHING              = 1595, //匹配中
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_2V2_DUP_PROFESSION       = 1596, //重复的职业2v2
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_2V2_MARRIAGE_LIMIT		= 1597,	//婚姻限制
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF_16	= 1598, //选择参战成员职业不合法_16
	ERROR_ELIMINATE_GROUP_ARENA_BATTLE_INVAILD_FIGHTER_PROF_18	= 1599, //选择参战成员职业不合法_18

	ERROR_SSP_COMPTITION_ACTIVITY_NOT_OPEN			= 1601, //朋友圈比赛关联活动未开启
	ERROR_SSP_COMPTITION_OPERATION_COOLDOWN			= 1602, //朋友圈比赛操作冷却中
	ERROR_SSP_COMPTITION_OPERATION_COUNT_LIMIT		= 1603,	//朋友圈比赛操作次数限制
	ERROR_SSP_COMPTITION_PLAT_ERROR				= 1604,	//朋友圈比赛平台返回错误

	ERROR_TEMPORARY_TEAM_COOLDOWN				= 1610, //退队冷却中
	ERROR_TEMPORARY_TEAM_EXIST				= 1611,	//已经有队伍
	ERROR_TEMPORARY_TEAM_TARGET_EXIST			= 1612,	//对方已经有队伍
	ERROR_TEMPORARY_TEAM_NOT_EXIST				= 1613,	//还没有队伍无法加入
	ERROR_TEMPORARY_TEAM_FULL				= 1614,	//队伍已满无法加入

	//社团约战
	ERROR_CORPS_DUEL_SWITCH_COOLDOWN        = 1630,     //设置社团约战状态冷却之中
	ERROR_CORPS_DUEL_INFO_NOT_UPLOAD        = 1631,     //社团约战信息没有上报到中心服
	ERROR_CORPS_DUEL_DUP_REQUEST            = 1632,     //社团约战重复邀请
	ERROR_CORPS_DUEL_TARGET_BUSY            = 1633,     //社团对战对战
	ERROR_CORPS_DUEL_REQUEST_COOLDOWN       = 1634,     //社团约战发起冷却中
	ERROR_CORPS_DUEL_TARGET_COOLDOWN        = 1635,     //社团约战发起冷却中
	ERROR_CORPS_DUEL_SWITCH_CLOSE           = 1636,     //社团约战被关闭
	ERROR_CORPS_DUEL_REQUEST_NOT_MASTER     = 1637,     //
	ERROR_CORPS_DUEL_REPLY_NOT_MASTER       = 1638,     //
	ERROR_CORPS_DUEL_REQUEST_NO_TARGET      = 1639,
	ERROR_CORPS_DUEL_ENTER_LEVEL            = 1640,
	ERROR_CORPS_DUEL_MASTER_OFFLINE         = 1641,
	ERROR_CORPS_DUEL_TARGET_NOT_FOUND       = 1642,
	// 育宠达人
	ERROR_BREED_COMMON_ERR								= 1700,	// 通用错误
	ERROR_BREED_HAS_ACTIVE								= 1701,	// 已经激活
	ERROR_BREED_NO_ACTIVE								= 1702,	// 未激活
	ERROR_BREED_LEVEL_UP_PROFICIENCY_NOT_ENOUGH			= 1703,	// 升级 熟练度不够
	ERROR_BREED_LEVEL_UP_TASK_ONGOING					= 1704,	// 升级 进行中
	ERROR_BREED_LEVEL_UP_TASK_CAN_NOT_DELIVER			= 1705,	// 升级 进行中
	ERROR_BREED_CUTE_PET_NOT_EXIST						= 1706,	// 萌宠 不存在
	ERROR_BREED_CUTE_PET_HAS_FOLLOW						= 1707,	// 萌宠已经跟随
	ERROR_BREED_CUTE_PET_NO_FOLLOW						= 1708,	// 萌宠没有萌宠跟随
	ERROR_BREED_CUTE_PET_NO_BUBBLE						= 1709,	// 萌宠没有气泡
	ERROR_BREED_CUTE_PET_HAS_CREDENTIALS				= 1710,	// 萌宠已经拥有证书
	ERROR_BREED_CUTE_PET_CAN_NOT_CREDENTIALS			= 1711,	// 萌宠阶段不能申请证书
	ERROR_BREED_CUTE_PET_CREDENTIALS_MONEY_NOT_ENOUGH	= 1712,	// 萌宠没钱申请证书
	ERROR_BREED_CUTE_PET_NO_CREDENTIALS					= 1713,	// 萌宠没有证书
	ERROR_BREED_CUTE_PET_CREDENTIALS_NUM_LIMIT			= 1714,	// 萌宠证书数量限制
	ERROR_BREED_CUTE_PET_HAS_DISEASE					= 1715,	// 萌宠生病了
	ERROR_BREED_CUTE_PET_NO_DISEASE						= 1716,	// 萌宠没有生病
	ERROR_BREED_CUTE_PET_CURE_MONEY_NOT_ENOUGH			= 1717,	// 萌宠没钱治疗
	ERROR_BREED_CUTE_PET_CURE_ONGOING					= 1718,	// 萌宠正在治疗
	ERROR_BREED_CUTE_PET_ILLEGAL_NAME					= 1719,	// 萌宠非法名字
	ERROR_BREED_CUTE_PET_PRESENT_NOT_SOUL_INTIMATE		= 1720,	// 萌宠赠送不是灵魂伴侣
	ERROR_BREED_CUTE_PET_PRESENT_NOT_IN_TEAM			= 1721,	// 萌宠赠送没有队伍
	ERROR_BREED_CUTE_PET_PRESENT_NOT_TEAM_LEADER		= 1722,	// 萌宠赠送不是队长
	ERROR_BREED_CUTE_PET_PRESENT_NOT_TEAM_MEMBER		= 1723,	// 萌宠赠送接受者不是队员
	ERROR_BREED_CUTE_PET_PRESENT_TEAM_MEMBER_LIMIT		= 1724,	// 萌宠赠送队伍人数限制
	ERROR_BREED_CUTE_PET_PRESENT_NO_TEAM_FOLLOW			= 1725,	// 萌宠赠送没有组队跟随
	ERROR_BREED_CUTE_PET_PRESENT_GRID_LIMIT				= 1726,	// 萌宠赠送接受者格子限制
	ERROR_BREED_CUTE_PET_PRESENT_WEEK_TOTAL_LIMIT		= 1727,	// 萌宠赠送周总次数限制
	ERROR_BREED_CUTE_PET_PRESENT_WEEK_SINGLE_LIMIT		= 1728,	// 萌宠赠送周每人次数限制
	ERROR_BREED_CUTE_PET_ERR_INTERACT					= 1729,	// 萌宠错误的交互
	ERROR_BREED_CUTE_PET_ERR_GROWTH_STAGE				= 1730,	// 萌宠错误的成长阶段
	ERROR_BREED_CUTE_PET_IN_BREED						= 1731,	// 萌宠正在繁育
	ERROR_BREED_CUTE_PET_IN_REST						= 1732,	// 萌宠繁育正在休息
	ERROR_BREED_CUTE_PET_IN_COOL						= 1733,	// 萌宠繁育冷却中
	ERROR_BREED_CUTE_PET_DIFFERENT_TYPE					= 1734,	// 萌宠繁育不同类型
	ERROR_BREED_CUTE_PET_COMMON_GENDER					= 1735,	// 萌宠繁育相同性别
	ERROR_BREED_CUTE_PET_RECEIVE_INVALID_TARGET			= 1736,	// 萌宠繁育目标无效
	ERROR_BREED_CUTE_PET_RECEIVE_INVALID_URL			= 1737,	// 萌宠繁育链接无效
	ERROR_BREED_CUTE_PET_RECEIVE_HAS_RECEIVE			= 1738,	// 萌宠繁育已经有人接受了
	ERROR_BREED_CUTE_PET_RECEIVE_SELF_HAS_RECEIVE		= 1739,	// 萌宠繁育自己已经接受了
	ERROR_BREED_CUTE_PET_NO_BREED						= 1740,	// 萌宠繁育没有繁育的萌宠
	ERROR_BREED_CUTE_PET_TRASH_EXPRESS_0				= 1741,	// 萌宠回收稀有
	ERROR_BREED_CUTE_PET_NO_HOMETOWN					= 1742,	// 萌宠没有家园
	ERROR_BREED_CUTE_PET_IN_HOMETOWN					= 1743,	// 萌宠在家园
	ERROR_BREED_CUTE_PET_NOT_IN_HOMETOWN				= 1744,	// 萌宠不在家园
	ERROR_BREED_CUTE_PET_PROSPERITY_LIMIT				= 1745,	// 萌宠返回家园温馨度限制
	ERROR_BREED_CUTE_PET_GRID_NUM_LIMIT					= 1746,	// 萌宠格子数量限制
	ERROR_BREED_CUTE_PET_DELIVER_TASK_FAIL				= 1747,	// 萌宠任务发放失败
	ERROR_BREED_ACTIVE_LEVEL_ERR						= 1748,	// 育宠达人激活等级不够
	ERROR_BREED_CUTE_PET_PRESENT_WEEK_TOTAL_RECEIVE_LIMIT	= 1749,	// 萌宠赠送周接受总次数限制
	ERROR_BREED_CUTE_PET_PRESENT_TARGET_NO_ACTIVE		= 1750,	// 萌宠赠送对方未激活

	ERROR_HARMONIOUS_ANNI_STATUS                            = 1781, //玩家状态错误
	ERROR_HARMONIOUS_ANNI_ID_WRONG                          = 1782, //参数错误
	ERROR_HARMONIOUS_ANNI_ALREADY                           = 1783, //已经领过奖励
	ERROR_HARMONIOUS_ANNI_EARLY                             = 1784, //还没到时间
	ERROR_HARMONIOUS_ANNI_LATE                              = 1785, //已过期

	ERROR_PDD_TEAM_EXIST				= 1800,	//已经有队伍
	ERROR_PDD_TEAM_TARGET_EXIST			= 1801,	//对方已经有拼团
	ERROR_PDD_TEAM_NOT_EXIST			= 1802,	//对方没有拼团无法加入
	ERROR_PDD_TEAM_FULL				= 1803,	//拼团已满
	ERROR_PDD_TEAM_INVALID				= 1804, //拼团无效
	ERROR_PDD_TEAM_TIMEOUT				= 1805, //拼团过期
	ERROR_PDD_TEAM_REWARD_ALREADY			= 1806,	//已领过奖励
	ERROR_PDD_TEAM_REWARD_ERR                       = 1807, //不满足领奖条件
	ERROR_MARRIAGE_STATUS_EXIST			= 1808,//婚姻状态出错
	ERROR_MARRIAGE_MONEY_NOENOUGH		= 1810,//婚姻金钱不够
	ERROR_PDD_TEAM_NOT_EXIST_2			= 1811,	//对方拼团验证错误

	ERROR_PLAYER_NOT_IN_GLOBAL_WORLD            = 1850, //玩家不在大世界
	ERROR_HARMONIOUS_DELIVER_CD                 = 1851, //良缘传送在CD
	ERROR_HARMONIOUS_DELIVER_INVILD				= 1852, //传送非法
	ERROR_HARMONIOUS_INVITE_SEND_SUCESS			= 1853, //良缘操作邀请发送成功

	ERROR_BLESS_WALL_ERR_COMMON					= 1860,
	ERROR_BLESS_WALL_ERR_QUERY_NO_RECORD		= 1861,	// 祝福墙查询没有记录
	// 时装搭配大赛
	ERROR_FASHION_DRESS_COMMON_ERR								= 1900,		// 通用错误
	ERROR_FASHION_DRESS_ACTIVITY_NOT_OPEN						= 1901,		// 活动未开
	ERROR_FASHION_DRESS_PERIOD_ERR								= 1902,		// 阶段错误
	ERROR_FASHION_DRESS_TXN_ERR									= 1903,		// 事务错误
	ERROR_FASHION_DRESS_FASHION_NOT_ACTIVE						= 1904,		// 时装没有激活
	ERROR_FASHION_DRESS_FASHION_NOT_EXPIRE						= 1905,		// 时装不是永久
	ERROR_FASHION_DRESS_PART_COLOR_NOT_ACTIVE					= 1906,		// 色块没有激活
	ERROR_FASHION_DRESS_SCHEME_SCORE_NOT_EQUAL					= 1907,		// 时装方案分数错误
	ERROR_FASHION_DRESS_DAY_HAS_UPLOAD_SCHEME					= 1908,		// 时装方案今日已上传
	ERROR_FASHION_DRESS_VOTE_TYPE_ERR							= 1909,		// 投票类型错误
	ERROR_FASHION_DRESS_VOTE_CAN_NOT_SELF						= 1910,		// 投票不能投自己
	ERROR_FASHION_DRESS_LOCAL_FREEDOM_VOTE_NOT_ENOUGH			= 1911,		// 本服投票自由票不够
	ERROR_FASHION_DRESS_LOCAL_RECOMMEND_VOTE_NOT_ENOUGH			= 1912,		// 本服投票推荐票不够
	ERROR_FASHION_DRESS_ROAM_VOTE_NOT_ENOUGH					= 1913,		// 跨服投票票不够
	ERROR_FASHION_DRESS_ROAM_VOTE_POS_ERR						= 1914,		// 跨服投票位置错误
	ERROR_FASHION_DRESS_ROAM_VOTE_AREA_ERR						= 1915,		// 跨服投票区域错误
	ERROR_FASHION_DRESS_ROAM_VOTE_HAS_OUT						= 1916,		// 跨服投票已被淘汰
	ERROR_FASHION_DRESS_ROAM_VOTE_AREA_HAS_VOTE					= 1917,		// 跨服投票区域已经投票过
	ERROR_FASHION_DRESS_ROAM_VOTE_AREA_HAS_SELECT				= 1918,		// 跨服投票区域已经选区
	ERROR_FASHION_DRESS_ROAM_ITEM_VOTE_NOT_ENOUGH				= 1919,		// 跨服打榜不够
	ERROR_FASHION_DRESS_ROAM_ITEM_VOTE_ITEM_ERR					= 1920,		// 跨服打榜物品错误
	ERROR_FASHION_DRESS_ROAM_ITEM_VOTE_ITEM_NOT_CG				= 1921,		// 跨服打榜不是cg玩家
	ERROR_FASHION_DRESS_ROAM_ATMOSPHERE_LEVEL_ERR				= 1922,		// 跨服氛围值奖励档次错误
	ERROR_FASHION_DRESS_ROAM_ATMOSPHERE_LEVEL_REWARD_NOT_ENOUGH	= 1923,		// 跨服氛围值奖励氛围值不够
	ERROR_FASHION_DRESS_ROAM_ATMOSPHERE_LEVEL_REWARD_HAS_GET	= 1924,		// 跨服氛围值奖励已领取
	ERROR_FASHION_DRESS_ROAM_CG_SOLICIT_VOTES_INDEX_ERR			= 1925,		// 跨服cg拉票索引错误
	ERROR_FASHION_DRESS_ROAM_BET_TIME_ERR						= 1926,		// 跨服竞猜时间错误
	ERROR_FASHION_DRESS_ROAM_BET_TYPE_ERR						= 1927,		// 跨服竞猜时间错误
	ERROR_FASHION_DRESS_ROAM_BET_MONEY_NOT_ENOUGH				= 1928,		// 跨服竞猜钱不够
	ERROR_FASHION_DRESS_ROAM_BET_HAS_BET						= 1929,		// 跨服竞猜已经竞猜了
	ERROR_FASHION_DRESS_ROAM_BET_TARGET_SCHEME_NOT_CG			= 1930,		// 跨服竞猜目标方案错误
	ERROR_FASHION_DRESS_VOTE_LEVEL_LIMIT						= 1931,		// 投票等级限制
	ERROR_FASHION_DRESS_END								= 1939,

	// 白帝神宫
	ERROR_BDSG_COMMON_ERR										= 1941,
	ERROR_BDSG_NOT_TEAM_LEADER									= 1942,		// 不是队长
	ERROR_BDSG_NOT_ONLINE										= 1943,		// 有队员不在线
	ERROR_BDSG_NOT_TEAM_FOLLOW									= 1944,		// 有队员没有组队跟随
	ERROR_BDSG_ENROLL_PERIOD_ERR								= 1945,		// 报名，阶段错误
	ERROR_BDSG_HAS_ENROLL										= 1946,		// 报名，已经有人报名过
	ERROR_BDSG_ENTER_BATTLE_PERIOD_ERR							= 1947,		// 进入战场，阶段错误
	ERROR_BDSG_ENTER_BATTLE_NOT_ENROLL							= 1948,		// 进入战场，有人没有报名
	ERROR_BDSG_ENTER_BATTLE_NOT_MATCH							= 1949,		// 进入战场，没有匹配
	ERROR_BDSG_ENTER_BATTLE_TIMEOUT								= 1950,		// 进入战场，超时(5分钟之内)
	ERROR_BDSG_ENTER_BATTLE_CONFIRM_TIMEOUT						= 1951,		// 进入战场确认，回复超时
	ERROR_BDSG_ENTER_BATTLE_CONFIRM_NOT_AGREE					= 1952,		// 进入战场确认，不同意
	ERROR_BDSG_ENTER_BATTLE_ONCE								= 1953,		// 进入战场，只能进入一次
	ERROR_BDSG_ENROLL_ROAM_NO_QUALIFICATION						= 1954,		// 报名，跨服没有资格
	ERROR_BDSG_BATTLE_NOT_GOING									= 1955,		// 战场没有开始
	ERROR_BDSG_ENROLL_CONFIRM_TIMEOUT							= 1956,		// 报名确认，回复超时
	ERROR_BDSG_ENROLL_CONFIRM_NOT_AGREE							= 1957,		// 报名确认，不同意
	ERROR_BDSG_JOIN_LEVEL_LIMIT									= 1958,		// 参加等级限制
	ERROR_BDSG_END												= 1960,

	ERROR_INTIMATE_FASHION_DRESS_COMMON_ERR						= 1961,	// 羁绊时装搭配
	ERROR_INTIMATE_FASHION_DRESS_NOT_ONLINE						= 1963,	// 羁绊时装搭配，对方不在线
	ERROR_INTIMATE_FASHION_DRESS_NOT_INTIMATE					= 1965,	// 羁绊时装搭配，不是羁绊
	ERROR_INTIMATE_FASHION_DRESS_NAME_SIZE						= 1967,	// 羁绊时装搭配，名字长度
	ERROR_INTIMATE_FASHION_DRESS_NAME_SENSITIVE					= 1968,	// 羁绊时装搭配，敏感词


	ERROR_ACR_BEGIN												= 1970,		// 匿名聊天室错误码占位
	ERROR_ACR_ROLE_DATA_NOT_INIT								= 1971,		// 玩家还没有设置外形相关数据
	ERROR_ACR_CREATE_ROOM_DATA_INVALID							= 1972,		// 无效数据
	ERROR_ACR_CREATE_ROOM_DATA_LOST								= 1973,		// 缺少数据
	ERROR_ACR_INVALID_THEME										= 1974,		// 无效主题
	ERROR_ACR_INVALID_MAX_ROLE_NUM								= 1975,		// 无效最大成员数
	ERROR_ACR_INVALID_KEEP_TIME									= 1976,		// 无效持续时长
	ERROR_ACR_TOO_MUCH_TAGS										= 1977,		// 标签数量超上限
	ERROR_ACR_NOT_ENOUGH_MONEY_OR_ITEM							= 1978,		// 钱币或消耗品不足
	ERROR_ACR_NOT_IN_ROOM										= 1979,		// 不在房间内
	ERROR_ACR_SERVICE_BUSY										= 1980,		// 服务器正忙
	ERROR_ACR_ROOM_NOT_EXSIT									= 1981,		// 房间不存在
	ERROR_ACR_HAVE_VALID_ROOM									= 1982,		// 存在有效房间
	ERROR_ACR_ALREADY_IN_ROOM									= 1983,		// 当前正在房间中
	ERROR_ACR_CAN_NOT_CLOSE_OTHER_ROLE_ROOM						= 1984,		// 不能关闭别人的房间
	ERROR_ACR_DISPLAY_NAME_INVALID								= 1985,		// 外显名字包含敏感字符
	ERROR_ACR_DISPLAY_DATA_LOST									= 1986,		// 外显数据缺失
	ERROR_ACR_INVALID_DISPLAY_DATA								= 1987,		// 无效外显数据
	ERROR_ACR_ROOM_NAME_OR_TAGS_INVALID							= 1988,		// 房间名字或Tags包含敏感字符
	ERROR_ACR_ROOM_NO_EMPTY_POS									= 1989,		// 房间满了
	ERROR_ACR_PASSWD_INVALID									= 1990,		// 密码无效
	ERROR_ACR_REQUEST_DATA_LOST									= 1991,		// 请求中数据丢失
	ERROR_ACR_CAN_NOT_OP_OTHER_ROLE_ROOM						= 1992,		// 不能操作别人的房间
	ERROR_ACR_CREATE_SUCCESS_BUT_ENTER_FAIL                     = 1993,     // 房间创建成功，但进入失败
	ERROR_ACR_END												= 1999,		// 匿名聊天室结束

	ERROR_CST_BEGIN												= 2000,		// 跨服组队开始
	ERROR_CST_SERVER_BUSY										= 2000, 	// 服务器忙
	ERROR_CST_SELF_HAS_IN_TEAM									= 2001, 	// 您当前已在跨服队伍中
	ERROR_CST_SELF_NOT_IN_TEAM									= 2002, 	// 您当前不在跨服队伍中
	ERROR_CST_TARGET_HAS_IN_TEAM								= 2003, 	// 该玩家已经有跨服队伍了
	ERROR_CST_TARGET_NOT_IN_TEAM								= 2004, 	// 对方当前不在跨服队伍中
	ERROR_CST_HAS_APPLY_THIS_TARGET								= 2005, 	// 您已发送跨服组队申请
	ERROR_CST_TARGET_TEAM_FULL									= 2006, 	// 跨服队伍已满
	ERROR_CST_TARGET_REFUSE_YOUR_APPLY							= 2007, 	// 对方拒绝了您的跨服组队申请
	ERROR_CST_TARGET_REFUSE_YOUR_INVITE							= 2008, 	// 对方拒绝了您的跨服组队邀请
	ERROR_CST_HAS_INVITE_THIS_TARGET							= 2009, 	// 您已发送跨服组队邀请
	ERROR_CST_YOUR_TEAM_FULL									= 2010, 	// 您的跨服队伍已经满员了
	ERROR_CST_YOU_ARE_NOT_LEADER								= 2011, 	// 您不是队长
	ERROR_CST_CAN_NOT_DO_THIS									= 2012, 	// 不能执行该操作
	ERROR_CST_DIFF_PVE_CENTER									= 2013, 	// 您和目标将在不同PVE服，不能执行该操作
	// 中间预留几个
	ERROR_CST_END												= 2019,		// 跨服组队结束
	ERROR_MAP_PERSON_FULL										= 2031,		// 地图人数已满

	ERROR_BM_INVALID_EXTRA_ITEM									= 2020,		// 该物品不能用于提升高阶物品产出概率
	ERROR_BM_INVALID_ITEM										= 2021,		// 该物品不能用于投喂书虫
	ERROR_BM_KEEP_ITEM_NOT_ENOUGH								= 2022,		// 您没有该物品或该物品数量不足
	ERROR_BM_UNKOWN_ITEM										= 2023,		// 不能投喂未知物品
	ERROR_BM_INVALID_QUALITY									= 2024,		// 不能投喂该品质物品
	ERROR_BM_MONEY_NOT_ENOUGH									= 2025,		// 金币不足
	ERROR_BM_EXTRA_ITEM_NOT_ENOUGH								= 2026,		// 您当前没有和田玉
	ERROR_BM_BACK_PACKET_FULL									= 2027,		// 背包空间不足
	ERROR_BM_TOO_MUCH_ITEM										= 2028,		// 处理数量超上限
	ERROR_SBW_ITEM_NOT_ENOUGH									= 2029,		// 道具不足，不能祝福
	ERROR_SBW_INVALID_CONTEXT                                   = 2030,		// 无效的祝福语内容
	ERROR_SBW_SENSITIVE_CONTENT                                 = 2106,		// 祝福语存在敏感字符
	ERROR_SBW_NOT_EXITS											= 2032,		// 祝福语不存在或已经失效
	ERROR_SBW_MSG_LENGTH_ERR									= 2033,		// 请输入正确长度的祝福语
	ERROR_SBW_TASK_FINISH_OR_NOT_EXIST							= 2034,		// 任务已完成或不存在

	ERROR_HG_SELF_HAS_GARDEN									= 2035,	// 已有花园
	ERROR_HG_SELF_LEVEL_LIMIT									= 2036,	// 等级不足
	ERROR_HG_SELF_NOT_LEADER									= 2037,	// 不是队长
	ERROR_HG_SELF_APPLYING										= 2038,	// 存在未结束的申请
	ERROR_HG_MEMBER_NOT_ONLIEN									= 2039,	// 有队伍成员不在线
	ERROR_HG_MEMBER_ROAM_OUT									= 2040,	// 有队伍成员正在跨服
	ERROR_HG_MEMBER_LEVEL_LIMIT									= 2041,	// 有队伍成员等级不足
	ERROR_HG_MEMBER_HAS_GARDEN									= 2042,	// 有队伍成员已有花园
	ERROR_HG_MEMBER_NOT_FOLLOW									= 2043,	// 有队伍成员不在跟随
	ERROR_HG_MEMBER_APPLYING									= 2044,	// 有队伍成员存在未结束的申请
	ERROR_HG_CANNOT_REPEAT_OP									= 2045,	// 不能重复操作
	ERROR_HG_TEAM_INFO_CHANGE									= 2046,	// 队伍信息发生变化
	ERROR_HG_SERVER_BUSY										= 2047,	// 服务器忙，请稍后重试
	ERROR_HG_INVALID_NAME										= 2048,	// 名称包含屏蔽字
	ERROR_HG_SELF_NO_GARDEN										= 2049,	// 当前没有花园
	ERROR_HG_TARGET_NOT_MEMBER									= 2050,	// 目标不是花园所属者
	ERROR_HG_MEMBER_NOT_NEAR									= 2051,	// 有队伍成员不在附近
	ERROR_HG_NAME_TOO_LONG										= 2052,	// 名字超长
	ERROR_HG_TARGET_NO_GARDEN									= 2053,	// 目标当前没有花园
	ERROR_HG_TARGET_OFFLINE_NOT_LIMIT							= 2054,	// 目标离线时间未超过限制
	ERROR_HG_LEADER_NOT_ONLINE									= 2055,	// 队长不在线
	ERROR_HG_OWNERS_NUM_LIMIT									= 2056,	// 所属者人数超上限
	ERROR_HG_SELF_IS_LEADER										= 2057,	// 自己已经是队长了
	ERROR_HG_TARGET_HAS_GARDEN									= 2058, // 目标已经有花园
	ERROR_HG_LEADER_REFUSE_APPLY								= 2059, // 队长拒绝了你的申请
	ERROR_HG_NOTFRIEND											= 2060, // 不是好友
	ERROR_HG_GARDEN_VISITOR_FULL								= 2061,
	ERROR_HG_TARGET_ONLINE										= 2062,	// 目标在线
	ERROR_HG_NO_ENOUGH_MONEY									= 2063, // 货币不足

	ERROR_HG_PARAM_ERROR            							= 2064, // 参数错误
	ERROR_HG_NO_GARDEN              							= 2065, // 没有花园（所有操作）
	ERROR_HG_GARDEN_DATA_UNLOAD									= 2066,	// 没有加载花园数据
	ERROR_HG_GARDEN_DATA_ERROR      							= 2067, // 花园数据异常（所有操作）
	ERROR_HG_GOLD_NOT_ENOUGH        							= 2068, // 花园币不够
	ERROR_HG_TICKET_NOT_ENOUGH      							= 2069, // 点券不足
	ERROR_HG_EXPAND_LEVEL_ERROR     							= 2070, // 扩建等级错误
	ERROR_HG_EXPAND_NO_CONFIG       							= 2071, // 没有扩建配置
	ERROR_HG_EXPAND_GARDEN_LEVEL_NOT_ENOUGH 					= 2072, // 花园等级达不到扩建等级
	ERROR_HG_EXPAND_GARDEN_GOLD_NOT_ENOUGH  					= 2073, // 扩建时金币不足
	ERROR_HG_EXPAND_GARDEN_ITEM_NOT_ENOUGH 						= 2074,	// 扩建道具不足
	ERROR_HG_ITEM_NOT_ENOUGH  									= 2075, // 物品不足
	ERROR_HG_NO_ITEM_TEMPLATE                   				= 2076, // 没有找到花园物品模板
	ERROR_HG_NO_PLANT_TEMPLATE									= 2077,	// 没有找到花园植物模板
	ERROR_HG_BUYITEM_LEVEL_NOT_ENOUGH           				= 2078, // 花园等级达不到购买物品需要的等级
	ERROR_HG_BUYITEM_PRICE_ERROR                				= 2079, // 购买物品时价格错误（物品价格配置为0）
	ERROR_HG_SPECIAL_EVENT_CONFIG_ERROR         				= 2080, // 没有这个特殊事件的配置
	ERROR_HG_SPECIAL_EVENT_LIMIT                				= 2081, // 特殊事件商品购买限制（特殊事件过期或者达到购买上限）
	ERROR_HG_OTHERS_GARDEN_ERROR            					= 2082, // 加载他人花园数据错误
	ERROR_HG_SLEFS_GARDEN_ERROR         						= 2083, // 加载自己花园数据错误
	ERROR_HG_PLANT_NOT_SEED										= 2084,	// 该物品不是种子，种不了
	ERROR_HG_PLANT_GROUND_ERROR									= 2085,	// 土地没开，种不了
	ERROR_HG_PLANT_NO_GROUND									= 2086,	// 没有土地配置，种不了
	ERROR_HG_PLANT_GRID_ERROR									= 2087,	// 种的格子不对，种不了
	ERROR_HG_PLANT_GRID_ALREADY_HAVE_PLANT 						= 2088,	// 这个格子已经有东西了，种不了
	ERROR_HG_PICK_NO_GROUND										= 2089,	// 收花的土地不对，收不了
	ERROR_HG_PICK_NO_GRID										= 2090, // 收花的格子不对，收不了
	ERROR_HG_PICK_NO_RIPE										= 2091, // 没成熟呢，收不了
	ERROR_HG_STEAL_NO_GROUND									= 2092, // 偷的土地不对，没有土地，偷不了
	ERROR_HG_STEAL_NO_GRID										= 2093,	// 偷的格子不对，没有这个格子，偷不了
	ERROR_HG_STEAL_NO_RIPE										= 2094,	// 没成熟，偷不了
	ERROR_HG_STEAL_NO_PLANT_T									= 2095,	// 偷的植物不对，找不到这个植物的模板配置了
	ERROR_HG_STEAL_ALREADY_STEAL								= 2096, // 你已经偷过这个了
	ERROR_HG_STEAL_MAX_STEAL									= 2097,	// 这个植物已经被偷完了
	ERROR_HG_NEGATIVE_CONFIG_ERROR								= 2098, // 消除不良状态配置不对
	ERROR_HG_NEGATIVE_NO_GROUND									= 2099, // 消除不良状态土地不对
	ERROR_HG_NEGATIVE_NO_GRID									= 2100, // 消除不良状态的格子不对
	ERROR_HG_NEGATIVE_ITEM_NOT_ENOUGH							= 2101, // 消除不良状态的道具不足
	ERROR_HG_NEGATIVE_NO_OP										= 2102, // 不需要消除不良状态
	ERROR_HG_NEGATIVE_HELP_LIMIT_MAX							= 2103, // 已经达到最大帮助他人的次数了
	ERROR_HG_DESTROY_NO_GROUND									= 2104, // 铲除时传的土地不对
	ERROR_HG_DESTROY_NO_GRID									= 2105, // 铲除时传的格子不对
	ERROR_HS_BARGAIN_REFUSED									= 2106,	// 砍价被拒绝

	ERROR_TR_DB_CHECK_FAILED                                    = 2107, // 校验失败
	ERROR_TR_TEAM_NOT_EXIST										= 2108, // 队伍不存在
	ERROR_TR_APPLY_FULL											= 2109, // 申请列表已满
	ERROR_TR_NOT_IN_TEAM										= 2110, // 不在队伍中
	ERROR_TR_NOT_LEADER											= 2111, // 不是队长
	ERROR_TR_INVLID_APPLY_INFO                                  = 2112, // 无效的申请信息
	ERROR_TR_MEMBER_FULL										= 2113, // 成员已满
	ERROR_TR_TARGET_HAS_TEAM                                    = 2114, // 目标已有队伍
	ERROR_TR_INVALID_NAME										= 2115, // 名字非法
	ERROR_TR_HAS_APPLY											= 2116, // 已经申请
	ERROR_TR_IS_LEADER											= 2117, // 队长不能离开
	ERROR_TR_HAS_RECHARGE										= 2118, // 已充值，不能离开
	ERROR_TR_HAS_TEAM											= 2119, // 已有队伍
	ERROR_TR_ACCOUNT_OTHER_ROLE_HAS_TEAM						= 2120, // 账号其他角色已有队伍
	ERROR_TR_COOLDOWN											= 2121, // 操作冷却中请稍后重试
	ERROR_TR_ACTIVITY_NOT_OPEN									= 2122, // 活动未开启或已结束
	ERROR_TR_NAME_USED											= 2123, // 小队名称已被占用

	ERROR_DARABASE_ERROR                                        = 2150, //数据加载失败
	ERROR_REFUSE_TRANSFER_GOLDENINTIMATE                        = 2151, //拒绝转本服羁绊
	ERROR_HARMONIOUS_LEVEL_LOW                                  = 2152, //等级不足
	ERROR_HARMONIOUS_BOTH_INTEAM_NEED                           = 2153, //队伍配置不对（人数或者没有队伍
	ERROR_HARMONIOUS_TEAM_LEADER_NEED                           = 2154, //不是队长
	ERROR_HARMONIOUS_NOT_TEAMFOLLOW                             = 2155, //非跟随状态
	ERROR_HARMONIOUS_NOT_PVE									= 2155, //玩家不在中心服


	ERROR_GD_GOODS_DISCOUNT_INFO_CHANGE							= 2161, // 商品折扣信息发生变化
	ERROR_GD_INVALID_GOODS										= 2162,	// 商品无效，未知商品
	ERROR_GD_GOODS_NUM_LIMIT									= 2163,	// 折扣商品数量已达上限
	ERROR_GD_NO_THIS_GOODS_DISCOUNT_INFO						= 2164,	// 没有该打折商品
	ERROR_GD_GOODS_DISCOUNT_LIMIT								= 2165,	// 商品折扣达到上限
	ERROR_GD_GOODS_ASSIST_LIMIT									= 2166,	// 商品被助力次数达到上限
	ERROR_GD_ITEM_NOT_ENOUGH									= 2167,	// 打折券不足
	ERROR_GD_INVALID_TICKET										= 2168,	// 打折券无效，未知物品
	ERROR_GD_THIS_TICKET_CANNOT_USE2TARGET						= 2169,	// 自用打折券不能用于他人
	ERROR_GD_DB_BUSY											= 2160,	// 数据库正忙，请稍后重试
	ERROR_GD_GOODS_HAS_BUY										= 2171,	// 商品已被购买，不能助力
	ERROR_GD_ACTIVITY_NOT_OPEN									= 2172,	// 活动未开启或已关闭
	ERROR_GD_GOODS_HAS_ASSIST									= 2173,	// 已助力过该玩家的此商品
	ERROR_GD_MONEY_NOT_ENOUGH									= 2174,	// 点券不足
	ERROR_GD_GOODS_HAS_IN_DISCOUNT_LIST							= 2175,	// 该商品已进入折扣列表

	ERROR_HG_NOT_MEMBER											= 2176, // 不是花园管理者
	ERROR_HG_SCENE_NOT_SUPPORT_THIS_OP							= 2177, // 当前场景不支持该操作
	ERROR_HG_BOUQUET_NEED_FLOWNER_LIMIT							= 2178, // 制作花束必须得5朵花
	ERROR_HG_MAKE_BOUQUET_MUST_FLOWER							= 2179, // 制作花束必须使用花朵
	ERROR_HG_BACK_PACKET_FULL									= 2180, // 背包空间不足

	ERROR_DELETE_ACCOUNT_MARRIGE								=	2195, // 婚姻关系还未解除

	ERROR_CORPS_UNION_BEGIN										= 2200,	// 帮派联盟错误码占位
	ERROR_CORPS_UNION_INVALID_CORPS								= 2200,	// 帮派联盟无效的帮派
	ERROR_CORPS_UNION_APPLY_COUNT_MAX							= 2201,	// 帮派联盟申请数量已经达到上限
	ERROR_CORPS_UNION_APPLY_DUPLICATE							= 2202,	// 帮派联盟重复申请
	ERROR_CORPS_UNION_COUNT_MAX									= 2203,	// 帮派联盟数量已经达到上限
	ERROR_CORPS_UNION_ALREADY_IN								= 2204,	// 帮派联盟已经处于联盟中
	ERROR_CORPS_UNION_NOT_IN_APPLY_LIST							= 2205,	// 帮派联盟未处于申请列表
	ERROR_CORPS_UNION_NOT_ALIGN									= 2206, // 帮派联盟未处于联盟中
	ERROR_CORPS_UNION_END										= 2230,	// 帮派联盟错误码占位

	ERROR_PLAYER_FUNC_FORBID_MODIFY								= 2231, // 禁止修改相关文本

	ERR_ZSPACE_HAS_ZPACE                                        = 2300, //已经有zspace空间了
	ERR_ZSPACE_LOW_ROLE_LEVEL                                   = 2301, //玩家等级过低
	ERR_ZSPACE_NOT_EXIST                                        = 2302, //z空间不存在
	ERR_ZSPACE_LEVEL_LOW                                        = 2303, //z空间扩展等级不足
	ERR_ZSPACE_PLACE_ITEM_FULL                                  = 2304, //在空间某一块墙放置物品过多
	ERR_ZSPACE_LESS_ITEM                                        = 2305, //缺少物品
	ERR_ZSPACE_OVER_ITEM 									    = 2306, //物品超过上限
	ERR_ZSPACE_NOT_ITEM                       					= 2307, //物品不存在
	ERR_ZSPACE_NOT_MONEY                                        = 2308, //钱不够
	ERR_ZSPACE_ERROR_OPERATOR_ID                                = 2309, //错误操作id
	ERR_ZSPACE_ALREAD_UPLOAD_BULLETIN                           = 2310, //已经上传过公告了
	ERR_ZSPACE_UPLOAD_BULLETIN_EXPRIE		                    = 2312, //
	ERR_ZSPACE_IN_BULLETIN_OPERATION      						= 2313, //正在操作中
	ERR_ZSPACE_BAN_UPLOAD_BULLETIN_EXPRIE     					= 2314, //禁止上传公告

	ERROR_UNKOWN_OP												= 2320,	// 未知操作
	ERROR_ROAM_COMMUNITY_BAD_WARDS								= 2321,	// 敏感字
	ERROR_ROAM_COMMUNITY_SELF_HAS_JOIN							= 2322,	// 自己当前已有社团
	ERROR_ROAM_COMMUNITY_SELF_NOT_JOIN							= 2323,	// 自己当前未在社团中
	ERROR_ROAM_COMMUNITY_TARGET_HAS_JOIN						= 2324,	// 目标当前已有社团
	ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN						= 2325,	// 目标当前未在社团中
	ERROR_ROAM_COMMUNITY_CREATE_FAILED							= 2326,	// 创建失败
	ERROR_ROAM_COMMUNITY_INIT_FAILED							= 2327,	// 初始化失败
	ERROR_ROAM_COMMUNITY_TARGET_OFFLINE							= 2328,	// 目标不在线
	ERROR_ROAM_COMMUNITY_NOT_EXIST								= 2329,	// 社团不存在
	ERROR_ROAM_COMMUNITY_NOT_SERVICE							= 2330,	// redis返回错误
	ERROR_ROAM_COMMUNITY_MEMBER_FULL							= 2331,	// 社团人数已满
	ERROR_ROAM_COMMUNITY_TARGET_NOT_JOIN_THIS					= 2332,	// 目标不在本社团中
	ERROR_ROAM_COMMUNITY_NOT_LEADER								= 2333,	// 不是团长
	ERROR_ROAM_COMMUNITY_LEADER_CANNOT_LEAVE					= 2334,	// 团长不能退出
	ERROR_ROAM_COMMUNITY_TARGET_REFUSE_INVITE					= 2335,	// 对方拒绝了邀请
	ERROR_ROAM_COMMUNITY_TARGET_REFUSE_APPLY					= 2336,	// 对方拒绝了申请
	ERROR_ROAM_COMMUNITY_HAS_OTHER_MEMBER						= 2337,	// 还有其他人，不能解散
	ERROR_ROAM_COMMUNITY_NO_THIS_APPLY_INFO						= 2338,	// 没有该玩家申请记录
	ERROR_ROAM_COMMUNITY_HAS_APPLY_INFO							= 2339,	// 已经申请了
	ERROR_ROAM_COMMUNITY_REFUSE_INVITE							= 2340,	// 对方拒绝了邀请
	ERROR_ROAM_COMMUNITY_MODIFY_ICON_CD							= 2341,	// 修改图标cd中
	ERROR_ROAM_COMMUNITY_MODIFY_ANNOUNCE_CD						= 2342,	// 修改公告cd中
	ERROR_ROAM_COMMUNITY_NAME_HAS_USED							= 2343,	// 名字被占用
	ERROR_ROAM_COMMUNITY_RESOURCE_NOT_ENOUGH					= 2344, // 资源不足
	ERROR_ROAM_COMMUNITY_CREATE_TIME_LIMIT						= 2345, // 创建时间不满足
	ERROR_ROAM_COMMUNITY_JOIN_TIME_LIMIT						= 2346, // 加入时间不满足
	ERROR_ROAM_COMMUNITY_INVALID_POINT_INDEX					= 2347, // 非法index
	ERROR_ROAM_COMMUNITY_CANNOT_VOTE_SELF_POINT					= 2348, // 不能投自己的据点
	ERROR_ROAM_COMMUNITY_CANNOT_VOTE_FOR_NO_LINK				= 2349, // 连通点中没有自己的据点
	ERROR_ROAM_COMMUNITY_AREADLY_VOTE_OTHER_POINT				= 2350, // 已经投过其他据点了
	ERROR_ROAM_COMMUNITY_CAN_NOT_VOTE_PVE						= 2351, // 已经投过其他据点了
	ERROR_ROAM_COMMUNITY_NOT_VOTE_STATE							= 2352, // 当前不是投票阶段
	ERROR_ROAM_COMMUNITY_SEASON_NOT_OPEN						= 2353, // 赛季未开始或已结束
	ERROR_ROAM_COMMUNITY_NO_RANK								= 2354, // 无法领取奖励，该社团没有战场排名
	ERROR_ROAM_COMMUNITY_NO_RANK_REWARD							= 2355, // 不可领取或已领取
	ERROR_ROAM_COMMUNITY_NOT_BATTLE_STATE						= 2356, // 当前不是战场阶段
	ERROR_ROAM_COMMUNITY_NOT_SELF_RC_BATTLE						= 2357, // 不是自己圣殿的战场
	ERROR_ROAM_COMMUNITY_FUNC_NOT_OPEN							= 2358, // 功能码未开启
	ERROR_ROAM_COMMUNITY_BATTLE_FINISHED						= 2359, // 战场已结束
	ERROR_ROAM_COMMUNITY_ENTER_INTERVAL_LIMIT					= 2360, // 进入副本冷却中
	ERROR_ROAM_COMMUNITY_BATTLE_STATE_CAN_NOT_ENHANCE			= 2361, // 当前阶段不能提升防御
	ERROR_ROAM_COMMUNITY_CAN_NOT_ENHANCE_OTHER_POINT			= 2362, // 不能提升其他所属者据点防御
	ERROR_ROAM_COMMUNITY_CAN_NOT_ENHANCE_PVE					= 2363, // 不能提升PVE
	ERROR_ROAM_COMMUNITY_INVALID_RESOURCE_NUM					= 2364, // 无效的资源数
	ERROR_ROAM_COMMUNITY_CREATE_INSTANCE_TIME_LIMIT				= 2365, // 创建副本时间限制
	ERROR_ROAM_COMMUNITY_NOT_FIND_RANK_DATA						= 2366, // 暂未发现该排行数据
	ERROR_ROAM_COMMUNITY_BATTLE_REPU_NOT_ENOUGH					= 2367, // 用于领取战场奖励的声望不足
	ERROR_ROAM_COMMUNITY_SEASON_REPU_NOT_ENOUGH					= 2368, // 用于领取赛季奖励的声望不足
	ERROR_ROAM_COMMUNITY_NOT_SELF_RC							= 2369, // 不是自己的跨服圣殿
	ERROR_ROAM_COMMUNITY_REWARD_ARREADY_PICK					= 2370, // 已经领取过奖励
	ERROR_ROAM_COMMUNITY_MAX_ADD_NUM_LIMIT						= 2371, // 活跃度增加资源到上限了
	ERROR_ROAM_COMMUNITY_CUR_STATE_CAN_NOT_GET_REWARD			= 2372, // 当前阶段不能领奖
	ERROR_ROAM_COMMUNITY_HAS_JOIN_OTHER_PVE_BATTLE				= 2373, // 骑士团已经参加其他pve据点
	ERROR_ROAM_COMMUNITY_VOTE_POINT_LIMIT						= 2374, // 本周已投据点已达上限
	ERROR_ROAM_COMMUNITY_CAN_VOTE_LESS_THAN_OTHER				= 2375, // 不能比当前别人投的少
	ERROR_ROAM_COMMUNITY_OCCUPY_NUM_LIMIT                       = 2376, // 已占领据点已达最大值
	ERROR_ROAM_COMMUNITY_THIS_LEVEL_OCCUPY_NUM_LIMIT            = 2377, // 当前等级据点占领已达最大值
	ERROR_ROAM_COMMUNITY_THIS_LEVEL_VOTE_NUM_LIMIT              = 2378, // 当前等级据点已投已达最大值

	ERROR_HUNDRED_GUESS_ALREADY       = 2400,  // 玩家已经对该场比赛竞猜
	ERROR_HUNDRED_GUESS_COUNT_LIMIT   = 2401,  // 玩家当前赛季已经达到竞猜上线
	ERROR_HUNDREDE_GUESS_WRONG_STATE   = 2402,  // 玩家在错误的状态进行竞猜
	ERROR_HUNDRED_GUESS_WRONG_TIME    = 2403,  //
	ERROR_HUNDRED_GUESS_WRONG_MONEY   = 2404,  //
	ERROR_HUNDRED_GUESS_NO_ORDER      = 2405,  //
	ERROR_HUNDRED_GUESS_WRONG_ID      = 2406,  //
	ERROR_HUNDRED_GUESS_NO_GUESS      = 2407,  //
	ERROR_HUNDRED_GUESS_ALREADY_AWARD = 2408,  //
	ERROR_HUNDRED_GUESS_AWARD_WRONG_STATE = 2409,  //
	ERROR_HUNDRED_GUESS_NO_RESULT     = 2410,
	ERROR_HUNDRED_GUESS_NO_AWARD      = 2411,
	ERROR_HUNDRED_ASSIST_ALREADY      = 2412,
	ERROR_HUNDRED_PUNIISH             = 2413, //处于惩罚时间


	// ERROR_REJECT_ANONYMOUS_GIFT								= 2413, // 拒收匿名礼物
	ERROR_REJECT_SECRET_MSG										= 2414, // 拒收好感留言
	ERROR_REJECT_SOCIAL_SPACE_ANONYMOUS_GIFT					= 2415, // 拒收朋友圈匿名礼物

	ERROR_BLESSING_INFO_SENSITIVE_CONTENT						= 2416, // 送花留言存在敏感内容
	ERROR_CENTER_ARENA_PROF_13_LIMIT 							= 2417,	// 中心服组队竞技场职业13限制
	ERR_ZSPACE_ERROR_COLLECTION_UNLOCK 				= 2418, //手办尚未解锁
	ERR_ZSPACE_ERROR_COLLECTION_REPEATED 				= 2419, //手办重复使用
	ERROR_CENTER_ARENA_PROF_14_LIMIT 							= 2420,	// 中心服组队竞技场职业14限制
	ERROR_CENTER_ARENA_PROF_15_LIMIT 							= 2421,	// 中心服组队竞技场职业15限制
	ERROR_CENTER_ARENA_PROF_16_LIMIT 							= 2422,	// 中心服组队竞技场职业16限制
	ERROR_CENTER_ARENA_PROF_17_LIMIT 							= 2423,	// 中心服组队竞技场职业17限制
	ERROR_CENTER_ARENA_PROF_18_LIMIT 							= 2424,	// 中心服组队竞技场职业18限制


	ERROR_MOUNT_SPACE_SELF_NO_ZSPACE							= 2430,	// 当前自己没有z空间
	ERROR_MOUNT_SPACE_TARGET_NO_ZSPACE							= 2431,	// 当前目标没有z空间
	ERROR_MOUNT_SPACE_SELF_NO_SPACE								= 2432,	// 当前自己没有幻影空间，这个应该是服务器错误
	ERROR_MOUNT_SPACE_INVALID_PARAM								= 2433,	// 协议参数错误
	ERROR_MOUNT_SPACE_INVALID_MOUNT_TID							= 2434,	// 非法的幻化id
	ERROR_MOUNT_SPACE_THIS_INDEX_NOT_FIND_SURFACE				= 2435,	// 改位置没有放置坐骑

	ERROR_GAUCTION_TARGETED_SALES_INVALID_ITEM					= 2450,	// 拍卖行，定向出售，非法物品
	ERROR_GAUCTION_TARGETED_SALES_INVALID_CATEGORY				= 2451,	// 拍卖行，定向出售，非法类型
	ERROR_GAUCTION_TARGETED_SALES_COMMON_USE_LIMIT				= 2452,	// 拍卖行，定向出售，公共限次限制
	ERROR_GAUCTION_TARGETED_SALES_CATEGORY_USE_LIMIT			= 2453,	// 拍卖行，定向出售，类型限次限制
	ERROR_GAUCTION_TARGETED_SALES_VIP_LEVEL_LIMIT				= 2454,	// 拍卖行，定向出售，VIP等级限制
	ERROR_GAUCTION_TARGETED_SALES_TOKEN_ITEM_NOT_ENOUGH			= 2455,	// 拍卖行，定向出售，令牌物品不足

	ERROR_CROSS_MULTI_PVP_ACTIVITY_NOT_OPEN						= 2460,	// 活动没开
	ERROR_CROSS_MULTI_PVP_NOT_BATTLE_STATE						= 2461,	// 当前不是战场阶段
	ERROR_CROSS_MULTI_PVP_SERVER_LEVEL_LIMIT					= 2462,	// 服务器等级不满足条件
	ERROR_CROSS_MULTI_PVP_BATTLE_NOT_CREATE						= 2463,	// 战场未开启
	ERROR_CROSS_MULTI_PVP_INVALID_BATTLE_ORDER					= 2464,	// 对阵表index非法
	ERROR_CROSS_MULTI_PVP_BATTLE_FINISHED						= 2465,	// 战场已经结束
	ERROR_CROSS_MULTI_PVP_NOT_BATTLE_SERVER						= 2466,	// 不是参战双方
	ERROR_CROSS_MULTI_PVP_SELF_ENTER_NUM_LIMIT					= 2467,	// 己方参战人数已满
	ERROR_CROSS_MULTI_PVP_PLAYER_LEVEL_LIMIT					= 2468,	// 玩家等级限制
	ERROR_CROSS_MULTI_PVP_PUNISH_TIME							= 2469,	// 退出惩罚状态

	ERROR_ROLE_TRADE_ERROR_STATUS_CHANGE 					= 2500, // [角色交易]错误的状态转换
	ERROR_ROLE_TRADE_RECOVER_BACKUP_DATA_FAILED	 			= 2501, // [角色交易]恢复备份数据失败
	ERROR_DELETE_ACCOUNT_HAVE_TRADE_ROLE					= 2502, // 账号注销时有正在交易中的角色
	ERROR_CREATE_ROLE_HAVE_TRADE_ROLE					= 2503, // 账号创角时有正在交易中的角色
	ERROR_ROLE_TRADE_ON_SHELF							= 2504, // 不可登陆了已上架角色交易平台的角色 

	ERROR_ARENA_MUTEX_REPU 							= 2510, // 参与竞技场时互斥声望不为0
	ERROR_ARENA_GROUP_JOIN_COOL_LIMIT				= 2511,	// 退出浩瀚天梯战队冷却期
	ERROR_ARENA_GROUP_TARGET_JOIN_COOL_LIMIT		= 2512,	// 目标退出浩瀚天梯战队冷却期
	ERROR_ARENA_GROUP_ONLY_LEADER_CAN_INVITE		= 2513,	// 浩瀚天梯战队只有队长可以邀请
	ERROR_ARENA_GROUP_DEC_SCORE						= 2514, // 退出战队扣分


	ERROR_DRESSUP_PARTY_FASHION_NOT_ACTIVE						= 2520,		// 时装没有激活
	ERROR_DRESSUP_PARTY_FASHION_NOT_EXPIRE						= 2521,		// 时装不是永久
	ERROR_DRESSUP_PARTY_COMMON_ERR								= 2522,		// 通用错误
	ERROR_DRESSUP_PARTY_PART_COLOR_NOT_ACTIVE					= 2523,		// 色块没有激活
	ERROR_DRESSUP_PARTY_SCHEME_SCORE_NOT_EQUAL					= 2524,		// 时装方案分数错误
	ERROR_DRESSUP_PARTY_ACTIVITY_NOT_OPEN						= 2525,		// 活动未开
	ERROR_DRESSUP_PARTY_DANCE_NO_TARGET							= 2526,		// 双人共舞目标不能为空
	ERROR_DRESSUP_PARTY_DANCE_MUST_DRESSUP						= 2527,		// 双人共舞双方必须上传方案
	ERROR_DRESSUP_PARTY_DANCE_MUST_INTIMATE						= 2528,		// 双人共舞双方必须是羁绊
	ERROR_DRESSUP_PARTY_DANCE_HAS_JOIN_DIFF_TARGET				= 2529,		// 双人共舞成员已经和别人参加了
	ERROR_DRESSUP_PARTY_DANCE_LEADER_HAS_JOIN_DIFF_TARGET		= 2530,		// 双人共舞队长已经和别人参加了
	ERROR_DRESSUP_PARTY_DANCE_TARGET_NOT_ONLINE					= 2531,		// 双人共舞对方不在线
	ERROR_DRESSUP_PARTY_DANCE_TARGET_REFUSE						= 2532,		// 双人共舞对方拒绝
	ERROR_DRESSUP_PARTY_DANCE_INVITE_TIMEOUT					= 2533,		// 双人共舞邀请超时
	ERROR_DRESSUP_PARTY_NOT_JOIN_DRESSUP						= 2534,		// 没有参与个人搭配
	ERROR_DRESSUP_PARTY_HAS_GET_JOIN_DRESSUP					= 2535,		// 已领取个人搭配参与奖
	ERROR_DRESSUP_PARTY_NOT_JOIN_DANCE							= 2536,		// 没有参与双人共舞
	ERROR_DRESSUP_PARTY_HAS_GET_JOIN_DANCE						= 2537,		// 已领取双人共舞参与奖
	ERROR_DRESSUP_PARTY_GIVE_GIFT_INVALID_PARAM					= 2538,		// 送礼无效参数
	ERROR_DRESSUP_PARTY_GIVE_GIFT_ITEM_NOT_ENOUGH				= 2539,		// 送礼物品不足
	ERROR_DRESSUP_PARTY_GIVE_GIFT_TIME_LIMIT					= 2540,		// 送礼次数不足
	ERROR_DRESSUP_PARTY_GIVE_GIFT_INVALID_TARGET				= 2541,		// 无效目标
	ERROR_DRESSUP_PARTY_UPLOAD_SCHEME_TIME_LIMIT				= 2542,		// 上传次数不足
	ERROR_DRESSUP_PARTY_HAS_JOIN								= 2543,		// 已经参加过了
	ERROR_DRESSUP_PARTY_ALL_ACTIVITY_CLOSE						= 2544,		// 所有场次标志活动都没开
	ERROR_DRESSUP_PARTY_RECV_THIS_ITEM_GIFT_MAX_TIMES			= 2545,		// 该物品接收次数已达上限

	ERROR_NEW_AUCTION_ROAM_NOT_PVE								= 2550,		// SERVER收到了应该PVE服处理的内容
	ERROR_NEW_AUCTION_ROAM_NOT_SERVER							= 2551,		// PVE服收到了应该SERVER处理的内容
	ERROR_NEW_AUCTION_ROAM_FUNC_CLOSE							= 2552,		// 功能码没开
	ERROR_NEW_AUCTION_ROAM_INVALID_ITEMID						= 2553,		// 非法物品tid
	ERROR_NEW_AUCTION_ROAM_INVALID_COUNT						= 2554,		// 非法物品数量
	ERROR_NEW_AUCTION_ROAM_INVALID_PRICE						= 2555,		// 非法物品价格
	ERROR_NEW_AUCTION_ROAM_INVALID_ELAPSE_TIME_TYPE				= 2556,		// 达到当前店铺等级的上架时间上限??
	ERROR_NEW_AUCTION_ROAM_ALLOC_ID_FAILED						= 2557,		// 申请id失败
	ERROR_NEW_AUCTION_ROAM_TARGET_EMPTY							= 2558,		// 跨服上架，主动操作只有定向售卖，目标为空
	ERROR_NEW_AUCTION_ROAM_INVALID_AUCTIONID					= 2559,		// 跨服购买，auctionid为空
	ERROR_NEW_AUCTION_ROAM_CAN_NOT_BUY_SELF						= 2560,		// 跨服购买，不能购买自己上架的
	ERROR_NEW_AUCTION_ROAM_LEFT_NOT_ENOUGH						= 2561,		// 跨服购买，数量不足
	ERROR_NEW_AUCTION_ROAM_END_TIME_LIMIT						= 2562,		// 跨服购买，过了购买期
	ERROR_NEW_AUCTION_ROAM_SHOW_TIME_LIMIT						= 2563,		// 跨服购买，还在公示期
	ERROR_NEW_AUCTION_ROAM_UNMARSHAL_DETAIL_FAILED				= 2564,		// 跨服上架，GNewAuctionDetail结构unmarshal失败
	ERROR_NEW_AUCTION_ROAM_TARGET_SERVER_NOT_ONLINE				= 2565,		// 目标服务器不在线

	ERROR_SHARE_BOX_INVALID_QUALITY								= 2570,		// 非法宝箱类型
	ERROR_SHARE_BOX_SHARE_LIMIT									= 2571,		// 分享达上限
	ERROR_PVE_ADD_LOCAL_FRIEND_LIMIT							= 2572,		// PVE服不能加本服好友

	ERROR_ROUGE_NOT_IN_TEAM										= 2600,		// rouge本报名没处于队伍中
	ERROR_ROUGE_NOT_TEAM_LEADER									= 2601,		// rouge本报名不是队长
	ERROR_ROUGE_WRONG_MEMBER_COUNT								= 2602,		// rouge本报名队伍人数不符
	ERROR_ROUGE_MEMBER_NOT_AROUND								= 2603,		// rouge本报名队伍成员不在线
	ERROR_ROUGE_ALREADY_MATCHED									= 2604,     // rouge本报名成员报名重复
	ERROR_ROUGE_OTHER_ALREADY_MATCHED							= 2605,     // rouge本报名成员报名其余战场
	ERROR_ROUGE_MEMBER_DIFF_LV_UNMATCHED						= 2606,     // rouge选择难度时成员不满足条件
	ERROR_ROUGE_MEMBER_LV_UNMATCHED								= 2607,     // rouge本报名成员不满足等级条件

	ERROR_MULT_CHAIJIE_FUNC_CLOSE								= 2608,		// 功能吗没开
	ERROR_MULT_CHAIJIE_INVALID_LOCATION							= 2609,		// 非法背包类型
	ERROR_MULT_CHAIJIE_INVENTORY_EMPTY							= 2610,		// 背包物品为空
	ERROR_MULT_CHAIJIE_INVALID_INDEX							= 2611,		// 非法索引
	ERROR_MULT_CHAIJIE_CAN_NOT_DECOMPOSE						= 2612,		// 物品不能分解
	ERROR_MULT_CHAIJIE_DECOMPOSE_ITEM_EMPTY						= 2613,		// 分解物品为空
	ERROR_MULT_CHAIJIE_INVALID_DECOMPOSE_ITEM					= 2614,		// 分解物品非法
	ERROR_MULT_CHAIJIE_SLOT_NOT_ENOUGH							= 2615,		// 背包空间不足

	ERROR_INVALID_TEAM_TYPE										= 2616,		// 非法队伍类型

	ERROR_END											= 2596,	// 占位
};

const int INTIMATE_FASHION_DRESS_SIZE = 5;
const int INTIMATE_FASHION_DRESS_INFO_NAME_SIZE = 6;

enum BREED_CUTE_PET_RETURN_HOMETOWN_OP
{
	BCPRH_OP_NONE										= 0,
	BCPRH_OP_ADD										= 1,	// 放回家园
	BCPRH_OP_CANCEL										= 2,	// 取消放回家园
	BCPRH_OP_UPDATE										= 3,	// 更新属性

};

enum HometownRoommateErrCode
{
	ERR_HOMETOWN_ROOMMATE_SUCCESS          = 0,
	ERR_HOMETOWN_ROOMMATE_FAIL             = 1,
	ERR_HOMETOWN_ROOMMATE_TARGET_OFFLINE   = 2, // 对方离线
	ERR_HOMETOWN_ROOMMATE_NO_HOME          = 3, // 无家园
	ERR_HOMETOWN_ROOMMATE_INVALID_PASSWORD = 4, // 密码无效
	ERR_HOMETOWN_ROOMMATE_DOOR_INVALID     = 5, // 无效的密码门
	ERR_HOMETOWN_ROOMMATE_NOT_FRIEND       = 6, // 非好友
	ERR_HOMETOWN_ROOMMATE_DST_ACCEPT       = 7, // 对方已接受
	ERR_HOMETOWN_ROOMMATE_DST_REFUSE       = 8, // 对方已拒绝
	ERR_HOMETOWN_ROOMMATE_COUNT_LIMIT      = 9, // 邀请好友已达上限
	ERR_HOMETOWN_ROOMMATE_SAME_PASSWORD    = 10, // 相同的密码
	ERR_HOMETOWN_ROOMMATE_INVITE_COOLDOWN  = 11, // 邀请冷却中
};

enum GAME_ACTIVE_CARD_USE_ERR
{
	GAME_ACTIVE_CARD_USE_ERR_SUCCESS				= 0,	// 成功
	GAME_ACTIVE_CARD_USE_ERR_CARD_NO_ACTIVE			= 1,	// 卡未激活
	GAME_ACTIVE_CARD_USE_ERR_CARD_NO_EXPIRE			= 2,	// 卡未到生效时间
	GAME_ACTIVE_CARD_USE_ERR_CARD_HAS_EXPIRE		= 3,	// 卡已过期失效
	GAME_ACTIVE_CARD_USE_ERR_CARD_HAS_USE_SUCCESS	= 4,	// 成功，卡已被该帐号在该服务器成功用过
	GAME_ACTIVE_CARD_USE_ERR_CARD_HAS_INVILD		= 5,	// 卡已被该帐号在该服务器用过，但卡所在批次已被置为无效
	GAME_ACTIVE_CARD_USE_ERR_CARD_HAS_USE_ERR		= 6,	// 卡已被该帐号在其他服务器用过
	GAME_ACTIVE_CARD_USE_ERR_CARD_HAS_USE_INVILD	= 7,	// 卡已被其他帐号用过
	GAME_ACTIVE_CARD_USE_ERR_CARD_NUM_ERR			= 8,	// 卡号不存在
	GAME_ACTIVE_CARD_USE_ERR_CSP_ERR				= 9,	// 其他错误(CSPProvider数据库异常)
	GAME_ACTIVE_CARD_USE_ERR_NET_ERR				= 10,	// 网络通信错误
	GAME_ACTIVE_CARD_USE_ERR_CARD_ERR				= 11,	// 卡号并不是此游戏的激活码
	GAME_ACTIVE_CARD_USE_ERR_ACCOUNT_ERR			= 12,	// 帐号格式错误
	GAME_ACTIVE_CARD_USE_ERR_SERVERID_ERR			= 13,	// 参数serverid与CSP中的session的serverid不匹配
	GAME_ACTIVE_CARD_USE_ERR_USER_NO_ACTIVE			= 14,	// 此玩家未在任何服务器激活
};
const time_t LOGIN_ACTIVE_CODE_TIME_OUT = 600;				// 输入激活码超时时间s

// 给CSP回复的DataBetweenCspAndGame_Re的错误码
enum CSP_RE_ERR
{
	CSP_RE_SUCCESS 						= 0,
	CSP_RE_ACCOUNT_INVALID 				= 10001,
	CSP_RE_PASSWORD_ERROR				= 10002,
	CSP_RE_COMMON_ERR					= 10003,
	CSP_RE_ACCOUNT_EXIST				= 10004,
	CSP_RE_REGISTER_FAILED				= 10005,
	CSP_RE_ACCOUNT_NOT_EXIST			= 10006,
	CSP_RE_COMMUNICATION_ERROR			= 10007,
	CSP_RE_PRIVILIEGE_ERROR				= 10008,
	// 游戏中基本只需要用这下面的错误码，上面的是CSP内部错误，写在这里仅仅为了参考。
	CSP_RE_PARAMETER_ERROR				= 10009,
	CSP_RE_DB_ERROR						= 10010,
	CSP_RE_QUARTZ_ERROR					= 10011,
	CSP_RE_DATE_ERROR					= 10012,
	CSP_RE_TIMEOUT_ERROR				= 10013,
	CSP_RE_ENCODEING_ERROR				= 10014,
	CSP_RE_NOT_CONNECT_TO_CSPSERVER		= 10015,
	CSP_RE_NOT_CONNECT_TO_GAMESERVER	= 10016,
	CSP_RE_ROLE_NOT_EXIST				= 10017,
	CSP_RE_SERVER_NOT_EXIST				= 10018,
	CSP_RE_JSON_PARSE_ERROR				= 10019,
	CSP_RE_MAIL_DUPLICATE_ERROR			= 10020,
	CSP_RE_IO_ERROR						= 10021,
	CSP_RE_ATTACHMENT_ERROR				= 10022,
	CSP_RE_VERIFY_ERROR					= 10023,
	CSP_RE_VERIFY_PARAMETER_ERROR		= 10024,
	CSP_RE_PARSE_PARAMETER_ERROR		= 10025,
	CSP_RE_GAME_ACCOUNT_NOT_EXIST		= 10026,
	CSP_RE_MAILBOX_FULL					= 10027,
	CSP_RE_ROLE_NOT_ONLINE			= 10028,
	CSP_RE_CORPS_NOT_EXIST			= 20057,
	CSP_RE_ALREADY_HAS_BEEN_DEPOSITED   = 20058,
};

// 个人空间平台返回的错误码，仅列举服务器相关的部分
enum SSP_RE_ERR
{
	SSP_RE_SUCESS			= 0,
	SSP_RE_PARAMETER_INVALID	= 40001,
	SSP_RE_STORE_FAILED		= 40004,
	SSP_RE_QUERY_FAILED		= 40005,
	SSP_RE_LOGIN_FAILED		= 40006,
	SSP_RE_USER_EXISTED		= 40007,
	SSP_RE_AWARDEE_ERROR		= 40008,
	SSP_RE_GAMEID_NOT_EXIST		= 40009,
	SSP_RE_COMMON_ERROR		= 40010,
	SSP_RE_JSON_ERROR		= 40011,
	SSP_RE_NOT_CONNECT_TO_GAMESERVER = 40014,
	SSP_RE_TIMEOUT_ERROR		= 40015,
	SSP_RE_NOT_CONNECT_TO_CSPSERVER	= 40016,
	SSP_RE_ROLE_NOT_EXIST		= 40022,
	SSP_RE_SPACE_NOT_OPEN		= 40023,
	SSP_RE_ALREDY_STEP_SPACE	= 40034,
	SSP_RE_OWNER_NOT_EXIST		= 40035,
	SSP_RE_INQUIRER_NOT_EXIST	= 40036,
	SSP_RE_GIFT_NOT_ENOUGH		= 40037,
	SSP_RE_PENDING			= 40038,
};

enum IDIP_RE_ERR
{
	IDIP_ERR_SUCCESS		= 0,
	IDIP_ERR_INVALID_ROLE           = 1, // 角色不存在
	IDIP_ERR_PARAM			= 2001, // 参数错误
	IDIP_ERR_INVALID_ACCOUNT	= 2003, // 账号错误
	IDIP_ERR_DB_NOTFOUND		= 2004, // 数据库错误
	IDIP_ERR_INVALID_ID		= 2005, // id 错误
	IDIP_ERR_ROLE_OFFLINE		= 2006, // 角色不在线
	IDIP_ERR_MAILBOX_FULL		= 2007, // 邮箱已满
	IDIP_ERR_CASH_NOT_ENOUGH	= 2008, // 点券不足
	IDIP_ERR_INVALID			= 2009, // 功能不可用

	IDIP_ERR_ROLE_TRADE_FORBID 	= 3001, //封号或禁言
	IDIP_ERR_ROLE_TRADE_LEVEL 	= 3002, //等级不够
	IDIP_ERR_ROLE_TRADE_SERVER_TIME	= 3003, //服务器开服时间不够
	IDIP_ERR_ROLE_TRADE_CORPS_MASTER= 3004, //不能是社团社长
	IDIP_ERR_ROLE_TRADE_AUCTION 	= 3005, //交易行未清空
	//IDIP_ERR_ROLE_TRADE_GS_AUCTION 	= 3006, //有物品在拍卖行出售
	IDIP_ERR_ROLE_TRADE_FULL_ROLE	= 3007, //角色数量已满
	IDIP_ERR_ROLE_TRADE_USER_WAIT_DETELE	= 3008, //账号注销流程中
	IDIP_ERR_ROLE_TRADE_FUNC_CLOSE	= 3009, //功能未开放
	IDIP_ERR_ROLE_TRADE_IN_PURSUE 	= 3010, //角色处于追缴状态
	
};

enum IDIP_TASK_STATUS
{
	IDIP_TASK_FINISH = 0,
	IDIP_TASK_NOT_FINISH = 1,
	IDIP_TASK_NOT_ACCEPT = 2,
};

enum MAIL_ALL_USER_TYPE
{
	MA_USER_OLD = 1, //只对邮件发送前注册玩家有效
	MA_USER_NEW = 2, //只对邮件发送后注册玩家有效
	MA_USER_BOTH = 3, //对所有玩家有效
};

enum
{
	BATTLE_STUB_TYPE_SINGLE = 1,	//单人竞技场
	BATTLE_STUB_TYPE_TEAM = 2,		//组队竞技场
	BATTLE_STUB_TYPE_MINIGAME = 3,		//小游戏对战
	BATTLE_STUB_TYPE_ELIMINATE  = 4,    //淘汰赛
	BATTLE_STUB_TYPE_CENTER_TEAM  = 5,    //跨服组队竞技场
	BATTLE_STUB_TYPE_CENTER_SINGLE = 6,    //跨服单人竞技场
};

enum
{
	CORPS_BATTLE_STUB_NONE = 0,
	CORPS_BATTLE_STUB_TYPE_COMMON = 1,	//普通帮派竞赛
	CORPS_BATTLE_STUB_TYPE_CITY = 2,	//帮派领土战
	CORPS_BATTLE_STUB_TYPE_TOWER = 3,	//帮派爬塔
	CORPS_BATTLE_STUB_TYPE_CENTER = 4,	//跨服帮派竞赛
	CORPS_BATTLE_STUB_TYPE_SERVER = 5,	//跨服服务器对战
	CORPS_BATTLE2_STUB_TYPE_COMMON = 6,	//普通帮派竞赛2
	CORPS_BATTLE_STUB_TYPE_ELIMINATE = 7,   //淘汰赛
	CORPS_BATTLE3_STUB_TYPE_COMMON = 8,		//普通帮派竞赛3
	CORPS_BATTLE3_STUB_TYPE_CENTER = 9,		//跨服帮派竞赛3
	CORPS_BATTLE_STUB_TYPE_CENTER_TOWER = 10,  //跨服社团爬塔
	CORPS_BATTLET_STUB_TYPE_CENTER    = 11, //百团大战

	CORPS_BATTLE_STUB_TYPE_COUNT,		//帮派竞赛类型数量
};
static bool IsLocalCorpsBattle(int battle_type)
{
	return (battle_type == CORPS_BATTLE_STUB_TYPE_COMMON || battle_type == CORPS_BATTLE_STUB_TYPE_CITY || battle_type == CORPS_BATTLE_STUB_TYPE_TOWER || battle_type == CORPS_BATTLE2_STUB_TYPE_COMMON || battle_type == CORPS_BATTLE3_STUB_TYPE_COMMON);
}
static bool IsCenterCoprsBattle(int battle_type)
{
	return (battle_type == CORPS_BATTLE_STUB_TYPE_CENTER || battle_type == CORPS_BATTLE3_STUB_TYPE_CENTER || battle_type == CORPS_BATTLE_STUB_TYPE_CENTER_TOWER || battle_type == CORPS_BATTLET_STUB_TYPE_CENTER);
}
static int CheckAndGetCorpsBattleCenterType(int battle_type)
{
	if (battle_type == CORPS_BATTLE_STUB_TYPE_COMMON)
	{
		return CORPS_BATTLE_STUB_TYPE_CENTER;
	}
	else if (battle_type == CORPS_BATTLE3_STUB_TYPE_COMMON)
	{
		return CORPS_BATTLE3_STUB_TYPE_CENTER;
	}
	else if (battle_type == CORPS_BATTLE_STUB_TYPE_TOWER)
	{
		return CORPS_BATTLE_STUB_TYPE_CENTER_TOWER;
	}
	return CORPS_BATTLE_STUB_NONE;
}
static int CheckAndGetCorpsBattleLocalType(int center_battle_type)
{
	if (center_battle_type == CORPS_BATTLE_STUB_TYPE_CENTER)
	{
		return CORPS_BATTLE_STUB_TYPE_COMMON;
	}
	else if (center_battle_type == CORPS_BATTLE3_STUB_TYPE_CENTER)
	{
		return CORPS_BATTLE3_STUB_TYPE_COMMON;
	}
	else if (center_battle_type == CORPS_BATTLE_STUB_TYPE_CENTER_TOWER)
	{
		return CORPS_BATTLE_STUB_TYPE_TOWER;
	}
	return CORPS_BATTLE_STUB_NONE;
}

// 帮派竞赛最低进入等级
const int CORPS_BATTLE_ENTER_LEVEL = 50;

enum
{
	PLAYER_GENDER_MALE = 0, // 男性
	PLAYER_GENDER_FEMALE = 1, // 女性
};

enum
{
	PLAYER_RACE_HUMAN = 0,	//人类
	PLAYER_RACE_ELF = 1,	//妖族

	PLAYER_RACE_MAX,
};

enum VOTE_RESULT
{
	VOTE_RE_AGREE = 0, // 同意
	VOTE_RE_DISAGREE = 1, // 不同意
	VOTE_RE_MUTE = 2, // 弃权
};

enum PLAYER_MESSAGE_ID
{
	PMID_PEEK_YOUR_PROFILE	= 1, // 有人在查看你的名片
	PMID_FAMILY_CREATE	= 2, // 建立结义
	PMID_FAMILY_ADD		= 3, // 结义加人
	PMID_FAMILY_CHANGENAME  = 4, // 结义改名
	PMID_FAMILY_NICKNAME	= 5, // 改个人结义名
	PMID_FAMILY_EXPEL	= 6, // 结义开人
	PMID_MARRIAGE_MARRY	= 7, // 结婚成功
	PMID_MARRIAGE_PROPOSE	= 8, // 订婚成功
	PMID_HOME_ACTION	= 9, // 家园操作
	PMID_PEEK_YOUR_EQUIP	= 10,//有人查看你的装备
	PMID_HOME_BUY		= 11,//获取家园成功
	PMID_WEDDING		= 12,//婚礼通知
	PMID_PARADING		= 13,//婚礼巡游
	PMID_OLDPLAYER_BACK	= 14,//老玩家通知在线好友
	PMID_ROLERENAME		= 15,//角色改名
};

enum GROUP_SERVER_ID
{
	GSI_INVALID		= 0,	//不可用
	GSI_FACTION_MASTER	= 1,	//帮主群

	GSI_COUNT,
};

//玩家计数器ID
enum COUNTER_ID
{
	COUID_BLESS		= 1,	//好友花票计数器
	//队伍成员关心的计数器id
	COUID_TEAM_MEMBER_MIN = 9,
	COUID_TEAM_MEMBER_MAX = 12,
	COUID_TEAM_MEMBER_COUNT = COUID_TEAM_MEMBER_MAX - COUID_TEAM_MEMBER_MIN + 1,
	// COUTER_ID 245~255 人格系统占用
	COUID_PERSONALITY_COLOR_MIN = 236,
	COUID_PERAONALITY_COLOR_MAX = 245,
};

enum REPUTATION_ID
{
	REPUID_CHARM		= 0,	// 社交魅力
	REPUID_SECT		= 2,	// 师德
	REPUID_FACTION_CONTRIBUTION = 7,	// 帮派贡献度
	REPUID_ACTIVITY		= 10,	// 活力值
	REPUID_VIP		= 11,	//vip声望
	REPUID_MONTH_ACTIVITY	= 12,	//月活跃度
	REPUID_DAY_ACTIVITY	= 13,	//日活跃度
	REPUID_FLOWER		= 16,	//收到鲜花累计
	REPUID_FLOWER_WEEK	= 17,	//会循环清空的鲜花累计
	REPUID_FLOWER_SEND	= 19,	//送出鲜花累计
	REPUID_FLOWER_SEND_WEEK	= 20,	//会循环清空的送花累计

	REPUID_DOUBLE_EXP_GOT   = 23,   //已领取的双倍经验点数

	REPUID_DISPLAY_MIN	= 32,	//最多能够显示的声望值
	REPUID_DISPLAY_MAX	= 32,	//最多能够显示的声望值

	//REPUID_JUEWEI           = 33,  // 爵位排名声望
	REPUID_LADDER_PROGRESS							= 34,	// 屠龙考核进度
	REPUID_LADDER_TIME								= 35,	// 屠龙考核完成时间
	REPUID_LADDER_HISTORY_HIGHEST_RANK				= 36,	// 屠龙考核历史最高排名
	REPUID_LADDER_HISTORY_HIGHEST_RANK_HAS_GET		= 37,	// 屠龙考核历史最高已领取档次
	REPUID_LADDER_FIRST_CLEARANCE_HIGHEST			= 38,	// 屠龙考核通关最高关卡
	REPUID_LADDER_FIRST_CLEARANCE_HIGHEST_HAS_GET	= 39,	// 屠龙考核通关已领取奖励关卡

	REPUID_MAIL_SEND	= 46,	//每日赠送物品数量上限
	REPUID_CENTER_ARENA_TEAM_BATTLE_LAST_RANK = 47, // 上月排名
	REPUID_CENTER_ARENA_TEAM_BATTLE_HIGHEST = 48, // 本月最高历史积分
	REPUID_GUAJI_FENGMO	= 50,	// 挂机封魔是否消耗双倍点数
	REPUID_RETINUE_SOUL	= 53,	// 仆从魂玉
	REPUID_CENTER_ARENA_TEAM_BATTLE		= 54,	// 跨服组队天梯积分
	REPUID_CENTER_ARENA_TEAM_BATTLE_LAST_MONTH		= 55,	// 跨服组队天梯上月积分
	REPUID_CHILD_BLESS_LUCKY_POINT	= 56,	// 孩子祝福幸运值
	REPUID_CENTER_ARENA_TEAM_BATTLE_LAST_MONTH_COUNT	= 58,	// 跨服组队天梯上月场次
	REPUID_FACTION_RECURIMENT       = 62,//社团招聘计次
	REPUID_FACTION_NEWS_EDITOR      = 63,//社团编辑计次
	REPUID_FACTION_CONTRI_ALL 	= 64,//历史帮贡
	REPUID_FACTION_CONTRI_FROZEN 	= 65,//冻结帮贡
	REPUID_FACTION_CONTRI_CUR_WEEK 	= 66,//本周帮贡
	REPUID_FACTION_CONTRI_LAST_WEEK = 67,//上周帮贡
	REPUID_FLYSWORD		= 68,	//飞行所需声望
	REPUID_MONTH_SIGN_IN_COMMON_REMAIN_NUM = 69,//月签到补签 非vip剩余次数
	REPUID_MAIL_RECV	= 70,		//每日接收物品数量上限
	REPUID_ARENA_SCORE	= 71,		//竞技场累积积分
	REPUID_HUNT_DRAGON	= 75,		//猎龙声望
	REPUID_CORPS_CITY_BATTLE = 84,	//帮派攻城战每周计次
	REPUID_CONJUGAL		= 93,	//夫妻恩爱值
	REPUID_CONJUGAL_WEEK	= 94,	//夫妻周恩爱值
	REPUID_CONTEST		= 96,	//答题声望
	REPUID_MARRIAGE_GIFT	= 97,	//结婚领奖声望
	REPUID_PRECREATE_SHARE_COUNT = 100,	//预创建分享成功记次
	REPUID_CORPS_TOWER_SCORE = 102,	//社团爬塔活动积分
	REPUID_CORPS_TOWER	= 103,	//是否参与社团爬塔活动
	REPUID_FREE_FIGHT	= 105,	//大乱斗杀人声望
	REPUID_CORPS_RACE_PLAYER_WIN_TIMES = 110, // 社团赛跑玩家获得冠军的次数
	REPUID_SOCIAL_SPACE_GIFT_HISTORY = 114, //空间送礼历史排行
	REPUID_RETRIEVE_COMMON_REMAIN_NUM = 118, //奖励找回 非VIP剩余次数
	REPUID_BOOK				= 120,	//读书声望
	REPUID_REVENGE_BATTLE	= 121,	//仇杀约战记次周声望
	REPUID_SECT_GRADUATE	= 126,	//记录出师人数的声望

	REPUID_TOPBATTLE_MAX	= 134,	// 镜像竞技场最高积分

	REPUID_HOMETOWN		= 149,	// 家园声望
	REPUID_BASKETBALL_GOAL_DAILY	= 150,	//篮球每日进球数
	REPUID_BASKETBALL_CONSECUTIVE_GOAL_DAILY	= 151,	//篮球每日连续进球数
	REPUID_BASKETBALL_CONSECUTIVE_GOAL_TOTAL	= 152,	//篮球累计连续进球数，有未投中就清空
	REPUID_BASKETBALL_CONSECUTIVE_MISS_TOTAL	= 153,	//篮球累计连续未进球数，有投中就清空
	REPUID_BASKETBALL_INSTANCE_MASK	= 157,	//篮球在副本中使用的掩码声望
	REPUID_TEAM_ARENA_SCORE = 191,   //组队竞技场积分
	REPUID_TEAM_ARENA_MONTH_COUNT = 192, //组队竞技场月参与次数
	REPUID_SOCIAL_SPACE_GIFT = 199, //空间送礼本周排行

	REPUID_IS_ROAM		= 200,	//是否跨服
	REPUID_SHANYUAN_LIMIT_1 = 215,  //善缘值日限制 - 地狱挑战
	REPUID_SHANYUAN_LIMIT_2 = 216,  //善缘值日限制 - 炼妖
	REPUID_TREASURE_LOFT    = 217,  //珍宝阁转盘奖励声望
	REPUID_GOLDEN_INTIMATE_MAX = 220, //羁绊值最大值
	REPUID_FASHION_BEAUTY_POINT = 228, // 时装靓丽度
	REPUID_LOGIN_DAYS		= 229,	//角色登录天数

	REPUID_TEAMRAND_LEVEL_S_TOTAL = 241, // 组队随机奖励开出S级奖励的总次数
	REPUID_TEAMRAND_LEVEL_SS_TOTAL = 242, // 组队随机奖励开出SS级奖励的总次数
	REPUID_TEAMRAND_LEVEL_S_SERIES = 243, // 组队随机奖励连续开出S级奖励的次数
	REPUID_TEAMRAND_LEVEL_SS_SERIES = 244, // 组队随机奖励连续开出SS级奖励的次数

	REPUID_DAY_ACTIVITY_FACTOR_EXP = 247,//本日获得的活动周期性经验，份数
	REPUID_DAY_FACTOR_EXP	= 248,	//本日获得的周期性经验
	REPUID_ACCOUT_OLD_PLAYER = 249, //账号老玩家 用于老玩家回归功能
	REPUID_IS_OLD_PLAYER	= 250,	//是否老玩家

	REPUID_DAY_BIND_MONEY   = 251,  // 本日获得的绑定金
	REPUID_DAY_MONEY        = 252,  // 本日获得的流通金
	REPUID_DAY_BIND_CASH    = 253,  // 本日获得的绑定元宝
	REPUID_DAY_EXP          = 254,  // 本日获得的经验
	REPUID_DAY_INIT_LEVEL   = 255,  // 本日初始经验

	REPUID_OPEN_CORPS_FUNC	= 288,	// 开启社团功能声望，创建社团，加入社团需要检查该声望，数值有策划配任务设置
	REPUID_ADVENTURE_TASK_POINT = 289,	// 奇遇任务点数

	REPUID_CAREER_SCORE		= 309,	//身份积分
	REPUID_QUARTER_CARD		= 345,	// 季卡勋章

	REPUID_CAR_RACE_JOIN_TIMES	= 351,	//赛车参加计次
	REPUID_CAR_RACE_PVP_BEST_RECORD_WEEKLY = 352, // 赛车pvp周最佳成绩与0xFFFFFFF的差值

	REPUID_ROAM_BEGIN		= 400,	//跨服声望开始
	REPUID_CORPS_RACE_FINISHED 	= 408, //帮派赛跑完成了活动计数

	REPUID_NEW_SECT_TEACH		= 426,	// 新师徒传道值
	REPUID_NEW_SECT_GROW		= 427,	// 新师徒成长值
	REPUID_SELL_REWARD 		= 428,	//sell_reward限次，付费购买经验

	REPUID_ROAM_ARENA_TEAM_WEEK_COUNT = 440, // 本周场次
	REPUID_ROAM_ARENA_TEAM_MONTH_COUNT = 441, // 本月场次
	REPUID_CAR_RACE_PVP_BEST_RECORD_FOREVER = 443, // 赛车PVP玩家终身最佳成绩与0xFFFFFFF的差值

	REPUID_PUBG_SINGLE_WEEK = 445,//吃鸡，本周完成单排战场次数
	REPUID_PUBG_TEAM_WEEK = 446,//吃鸡，本周完成组排战场次数
	REPUID_PUBG_SINGLE_COUNT = 447,//吃鸡，累计参与单排战场次数
	REPUID_PUBG_TEAM_COUNT = 448,//吃鸡，累计参与组排战场次数
	REPUID_PUBG_SINGLE_WIN = 449,//吃鸡，单排战场吃鸡数
	REPUID_PUBG_TEAM_WIN = 450,//吃鸡，组排战场吃鸡数
	REPUID_PUBG_SINGLE_KILL = 451,//吃鸡，单排战场杀人数
	REPUID_PUBG_TEAM_KILL = 452,//吃鸡，组排战场杀人数
	REPUID_PUBG_SINGLE_ASSIST = 453,//吃鸡，单排战场助攻数
	REPUID_PUBG_TEAM_ASSIST = 454,//吃鸡，组排战场助攻数
	REPUID_PUBG_SINGLE_ALIVE = 455,//吃鸡，单排战场累计存活秒数
	REPUID_PUBG_TEAM_ALIVE = 456,//吃鸡，组排战场累计存活秒数
	REPUID_PUBG_SINGLE_DAMAGE = 457, //吃鸡，单排战场累计伤害量
	REPUID_PUBG_TEAM_DAMAGE = 458, //吃鸡，组排战场累计伤害量
	REPUID_PUBG_SINGLE_HEAL = 459,//吃鸡，单排战场累计治疗量
	REPUID_PUBG_TEAM_HEAL = 460,//吃鸡，组排战场累计治疗量
	REPUID_PUBG_SINGLE_SCORE_WEEK = 461,//吃鸡，单排战场本周积分
	REPUID_PUBG_TEAM_SCORE_WEEK = 462,//吃鸡，组排战场本周积分
	REPUID_PUBG_SINGLE_SCORE = 463,//吃鸡，单排战场累计积分
	REPUID_PUBG_TEAM_SCORE = 464,//吃鸡，组排战场累计积分
	REPUID_PUBG_COUNT_WEEK = 465,//吃鸡，本周单排/组排总次数
	REPUID_PUBG_SINGLE_WIN_WEEK = 466,//吃鸡，本周单排战场吃鸡数
	REPUID_PUBG_TEAM_WIN_WEEK = 467,//吃鸡，本周组排战场吃鸡数
	REPUID_PUBG_JUST_WIN = 468,//吃鸡，本局是否吃鸡，1为单排，2为组排
	REPUID_PUBG_WIN_REWARD = 469,//吃鸡，奖章数
	REPUID_PUBG_WIN_REWARD_FRAGMENT = 470,//吃鸡，奖章碎片数
	REPUID_PUBG_TAG_TANGZHECHIJI = 472,//吃鸡标签，躺着吃鸡
	REPUID_PUBG_TAG_BAOZOUJIWANG = 473,//吃鸡标签，暴走鸡王
	REPUID_PUBG_TAG_GOUCHUYIPIANTIAN = 474,//吃鸡标签，苟出一片天
	REPUID_PUBG_TAG_LUODICHENGHE = 475,//吃鸡标签，落地成盒

	REPUID_PUBG_SINGLE_WELFARE_POINT = 476,//吃鸡，单人福利积分
	REPUID_PUBG_TEAM_WELFARE_POINT = 477,//吃鸡，组队福利积分
	REPUID_PUBG_SINGLE_WELFARE_POINT_DAILY = 478,//吃鸡，单人福利积分（每日）
	REPUID_PUBG_TEAM_WELFARE_POINT_DAILY = 479,//吃鸡，组队福利积分（每日）

	REPUID_PUBG_MEDAL 		 = 482,//吃鸡，正在使用的勋章，实际上是一个开关id
	REPUID_PUBG_SEASON_SCORE = 483,//吃鸡，赛季积分
	REPUID_PUBG_KILL_10_WIN  = 484,//吃鸡，击杀超过10人吃鸡
	REPUID_PUBG_QUALITY_4_EQUIP_NUM = 485,//吃鸡，获得橙装数量

	REPUID_PUBG_ALL_KILL = 487,//吃鸡，单排和组队战场杀人数
	REPUID_PUBG_RESCUE   = 488,//吃鸡, 累计营救次数
	REPUID_PUBG_ALL_HEAL = 489,//吃鸡，单排和组队战场累计治疗量
	REPUID_PUBG_ALL_DAMAGE = 490,//吃鸡，单排和组队战场累计伤害量
	REPUID_PUBG_ALL_ALIVE = 491, //吃鸡, 单排和组队战场累计存活秒数
	REPUID_PUBG_SEASON_AWARD = 496, //吃鸡, 吃鸡赛季奖励声望
	REPUID_PUBG_WIN_SEASON = 497,				//吃鸡，本赛季吃鸡数

	REPUID_ROAM_RESULT		= 499, // 记录跨服战斗最近16场的胜负
	REPUID_ROAM_END			= 499,	//跨服声望结束

	REPUID_TREASURE_LOFT_DICE_DAILY_COUNT		= 541,	// 星辰阶梯活动每日外圈抽奖次数
	REPUID_TREASURE_LOFT_DICE_DAILY_HAS_GET		= 542,	// 星辰阶梯活动每日已经领取的档次
	REPUID_TREASURE_LOFT_DICE_LOTTERY_ORDER		= 563,	// 星辰阶梯活动内圈抽奖顺序

	REPUID_VIDEO_GAME_DAILY_HURT		= 553,	// 每日绘梨衣的游戏机伤害量
	REPUID_VIDEO_GAME_DAILY_HAS_GET		= 554,	// 每日已经领取的档次
	REPUID_VIDEO_GAME_ENDLESS_MODE_LEVEL_AWARD_DAILY  = 564,	// 每日绘梨衣的游戏机无尽关卡奖励，高16位表示已领取次数，低16位表示可领取次数

	REPUID_DAILY_SALES_DAILY_DRAW_1 = 565,	// 每日促销抽取次数1
	REPUID_DAILY_SALES_DAILY_DRAW_2 = 566,	// 每日促销抽取次数2
	REPUID_DAILY_SALES_DAILY_DRAW_3 = 567,	// 每日促销抽取次数3
	REPUID_DAILY_SALES_DAILY_DRAW_4 = 568,	// 每日促销抽取次数4
	REPUID_DAILY_SALES_DAILY_DRAW_5 = 569,	// 每日促销抽取次数5
	REPUID_DAILY_SALES_DAILY_DRAW_6 = 570,	// 每日促销抽取次数6
	REPUID_DAILY_SALES_DAILY_DRAW_7 = 571,	// 每日促销抽取次数7
	REPUID_DAILY_SALES_DAILY_DRAW_8 = 573,	// 每日促销抽取次数8
	REPUID_DAILY_SALES_DAILY_DRAW_9 = 574,	// 每日促销抽取次数9
	REPUID_DAILY_SALES_DAILY_DRAW_10 = 575,	// 每日促销抽取次数10
	REPUID_DAILY_SALES_DAILY_DRAW_11 = 576,	// 每日促销抽取次数11
	REPUID_DAILY_SALES_DAILY_DRAW_12 = 577,	// 每日促销抽取次数12
	REPUID_DAILY_SALES_DAILY_DRAW_13 = 578,	// 每日促销抽取次数13
	REPUID_DAILY_SALES_DAILY_DRAW_14 = 579,	// 每日促销抽取次数14
	REPUID_DAILY_SALES_DAILY_DRAW_15 = 580,	// 每日促销抽取次数15
	REPUID_DAILY_SALES_DAILY_DRAW_16 = 581,	// 每日促销抽取次数16
	REPUID_DAILY_SALES_DAILY_DRAW_17 = 582,	// 每日促销抽取次数17
	REPUID_DAILY_SALES_DAILY_DRAW_18 = 583,	// 每日促销抽取次数18
	REPUID_DAILY_SALES_DAILY_DRAW_19 = 584,	// 每日促销抽取次数19
	REPUID_DAILY_SALES_DAILY_DRAW_20 = 585,	// 每日促销抽取次数20
	REPUID_DAILY_SALES_DAILY_DRAW_21 = 586,	// 每日促销抽取次数21
	REPUID_DAILY_SALES_DAILY_DRAW_22 = 587,	// 每日促销抽取次数22
	REPUID_DAILY_SALES_DAILY_DRAW_23 = 588,	// 每日促销抽取次数23
	REPUID_DAILY_SALES_DAILY_DRAW_24 = 790,	// 每日促销抽取次数24

	//REPUID_CORPS_LUCKY_VALUE_USED_TODAY = 598, // 社团幸运值今天是否用过
	REPUID_CORPS_LUCKY_VALUE = 599, // 社团幸运值

	REPUID_RETRIEVE_VIP_REMAIN_NUM = 600, //奖励找回 VIP剩余次数
	REPUID_MONTH_SIGN_IN_VIP_REMAIN_NUM = 601,//月签到补签 vip剩余次数

	REPUID_TSS_GIFTS_BUY_COUNT_TODAY	= 651,	//腾讯礼包今日购买次数
	REPUID_TSS_GIFTS_BUY_COUNT_YESTERDAY	= 653,	//腾讯礼包昨日购买次数
	REPUID_ARENA_BATTLE_CONTINUOUS_TIMES_1 = 654, // 竞技场连胜计次声望1
	REPUID_ARENA_BATTLE_CONTINUOUS_TIMES_2 = 655, // 竞技场连胜计次声望2
	REPUID_TSS_GIFTS_ADVANCE_BUY_COUNT_TODAY	= 657,	//腾讯高级礼包今日购买次数
	REPUID_FRIEND_AMITY_BEGIN	= 659, //好友度达成人数声望
	REPUID_FRIEND_AMITY_END		= 668,
	REPUID_CAREER_POPULAR		= 672,	// 小店人气
	REPUID_CAREER_FOUND			= 673,	// 小店资金
	REPUID_CAREER_SHOP_FREE		= 678,	// 小店免单券使用次数

	REPUID_ARENA_ZONE			= 670,	//当前战区
	REPUID_ARENA_ZONE_TIMESTAMP	= 671,	//战区所属的时间戳
	REPUID_ARENA_ZONE_LAST		= 693,	//上月战区

	REPUID_PLAYER_COMEBACK_FLAG = 694, // 玩家回归标志
	REPUID_HOMETOWN_PROSPERITY = 701, // 家园繁荣度（后改名为温馨度）
	REPUID_HOMETOWN_GIFT =  702, // 家园本周送礼赞赏值
	REPUID_PLAYER_COMEBACK_TS   = 703, // 玩家回归时间戳

	REPUID_OWNER_HOMETOWN = 704, // 玩家是否有家园
	REPUID_PLAYER_HOMETOWN_ARREARAGE	= 706,	//家园欠费时间(周)

	REPUID_IDIOM_SOLITAIRE		= 707,	//玩家最大成语接龙次数

	REPUID_HOMETOWN_GIFT_HISTORY = 709, // 家园历史送礼赞赏值

	REPUID_PUBG_SELECT			= 711,	//吃鸡战场选择的职业倾向
	REPUID_NEW_SERVER_OPEN_DAY_SCORE	= 712,	//新的开服天数奖励积分
	REPUID_WEEK_ACTIVITY		= 713,	//周累积活跃度
	REPUID_HOMETOWN_MONEY = 715, // 家园币
	REPUID_HOMETOWN_APPRECIATION = 719, // 家园总赞赏值

	REPUID_CAR_RACE_PVP_NO_SIMULATE = 725, // 深夜狂飙 胜利场数

	REPUID_ARENA_MINIGAME_WIN_COUNT = 727, // 小游戏对战连胜次数，失败清零，跨活动不清零
	REPUID_ARENA_MINIGAME_WIN_COUNT_MAX = 728, // 小游戏最大连胜次数，不清零

	REPUID_MORA			= 730,	//猜拳连胜次数

	REPUID_HOMETOWN_ONLY_GIFT_POPULARITY = 732, // 家园送礼物人气值
	REPUID_HOMETOWN_ONLY_GIFT_APPRECIATION = 733, // 家园收礼物赞赏值
	REPUID_HOMETOWN_VISITOR_COUNT      = 737, // 家园累计被拜访次数

	REPUID_INVITE_CODE_POINT 	= 741, //邀请码成功邀请获得积分

	REPUID_PUBG_TAG_TANGZHECHIJI_LOCAL = 749,//吃鸡标签，躺着吃鸡
	REPUID_PUBG_TAG_BAOZOUJIWANG_LOCAL = 750,//吃鸡标签，暴走鸡王
	REPUID_PUBG_TAG_GOUCHUYIPIANTIAN_LOCAL = 751,//吃鸡标签，苟出一片天
	REPUID_PUBG_TAG_LUODICHENGHE_LOCAL = 752,//吃鸡标签，落地成盒

	REPUID_SOUL_TREE_MAX_COUNT = 753,	// 最高誓约树果实数量

	REPUID_THANKS_GIVING_COUNTER	= 755,	//温情计划计次声望

	REPUID_HOMETOWN_PARTY	= 760,	//开启家园派对计次
	REPUID_HOMETOWN_PARTY1_LEVEL	= 761,	//家园派对类型1气氛等级
	REPUID_HOMETOWN_PARTY2_LEVEL	= 762,	//家园派对类型2气氛等级
	REPUID_HOMETOWN_PARTY3_LEVEL	= 763,	//家园派对类型3气氛等级
	REPUID_HOMETOWN_PARTY4_LEVEL	= 764,	//家园派对类型4气氛等级

	REPUID_HOMETOWN_DESIGN_RECOMMEND_CHANCE = 780, // 家园设计推荐券
	REPUID_HOMETOWN_DESIGN_RECOMMEND_VAL_HISTORY = 781, // 历史家园设计推荐度
	REPUID_FAIR_SWITCH = 791, //公平竞技场开关
	REPUID_FAIR_WEEK = 792, //公平竞技场跨周记录

	REPUID_SSP_COMPTITION_VOTE = 793,	//朋友圈比赛每周票数
	REPUID_LADDER2_LAYER = 794, //层数声望id 794
	REPUID_LADDER2_TIME = 795, // 时间声望id 795

	REPUID_FAIR_BATTLE_COUNT = 796,	// 公平竞技场次

	REPUID_SSP_COMPTITION_VOTE_OUT = 799,   //朋友圈比赛已投出去的票数
	REPUID_ACTIVITY_LAST_WEEK = 800,   //上周总活跃度
	REPUID_WITCH_COTTAGE_DAILY_DRAW				= 801,	// 每日魔女小屋抽取次数
	REPUID_WITCH_COTTAGE_DAILY_DRAW_HAS_GET		= 802,	// 每日已经领取的档次

	REPUID_TRIGGER_GIFT_CONSECUTIVE_COUNT		= 898,	// 触发礼包连续购买次数

	REPUID_CHILD_DESTINY_POINT		= 953, // 天命点数

	REPUID_PROF1_FAIR_LAST_SCORE = 957, // 公平竞技场职业1上赛季积分
	REPUID_PROF2_FAIR_LAST_SCORE = 958, // 公平竞技场职业2上赛季积分
	REPUID_PROF3_FAIR_LAST_SCORE = 959, // 公平竞技场职业3上赛季积分
	REPUID_PROF4_FAIR_LAST_SCORE = 960, // 公平竞技场职业4上赛季积分
	REPUID_PROF5_FAIR_LAST_SCORE = 961, // 公平竞技场职业5上赛季积分
	REPUID_PROF6_FAIR_LAST_SCORE = 962, // 公平竞技场职业6上赛季积分
	REPUID_PROF7_FAIR_LAST_SCORE = 963, // 公平竞技场职业7上赛季积分
	REPUID_PROF8_FAIR_LAST_SCORE = 964, // 公平竞技场职业8上赛季积分
	REPUID_PROF9_FAIR_LAST_SCORE = 965, // 公平竞技场职业9上赛季积分
	REPUID_PROF10_FAIR_LAST_SCORE = 966, // 公平竞技场职业10上赛季积分
	REPUID_PROF11_FAIR_LAST_SCORE = 967, // 公平竞技场职业11上赛季积分
	REPUID_PROF12_FAIR_LAST_SCORE = 968, // 公平竞技场职业12上赛季积分

	REPUID_PROF1_FAIR_CUR_SCORE = 969, // 公平竞技场职业1本赛季积分
	REPUID_PROF2_FAIR_CUR_SCORE = 970, // 公平竞技场职业2本赛季积分
	REPUID_PROF3_FAIR_CUR_SCORE = 971, // 公平竞技场职业3本赛季积分
	REPUID_PROF4_FAIR_CUR_SCORE = 972, // 公平竞技场职业4本赛季积分
	REPUID_PROF5_FAIR_CUR_SCORE = 973, // 公平竞技场职业5本赛季积分
	REPUID_PROF6_FAIR_CUR_SCORE = 974, // 公平竞技场职业6本赛季积分
	REPUID_PROF7_FAIR_CUR_SCORE = 975, // 公平竞技场职业7本赛季积分
	REPUID_PROF8_FAIR_CUR_SCORE = 976, // 公平竞技场职业8本赛季积分
	REPUID_PROF9_FAIR_CUR_SCORE = 977, // 公平竞技场职业9本赛季积分
	REPUID_PROF10_FAIR_CUR_SCORE = 978, // 公平竞技场职业10本赛季积分
	REPUID_PROF11_FAIR_CUR_SCORE = 979, // 公平竞技场职业11本赛季积分
	REPUID_PROF12_FAIR_CUR_SCORE = 980, // 公平竞技场职业12本赛季积分

	REPUID_FAIR_BATTLE_SEASON_COUNT = 981,	//公平竞技场赛季战斗场次
	REPUID_FAIR_BATTLE_BATTLE_PRIZE = 982,	//公平竞技场战斗结算

	REPUID_PROF1_FAIR_RECORD_WIN = 983,   // 公平竞技场职业1胜场
	REPUID_PROF1_FAIR_RECORD_ALL = 984,  // 公平竞技场职业1总胜场
	REPUID_PROF2_FAIR_RECORD_WIN = 985,
	REPUID_PROF2_FAIR_RECORD_ALL = 986,
	REPUID_PROF3_FAIR_RECORD_WIN = 987,
	REPUID_PROF3_FAIR_RECORD_ALL = 988,
	REPUID_PROF4_FAIR_RECORD_WIN = 989,
	REPUID_PROF4_FAIR_RECORD_ALL = 990,
	REPUID_PROF5_FAIR_RECORD_WIN = 991,
	REPUID_PROF5_FAIR_RECORD_ALL = 992,
	REPUID_PROF6_FAIR_RECORD_WIN = 993,  // 公平竞技场职业6胜场
	REPUID_PROF6_FAIR_RECORD_ALL = 994, // 公平竞技场职业6总胜场

	REPUID_PROF1_FAIR_BATTLE_WIN = 995,   // 公平竞技场职业1周胜场
	REPUID_PROF1_FAIR_BATTLE_ALL = 996,   // 公平竞技场职业1周总场
	REPUID_PROF1_FAIR_BATTLE_COUNT = 997,  // 公平竞技场职业1周胜率
	REPUID_PROF2_FAIR_BATTLE_WIN = 998,
	REPUID_PROF2_FAIR_BATTLE_ALL = 999,
	REPUID_PROF2_FAIR_BATTLE_COUNT = 1000,
	REPUID_PROF3_FAIR_BATTLE_WIN = 1001,
	REPUID_PROF3_FAIR_BATTLE_ALL = 1002,
	REPUID_PROF3_FAIR_BATTLE_COUNT = 1003,
	REPUID_PROF4_FAIR_BATTLE_WIN = 1004,
	REPUID_PROF4_FAIR_BATTLE_ALL = 1005,
	REPUID_PROF4_FAIR_BATTLE_COUNT = 1006,
	REPUID_PROF5_FAIR_BATTLE_WIN = 1007,
	REPUID_PROF5_FAIR_BATTLE_ALL = 1008,
	REPUID_PROF5_FAIR_BATTLE_COUNT = 1009,
	REPUID_PROF6_FAIR_BATTLE_WIN = 1010,
	REPUID_PROF6_FAIR_BATTLE_ALL = 1011,
	REPUID_PROF6_FAIR_BATTLE_COUNT = 1012,
	REPUID_PROF7_FAIR_BATTLE_WIN = 1013,
	REPUID_PROF7_FAIR_BATTLE_ALL = 1014,
	REPUID_PROF7_FAIR_BATTLE_COUNT = 1015,
	REPUID_PROF8_FAIR_BATTLE_WIN = 1016,
	REPUID_PROF8_FAIR_BATTLE_ALL = 1017,
	REPUID_PROF8_FAIR_BATTLE_COUNT = 1018,
	REPUID_PROF9_FAIR_BATTLE_WIN = 1019,
	REPUID_PROF9_FAIR_BATTLE_ALL = 1020,
	REPUID_PROF9_FAIR_BATTLE_COUNT = 1021,
	REPUID_PROF10_FAIR_BATTLE_WIN = 1022,
	REPUID_PROF10_FAIR_BATTLE_ALL = 1023,
	REPUID_PROF10_FAIR_BATTLE_COUNT = 1024,
	REPUID_PROF11_FAIR_BATTLE_WIN = 1025,
	REPUID_PROF11_FAIR_BATTLE_ALL = 1026,
	REPUID_PROF11_FAIR_BATTLE_COUNT = 1027,
	REPUID_PROF12_FAIR_BATTLE_WIN = 1028, // 公平竞技场职业12周胜场
	REPUID_PROF12_FAIR_BATTLE_ALL = 1029, // 公平竞技场职业12周总场
	REPUID_PROF12_FAIR_BATTLE_COUNT = 1030, // 公平竞技场职业12周胜率

	REPUID_OVERCOOK_SCORE = 1070, //异界厨房总积分
	REPUID_CORPS_SEASON_MEMBER  = 1071,     //社团赛季个人领奖声望
	REPUID_CORPS_SEASON_LOCAL   = 1072,     //社团赛季本服领奖声望
	REPUID_CORPS_SEASON_ROAM    = 1073,     //社团赛季跨服领奖声望

	REPUID_HARMONIOUS_FLAG	    = 1084, //永恒契约状态标志 1 结成 0 未

	REPUID_ROAM_2_BEGIN = 1100,	//跨服声望

	REPUID_WINTER_PROJECT_WIN = 1134,	//冬日计划，阵营胜负
	REPUID_WINTER_PROJECT_SCORE = 1135,	//冬日计划，个人积分
	REPUID_WINTER_PROJECT_BAD_MONEY = 1136,	//冬日计划，狼人代币
	REPUID_WINTER_PROJECT_RESOURCE = 1138,	//冬日计划，杀怪的获得资源数
	REPUID_PVE_ACTIVITY				= 1157,	//PVE服务器活动声望
	REPUID_HUNDRED_CENTER_CORPS_PERSONA_SCORE = 1161, //跨服个人声望
	REPUID_HUNDRED_CENTER_CORPS_PERSONA_JOIN_COUNT = 1163,
	REPUID_ROAM_2_END = 1199, //跨服声望
	REPUID_SSP_COMPTITION_ROAM_VOTE = 1201, //朋友圈比赛跨服票数
	REPUID_SSP_COMPTITION_ROAM_VOTE_OUT = 1202, //朋友圈比赛跨服已投出票数
	REPUID_SSP_COMPTITION_RECOMMENED_COUNT = 1206, //朋友圈比赛每日推荐次数
	REPUID_TEMPORARY_REWARD_LEVEL = 1207, //临时小队已领取奖励等级 + version

	REPUID_MINIGAME_ROAM_ALL_COUNT = 1209, // 电玩大对决总场次
	REPUID_MINIGAME_ROAM_WIN_COUNT = 1210, // 电玩大对决总胜场
	REPUID_MINIGAME_ROAM_SCORE = 1211, // 电玩大对决总积分
	REPUID_MINIGAME_ROAM_ALL_COUNT_WEEK = 1212, // 电玩大对决周场次
	REPUID_MINIGAME_ROAM_WIN_COUNT_WEEK = 1213, // 电玩大对决周胜场
	REPUID_MINIGAME_ROAM_SCORE_WEEK = 1214, // 电玩大对决周积分
	REPUID_MINIGAME_ROAM_ALWAYS_WIN = 1215, // 电玩大对决周连胜
	REPUID_MINIGAME_ROAM_REWARD_DAILY = 1216, // 电玩大对决每日奖励
	REPUID_MINIGAME_ROAM_REWARD_3WIN = 1217, // 电玩大对决3连胜奖励
	REPUID_MINIGAME_ROAM_REWARD_5WIN = 1218, // 电玩大对决5连胜奖励
	REPUID_MINIGAME_ROAM_REWARD_7WIN = 1219, // 电玩大对决7连胜奖励
	REPUID_MINIGAME_ROAM_WIN_DAILY = 1220, // 电玩大对决每日胜场

	REPUID_LMFSHOP_RACK_UNLOCK		= 1289, // 明非特卖解锁货柜

	REPUID_RETURNPLAYER_DAILY_POINT  = 1301, //新版玩家回归每日积分
	REPUID_RETURNPLAYER_POINT	     = 1302, //新版老玩家回归积分
	REPUID_HARMONIOUS_DIAMOUD			 = 1303, //良缘结晶
	REPUID_CLOUD_TRUST_LOGIN_IN_PLAYER	= 1304,	// 云游戏托管登录
	REPUID_OWNER_CONTRACT_HOMETOWN = 1309, // 玩家是否有契约家园
	REPUID_CONTRACT_HOMETOWN            = 1313, // 契约家园声望
	REPUID_CONTRACT_HOMETOWN_PROSPERITY = 1314, // 契约家园温馨度
	REPUID_CONTRACT_HOMETOWN_GIFT       = 1315,   //契约家园本周送礼赞赏值
	REPUID_PLAYER_CONTRACT_HOMETOWN_ARREARAGE = 1316,   //契约家园欠费时间（周）
	REPUID_CONTRACT_HOMETOWN_GIFT_HISTORY   = 1317,     //契约家园历史送礼赞赏值
	REPUID_CONTRACT_HOMETOWN_MONEY          = 1318,     //契约家园币
	REPUID_CONTRACT_HOMETOWN_APPRECIATION   = 1319,     //契约家园总赞赏值
	REPUID_CONTRACT_HOMETOWN_ONLY_GIFT_POPULARITY = 1320,   //契约家园送礼物人气值
	REPUID_CONTRACT_HOMETOWN_ONLY_GIFT_APPRECIATION = 1321, //契约家园收礼物赞赏值
	REPUID_CONTRACT_HOMETOWN_VISITOR_COUNT  = 1322,         //契约家园累计被摆放次数
	REPUID_CONTRACT_HOMETOWN_DESIGN_RECOMMEND_CHANCE = 1323,    //契约家园推荐券
	REPUID_CONTRACT_HOMETOWN_DESIGN_RECOMMEND_VAL_HISTORY = 1324,   //历史契约家园设计推荐度

	REPUID_BDSG_ROAM_ENROLL_QUALIFICATION = 1352,	// 白底神功跨服报名资格

	REPUID_REPUTATION_TRANSFER_1		= 1401,	// 可以转移的声望1
	REPUID_REPUTATION_TRANSFER_2		= 1402,	// 可以转移的声望2
	REPUID_REPUTATION_TRANSFER_3		= 1403,	// 可以转移的声望3
	REPUID_REPUTATION_TRANSFER_4		= 1404,	// 可以转移的声望4
	REPUID_REPUTATION_TRANSFER_5		= 1405,	// 可以转移的声望5
	REPUID_REPUTATION_TRANSFER_6		= 1406,	// 可以转移的声望6
	REPUID_REPUTATION_TRANSFER_BEGIN		= 1560,	// 新增可以转移的声望 开始
	REPUID_REPUTATION_TRANSFER_END		= 1609,	// 新增可以转移的声望 结束
	REPUID_COOL_RUNNING                 = 1407, // 酷跑声望

	REPUID_CONTEST_TEXT_EVERYDAY	= 1408,	//每日文字答题
	REPUID_CONTEST_TEXT_SATURDAY	= 1409,	//周六文字答题
	REPUID_CONTEST_TEXT_SUNDAY	    = 1410,	//周日文字答题
	REPUID_CONTEST_GRAPH_EVERYDAY	= 1411,	//每日图片答题，实际上周一到周四进行
	REPUID_CONTEST_GRAPH_WEEKEND	= 1412,	//周末答题，实际上是周五，周六，周日进行

	// 时装搭配大赛，本服：1327 - 1336，跨服：1337 - 1346
	REPUID_FASHION_DRESS_BEGIN											= 1327,
	REPUID_FASHION_DRESS_LOCAL_UPLOAD_SCHEME_HISTORY_HIGHEST_SCORE		= 1328,		// 本服上传方案历史最高积分
	REPUID_FASHION_DRESS_LOCAL_UPLOAD_SCHEME_DAY_COUNT					= 1329,		// 本服上传方案今日次数，日重置
	REPUID_FASHION_DRESS_LOCAL_FREEDOM_VOTE								= 1330,		// 本服自由票
	REPUID_FASHION_DRESS_LOCAL_RECOMMEND_VOTE							= 1331,		// 本服推荐票，日重置
	REPUID_FASHION_DRESS_LOCAL_RECOMMEND_VOTE_DAY_VOTE					= 1332,		// 本服推荐票投票次数，日重置
	REPUID_FASHION_DRESS_LAST_RESET_DAILY_TIME							= 1333,		// 上次重置每日声望时间戳

	REPUID_FASHION_DRESS_ROAM_VOTE										= 1337,		// 跨服票声望，日重置
	REPUID_FASHION_DRESS_ROAM_DAY_VOTE									= 1338,		// 跨服票今日次数，日重置
	REPUID_FASHION_DRESS_ROAM_ELIMINATE_VOTE_ROUND						= 1339,		// 跨服淘汰赛当前投票轮次(cur_round+1)
	REPUID_FASHION_DRESS_ROAM_ELIMINATE_VOTE_AREA						= 1340,		// 跨服淘汰赛投票区域，0-15位表示投票轮次(cur_round+1)，16-31位表示投票区域
	REPUID_FASHION_DRESS_ROAM_ATMOSPHERE_REWARD							= 1341,		// 跨服氛围值奖励，0-7位表示是否已领取，日重置
	REPUID_FASHION_DRESS_END											= 1346,
	REPUID_SPRING_INNER_DRAW_COUNT										= 1375,			// 春节彩票内可抽奖总次数
	REPUID_FORBID_SYS_OP												= 1430,         // 设置的一些记录（如屏蔽陌生人消息等，高16位为等级）

	REPUID_THUNDERSTRIKE_PLAYER_MODE										= 1448,			// 天谴计划玩家达到的最大难度，用来判断玩家能否进入某个难度
	REPUID_THUNDERSTRIKE_PLAYER_COST_TIME									= 1450,			// 天谴计划玩家耗时
	REPUID_THUNDERSTRIKE_PLAYER_LASTWEEK_MODE                               = 1458,         //天谴计划玩家上周达到的最大难
	REPUID_HONEY_GARDEN_BARGAIN_COIN										= 1459,			// 甜蜜花园砍价代币

	REPUID_RESTAURANT_SP_ITEM_COUNT											= 1489,			// 餐厅持有特殊道具数量
	REPUID_RESTAURANT_PRODUCER_COUNT_AND_CAN_STEAL_COUNT					= 1490,			// 餐厅可保持特殊道具最大数量(前16)和现存可偷取数量(后16)
	REPUID_RESTAURANT_SP_ITEM_TIMESTAMP										= 1491,			// 餐厅特殊道具上次开始生产时间
	REPUID_RESTAURANT_EXPLORE_VITALITY										= 1475,			// 餐厅功能探索体力值
	REPUID_FASHION_DRESS_LOCAL_FREEDOM_VOTE_DAY_VOTE						= 1492,         // 本服自由票投票次数，日重置

	REPUID_SKATEBOARD_RACE_PLAY_TIMES									= 1469,	// 彩虹航线游玩次数
	REPUID_SKATEBOARD_RACE_FINISH_TIMES									= 1470,	// 彩虹航线完成次数
	REPUID_SKATEBOARD_RACE_FIRST_TIMES									= 1471,	// 彩虹航线第一名次数
	REPUID_SKATEBOARD_RACE_FIFTH_TIMES									= 1472,	// 彩虹航线第五名次数
	REPUID_SKATEBOARD_RACE_BEST_FINISH_TIME								= 1473,	// 彩虹航线最佳用时 (ms)
	REPUID_SKATEBOARD_RACE_DAY_GET_REWARD								= 1474,	// 彩虹航线每日领奖次数计次

	REPUID_ZSPACE_MONEY                                                     = 1498,
	REPUID_ZSPACE_CUR_LIKES            								 		= 1499,			//累计人气值
	REPUID_ZSPACE_EXCHANGE_LIKES											= 1500,			//充能人气值
	REPUID_THUNDERSTRIKE_BEGIN											= 1501,         // 天谴计划BUFF
	REPUID_THUNDERSTRIKE_END												= 1510,         // 天谴计划BUFF (按位存，对应的位为1)
	REPUID_OPTIONAL_QUARTER_CARD											= 1647,	// 可选季卡

	REPUID_ZSPACE_DAILY_LINKS                                               = 1653,  //每日人气值

	REPUID_HUNDRED_CORPS_BATTLE_PERSIONAL_SCORE                             = 1657,

	REPUID_ROAM_COMMUNITY_ACTIVE											= 1658,  //跨服社团活跃值
	REPUID_ROAM_COMMUNITY_AWARD_MASK										= 1659,  //跨服社团活跃值领奖记录

	REPUID_QXQY_TOTAL_DRAW_COUNT											= 1683,  //千寻奇遇总抽声望
	REPUID_QXQY_LEVEL														= 1684,  //千寻奇遇圈数声望

	REPUID_MING_YUN_ZHI_LUN													= 1695,  //命运之轮排行榜声望
	REPUID_ACHIEVEMENT_POINT												= 1696, // 新成就点数

	REPUID_FISH_FISH_COIN 													= 1700, //鱼鱼币
	REPUID_FISHING_EXP														= 1701, // 钓鱼经验

	REPUID_CUMULATIVE_AMOUNT												= 1702, // 累计消费
	REPUID_YESTERDAY_ACTIVITY												= 1729, // 昨日的日活跃度(13号声望值)

	REPUID_TOWNLET_EXP														= 1755,	// 小镇经验
	REPUID_TOWNLET_COIN														= 1756,	// 小镇代币

	REPUID_ARENA_CENTER_SINGLE_BATTLE_CONTINUOUS_TIMES 			= 1765, // 浩瀚实训竞技场连胜计次声望
	REPUID_ARENA_GROUP_QUIT_TIME						= 1792, // 退出天梯战队的时间
	REPUID_CUBE_MIRROR_SKILL_IDX 						= 1800, // 次元之境技能索引声望

	REPUID_TOWNLET_COIN_INC_TIMESTAMP                   = 1801, // 小镇资金增长的时间戳
	REPUID_TOWNLET_LEVEL      				             = 1802, // 小镇等级
	REPUID_TOWNLET_TODAY_COIN_GAIN      				 = 1803, // 小镇今日获取资金

	REPUID_TURNTABLE_RECHARGE_EXTRA_RECHARGE			= 1813, // 转盘额外充值

	REPUID_THUNDERSTRIKE_PLAYER_MULTI_MODE_TOPLIST							= 1824,			// 天谴计划玩家组队达到的最大难度,排行榜用
	REPUID_THUNDERSTRIKE_PLAYER_MULTI_COST_TIME								= 1825,			// 天谴计划玩家组队耗时，排行榜用
	REPUID_THUNDERSTRIKE_PLAYER_MODE_TOPLIST								= 1828,			// 天谴计划玩家单人达到的最大难度,排行榜用

	REPUID_ROUGE_SELECT			= 1843,	//rouge选择的职业
	REPUID_ROUGE_DIFF_LV		= 1861,	//rouge选择的难度
	REPUID_ROUGE_COMPLETE_DIFF_LV = 1844,	//rouge已通关难度

	REPUID_CASKET_RECHARGE_EXTRA_RECHARGE									= 1855, // 缘金绮匣额外充值数
	REPUID_CASKET_RECHARGE_EXTRA_OUTER_TIMES								= 1856, // 缘金绮匣额外外圈次数
	REPUID_CASKET_RECHARGE_EXTRA_INNER_TIMES								= 1857, // 缘金绮匣额外内圈次数
	REPUID_CASKET_RECHARGE_EXTRA_KEY_NUM									= 1858, // 缘金绮匣额外福袋钥匙个数
	REPUID_CASKET_RECHARGE_EXTRA_GOODY_TIMES								= 1859, // 缘金绮匣额外福袋个数

	REPUID_TETRAHEDRON_REFINE_ENERGY = 1864, //圣核精炼能量

	REPUID_PERSONAL_TARGET_DRESSUP_SCORE	= 1876,	//双人共舞个人搭配转个人目标分数
	REPUID_PERSONAL_TARGET_DRESSUP_TOTAL_SCORE	= 1877,	//双人共舞总分转个人目标分数
	REPUID_NEW_BP_CARD							= 1964,	// 特训协议(新pb)勋章

	REPUID_MAX		= 2048,	// 声望索引上限，最高可以扩展到65535
};

const int THUNDERSTRIKE_REPU_COUNT = GNET::REPUID_THUNDERSTRIKE_END - GNET::REPUID_THUNDERSTRIKE_BEGIN + 1;

// 用于SNSInfo的声望
const int SNSINFO_REPU_IDS[] =
{
	REPUID_FLOWER,
	REPUID_FLOWER_WEEK,
	REPUID_FLOWER_SEND,
	REPUID_FLOWER_SEND_WEEK,
	REPUID_PROF1_FAIR_BATTLE_ALL,
	REPUID_PROF2_FAIR_BATTLE_ALL,
	REPUID_PROF3_FAIR_BATTLE_ALL,
	REPUID_PROF4_FAIR_BATTLE_ALL,
	REPUID_PROF5_FAIR_BATTLE_ALL,
	REPUID_PROF6_FAIR_BATTLE_ALL,
	REPUID_PROF7_FAIR_BATTLE_ALL,
	REPUID_PROF8_FAIR_BATTLE_ALL,
	REPUID_PROF9_FAIR_BATTLE_ALL,
	REPUID_PROF10_FAIR_BATTLE_ALL,
	REPUID_PROF11_FAIR_BATTLE_ALL,
	REPUID_PROF12_FAIR_BATTLE_ALL,
	REPUID_PROF1_FAIR_BATTLE_WIN,
	REPUID_PROF2_FAIR_BATTLE_WIN,
	REPUID_PROF3_FAIR_BATTLE_WIN,
	REPUID_PROF4_FAIR_BATTLE_WIN,
	REPUID_PROF5_FAIR_BATTLE_WIN,
	REPUID_PROF6_FAIR_BATTLE_WIN,
	REPUID_PROF7_FAIR_BATTLE_WIN,
	REPUID_PROF8_FAIR_BATTLE_WIN,
	REPUID_PROF9_FAIR_BATTLE_WIN,
	REPUID_PROF10_FAIR_BATTLE_WIN,
	REPUID_PROF11_FAIR_BATTLE_WIN,
	REPUID_PROF12_FAIR_BATTLE_WIN,
	REPUID_FAIR_SWITCH,

};

enum REPUTATION_CONSTANT
{
	REPUTATION_VERSION	= 0x02,
	MIN_REPUTATION_VALUE	= 0,
	MAX_REPUTATION_VALUE	= 2000000000,
	FORCE_INT		= 0x7fffffff,
};

/*const int TEAM_REPUTATION_IDS[] = {};
const int TEAM_REPUTATION_IDS_COUNT = sizeof(TEAM_REPUTATION_IDS)/sizeof(int);
inline bool IsTeamReputation(int reputation_id)
{
	for (int i = 0; i < TEAM_REPUTATION_IDS_COUNT; ++i)
	{
		if (reputation_id == TEAM_REPUTATION_IDS[i])
		{
			return true;
		}
	}
	return false;
}*/

enum COUNTER_CONSTANT
{
	COUNTER_VERSION = 0x01,
	COUNTER_MAX = 256,
};
inline size_t GetUserContentSize(const void *data, size_t len)
{
#ifdef USE_UTF8
	size_t length = 0;
	const unsigned char *begin = (const unsigned char *)data;
	for (size_t i = 0, offset = 0; i != len; i += offset)
	{
		unsigned char byte = begin[i];
		if (byte >= 0xFC)
		{
			offset = 6;
		}
		else if (byte >= 0xF8)
		{
			offset = 5;
		}
		else if (byte >= 0xF0)
		{
			offset = 4;
		}
		else if (byte >= 0xE0)
		{
			offset = 3;
		}
		else if (byte >= 0xC0)
		{
			offset = 2;
		}
		else
		{
			offset = 1;
		}
		length++;
	}
	return length;
#else
	return len;
#endif
}
inline size_t GetUserContentSize(const GNET::Octets& data)
{
	return GetUserContentSize(data.begin(), data.size());
}
inline size_t GetUserContentSize(const std::string& str)
{
	return GetUserContentSize(str.c_str(), str.length());
}
//该接口用于检查客户端发来的数据的字符数
inline bool CheckUserContentSize(const void *data, size_t len, size_t min_size, size_t max_size)
{
#ifdef USE_UTF8
	size_t length = GetUserContentSize(data, len);
	return length >= min_size && length <= max_size;
#else
	return (len / 2) >= min_size  && (len / 2) <= max_size ;
#endif
}
inline bool CheckUserContentSize(const GNET::Octets& data, size_t min_size, size_t max_size)
{
	return CheckUserContentSize(data.begin(), data.size(), min_size, max_size);
}
inline bool CheckUserContentSize(const std::string& str, size_t min_size, size_t max_size)
{
	return CheckUserContentSize(str.c_str(), str.length(), min_size, max_size);
}
inline int DetachZoneidAndAccount(const GNET::Octets& inAccount, GNET::Octets& outAccount)
{
	outAccount.clear();
	std::string  account_str( (const char *)inAccount.begin(), inAccount.size() );
	size_t pos = account_str.find_last_of('@');
	if (pos != std::string::npos )
	{
		int offset = static_cast<int>(pos);
		if ( offset < 1 || offset >= (int)inAccount.size())
		{
			outAccount = inAccount;
			return -1;
		}
		//pos是@的位置，切account和zoneid的时候都要跳过
		outAccount.replace(inAccount.begin(), offset);
		std::string zoneidStr( (char *)inAccount.begin() + offset + 1, inAccount.size() - offset - 1 );
		return atoi( zoneidStr.c_str() );
	}
	else    //原本就没有zoneid
	{
		outAccount = inAccount;
		return -1;
	}
}

enum MIDAS_AUCTION_TRADE_STATUS
{
	MAT_UNCERTAIN		= 1,
	MAT_SUCCEED		= 2,
	MAT_FAILED		= 3,
};
enum MIDAS_ORDER_CONFIRM_STATUS
{
	MOC_DB_UNSAVE		= 1,
	MOC_DB_SAVED		= 2,
	MOC_MIDAS_CONFIRMED	= 3,
	MOC_DB_CONFIRMED	= 4,
};
enum MIDAS_AUANY_RPC_TYPE
{
	MART_MIDAS_TRADE 	= 1,
	MART_MIDAS_CONFIRM 	= 2,
	MART_MIDAS_GET		= 3,
	MART_MIDAS_FROM_DB	= 4,
	MART_HTTP_PROXY		= 5,
	MART_GET_GROUPOPENID	= 6,
	MART_OP_GROUP	= 7,
};

enum MIDAS_RETCODE
{
	MIDAS_WRONG_PARAM	= 1001, //参数填错了
	//MIDAS_DENY_OP		=1002,//系统操作中，不允许操作
	MIDAS_NOT_ENOUGH_MONEY	= 1004, //钱不够
	MIDAS_WRONG_TOKEN	= 1018, //token 失效
	MIDAS_WRONG_BILLNO	= 1002215, //确认前一笔订单成功
	MIDAS_WRONG_FREQUENT	= 1133, //频繁
	MIDAS_WRONG_TIMEOUT	= 3000111, //midas内部逻辑出错,需要重点排查
};
enum MIDAS_OPCODE
{
	MIDAS_PRETRANSFER	= 1,
	MIDAS_CONFIRM_TRANSFER	= 2,
	MIDAS_CANCEL_TRANSFER	= 3,
	MIDAS_GETBALANCE	= 4,
	MIDAS_PAY		= 5,
	MIDAS_CANCEL_PAY	= 6,
	MIDAS_PRESENT		= 7,
	MIDAS_DEBUG		= 8,
};

//当前最大的职业数量
const int MAX_PROFESSION_COUNT = 17;

//const int TOP_REPUTATION_IDS[] = {REPUID_FLOWER, REPUID_FLOWER_WEEK, REPUID_FLOWER_SEND, REPUID_FLOWER_SEND_WEEK, REPUID_CONJUGAL, REPUID_CONJUGAL_WEEK, REPUID_HOMETOWN};
const int TOP_REPUTATION_IDS[] = {};
inline void GetToplistReputationID(std::vector<int>& id_vec)
{
	std::vector<int> tmp_vec(TOP_REPUTATION_IDS, TOP_REPUTATION_IDS + sizeof(TOP_REPUTATION_IDS) / sizeof(int));
	id_vec.swap(tmp_vec);
}
inline bool IsTopReputationID(int rep_id)
{
	static const std::set<int> rep_set(TOP_REPUTATION_IDS, TOP_REPUTATION_IDS + sizeof(TOP_REPUTATION_IDS) / sizeof(int));
	return rep_set.find(rep_id) != rep_set.end();
}
/*inline bool IsLotteryReputationID(int rep_id)
{
	return (rep_id >= REPUID_TP_POWERDICE_1 && rep_id <= REPUID_TP_POWERDICE_30);
}*/
// 此函数不要在逻辑里手动调用判断
inline bool CheckRoamRepuIDInner(unsigned short id)
{
	if (id >= GNET::REPUID_ROAM_BEGIN && id <= GNET::REPUID_ROAM_END)
	{
		return true;
	}
	if (id >= GNET::REPUID_ROAM_2_BEGIN && id <= GNET::REPUID_ROAM_2_END)
	{
		return true;
	}
	if (id >= GNET::REPUID_PROF1_FAIR_CUR_SCORE && id <= GNET::REPUID_PROF12_FAIR_CUR_SCORE)
	{
		return true;
	}
	if (id == GNET::REPUID_FAIR_BATTLE_SEASON_COUNT || id == GNET::REPUID_FAIR_BATTLE_BATTLE_PRIZE)
	{
		return true;
	}
	if (id >= GNET::REPUID_PROF1_FAIR_RECORD_WIN  && id <= GNET::REPUID_PROF12_FAIR_BATTLE_COUNT)
	{
		return true;
	}
	if (id == GNET::REPUID_FAIR_BATTLE_COUNT)
	{
		return true;
	}
	/*switch (id)
	{
		case GNET::REPUID_ROAM_0 ... GNET::REPUID_ROAM_9:
			{
				return true;
			}
			break;
		case GNET::REPUID_ROAM_10 ... GNET::REPUID_ROAM_14:
			{
				return true;
			}
			break;
		default:
			break;
	}*/
	return false;
}

inline bool IsDeliverReputation(unsigned short id) //deliver负责保存的声望
{
	switch (id)
	{
	case GNET::REPUID_FLOWER:
	case GNET::REPUID_NEW_SECT_TEACH:
	case GNET::REPUID_CONTEST:
	case GNET::REPUID_FLOWER_WEEK:
	case GNET::REPUID_FLOWER_SEND_WEEK:
	case GNET::REPUID_FLOWER_SEND:
	case GNET::REPUID_CONJUGAL:
	case GNET::REPUID_CONJUGAL_WEEK:
	case GNET::REPUID_ARENA_SCORE:
	case GNET::REPUID_TOPBATTLE_MAX:
	case GNET::REPUID_SECT_GRADUATE:
	case GNET::REPUID_CENTER_ARENA_TEAM_BATTLE:
	case GNET::REPUID_CENTER_ARENA_TEAM_BATTLE_LAST_MONTH:
	case GNET::REPUID_CENTER_ARENA_TEAM_BATTLE_LAST_MONTH_COUNT:
	case GNET::REPUID_CENTER_ARENA_TEAM_BATTLE_LAST_RANK:
	case GNET::REPUID_CENTER_ARENA_TEAM_BATTLE_HIGHEST:
	case GNET::REPUID_GOLDEN_INTIMATE_MAX:
	case GNET::REPUID_CORPS_RACE_FINISHED:
	case GNET::REPUID_PRECREATE_SHARE_COUNT:
	case GNET::REPUID_CORPS_RACE_PLAYER_WIN_TIMES:
	case GNET::REPUID_ARENA_BATTLE_CONTINUOUS_TIMES_1:
	case GNET::REPUID_ARENA_BATTLE_CONTINUOUS_TIMES_2:
	case GNET::REPUID_ARENA_ZONE:
	case GNET::REPUID_ARENA_ZONE_TIMESTAMP:
	case GNET::REPUID_OWNER_HOMETOWN:
	case GNET::REPUID_OWNER_CONTRACT_HOMETOWN:
	case GNET::REPUID_ARENA_ZONE_LAST:
	case GNET::REPUID_FRIEND_AMITY_BEGIN ... GNET::REPUID_FRIEND_AMITY_END:
	case GNET::REPUID_PLAYER_COMEBACK_FLAG:
	case GNET::REPUID_PLAYER_COMEBACK_TS:
	case GNET::REPUID_IDIOM_SOLITAIRE:
	case GNET::REPUID_HOMETOWN_ONLY_GIFT_APPRECIATION:
	case GNET::REPUID_HOMETOWN_APPRECIATION:
	case GNET::REPUID_PUBG_SELECT:
	case GNET::REPUID_ROUGE_SELECT:
	case GNET::REPUID_ROUGE_DIFF_LV:
	case GNET::REPUID_ROUGE_COMPLETE_DIFF_LV:
	case GNET::REPUID_ARENA_MINIGAME_WIN_COUNT:
	case GNET::REPUID_ARENA_MINIGAME_WIN_COUNT_MAX:
	case GNET::REPUID_HOMETOWN_VISITOR_COUNT:
	case GNET::REPUID_INVITE_CODE_POINT:
	case GNET::REPUID_SOUL_TREE_MAX_COUNT:
	case GNET::REPUID_SSP_COMPTITION_VOTE:
	case GNET::REPUID_HOMETOWN_DESIGN_RECOMMEND_VAL_HISTORY:
	case GNET::REPUID_SSP_COMPTITION_ROAM_VOTE:
	case GNET::REPUID_SSP_COMPTITION_RECOMMENED_COUNT:
	case GNET::REPUID_TEMPORARY_REWARD_LEVEL:
	case GNET::REPUID_CLOUD_TRUST_LOGIN_IN_PLAYER:
	case GNET::REPUID_REPUTATION_TRANSFER_1 ... GNET::REPUID_REPUTATION_TRANSFER_6:
	case GNET::REPUID_REPUTATION_TRANSFER_BEGIN ... GNET::REPUID_REPUTATION_TRANSFER_END:
	case GNET::REPUID_HARMONIOUS_DIAMOUD:
	case GNET::REPUID_HARMONIOUS_FLAG:
	case GNET::REPUID_CONTEST_TEXT_EVERYDAY:
	case GNET::REPUID_CONTEST_TEXT_SATURDAY:
	case GNET::REPUID_CONTEST_TEXT_SUNDAY:
	case GNET::REPUID_CONTEST_GRAPH_EVERYDAY:
	case GNET::REPUID_CONTEST_GRAPH_WEEKEND:
	case GNET::REPUID_FASHION_DRESS_BEGIN ... GNET::REPUID_FASHION_DRESS_END:
	case GNET::REPUID_BDSG_ROAM_ENROLL_QUALIFICATION:
	case GNET::REPUID_FORBID_SYS_OP:
	case GNET::REPUID_RESTAURANT_SP_ITEM_COUNT:
	case GNET::REPUID_RESTAURANT_PRODUCER_COUNT_AND_CAN_STEAL_COUNT:
	case GNET::REPUID_RESTAURANT_SP_ITEM_TIMESTAMP:
	case GNET::REPUID_FASHION_DRESS_LOCAL_FREEDOM_VOTE_DAY_VOTE:
	case GNET::REPUID_ZSPACE_CUR_LIKES:
	case GNET::REPUID_ZSPACE_EXCHANGE_LIKES:
	case GNET::REPUID_HUNDRED_CORPS_BATTLE_PERSIONAL_SCORE:
	case GNET::REPUID_ZSPACE_DAILY_LINKS:
	case GNET::REPUID_ARENA_CENTER_SINGLE_BATTLE_CONTINUOUS_TIMES:
	case GNET::REPUID_ARENA_GROUP_QUIT_TIME:
	case GNET::REPUID_PERSONAL_TARGET_DRESSUP_SCORE:
	case GNET::REPUID_PERSONAL_TARGET_DRESSUP_TOTAL_SCORE:
		return true;
	default:
	{
		if (CheckRoamRepuIDInner(id))
		{
			return true;
		}
	}
	}

	return false;
}

inline bool IsGsReputation(unsigned short id) //gs负责保存的声望
{
	if (IsDeliverReputation(id))
	{
		return false;
	}
	return true;
}

//NOTE: 声望不能在月清和非月清之间摇摆, 要存盘的!
inline bool IsMonthClearReputation(unsigned short id) //月清个人声望列表
{
	if (/*id == GNET::REPUID_JUEWEI ||*/ id == REPUID_MONTH_ACTIVITY)
	{
		return true;
	}
	return false;
}

inline bool IsWeekClearReputation(unsigned short id) //周清个人声望列表
{
	switch (id)
	{
	case GNET::REPUID_FLOWER_WEEK:
	case GNET::REPUID_FLOWER_SEND_WEEK:
	case GNET::REPUID_CONJUGAL_WEEK:
	case GNET::REPUID_REVENGE_BATTLE:
	case GNET::REPUID_CONTEST_TEXT_SATURDAY:
	case GNET::REPUID_CONTEST_TEXT_SUNDAY:
		return true;

	default:
		return false;
	}
}

inline bool IsQuarterClearReputation(unsigned short id) //旬清个人声望列表
{
	return false;
}

inline bool IsPeriodReputation(unsigned short id) //月清日清等类型的声望
{
	if (IsMonthClearReputation(id))
	{
		return true;
	}
	if (IsWeekClearReputation(id))
	{
		return true;
	}
	if (IsQuarterClearReputation(id))
	{
		return true;
	}
	return false;
}

inline bool IsRomateSlaveRepu(unsigned short id)
{
	if (!IsDeliverReputation(id))
	{
		return false;
	}
	if (IsPeriodReputation(id))
	{
		return false;
	}
	if (CheckRoamRepuIDInner(id))
	{
		return true;
	}
	return false;
}

/*
inline int GetClearReputationDelay(unsigned short id)
{
	//if(id == GNET::REPUID_JUEWEI)
	//	return (6-1)*86400 + 0*3600 + 0*0; //爵位声望每月6日0点0分清零
	if(id == REPUID_MONTH_ACTIVITY)
		return (1-1)*86400 + 0*3600 + 0*0; //月活跃度每月1日0点0分清零
	return 0;
}
*/

//帮派声望数值
enum CORP_REPUTATION_ID
{
	CRID_HOLY_BOSS_EXP		= 1,	//圣兽经验值
	CRID_MERGE_COUNT_1		= 2,	//手动合帮计数器 ， 帮派活跃度 < 150, +1,
	CRID_MERGE_COUNT_2		= 3,	//自动合帮计数器 ， 帮派活跃度 < 30,  +1,
	CRID_AVERAGE_ACTIVITY 		= 4,	//日平均活跃度
	CRID_MOMENT_DAY_COUNT		= 5,	//发送社团状态每天次数
	CRID_ABDICATE			= 6,	//传位 每天一次
	CRID_HOLY_BOSS_1_EXP		= 7,
	CRID_HOLY_BOSS_2_EXP		= 8,
	CRID_HOLY_BOSS_3_EXP		= 9,
	CRID_HOLY_BOSS_4_EXP		= 10,
	CRID_ACTIVITY			= 17,	//帮派活跃度
	CRID_LASTWEEK_ACTIVITY		= 18,	//帮派上周活跃度
	CRID_RACE			= 19,	//帮派竞赛声望
	CRID_MANAGER_BONUS_MASTER	= 20,	//社团管理工资会长领取次数 周清
	CRID_MANAGER_BONUS_VICE_MASTER	= 21,	//社团管理工资副会长领取次数 周清
	CRID_HOLY_BOSS_WEEK_COUNT	= 22,	//帮派召唤每周次数
	CRID_HOLY_BOSS_TOTAL_COUNT	= 23,	//帮派召唤总次数

	CRID_MEMBER_COUNT_CORPS_DINNER = 24, // 社团晚宴参与人数
	CRID_MEMBER_COUNT_MOLDER_CORPS = 25, // 崩坏社团参与人数
	CRID_MEMBER_COUNT_CORPS_ALCHEMY = 26, // 社团炼金参与人数
	******************************** = 27, // 冰与火之歌参与人数
	CRID_MEMBER_COUNT_RHEIN_PLAN = 28, // 莱茵计划参与人数
	CRID_MEMBER_COUNT_TOY_STORY = 29, // 玩具总动员参与人数
	CRID_MEMBER_COUNT_CASSEL_STAR = 30, // 卡塞尔之星参与人数
	CRID_MEMBER_COUNT_KNIFE_SWORD = 31, // 刀剑相鸣之时参与人数
	CRID_MEMBER_COUNT_CORPS_SIMULATION_WAR = 32, // 社团模拟战参与人数
	CRID_MEMBER_COUNT_CORPS_COMPETITION = 33, // 社团竞赛参与人数

	CRID_SEDUCE_DRAGON_FACILITY_ENERGY = 37,	// 引龙行动引龙装置能量值

	CRID_DAY_ACTIVITY		= 38,	// 社团今日活跃度
	CRID_LASTDAY_ACTIVITY	= 39,	// 社团昨日活跃度

	CRID_CORPS_BOSS_WEEK_LIMIT = 40, // 社团BOSS周限次

	CRID_CORPS_TARGET_SCORE		= 41,	// 社团目标积分，周清
	CRID_CORPS_TARGET_TASK_1	= 42,	// 社团目标任务，周清
	CRID_CORPS_TARGET_TASK_2	= 43,
	CRID_CORPS_TARGET_TASK_3	= 44,
	CRID_CORPS_TARGET_TASK_4	= 45,
	CRID_CORPS_TARGET_TASK_5	= 46,
	CRID_CORPS_TARGET_TASK_6	= 47,
	CRID_CORPS_TARGET_TASK_7	= 48,
	CRID_CORPS_TARGET_TASK_8	= 49,
	CRID_CORPS_TARGET_TASK_9	= 50,
	CRID_CORPS_TARGET_TASK_10	= 51,
	CRID_CORPS_TARGET_TASK_11	= 52,
	CRID_CORPS_TARGET_TASK_12	= 53,
	CRID_CORPS_TARGET_TASK_13	= 54,
	CRID_CORPS_TARGET_TASK_14	= 55,
	CRID_CORPS_TARGET_TASK_15	= 56,
	CRID_CORPS_TARGET_TASK_16	= 57,
	CRID_CORPS_TARGET_TASK_17	= 58,
	CRID_CORPS_TARGET_TASK_18	= 59,
	CRID_CORPS_TARGET_TASK_19	= 60,
	CRID_CORPS_TARGET_TASK_20	= 61,
	CRID_CORPS_TARGET_TASK_COUNT_1	= 62,	// 社团目标任务次数，周清
	CRID_CORPS_TARGET_TASK_COUNT_2	= 63,
	CRID_CORPS_TARGET_TASK_COUNT_3	= 64,
	CRID_CORPS_TARGET_TASK_COUNT_4	= 65,
	CRID_CORPS_TARGET_TASK_COUNT_5	= 66,
	CRID_CORPS_TARGET_TASK_COUNT_6	= 67,
	CRID_CORPS_TARGET_TASK_COUNT_7	= 68,
	CRID_CORPS_TARGET_TASK_COUNT_8	= 69,
	CRID_CORPS_TARGET_TASK_COUNT_9	= 70,
	CRID_CORPS_TARGET_TASK_COUNT_10	= 71,
	CRID_CORPS_TARGET_TASK_COUNT_11	= 72,
	CRID_CORPS_TARGET_TASK_COUNT_12	= 73,
	CRID_CORPS_TARGET_TASK_COUNT_13	= 74,
	CRID_CORPS_TARGET_TASK_COUNT_14	= 75,
	CRID_CORPS_TARGET_TASK_COUNT_15	= 76,
	CRID_CORPS_TARGET_TASK_COUNT_16	= 77,
	CRID_CORPS_TARGET_TASK_COUNT_17	= 78,
	CRID_CORPS_TARGET_TASK_COUNT_18	= 79,
	CRID_CORPS_TARGET_TASK_COUNT_19	= 80,
	CRID_CORPS_TARGET_TASK_COUNT_20	= 81,

	CRID_CORPS_BOSS_SCORE			= 82,	// 社团boss积分
	CRID_PVE_ACTIVITY				= 83,	// PVE服务器活动
	CRID_LOCAL_HUNDRED_CORPS_BATTBLE_SCORE = 84, //百团大招本服积分
	CRID_HUNDRED_CORPS_BATTBLE_ASSIST_SCORE = 85, // 新社团联赛助战值
	CRID_BAIYUEJING					= 90,	// 跨服白月境
};

enum CORPS_SEASON_REPUTATION_ID
{
	CSRID_NONE            = 0,
	CSRID_LOCAL_SCORE     = 1,          //战团积分
	CSRID_ROAM_SCORE      = 2,          //巅峰战团积分
	CSRID_MAX             = 3,
};

inline bool IsRoamCorpsSeasonRepuID(int id)
{
	switch (id)
	{
	case CSRID_ROAM_SCORE:
		return true;
	default:
		return false;
	}
	return false;
}
inline int GetHolyBossExp(int index)
{
	switch (index)
	{
	case 0:
		return CRID_HOLY_BOSS_EXP;
	case 1:
		return CRID_HOLY_BOSS_1_EXP;
	case 2:
		return CRID_HOLY_BOSS_2_EXP;
	case 3:
		return CRID_HOLY_BOSS_3_EXP;
	case 4:
		return CRID_HOLY_BOSS_4_EXP;
	default:
		return 0;
	}
	return 0;
}

inline bool IsTaskCorpRepu(int id)
{
	return id == CRID_HOLY_BOSS_EXP
	       || id == CRID_ACTIVITY
	       || id == CRID_HOLY_BOSS_1_EXP
	       || id == CRID_HOLY_BOSS_2_EXP
	       || id == CRID_HOLY_BOSS_3_EXP
	       || id == CRID_HOLY_BOSS_4_EXP
	       || id == CRID_MEMBER_COUNT_CORPS_DINNER
	       || id == CRID_MEMBER_COUNT_MOLDER_CORPS
	       || id == CRID_MEMBER_COUNT_CORPS_ALCHEMY
	       || id == ********************************
	       || id == CRID_MEMBER_COUNT_RHEIN_PLAN
	       || id == CRID_MEMBER_COUNT_TOY_STORY
	       || id == CRID_MEMBER_COUNT_CASSEL_STAR
	       || id == CRID_MEMBER_COUNT_KNIFE_SWORD
	       || id == CRID_MEMBER_COUNT_CORPS_SIMULATION_WAR
	       || id == CRID_MEMBER_COUNT_CORPS_COMPETITION
	       ;
}



inline bool IsPeriodCorpRepu(short id)
{
	return (id == CRID_ACTIVITY || id == CRID_HOLY_BOSS_WEEK_COUNT || id == CRID_ABDICATE || id == CRID_RACE || id == CRID_MANAGER_BONUS_MASTER || id == CRID_MANAGER_BONUS_VICE_MASTER || id == CRID_MOMENT_DAY_COUNT || id == CRID_DAY_ACTIVITY || id == CRID_PVE_ACTIVITY);
}

inline bool IsNonnegativeRepu(short id)
{
	return (id == GNET::CRID_SEDUCE_DRAGON_FACILITY_ENERGY);
}

// 结义内投票
enum FAMILY_VOTE_REASON
{
	FAMILY_VOTE_RS_CHANGE_NAME = 0,	// 改结义名
	FAMILY_VOTE_RS_EXPEL_MEMBER,	// 开除成员
	FAMILY_VOTE_RS_GLAD_BIRTH,	// 成员生日（以下不是投票而是喜事）
	FAMILY_VOTE_RS_GLAD_MARRY,	// 成员结婚
	FAMILY_VOTE_RS_GLAD_FACTION,	// 成员建立帮派
	FAMILY_VOTE_RS_GLAD_TOP,	// 成员上排行榜
	FAMILY_VOTE_RS_COUNT,	// 理由数
};
enum FAMILY_VOTE_STATUS
{
	FAMILY_VOTE_ST_VOTING = 0,	// 投票中
	FAMILY_VOTE_ST_AGREED = 1,	// 投票结束，通过
	FAMILY_VOTE_ST_DISAGREED = 2,	// 投票结束，拒绝
	FAMILY_VOTE_ST_GLAD = 3,	// 喜事，不是投票
};
enum FAMILY_VOTE_EVENT
{
	FAMILY_VOTE_EVT_NEW = 0,	// 开始新投票
	FAMILY_VOTE_EVT_REPLY = 1,	// 有人投票
	FAMILY_VOTE_EVT_END = 2,	// 投票结束
	FAMILY_VOTE_EVT_DEL = 3,	// 删除条目
};
enum TITLE_INDEX
{
	TITLE_INDEX_NULL = 0,	// 不使用title
	TITLE_INDEX_MARRIAGE_GROOM = 1,	// 给新郎的称号，比如***的相公
	TITLE_INDEX_MARRIAGE_BRIDE = 2,	// 给新娘的称号，比如***的娘子
	TITLE_INDEX_FAMILY = 3,	// 使用结义title
	TITLE_INDEX_SECT = 4,	// 使用师徒title
	TITLE_INDEX_FACTION_POS = 5,	// 使用帮派职位title
	TITLE_INDEX_FACTION_TI = 6,	// 使用帮派荣誉身份title
	//TITLE_INDEX_INTIMATE_FRIEND = 7,// 使用情缘好友称号
	TITLE_INDEX_SECT_MENTOR	= 8,	// 师徒称号师傅
	TITLE_INDEX_SECT_DISCIPLE = 9,	// 师徒称号徒弟
	TITLE_INDEX_ARENA_GROUP = 10,   //战队称号，是个假的称号,为了在gs能取到战队名字
	TITLE_INDEX_CITY_INFO = 11,   //服务器对战城市信息称号，是个假的称号,为了在gs能取到城市信息
	TITLE_INDEX_ELIMINATE_GROUP = 12,   //淘汰赛战队称号，是个假的称号,为了在gs能取到淘汰赛战队名字

	TITLE_INDEX_NORMAL_BEGIN = 1000,	// 普通title的起始id
};

enum SECT_STATUS
{
	SECT_STATUS_NULL = 0,	// 还没有拜师
	SECT_STATUS_DISCIPLE = 1,	// 当徒弟了
	SECT_STATUS_GRADUATE = 2,	// 出师了
	SECT_STATUS_MENTOR = 3,	// 收徒了，即使有师傅也是出师状态
};

enum SECT_QUIT_REASON
{
	SECT_QUIT_REASON_EXPEL = 0,	// 被驱逐
	SECT_QUIT_REASON_QUIT = 1,	// 叛师
	SECT_QUIT_REASON_GRADUATE = 2,	// 出师
};

enum
{
	SECT_DISCIPLE_LEVEL_LIMIT = 60,	// 徒弟等级上限
	SECT_MENTOR_LEVEL_LIMIT = 50,	// 师父等级下限
	SECT_VICE_MENTOR_MAX = 6,	// 徒弟最多有几个记名师父
	SECT_OFFLINE_SAFE_TIME = 72 * 3600,	// 多久不上线可被无责任开除
	SECT_TEACH_AMITY = 20,		// 教徒弟互相增加友好度
	SECT_CONSULT_AMITY = 3,		// 请教记名师父增加友好度
	SECT_CONSULT_REPUTATION = 5,	// 请教记名师父增加善值
};

enum
{
	FRIEND_GROUP_SPOUSE 	= 0x8000,	// 结婚
	FRIEND_GROUP_FAMILY 	= 0x4000,	// 结义
	FRIEND_GROUP_SECT 	= 0x2000,	// 师徒组：师父、记名师父、弟子
	FRIEND_GROUP_INTIMATE 	= 0x1000, 	// 情缘好友
	FRIEND_GROUP_INLAWS 	= 0x0800, 	// 亲家


	FRIEND_GROUP_SYS_MASK 	= 0xff00,
	FRIEND_GROUP_USER_MASK 	= 0x00ff,
};

enum BUFF_ADD_REASON
{
	BUFF_ADD_BY_FAMILY_CREATE = 0,	// 结义成功
	BUFF_ADD_BY_SECT_QUIT = 1,	// 叛离师门
	BUFF_ADD_BY_SECT_EXPEL = 2,	// 逐出弟子
	BUFF_ADD_BY_SECT_ENCOURAGE1 = 3,	// 师父鼓励弟子
	BUFF_ADD_BY_SECT_ENCOURAGE2 = 4,	// 记名师父鼓励弟子
};

enum TITLE_ADD_REASON
{
	TITLE_ADD_BY_SECT_EXPEL = 0,	// 逐出弟子
	TITLE_ADD_BY_HOME_STEAL_CAUGHT = 1, // 家园偷窃被抓
};

enum ALLOC_NAME_CATEGORY
{
	ALLOC_FACTION_NAME = 1,
	ALLOC_ARENA_GROUP_NAME = 2,
	ALLOC_IDIP_FACTION_NAME = 3,
};

enum PLAYER_FACTION_STATE
{
	PAS_ACTIVITY		= 0x01,	// 活跃
	PAS_FACTION_ACTIVITY	= 0x02,	// 帮派活跃
	PAS_LEAVE_FACTION	= 0x04,	// 离开帮派 (ds->gs)

	PAS_JOIN_FACTION	= 0x20,	// 加入帮派 (ds->gs)

	PAS_GS_MASK		= 0,
	PAS_DS_MASK		= PAS_ACTIVITY | PAS_FACTION_ACTIVITY | PAS_LEAVE_FACTION | PAS_JOIN_FACTION,
};

enum FACTION_DS_SYNC_MASK
{
	FDSM_MEMBER		= 0x01,	//同步玩家数据
	FDSM_LEVEL		= 0x02,	//等级
	FDSM_VALUE		= 0x04,	//普通数据
	FDSM_SUBFACTION		= 0x08,	//分舵数据
	FDSM_PARTNER		= 0x10,	//合作帮派数据
	FDSM_CLOSEBASE		= 0x20,	//完全关闭基地
	FDSM_VOLATILITY		= 0x40,	//帮派易变属性
};

enum FACTION_REBEL_STATUS
{
	FRS_SUCCESS		= 0x01, // 造反成功
	FRS_FAILED		= 0x02, // 造反失败，由于投票不通过
	FRS_REBEL		= 0x03, // 有人造反
	FRS_STOP		= 0x04, // 平叛成功，由于有人一票否决
};

enum FACTION_BASE_NOENTER_TYPE
{
	FBCT_BASECLOSE		= 1,	//基地关闭
	FBCT_NOBASE		= 2,	//没有基地
};

enum FACTION_COLLECTLEADER_TYPE
{
	FCT_PUSHLEADER	= 0,	//复杂推送信息处理的相关领导
	FCT_MASTER	= 1,	//帮主
	FCT_LEADER_CANADD = 2,	//有邀请权限的领导

	FCT_COUNT,
};

enum FACTION_COLLECTLEADER_STATUS
{
	FCS_ALL		= 0,	//全部信息，不管在线与否
	FCS_OFFLINE	= 1,	//离线领导
	FCS_ONLINE	= 2,	//在线领导
};

enum
{
	CORPS_MIN_NAME_LEN		= 2,		//社团名字最短
	CORPS_MAX_NAME_LEN		= 6,		//社团名字最长
	CORPS_MIN_RECRUITMENT_LEN	= 1,		//社团招聘宣言最短
	CORPS_MAX_RECRUITMENT_LEN	= 50,		//社团招聘宣言最长
	CORPS_ANNOUNCE_MIN_LEN		= 1,		//社团宣言最小长度
	CORPS_ANNOUNCE_MAX_LEN		= 200,		//社团宣言最大长度
	SECRET_MSG_SIZE			= 200,		//暗恋悄悄化长度
	LEAVE_CORPS_COOLDOWN		= 24 * 60 * 60,	//离开社团后进入新社团时间限制
	CORPS_MAX_APPLY_SIZE		= 50,		//社团最多申请数
	CORPS_APPLY_LIVE_TIME		= 24 * 60 * 60,	//申请最长时间
	APPLY_CORPS_MIN_LEVEL		= 10,		//加入社团最小等级
	APPLY_CORPS_APPRENTICE_MAX_LEVEL = 40,		//成为社团学徒最大等级
	CORPS_MONEY_PER_CON		= 100,		// 每捐赠多少钱可以获取1点帮贡
	MIN_CONTRI_CORP_MONEY		= 100,		// 最少捐献钱数
	MARRIAGE_MSG_SIZE		= 100,		// 情侣宣言长度
};

enum
{
	FACTION_MAX_NAME_LEN		= 16,			//帮派最长名字
	FACTION_BASE_COST		= 20000,	// 获取帮派基地费用
	FACTION_EXT_ROOM_COST		= 0,		// 帮派升级厢房费用
	FACTION_MEMBER_PER_PAGE		= 8,		// 显示帮派成员时每页显示数量
	FACTION_LEVEL_MAX		= 9,		// 帮派最高等级
	FACTION_VALUE_TRANSFER		= 80,		// 帮派基本属性转移 百分比
	FACTION_DOMAIN_TRANSFER_DIFF	= 10,		// 帮派产业不同时数值转移 百分比
	FACTION_DOMAIN_TRANSFER_SAME	= 40,		// 帮派产业相同时数值转移 百分比
	FACTION_ACTIVE_CONSUME_COUNT	= 5,		// 帮派激活所需要的物品数量
	FACTION_BASE_RENT_PER_LEVEL	= 100000,	// 帮会基地每等级需要租金
	FACTION_SUB_MAX			= 3,		// 帮派最多分舵数
	FACTION_TEMP_MEMBER_LEVEL	= 12,		// 正式成员等级阈值
	FACTION_NICKNAME_MAX_SIZE	= 12,		// 帮派昵称最大字节数
	FACTION_ANNOUNCE_MAX_SIZE	= 256,		// 帮派宣言最大字节数
	FACTION_MONEY_PER_CON		= 1000000,	// 每捐赠多少钱可以获取5点帮贡
	FACTION_BASE_MIN_ACTIVITY	= 20,		// 帮派基地开启活跃度下限
	FACTION_BASE_MIN_MEMBERS	= 10,		// 帮派基地开启人数下限
	FACTION_BASE_MIN_MONEY		= 0,		// 帮派基地开启资金下限
	FACTION_JOIN_MINLEVEL		= 12,		// 加入帮派需要的最小等级
	FACTION_OWNCITY_MAX		= 17,           // 每个帮派最多拥有17势力地图
	FACTION_BASE_TACTIVITY_TIME	= 72 * 60 * 60,		//下限持续时间
	FACTION_BASE_MEMBERS_TIME	= 72 * 60 * 60,		//下限持续时间
	FACTION_BASE_MONEY_TIME		= 3 * 60 * 60,		//下限持续时间
	FACTION_BASE_RENT_FREE_TIME	= 7 * 24 * 60 * 60,	// 帮派基地第一周租金免费
	FACTION_BASE_RENT_TIME		= 12 * 60 * 60,		// 帮会基地租金周期
	FACTION_SUBFACTION_COOLDOWN	= 24 * 60 * 60,		// 删除分舵冷却时间
	FACTION_MEMBER_ACTIVITY		= 3 * 24 * 60 * 60,	// 帮派活跃玩家定义，上线时间
};

//使用const int 可以便于运行时修改测试, 只能当做局部变量改变


enum FACTION_MERAGE_MEMBER_OP
{
	FMMO_ADD	= 0,	//添加
	FMMO_DEL	= 1,	//删除
	FMMO_OK		= 2, 	//确定
	FMMO_ALL	= 3,	//获取
	FMMO_SELF	= 4,	//添加个人
};

enum FACTION_UPGRADE_TYPE
{
	FUT_NONE	= 0,
	FUT_LEVEL	= 1,	//升级等级
	FUT_BASE	= 2,	//开宗建派
	FUT_ACTIVE	= 3,	//激活帮派
	FUT_EXT_ROOM	= 4,	//升级厢房
	FUT_BASE_ACTIVE	= 5,	//重新激活基地
};

enum FACTION_BUILDING_TYPE
{
	DSEXP_FACTIONBLD_PLACE_SPECIAL2 = 8,	//EXP_FACTIONBLD_PLACE_SPECIAL2
	DSEXP_FACTIONBLD_PLACE_SPECIAL3 = 9,	//EXP_FACTIONBLD_PLACE_SPECIAL3
	DSEXP_FACTIONBLD_PLACE_SPECIAL4 = 10,	//EXP_FACTIONBLD_PLACE_SPECIAL4
};

enum FACTION_STATUS	// 帮派状态
{
	FS_NORMAL	= 0,	// 正常
	//FS_REBEL	= 1,	// 造反中
	FS_MERGEREQ	= 2,	// 合并请求
	FS_MERGEVOTE	= 3,	// 合并投票
	FS_MERGEVOTEEND = 4,	// 投票结束
	FS_MERGESTART	= 5,	// 开始合并,锁定帮派操作
	FS_CLEAR_DATA	= 6,	// GSLoad used,新数据
	FS_MERGEPRESTART = 7,	// 合并双方能够开始合并时(还未合并)的状态
};

enum FACTION_POSITION	// 帮派职位
{
	FP_NONE = 0,		// 帮众
	// 独立职位
	FP_MASTER = 1,		// 帮主
	FP_VICEMASTER1 = 2,	// 副帮主
	FP_VICEMASTER2 = 3,
	FP_VICEMASTER3 = 4,
	FP_HUFA1 = 11,		// 护法
	FP_HUFA2 = 12,
	FP_ZHANGLAO1 = 21,	// 长老
	FP_ZHANGLAO2 = 22,
	FP_ZHANGLAO3 = 23,
	FP_ZHANGLAO4 = 24,
	FP_SUBMASTER 	= 31,	// 分舵主
	//人数限制
	FP_BEAUTY	= 42,	// 帮花
	FP_TALKER	= 43,	// 话唠
	FP_KNOW_ALL	= 44,	// 百事通
	FP_GOOD_GUY	= 45,	// 老好人
	FP_ELITE	= 46,	// 精英
	FP_S_PET_TUTOR	= 47,	// 首席门徒导师
	FP_S_CHEMIST	= 48,	// 首席药师
	FP_S_COOK	= 49,	// 首席厨师
	FP_S_STONE_TUTOR = 50,	// 首席金石师
	FP_S_WOOD_TUTOR	= 51,	// 首席木师
	FP_S_CLOTH_TUTOR = 52,	// 首席布师
	FP_S_SOCIALITE	= 53,	// 首席社交师
	FP_PET_TUTOR	= 54,	// 高级门徒导师
	FP_CHEMIST	= 55,	// 高级药师
	FP_COOK		= 56,	// 高级厨师
	FP_STONE_TUTOR	= 57,	// 高级金石师
	FP_WOOD_TUTOR	= 58,	// 高级木师
	FP_CLOTH_TUTOR	= 59,	// 高级布师
	FP_SOCIALITE	= 60,	// 高级社交师
	FP_UNDERGRADUATE = 61,	// 新手辅导员
	FP_GRADUATE	= 62,	// 进阶辅导员
	FP_DOCTOR	= 63,	// 老手辅导员

	// 附属职位
	FP_MASTER_SPOUSE = 101,		// 帮主配偶
	FP_VICEMASTER_SPOUSE = 102,	// 副帮主配偶
	FP_MASTER_TRUSTED = 103,	// 帮主亲信
	FP_HUFA_TRUSTED = 104,		// 护法亲信
	FP_ZHANGLAO_TRUSTED = 105,	// 长老亲信
	FP_SUBMASTER_TRUSTED = 106,	// 分舵主亲信


	FP_UNKNOWN = 255,	// 非本帮派成员
};

enum FACTION_TITLE // 帮派荣誉身份
{
	FTI_NONE = 0,	// 帮众
	FTI_1 = 1,	// 1阶成员
	FTI_2 = 2,	// 2阶成员
	FTI_3 = 3,	// 3阶成员
	FTI_4 = 4,	// 4阶成员
	FTI_5 = 5,	// 5阶成员
	FTI_6 = 6,	// 6阶成员
	FTI_7 = 7,	// 7阶成员
	FTI_8 = 8,	// 8阶成员
	FTI_9 = 9,	// 9阶成员
	FTI_10 = 10,	// 10阶成员
	FTI_TMP = 101,	// 挂名成员
};

enum FACTION_SUB_NAME
{
	FSN_NONE	= 0,	// 空
	FSN_1		= 1,	// 青龙
	FSN_2		= 2,	// 白虎
	FSN_3		= 3,	// 朱雀
	FSN_4		= 4,	// 玄武
	FSN_5		= 5,	// 惊云
	FSN_6		= 6,	// 秀月
	FSN_7		= 7,	// 琴心
	FSN_8		= 8,	// 神策
	FSN_9		= 9,	// 开天
	FSN_10		= 10,	// 劈地
	FSN_11		= 11,	// 逐日
	FSN_12		= 12,	// 奔月
	FSN_13		= 13,	// 乾坤
	FSN_14		= 14,	// 天机
	FSN_15		= 15,   // 神途
	FSN_16		= 16,	// 霸业

};

enum FACTION_CHANGE_MONEY_TYPE
{
	FAMT_NORMAL	= 0,	//通用
	FAMT_TASK	= 1,	//任务
	FAMT_CONTRI	= 2,	//捐赠
	FAMT_SERVICE	= 3,	//维护性发放

	FDMT_UPGRADE			= 101,	//升级
	FDMT_SERVICE			= 102,	//维护费
	FDMT_UPGRADE_SHOP		= 103,	//升级商店
	FDMT_UPGRADE_SKILL_ROOM		= 104,	//升级练功房
	FDMT_UPGRADE_SKILL		= 105,	//升级技能
	FDMT_UPGRADE_CHARIOT_CAMP       = 106,    //升级战车营
	FDMT_UPGRADE_CHARIOT	        = 107,    //升级战车
	FDMT_SET_BADGE			= 108,	//设置社团徽章
	FDMT_SET_SUPPORT_SIDE		= 109,	//设置社团拥护
};

enum NATION_DONATE_TYPE
{
	NDT_MONEY	= 1,	//金钱
};

enum NATION_ID
{
	NATION_ID_INVALID 	= 0,
	NATION_ID_BING_ZHOU 	= 1,
	NATION_ID_JI_ZHOU	= 2,
	NATION_ID_YU_ZHOU	= 3,
	NATION_ID_XU_ZHOU 	= 4,
	NATION_ID_JING_ZHOU 	= 5,
	NATION_ID_YI_ZHOU 	= 6,
};

enum FACTION_THING	// 帮派权限操作
{
//DS相关操作
	FT_ADD		= 1,	// 加成员
	FT_UPGRADE	= 2,	// 帮派升级
	FT_ANNOUNCE	= 3,	// 修改宣言
	FT_ABDICATE	= 4,	// 传位
	FT_REBEL1	= 5,	// 7天篡位
	FT_SUPPRESS	= 6,	// 反对7天篡位
	FT_REBEL2	= 7,	// 15天篡位
	FT_RESIGN	= 8,	// 辞职
	FT_EXPEL	= 9,	// 开除
	FT_QUIT		= 10,	// 退出
	FT_OP_MERGE	= 11,	// 选择合并成员列表
	FT_MERGE_OK	= 12,	// 确认合并成员列表
	FT_APPLY_SUB	= 13,	// 申请分舵
	FT_OP_SUB	= 14,	// 建立、删除分舵
	FT_SUBCITYAPPLY = 15,	// 申请势力范围
	FT_SUBCITYAPPLYDEAL = 16,// 处理势力申请
	FT_AUCTION_OFFERPRICE = 17,// 龙头竞价
	FT_MAINCITYOPER = 18,	// 总舵处理
	FT_SUBRESET	= 19,	// 分舵设置
	FT_CONTRI_MONEY = 20,	// 捐钱
	FT_BASE_ACTIVE	= 21,	//重新激活已经关闭的基地
	FT_SETTIGUAN	= 22,	//设置玩家战斗队
	FT_TIGUAN	= 23,	//发起踢馆

//GS相关操作
	FT_STORE	= 50,	// 操作基地仓库
	FT_UPGRADE_BUILD = 51,	// 升级基地建筑
	FT_GET_WELWARE	= 52,	// 获取福利
	FT_HIREINFO	= 53,	// 招工信息管理
	FT_OPENACTIVITY	= 54,	// 打开活动
	FT_GET_SALARY	= 55,	// 领工资
	FT_GET_BONUS	= 56,	// 领供奉
	FT_COREMSG	= 57,	// 操作核心消息仓库
	FT_NORMALMSG	= 58,	// 操作小道消息仓库
	FT_TREASURE	= 59,	// 操作宝物仓库
	FT_TREASURE_TRAP = 60,	// 宝物机关操作
	FT_CONTRI_ITEM	= 61,	// 捐物品
	FT_PARTY	= 62,	// 用帮派资金打开宴会
	FT_DELACTIVITY	= 63,	// 删除活动
	FT_GET_MONEYTASK = 64,	// 获取发粮任务
};

enum FACTION_GETTYPE//获取帮派方式
{
	FG_GLOBLEGET = 0,	//无筛选条件，直接按照帮派ID获取
	FG_HAVEBASE = 1,	//有帮派基地
	FG_NOBASE = 2,		//没有帮派基地
};

enum FACTION_INVITE_TYPE //帮派成员邀请方式
{
	FIT_PUSH = 1,	//系统推送
	FIT_INVITE = 2,	//玩家主动邀请
};

enum FACTION_MERGE_REQ
{
	FMR_AGREE = 1, //同意合并
	FMR_DISAGREE = 2,//不同意合并
};

enum FACTION_SYNC_HIREINFO_TYPE
{
	FSHT_CHANGE = 0,
	FSHT_UPDATE = 1,
	FSHT_INIT = 2,
};

enum FACTION_SYNC_HIREINFO_RESULT
{
	FSHR_SUCCESS = 0,
	FSHR_FAILED = 1,
	FSHR_TIMEOUT = 2,
};

enum FACTION_VOTE_RESULT
{
	FVR_DEFAULT = 0,//初始化值，未决状态
	FVR_PASS = 1,//通过
	FVR_NOTPASS = 2,//没有通过
};

enum FACTION_ACTIVITY_RESULT
{
	FAR_SUCCESS = 0,
	FAR_NOTALLOW = 1,
	FAR_CLUBLESS = 2,
	FAR_TIMEOUT = 3,
	FAR_SERVERERROR = 4,
	FAR_ACTIVITYLESS = 5,
	FAR_WARMLESS = 6,
	FAR_INSTANCEEXIST = 7,
};

enum FACTION_ACTIVITY_STATUS_MODE
{
	FASM_READY	= 0,	// 活动预备开始
	FASM_ON		= 1,	// 活动开启中
	FASM_OFF	= 2,	// 活动结束
	FASM_ON_OFF	= 3,	// 开启后自动结束，不需要在结束时再通知客户端了
};

enum FACTION_ACTIVITY_TYPE
{
	FAT_ACTIVITY	= 0, //活动
	FAT_PARTY	= 1, //宴会
	FAT_LVUPCELE	= 2, //升级庆典
	FAT_AUCTION	= 3, //竞标
};

enum FACTION_RECORD_THING_TYPE
{
	FRTT_AUCPOINT_DONATE	= 1,	//捐赠竞标点
	FRTT_AUCPOINT_RECEIVE	= 2,	//接受竞标点
	FRTT_CITY_ADD		= 3,	//增加势力地图
	FRTT_CITY_DEL		= 4,	//删除势力地图
	FRTT_MAIN_EXCHANGE	= 5,	//搬迁总舵
	FRTT_TIGUAN		= 6,	//踢馆
	FRTT_BE_TIGUAN		= 7,	//被踢馆
};

enum FACTION_TEAM_STATUS_TYPE
{
	FTST_TIGUAN	= 0,

	FTST_MAX,
};

enum FACTION_TEAM_STATUS_MASK
{
	FTSM_TIGUAN	= 1 << FTST_TIGUAN,
};

enum FACTIONCITY_SAVE_TYPE
{
	FCST_KING	= 0x00000001,
	FCST_SUBADD	= 0x00000002,
	FCST_SUBDEL	= 0x00000004,
	FCST_BASIC	= 0x00000008,
	FCST_APPLY	= 0x00000010,
	FCST_INITAUCTION = 0x00000020,
	FCST_CLEARAUCTION = 0x00000040,
	FCST_TOTALPOINT = 0x00000080,
};

enum FACTIONCITY_INIT_CITY
{
	FIC_FUZHOU	= 68,
	FIC_HENGSHAN	= 67,
};

enum FACTIONCITY_SUB_OPER_TYPE
{
	FCSOT_ADD	= 1,
	FCSOT_DEL	= 2,
};

enum FCITY_GET_TYPE
{
	FGT_BASE	= 0x01,
	FGT_MAIN	= 0x02,
	FGT_SUB		= 0x04,
	FGT_APPLY	= 0x08,

	FGT_ALL		= FGT_BASE | FGT_MAIN | FGT_SUB | FGT_APPLY,
};

enum FCITY_RESET_TYPE
{
	FCRT_SUB	= 0,
	FCRT_WEIGHT	= 1,
};

enum FACTION_GATHER_INFO_RESULT
{
	FGIR_SUCCESS	= 0,//可以采集
	FGIR_NOCITY	= 1,//无势力地图
};

// 在数据库vote_mask字段中，如果该位置1，表示该玩家已经投票，置0，表示该玩家未投票
// 在数据库vote_result字段中(如果vote_mask字段中对应位已经被置1),置1，表示同意，置0，表示不同意
enum VOTE_MASK
{
	VM_MERGE_VOTE		= 0x00000001,
	VM_SCORE_VOTE		= 0x00000002,
	VM_MERGE_TRANSFER	= 0x00000004,	//准备被转移的人员
	VM_MERGED_VOTE		= 0x00000008,
	VM_REBEL_VOTE		= 0x00000010,
	VM_SALARY_GET		= 0x00000020,	//领取薪水标志
	VM_BONUS_GET		= 0x00000040,	//领取供奉标志
	VM_WELF_EXP_GET		= 0x00000080,	//领取福利经验标识

	VM_GS_ONLY_MASK		= VM_WELF_EXP_GET,
};

enum LINK_TYPE
{
	LINK_TYPE_LS   = 1,
	LINK_TYPE_CS   = 2,
	LINK_TYPE_IWEB = 3,
};

//与GS内部定义一致
enum USE_MONEY_TYPE_MASK
{
	UMT_BIND  = 0x01, //使用绑定币
	UMT_TRADE = 0x02, //使用交易币
};

enum USE_MONEY_TYPE
{
	USE_MT_BIND,
	USE_MT_TRADE,

	USE_MT_COUNT,
};

enum USE_CASH_TYPE_MASK
{
	UCT_BIND  = 0x01, //使用绑定元宝
	UCT_TRADE = 0x02, //使用交易元宝
};

enum TOPLIST_NAME
{
	TPN_LEVEL			= 1,	//等级榜
	TPN_LEVEL_OLDDAY 		= 2,	//等级一天前老榜
	TPN_PERSON_FIGHT 		= 3,	//个人战力榜
	TPN_PERSON_FIGHT_OLDDAY		= 4,	//个人战力老榜
	TPN_FIGHT 			= 5,	//综合战斗力榜
	TPN_FIGHT_OLDDAY 		= 6,	//综合战斗力一天前老榜
	TPN_PET_FIGHT 			= 7,	//宠物战力
	TPN_PET_FIGHT_OLDDAY 		= 8,	//宠物战力一天前老榜
	TPN_FLOWER_SEND 		= 9,	//累计送花榜
	TPN_FLOWER_SEND_OLDDAY 		= 10,	//累计送花老榜
	TPN_FLOWER_RECV 		= 11,	//累计收鲜花榜
	TPN_FLOWER_RECV_OLDDAY 		= 12,	//累计收鲜花榜老榜
	TPN_FLOWER_SEND_WEEK 		= 13,	//周累计鲜花榜
	TPN_FLOWER_SEND_WEEK_OLDDAY 	= 14,	//周累计鲜花榜老榜
	TPN_FLOWER_RECV_WEEK		= 15,	//周累计收花
	TPN_FLOWER_RECV_WEEK_OLDDAY	= 16,	//周累计收花老榜
	TPN_FIGHT_PROF_BLADE		= 17,	//职业综合战力 刀 天罡
	TPN_FIGHT_PROF_SPEAR		= 18,	//职业综合战力 枪 夜叉
	TPN_FIGHT_PROF_SWORD		= 19,	//职业综合战力 剑 琼华
	TPN_FIGHT_PROF_RING		= 20,	//职业综合战力 环 巫月
	TPN_FIGHT_PROF_UNBRELLA 	= 21,	//职业综合战力 伞 霓裳
	TPN_FIGHT_PROF_BLADE_OLDDAY	= 22,	//职业综合战力老榜 刀 天罡
	TPN_FIGHT_PROF_SPEAR_OLDDAY	= 23,	//职业综合战力老榜 枪 夜叉
	TPN_FIGHT_PROF_SWORD_OLDDAY	= 24,	//职业综合战力老榜 剑 琼华
	TPN_FIGHT_PROF_RING_OLDDAY    	= 25,	//职业综合战力老榜 环 巫月
	TPN_FIGHT_PROF_UNBRELLA_OLDDAY	= 26,	//职业综合战力老榜 伞 霓裳
	TPN_CONJUGAL			= 27,	//结婚恩爱值排行榜
	TPN_CONJUGAL_OLDDAY		= 28,	//结婚恩爱值排行版老榜
	TPN_CONJUGAL_WEEK		= 29,	//结婚周恩爱值排行榜
	TPN_CONJUGAL_WEEK_OLDDAY	= 30,	//结婚周恩爱值排行榜老榜
	TPN_POWERDICE_1			= 31,
	TPN_POWERDICE_2         = 32,
	TPN_POWERDICE_3         = 33,
	TPN_POWERDICE_4         = 34,
	TPN_POWERDICE_5         = 35,
	TPN_POWERDICE_6         = 36,
	TPN_POWERDICE_7         = 37,
	TPN_POWERDICE_8         = 38,
	TPN_POWERDICE_9         = 39,
	TPN_POWERDICE_10         = 40,
	TPN_POWERDICE_11         = 41,
	TPN_POWERDICE_12         = 42,
	TPN_POWERDICE_13         = 43,
	TPN_POWERDICE_14         = 44,
	TPN_POWERDICE_15         = 45,
	TPN_POWERDICE_16         = 46,
	TPN_POWERDICE_17         = 47,
	TPN_POWERDICE_18         = 48,
	TPN_POWERDICE_19         = 49,
	TPN_POWERDICE_20         = 50,
	TPN_POWERDICE_21         = 51,
	TPN_POWERDICE_22         = 52,
	TPN_POWERDICE_23         = 53,
	TPN_POWERDICE_24         = 54,
	TPN_POWERDICE_25         = 55,
	TPN_POWERDICE_26         = 56,
	TPN_POWERDICE_27         = 57,
	TPN_POWERDICE_28         = 58,
	TPN_POWERDICE_29         = 59,
	TPN_POWERDICE_30         = 60,
	TPN_POWERDICE_OLD_1         = 61,
	TPN_POWERDICE_OLD_2         = 62,
	TPN_POWERDICE_OLD_3         = 63,
	TPN_POWERDICE_OLD_4         = 64,
	TPN_POWERDICE_OLD_5         = 65,
	TPN_POWERDICE_OLD_6         = 66,
	TPN_POWERDICE_OLD_7         = 67,
	TPN_POWERDICE_OLD_8         = 68,
	TPN_POWERDICE_OLD_9         = 69,
	TPN_POWERDICE_OLD_10         = 70,
	TPN_POWERDICE_OLD_11         = 71,
	TPN_POWERDICE_OLD_12         = 72,
	TPN_POWERDICE_OLD_13         = 73,
	TPN_POWERDICE_OLD_14         = 74,
	TPN_POWERDICE_OLD_15         = 75,
	TPN_POWERDICE_OLD_16         = 76,
	TPN_POWERDICE_OLD_17         = 77,
	TPN_POWERDICE_OLD_18         = 78,
	TPN_POWERDICE_OLD_19         = 79,
	TPN_POWERDICE_OLD_20         = 80,
	TPN_POWERDICE_OLD_21         = 81,
	TPN_POWERDICE_OLD_22         = 82,
	TPN_POWERDICE_OLD_23         = 83,
	TPN_POWERDICE_OLD_24         = 84,
	TPN_POWERDICE_OLD_25         = 85,
	TPN_POWERDICE_OLD_26         = 86,
	TPN_POWERDICE_OLD_27         = 87,
	TPN_POWERDICE_OLD_28         = 88,
	TPN_POWERDICE_OLD_29         = 89,
	TPN_POWERDICE_OLD_30         = 90,
	TPN_HOMETOWN		     = 91,	//家园排行榜
	TPN_HOMETOWN_OLDDAY	     = 92,	//家园排行榜老榜
	TPN_MOONLIGHT		     = 93,	//月光排行榜
	TPN_MOONLIGHT_OLDDAY	     = 94,	//月光排行榜老榜
	TPN_FIGHT_PROF_BOW	     = 95,	//职业综合战力榜 弓箭 飞羽
	TPN_FIGHT_PROF_BOW_OLDDAY    = 96,	//职业综合战力榜老榜 弓箭 飞羽
	TPN_TIANGUAN		     = 97,	//闯天关新榜
	TPN_TIANGUAN_OLDDAY	     = 98,	//闯天关老榜
	TPN_FIGHT_PROF_SUMMON	     = 99,	//职业综合战力榜 召唤 御灵
	TPN_FIGHT_PROF_SUMMON_OLDDAY = 100,	//职业综合战力榜老榜 召唤 御灵
	TPN_FIGHT_PROF_TIANSHA	     = 101,	//职业综合战力榜 天煞
	TPN_FIGHT_PROF_TIANSHA_OLDDAY = 102,	//职业综合战力榜老榜 天煞

	// 屠龙考核职业 排行榜/排行榜老榜 (103-134)！！！
	// 职业prof排行榜tid = TPN_LADDER_PROF1 + (prof - 1) * 2
	// 职业prof排行榜老榜tid = TPN_LADDER_PROF1_OLD + (prof - 1) * 2
	TPN_LADDER_PROF1            = 103,  // 屠龙考核职业1排行榜
	TPN_LADDER_PROF1_OLD        = 104,  // 屠龙考核职业1排行榜老榜
	TPN_LADDER_PROF2            = 105,  // 屠龙考核职业2排行榜
	TPN_LADDER_PROF2_OLD        = 106,  // 屠龙考核职业2排行榜老榜
	TPN_LADDER_PROF3            = 107,  // 屠龙考核职业3排行榜
	TPN_LADDER_PROF3_OLD        = 108,  // 屠龙考核职业3排行榜老榜
	TPN_LADDER_PROF4            = 109,  // 屠龙考核职业4排行榜
	TPN_LADDER_PROF4_OLD        = 110,  // 屠龙考核职业4排行榜老榜
	TPN_LADDER_PROF5            = 111,  // 屠龙考核职业5排行榜
	TPN_LADDER_PROF5_OLD        = 112,  // 屠龙考核职业5排行榜老榜
	TPN_LADDER_PROF6            = 113,  // 屠龙考核职业6排行榜
	TPN_LADDER_PROF6_OLD        = 114,  // 屠龙考核职业6排行榜老榜
	TPN_LADDER_PROF7            = 115,  // 屠龙考核职业7排行榜
	TPN_LADDER_PROF7_OLD        = 116,  // 屠龙考核职业7排行榜老榜
	TPN_LADDER_PROF8            = 117,  // 屠龙考核职业8排行榜
	TPN_LADDER_PROF8_OLD        = 118,  // 屠龙考核职业8排行榜老榜
	TPN_LADDER_PROF9            = 119,  // 屠龙考核职业9排行榜
	TPN_LADDER_PROF9_OLD        = 120,  // 屠龙考核职业9排行榜老榜
	TPN_LADDER_PROF10			= 121,  // 屠龙考核职业10排行榜
	TPN_LADDER_PROF10_OLD		= 122,  // 屠龙考核职业10排行榜老榜
	TPN_LADDER_PROF11			= 123,  // 屠龙考核职业11排行榜
	TPN_LADDER_PROF11_OLD		= 124,  // 屠龙考核职业11排行榜老榜
	TPN_LADDER_PROF12			= 125,  // 屠龙考核职业12排行榜
	TPN_LADDER_PROF12_OLD		= 126,  // 屠龙考核职业12排行榜老榜
	TPN_LADDER_PROF13			= 127,  // 屠龙考核职业13排行榜
	TPN_LADDER_PROF13_OLD		= 128,  // 屠龙考核职业13排行榜老榜
	TPN_LADDER_PROF14			= 129,  // 屠龙考核职业14排行榜
	TPN_LADDER_PROF14_OLD		= 130,  // 屠龙考核职业14排行榜老榜
	TPN_LADDER_PROF15			= 131,  // 屠龙考核职业15排行榜
	TPN_LADDER_PROF15_OLD		= 132,  // 屠龙考核职业15排行榜老榜
	TPN_LADDER_PROF16			= 133,  // 屠龙考核职业16排行榜
	TPN_LADDER_PROF16_OLD		= 134,  // 屠龙考核职业16排行榜老榜

	// 星辰阶梯活动排行榜 (135-174)！！！多预留10个
	TPN_TREASURE_LOFT_DICE1		= 135,	// 星辰阶梯活动1排行榜
	TPN_TREASURE_LOFT_DICE1_OLD	= 136,	// 星辰阶梯活动1排行榜老榜
	TPN_TREASURE_LOFT_DICE2		= 137,	// 星辰阶梯活动2排行榜
	TPN_TREASURE_LOFT_DICE2_OLD	= 138,	// 星辰阶梯活动2排行榜老榜
	TPN_TREASURE_LOFT_DICE3		= 139,	// 星辰阶梯活动3排行榜
	TPN_TREASURE_LOFT_DICE3_OLD	= 140,	// 星辰阶梯活动3排行榜老榜
	TPN_TREASURE_LOFT_DICE4		= 141,	// 星辰阶梯活动4排行榜
	TPN_TREASURE_LOFT_DICE4_OLD	= 142,	// 星辰阶梯活动4排行榜老榜
	TPN_TREASURE_LOFT_DICE5		= 143,	// 星辰阶梯活动5排行榜
	TPN_TREASURE_LOFT_DICE5_OLD	= 144,	// 星辰阶梯活动5排行榜老榜
	TPN_TREASURE_LOFT_DICE6		= 145,	// 星辰阶梯活动6排行榜
	TPN_TREASURE_LOFT_DICE6_OLD	= 146,	// 星辰阶梯活动6排行榜老榜
	TPN_TREASURE_LOFT_DICE7		= 147,	// 星辰阶梯活动7排行榜
	TPN_TREASURE_LOFT_DICE7_OLD	= 148,	// 星辰阶梯活动7排行榜老榜
	TPN_TREASURE_LOFT_DICE8		= 149,	// 星辰阶梯活动8排行榜
	TPN_TREASURE_LOFT_DICE8_OLD	= 150,	// 星辰阶梯活动8排行榜老榜
	TPN_TREASURE_LOFT_DICE9		= 151,	// 星辰阶梯活动9排行榜
	TPN_TREASURE_LOFT_DICE9_OLD	= 152,	// 星辰阶梯活动9排行榜老榜
	TPN_TREASURE_LOFT_DICE10	= 153,	// 星辰阶梯活动10排行榜
	TPN_TREASURE_LOFT_DICE10_OLD = 154,	// 星辰阶梯活动10排行榜老榜

	//TPN_LOTTERY_SEA				= 175,	// 星海密藏排行榜(重用TPN_VIDEO_GAME1)
	//TPN_LOTTERY_SEA_OLD			= 176,	// 星海密藏排行榜老榜(重用TPN_VIDEO_GAME1_OLD)
	// 绘梨衣的游戏机活动排行榜 (175-214)！！！多预留10个
	//TPN_VIDEO_GAME1				= 175,	// 绘梨衣的游戏机活动1排行榜
	//TPN_VIDEO_GAME1_OLD			= 176,	// 绘梨衣的游戏机活动1排行榜老榜
	TPN_VIDEO_GAME2				= 177,	// 绘梨衣的游戏机活动2排行榜
	TPN_VIDEO_GAME2_OLD			= 178,	// 绘梨衣的游戏机活动2排行榜老榜
	TPN_VIDEO_GAME3				= 179,	// 绘梨衣的游戏机活动3排行榜
	TPN_VIDEO_GAME3_OLD			= 180,	// 绘梨衣的游戏机活动3排行榜老榜
	TPN_VIDEO_GAME4				= 181,	// 绘梨衣的游戏机活动4排行榜
	TPN_VIDEO_GAME4_OLD			= 182,	// 绘梨衣的游戏机活动4排行榜老榜
	TPN_VIDEO_GAME5				= 183,	// 绘梨衣的游戏机活动5排行榜
	TPN_VIDEO_GAME5_OLD			= 184,	// 绘梨衣的游戏机活动5排行榜老榜
	TPN_VIDEO_GAME6				= 185,	// 绘梨衣的游戏机活动6排行榜
	TPN_VIDEO_GAME6_OLD			= 186,	// 绘梨衣的游戏机活动6排行榜老榜
	TPN_VIDEO_GAME7				= 187,	// 绘梨衣的游戏机活动7排行榜
	TPN_VIDEO_GAME7_OLD			= 188,	// 绘梨衣的游戏机活动7排行榜老榜
	TPN_VIDEO_GAME8				= 189,	// 绘梨衣的游戏机活动8排行榜
	TPN_VIDEO_GAME8_OLD			= 190,	// 绘梨衣的游戏机活动8排行榜老榜
	TPN_VIDEO_GAME9				= 191,	// 绘梨衣的游戏机活动9排行榜
	TPN_VIDEO_GAME9_OLD			= 192,	// 绘梨衣的游戏机活动9排行榜老榜
	TPN_VIDEO_GAME10			= 193,	// 绘梨衣的游戏机活动10排行榜
	TPN_VIDEO_GAME10_OLD		= 194,	// 绘梨衣的游戏机活动10排行榜老榜

	TPN_DAILY_SALES1			= 215,	// 每日促销活动1排行榜
	TPN_DAILY_SALES1_OLD		= 216,	// 每日促销活动1排行榜老榜
	TPN_DAILY_SALES2			= 217,	// 每日促销活动2排行榜
	TPN_DAILY_SALES2_OLD		= 218,	// 每日促销活动2排行榜老榜
	TPN_DAILY_SALES3			= 219,	// 每日促销活动3排行榜
	TPN_DAILY_SALES3_OLD		= 220,	// 每日促销活动3排行榜老榜
	TPN_DAILY_SALES4			= 221,	// 每日促销活动4排行榜
	TPN_DAILY_SALES4_OLD		= 222,	// 每日促销活动4排行榜老榜
	TPN_DAILY_SALES5			= 223,	// 每日促销活动5排行榜
	TPN_DAILY_SALES5_OLD		= 224,	// 每日促销活动5排行榜老榜
	TPN_DAILY_SALES6			= 225,	// 每日促销活动6排行榜
	TPN_DAILY_SALES6_OLD		= 226,	// 每日促销活动6排行榜老榜
	TPN_DAILY_SALES7			= 227,	// 每日促销活动7排行榜
	TPN_DAILY_SALES7_OLD		= 228,	// 每日促销活动7排行榜老榜
	TPN_DAILY_SALES8			= 229,	// 每日促销活动8排行榜
	TPN_DAILY_SALES8_OLD		= 230,	// 每日促销活动8排行榜老榜
	TPN_DAILY_SALES9			= 231,	// 每日促销活动9排行榜
	TPN_DAILY_SALES9_OLD		= 232,	// 每日促销活动9排行榜老榜
	TPN_DAILY_SALES10			= 233,	// 每日促销活动10排行榜
	TPN_DAILY_SALES10_OLD		= 234,	// 每日促销活动10排行榜老榜
	TPN_DAILY_SALES11			= 235,	// 每日促销活动11排行榜
	TPN_DAILY_SALES11_OLD		= 236,	// 每日促销活动11排行榜老榜
	TPN_DAILY_SALES12			= 237,	// 每日促销活动12排行榜
	TPN_DAILY_SALES12_OLD		= 238,	// 每日促销活动12排行榜老榜
	TPN_DAILY_SALES13			= 239,	// 每日促销活动13排行榜
	TPN_DAILY_SALES13_OLD		= 240,	// 每日促销活动13排行榜老榜
	TPN_DAILY_SALES14			= 241,	// 每日促销活动14排行榜
	TPN_DAILY_SALES14_OLD		= 242,	// 每日促销活动14排行榜老榜
	TPN_DAILY_SALES15			= 243,	// 每日促销活动15排行榜
	TPN_DAILY_SALES15_OLD		= 244,	// 每日促销活动15排行榜老榜
	TPN_DAILY_SALES16			= 245,	// 每日促销活动16排行榜
	TPN_DAILY_SALES16_OLD		= 246,	// 每日促销活动16排行榜老榜
	TPN_DAILY_SALES17			= 247,	// 每日促销活动17排行榜
	TPN_DAILY_SALES17_OLD		= 248,	// 每日促销活动17排行榜老榜
	TPN_DAILY_SALES18			= 249,	// 每日促销活动18排行榜
	TPN_DAILY_SALES18_OLD		= 250,	// 每日促销活动18排行榜老榜
	TPN_DAILY_SALES19			= 251,	// 每日促销活动19排行榜
	TPN_DAILY_SALES19_OLD		= 252,	// 每日促销活动19排行榜老榜
	TPN_DAILY_SALES20			= 253,	// 每日促销活动20排行榜
	TPN_DAILY_SALES20_OLD		= 254,	// 每日促销活动20排行榜老榜
	TPN_DAILY_SALES21			= 255,	// 每日促销活动21排行榜
	TPN_DAILY_SALES21_OLD		= 256,	// 每日促销活动21排行榜老榜
	TPN_DAILY_SALES22			= 257,	// 每日促销活动22排行榜
	TPN_DAILY_SALES22_OLD		= 258,	// 每日促销活动22排行榜老榜

	TPN_HEAVEN1            = 260,  // 夏妮尔的游乐场活动1排行榜
	TPN_HEAVEN1_OLD        = 261,  // 夏妮尔的游乐场活动1排行榜老榜
	TPN_HEAVEN2            = 262,  // 夏妮尔的游乐场活动2排行榜
	TPN_HEAVEN2_OLD        = 263,  // 夏妮尔的游乐场活动2排行榜老榜
	TPN_HEAVEN3            = 264,  // 夏妮尔的游乐场活动3排行榜
	TPN_HEAVEN3_OLD        = 265,  // 夏妮尔的游乐场活动3排行榜老榜
	TPN_HEAVEN4            = 266,  // 夏妮尔的游乐场活动4排行榜
	TPN_HEAVEN4_OLD        = 267,  // 夏妮尔的游乐场活动4排行榜老榜
	TPN_HEAVEN5            = 268,  // 夏妮尔的游乐场活动5排行榜
	TPN_HEAVEN5_OLD        = 269,  // 夏妮尔的游乐场活动5排行榜老榜
	TPN_HEAVEN6            = 270,  // 夏妮尔的游乐场活动6排行榜
	TPN_HEAVEN6_OLD        = 271,  // 夏妮尔的游乐场活动6排行榜老榜
	TPN_HEAVEN7            = 272,  // 夏妮尔的游乐场活动7排行榜
	TPN_HEAVEN7_OLD        = 273,  // 夏妮尔的游乐场活动7排行榜老榜
	TPN_HEAVEN8            = 274,  // 夏妮尔的游乐场活动8排行榜
	TPN_HEAVEN8_OLD        = 275,  // 夏妮尔的游乐场活动8排行榜老榜
	TPN_HEAVEN9            = 276,  // 夏妮尔的游乐场活动9排行榜，已经转为巨龙宝库排行榜
	TPN_HEAVEN9_OLD        = 277,  // 夏妮尔的游乐场活动9排行榜老榜，已经转为巨龙宝库排行榜
	TPN_HEAVEN10           = 278,  // 夏妮尔的游乐场活动10排行榜，已经转为巨龙宝库排行榜
	TPN_HEAVEN10_OLD       = 279,  // 夏妮尔的游乐场活动10排行榜老榜，已经转为巨龙宝库排行榜

	//  海外 绘梨衣的游戏机活动排行榜 (321-340)
	TPN_OUT_VIDEO_GAME1					= 321,	// 海外 绘梨衣的游戏机活动1排行榜
	TPN_OUT_VIDEO_GAME1_OLD				= 322,	// 海外 绘梨衣的游戏机活动1排行榜老榜
	TPN_OUT_VIDEO_GAME2					= 323,	// 海外 绘梨衣的游戏机活动2排行榜
	TPN_OUT_VIDEO_GAME2_OLD				= 324,	// 海外 绘梨衣的游戏机活动2排行榜老榜
	TPN_OUT_VIDEO_GAME3					= 325,	// 海外 绘梨衣的游戏机活动3排行榜
	TPN_OUT_VIDEO_GAME3_OLD				= 326,	// 海外 绘梨衣的游戏机活动3排行榜老榜
	TPN_OUT_VIDEO_GAME4					= 327,	// 海外 绘梨衣的游戏机活动4排行榜
	TPN_OUT_VIDEO_GAME4_OLD				= 328,	// 海外 绘梨衣的游戏机活动4排行榜老榜
	TPN_OUT_VIDEO_GAME5					= 329,	// 海外 绘梨衣的游戏机活动5排行榜
	TPN_OUT_VIDEO_GAME5_OLD				= 330,	// 海外 绘梨衣的游戏机活动5排行榜老榜
	TPN_OUT_VIDEO_GAME6					= 331,	// 海外 绘梨衣的游戏机活动6排行榜
	TPN_OUT_VIDEO_GAME6_OLD				= 332,	// 海外 绘梨衣的游戏机活动6排行榜老榜
	TPN_OUT_VIDEO_GAME7					= 333,	// 海外 绘梨衣的游戏机活动7排行榜
	TPN_OUT_VIDEO_GAME7_OLD				= 334,	// 海外 绘梨衣的游戏机活动7排行榜老榜
	TPN_OUT_VIDEO_GAME8					= 335,	// 海外 绘梨衣的游戏机活动8排行榜
	TPN_OUT_VIDEO_GAME8_OLD				= 336,	// 海外 绘梨衣的游戏机活动8排行榜老榜
	TPN_OUT_VIDEO_GAME9					= 337,	// 海外 绘梨衣的游戏机活动9排行榜
	TPN_OUT_VIDEO_GAME9_OLD				= 338,	// 海外 绘梨衣的游戏机活动9排行榜老榜
	TPN_OUT_VIDEO_GAME10				= 339,	// 海外 绘梨衣的游戏机活动10排行榜
	TPN_OUT_VIDEO_GAME10_OLD			= 340,	// 海外 绘梨衣的游戏机活动10排行榜老榜

	TPN_DAILY_SALES23			= 341,	// 每日促销活动23排行榜
	TPN_DAILY_SALES23_OLD		= 342,	// 每日促销活动23排行榜老榜
	TPN_DAILY_SALES24			= 343,	// 每日促销活动24排行榜
	TPN_DAILY_SALES24_OLD		= 344,	// 每日促销活动24排行榜老榜

	TPN_PERSONAL_GOAL_TOTAL_1					= 357,	//第一期个人目标总积分排行榜
	TPN_PERSONAL_GOAL_TOTAL_1_OLD				= 358,	//第一期个人目标总积分排行榜老榜
	TPN_RECHARGE_CONSUME		= 369,	// 充值消费
	TPN_RECHARGE_CONSUME_OLD	= 370,

	//预留8个职业 384 - 399
	TPN_FIGHT_PROF_SKATE                = 384, //职业综合战力榜 滑板
	TPN_FIGHT_PROF_SKATE_OLD            = 385, //职业综合战力榜老榜 滑板

	TPN_FIGHT_PROF_MAGICIAN               = 386, //职业综合战力榜 魔术师
	TPN_FIGHT_PROF_MAGICIAN_OLD            = 387, //职业综合战力榜老榜 魔术师
	TPN_FIGHT_PROF_11                   = 388,
	TPN_FIGHT_PROF_11_OLD               = 389,
	TPN_FIGHT_PROF_12                   = 390,
	TPN_FIGHT_PROF_12_OLD               = 391,
	TPN_FIGHT_PROF_13                   = 392,
	TPN_FIGHT_PROF_13_OLD               = 393,
	TPN_FIGHT_PROF_14                   = 394,
	TPN_FIGHT_PROF_14_OLD               = 395,
	TPN_FIGHT_PROF_15                   = 396,
	TPN_FIGHT_PROF_15_OLD               = 397,
	TPN_FIGHT_PROF_16                   = 398,
	TPN_FIGHT_PROF_16_OLD               = 399,


	TPN_FIGHT_OPEN_SERVER_DAY		= 400,	//开服战斗力榜
	TPN_FIGHT_OPEN_SERVER_DAY_OLD	= 401,	//开服战斗力老榜

	TPN_DRAGON_HOUSE			= 402,	// 巨龙宝库活动排行榜
	TPN_DRAGON_HOUSE_OLD		= 403,	// 巨龙宝库活动排行榜老榜

	TPN_PLEASURE                = 406,  // 趣味夺宝排行榜
	TPN_PLEASURE_OLD            = 407,  // 趣味夺宝排行榜老榜

	TPN_SPRING                  = 408,  // 春节排行榜
	TPN_SPRING_OLD              = 409,  // 春节排行榜老榜

	TPN_FISH_MATCH 				= 410,	//钓鱼挑战赛
	TPN_FISH_MATCH_OLD 			= 411,
	// 社团boss积分
	TPN_CORPS_BOSS_SCORE				= 412,
	TPN_CORPS_BOSS_SCORE_OLD			= 413,
	TPN_GOD_EXPLORE				= 414,	// 神迹探索排行榜
	TPN_GOD_EXPLORE_OLD			= 415,	// 神迹探索排行榜老榜
	TPN_TEAM_RECHARGE_ROLE				= 416,	// 组队充值个人排行榜
	TPN_TEAM_RECHARGE_ROLE_OLD			= 417,	// 组队充值个人排行榜老榜
	TPN_TEAM_RECHARGE_TEAM				= 2048,	// 组队充值队伍排行榜
	TPN_TEAM_RECHARGE_TEAM_OLD			= 2049,	// 组队充值队伍排行榜老榜

	TPN_THUNDERSTRIKE			= 421,	// 天谴计划榜
	TPN_THUNDERSTRIKE_OLD		= 422,	// 天谴计划老榜

	TPN_GUARD_FIGHT				= 423,
	TPN_GUARD_FIGHT_OLDDAY 		= 424,	//守护灵战力一天前老榜

	TPN_FASHION					= 425,	//时装激活颜色排行榜
	TPN_FASHION_OLDDAY			= 426,	//时装激活颜色排行榜(老榜)
	TPN_PVE2_NIGHT              = 427,       //晚间PVE2积分排名榜
	TPN_PVE2_NIGHT_OLDDAY       = 428,       //晚间PVE2积分排名榜老榜
	TPN_CARRACE				= 429,	//赛车成绩排行榜
	TPN_CARRACE_OLDWEEK			= 430,  //赛车成绩周排行榜
	TPN_SOCIAL_SPACE_GIFT	     = 431,	//空间送礼物排行榜
	TPN_SOCIAL_SPACE_GIFT_OLD    = 432,	//空间送礼物排行榜老榜
	TPN_SOCIAL_SPACE_GIFT_HISTORY = 433,	//空间送礼物历史排行榜
	TPN_SOCIAL_SPACE_GIFT_HISTORY_OLD = 434, //空间送礼物历史排行榜老榜
	TPN_PVE1_JIDAO_ONE			= 435, //晚间PVE1极道巅峰第一场积分排名榜
	TPN_PVE1_JIDAO_ONE_OLD		= 436, //晚间PVE1极道巅峰第一场积分排名榜老榜
	TPN_STAR_POPULAR			= 437, //明星流量排行榜
	TPN_STAR_POPULAR_OLDDAY		= 438, //明星流量排行榜老榜
	TPN_PVE1_JIDAO_TWO			= 439, //晚间PVE1极道巅峰第二场积分排名榜
	TPN_PVE1_JIDAO_TWO_OLD		= 440, //晚间PVE1极道巅峰第二场积分排名榜老榜
	TPN_CAR_RACE_PVP			= 441, //赛车PVP榜
	TPN_CAR_RACE_PVP_OLD		= 442, //赛车PVP旧榜
	TPN_CAREER_SHOP				= 443, //小店排行榜
	TPN_CAREER_SHOP_OLD			= 444, //小店排行榜老榜
	TPN_CAREER_SHOP_FOUND		= 445, //小店资金排行榜
	TPN_CAREER_SHOP_FOUND_OLD	= 446, //小店资金排行榜老榜
	TPN_HOMETOWN_PROSPERITY     = 447, //家园温馨值排行榜
	TPN_HOMETOWN_PROSPERITY_OLD = 448, //家园温馨值排行老榜
	TPN_HOMETOWN_GIFT			= 449,		//家园倾慕周排行榜
	TPN_HOMETOWN_GIFT_OLD		= 450,		//家园倾慕周排行榜老榜
	TPN_HOMETOWN_GIFT_HISTORY	= 451,		//家园倾慕历史排行榜
	TPN_HOMETOWN_GIFT_HISTORY_OLD	= 452,		//家园倾慕历史排行榜老榜
	TPN_CAR_RACE_PVE			= 453, //赛车pve
	TPN_CAR_RACE_PVE_OLD		= 454, //赛车pve旧榜
	TPN_CAR_RACE_PVP_2			= 455, // 赛车pvp2
	TPN_CAR_RACE_PVP_2_OLD		= 456, // 赛车pvp2旧榜

	TPN_MINIGAME_ARENA_HIS_WIN			= 457, // 小游戏对战历史最高连胜
	TPN_MINIGAME_ARENA_HIS_WIN_OLD		= 458, // 小游戏对战历史最高连胜旧榜

	TPN_PUBG_SINGLE_HISTORY		= 459, //单人吃鸡历史榜
	TPN_PUBG_SINGLE_WEEK		= 460, //单人吃鸡周榜
	TPN_PUBG_SINGLE_WEEK_OLD	= 461, //单人吃鸡周榜老榜

	TPN_PUBG_TEAM_HISTORY		= 462, //组队吃鸡历史榜
	TPN_PUBG_TEAM_WEEK			= 463, //组队吃鸡周榜
	TPN_PUBG_TEAM_WEEK_OLD		= 464, //组队吃鸡周榜老榜

	TPN_TASKADVANTURE_POINT		= 465, //异闻排行榜
	TPN_TASKADVANTURE_POINT_OLD	= 466, //异闻排行榜老榜
	TPN_PUBG_SEASON_SCORE		= 467, //吃鸡赛季积分榜
	TPN_PUBG_SEASON_SCORE_OLD	= 468, //吃鸡赛季积分老榜

	TPN_EQUITY_PVP_OCC1 = 469, //公平竞技场职业1
	TPN_EQUITY_PVP_OCC1_OLD = 470, //公平竞技场职业1老榜
	TPN_EQUITY_PVP_OCC2 = 471, //公平竞技场职业2
	TPN_EQUITY_PVP_OCC2_OLD = 472, //公平竞技场职业2老榜
	TPN_EQUITY_PVP_OCC3 = 473, //公平竞技场职业3
	TPN_EQUITY_PVP_OCC3_OLD = 474, //公平竞技场职业3老榜
	TPN_EQUITY_PVP_OCC4 = 475, //公平竞技场职业4
	TPN_EQUITY_PVP_OCC4_OLD = 476, //公平竞技场职业4老榜
	TPN_EQUITY_PVP_OCC5 = 477, //公平竞技场职业5
	TPN_EQUITY_PVP_OCC5_OLD = 478, //公平竞技场职业5老榜
	TPN_EQUITY_PVP_OCC6 = 479, //公平竞技场职业6
	TPN_EQUITY_PVP_OCC6_OLD = 480, //公平竞技场职业6老榜
	TPN_EQUITY_PVP_OCC7 = 481, //公平竞技场职业7
	TPN_EQUITY_PVP_OCC7_OLD = 482, //公平竞技场职业7老榜
	TPN_EQUITY_PVP_OCC8 = 483, //公平竞技场职业8
	TPN_EQUITY_PVP_OCC8_OLD = 484, //公平竞技场职业8老榜
	TPN_EQUITY_PVP_OCC9 = 485, //公平竞技场职业9
	TPN_EQUITY_PVP_OCC9_OLD = 486, //公平竞技场职业9老榜
	TPN_EQUITY_PVP_OCC10 = 487, //公平竞技场职业10
	TPN_EQUITY_PVP_OCC10_OLD = 488, //公平竞技场职业10老榜
	TPN_EQUITY_PVP_OCC11 = 489, //公平竞技场职业11
	TPN_EQUITY_PVP_OCC11_OLD = 490, //公平竞技场职业11老榜
	TPN_EQUITY_PVP_OCC12 = 491, //公平竞技场职业12
	TPN_EQUITY_PVP_OCC12_OLD = 492, //公平竞技场职业12老榜
	TPN_LADDER2_RANK = 493, // 天梯2排行榜
	TPN_LADDER2_RANK_OLD = 494, // 天梯2排行榜老榜
	TPN_OVERCOOK_SCORE = 495,
	TPN_OVERCOOK_SCORE_OLD = 496,
	TPN_COOL_RUNNING        = 497,  // 酷跑排行榜
	TPN_COOL_RUNNING_OLD    = 498,  // 酷跑排行榜老榜

	TPN_MAX,					//排行榜编号最大值+1(以上排行榜都为在gamedbd中的排行榜)

	// 下面是DS维护的排行榜
	/*
	TPN_FACTION_INDUSTRY = 300,	//帮派总体实力排行榜
	TPN_FACTION_ESCORT = 301,	//帮派镖局排行榜
	TPN_FACTION_CARAVAN = 302,	//帮派马帮排行榜
	TPN_FACTION_COTTAGE = 303,	//帮派山寨排行榜
	TPN_FACTION_FACTORY = 304,	//帮派工坊排行榜
	TPN_FACTION_LEVEL = 305,	//帮派等级排行榜
	TPN_FACTION_CONSINC = 306,	//帮派建设度增量排行榜
	TPN_FACTION_INDUSTRY_OLD1 = 307,	//帮派总体实力老榜1(每天存一次)
	TPN_FACTION_INDUSTRY_OLD2 = 308,	//帮派总体实力老榜2(每周存一次)
	TPN_FACTION_ACTIVITY_FEATURE = 309,	//帮派特色活动进度排行榜
	TPN_FACTION_ACTIVITY_TREASURE = 310,	//帮派寻宝活动进度排行榜
	TPN_FACTION_ACTIVITY_EXTINCTION = 311,	//帮派灭门活动进度排行榜
	*/
	TPN_FACTION_INDUSTRY_N1 = 500,		//帮派建设度排行榜N1
	TPN_FACTION_INDUSTRY_N2 = 501,		//帮派建设度排行榜N2
	TPN_FACTION_INDUSTRY_N3 = 502,		//帮派建设度排行榜N3
	TPN_FACTION_INDUSTRY_N4 = 503,		//帮派建设度排行榜N4
	TPN_FACTION_INDUSTRY_N5 = 504,		//帮派建设度排行榜N5
	TPN_FACTION_INDUSTRY_N6 = 505,		//帮派建设度排行榜N6
	TPN_FACTION_INDUSTRY_N1_OLD = 506,	//帮派建设度排行榜老榜N1
	TPN_FACTION_INDUSTRY_N2_OLD = 507,	//帮派建设度排行榜老榜N2
	TPN_FACTION_INDUSTRY_N3_OLD = 508,	//帮派建设度排行榜老榜N3
	TPN_FACTION_INDUSTRY_N4_OLD = 509,	//帮派建设度排行榜老榜N4
	TPN_FACTION_INDUSTRY_N5_OLD = 510,	//帮派建设度排行榜老榜N5
	TPN_FACTION_INDUSTRY_N6_OLD = 511,	//帮派建设度排行榜老榜N6
	TPN_FACTION_INDUSTRY = 512,		//帮派建设度排行榜,世界
	TPN_FACTION_INDUSTRY_OLD = 513,		//帮派建设度排行榜

	TPN_ARENA_GROUP = 514,			//跨服战队排行榜
	TPN_ARENA_GROUP_OLD = 515,		//跨服战队排行榜老榜
	TPN_SOCIAL_SPACE_POP				= 516,		//个人空间人气值排行榜
	TPN_SOCIAL_SPACE_POP_OLD			= 517,		//个人空间人气值排行老榜
	TPN_SOCIAL_SPACE_POP_HISTORY		= 518,		//个人空间人气值历史排行榜
	TPN_SOCIAL_SPACE_POP_HISTORY_OLD	= 519,		//个人空间人气值历史排行榜老榜

	TPN_HOMETOWN_POP = 520,  //家园人气值排行榜
	TPN_HOMETOWN_POP_OLD = 521, // 家园人气值排行老榜
	TPN_HOMETOWN_POP_HISTORY  = 522, //家园人气值历史排行榜
	TPN_HOMETOWN_POP_HISTORY_OLD = 523, // 家园人气值历史排行老榜
	TPN_HOMETOWN_DESIGN_RECOMMEND_VAL_HISTORY = 524, // 家园设计推荐度历史榜
	TPN_HOMETOWN_DESIGN_RECOMMEND_VAL_HISTORY_OLD = 525, // 家园设计推荐度历史旧榜
	TPN_HOMETOWN_DESIGN_RECOMMEND_VAL_WEEKLY = 526, // 家园设计推荐度周榜
	TPN_HOMETOWN_DESIGN_RECOMMEND_VAL_WEEKLY_OLD = 527, // 家园设计推荐度周榜
	TPN_HOMETOWN_DESIGN_RECOMMEND_VAL_ALL_SRV = 528, // 家园设计推荐度跨服榜
	TPN_HOMETOWN_DESIGN_RECOMMEND_VAL_ALL_SRV_OLD = 529, // 家园设计推荐度跨服旧榜

	TPN_CORPS_ACTIVITY			= 530,		//社团活跃度排行榜
	TPN_CORPS_ACTIVITY_OLD			= 531,		//社团活跃度排行榜老榜
	TPN_CORPS_ACTIVITY_WEEK			= 532,		//社团周活跃度排行榜
	TPN_CORPS_ACTIVITY_WEEK_OLD		= 533,		//社团周活跃度排行榜老榜
	TPN_CORPS_BATTLE_SCORE                  = 534,          //社团竞赛天梯积分排行榜
	TPN_CORPS_BATTLE_SCORE_OLD              = 535,          //社团竞赛天梯积分老榜
	TPN_CORPS_BATTLE_TOWER                  = 536,
	TPN_CORPS_BATTLE_TOWER_OLD              = 537,

	TPN_INTIMATE_VALUE			= 540,		//羁绊排行榜
	TPN_INTIMATE_VALUE_OLD			= 541,
	TPN_INTIMATE_VALUE_WEEK			= 542,
	TPN_INTIMATE_VALUE_WEEK_OLD		= 543,

	TPN_ARENA_PUSHCAR_LEVEL_ONE               = 550, //竞技场爆破行动50~90级排行榜
	TPN_ARENA_PUSHCAR_LEVEL_ONE_OLD           = 551, //竞技场爆破行动50~90级排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_TWO               = 552, //竞技场爆破行动90~109级排行榜
	TPN_ARENA_PUSHCAR_LEVEL_TWO_OLD           = 553, //竞技场爆破行动90~109级排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_THREE             = 554, //竞技场爆破行动110~200级排行榜
	TPN_ARENA_PUSHCAR_LEVEL_THREE_OLD         = 555, //竞技场爆破行动110~200级排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_FOUR			= 556, //竞技场爆破行动4排行榜
	TPN_ARENA_PUSHCAR_LEVEL_FOUR_OLD		= 557, //竞技场爆破行动4排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_FIVE			= 558, //竞技场爆破行动5排行榜
	TPN_ARENA_PUSHCAR_LEVEL_FIVE_OLD		= 559, //竞技场爆破行动5排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_SIX				= 1893, //竞技场爆破行动6排行榜
	TPN_ARENA_PUSHCAR_LEVEL_SIX_OLD			= 1894, //竞技场爆破行动6排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_SEVEN			= 2040, //竞技场爆破行动7排行榜
	TPN_ARENA_PUSHCAR_LEVEL_SEVEN_OLD		= 2041, //竞技场爆破行动7排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_EIGHT			= 2042, //竞技场爆破行动8排行榜
	TPN_ARENA_PUSHCAR_LEVEL_EIGHT_OLD		= 2043, //竞技场爆破行动8排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_NINE			= 2044, //竞技场爆破行动9排行榜
	TPN_ARENA_PUSHCAR_LEVEL_NINE_OLD		= 2045, //竞技场爆破行动9排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_TEN				= 2046, //竞技场爆破行动10排行榜
	TPN_ARENA_PUSHCAR_LEVEL_TEN_OLD			= 2047, //竞技场爆破行动10排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_ELEVEN			= 2086, //竞技场爆破行动11排行榜
	TPN_ARENA_PUSHCAR_LEVEL_ELEVEN_OLD		= 2087, //竞技场爆破行动11排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_TWELVE			= 2128, //竞技场爆破行动12排行榜
	TPN_ARENA_PUSHCAR_LEVEL_TWELVE_OLD		= 2129, //竞技场爆破行动12排行榜老榜
	TPN_ARENA_PUSHCAR_LEVEL_THIRTEEN		= 2550, //竞技场爆破行动13排行榜
	TPN_ARENA_PUSHCAR_LEVEL_THIRTEEN_OLD	= 2551, //竞技场爆破行动13排行榜老榜

	// 单人竞技场职业x等级段1 排行榜/排行榜老榜 (预留16个职业 560-591) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL1			= 560,	// 单人竞技场职业1等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL1_OLD		= 561,	// 单人竞技场职业1等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL1			= 562,	// 单人竞技场职业2等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL1_OLD		= 563,	// 单人竞技场职业2等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL1			= 564,	// 单人竞技场职业3等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL1_OLD		= 565,	// 单人竞技场职业3等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL1			= 566,	// 单人竞技场职业4等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL1_OLD		= 567,	// 单人竞技场职业4等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL1			= 568,	// 单人竞技场职业5等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL1_OLD		= 569,	// 单人竞技场职业5等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL1			= 570,	// 单人竞技场职业6等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL1_OLD		= 571,	// 单人竞技场职业6等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL1			= 572,	// 单人竞技场职业7等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL1_OLD		= 573,	// 单人竞技场职业7等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL1			= 574,	// 单人竞技场职业8等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL1_OLD		= 575,	// 单人竞技场职业8等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL1			= 576,	// 单人竞技场职业9等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL1_OLD		= 577,	// 单人竞技场职业9等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL1			= 578,	// 单人竞技场职业10等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL1_OLD		= 579,	// 单人竞技场职业10等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL1			= 580,	// 单人竞技场职业11等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL1_OLD		= 581,	// 单人竞技场职业11等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL1			= 582,	// 单人竞技场职业12等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL1_OLD		= 583,	// 单人竞技场职业12等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL1			= 584,	// 单人竞技场职业13等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL1_OLD		= 585,	// 单人竞技场职业13等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL1			= 586,	// 单人竞技场职业14等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL1_OLD		= 587,	// 单人竞技场职业14等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL1			= 588,	// 单人竞技场职业15等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL1_OLD		= 589,	// 单人竞技场职业15等级段1排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL1			= 590,	// 单人竞技场职业16等级段1排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL1_OLD		= 591,	// 单人竞技场职业16等级段1排行榜老榜
	// 单人竞技场职业x等级段2 排行榜/排行榜老榜 (预留16个职业 592-623) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL2			= 592,	// 单人竞技场职业1等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL2_OLD		= 593,	// 单人竞技场职业1等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL2			= 594,	// 单人竞技场职业2等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL2_OLD		= 595,	// 单人竞技场职业2等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL2			= 596,	// 单人竞技场职业3等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL2_OLD		= 597,	// 单人竞技场职业3等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL2			= 598,	// 单人竞技场职业4等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL2_OLD		= 599,	// 单人竞技场职业4等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL2			= 600,	// 单人竞技场职业5等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL2_OLD		= 601,	// 单人竞技场职业5等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL2			= 602,	// 单人竞技场职业6等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL2_OLD		= 603,	// 单人竞技场职业6等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL2			= 604,	// 单人竞技场职业7等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL2_OLD		= 605,	// 单人竞技场职业7等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL2			= 606,	// 单人竞技场职业8等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL2_OLD		= 607,	// 单人竞技场职业8等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL2			= 608,	// 单人竞技场职业9等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL2_OLD		= 609,	// 单人竞技场职业9等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL2			= 610,	// 单人竞技场职业10等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL2_OLD		= 611,	// 单人竞技场职业10等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL2			= 612,	// 单人竞技场职业11等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL2_OLD		= 613,	// 单人竞技场职业11等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL2			= 614,	// 单人竞技场职业12等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL2_OLD		= 615,	// 单人竞技场职业12等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL2			= 616,	// 单人竞技场职业13等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL2_OLD		= 617,	// 单人竞技场职业13等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL2			= 618,	// 单人竞技场职业14等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL2_OLD		= 619,	// 单人竞技场职业14等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL2			= 620,	// 单人竞技场职业15等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL2_OLD		= 621,	// 单人竞技场职业15等级段2排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL2			= 622,	// 单人竞技场职业16等级段2排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL2_OLD		= 623,	// 单人竞技场职业16等级段2排行榜老榜
	// 单人竞技场职业x等级段3 排行榜/排行榜老榜 (预留16个职业 660-691) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL3			= 660,	// 单人竞技场职业1等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL3_OLD		= 661,	// 单人竞技场职业1等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL3			= 662,	// 单人竞技场职业2等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL3_OLD		= 663,	// 单人竞技场职业2等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL3			= 664,	// 单人竞技场职业3等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL3_OLD		= 665,	// 单人竞技场职业3等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL3			= 666,	// 单人竞技场职业4等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL3_OLD		= 667,	// 单人竞技场职业4等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL3			= 668,	// 单人竞技场职业5等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL3_OLD		= 669,	// 单人竞技场职业5等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL3			= 670,	// 单人竞技场职业6等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL3_OLD		= 671,	// 单人竞技场职业6等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL3			= 672,	// 单人竞技场职业7等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL3_OLD		= 673,	// 单人竞技场职业7等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL3			= 674,	// 单人竞技场职业8等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL3_OLD		= 675,	// 单人竞技场职业8等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL3			= 676,	// 单人竞技场职业9等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL3_OLD		= 677,	// 单人竞技场职业9等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL3			= 678,	// 单人竞技场职业10等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL3_OLD		= 679,	// 单人竞技场职业10等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL3			= 680,	// 单人竞技场职业11等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL3_OLD		= 681,	// 单人竞技场职业11等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL3			= 682,	// 单人竞技场职业12等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL3_OLD		= 683,	// 单人竞技场职业12等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL3			= 684,	// 单人竞技场职业13等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL3_OLD		= 685,	// 单人竞技场职业13等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL3			= 686,	// 单人竞技场职业14等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL3_OLD		= 687,	// 单人竞技场职业14等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL3			= 688,	// 单人竞技场职业15等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL3_OLD		= 689,	// 单人竞技场职业15等级段3排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL3			= 690,	// 单人竞技场职业16等级段3排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL3_OLD		= 691,	// 单人竞技场职业16等级段3排行榜老榜
	// 单人竞技场职业x等级段4 排行榜/排行榜老榜 (预留16个职业 750-781) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL4			= 750,	// 单人竞技场职业1等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL4_OLD		= 751,	// 单人竞技场职业1等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL4			= 752,	// 单人竞技场职业2等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL4_OLD		= 753,	// 单人竞技场职业2等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL4			= 754,	// 单人竞技场职业3等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL4_OLD		= 755,	// 单人竞技场职业3等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL4			= 756,	// 单人竞技场职业4等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL4_OLD		= 757,	// 单人竞技场职业4等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL4			= 758,	// 单人竞技场职业5等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL4_OLD		= 759,	// 单人竞技场职业5等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL4			= 760,	// 单人竞技场职业6等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL4_OLD		= 761,	// 单人竞技场职业6等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL4			= 762,	// 单人竞技场职业7等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL4_OLD		= 763,	// 单人竞技场职业7等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL4			= 764,	// 单人竞技场职业8等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL4_OLD		= 765,	// 单人竞技场职业8等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL4			= 766,	// 单人竞技场职业9等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL4_OLD		= 767,	// 单人竞技场职业9等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL4			= 768,	// 单人竞技场职业10等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL4_OLD		= 769,	// 单人竞技场职业10等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL4			= 770,	// 单人竞技场职业11等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL4_OLD		= 771,	// 单人竞技场职业11等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL4			= 772,	// 单人竞技场职业12等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL4_OLD		= 773,	// 单人竞技场职业12等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL4			= 774,	// 单人竞技场职业13等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL4_OLD		= 775,	// 单人竞技场职业13等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL4			= 776,	// 单人竞技场职业14等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL4_OLD		= 777,	// 单人竞技场职业14等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL4			= 778,	// 单人竞技场职业15等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL4_OLD		= 779,	// 单人竞技场职业15等级段4排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL4			= 780,	// 单人竞技场职业16等级段4排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL4_OLD		= 781,	// 单人竞技场职业16等级段4排行榜老榜
	// 单人竞技场职业x等级段5 排行榜/排行榜老榜 (预留16个职业 1811-1842) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL5			= 1811,	// 单人竞技场职业1等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL5_OLD		= 1812,	// 单人竞技场职业1等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL5			= 1813,	// 单人竞技场职业2等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL5_OLD		= 1814,	// 单人竞技场职业2等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL5			= 1815,	// 单人竞技场职业3等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL5_OLD		= 1816,	// 单人竞技场职业3等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL5			= 1817,	// 单人竞技场职业4等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL5_OLD		= 1818,	// 单人竞技场职业4等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL5			= 1819,	// 单人竞技场职业5等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL5_OLD		= 1820,	// 单人竞技场职业5等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL5			= 1821,	// 单人竞技场职业6等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL5_OLD		= 1822,	// 单人竞技场职业6等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL5			= 1823,	// 单人竞技场职业7等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL5_OLD		= 1824,	// 单人竞技场职业7等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL5			= 1825,	// 单人竞技场职业8等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL5_OLD		= 1826,	// 单人竞技场职业8等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL5			= 1827,	// 单人竞技场职业9等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL5_OLD		= 1828,	// 单人竞技场职业9等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL5			= 1829,	// 单人竞技场职业10等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL5_OLD		= 1830,	// 单人竞技场职业10等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL5			= 1831,	// 单人竞技场职业11等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL5_OLD		= 1832,	// 单人竞技场职业11等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL5			= 1833,	// 单人竞技场职业12等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL5_OLD		= 1834,	// 单人竞技场职业12等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL5			= 1835,	// 单人竞技场职业13等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL5_OLD		= 1836,	// 单人竞技场职业13等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL5			= 1837,	// 单人竞技场职业14等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL5_OLD		= 1838,	// 单人竞技场职业14等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL5			= 1839,	// 单人竞技场职业15等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL5_OLD		= 1840,	// 单人竞技场职业15等级段5排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL5			= 1841,	// 单人竞技场职业16等级段5排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL5_OLD		= 1842,	// 单人竞技场职业16等级段5排行榜老榜
	// 单人竞技场职业x等级段6 排行榜/排行榜老榜 (预留16个职业 1861-1892) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL6			= 1861,	// 单人竞技场职业1等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL6_OLD		= 1862,	// 单人竞技场职业1等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL6			= 1863,	// 单人竞技场职业2等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL6_OLD		= 1864,	// 单人竞技场职业2等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL6			= 1865,	// 单人竞技场职业3等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL6_OLD		= 1866,	// 单人竞技场职业3等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL6			= 1867,	// 单人竞技场职业4等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL6_OLD		= 1868,	// 单人竞技场职业4等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL6			= 1869,	// 单人竞技场职业5等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL6_OLD		= 1870,	// 单人竞技场职业5等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL6			= 1871,	// 单人竞技场职业6等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL6_OLD		= 1872,	// 单人竞技场职业6等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL6			= 1873,	// 单人竞技场职业7等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL6_OLD		= 1874,	// 单人竞技场职业7等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL6			= 1875,	// 单人竞技场职业8等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL6_OLD		= 1876,	// 单人竞技场职业8等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL6			= 1877,	// 单人竞技场职业9等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL6_OLD		= 1878,	// 单人竞技场职业9等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL6			= 1879,	// 单人竞技场职业10等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL6_OLD		= 1880,	// 单人竞技场职业10等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL6			= 1881,	// 单人竞技场职业11等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL6_OLD		= 1882,	// 单人竞技场职业11等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL6			= 1883,	// 单人竞技场职业12等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL6_OLD		= 1884,	// 单人竞技场职业12等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL6			= 1885,	// 单人竞技场职业13等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL6_OLD		= 1886,	// 单人竞技场职业13等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL6			= 1887,	// 单人竞技场职业14等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL6_OLD		= 1888,	// 单人竞技场职业14等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL6			= 1889,	// 单人竞技场职业15等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL6_OLD		= 1890,	// 单人竞技场职业15等级段6排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL6			= 1891,	// 单人竞技场职业16等级段6排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL6_OLD		= 1892,	// 单人竞技场职业16等级段6排行榜老榜
	// 单人竞技场职业x等级段7 排行榜/排行榜老榜 (预留16个职业 1910-1941) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL7			= 1910,	// 单人竞技场职业1等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL7_OLD		= 1911,	// 单人竞技场职业1等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL7			= 1912,	// 单人竞技场职业2等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL7_OLD		= 1913,	// 单人竞技场职业2等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL7			= 1914,	// 单人竞技场职业3等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL7_OLD		= 1915,	// 单人竞技场职业3等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL7			= 1916,	// 单人竞技场职业4等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL7_OLD		= 1917,	// 单人竞技场职业4等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL7			= 1918,	// 单人竞技场职业5等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL7_OLD		= 1919,	// 单人竞技场职业5等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL7			= 1920,	// 单人竞技场职业6等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL7_OLD		= 1921,	// 单人竞技场职业6等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL7			= 1922,	// 单人竞技场职业7等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL7_OLD		= 1923,	// 单人竞技场职业7等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL7			= 1924,	// 单人竞技场职业8等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL7_OLD		= 1925,	// 单人竞技场职业8等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL7			= 1926,	// 单人竞技场职业9等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL7_OLD		= 1927,	// 单人竞技场职业9等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL7			= 1928,	// 单人竞技场职业10等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL7_OLD		= 1929,	// 单人竞技场职业10等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL7			= 1930,	// 单人竞技场职业11等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL7_OLD		= 1931,	// 单人竞技场职业11等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL7			= 1932,	// 单人竞技场职业12等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL7_OLD		= 1933,	// 单人竞技场职业12等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL7			= 1934,	// 单人竞技场职业13等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL7_OLD		= 1935,	// 单人竞技场职业13等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL7			= 1936,	// 单人竞技场职业14等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL7_OLD		= 1937,	// 单人竞技场职业14等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL7			= 1938,	// 单人竞技场职业15等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL7_OLD		= 1939,	// 单人竞技场职业15等级段7排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL7			= 1940,	// 单人竞技场职业16等级段7排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL7_OLD		= 1941,	// 单人竞技场职业16等级段7排行榜老榜
	// 单人竞技场职业x等级段8 排行榜/排行榜老榜 (预留16个职业 1942-1973) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL8			= 1942,	// 单人竞技场职业1等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL8_OLD		= 1943,	// 单人竞技场职业1等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL8			= 1944,	// 单人竞技场职业2等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL8_OLD		= 1945,	// 单人竞技场职业2等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL8			= 1946,	// 单人竞技场职业3等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL8_OLD		= 1947,	// 单人竞技场职业3等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL8			= 1948,	// 单人竞技场职业4等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL8_OLD		= 1949,	// 单人竞技场职业4等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL8			= 1950,	// 单人竞技场职业5等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL8_OLD		= 1951,	// 单人竞技场职业5等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL8			= 1952,	// 单人竞技场职业6等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL8_OLD		= 1953,	// 单人竞技场职业6等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL8			= 1954,	// 单人竞技场职业7等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL8_OLD		= 1955,	// 单人竞技场职业7等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL8			= 1956,	// 单人竞技场职业8等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL8_OLD		= 1957,	// 单人竞技场职业8等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL8			= 1958,	// 单人竞技场职业9等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL8_OLD		= 1959,	// 单人竞技场职业9等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL8			= 1960,	// 单人竞技场职业10等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL8_OLD		= 1961,	// 单人竞技场职业10等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL8			= 1962,	// 单人竞技场职业11等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL8_OLD		= 1963,	// 单人竞技场职业11等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL8			= 1964,	// 单人竞技场职业12等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL8_OLD		= 1965,	// 单人竞技场职业12等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL8			= 1966,	// 单人竞技场职业13等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL8_OLD		= 1967,	// 单人竞技场职业13等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL8			= 1968,	// 单人竞技场职业14等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL8_OLD		= 1969,	// 单人竞技场职业14等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL8			= 1970,	// 单人竞技场职业15等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL8_OLD		= 1971,	// 单人竞技场职业15等级段8排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL8			= 1972,	// 单人竞技场职业16等级段8排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL8_OLD		= 1973,	// 单人竞技场职业16等级段8排行榜老榜
	// 单人竞技场职业x等级段9 排行榜/排行榜老榜 (预留16个职业 1974-2005) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL9			= 1974,	// 单人竞技场职业1等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL9_OLD		= 1975,	// 单人竞技场职业1等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL9			= 1976,	// 单人竞技场职业2等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL9_OLD		= 1977,	// 单人竞技场职业2等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL9			= 1978,	// 单人竞技场职业3等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL9_OLD		= 1979,	// 单人竞技场职业3等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL9			= 1980,	// 单人竞技场职业4等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL9_OLD		= 1981,	// 单人竞技场职业4等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL9			= 1982,	// 单人竞技场职业5等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL9_OLD		= 1983,	// 单人竞技场职业5等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL9			= 1984,	// 单人竞技场职业6等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL9_OLD		= 1985,	// 单人竞技场职业6等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL9			= 1986,	// 单人竞技场职业7等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL9_OLD		= 1987,	// 单人竞技场职业7等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL9			= 1988,	// 单人竞技场职业8等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL9_OLD		= 1989,	// 单人竞技场职业8等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL9			= 1990,	// 单人竞技场职业9等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL9_OLD		= 1991,	// 单人竞技场职业9等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL9			= 1992,	// 单人竞技场职业10等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL9_OLD		= 1993,	// 单人竞技场职业10等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL9			= 1994,	// 单人竞技场职业11等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL9_OLD		= 1995,	// 单人竞技场职业11等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL9			= 1996,	// 单人竞技场职业12等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL9_OLD		= 1997,	// 单人竞技场职业12等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL9			= 1998,	// 单人竞技场职业13等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL9_OLD		= 1999,	// 单人竞技场职业13等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL9			= 2000,	// 单人竞技场职业14等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL9_OLD		= 2001,	// 单人竞技场职业14等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL9			= 2002,	// 单人竞技场职业15等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL9_OLD		= 2003,	// 单人竞技场职业15等级段9排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL9			= 2004,	// 单人竞技场职业16等级段9排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL9_OLD		= 2005,	// 单人竞技场职业16等级段9排行榜老榜
	// 单人竞技场职业x等级段10 排行榜/排行榜老榜 (预留16个职业 2006-2037) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL10			= 2006,	// 单人竞技场职业1等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL10_OLD		= 2007,	// 单人竞技场职业1等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL10			= 2008,	// 单人竞技场职业2等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL10_OLD		= 2009,	// 单人竞技场职业2等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL10			= 2010,	// 单人竞技场职业3等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL10_OLD		= 2011,	// 单人竞技场职业3等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL10			= 2012,	// 单人竞技场职业4等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL10_OLD		= 2013,	// 单人竞技场职业4等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL10			= 2014,	// 单人竞技场职业5等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL10_OLD		= 2015,	// 单人竞技场职业5等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL10			= 2016,	// 单人竞技场职业6等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL10_OLD		= 2017,	// 单人竞技场职业6等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL10			= 2018,	// 单人竞技场职业7等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL10_OLD		= 2019,	// 单人竞技场职业7等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL10			= 2020,	// 单人竞技场职业8等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL10_OLD		= 2021,	// 单人竞技场职业8等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL10			= 2022,	// 单人竞技场职业9等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL10_OLD		= 2023,	// 单人竞技场职业9等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL10			= 2024,	// 单人竞技场职业10等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL10_OLD		= 2025,	// 单人竞技场职业10等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL10			= 2026,	// 单人竞技场职业11等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL10_OLD		= 2027,	// 单人竞技场职业11等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL10			= 2028,	// 单人竞技场职业12等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL10_OLD		= 2029,	// 单人竞技场职业12等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL10			= 2030,	// 单人竞技场职业13等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL10_OLD		= 2031,	// 单人竞技场职业13等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL10			= 2032,	// 单人竞技场职业14等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL10_OLD		= 2033,	// 单人竞技场职业14等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL10			= 2034,	// 单人竞技场职业15等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL10_OLD		= 2035,	// 单人竞技场职业15等级段10排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL10			= 2036,	// 单人竞技场职业16等级段10排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL10_OLD		= 2037,	// 单人竞技场职业16等级段10排行榜老榜
	// 单人竞技场职业x等级段11 排行榜/排行榜老榜 (预留16个职业 2054-2085) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL11			= 2054,	// 单人竞技场职业1等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL11_OLD		= 2055,	// 单人竞技场职业1等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL11			= 2056,	// 单人竞技场职业2等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL11_OLD		= 2057,	// 单人竞技场职业2等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL11			= 2058,	// 单人竞技场职业3等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL11_OLD		= 2059,	// 单人竞技场职业3等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL11			= 2060,	// 单人竞技场职业4等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL11_OLD		= 2061,	// 单人竞技场职业4等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL11			= 2062,	// 单人竞技场职业5等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL11_OLD		= 2063,	// 单人竞技场职业5等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL11			= 2064,	// 单人竞技场职业6等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL11_OLD		= 2065,	// 单人竞技场职业6等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL11			= 2066,	// 单人竞技场职业7等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL11_OLD		= 2067,	// 单人竞技场职业7等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL11			= 2068,	// 单人竞技场职业8等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL11_OLD		= 2069,	// 单人竞技场职业8等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL11			= 2070,	// 单人竞技场职业9等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL11_OLD		= 2071,	// 单人竞技场职业9等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL11			= 2072,	// 单人竞技场职业10等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL11_OLD		= 2073,	// 单人竞技场职业10等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL11			= 2074,	// 单人竞技场职业11等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL11_OLD		= 2075,	// 单人竞技场职业11等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL11			= 2076,	// 单人竞技场职业12等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL11_OLD		= 2077,	// 单人竞技场职业12等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL11			= 2078,	// 单人竞技场职业13等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL11_OLD		= 2079,	// 单人竞技场职业13等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL11			= 2080,	// 单人竞技场职业14等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL11_OLD		= 2081,	// 单人竞技场职业14等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL11			= 2082,	// 单人竞技场职业15等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL11_OLD		= 2083,	// 单人竞技场职业15等级段11排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL11			= 2084,	// 单人竞技场职业16等级段11排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL11_OLD		= 2085,	// 单人竞技场职业16等级段11排行榜老榜
	// 单人竞技场职业x等级段12 排行榜/排行榜老榜 (预留16个职业 2096-2127) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL12			= 2096,	// 单人竞技场职业1等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL12_OLD		= 2097,	// 单人竞技场职业1等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL12			= 2098,	// 单人竞技场职业2等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL12_OLD		= 2099,	// 单人竞技场职业2等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL12			= 2100,	// 单人竞技场职业3等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL12_OLD		= 2101,	// 单人竞技场职业3等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL12			= 2102,	// 单人竞技场职业4等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL12_OLD		= 2103,	// 单人竞技场职业4等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL12			= 2104,	// 单人竞技场职业5等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL12_OLD		= 2105,	// 单人竞技场职业5等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL12			= 2106,	// 单人竞技场职业6等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL12_OLD		= 2107,	// 单人竞技场职业6等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL12			= 2108,	// 单人竞技场职业7等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL12_OLD		= 2109,	// 单人竞技场职业7等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL12			= 2110,	// 单人竞技场职业8等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL12_OLD		= 2111,	// 单人竞技场职业8等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL12			= 2112,	// 单人竞技场职业9等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL12_OLD		= 2113,	// 单人竞技场职业9等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL12			= 2114,	// 单人竞技场职业10等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL12_OLD		= 2115,	// 单人竞技场职业10等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL12			= 2116,	// 单人竞技场职业11等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL12_OLD		= 2117,	// 单人竞技场职业11等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL12			= 2118,	// 单人竞技场职业12等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL12_OLD		= 2119,	// 单人竞技场职业12等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL12			= 2120,	// 单人竞技场职业13等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL12_OLD		= 2121,	// 单人竞技场职业13等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL12			= 2122,	// 单人竞技场职业14等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL12_OLD		= 2123,	// 单人竞技场职业14等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL12			= 2124,	// 单人竞技场职业15等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL12_OLD		= 2125,	// 单人竞技场职业15等级段12排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL12			= 2126,	// 单人竞技场职业16等级段12排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL12_OLD		= 2127,	// 单人竞技场职业16等级段12排行榜老榜
	// 单人竞技场职业x等级段13 排行榜/排行榜老榜 (预留16个职业 2486-2517) ！！！
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL13			= 2486,	// 单人竞技场职业1等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF1_LEVEL13_OLD		= 2487,	// 单人竞技场职业1等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL13			= 2488,	// 单人竞技场职业2等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF2_LEVEL13_OLD		= 2489,	// 单人竞技场职业2等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL13			= 2490,	// 单人竞技场职业3等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF3_LEVEL13_OLD		= 2491,	// 单人竞技场职业3等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL13			= 2492,	// 单人竞技场职业4等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF4_LEVEL13_OLD		= 2493,	// 单人竞技场职业4等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL13			= 2494,	// 单人竞技场职业5等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF5_LEVEL13_OLD		= 2495,	// 单人竞技场职业5等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL13			= 2496,	// 单人竞技场职业6等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF6_LEVEL13_OLD		= 2497,	// 单人竞技场职业6等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL13			= 2498,	// 单人竞技场职业7等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF7_LEVEL13_OLD		= 2499,	// 单人竞技场职业7等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL13			= 2500,	// 单人竞技场职业8等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF8_LEVEL13_OLD		= 2501,	// 单人竞技场职业8等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL13			= 2502,	// 单人竞技场职业9等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF9_LEVEL13_OLD		= 2503,	// 单人竞技场职业9等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL13			= 2504,	// 单人竞技场职业10等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF10_LEVEL13_OLD		= 2505,	// 单人竞技场职业10等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL13			= 2506,	// 单人竞技场职业11等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF11_LEVEL13_OLD		= 2507,	// 单人竞技场职业11等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL13			= 2508,	// 单人竞技场职业12等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF12_LEVEL13_OLD		= 2509,	// 单人竞技场职业12等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL13			= 2510,	// 单人竞技场职业13等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF13_LEVEL13_OLD		= 2511,	// 单人竞技场职业13等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL13			= 2512,	// 单人竞技场职业14等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF14_LEVEL13_OLD		= 2513,	// 单人竞技场职业14等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL13			= 2514,	// 单人竞技场职业15等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF15_LEVEL13_OLD		= 2515,	// 单人竞技场职业15等级段13排行榜老榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL13			= 2516,	// 单人竞技场职业16等级段13排行榜
	TPN_ARENA_BATTLE_SINGLE_PROF16_LEVEL13_OLD		= 2517,	// 单人竞技场职业16等级段13排行榜老榜

	//TPN_TOPSTAR_POPULAR_DAILY						= 600,	// 人气巨星：今日人气榜
	//TPN_TOPSTAR_POPULAR_DAILY_OLD					= 601,	// 人气巨星：今日人气榜老榜
	//TPN_TOPSTAR_POPULAR_TOTAL						= 602,	// 人气巨星：总人气榜
	//TPN_TOPSTAR_POPULAR_TOTAL_OLD					= 603,	// 人气巨星：总人气榜老榜
	//TPN_TOPSTAR_CHEER_DAILY							= 604,	// 人气巨星：今日应援榜
	//TPN_TOPSTAR_CHEER_DAILY_OLD						= 605,	// 人气巨星：今日应援榜老榜
	//TPN_TOPSTAR_CHEER_TOTAL							= 606,	// 人气巨星：总应援榜
	//TPN_TOPSTAR_CHEER_TOTAL_OLD						= 607,	// 人气巨星：总应援榜老榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR1					= 608,	// 人气巨星：明星1今日应援榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR1_OLD				= 609,	// 人气巨星：明星1今日应援榜老榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR1					= 610,	// 人气巨星：明星1总应援榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR1_OLD				= 611,	// 人气巨星：明星1总应援榜老榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR2					= 612,	// 人气巨星：明星2今日应援榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR2_OLD				= 613,	// 人气巨星：明星2今日应援榜老榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR2					= 614,	// 人气巨星：明星2总应援榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR2_OLD				= 615,	// 人气巨星：明星2总应援榜老榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR3					= 616,	// 人气巨星：明星3今日应援榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR3_OLD				= 617,	// 人气巨星：明星3今日应援榜老榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR3					= 618,	// 人气巨星：明星3总应援榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR3_OLD				= 619,	// 人气巨星：明星3总应援榜老榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR4					= 620,	// 人气巨星：明星4今日应援榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR4_OLD				= 621,	// 人气巨星：明星4今日应援榜老榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR4					= 622,	// 人气巨星：明星4总应援榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR4_OLD				= 623,	// 人气巨星：明星4总应援榜老榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR5					= 624,	// 人气巨星：明星5今日应援榜
	//TPN_TOPSTAR_CHEER_DAILY_STAR5_OLD				= 625,	// 人气巨星：明星5今日应援榜老榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR5					= 626,	// 人气巨星：明星5总应援榜
	//TPN_TOPSTAR_CHEER_TOTAL_STAR5_OLD				= 627,	// 人气巨星：明星5总应援榜老榜

	TPN_TOPSTAR_POPULAR_DAILY						= 628,	// 人气巨星：今日人气榜
	TPN_TOPSTAR_POPULAR_DAILY_OLD					= 629,	// 人气巨星：今日人气榜老榜
	TPN_TOPSTAR_POPULAR_TOTAL						= 630,	// 人气巨星：总人气榜
	TPN_TOPSTAR_POPULAR_TOTAL_OLD					= 631,	// 人气巨星：总人气榜老榜
	TPN_TOPSTAR_CHEER_DAILY							= 632,	// 人气巨星：今日应援榜
	TPN_TOPSTAR_CHEER_DAILY_OLD						= 633,	// 人气巨星：今日应援榜老榜
	TPN_TOPSTAR_CHEER_TOTAL							= 634,	// 人气巨星：总应援榜
	TPN_TOPSTAR_CHEER_TOTAL_OLD						= 635,	// 人气巨星：总应援榜老榜
	TPN_TOPSTAR_CHEER_DAILY_STAR1					= 636,	// 人气巨星：明星1今日应援榜
	TPN_TOPSTAR_CHEER_DAILY_STAR1_OLD				= 637,	// 人气巨星：明星1今日应援榜老榜
	TPN_TOPSTAR_CHEER_TOTAL_STAR1					= 638,	// 人气巨星：明星1总应援榜
	TPN_TOPSTAR_CHEER_TOTAL_STAR1_OLD				= 639,	// 人气巨星：明星1总应援榜老榜
	TPN_TOPSTAR_CHEER_DAILY_STAR2					= 640,	// 人气巨星：明星2今日应援榜
	TPN_TOPSTAR_CHEER_DAILY_STAR2_OLD				= 641,	// 人气巨星：明星2今日应援榜老榜
	TPN_TOPSTAR_CHEER_TOTAL_STAR2					= 642,	// 人气巨星：明星2总应援榜
	TPN_TOPSTAR_CHEER_TOTAL_STAR2_OLD				= 643,	// 人气巨星：明星2总应援榜老榜
	TPN_TOPSTAR_CHEER_DAILY_STAR3					= 644,	// 人气巨星：明星3今日应援榜
	TPN_TOPSTAR_CHEER_DAILY_STAR3_OLD				= 645,	// 人气巨星：明星3今日应援榜老榜
	TPN_TOPSTAR_CHEER_TOTAL_STAR3					= 646,	// 人气巨星：明星3总应援榜
	TPN_TOPSTAR_CHEER_TOTAL_STAR3_OLD				= 647,	// 人气巨星：明星3总应援榜老榜

	TPN_TOPSTAR_BEGIN								= TPN_TOPSTAR_POPULAR_DAILY,
	TPN_TOPSTAR_END									= TPN_TOPSTAR_CHEER_TOTAL_STAR3_OLD,

	TPN_ARENA_GROUP_CENTER_0		= 701,	//跨服战队排名0
	TPN_ARENA_GROUP_CENTER_1		= 702,	//跨服战队排名1
	TPN_ARENA_GROUP_CENTER_2		= 703,	//跨服战队排名2
	TPN_ARENA_GROUP_CENTER_3		= 704,	//跨服战队排名3
	TPN_ARENA_GROUP_CENTER_4		= 705,	//跨服战队排名4
	TPN_ARENA_GROUP_CENTER_5		= 706,	//跨服战队排名5
	TPN_ARENA_GROUP_CENTER_6		= 707,	//跨服战队排名6
	TPN_ARENA_GROUP_CENTER_7		= 708,	//跨服战队排名7
	TPN_ARENA_GROUP_CENTER_8		= 709,	//跨服战队排名8
	TPN_ARENA_GROUP_CENTER_9		= 710,	//跨服战队排名9
	TPN_ARENA_GROUP_CENTER_10		= 711,	//跨服战队排名10
	TPN_ARENA_GROUP_CENTER_11		= 712,	//跨服战队排名11
	TPN_ARENA_GROUP_CENTER_12		= 713,	//跨服战队排名12
	TPN_ARENA_GROUP_CENTER_13		= 714,	//跨服战队排名13
	TPN_ARENA_GROUP_CENTER_14		= 715,	//跨服战队排名14
	TPN_ARENA_GROUP_CENTER_15		= 716,	//跨服战队排名15
	TPN_ARENA_GROUP_CENTER_16		= 717,	//跨服战队排名16
	TPN_ARENA_GROUP_CENTER_17		= 718,	//跨服战队排名17
	TPN_ARENA_GROUP_CENTER_18		= 719,	//跨服战队排名18
	TPN_ARENA_GROUP_CENTER_19		= 720,	//跨服战队排名19

	TPN_ARENA_ELIMINATE_GROUP   = 741,  //
	TPN_ARENA_ELIMINATE_GROUP_OLD = 742,//

	TPN_ARENA_ELIMINATE_GROUP_2   = 745,  //
	TPN_ARENA_ELIMINATE_GROUP_2_OLD = 746,//

	TPN_ARENA_ELIMINATE_GROUP_3   = 747,  //
	TPN_ARENA_ELIMINATE_GROUP_3_OLD = 748,//

	TPN_ARENA_GROUP_SINGLE_RANK = 800, // 名人堂排行榜
	TPN_ARENA_GROUP_HIS_RANK_START = 801, // 跨服战队历史排名801~1200
	TPN_ARENA_GROUP_HIS_RANK_END = 1200,

	TPN_SSP_COMPTITION_RANK_START = 1601, //朋友圈比赛排行榜
	TPN_SSP_COMPTITION_RANK_END = 1800,

	TPN_CORPS_BOSS_RACE = 1801, // 社团BOSS竞速
	TPN_CORPS_BOSS_RACE_OLD = 1802, //

	TPN_CONTRACT_VALUE					= 1850,
	TPN_CONTRACT_VALUE_OLD				= 1851,
	TPN_HUNDRED_CORPS_BATTLE_SCORE      = 2052,
	TPN_HUNDRED_CORPS_BATTLE_SCORE_OLD      = 2053,


	TPN_HUNDRED_CORPS_CENTER_BATTLE_PERSONAL_SCORE      = 2092,
	TPN_HUNDRED_CORPS_CENTER_BATTLE_PERSONAL_SCORE_OLD  = 2093,
	TPN_HUNDRED_CORPS_CENTER_BATTLE_CORPS_SCORE      = 2094,
	TPN_HUNDRED_CORPS_CENTER_BATTLE_CORPS_SCORE_OLD      = 2095,


	TPN_DS_MAIL_RANK_REWARD_CONF_BEGIN = 4000,		// DS转榜自动使用邮件发奖的排行榜起始
	TPN_DRESSUP_SINGLE			= 4001,	// 换装舞会单人榜
	TPN_DRESSUP_SINGLE_OLD		= 4002,	// 换装舞会单人榜老榜
	TPN_DRESSUP_DOUBLE			= 4003,	// 换装舞会双人榜
	TPN_DRESSUP_DOUBLE_OLD		= 4004,	// 换装舞会双人榜老榜
	TPN_DRESSUP_TOTAL			= 4005,	// 换装舞会总榜
	TPN_DRESSUP_TOTAL_OLD		= 4006,	// 换装舞会总榜老榜
	TPN_DRESSUP_SHOW			= 4007,	// 换装舞会走秀榜
	TPN_DRESSUP_SHOW_OLD		= 4008,	// 换装舞会走秀榜老榜

	TPN_DS_MAIL_RANK_REWARD_CONF_END = 5000,        // DS转榜自动使用邮件发奖的排行榜结束

	TPN_FACTION_CONTRIBUTIONINC = 10000,//这个一个特殊的排行榜编号，该编号代表一个特殊的排行榜(每个玩家对应自己帮派中的帮贡增量排行榜)，该编号主要用于领奖用.


	TPN_NEW_DB_BEGIN			= 10001, // 新db榜开始索引
	TPN_LOTTERY_SEA				= 10001,	// 星海密藏排行榜
	TPN_LOTTERY_SEA_OLD			= 10002,	// 星海密藏排行榜老榜
	TPN_ZSPACE_DAILY_LINKS      = 10003, //z空间每日人气值
	TPN_ZSPACE_DAILY_LINKS_OLD      = 10004, //z空间每日人气值
	TPN_ZSPACE_CUR_LINKS        = 10005, //z空间人气值
	TPN_ZSPACE_CUR_LINKS_OLD        = 10006, //z空间人气值
	TPN_LOTTERY_SHIP			= 10007,	// 冰海行动排行榜
	TPN_LOTTERY_SHIP_OLD		= 10008,	// 冰海行动排行榜老榜
	TPN_QXQY					= 10012,	// 千寻奇遇排行榜
	TPN_QXQY_OLD				= 10013,	// 千寻奇遇排行榜老榜

	TPN_THUNDERSTRIKE_MULTI		= 10022,	// 天谴计划组队榜
	TPN_THUNDERSTRIKE_MULTI_OLD		= 10023,	// 天谴计划组队老榜

	TPN_MAIL_RANK_REWARD_CONF_BEGIN	= 15000,	// 转榜自动使用邮件发奖的排行榜起始
	TPN_MAIL_RANK_REWARD_CONF_END	= 17000,	// 转榜自动使用邮件发奖的排行榜结束

	TPN_ARENA_GROUP_NEW_PLAYRE_RANK		= 18003, // 新荣耀天梯名人堂排行榜
	TPN_ARENA_GROUP_NEW_HIS_RANK_START 	= 18005, // 跨服战队历史排名18005~18017
	TPN_ARENA_GROUP_NEW_HIS_RANK_END 	= 18017, //

	TPN_NEW_DB_END				= 20000, // 新db榜结束索引(最大可用索引+1)
};

enum TOPLIST_CATEGORY
{
	TPT_GLOBLE = 0, //全局排行榜
	TPT_FACTION_CONTRIBUTE_INC = 1, //帮派帮贡增量排行榜--老榜
	TPT_FACTION_CONTRIBUTE_INC_TODAY = 2, //帮派帮贡增量排行榜--新帮
	TPT_ARENA_GROUP = 3,	//战队排行榜
};
enum TOPLIST_INFO_TYPE
{
	TIT_PLAYER      = 0,    //玩家数据排行榜
	TIT_FACTION     = 1,    //帮派数据排行榜
	TIT_GROUP	= 2,	//战队排行榜
};
enum TOPLIST_SAVEOLD_TYPE
{
	TST_INVALID = -1000,
	TST_BEGIN,
	TST_HOUR,
	TST_DAY,
	TST_WEEK,
	TST_MONTH,
	TST_MONTHWEEK,
	TST_FOREVER,//不会删除的永久排行榜，一定是主榜
	TST_NOTAUTO,
	TST_OPEN_SERVER_DAY,
	TST_END,
};
/*
inline int GetFactionIndustryTopTid(int nation)
{
	return TPN_FACTION_INDUSTRY_N1 + nation - 1;
}
*/
inline int  IsTpnCorpsRepu(int repu)
{
	static  std::map<int, int> tpn =
	{
		{ CRID_LOCAL_HUNDRED_CORPS_BATTBLE_SCORE, TPN_HUNDRED_CORPS_BATTLE_SCORE},
	};

	auto it = tpn.find(repu);
	if (it != tpn.end())
	{
		return it->second;
	}
	return 0;
}
inline bool IsDailySalesOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_DAILY_SALES1_OLD || top_tid == GNET::TPN_DAILY_SALES2_OLD || top_tid == GNET::TPN_DAILY_SALES3_OLD || top_tid == GNET::TPN_DAILY_SALES4_OLD || top_tid == GNET::TPN_DAILY_SALES5_OLD || top_tid == GNET::TPN_DAILY_SALES6_OLD || top_tid == GNET::TPN_DAILY_SALES7_OLD || top_tid == GNET::TPN_DAILY_SALES8_OLD || top_tid == GNET::TPN_DAILY_SALES9_OLD || top_tid == GNET::TPN_DAILY_SALES10_OLD || top_tid == GNET::TPN_DAILY_SALES11_OLD || top_tid == GNET::TPN_DAILY_SALES12_OLD || top_tid == GNET::TPN_DAILY_SALES13_OLD || top_tid == GNET::TPN_DAILY_SALES14_OLD || top_tid == GNET::TPN_DAILY_SALES15_OLD || top_tid == GNET::TPN_DAILY_SALES16_OLD || top_tid == GNET::TPN_DAILY_SALES17_OLD || top_tid == GNET::TPN_DAILY_SALES18_OLD || top_tid == GNET::TPN_DAILY_SALES19_OLD || top_tid == GNET::TPN_DAILY_SALES20_OLD || top_tid == GNET::TPN_DAILY_SALES21_OLD || top_tid == GNET::TPN_DAILY_SALES22_OLD || top_tid == GNET::TPN_DAILY_SALES23_OLD || top_tid == GNET::TPN_DAILY_SALES24_OLD);
}
inline bool IsVideoGameOldTopTid(int top_tid)
{
	return (/*top_tid == GNET::TPN_VIDEO_GAME1_OLD || */top_tid == GNET::TPN_VIDEO_GAME2_OLD || top_tid == GNET::TPN_VIDEO_GAME3_OLD || top_tid == GNET::TPN_VIDEO_GAME4_OLD || top_tid == GNET::TPN_VIDEO_GAME5_OLD || top_tid == GNET::TPN_VIDEO_GAME6_OLD || top_tid == GNET::TPN_VIDEO_GAME7_OLD || top_tid == GNET::TPN_VIDEO_GAME8_OLD || top_tid == GNET::TPN_VIDEO_GAME9_OLD || top_tid == GNET::TPN_VIDEO_GAME10_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME1_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME2_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME3_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME4_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME5_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME6_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME7_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME8_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME9_OLD || top_tid == GNET::TPN_OUT_VIDEO_GAME10_OLD);
}
inline bool IsPleasureOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_PLEASURE_OLD);
}
inline bool IsSpringOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_SPRING_OLD);
}
inline bool IsDragonHouseOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_DRAGON_HOUSE_OLD || top_tid == GNET::TPN_HEAVEN10_OLD || top_tid == GNET::TPN_HEAVEN9_OLD);
}
inline bool IsGodExploreOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_GOD_EXPLORE_OLD);
}
inline bool IsPersonalTargetOldTopTid(int top_tid)
{
	return top_tid == GNET::TPN_PERSONAL_GOAL_TOTAL_1_OLD;
}
inline bool IsTeamRechargeRoleOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_TEAM_RECHARGE_ROLE_OLD);
}
inline bool IsTeamRechargeOldTopTid(int top_tid)
{
	return top_tid == TPN_TEAM_RECHARGE_TEAM_OLD;
}
inline bool IsToTeamRechargeOldTopTid(int top_tid)
{
	return IsTeamRechargeOldTopTid(top_tid);
}
inline bool IsLotterySeaOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_LOTTERY_SEA_OLD);
}
inline bool IsLotteryShipOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_LOTTERY_SHIP_OLD);
}
inline bool IsTopStarOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_TOPSTAR_CHEER_TOTAL_OLD || top_tid == GNET::TPN_TOPSTAR_CHEER_TOTAL_STAR1_OLD || top_tid == GNET::TPN_TOPSTAR_CHEER_TOTAL_STAR2_OLD || top_tid == GNET::TPN_TOPSTAR_CHEER_TOTAL_STAR3_OLD);
}
inline bool IsHeavenDiceOldTopTid(int top_tid)
{
	return (top_tid == GNET::TPN_HEAVEN1_OLD || top_tid == GNET::TPN_HEAVEN2_OLD || top_tid == GNET::TPN_HEAVEN3_OLD || top_tid == GNET::TPN_HEAVEN4_OLD || top_tid == GNET::TPN_HEAVEN5_OLD || top_tid == GNET::TPN_HEAVEN6_OLD || top_tid == GNET::TPN_HEAVEN7_OLD || top_tid == GNET::TPN_HEAVEN8_OLD);
}
inline bool IsSSPOldTopTid(int top_tid)
{
	return top_tid >= TPN_SSP_COMPTITION_RANK_START && top_tid <= TPN_SSP_COMPTITION_RANK_END;
}
inline bool IsRechargeConsumeOldTopTid(int top_tid)
{
	return top_tid == TPN_RECHARGE_CONSUME_OLD;
}
inline bool IsCorpsBossScoreOldTopTid(int top_tid)
{
	return top_tid == TPN_CORPS_BOSS_SCORE_OLD;
}
inline bool IsToCorpsOldTopTid(int top_tid)
{
	return IsCorpsBossScoreOldTopTid(top_tid);
}
inline bool IsLocalHundredCorpsBattleOldTid(int top_tid)
{
	return (top_tid == GNET::TPN_HUNDRED_CORPS_BATTLE_SCORE_OLD);

}
inline bool IsLocalHundredCenterCorpsBattleOldTid(int top_tid)
{
	return (top_tid == GNET::TPN_HUNDRED_CORPS_CENTER_BATTLE_CORPS_SCORE_OLD);
}
inline bool IsLocalHundredCenterCorpsBattlePersonalOldTid(int top_tid)
{
	return (top_tid == GNET::TPN_HUNDRED_CORPS_CENTER_BATTLE_PERSONAL_SCORE_OLD);
}

inline bool IsMailRankRewardTop(int top_tid)
{
	bool is_old_tid = false;
	switch (top_tid)
	{
	case GNET::TPN_QXQY_OLD:
	case GNET::TPN_MAIL_RANK_REWARD_CONF_BEGIN ... GNET::TPN_MAIL_RANK_REWARD_CONF_END:
	{
		is_old_tid = true;
	}
	break;
	case GNET::TPN_DS_MAIL_RANK_REWARD_CONF_BEGIN ... GNET::TPN_DS_MAIL_RANK_REWARD_CONF_END:
	{
		is_old_tid = true;
	}
	break;
	default:
		break;
	}
	return is_old_tid;
}

inline int GetMailRewardMailCategory(int top_tid)
{
	if (IsDailySalesOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_DAILY_SALES;
	}
	else if (IsPleasureOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_PLEASURE_DICE;
	}
	else if (IsSpringOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_SPRING_DICE;
	}
	else if (IsDragonHouseOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_DRAGON_HOUSE;
	}
	else if (IsGodExploreOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_GOD_EXPLORE;
	}
	else if (IsPersonalTargetOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_PERSONAL_TARGET;
	}
	else if (IsVideoGameOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_VIDEO_GAME;
	}
	else if (IsTopStarOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_TOP_STAR;
	}
	else if (IsHeavenDiceOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_HEAVEN_DICE;
	}
	else if (IsSSPOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_SSP;
	}
	else if (IsRechargeConsumeOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_RECHARGE_CONSUME;
	}
	else if (IsCorpsBossScoreOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_CORPS_BOSS_SCORE;
	}
	else if (IsTeamRechargeOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_TEAM_RECHARGE;
	}
	else if (IsTeamRechargeRoleOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_TOP_LIST_MAIL_REWARD_TEAM_RECHARGE_ROLE;
	}
	else if (IsLotterySeaOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_LOTTERY_SEA;
	}
	else if (IsLotteryShipOldTopTid(top_tid))
	{
		return MAIL_CATEGORY_LOTTERY_SHIP;
	}
	else if (IsLocalHundredCorpsBattleOldTid(top_tid))
	{
		return MAIL_CATEGORY_HUNDRED_CORPS_BATTLE;
	}
	else if (IsLocalHundredCenterCorpsBattlePersonalOldTid(top_tid))
	{
		return MAIL_CATEGORY_HUNDRED_CENTER_CORPS_BATTLE_PERSONA_SCORE;
	}
	else if (IsLocalHundredCenterCorpsBattleOldTid(top_tid))
	{
		return MAIL_CATEGORY_HUNDRED_CENTER_CORPS_BATTLE;
	}
	else if (IsMailRankRewardTop(top_tid))
	{
		return MAIL_CATEGORY_COMMON_PB_MAIL_FOR_DICE_TOP_LIST;
	}

	return -1;
}

inline bool IsMailRewardOldTopTid(int top_tid)
{
	return (GetMailRewardMailCategory(top_tid) >= 0);
}
inline bool IsLotteryActivityTopList(int top_tid)
{
	if (top_tid >= TPN_TOPSTAR_BEGIN && top_tid <= TPN_TOPSTAR_END)
	{
		return true;
	}

	return false;
}
inline int GetProfessionFightingTopTid(int profession)
{
	if (profession == 6)
	{
		return TPN_FIGHT_PROF_BOW;
	}
	else if (profession == 7)
	{
		return TPN_FIGHT_PROF_SUMMON;
	}
	else if (profession == 8)
	{
		return TPN_FIGHT_PROF_TIANSHA;
	}
	else if (profession >= 9 && profession <= 16)
	{
		return TPN_FIGHT_PROF_SKATE  + 2 * (profession - 9);
	}
	return TPN_FIGHT_PROF_BLADE + (profession - 1);
}
inline bool IsProfessionFightingTopTid(int tid)
{
	if ( tid >= TPN_FIGHT_PROF_SKATE && tid <= TPN_FIGHT_PROF_16_OLD &&  !(tid & 0x1))
	{
		return  true;
	}
	return false;
}
inline bool IsLadderInstance(int tid)
{
	return (279 == tid);
}
inline int GetLadderLevelRange(int level)
{
	if (level < 35)
	{
		return -1;
	}
	if (level < 70)
	{
		return 0;
	}
	return 1;
}
inline int GetLadderTopTid(unsigned char prof)
{
	if (prof <= 0 || prof > MAX_PROFESSION_COUNT)
	{
		return -1;
	}

	return (TPN_LADDER_PROF1 + (prof - 1) * 2);
}
inline int GetLadderOldTopTid(unsigned char prof)
{
	if (prof <= 0 || prof > MAX_PROFESSION_COUNT)
	{
		return -1;
	}

	return (TPN_LADDER_PROF1_OLD + (prof - 1) * 2);
}
inline bool GetLadderRepuID(int level, int& repu_progress, int& repu_time)
{
	if (level < 35)
	{
		return false;
	}
	repu_progress = REPUID_LADDER_PROGRESS;
	repu_time = REPUID_LADDER_TIME;
	return true;
}

inline bool ConvertDSRepuidToTopTid(int rep_id, int& top_tid, int nation)
{
	switch (rep_id)
	{
	case REPUID_FLOWER:
		top_tid = TPN_FLOWER_RECV;
		return true;
	case REPUID_FLOWER_WEEK:
		top_tid = TPN_FLOWER_RECV_WEEK;
		return true;
	case REPUID_FLOWER_SEND:
		top_tid = TPN_FLOWER_SEND;
		return true;
	case REPUID_FLOWER_SEND_WEEK:
		top_tid = TPN_FLOWER_SEND_WEEK;
		return true;
	default:
		break;
	}
	return false;
}
inline bool CheckThanksGivingCoolDown(ruid_t roleid, int day_begin, int week_begin, int today_timestamp, int cooldown_timestamp, const std::set<ruid_t>& roleid_set)
{
	if (day_begin == today_timestamp)
	{
		return false;
	}
	if (week_begin - cooldown_timestamp >= SECOND_PER_WEEK * 2)
	{
		return true;
	}
	else if (roleid_set.find(roleid) != roleid_set.end())
	{
		return false;
	}
	return true;
}
inline void HandleThanksGivingCoolDown(ruid_t roleid, int day_begin, int week_begin, int cooldown_timestamp, const std::function<void(bool, ruid_t, int, int)>& func)
{
	bool need_refresh_cool_down = false;
	if (week_begin - cooldown_timestamp >= SECOND_PER_WEEK * 2)
	{
		need_refresh_cool_down = true;
	}

	func(need_refresh_cool_down, roleid, day_begin, week_begin);
}
enum CAMPAIGN_SYNC_MODE
{
	CSM_INIT = 0,	//初始化活动信息
	CSM_UPDATE = 1,	//更新活动信息
};

enum CAMPAIGN_INFO_TYPE
{
	CIT_OPEN	= 0,	//开启活动
	CIT_CLOSE	= 1,	//关闭活动
	CIT_FORBID_OPEN	= 2,	//时间到，但是被打开条件限制
};

enum INSTANCE_RETURN_OP
{
	IRO_CANCEL	= 0,	//删除进入信息
	IRO_ENTER	= 1,	//进入当前副本
};

enum ELIMINATE_GROUP_TYPE : int
{
	EGT_5V5					= 0,
	EGT_3V3					= 1,
	EGT_2V2					= 2,
};
static bool IsValidEliminateGroupType(int egt)
{
	return (egt == EGT_5V5 || egt == EGT_3V3 || egt == EGT_2V2);
}
static int GetEliminateGroupTopTid(int egt)
{
	if (!IsValidEliminateGroupType(egt))
	{
		return 0;
	}

	if (egt == EGT_5V5)
	{
		return TPN_ARENA_ELIMINATE_GROUP;
	}
	else if (egt == EGT_3V3)
	{
		return TPN_ARENA_ELIMINATE_GROUP_2;
	}
	else if (egt == EGT_2V2)
	{
		return TPN_ARENA_ELIMINATE_GROUP_3;
	}

	return 0;
}

enum SERVER_MODE
{
	SMODE_NORMAL	= 0,	// 正常线
	SMODE_PRIVATE	= 1,	// 私有线
};

enum TEAM_STATUS
{
	TEAM_STATUS_CREATE,								// 队伍创建
	TEAM_STATUS_JOIN,								// 队员加入
	TEAM_STATUS_SYNC_DATA,							// 队伍信息同步
	TEAM_STATUS_LEAVE,								// 队员退出
	TEAM_STATUS_LEAVE_IGNORE_TEAM_FOLLOE_INVITE,	// 队员退出，忽略发起组队跟随
	TEAM_STATUS_DISMISS,							// 队伍解散
	TEAM_STATUS_ABDICATE,							// 改变队长
	TEAM_STATUS_ABDICATE_IGNORE_TEAM_FOLLOE_INVITE,	// 改变队长，忽略发起组队跟随
	TEAM_STATUS_SETRULE,							// 改变队伍规则
	TEAM_STATUS_CHANGE_ZONE,						// 跨服
	TEAM_STATUS_TEAM_FOLLOW_INVITE,					// 发起一次组队跟随邀请
};

enum TEAM_TASK_INFO
{
	GEN_MONSTER_FAIL	= 0,
	TEAM_MONSTER_DIE	= 1,
	CLEAR_MONSTER		= 2,
	MONSTER_CLEARED		= 3,
	CLEAR_NATION_ESCORT	= 4,
};

enum MINGXING_EVENT
{
	MX_EVENT_INVALID	= 0, //无效事件
	MX_EVENT_NEW_BY_TASK	= 1, //任务产生新明星
	MX_EVENT_NEW_BY_TP	= 2, //排行榜产生新明星
};

enum FACTION_TIGUAN_STATE
{
	FTS_NONE		= 0,
	FTS_PREPARE0		= 1, //准备阶段0, 服务器保护
	FTS_PREPARE1		= 2, //准备阶段1, 踢馆被服务器接受
	FTS_PREPARE2		= 3, //准备阶段2, 开始限制进入人员, 随后清场
	FTS_BEGIN_FIGHT		= 4, //开始战斗
	FTS_END			= 5, //结束战斗, 接下来可能进入奖励时间按
};

enum FACTION_WAR_TYPE
{
	FWT_TIGUAN		= 1, //踢馆
};

enum FACTION_TIGUAN_GOAL
{
	FTG_PLAY		= 0, //娱乐
	FTG_SUB			= 1, //实例通行
	FTG_MAIN		= 2, //抢占驻地
};

enum DB_SAVE_ROLE_TYPE
{
	DSRT_IS_GM		= 0x01,		//是GM
	DSRT_IS_ROAMER		= 0x02,		//是漫游者
};

enum DB_PACKE_ROAM_ROLE_TYPE
{
	DPRRT_ALL_DATA		= 0,		//打包所有数据
	DPRRT_GS_DATA		= 1,		//打包GS数据
	DPRRT_DIFF_DATA     = 2,

	DPRRT_COUNT,
};

enum DB_ROLE_SAVE_PRIORITY
{
	DRSP_NOTING		= 0,		//未定义
	DRSP_AUTO_SAVE		= 1,		//自动保存
	DRSP_LOGOUT		= 2,		//离线后保存
	DRSP_ROAM_BACK		= 3,		//跨服回归存盘
};

enum ROAM_PROTOCOL_DIRECTION
{
	RPD_SEND_TO_DST		= 0,		//协议发向目标副
	RPD_SEND_TO_SRC		= 1,		//协议发向源服
};

enum ROAM_SYNC_STATUS_TYPE
{
	RSS_LOGOUT		= 0,		//登出操作
	RSS_ROAMIN_SUCCESS	= 1,		//跨服登陆正常
	RSS_ROAM_BACK		= 2,		//尝试回归
	RSS_LOSTCONNECT		= 3,		//断线了
};

enum LOGIN_MASK
{
	LOGIN_ROAM 		= 0x01, 	//跨服登录
	LOGIN_DEFAULT_POS 	= 0x02, 	//默认出生点登录
	LOGIN_CHANGE_LINE	= 0x04,		//换线登陆
	LOGIN_ROAM_RECONNECT	= 0x08,		//断线重连登陆
	LOGIN_RECONNECT		= 0x10,		//正常游戏断线重连
	LOGIN_KICK_ROAM		= 0x20,		//踢掉跨服角色

	LOGIN_CLINET_USE	= LOGIN_DEFAULT_POS | LOGIN_KICK_ROAM,
};

enum INSTANCE_DELVERY_STATE
{
	IS_RUNNING		= 0,		//正在运行
	IS_EXPORT_BORAD         = 1,            //到达出口版面
	IS_SUCCEED_FIHISH	= 2,		//成功完成副本
	IS_CLOSED		= 3,		//副本关闭，玩家被踢出
};

enum DEVICE_OS
{
	DEVICE_OS_UNKNOWN	= -1,
	DEVICE_OS_IOS		= 0,
	DEVICE_OS_ANDROID	= 1,
	DEVICE_OS_WP		= 2,
};

enum DEVICE_OS_EN
{
	DEVICE_OS_EN_UNKNOW		= -1,
	DEVICE_OS_EN_IOS		= 2,
	DEVICE_OS_EN_ANDROID	= 3,
	DEVICE_OS_EN_PC			= 4,
};

enum SOUL_TREE_HOMETOWN_OBJ_TYPE
{
	SOUL_TREE_OBJ_TYPE_1 = 18982,
	SOUL_TREE_OBJ_TYPE_2 = 19348,
	SOUL_TREE_OBJ_TYPE_3 = 19349,
};

enum ROAM_GLOBAL_WORLD_TYPE
{
	RGWT_COMMON			= 1,
	RGWT_ARENA_TEAM		= 2,
	RGWT_BDSG			= 3,
};

inline bool IsSoulTreeHometownObjType(int obj_type)
{
	return obj_type == SOUL_TREE_OBJ_TYPE_1 || obj_type == SOUL_TREE_OBJ_TYPE_2 || obj_type == SOUL_TREE_OBJ_TYPE_3;
}

inline bool ValidMingxingShowId(const ruid_t& id)
{
	return (id >= 200 && id < 300);
}

inline char _i2c(unsigned char i)
{
	const char *_table = "0123456789ABCDEF";
	if (i < 16)
	{
		return _table[i];
	}
	return '0';
}
//确保传入src_str的大小要小于256
inline void Str2Octets_256(const std::string& src_str, Octets& dst)
{
	Octets from(src_str.data(), src_str.size());
	CharsetConverter::conv_charset_t2u(from, dst);
}
inline Octets& B16Encode(Octets& o)
{
	Octets dst;
	dst.resize(o.size() * 2);
	unsigned char *src_data = (unsigned char *)o.begin();
	char *dst_data = (char *)dst.begin();
	for (unsigned int i = 0; i < o.size(); i++)
	{
		dst_data[2 * i] = _i2c(src_data[i] >> 4);
		dst_data[2 * i + 1] = _i2c(src_data[i] & 0x0f);
	}
	o.swap(dst);
	return o;
}

//静态数组 用于object
struct user_account
{
	enum { MAX_PLAYER_NAME_LEN	= 101, };
	char str[MAX_PLAYER_NAME_LEN];
	unsigned char len;

	user_account()
	{
		len = 0;
		memset(str, 0, sizeof(str));
	}

	// copy constructor
	user_account(const user_account& rhs)
	{
		assert(rhs.len < MAX_PLAYER_NAME_LEN);
		memcpy(str, rhs.str, rhs.len);
		str[(len = rhs.len)] = 0;
	}
	user_account& operator = (const std::string& rhs)
	{
		assert(rhs.size() < MAX_PLAYER_NAME_LEN);
		memcpy(str, rhs.data(), rhs.size());
		str[(len = rhs.size())] = 0;
		return *this;
	}
	user_account& operator = (const GNET::Octets& rhs)
	{
		assert(rhs.size() < MAX_PLAYER_NAME_LEN);
		memcpy(str, rhs.begin(), rhs.size());
		str[(len = rhs.size())] = 0;
		return *this;
	}
	user_account& operator = ( const user_account& rhs)
	{
		assert(rhs.len < MAX_PLAYER_NAME_LEN);
		memcpy(str, rhs.str, rhs.len);
		str[(len = rhs.len)] = 0;
		return *this;
	}

	bool operator < (const user_account& rhs) const
	{
		int rst	= memcmp(str, rhs.str, std::min(len, rhs.len));
		if (rst)
		{
			return rst < 0;
		}
		else
		{
			return len < rhs.len;
		}
	}
	const std::string ToStr() const
	{
		return std::string(str, len);
		//return ret.assign(str, len);
	}

	const GNET::Octets ToOctets() const
	{
		return GNET::Octets(str, len);
		//GNET::Octets data;
		//return data.replace(str, len);
	}
};

struct BIPlayerInfo
{
	std::string appid;
	int		from;
	std::string	account;
	std::string account_without_zoneid;
	std::string	platform;
	std::string	mac;
	int		os;
	std::string	peer;
	std::string	register_channel;
	std::string	channel;
	std::string client_version;
	std::string client_res_version;
	std::string device_id;
	std::string device_model;
	std::string open_key;
	std::string oaid;
	std::string caid;

	BIPlayerInfo(): from(0), os(-1) {}

	template<typename SyncBIPlayerInfo>
	void Build(SyncBIPlayerInfo& info) const
	{
		info.appid = appid;
		info.account = account;
		info.platform = platform;
		info.register_channel = register_channel;
		info.channel = channel;
		info.mac = mac;
		info.peer = peer;
		info._os = os;
		info.from = from;
		info.client_version = client_version;
		info.client_res_version = client_res_version;
		info.device_id = device_id;
		info.device_model = device_model;
		info.open_key = open_key;
		info.oaid = oaid;
		info.caid = caid;
	}

	template<typename SyncBIPlayerInfo>
	void Load(const SyncBIPlayerInfo& info, int zoneid = 0)
	{
		appid = info.appid;
		account = info.account;
		platform = info.platform;
		register_channel = info.register_channel;
		channel = info.channel;
		mac = info.mac;
		peer = info.peer;
		os = info._os;
		from = zoneid ? zoneid : info.from;
		client_version = info.client_version;
		client_res_version = info.client_res_version;
		device_id = info.device_id;
		device_model = info.device_model;
		open_key = info.open_key;
		oaid = info.oaid;
		caid = info.caid;
	}
};

/*class BIPlayerInfoManager
{
	std::map<std::string, BIPlayerInfo> _map; //userid => BIPlayerInfo
	mutable GNET::Thread::Mutex locker;

	BIPlayerInfoManager(): locker("BIPlayerInfoManager") {}

public:
	static BIPlayerInfoManager& GetInstance()
	{
		static BIPlayerInfoManager _instance;
		return _instance;
	}
	bool Get(const std::string& account, BIPlayerInfo& info) const
	{
		GNET::Thread::Mutex::Scoped l(locker);

		std::map<std::string, BIPlayerInfo>::const_iterator it = _map.find(account);
		if (it != _map.end())
		{
			info = it->second;
			return true;
		}
		return false;
	}
	bool Get(const user_account& user_id, BIPlayerInfo& info) const
	{
		return Get(user_id.ToStr(), info);
	}
	bool Get(const GNET::Octets& user_id, BIPlayerInfo& info) const
	{
		return Get(std::string((char *)user_id.begin(), user_id.size()), info);
	}
	int GetFrom() const
	{
		if (_map.empty()) return 0;
		return _map.begin()->second.from;
	}
	void Set(const BIPlayerInfo& info)
	{
		GNET::Thread::Mutex::Scoped l(locker);

		_map[info.account] = info;
		_map[info.account].account_without_zoneid = info.account.substr(0, info.account.find('$'));
	}
};*/

inline bool GetZoneId(const std::string& account_str, int *_zoneid)
{
	if (!_zoneid)
	{
		return false;
	}

	int& zoneid = *_zoneid;
	size_t pos = account_str.find_last_of('@');
	if (pos != std::string::npos )
	{
		int offset = static_cast<int>(pos);
		if ( offset > 1 && offset < (int)account_str.size())
		{
			std::string zoneidStr( account_str.c_str() + offset + 1, account_str.size() - offset - 1 );
			zoneid = atoi( zoneidStr.c_str() );
			return true;
		}
	}

	extern int g_zoneid;
	zoneid = g_zoneid;
	return false;
}

inline bool GetZoneId(const Octets& account, int *_zoneid)
{
	std::string account_str((char *)account.begin(), account.size());
	return GetZoneId(account_str, _zoneid);
}

/*
 *  获取新版Midas的auany appid
 *  @account: 账号
 *  @return: auany appid
 */
inline std::string GetAuanyAppID(const GNET::BIPlayerInfo& bi)
{
	if (bi.os != DEVICE_OS_IOS && bi.os != DEVICE_OS_ANDROID)
	{
		return "1";
	}
	std::stringstream ss;
	if (bi.account.size() > 2 && strncmp(bi.account.c_str(), "G_", 2) == 0)
	{
		ss << "G_";
	}
	if (bi.os == DEVICE_OS_IOS)
	{
		ss << "ios_";
	}
	else
	{
		ss << "android_";
	}
	ss << bi.platform;

	return ss.str();
}

inline int GetRoleServerID(const std::string& account_str)
{
	extern int g_zoneid;
	if (!account_str.empty())
	{
		size_t pos = account_str.find_last_of('@');
		if (pos != std::string::npos )
		{
			int offset = static_cast<int>(pos);
			if ( offset < 1 || offset >= (int)account_str.size())
			{
				return g_zoneid;
			}
			//pos是@的位置，切account和zoneid的时候都要跳过
			//std::string zoneidStr( (char*)account_str.begin() + offset + 1 , account_str.size()- offset -1 );
			std::string zoneidStr = account_str.substr(offset + 1);
			return atoi( zoneidStr.c_str() );
		}
	}
	return g_zoneid;
}


template< class ... Sentence >
inline void ss_cat( std::stringstream& ss, const Sentence& ... words  )
{
	std::initializer_list<int> { (ss << words, 0) ... } .size();
}

template< class ... Sentence >
inline std::string tcat( const Sentence& ... words  )
{
	std::stringstream ss;
	ss_cat( ss, words ... );
	return ss.str();
}

// 提高list.size() 的效率
template<typename ListType>
struct sized_list
{
	typedef std::list<ListType> RealList;
	typedef typename RealList::iterator iterator;
private:
	RealList _list;
	size_t _count = 0;
public:
	unsigned long size() const
	{
		return _count;
	}
	bool empty() const
	{
		return _count == 0;
	}
	iterator begin ()
	{
		return _list.begin();
	}
	iterator end ()
	{
		return _list.end();
	}
	ListType& front()
	{
		return _list.front();
	}
	ListType& back()
	{
		return _list.back();
	}

	iterator insert(ListType& node)
	{
		++_count;
		return _list.insert(_list.end(), node);
	}

	void push_back(const ListType& node)
	{
		++_count;
		return _list.push_back(node);
	}
	iterator erase(iterator it)
	{
		--_count;
		return _list.erase(it);
	}
};

enum BCOVER_TYPE
{
	BTYPE_LEVEL = 1,							// 等级
	BTYPE_BIND_MONEY = 2,						// 金钱（钻石、金币、点卷）
	BTYPE_LOGIN_TIME = 8,						// 最近登录时间
	BTYPE_PLAT = 12,							// 平台类型，（涉及礼包发货， 表示字段和在idip上面的ID一致） 0-ios ,1-安卓
	BTYPE_GAME_MODE = 13,						// 游戏模式
	BTYPE_PLAY_TIME = 14,						// 单次游戏时长
	BTYPE_FIGHTCAPACITY = 17,					// 战力
	BTYPE_REGIEST_TIME = 25,					// 用户注册时间
	BTYPE_AREAID = 26,							// 大区信息（涉及礼包发货， 表示字段和在idip上面的ID一致）
	BTYPE_ZONEID = 27,							// 服务器信息（涉及礼包发货， 表示字段和在idip上面的ID一致）
	BTYPE_ROLEID = 28,							// 角色ID（涉及礼包发货， 表示字段和在idip上面的ID一致）
	BTYPE_ROLENAME = 29,						// 角色名称
	BTYPE_CORPSID = 30,							// 所属公会ID
	BTYPE_CORPS_JOINTIME = 31,					// 加入公会时间
	BTYPE_ACTIVE_TIME_NOCHARGE = 36,			// 用户累计活跃时长达N分钟且未付费
	BTYPE_LEVEL_NOCHARGE = 37,					// 用户达到N等级且未付费
	BTYPE_TOTAL_AMT = 43,						// 累积充值金额
	BTYPE_RECHARGE_AMT = 44,					// 单笔充值金额
	BTYPE_VIP_LEVEL = 45,						// 游戏内玩家VIP等级
	BTYPE_RECHARGE_TIME = 46,					// 充值时间
	BTYPE_NICKNAME = 47,						// 游戏昵称
	BTYPE_DOWNLOAD_TIME = 100,					// 下载时间
	BTYPE_LOGIN_CHANNEL = 201,					// 登录渠道号
	BTYPE_REG_CHANNEL = 202,					// 注册渠道号
	BTYPE_CORPS_NAME = 301,						// 公会名称
	BTYPE_CORPS_LEVEL = 302,					// 公会等级（升级）
	BTYPE_CORPS_REPU = 305,						// 公会荣誉
	BTYPE_CORPS_CREATE_TIME = 306,				// 公会创建时间
	BTYPE_CORPS_MEMBER_NUM = 308,				// 公会成员人数
	BTYPE_CORPS_MEMBER_CHANGE = 309,			// 公会成员变动（1-加入，2-退出）
	BTYPE_CORPS_MEMBER_POS_CHANGE = 311,		// 公会人员身份变更（1-会长，2-副会长，3-成员）
	BTYPE_CORPS_BIND_QQ = 312,					// 公会绑定的QQ群
	BTYPE_CORPS_BIND_QQ_TIME = 313,				// QQ群的绑定时间
	BTYPE_SUCCEED_INSTANCE = 1011,				// 通关挑战副本
	BTYPE_FINISH_STORY = 1012,					// 剧情完成
	BTYPE_AVENTURE_POINT = 1013,				// 异闻点数
	BTYPE_GUARD_UPGRADE = 1014,					// 宠物进阶数
	BTYPE_GET_LONGYU_EQUIP = 1015,				// 获得一件龙语装备
	BTYPE_CAREER_LEVELUP = 1016,				// 身份升级
	BTYPE_SHOP_SELL = 1017,						// 商业街出售一件物品
	BTYPE_ACTIVE_POINT_FULL = 1018,				// 活跃度满150
	BTYPE_ROAM_WIN = 1019,						// 跨服战场胜利一次
	BTYPE_SHARE_PHOTO = 1020,					// 分享一张合照到社区
	BTYPE_GURAD_UPGRADE_FULL = 1021,			// 一只宠物进化到完全体
	BTYPE_ACTIVE_TOP_LONGYU = 1022,				// 激活一个新的橙色龙语
	BTYPE_5STAR_RETINUE = 1023,					// 获得一个五星以上的伙伴
	BTYPE_ADVENTRUE_TASK_FINISH = 1024,			// 完成一个异闻任务
	BTYPE_NEW_SURFACE = 1025,					// 获得一个新的座驾
	BTYPE_SURFACE_MODIFY = 1026,				// 座驾改装一次
	BTYPE_INSTANCE_FIRST_FAIL_GUANGMING = 1027, // 光明副本首次失败
	BTYPE_INSTANCE_FIRST_FAIL_JILE = 1028,		// 极乐副本首次失败
	BTYPE_INSTANCE_FIRST_FAIL_MORI = 1029,		// 末日副本首次失败
	BTYPE_ROAM_FAIL_SERIES_TWO = 1030,			// 跨服战场连续失败两次
	BTYPE_CORPS_RACE_FAIL = 1031,				// 社团竞赛失败
	BTYPE_INSTANCE_FAIL_SHIXUN = 1032,			// 实训竞技场连续失败两次
	BTYPE_INSTANCE_FAIL_MORI = 1033,			// 末日挑战副本失败
	BTYPE_FINISH_LONG_ADVENTURE = 1034,			// 完成任意一个长异闻
	BTYPE_BEAUTY_LEVEL = 1035,					// 靓丽度等级数
	BTYPE_FASHION_COUNT = 1036,					// 获得时装数
	BTYPE_PERSONALITY_COLOR = 1037,				// 完成一个人格色彩
	BTYPE_RETINUE_COUNT = 1038,					// 获得伙伴数
	BTYPE_LZ_REG_CHANNEL = 1039,				// 最初上报的注册渠道号
	BTYPE_ONLINE_TIME_CUR_DAY = 6000,			// 当天累计游戏时长
};
}

enum TOP_BATTLE_CONST
{
	MAX_RECORD_COUNT	= 4,			//最多保存4次战绩信息
};

enum GOD_EXPLORE_EVENT
{
	GEE_NONE			= 0,		// 无
	GEE_EMPLOY			= 1,		// 佣兵
	GEE_CROSS_LEVEL		= 2,		// 去下一层
	GEE_BLACK_BOSS		= 3,		// 黑市商人
	GEE_MONSTER			= 4,		// 神秘怪兽
};

namespace SYS_SPEAK
{
enum
{
	PLAYER_NAME,	//type + role_id + name
	NPC_NAME,	//type + npc_tid
	FACTION_NAME,	//type + faction_id + name
	NATION_NAME,	//type + nation_id
	NATION_OFFICER_NAME,	//type + officer_id
	RELATION_1,
	RELATION_2,
	INT_1,
	INT_2,
	SCENE_ID,
	PLAYER_NAME_2,	//type + role_id + name
	TITLE_NAME,	//type + title_id
	ACHIEVEMENT_NAME,//type + achieve_tid
	ITEM_ID,	//type + item_id
	ITEM,
	NPC_NAME_WITH_TITLE,
	PLAYER_POS,
	PLAYER_POS_NATION,//type + nation
	CHARIOT_NAME,    //type + chariot_tid
	USER_DATA,	//type + userdata
	RED_ENVELOPE_ID,	//type + int
	ITEM_ID_2,	//type + item_id
	RED_ENVELOPE_ID_2,	//type + int
	PLAYER_NAME_3,//type + role_id + name
	INT_3,
	IDPHOTO,	//type + short
	MIRROR_ID,	//int
	TEXIAO_ID,	//int
	SERVER_NAME,	//type + short
	CHILD_NAME,	//type + guid + name
	CHILD_NAME_2,	//type + guid + name
	PLAYER_NAME_WITH_FACTION,//type + role_id + faction + name
	PLAYER_NAME_2_WITH_FACTION,//type + role_id + faction + name
	FACTION_RECURIT,	// type + faction_id
	SHOW_FACTION,//type + faction
	LONGYU_ID, // type + longyu_id
	PLAYER_NAME_4,//type + role_id + name
	PLAYER_NAME_5,//type + role_id + name
	FACTION_NAME_2,	//type + faction_id + name
	REWARD_TID,     //int
	INSTANCE_ID,//type + 副本id
	LOTTERY_ID,//type + 彩票id
	APPEND_FACTION,	//type + faction_id
	APPEND_TEAM,	//type + teamid
	RETINUE,	//retinue_id + prop *9
	ENHANCE_PART,	//prof * 100 + part
	ENHANCE_LEVEL,
	IDPHOTO_2,	//type + int64
	ATTACKER_NAME,	//type + role_id + name
	CLONE_NAME,//type + role_id + name
	INT_4,
	SUIT_GROUP_ID,	//type + suit_group_id
	LONGYU_ID_2,	//type + longyu_id
	STRING_1, // string
	STRING_2, // string
	CHILD_COLLECTION, //type + collection_id
	SEVEN_CRIME_SWORD_ID,//int
	SEVEN_CRIME_SWORD_LEVEL,//int
	SEVEN_CRIME_TYPE_LEVEL,//int, type * 1000 + level
	// 炼金法阵
	CIRCLE_ELEMENT_ID,
	CIRCLE_POINT_ID,
	CIRCLE_POINT_LEVEL,
	RUNE_LOTTERY_ITEM_ID,
	TASK_ID,

	// ------------先占个位------------
	PLAYER_NAME_6,//type + role_id + name
	PLAYER_NAME_7,//type + role_id + name
	PLAYER_NAME_8,//type + role_id + name
	PLAYER_NAME_9,//type + role_id + name
	PLAYER_NAME_10,//type + role_id + name
	//

	MASK_PLAYER_NAME 	=	1ULL << PLAYER_NAME,
	MASK_NPC_NAME 		=	1ULL << NPC_NAME,
	MASK_FACTION_NAME 	=	1ULL << FACTION_NAME,
	MASK_NATION_NAME 	=	1ULL << NATION_NAME,
	MASK_NATION_OFFICER_NAME =	1ULL << NATION_OFFICER_NAME,
	MASK_RELATION_1 	=	1ULL << RELATION_1,
	MASK_RELATION_2		=	1ULL << RELATION_2,
	MASK_INT_1		=	1ULL << INT_1,
	MASK_INT_2		=	1ULL << INT_2,
	MASK_SCENE_ID		=	1ULL << SCENE_ID,
	MASK_PLAYER_NAME_2 	=	1ULL << PLAYER_NAME_2,
	MASK_TITLE_NAME 	=	1ULL << TITLE_NAME,
	MASK_ACHIEVEMENT_NAME 	=	1ULL << ACHIEVEMENT_NAME,
	MASK_ITEM_ID		=	1ULL << ITEM_ID,
	MASK_ITEM 		=	1ULL << ITEM,
	MASK_NPC_NAME_WITH_TITLE	=	1ULL << NPC_NAME_WITH_TITLE,
	MASK_PLAYER_POS		=	1ULL << PLAYER_POS,
	MASK_PLAYER_POS_NATION	=	1ULL << PLAYER_POS_NATION,
	MASK_CHARIOT_NAME	=       1ULL << CHARIOT_NAME,
	MASK_USER_DATA		= 	1ULL << USER_DATA,
	MASK_RED_ENVELOPE_ID	= 	1ULL << RED_ENVELOPE_ID,
	MASK_ITEM_ID_2		= 	1ULL << ITEM_ID_2,
	MASK_RED_ENVELOPE_ID_2	= 	1ULL << RED_ENVELOPE_ID_2,
	MASK_PLAYER_NAME_3	=	1ULL << PLAYER_NAME_3,
	MASK_INT_3		=	1ULL << INT_3,
	MASK_IDPHOTO		=	1ULL << IDPHOTO,
	MASK_MIRROR_ID		=	1ULL << MIRROR_ID,
	MASK_TEXIAO_ID		= 	1ULL << TEXIAO_ID,
	MASK_SERVER_NAME	=	1ULL << SERVER_NAME,
	MASK_CHILD_NAME		=	1ULL << CHILD_NAME,
	MASK_CHILD_NAME_2	=	1ULL << CHILD_NAME_2,
	MASK_PLAYER_NAME_WITH_FACTION = 1ULL << PLAYER_NAME_WITH_FACTION,
	MASK_PLAYER_NAME_2_WITH_FACTION = 1ULL << PLAYER_NAME_2_WITH_FACTION,
	MASK_FACTION_RECURIT	=	1ULL << FACTION_RECURIT,
	MASK_SHOW_FACTION		=	1ULL << SHOW_FACTION,
	MASK_LONGYU_ID			=	1ULL << LONGYU_ID,
	MASK_PLAYER_NAME_4		=	1ULL << PLAYER_NAME_4,
	MASK_PLAYER_NAME_5		=	1ULL << PLAYER_NAME_5,
	MASK_FACTION_NAME_2 		=	1ULL << FACTION_NAME_2,
	MASK_REWARD_TID			= 1ULL << REWARD_TID,
	MASK_INSTANCE_ID		=	1ULL << INSTANCE_ID,
	MASK_LOTTERY_ID		=	1ULL << LOTTERY_ID,
	MASK_APPEND_FACTION		= 1ULL << APPEND_FACTION,
	MASK_APPEND_TEAM		= 1ULL << APPEND_TEAM,
	MASK_RETINUE		=	1ULL << RETINUE,
	MASK_ENHANCE_PART	=	1ULL << ENHANCE_PART,
	MASK_ENHANCE_LEVEL	=	1ULL << ENHANCE_LEVEL,
	MASK_ATTACKER_NAME	=	1ULL << ATTACKER_NAME,
	MASK_CLONE_NAME	=	1ULL << CLONE_NAME,
	MASK_INT_4		=	1ULL << INT_4,
	MASK_SUIT_GROUP_ID	= 	1ULL << SUIT_GROUP_ID,
	MASK_IDPHOTO_2           =       1ULL << IDPHOTO_2,
	MASK_LONGYU_ID_2	=	1ULL << LONGYU_ID_2,
	MASK_STRING_1	=	1ULL << STRING_1,
	MASK_STRING_2	=	1ULL << STRING_2,
	MASK_CHILD_COLLECTION = 1ULL << CHILD_COLLECTION,
	MASK_SEVEN_CRIME_SWORD_ID = 1ULL << SEVEN_CRIME_SWORD_ID,
	MASK_SEVEN_CRIME_SWORD_LEVEL = 1ULL << SEVEN_CRIME_SWORD_LEVEL,
	MASK_SEVEN_CRIME_TYPE_LEVEL = 1ULL << SEVEN_CRIME_TYPE_LEVEL,
	// 炼金法阵
	MASK_CIRCLE_ELEMENT_ID = 1ULL << CIRCLE_ELEMENT_ID,
	MASK_CIRCLE_POINT_ID = 1ULL << CIRCLE_POINT_ID,
	MASK_CIRCLE_POINT_LEVEL = 1ULL << CIRCLE_POINT_LEVEL,
	MASK_RUNE_LOTTERY_ITEM_ID = 1ULL << RUNE_LOTTERY_ITEM_ID,
	MASK_TASK_ID = 1ULL << TASK_ID,

	/*
	PLAYER_POS,
	MONSTER_ENMITY,
	MONSTER_TARGET,
	MONSTER_POS,
	ECTYPE_CREATOR,
	ECTYPE_OWNER,
	ECTYPE_MEMBER,
	ECTYPE_ID,
	ITEM_LOOT_OWNER,
	ITEM_LOTTERY,
	ITEM_TASK,
	ITEM,
	SWORN_NICKNAME,
	PLAYER_NAME_2,
	PLAYER_NAME_3,
	PLAYER_NAME_4,
	ECTYPE_CREATOR_FACTION,
	ECTYPE_OWNER_FACTION,
	SCENE_ID,
	RELATION_1,
	RELATION_2,
	RELATION_3,
	RELATION_4,
	INT_1,
	INT_2,
	INT_3,
	INT_4,
	SERVER_NAME,
	WALL_ID,
	NATION_NAME,
	NATION_OFFICER_NAME,

	MASK_PLAYER_POS 	=	1ULL << PLAYER_POS,
	MASK_PLAYER_NAME =		1ULL << PLAYER_NAME,
	MASK_FACTION_NAME =		1ULL << FACTION_NAME,
	MASK_SWORN_NAME =		1ULL << SWORN_NAME,
	MASK_MASTER_NAME =		1ULL << MASTER_NAME,
	MASK_SPOUSE_NAME =		1ULL << SPOUSE_NAME,
	MASK_PLAYER_POS =		1ULL << PLAYER_POS,
	MASK_MONSTER_ENMITY =		1ULL << MONSTER_ENMITY,
	MASK_MONSTER_TARGET =		1ULL << MONSTER_TARGET,
	MASK_MONSTER_POS =		1ULL << MONSTER_POS,
	MASK_ECTYPE_CREATOR =		1ULL << ECTYPE_CREATOR,
	MASK_ECTYPE_OWNER =		1ULL << ECTYPE_OWNER,
	MASK_ECTYPE_MEMBER =		1ULL << ECTYPE_MEMBER,
	MASK_ECTYPE_ID =		1ULL << ECTYPE_ID,
	MASK_ITEM_LOOT_OWNER =		1ULL << ITEM_LOOT_OWNER,
	MASK_ITEM_LOTTERY =		1ULL << ITEM_LOTTERY,
	MASK_ITEM_TASK =		1ULL << ITEM_TASK,
	MASK_SWORN_NICKNAME =		1ULL << SWORN_NICKNAME,
	MASK_PLAYER_NAME_2 =		1ULL << PLAYER_NAME_2,
	MASK_PLAYER_NAME_3 =		1ULL << PLAYER_NAME_3,
	MASK_PLAYER_NAME_4 =		1ULL << PLAYER_NAME_4,
	MASK_ECTYPE_CREATOR_FACTION =	1ULL << ECTYPE_CREATOR_FACTION,
	MASK_ECTYPE_OWNER_FACTION =	1ULL << ECTYPE_OWNER_FACTION,
	MASK_SCENE_ID =			1ULL << SCENE_ID,
	MASK_RELATION_1 =		1ULL << RELATION_1,
	MASK_RELATION_2 =		1ULL << RELATION_2,
	MASK_RELATION_3 =		1ULL << RELATION_3,
	MASK_RELATION_4 =		1ULL << RELATION_4,
	MASK_INT_1 =			1ULL << INT_1,
	MASK_INT_2 =			1ULL << INT_2,
	MASK_INT_3 =			1ULL << INT_3,
	MASK_INT_4 =			1ULL << INT_4,
	MASK_SERVER_NAME =		1ULL << SERVER_NAME,
	MASK_WALL_ID =			1ULL << WALL_ID,
	MASK_NATION_NAME = 		1ULL << NATION_NAME,
	MASK_NATION_OFFICER_NAME = 	1ULL << NATION_OFFICER_NAME,
	*/
};

static std::map<std::string, uint64_t> map_wildcards =
{
	{"#player_name#", MASK_PLAYER_NAME }, //玩家名字
	{"#npc_name#", MASK_NPC_NAME }, //npc名字
	{"#faction_name#", MASK_FACTION_NAME}, //帮派名字
	{"#nation_name#", MASK_NATION_NAME}, //国家名
	{"#nation_officer_name#", MASK_NATION_OFFICER_NAME}, //国家官职名
	{"#relation_1#", MASK_RELATION_1}, //社会关系名
	{"#relation_2#", MASK_RELATION_2}, //社会关系名
	{"#int_1#", MASK_INT_1}, //整数
	{"#int_2#", MASK_INT_2}, //整数
	{"#scene_id#", MASK_SCENE_ID}, //场景
	{"#player_name_2#", MASK_PLAYER_NAME_2}, //玩家名字
	{"#title_name#", MASK_TITLE_NAME}, //称号名
	{"#achievement_name#", MASK_ACHIEVEMENT_NAME}, //成就名
	{"#item_id#", MASK_ITEM_ID}, //物品id
	{"#item#", MASK_ITEM}, //物品
	{"#npc_name_title#", MASK_NPC_NAME_WITH_TITLE}, //npc姓名加title
	{"#player_pos#", MASK_PLAYER_POS}, //玩家位置 坐标
	{"#player_pos_nation#", MASK_PLAYER_POS_NATION}, //玩家所在地属国
	{"#chariot_name#", MASK_CHARIOT_NAME}, //镖车信息
	{"#user_data#", MASK_USER_DATA}, //玩家信息
	{"#red_envelope_id#", MASK_RED_ENVELOPE_ID}, //红包ID
	{"#item_id_2#", MASK_ITEM_ID_2}, //物品id2
	{"#red_envelope_id_2#", MASK_RED_ENVELOPE_ID_2}, //红包ID2
	{"#player_name_3#", MASK_PLAYER_NAME_3}, //玩家名字3
	{"#int_3#", MASK_INT_3}, //整数3
	{"#idphoto#", MASK_IDPHOTO}, //头像
	{"#mirror_id#", MASK_MIRROR_ID}, //镜像id
	{"#texiao_id#", MASK_TEXIAO_ID}, //特效ID
	{"#server_name#", MASK_SERVER_NAME}, //服务器名字
	{"#child_name#", MASK_CHILD_NAME}, //孩子名字
	{"#child_name_2#", MASK_CHILD_NAME_2}, //其他人孩子名字
	{"#player_name_with_faction#", MASK_PLAYER_NAME_WITH_FACTION}, //玩家名字加阵营
	{"#player_name_2_with_faction#", MASK_PLAYER_NAME_2_WITH_FACTION}, //玩家名字加阵营
	{"#faction_recruit#", MASK_FACTION_RECURIT}, //社团招聘
	{"#show_faction#", MASK_SHOW_FACTION }, //喊话显示阵营
	{"#longyu_id#", MASK_LONGYU_ID }, //龙语喊话
	{"#reward_tid#", MASK_REWARD_TID}, //奖励模板id
	{"#instance_id#", MASK_INSTANCE_ID}, //副本id
	{"#lottery_id#", MASK_LOTTERY_ID}, //彩票id
	{"#append_faction#", MASK_APPEND_FACTION}, //附加帮派频道
	{"#append_team#", MASK_APPEND_TEAM}, //附加队伍频道
	{"#retinue#", MASK_RETINUE}, //伙伴数据
	{"#enhance_part#", MASK_ENHANCE_PART},
	{"#enhance_level#", MASK_ENHANCE_LEVEL},
	{"#attacker_name#", MASK_ATTACKER_NAME}, //攻击者名字
	{"#clone_name#", MASK_CLONE_NAME}, //玩家镜像名字
	{"#int_4#", MASK_INT_4}, //整数4
	{"#suit_group_id#", MASK_SUIT_GROUP_ID}, //套装ID
	{"#idphoto_2#", MASK_IDPHOTO_2}, //头像
	{"#longyu_id_2#", MASK_LONGYU_ID_2},	//龙语ID2
	{"#string_1#", MASK_STRING_1},	//龙语ID2
	{"#string_2#", MASK_STRING_2},	//龙语ID2
	{"#child_collection#", MASK_CHILD_COLLECTION}, //继承者纪念品
	{"#seven_crime_sword_id#", MASK_SEVEN_CRIME_SWORD_ID}, //七宗罪剑的id
	{"#seven_crime_sword_level#", MASK_SEVEN_CRIME_SWORD_LEVEL}, //七宗罪剑的等级
	{"#seven_crime_type_level#", MASK_SEVEN_CRIME_TYPE_LEVEL}, //七宗罪的类型等级
	// 炼金法阵
	{"#circle_element_id#", MASK_CIRCLE_ELEMENT_ID},
	{"#circle_point_id#", MASK_CIRCLE_POINT_ID},
	{"#circle_point_level#", MASK_CIRCLE_POINT_LEVEL},
	{"#rune_lottery_item_id#", MASK_RUNE_LOTTERY_ITEM_ID},
	{"#task_id#", MASK_TASK_ID}, //任务id
};
}

enum ADD_CASH
{
	//ADD_CASH_MAX		= 1000000,	//每次最多充值元宝数: 1m
	PENDING_ORDER_MAX	= 3,		//最多可以有几个未支付订单
};

enum ACTIVE_CODE_TYPE
{
	ACTIVE_CODE_TYPE_IOS_LOGIN	= 1,	//IOS设备登录游戏用的激活码
	ACTIVE_CODE_TYPE_ANDROID_LOGIN	= 2,	//ANDROID登录游戏用的激活码
};

enum NATION_OFFICER
{
	NATION_OFFICER_NONE		= 0,
	NATION_OFFICER_GUO_WANG		= 1,
	NATION_OFFICER_WANG_FEI		= 2,
	NATION_OFFICER_JIANG_JUN	= 3,
	NATION_OFFICER_TAI_SHI		= 4,
	NATION_OFFICER_TAI_WEI		= 5,
	NATION_OFFICER_CHENG_XIANG	= 6,
	NATION_OFFICER_WU_WEI_1		= 7,
	NATION_OFFICER_WU_WEI_2		= 8,
	NATION_OFFICER_YU_SHI_1		= 9,
	NATION_OFFICER_YU_SHI_2		= 10,
};
enum WAR_FIELD_MSG
{
	MSG_WAR_FIELD_PREPARE 	= 1,	//DS -> GS
	MSG_WAR_FIELD_BEGIN     = 2,    //DS -> GS
	MSG_WAR_FIELD_OPEN_OK   = 3,    //GS -> DS
	MSG_WAR_FIELD_FINISH    = 4,    //DS -> GS
	MSG_WAR_FIELD_RESULT    = 5,    //GS -> DS

	MSG_WAR_PLAYER_KILL_PLAYER = 10,	//GS -> DS
	MSG_WAR_NPC_KILL_PLAYER	= 11,	//GS -> DS
	MSG_WAR_NPC_DEATH 	= 12,    //GS -> DS
	MSG_WAR_REVIVE_MODIFY   = 13,    //DS -> GS
	MSG_WAR_PLAYER_ENTER 	= 14,    //GS -> DS
	MSG_WAR_PLAYER_LEAVE	= 15,    //GS -> DS
	MSG_WAR_PLAYER_REVIVE	= 16,	//GS -> DS
};

enum QUIT_BATTLE_TYPE
{
	QBT_C2S_BY_CLIENT 			= 0,	// 战队、队伍、上下线等，战场client发起的退出
	QBT_C2S_BY_PLAYER			= 1,	// 客户端主动发起的退出
	QBT_S2C_ENTER_BATTLE_FAILED	= 2,	// 战场server进入失败
	QBT_S2C_REPLY_C_QUIT		= 3,	// 战场server回应client发起的退出
	QBT_S2C_DS_CHECK_FAILED		= 4,	// 战场server跨服跳转，DS检测失败
};


//通用数据表key前缀名定义表
//军团用数据表
#define 	CORPS_COUNTER		"corps_counter"
#define		CORPS_TABLE_NAME	"corps"
#define		CORPS_MEM_TABLE_NAME	"corps_member"
#define		USER_MIDAS_FAIL		"midas"
#define		PB_TOP_REWARD		"top_reward"

//帮派竞赛前缀名定义表
#define		CORPS_BATTLE_ORDER	"corps_battle_order"
#define 	CORPS_BATTLE_INFO	"corps_battle_info"
#define 	CORPS_BATTLE_CONFIG	"corps_battle_config"

namespace DBOP
{
#define CREATE_INFO_MAP(infomap)				\
	class infomap						\
	{							\
		std::map<int, std::string> _map;		\
		public:						\
		infomap() {}					\
		static infomap& Instance()			\
		{						\
			static infomap _instance;		\
			return _instance;			\
		}						\
		std::string Insert(int value, const std::string& name)	\
		{						\
			_map[value] = name;			\
			return name;				\
		} 						\
		const std::string& GetInfo(int value) const	\
		{						\
			static std::string nulls("unkown");	\
			std::map<int, std::string>::const_iterator it = _map.find(value);	\
			if(it == _map.end()) return nulls;	\
			return it->second;			\
		}						\
	};

CREATE_INFO_MAP(op_map)
#define INSERT_COUNT(name, value, info)	\
	const int name = value;	\
	const std::string INFO_##name = op_map::Instance().Insert(value, std::string("[") + #name "]");
//	const std::string INFO_##name = op_map::Instance().Insert(value, std::string("[") + #name + "|" + std::string(info) + "]");

INSERT_COUNT(DBSTRUCT_CREATE,	0,	"create data/hash");
INSERT_COUNT(DBSTRUCT_SET,	1,	"set data");
INSERT_COUNT(DBSTRUCT_GET,	2,	"get data");
INSERT_COUNT(DBSTRUCT_DEL,	3,	"删除data/hash");
INSERT_COUNT(DBSTRUCT_H_GETALL,	4,	"get hash");
INSERT_COUNT(DBSTRUCT_H_SETONE,	5,	"hash写单个数据");
INSERT_COUNT(DBSTRUCT_H_GETONE,	6,	"hash获取单个数据");
INSERT_COUNT(DBSTRUCT_H_COUNT,	7,	"hash数据个数");
INSERT_COUNT(DBSTRUCT_INC,	8,	"value_type = 0, int64_t inc");
INSERT_COUNT(DBSTRUCT_H_INSERT,	9,	"insert one which mustnot exist");
INSERT_COUNT(DBSTRUCT_H_EXIST,	10,	"if not exist throw exception");
INSERT_COUNT(DBSTRUCT_FACEBOOK,	11,	"replace facebook->protocbuf");
INSERT_COUNT(DBSTRUCT_CREATE_WITH_INCID,	12,	"create with table id by DBSTRUCT_INC 0_xxx or zoneid_xx");
INSERT_COUNT(DBSTRUCT_H_DELONE,	13,	"hash数据单个删除");
INSERT_COUNT(DBSTRUCT_SHOW,	14,	"列出一个表的所有key");
INSERT_COUNT(DBSTRUCT_FACEBOOK_TEST,		15,	"检查指定玩家社会关系是否如输入，如果不一样整个操作回滚");

#undef	INSERT_COUNT
#define DBSTRUCTOP_INFO(value) \
	DBOP::op_map::Instance().GetInfo(value).c_str()

CREATE_INFO_MAP(error_map)
#define INSERT_COUNT(name, value, info)	\
	const int name = value;	\
	const std::string INFO_##name = error_map::Instance().Insert(value, std::string(#name));
//	const std::string INFO_##name = error_map::Instance().Insert(value, std::string(#name)+"[" + info + "]");

INSERT_COUNT(CDB_ERR_KEY_INVALID,	1001,	"invalid key");
INSERT_COUNT(CDB_ERR_DIS_CONNECT,	1002,	"db disconnect");
INSERT_COUNT(CDB_ERR_DB_TIMEOUT,	1003,	"db timeout");
INSERT_COUNT(CDB_ERR_NO_VALUE,		1004,	"value not set");
INSERT_COUNT(CDB_ERR_PROTOC_SERIALIZE,	1005,	"protocbuf serialize error");
INSERT_COUNT(CDB_ERR_CREATE_DUP,	1006,	"key create is exist");
INSERT_COUNT(CDB_ERR_NO_KEY,		1007,	"key dels not exist");
INSERT_COUNT(CDB_ERR_NOT_SUPPORT_OP,	1008,	"opration not support");
INSERT_COUNT(CDB_ERR_INCONSIS_TYPE,	1009,	"value type inconsistent");
INSERT_COUNT(CDB_ERR_GP_MSG_INVALID,	1010,	"invalid googleprotoc message");
INSERT_COUNT(CDB_ERR_OP_INVALID,	1011,	"invalid db operation");
INSERT_COUNT(CDB_SUCCESS,		0,	"success");

#define DBSTRUCTERR_INFO(value) \
	DBOP::error_map::Instance().GetInfo(value).c_str()

#undef	INSERT_COUNT
};

class CommonDBError
{
	int err;
public:
	CommonDBError() : err(0) {}
	void SetError(int value)
	{
		err = value;
	}
	const int GetError() const
	{
		return err;
	}
	const std::string& GetInfo() const
	{
		return DBOP::error_map::Instance().GetInfo(err);
	}
};

enum SEARCH_TYPE
{
	SEAT_NORMAL             = 0,    //通用搜索
	SEAT_FACITON            = 1,    //帮派搜索
	SEAT_PLAYER		= 2,	//玩家搜索

	SEAT_COUNT,                     //搜索数量
};

enum GLOBAL_DATA_BROCADCAST_TYPE
{
	GDBT_GLOBAL_TASK_ITEM_1 = 1,
	GDBT_GLOBAL_TASK_ITEM_2,
	GDBT_GLOBAL_TASK_ITEM_3,
	GDBT_GLOBAL_TASK_ITEM_4,
	GDBT_GLOBAL_TASK_ITEM_5,
	GDBT_GLOBAL_TASK_ITEM_6,
	GDBT_GLOBAL_TASK_ITEM_7,
	GDBT_GLOBAL_TASK_ITEM_8,
	GDBT_GLOBAL_TASK_ITEM_9,
	GDBT_GLOBAL_TASK_ITEM_10,

	GDBT_GLOBAL_PRIZE_1 = 21,

	GDBT_GLOBAL_TASK_ITEM_10001 = 10001,
	GDBT_GLOBAL_TASK_ITEM_20000 = 20000,
};

enum GLOBAL_DATA_FAIR_TYPE
{
	GDFT_PROF1 = 901,
	//...
	GDFT_PROF12 = 912,
};

enum INTERPROCESS_SERVER_GLOBAL_DATA_INDEX
{
	ISGD_FAUCTION		= 0,	//假拍卖行数据
	ISGD_CENTER_FOREVER_SERVICE	= 1,	//中心服服务数据
	ISGD_CENTER_ARENA_TEAM_SERVER = 2,	//中心服组队竞技场数据
	ISGD_CENTER_ARENA_GROUP_RANK_MIN = 3,	//中心服战队排行榜开始
	ISGD_CENTER_ARENA_GROUP_RANK_MAX = 102,	//中心服战队排行榜结束
	ISGD_CENTER_ARENA_GROUP_TIMESTAMP = 103,	//中心服战队排行榜刷新时间戳
	ISGD_CENTER_ARENA_GROUP_RANK_CLIENT = 104,	//本期的战队排名
	ISGD_CENTER_ARENA_GROUP_RANK_CLIENT_LAST = 105,	//上期的战队排名
	ISGD_CENTER_ARENA_GROUP_ZONE_LAST = 106,	//上期的所属战区
	ISGD_CENTER_ARENA_GROUP_STATUS	= 107,	//上期的榜首战队成员信息
	ISGD_CENTER_ARENA_GROUP_ZONE = 108,	//本期的所属战区
	ISGD_TOPSTAR = 109,	//明星应援
	ISGD_LIMITLOTTERY = 110, // 限时抽奖
	ISGD_ELIMINATE_BATTLE_STATE  = 111, //淘汰赛战场状态
	ISGD_ELIMINATE_BATTLE_RANK   = 112, //本服淘汰赛排行榜
	ISGD_CORPS_SEASON_INDEX      = 113, //社团赛季赛季索引
	ISGD_CORPS_SEASON_LOCAL_RANK = 114, //社团赛季本地排行榜
	ISGD_CORPS_SEASON_ROAM_RANK  = 115, //社团赛季跨服排行榜
	ISGD_TEMPORART_TEAM_VERSION = 116, //考核小队版本号
	ISGD_PLEASURE = 117, // 趣味夺宝
	ISGD_GOD_EXPLORE_LEVEL = 118, // 神迹探索层
	ISGD_GOD_EXPLORE_BOSS = 119, // 神迹探索boss
	ISGD_HUNDRED_CORPS_BATTLE_STATE = 120, // 百团大战状态
};

// 活动彩票声望，不要和top_mail_rank_reward_conf.lua中activity_ticket_repu冲突
enum ACTIVITY_TICKET_VALUE_KEY
{
	ATVK_NONE				= 0,
	ATVK_BEGIN				= 0,
	ATVK_DRAGON_HOUSE		= 1,	// 巨龙宝库
	ATVK_VIDEO_GAME			= 2,	// 绘梨衣的游戏机
	ATVK_HEAVEN				= 3,	// 夏妮尔的游乐场
	ATVK_PLEASURE           = 4,    // 趣味夺宝
	ATVK_SPRING             = 5,    // 趣味夺宝
	ATVK_FASHION_DRESS		= 6,	// 时装设计大赛
	ATVK_KING_HOUSE			= 7,	// 王之宝库
	ATVK_GOD_EXPLORE		= 8,	// 神迹探险
	ATVK_GATHER_WORD		= 9,	// 集字
	ATVK_TREASURE_TAG		= 10,	// 宝签
	ATVK_PROMOTE_SALES		= 11,	// 促销
	ATVK_TOP_STAR			= 12,	// 应援
	ATVK_PERSONAL_TARGET	= 13,	// 个人目标
	ATVK_RECHARGE_CONSUME	= 14,	// 充值消费
	ATVK_LOTTERY_SEA		= 15,	// 星海密藏
	ATVK_LOTTERY_SHIP		= 16,
	ATVK_SELF_SELECT_REWARD	= 17,
	ATVK_ROAM_COMMUNITY_SEASON = 18,
	ATVK_QXQY				= 19,	// 千寻奇遇

	ATVK_DS_TOP_MAIL_RANK_REWARD_BEGIN	= 4000,
	ATVK_DS_TOP_MAIL_RANK_REWARD_END	= 5000,
	ATVK_TOP_MAIL_RANK_REWARD_NO_TOP_BEGIN	= 14000,
	ATVK_TOP_MAIL_RANK_REWARD_NO_TOP_END	= 14999,
	ATVK_TOP_MAIL_RANK_REWARD_BEGIN	= 15000,
	ATVK_TOP_MAIL_RANK_REWARD_END	= 17000,
	ATVK_COUNT,
};

// 彩票声望版本的key
enum TICKET_REPU_VERSION_KEY
{
	TRVK_NONE				= 0,
	TRVK_BEGIN				= 1,
	TRVK_TREASURE			= 1,	// 星辰阶梯
	TRVK_DRAGON_HOUSE		= 2,	// 巨龙宝库
	TRVK_COUNT,
};
static bool IsValidTicketRepuVersionKey(int key)
{
	return (key >= TRVK_BEGIN && key < TRVK_COUNT);
}

enum FASHION_DRESS_PERIOD : int
{
	FDP_NONE					= 0,
	FDP_LOCAL_1_ONGOING			= 100,
	FDP_LOCAL_1_END,
	FDP_LOCAL_2_ONGOING			= 200,
	FDP_LOCAL_2_END,
	FDP_ROAM_1_1_ONGOING		= 300,
	FDP_ROAM_1_1_END,
	FDP_ROAM_1_2_ONGOING,
	FDP_ROAM_1_2_END,
	FDP_ROAM_1_3_ONGOING,
	FDP_ROAM_1_3_END,
	FDP_ROAM_1_4_ONGOING,
	FDP_ROAM_1_4_END,
	FDP_ROAM_1_5_ONGOING,
	FDP_ROAM_1_5_END,
	FDP_ROAM_1_6_ONGOING,
	FDP_ROAM_1_6_END,
	FDP_ROAM_1_7_ONGOING,
	FDP_ROAM_1_7_END,
	FDP_ROAM_1_8_ONGOING,
	FDP_ROAM_1_8_END,
	FDP_ROAM_1_9_ONGOING,
	FDP_ROAM_1_9_END,
	FDP_ROAM_1_10_ONGOING,
	FDP_ROAM_1_10_END,
	FDP_ROAM_2_ONGOING			= 400,
	FDP_ROAM_2_END,
	FDP_END						= 500,

	FDP_ROAM_1_PERIOD_COUNT_MAX = 10,			// 跨服1阶段最大阶段数量
};

static bool IsValidFashionDressPeriod(int period)
{
	return ((period >= FDP_LOCAL_1_ONGOING && period <= FDP_LOCAL_1_END)
	        || (period >= FDP_LOCAL_2_ONGOING && period <= FDP_LOCAL_2_END)
	        || (period >= FDP_ROAM_1_1_ONGOING && period <= FDP_ROAM_1_10_END)
	        || (period >= FDP_ROAM_2_ONGOING && period <= FDP_ROAM_2_END));
}

static FASHION_DRESS_PERIOD GetBeginPeriodByPeriod(int period_index)
{
	return (FASHION_DRESS_PERIOD)(FDP_ROAM_1_1_ONGOING + (period_index - 1) * 2);
}

static bool IsRoam1EliminateOngoing(FASHION_DRESS_PERIOD period)
{
	return ((period >= FDP_ROAM_1_1_ONGOING && period <= FDP_ROAM_1_10_END) && period % 2 == 0);
}

static bool IsRoam1EliminateEnd(FASHION_DRESS_PERIOD period)
{
	return ((period >= FDP_ROAM_1_1_ONGOING && period <= FDP_ROAM_1_10_END) && period % 2 == 1);
}

static int GetRoam1RoundByPeriod(FASHION_DRESS_PERIOD period)
{
	if (period >= FDP_ROAM_1_1_ONGOING && period <= FDP_ROAM_1_10_END)
	{
		if (period % 2 == 0)
		{
			// 阶段开始时，round=0
			return ((period - FDP_ROAM_1_1_ONGOING) / 2);
		}
		else
		{
			// 阶段结束时，round=1
			return ((period - FDP_ROAM_1_1_ONGOING) / 2 + 1);
		}
	}

	return 0;
}

// 时装搭配大赛的key
enum FASHION_DRESS_KEY
{
	FDK_PERIOD				= 0,		// 阶段，FASHION_DRESS_PERIOD
	FDK_ATMOSPHERE			= 1,		// 氛围值
	FDK_GAMBLE				= 2,		// 竞猜
};

enum INTERPROCESS_GLOBAL_DATA_KEY_TYPE
{
	IGKT_DEFAULT		= 0,
	IGKT_SCENE_WEATHER	= 1, //场景天气数据
	IGKT_SCENE_TEMPERATURE	= 2, //场景温度
	IGKT_SCENE_HUMIDITY	= 3, //场景湿度
	IGKT_SCENE_USEDEAFULT_TEMPERATURE_HUMIDITY = 4, //是否使用默认温度湿度配置
	IGKT_SERVER_DATA = 5,	//服务器使用的数据
	IGKT_SIMPLE_STOCK	= 6,
	IGKT_INTIMATE_PARTY = 7, //羁绊派对
	IGKT_INTIMATE_PARTY2 = 8, //羁绊派对
	IGKT_INTIMATE_PARTY3 = 9, //羁绊派对
	IGKT_INTIMATE_PARTY4 = 10, //羁绊派对
	IGKT_INTIMATE_PARTY5 = 11, //羁绊派对
	IGKT_QQ_PUSH_DATA = 12, //qq推送存盘数据
	IGKT_DIAOXIANG		= 13,	//雕像数据
	IGKT_COMMUNITY   = 14,  // 社区
	IGKT_TICKET_REPU_VERSION = 15,	// 彩票声望版本
	IGKT_DIAOXIANG_EDIT = 16,   //雕像编辑数据
	IGKT_DIAOXIANG_MAP  = 17,   //玩家id与雕像索引的映射关系
	IGKT_WEDDING_PARTY  = 18,   //婚礼
	IGKT_WEDDING_PARTY2 = 19, //婚礼
	IGKT_WEDDING_PARTY3 = 20, //婚礼
	IGKT_WEDDING_PARTY4 = 21, //婚礼
	IGKT_WEDDING_PARTY5 = 22, //婚礼
	IGKT_FASHION_DRESS	= 23, //时装搭配大赛
	IGKT_CORPS_TARGET_WEEKLY_REPUS	= 24,	// 社团目标每周声望
	IGKT_SCRAWL_PASTED	= 25, //墙面涂鸦
	IGKT_SINGLE_BLESS_WALL	= 26, //单人祝福墙
	IGKT_LMFSHOP = 27, //新商业化活动
	IGKT_CONVERT_DISNEY_CARDS_NUM = 28, //日服招募迪士尼卡全服兑换次数
	IGKT_FISHING_RECORD	= 29, //钓鱼排行榜
	IGKT_LOTTERY_MACHINE_MONEY	= 30, //街头游戏机彩票全服钻石奖池
	IGKT_TURNTABLE_ATTRIBUTE	= 31, //轮盘彩票全服贡献值
	IGKT_SHARE_BOX				= 32, //宝箱彩票分享的宝箱
	IGKT_CASKET					= 33, //缘金绮匣
	IGKT_MAGIC_GATHERING_GLOBAL_PROGRESS = 34, //魔力汇聚全服进度
	IGKT_POP_FACE = 35, // 拍脸数据
};
#define IP_GLOBAL_DATA_KEY(type, key) ((int)(type) << 24 | (key))
#define GET_IP_GLOBAL_DATA_KEY_TYPE(key) (key >> 24 & 0xFFF)

const int NEED_SAVE_GLOBAL_DATA_ID[] = {IP_GLOBAL_DATA_KEY(IGKT_SIMPLE_STOCK, 0), IP_GLOBAL_DATA_KEY(IGKT_QQ_PUSH_DATA, 0)};
const int NEED_SEND_CLIENT_GLOBAL_DATA_ID[] = {};

inline bool ip_global_data_need_save(int id)
{
	static const std::set<int> need_save_set(NEED_SAVE_GLOBAL_DATA_ID, NEED_SAVE_GLOBAL_DATA_ID + sizeof(NEED_SAVE_GLOBAL_DATA_ID) / sizeof(int));
	if (need_save_set.find(id) != need_save_set.end())
	{
		return true;
	}
	//因为下边IGKT_COMMUNITY的bug，所以现在全改成了true
	return true;
	/*int key_type = id >> 24;
	return (key_type == IGKT_DEFAULT
	        || key_type == IGKT_SERVER_DATA
	        || key_type == IGKT_DIAOXIANG
	        || key_type == IGKT_TICKET_REPU_VERSION
	        || key_type == IGKT_FASHION_DRESS
	        || key_type == IGKT_SCRAWL_PASTED
	        || IGKT_COMMUNITY );*/
}

inline bool ip_global_data_need_send_client(int id)
{
	if (id >= GDBT_GLOBAL_TASK_ITEM_1 && id <= GDBT_GLOBAL_TASK_ITEM_10)
	{
		return true;
	}
	if (id >= GDBT_GLOBAL_TASK_ITEM_10001 && id <= GDBT_GLOBAL_TASK_ITEM_20000)
	{
		return true;
	}
	static const std::set<int> need_send_client_set(NEED_SEND_CLIENT_GLOBAL_DATA_ID, NEED_SEND_CLIENT_GLOBAL_DATA_ID + sizeof(NEED_SEND_CLIENT_GLOBAL_DATA_ID) / sizeof(int));
	if (need_send_client_set.find(id) != need_send_client_set.end())
	{
		return true;
	}
	int key_type = id >> 24;
	if (key_type == IGKT_MAGIC_GATHERING_GLOBAL_PROGRESS && (id & 0xFF) == 1)
	{
		return true;
	}

	return (key_type == IGKT_INTIMATE_PARTY ||
	        key_type == IGKT_INTIMATE_PARTY2 ||
	        key_type == IGKT_INTIMATE_PARTY3 ||
	        key_type == IGKT_INTIMATE_PARTY4 ||
	        key_type == IGKT_INTIMATE_PARTY5 ||
	        key_type == IGKT_WEDDING_PARTY ||
	        key_type == IGKT_WEDDING_PARTY2 ||
	        key_type == IGKT_WEDDING_PARTY3 ||
	        key_type == IGKT_WEDDING_PARTY4 ||
	        key_type == IGKT_WEDDING_PARTY5 ||
	        key_type == IGKT_FASHION_DRESS ||
			key_type == IGKT_TURNTABLE_ATTRIBUTE ||
			key_type == IGKT_LOTTERY_MACHINE_MONEY ||
			//key_type == IGKT_MAGIC_GATHERING_GLOBAL_PROGRESS||
			key_type == IGKT_CASKET
	       );

}

/*inline int CanTransferReputation(int repu_id, int transfer_value, int cur_value, int target_cur_value)
{
	static std::map<int, std::vector<int>> transfer_reputations = {
		//{声望id，{{每次最多转移多少点，声望上限}}
		{GNET::REPUID_REPUTATION_TRANSFER_1, {1, 99999}},
		{GNET::REPUID_REPUTATION_TRANSFER_2, {1, 99999}},
		{GNET::REPUID_REPUTATION_TRANSFER_3, {1, 99999}},
		{GNET::REPUID_REPUTATION_TRANSFER_4, {1, 99999}},
		{GNET::REPUID_REPUTATION_TRANSFER_5, {1, 99999}},
		{GNET::REPUID_REPUTATION_TRANSFER_6, {1, 99999}},
	};

	auto it = transfer_reputations.find(repu_id);
	if (it == transfer_reputations.end())
	{
		return GNET::ERROR_REPUTATION_TRANSFER_WRONG;
	}
	auto &r = it->second;
	if (r.size() != 2)
	{
		return GNET::ERROR_REPUTATION_TRANSFER_WRONG;
	}
	if (transfer_value <= 0 || transfer_value > r[0])
	{
		return GNET::ERROR_REPUTATION_TRANSFER_WRONG;
	}
	if (cur_value < transfer_value)
	{
		return GNET::ERROR_REPUTATION_TRANSFER_NOT_ENOUGH;
	}
	if (target_cur_value + transfer_value < 0 || target_cur_value + transfer_value> r[1])
	{
		return GNET::ERROR_REPUTATION_TRANSFER_MAX;
	}
	return ERROR_SUCCESS;
}*/

//#define DAY_SECOND 86400
//#define WEEK_SECOND DAY_SECOND*7
#define NATION_WAR_BEGIN_DIFF (20 * 3600 - 300)

//目前只有一个国家
#define MIRROR_ID_MASK		0xFF
#define INVALID_MIRROR_ID	0xFF
#define NATION_COUNT		1
#define MIRROR_NATION_COUNT 	1
#define NATION_ID_BEGIN		1
#define NATION_ID_END		1
//1 -6 正常国家 7是中立区
/*#define NATION_ID_BEGIN		1
#define NATION_ID_END		1
#define NATION_COUNT		1
#define	MIRROR_NATION_MASK	0x80
#define MIRROR_ID_MASK		0xfF
#define NAUTRAL_NATION		2	//2为中立区国家
#define MIRROR_NATION_COUNT	1
//根据baseid计算 mid
#define NATION_MIRROR_ID(nation, mirror_id)	\
	(((((size_t)(nation) - 1) % MIRROR_NATION_COUNT) << 7) | ( (mirror_id) & MIRROR_ID_MASK))*/
#define NATION_MIRROR_ID(nation, mirror_id)	 mirror_id

//mirror -> nationid
/*#define MIRROR_TO_NATION(id) \
	((int)((((size_t)id) & MIRROR_NATION_MASK) >> 7) % MIRROR_NATION_COUNT + 1)
#define VALID_NATION_ID(id) \
	(id >= NATION_ID_BEGIN && id <= NATION_ID_END)*/
#define MIRROR_TO_NATION(id)  1
#define VALID_NATION_ID(id) ((id) == 1)
//#define LINE_MAX_COUNT		6

class SpecailSceneMIDGenerator
{
	bool close_write;
	std::map<int, int> _scene_nation;
	SpecailSceneMIDGenerator() : close_write(false) {}
public:
	static SpecailSceneMIDGenerator& Instance()
	{
		static SpecailSceneMIDGenerator _instance;
		return _instance;
	}
	//开线程之后注意关闭
	void Closur()
	{
		close_write = true;
	}
	void Insert(int scene, int nation)
	{
		assert(close_write == false);
		_scene_nation[scene] = nation;
	}
	int Nation(int scene)
	{
		if (_scene_nation.size() == 0)
		{
			return 0;
		}
		std::map<int, int>::iterator it = _scene_nation.find(scene);
		if (it == _scene_nation.end())
		{
			return 0;
		}
		return it->second % MIRROR_NATION_COUNT;
	}
};

#define SP_NATION(scene_tag, lineid) \
	(SpecailSceneMIDGenerator::Instance().Nation(scene_tag))

//tag + lineid 计算 应该是哪个nation的 主镜像id
/*#define MASTER_MIRROR_NATION(scene_tag, lineid) \
	( SP_NATION((scene_tag), (lineid)) ? SP_NATION((scene_tag), (lineid)) : ((((scene_tag) + (lineid)) % NATION_COUNT) + 1))*/
//#define MASTER_MIRROR_NATION(scene_tag, lineid) ((((scene_tag) + (lineid)) % (LINE_MAX_COUNT)) == 0)

//tag + lineid 计算 应该是哪个nation的 主镜像id
//内服测试版本每条线一个国家 -- 改为 测试版本 只有 1 2 3跳线 1 2 3 三个国家
/*#define GS_DEBUG_MASTER_MIRROR_NATION(scene_tag, lineid) \
	(SP_NATION((scene_tag), (lineid)) ? SP_NATION((scene_tag), (lineid)) : ((lineid) <= 3 ? ((((scene_tag) + (lineid)) % 3) + 1) : ((((scene_tag) + (lineid)) % 3) + 4)))*/

//mirror -> baseid
/*#define MIRROR_BASEID(mid)	\
	( (mid) & MIRROR_ID_MASK)*/
#define MIRROR_BASEID(mid)	mid

//国家随机选择镜像
/*#define NATION_DEFAULT_MIRROR(nation)	\
	(NATION_MIRROR_ID((nation), MIRROR_ID_MASK))*/
#define NATION_DEFAULT_MIRROR(nation)	INVALID_MIRROR_ID

#define SET_ONE_BIT(mask)	\
	((mask) && (!((mask) & ((mask) - 1))))

#define IS_DEFAULT_NATION_MIRROR(mid) 	\
	(((mid) & MIRROR_ID_MASK) == MIRROR_ID_MASK)
//(((mid) & MIRROR_ID_MASK) == MIRROR_ID_MASK && mid != INVALID_MIRROR_ID)


#define NATION_TO_FACTION_MASK_PLAYER(nation) \
	(1 << ( (nation - 1) % NATION_COUNT ) )
#define NATION_TO_FACTION_MASK_NPC(nation) \
	(1 << ( (nation - 1) % NATION_COUNT + 6 ) )



inline int FACTION_MASK_TO_NATION_ID(int mask)
{
	for (int i = 0; i < NATION_COUNT; ++i)
	{
		if (mask & (0x41 << i))
		{
			return i + 1;
		}
	}
	return 0;
}

// 计算UTF8字符数，不做UTF8编码合法性校验
inline size_t strlen_utf8(const char *s, size_t n)
{
	size_t len = 0;
	for (size_t i = 0; i < n && s[i]; i++)
	{
		if ((s[i] & 0xC0) != 0x80)
		{
			len++;
		}
	}
	return len;
}

inline GNET::Octets GetAccountID(const GNET::Octets& account)
{
	std::string account_str((char *)account.begin(), account.size());
	size_t pos = account_str.find_last_of('@');
	if (pos != std::string::npos)
	{
		return GNET::Octets(account_str.c_str(), pos);
	}
	return account;
}

inline bool GetOpenPlat(const GNET::Octets& account_in, GNET::Octets& plat)
{
	GNET::Octets account;
	DetachZoneidAndAccount( account_in, account );

	std::string account_str((char *)account.begin(), account.size());
	size_t pos = account_str.find_last_of('$');
	if (pos != std::string::npos)
	{
		int offset = static_cast<int>(pos) + 1;
		size_t sysplat_pos = account_str.find_first_of("#", pos);
		if (sysplat_pos != std::string::npos && (sysplat_pos > pos + 1) )
		{
			// 新账号形式
			plat = GNET::Octets((char *)account.begin() + offset, sysplat_pos - pos - 1);
		}
		else
		{
			// 老账号形式
			if ((size_t)offset < account.size())
			{
				plat = GNET::Octets((char *)account.begin() + offset, account.size() - offset);
			}
		}

		return true;
	}
	return false;
}

inline void GetOpenid(const std::string& account, std::string& str_openid)
{
	size_t pos = account.find_last_of('$');
	if (pos != std::string::npos)
	{
		str_openid = std::string(account.c_str(), pos);
		return;
	}
	str_openid = account;
}

inline GNET::Octets GetOpenid(const GNET::Octets& account)
{
	std::string account_str((char *)account.begin(), account.size());
	size_t pos = account_str.find_last_of('$');
	if (pos != std::string::npos)
	{
		return GNET::Octets(account_str.c_str(), pos);
	}
	return account;
}

inline std::string GetOpenid(const std::string& account)
{
	std::string str_openid;
	GetOpenid(account, str_openid);
	return str_openid;
}

inline void GetOpenid(const GNET::Octets& account, std::string& str_openid)
{
	std::string account_str((char *)account.begin(), account.size());
	GetOpenid(account_str, str_openid);
}

/*inline GNET::Octets Openid2Account(const GNET::Octets & openid, int areaid)
{
	if ( areaid < 1 || areaid > 10 )
		return openid;
	static const char * sub_fix[4] = {"$wechat", "$qq", "$qq", "$zulong"};
	--areaid;
	const char * sub = sub_fix[areaid%4];
	GNET::Octets account = openid;
	account.insert(account.end(), sub, strlen(sub));
	return account;
}*/

inline unsigned char GetOpenPlat(const GNET::Octets& plat)
{
	std::string str_plat((char *)plat.begin(), plat.size());

	static const char *sub_fix[4] = {"wechat", "qq", "qq", "zulong"};
	for (size_t i = 0; i < 4; ++i)
	{
		if ( strncmp(str_plat.c_str(), sub_fix[i], std::max( str_plat.size(), strlen(sub_fix[i]) ) ) == 0)
		{
			return i;
		}
	}
	return 0;
}

inline void timestamp_to_string(int utc_time, std::string& time_str)
{
	struct tm l_tm;
	time_t u_time = utc_time;
	localtime_r(&u_time, &l_tm);
	char buf[128] = "";
	sprintf(buf, "%d-%d-%d %d:%d:%d", l_tm.tm_year + 1900, l_tm.tm_mon + 1, l_tm.tm_mday, l_tm.tm_hour, l_tm.tm_min, l_tm.tm_sec);
	time_str = buf;
}

inline void DetachPlat(const GNET::Octets& inAccount, GNET::Octets& outAccount)
{
	outAccount.clear();
	std::string account_str( (const char *)inAccount.begin(), inAccount.size() );
	size_t pos = account_str.find_last_of('$');
	if (pos != std::string::npos)
	{
		int offset = static_cast<int>(pos);
		if ( offset < 1 || offset > (int)inAccount.size())
		{
			outAccount = inAccount;
			return;
		}
		outAccount.replace(inAccount.begin(), offset);
		return;
	}
	else
	{
		outAccount = inAccount;
	}
}

inline std::string GetPlat(const GNET::Octets& account)
{
	std::string plat = "";
	std::string account_str( (const char *)account.begin(), account.size() );
	size_t plat_pos = account_str.find_last_of('$');
	size_t zone_pos = account_str.find_last_of('@');
	if ( plat_pos != std::string::npos && zone_pos != std::string::npos && (zone_pos > plat_pos + 1) )
	{
		size_t sysplat_pos = account_str.find_first_of("#", plat_pos);
		if ( sysplat_pos != std::string::npos && (sysplat_pos > plat_pos + 1) && (sysplat_pos + 1 < zone_pos) )
		{
			// 新的账号模式
			plat = account_str.substr(plat_pos + 1, sysplat_pos - plat_pos - 1);
		}
		else
		{
			plat = account_str.substr(plat_pos + 1, zone_pos - plat_pos - 1);
		}
	}
	return plat;
}

// 获取系统平台: ios/android/pc
inline std::string GetAccountSysPlat(const GNET::Octets& account)
{
	std::string sys_plat = "";
	std::string account_str( (const char *)account.begin(), account.size() );
	size_t plat_pos = account_str.find_last_of('$');
	size_t zone_pos = account_str.find_last_of('@');
	if ( plat_pos != std::string::npos && zone_pos != std::string::npos && (zone_pos > plat_pos + 1) )
	{
		size_t sysplat_pos = account_str.find_first_of("#", plat_pos);
		if ( sysplat_pos != std::string::npos && (sysplat_pos > plat_pos + 1) && (sysplat_pos + 1 < zone_pos) )
		{
			// 新的账号模式
			sys_plat = account_str.substr(sysplat_pos + 1, zone_pos - sysplat_pos - 1);
		}
	}
	return sys_plat;
}

// 获取账号中的系统平台
// 0: ios, 1: android -1: pc
inline int GetAccountPlat(const GNET::Octets& account)
{
	int ret = GNET::DEVICE_OS_UNKNOWN;
	std::string sysplat = GetAccountSysPlat(account);
	if (sysplat == "ios")
	{
		ret = GNET::DEVICE_OS_IOS;
	}
	else if (sysplat == "android")
	{
		ret = GNET::DEVICE_OS_ANDROID;
	}
	return ret;
}
//
inline bool IsOpenPlat(const std::string& plat_str)
{
	if (plat_str == "qq" || plat_str == "wechat")
	{
		return true;
	}
	return false;
}

inline bool IsOpenPlat(const GNET::Octets& account)
{
	std::string plat_str = GetPlat(account);
	return IsOpenPlat(plat_str);
}

inline std::string GetAppID(const std::string& platform, const GNET::Octets& account)
{
	bool is_guest = false;
	if (account.size() > 2 && strncmp((char *)account.begin(), "G_", 2) == 0)
	{
		is_guest = true;
	}

	if (strcmp(platform.c_str(), "qq") == 0)
	{
		if (is_guest)
		{
			return std::string("G_**********");
		}
		return std::string("**********");
	}
	else if (strcmp(platform.c_str(), "wechat") == 0)
	{
		return std::string("wxebffc92f0b100397");
	}
	return std::string();
}

inline std::string GetAppID(const std::string& platform, bool is_guest)
{
	if (strcmp(platform.c_str(), "qq") == 0)
	{
		if (is_guest)
		{
			return std::string("G_**********");
		}
		return std::string("**********");
	}
	else if (strcmp(platform.c_str(), "wechat") == 0)
	{
		return std::string("wxebffc92f0b100397");
	}
	return std::string();
}

// area_id wechat=1 ,qq=2
inline std::string GetAppID(int area_id)
{
	if (area_id == 2)
	{
		return std::string("**********");
	}
	else if (area_id == 1)
	{
		return std::string("wxebffc92f0b100397");
	}
	return std::string();
}


inline int GetAreaID(const std::string& platform)
{
	if (strcmp(platform.c_str(), "qq") == 0)
	{
		return 2;
	}
	else if (strcmp(platform.c_str(), "wechat") == 0)
	{
		return 1;
	}
	return 2;
}

inline int GetAreaID(const GNET::Octets& account)
{
	GNET::Octets plat;
	if (GetOpenPlat(account, plat))
	{
		return GetOpenPlat(plat) + 1;
	}
	// 默认返回手Q
	return 2;
}
inline int GetSecLogAreaID(const GNET::Octets& account)
{
	std::string  account_str((const char *)account.begin(), account.size());
	size_t pos = account_str.find_last_of('$');
	if (pos != std::string::npos)
	{
		int offset = static_cast<int>(pos) + 1;
		if ((size_t)offset < account.size())
		{
			std::string plat((char *)account.begin() + offset, account.size() - offset);
			if (strncmp(account_str.c_str(), "G_", 2) == 0)
			{
				return 3;
			}
			if (strncmp(plat.c_str(), "qq", 2) == 0)
			{
				return 1;
			}
		}
	}
	return 0;//默认用微信
}

inline void GetIpAddress(int ip, std::string& addr)
{
	char ipaddr[20] = {0}; //保存转换后的地址
	struct in_addr  inaddr;
	inaddr.s_addr = ip;
	char *ipaddr_str = inet_ntoa(inaddr);
	strncpy(ipaddr_str, (char *)&ipaddr, 20);
	addr = std::string(addr, 20);
}

/**
 *	尝试对域名进行解析
 *	@host: 待解析的域名
 *	@return: 解析出的ip地址, 如果未解析出，则原样返回
 */
inline std::string GetIpAddress(const char *host)
{
	if (host == nullptr)
	{
		return "";
	}
	struct addrinfo *answer, hint, *curr;
	const char *addr = nullptr;
	char ipstr[16];
	bzero(&hint, sizeof(hint));
	hint.ai_family = AF_INET;
	hint.ai_socktype = SOCK_STREAM;
	int ret = getaddrinfo(host, NULL, &hint, &answer);
	if (ret != 0)
	{
		return host;
	}
	for (curr = answer; curr != NULL; curr = curr->ai_next)
	{
		addr = inet_ntop(AF_INET, &(((struct sockaddr_in *)(curr->ai_addr))->sin_addr), ipstr, 16);
		if (addr)
		{
			return addr;
		}
	}
	return host;
}



inline std::string GetAppKey( const std::string& platform)
{
	if (strcmp(platform.c_str(), "qq") == 0)
	{
		return std::string("HnhIGpKPXosfpfvN");
	}
	else if (strcmp(platform.c_str(), "wechat") == 0)
	{
		return std::string("530baf5537f9920aa2cd3630ae1545be");
	}
	return std::string();
}
inline std::string GetTimeStr(time_t t, bool is_simple = false)
{
	struct tm time_tm;
	localtime_r(&t, &time_tm);
	char buf[64] = {0};
	if (is_simple)
	{
		strftime(buf, sizeof(buf), "%d %H:%M:%S", &time_tm);
	}
	else
	{
		strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", &time_tm);
	}
	return buf;
}
enum
{
	MIRROR_CLOSED_TIMEOUT_MAX		= 2 * 60 * 60,		//镜像关闭超时最长2个小时
};

#if defined __GAME_SERVER__
extern time_t GetSysTime();
static std::function<time_t()> time_func = GetSysTime;
#else
static std::function<time_t()> time_func = GNET::Timer::GetTime;
#endif

namespace TIME_HELPER
{

class TimeHelper
{
	time_t _day_begin;
	time_t _last_day_begin;
	time_t _next_day_begin;

	time_t _week_begin;
	time_t _last_week_begin;
	time_t _next_week_begin;

	time_t _month_begin;
	time_t _last_month_begin;
	time_t _next_month_begin;

	time_t _quarter_begin;
	time_t _last_quarter_begin;
	time_t _next_quarter_begin;

	struct tm _curr_day_TM;
	int    _day_offset;

#ifdef __GAME_SERVER__
	int	_lock;
#endif

	TimeHelper()
		: _day_begin(0), _last_day_begin(0), _next_day_begin(0)
		, _week_begin(0), _last_week_begin(0), _next_week_begin(0)
		, _month_begin(0), _last_month_begin(0), _next_month_begin(0)
		, _quarter_begin(0), _last_quarter_begin(0), _next_quarter_begin(0)
		, _day_offset(0)
#ifdef __GAME_SERVER__
		, _lock(0)
#endif
	{
		memset(&_curr_day_TM, 0, sizeof(_curr_day_TM));
	}

	void Check()
	{
		time_t now = time_func();

		if (now < _next_day_begin)
		{
			return;
		}
#ifdef __GAME_SERVER__
		spin_autolock keeper(_lock);
#endif
		if (now < _next_day_begin)
		{
			return;
		}

		{
			//update current day TM
			struct tm tm;
			localtime_r(&now, &tm);
			tm.tm_sec = 0;
			tm.tm_min = 0;
			tm.tm_hour = 0;
			memcpy(&_curr_day_TM, &tm, sizeof(tm));
			_day_offset = _curr_day_TM.tm_gmtoff > 0 ? SECOND_PER_DAY - _curr_day_TM.tm_gmtoff : -_curr_day_TM.tm_gmtoff;

			//update day info
			tm.tm_isdst = -1;
			_day_begin = mktime(&tm);

			tm.tm_mday--;
			tm.tm_isdst = -1;
			_last_day_begin = mktime(&tm);

			tm.tm_mday += 2;
			tm.tm_isdst = -1;
			_next_day_begin = mktime(&tm);
		}
		//update week info
		if (now >= _next_week_begin)
		{
			struct tm tm;
			memcpy(&tm, &_curr_day_TM, sizeof(tm));
			tm.tm_sec = 0;
			tm.tm_min = 0;
			tm.tm_hour = 0;

			int dfweek = 1 - ((tm.tm_wday == 0) ? 7 : tm.tm_wday);
			std::cout << "dfweek=" << dfweek << std::endl;
			tm.tm_mday += dfweek;
			tm.tm_isdst = -1;
			_week_begin = mktime(&tm);

			tm.tm_mday -= 7;
			tm.tm_isdst = -1;
			_last_week_begin = mktime(&tm);

			tm.tm_mday += 14;
			tm.tm_isdst = -1;
			_next_week_begin = mktime(&tm);
		}

		//update month info
		if (now >= _next_month_begin)
		{
			struct tm tm;
			localtime_r(&now, &tm);
			tm.tm_sec = 0;
			tm.tm_min = 0;
			tm.tm_hour = 0;
			tm.tm_mday = 1;
			tm.tm_isdst = -1;
			_month_begin = mktime(&tm);

			if (++tm.tm_mon == 12)
			{
				tm.tm_mon = 0;
				tm.tm_year++;
			}
			tm.tm_isdst = -1;
			_next_month_begin = mktime(&tm);

			tm.tm_mon -= 2;
			if ( tm.tm_mon < 0 )
			{
				tm.tm_mon += 12;
				-- tm.tm_year;
			}
			tm.tm_isdst = -1;
			_last_month_begin = mktime(&tm);
		}
		//update quarter info
		{
			struct tm tm;
			localtime_r(&now, &tm);
			tm.tm_sec = 0;
			tm.tm_min = 0;
			tm.tm_hour = 0;
			tm.tm_mday = 1;
			int tmp = tm.tm_mon / 3;
			tm.tm_mon = tmp  * 3;
			tm.tm_isdst = -1;
			_quarter_begin = mktime(&tm);

			if (tmp >= 3)
			{
				tm.tm_mon = 0;
				tm.tm_year ++;
			}
			else
			{
				tm.tm_mon = tmp * 3 + 3;
			}
			tm.tm_isdst = -1;
			_next_quarter_begin = mktime(&tm);

			tm.tm_mon = tmp * 3 - 3;
			if (tm.tm_mon < 0)
			{
				tm.tm_mon += 12;
				-- tm.tm_year;
			}
			if (tmp >= 3)
			{
				-- tm.tm_year;
			}
			tm.tm_isdst = -1;
			_last_quarter_begin = mktime(&tm);
		}
	}
	static TimeHelper& GetInstance()
	{
		static TimeHelper *_inst = NULL;
		if (!_inst)
		{
			_inst = new TimeHelper();

		}
		_inst->Check();
		return *_inst;
	}
public:

	static time_t GetLocalDayBegin()
	{
		return GetInstance()._day_begin;
	}
	static time_t GetLocalWeekBegin()
	{
		return GetInstance()._week_begin;
	}
	static time_t GetLocalMonthBegin()
	{
		return GetInstance()._month_begin;
	}
	static time_t GetLocalQuarterBegin()
	{
		return GetInstance()._quarter_begin;
	}

	static time_t GetLocalNextDayBegin()
	{
		return GetInstance()._next_day_begin;
	}
	static time_t GetLocalNextWeekBegin()
	{
		return GetInstance()._next_week_begin;
	}
	static time_t GetLocalNextMonthBegin()
	{
		return GetInstance()._next_month_begin;
	}
	static time_t GetLocalNextQuarterBegin()
	{
		return GetInstance()._next_quarter_begin;
	}

	static time_t GetLocalLastDayBegin()
	{
		return GetInstance()._last_day_begin;
	}
	static time_t GetLocalLastWeekBegin()
	{
		return GetInstance()._last_week_begin;
	}
	static time_t GetLocalLastMonthBegin()
	{
		return GetInstance()._last_month_begin;
	}
	static time_t GetLocalLastQuarterBegin()
	{
		return GetInstance()._last_quarter_begin;
	}
	static void   GetLocalDayTMStruct(struct tm& tm)
	{
		memcpy(&tm, &GetInstance()._curr_day_TM, sizeof(tm));
	}

	static int    GetLocalMDay()
	{
		return GetInstance()._curr_day_TM.tm_mday;
	}
	static int    GetLocalWDay()
	{
		return GetInstance()._curr_day_TM.tm_wday;
	}
	static int    GetLocalYDay()
	{
		return GetInstance()._curr_day_TM.tm_yday;
	}
	static int    GetLocalMon()
	{
		return GetInstance()._curr_day_TM.tm_mon;
	}
	static int    GetLocalYear()
	{
		return GetInstance()._curr_day_TM.tm_year;
	}
	static int    GetLocalGMTOffset()
	{
		return GetInstance()._curr_day_TM.tm_gmtoff;
	}
	static int    GetDayOffSet()
	{
		return GetInstance()._day_offset;
	}

	static int    GetDay(time_t time)
	{
#ifndef _WITH_DAYLIGHT_SAVING_TIME_
		return (time + GetLocalGMTOffset()) / SECOND_PER_DAY;
#else
		auto delta = (time + GetLocalGMTOffset()) % SECOND_PER_DAY;
		if (delta > 3600 && delta < 82800)
		{
			return (time + GetLocalGMTOffset()) / SECOND_PER_DAY;
		}
		struct tm tm;
		localtime_r(&time, &tm);
		return (time + tm.tm_gmtoff) / SECOND_PER_DAY;
#endif
	}
	static int    GetDayBegin(time_t time)
	{
#ifndef _WITH_DAYLIGHT_SAVING_TIME_
		int the_day_begin = time - time % SECOND_PER_DAY + GetDayOffSet();
		if (time < the_day_begin)
		{
			the_day_begin -= SECOND_PER_DAY;
		}
		return the_day_begin;
#else
		struct tm tm;
		localtime_r (&time, &tm);
		tm.tm_sec = 0;
		tm.tm_min = 0;
		tm.tm_hour = 0;
		tm.tm_isdst = -1;
		return mktime(&tm);
#endif
	}

	static std::string GetNowTimeStr(bool is_simple = false)
	{
		time_t now_time = time_func();
		return GetTimeStr(now_time, is_simple);
	}

	static int GetDayBeginTimeAfterNow(size_t delta_days)
	{
#ifndef _WITH_DAYLIGHT_SAVING_TIME_
		return GetLocalDayBegin() + SECOND_PER_DAY * delta_days;
#else
		struct tm cur_tm;
		GetLocalDayTMStruct(cur_tm);
		cur_tm.tm_mday += delta_days;
		cur_tm.tm_isdst = -1;
		return mktime(&cur_tm);
#endif
	}
};
}
inline time_t GetLocalDayBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalDayBegin();
}
inline time_t GetLocalLastDayBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalLastDayBegin();
}
inline time_t GetLocalNextDayBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalNextDayBegin();
}
inline time_t GetLocalWeekBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalWeekBegin();
}

inline time_t GetLocalWeekBegin(time_t& currentWeek, time_t& lastWeek)
{
	lastWeek = TIME_HELPER::TimeHelper::GetLocalLastWeekBegin();
	currentWeek = TIME_HELPER::TimeHelper::GetLocalWeekBegin();
	return currentWeek;
}
inline time_t GetLocalLastWeekBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalLastWeekBegin();
}
inline time_t GetLocalNextWeekBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalNextWeekBegin();
}
inline time_t GetLocalMonthBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalMonthBegin();
}
inline time_t GetLocalMonthBegin(time_t& currentMonth, time_t& lastMonth)
{
	lastMonth = TIME_HELPER::TimeHelper::GetLocalLastMonthBegin();
	currentMonth = TIME_HELPER::TimeHelper::GetLocalMonthBegin();
	return currentMonth;
}
inline time_t GetLocalLastMonthBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalLastMonthBegin();
}
inline time_t GetLocalNextMonthBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalNextMonthBegin();
}
inline time_t GetLocalQuarterBegin(time_t& currentQuarter, time_t& lastQuarter)
{
	lastQuarter = TIME_HELPER::TimeHelper::GetLocalLastQuarterBegin();
	currentQuarter = TIME_HELPER::TimeHelper::GetLocalQuarterBegin();
	return currentQuarter;
}
inline time_t GetLocalNextQuarterBegin()
{
	return TIME_HELPER::TimeHelper::GetLocalNextQuarterBegin();
}
inline void GetLocalDayTMStruct(struct tm& tm)
{
	TIME_HELPER::TimeHelper::GetLocalDayTMStruct(tm);
}
inline int GetDayOffSet()
{
	return TIME_HELPER::TimeHelper::GetDayOffSet();
}
inline int GetDay(time_t time)
{
	return TIME_HELPER::TimeHelper::GetDay(time);
}
inline int GetDayBegin(time_t time)
{
	return TIME_HELPER::TimeHelper::GetDayBegin(time);
}
inline int DeltaDays(time_t day1, time_t day2)
{
	return std::abs(TIME_HELPER::TimeHelper::GetDay(day1) - TIME_HELPER::TimeHelper::GetDay(day2));
}
inline bool InSameLocalDay(time_t day1, time_t day2)
{
	return GetDay(day1) == GetDay(day2);
}
inline int GetLocalGMTOffset()
{
	return TIME_HELPER::TimeHelper::GetLocalGMTOffset();
}
inline int GetLocalMDay()
{
	return TIME_HELPER::TimeHelper::GetLocalMDay();
}
inline int GetLocalWDay()
{
	return TIME_HELPER::TimeHelper::GetLocalWDay();
}
inline int GetLocalYDay()
{
	return TIME_HELPER::TimeHelper::GetLocalYDay();
}
inline int GetLocalMon()
{
	return TIME_HELPER::TimeHelper::GetLocalMon();
}
inline int GetLocalYear()
{
	return TIME_HELPER::TimeHelper::GetLocalYear();
}

//获取现在开始若干天之后某天的开始时间
inline time_t GetDayBeginTimeAfterNow(size_t delta_days)
{
	if (delta_days == 0)
	{
		return GetLocalDayBegin();
	}
	return TIME_HELPER::TimeHelper::GetDayBeginTimeAfterNow(delta_days);
}

//获取当前在游戏内是几点
inline int GetNowInGameHour()
{
	int cur_mins = (time_func() - GetLocalDayBegin()) / 60;
	int hour = ((cur_mins % 240) * 6) / 60 + 6;
	if (hour >= 24)
	{
		hour = hour - 24;
	}
	return hour;
}

inline void GetInGameTime(int& hour, int& min, time_t time = 0)
{
	int cur_mins = 0; //当前距现在实际经过了多少分钟
	if (time == 0)
	{
		cur_mins = (time_func() - GetLocalDayBegin()) / 60;
	}
	else
	{
		struct tm l_tm;
		localtime_r(&time, &l_tm);
		cur_mins = l_tm.tm_hour * 60 + l_tm.tm_min;
	}
	int game_mins = (cur_mins % 240) * 6;
	hour = game_mins / 60 + 6;
	if (hour >= 24)
	{
		hour = hour - 24;
	}
	min = game_mins % 60;
}

// 获取当前
inline int GetCurHour(int now = 0)
{
	if (!now)
	{
		now = time_func();
	}
	return (now - GetLocalDayBegin()) / SECOND_PER_HOUR;
}

inline bool NeedClear(unsigned short id, time_t last_mod, time_t now)
{
	time_t lastCheckPoint = 0;
	time_t currentCheckPoint = 0;
	if (GNET::IsMonthClearReputation(id))
	{
		GetLocalMonthBegin(currentCheckPoint, lastCheckPoint);
	}
	else if (GNET::IsWeekClearReputation(id))
	{
		GetLocalWeekBegin(currentCheckPoint, lastCheckPoint);
	}
	else if (GNET::IsQuarterClearReputation(id))
	{
		GetLocalQuarterBegin(currentCheckPoint, lastCheckPoint);
	}

	/*
	currentCheckPoint += GNET::GetClearReputationDelay(id);
	lastCheckPoint += GNET::GetClearReputationDelay(id);
	*/

	if ( last_mod < lastCheckPoint )
	{
		return true;
	}
	else if ( last_mod < currentCheckPoint && currentCheckPoint <= now )
	{
		return true;
	}
	return false;
}

#ifndef PRT_AC
#define PRT_AC "%.*s"
#endif

#ifndef LOG_AC
#define LOG_AC(ac) (int)(ac).size(), (const char *)(ac).cbegin()
#endif

inline int GetUTF8StringLength(const char *s, int len)
{
	if (!s || len <= 0)
	{
		return 0;
	}
	int utf8 = 0;
	for (int i = 0; i < len; i ++)
	{
		if ((s[i] & 0xc0) != 0x80)
		{
			utf8 ++;
		}
	}
	return utf8;
}

enum
{
	MIDAS_DEC_CASH_TYPE_MALL		= 0,	//商城
	MIDAS_DEC_CASH_TYPE_CREATE_CORPS	= 1,	//创建帮派
	MIDAS_DEC_CASH_TYPE_EASY_MALL		= 2,	//随身商店(钱庄彩票)
//	MIDAS_DEC_CASH_TYPE_REVIVE		= 3,	//原地复活
	MIDAS_DEC_CASH_TYPE_BLESSING		= 4,	//好友祝福
	MIDAS_DEC_CASH_TYPE_LOGOUT_EXP		= 5,	//离线经验
	MIDAS_DEC_CASH_TYPE_MAFIA_MONEY		= 6,	//帮派捐献
	MIDAS_DEC_CASH_TYPE_BLACK_REFRESH	= 7,	//黑市刷新
	MIDAS_DEC_CASH_TYPE_REFRESH_HERO	= 8,	//名将试炼刷新
	MIDAS_DEC_CASH_TYPE_TOWER_RUSH		= 9,	//闯天关直接完成
	MIDAS_DEC_CASH_TYPE_CAMP_FIRE		= 10,	//帮派篝火
	MIDAS_DEC_CASH_TYPE_ADD_BACKPACK	= 11,	//扩背包
	MIDAS_DEC_CASH_TYPE_BUY_INSTANCE	= 12,	//副本购买次数
	MIDAS_DEC_CASH_TYPE_PEFECT_REVIVE	= 13,	//完美复活
	MIDAS_DEC_CASH_TYPE_BUY_FORCE		= 14,	//购买体力
	MIDAS_DEC_CASH_TYPE_HARVEST		= 15,	//快速种植
	MIDAS_DEC_CASH_TYPE_NATION_JUMP		= 16,	//国战传送
	MIDAS_DEC_CASH_TYPE_DAY_FUND		= 17,	//购买成长基金
	MIDAS_DEC_CASH_TYPE_NATION_DONATE	= 18,	//
	MIDAS_DEC_CASH_TYPE_TASK		= 19,	//任务
	MIDAS_DEC_CASH_TYPE_RETRIEVE		= 20,	//找回
	MIDAS_DEC_CASH_TYPE_MALL_TRADE		= 21,	//商城非绑定钻石
	MIDAS_DEC_CASH_TYPE_AUCTION		= 22,	//拍卖行
	MIDAS_DEC_CASH_TYPE_IDIP_BIND		= 23,
	MIDAS_DEC_CASH_TYPE_IDIP_UNBIND		= 24,
	MIDAS_DEC_CASH_TYPE_WUHUN_UPGRADE	= 25,
	MIDAS_DEC_CASH_TYPE_UPGRADE_CHARIOT     = 26,	//帮会战车升级
	MIDAS_DEC_CASH_TYPE_ADD_DEPOSITORY	= 27,	//扩仓库
};

inline bool NeedTradeType(int type)
{
	return type == MIDAS_DEC_CASH_TYPE_DAY_FUND || type == MIDAS_DEC_CASH_TYPE_MALL_TRADE || type == MIDAS_DEC_CASH_TYPE_IDIP_UNBIND;
}

inline bool IsPrePayType(int type)
{
	return type == MIDAS_DEC_CASH_TYPE_PEFECT_REVIVE || type == MIDAS_DEC_CASH_TYPE_IDIP_UNBIND || type == MIDAS_DEC_CASH_TYPE_IDIP_BIND;
}

enum WAR_FUNCTION_TYPE
{
	WAR_FUNCTION_INVALID 	= 0,
	WAR_FUNCTION_NOT_OPEN  	= 1,
	WAR_FUNCTION_STEP_1	= 2,
	WAR_FUNCTION_STEP_2	= 3,
	WAR_FUNCTION_STEP_3	= 4,
};

enum EM_NATION_NAME
{
	NATION_YOUZHOU 	= 1,
	NATION_JIZHOU 	= 2,
	NATION_YUZHOU 	= 3,
	NATION_YANGZHOU = 4,
	NATION_JINGZHOU = 5,
	NATION_YIZHOU 	= 6,
};

enum SEC_IDIP_TYPE
{
	SEC_IDIP_MONEY			= 1,
	SEC_IDIP_DIAMOND		= 2,
	SEC_IDIP_TOPLIST		= 3,
	SEC_IDIP_RESET			= 4,
	SEC_IDIP_ZERO_BENEFIT	= 5,
	SEC_IDIP_MESSAGE		= 6,
	SEC_IDIP_BAN_GAME		= 7,
	SEC_IDIP_BAN_GAME_ALL	= 8,
	SEC_IDIP_UNBAN_GAME		= 9,
	SEC_IDIP_UNFREEZE		= 10,	// 解冻[经验、周期性经验、金币、钻石]
	SEC_IDIP_CHANGE_REPU	= 11,	// 更改声望
	SEC_IDIP_DEL_TASK		= 12,	// 删除任务
	SEC_IDIP_DEL_ITEM		= 13,	// 删除物品
	SEC_IDIP_DISCARD_FREEZE	= 14,	// 丢弃冻结[经验、周期性经验、金币、钻石]
	SEC_IDIP_SEND_EXP	= 15,	// 发经验
	SEC_IDIP_CASH		= 16,	// 发送货币点券
	SEC_IDIP_FINISH_TASK		= 17,	// 完成任务
	SEC_IDIP_CASH_PURSUE	= 18,	//追缴状态
	SEC_IDIP_CASH_PURSUE_CLEAN	= 19,	//清除追缴状态
	SEC_IDIP_TRANSPORT	= 20,	//传送玩家
	SEC_IDIP_QUERY_PURSUE	= 21,	//查询玩家追缴状态
	SEC_IDIP_DEL_LONGYU		= 22,	// 删除龙语
	SEC_IDIP_ADD_LONGYU		= 23,	// 添加龙语
	SEC_IDIP_CHG_LONGYU		= 24,	// 替换/删除已解锁的龙语
	SEC_IDIP_ADD_EQUIP		= 25,	// 添加装备
	SEC_IDIP_ADD_FURNITURE  = 26,   // 添加家具
	SEC_IDIP_DEL_FURNITURE  = 27,   // 删除家具
	SEC_IDIP_UNLEARN_KOTODAMA	= 28,   // 反解锁言灵
	SEC_IDIP_EXCHANGE_EQUIP_SUIT	= 29,	// 替换/新增/删除套装
	SEC_IDIP_DEL_TITLE		= 30,   // 删除称号
	SEC_IDIP_DEL_FASHION    = 31,   // 删除时装
	//SEC_IDIP_SOUL_CHILD_DELETE	= 32,	// 删除伴侣孩子
	SEC_IDIP_DEL_PHOTO      = 33,   // 删除头像
	SEC_IDIP_CHILD_MODIFY	= 34,	//  修改继承者数据
	SEC_IDIP_SEND_REWARD	= 35,	// 发放奖励模板
	SEC_IDIP_DEL_VEHICLE    = 36,   //  删除玩家座驾
	SEC_IDIP_EXP_TIME_FORBID = 37, //封禁经验获取 按时间
	SEC_IDIP_EXP_NUM_FORBID = 38, //封禁经验获取 按数量
	SEC_IDIP_DEL_SS_STYLE = 39, //删除朋友圈装扮
	SEC_IDIP_DEL_CHATBOX = 40, //删除聊天装扮
	SEC_IDIP_SET_ENHANCE_LEVEL			= 41, // 设置赋能等级
	SEC_IDIP_SET_SEVEN_CRIME_LEVEL		= 42, // 设置七宗罪等级
	SEC_IDIP_DEL_ERR_DRAGONBORN			= 43, // 删除错误龙裔
	SEC_IDIP_SET_GUARD_LEVEL			= 44, // 设置宠物等级
	SEC_IDIP_DIAMOND_PURSUE	= 45,	//钻石追缴状态
	SEC_IDIP_DIAMOND_PURSUE_CLEAN	= 46,	//清除钻石追缴状态
	SEC_IDIP_DEL_PHOTO_DECO      = 47,   // 删除头像框
	SEC_IDIP_MODIFY_TOP_STAR      = 48,   // 修改应援活动抽奖次数
	SEC_IDIP_MODIFY_CAREER_SHOP_NAME 	= 54, //修改副业小店名字
	SEC_IDIP_MODIFY_CAREER_SHOP_SLOGAN	= 55, //修改副业小店宣传语
	SEC_IDIP_MODIFY_CUTE_PET_NAME		= 56, //修改副业萌宠名
	SEC_IDIP_MODIFY_CHILD_NAME			= 57, //修改继承者名字
	SEC_IDIP_MODIFY_SKILL_PLAN_NAME		= 58, //修改技能方案名字
	SEC_IDIP_MODIFY_BLOOD_PLAN_NAME		= 59, //修改血统方案名字
	SEC_IDIP_MODIFY_YICHU_FASHION_DRESS_NAME	= 60, //修改衣橱时装搭配名字
	SEC_IDIP_MODIFY_HARMOUIOUS_NAME		= 61, //修改金玉良缘名字
	SEC_IDIP_MODIFY_HARMOUIOUS_NAME_TARGET		= 62, //修改对方金玉良缘名字
	SEC_IDIP_MODIFY_GUARD_NAME			= 63, //修改宠物名字
	SEC_IDIP_MODIFY_HOMETOWN_SCHEME_NAME = 64, //修改家园设计方案
	SEC_IDIP_MODIFY_BLESSING_INFO		= 65, //送花寄语
	SEC_IDIP_ADD_TASK					= 66, //接任务
	SEC_IDIP_DEL_LONGHUN				= 67,	// 删除龙魂
	SEC_IDIP_ADD_LONGHUN				= 68,	// 添加龙魂
	SEC_IDIP_CHG_LONGHUN				= 69,	// 替换/删除已解锁的龙魂
	SEC_IDIP_MODIFY_HORSE_LEVEL     = 49,  //修改座驾等级
	SEC_IDIP_MODIFY_RETINUE_LEVEL  =  50, // 修改伙伴等级
	SEC_IDIP_DEL_DELTETRAHEDRON    =  51, //删除圣核
	SEC_IDIP_MODIFY_CHILD_LEVEL    =  52, // 修改继承者等级天命层数
	SEC_IDIP_MODIFY_DRAGONBORN_LEVEL = 53, //修改龙裔等级
	SEC_IDIP_DEL_ITEM_MAIL			= 70,	// 删除邮件物品
	SEC_IDIP_DEL_ITEM_REWARD_BAG	= 71,	// 删除临时背包物品
	SEC_IDIP_COMPLETE_TOWNLET_CHAT_EVENT	= 72, // 完成小镇伙伴聊天事件
	SEC_IDIP_FINISH_PLAYER_GUIDE	= 73, // 完成新手引导
}; 
enum SEC_IDIP_PLAY_TYPE
{
	SEC_IDIP_PLAY_MINIGAME	= 1,
	SEC_IDIP_PLAY_WINE	= 2,
	SEC_IDIP_PLAY_INSTANCE	= 3,
};

const int IDIP_FORBID_DATA_SIZE = 65536*2;		// 禁止开关bitmap大小，[1, 65535*2]有效
const int IDIP_FORBID_ADDON_SIZE = 64;			// 禁止属性最大数量
const int IDIP_FORBID_ADDON_GROUP_SIZE = 64;	// 禁止属性组最大数量

enum PLAT_VIP_KIND
{
	PLAT_VIP_NULL          = 0,
	PLAT_VIP_QQ_CENTER     = 1,	//qq游戏中心启动
	PLAT_VIP_WEIXIN_CENTER = 2,	//微信游戏中心启动
	PLAT_VIP_QQ_MEMBER     = 3,	//qq会员
	PLAT_VIP_QQ_SMEMBER    = 4,	//qq超级会员
	PLAT_VIP_MAX,


	PLAT_VIP_QQ_CENTER_MASK     = 1 << (PLAT_VIP_QQ_CENTER - 1),	//qq游戏中心启动
	PLAT_VIP_WEIXIN_CENTER_MASK = 1 << (PLAT_VIP_WEIXIN_CENTER - 1),	//微信游戏中心启动
	PLAT_VIP_QQ_MEMBER_MASK     = 1 << (PLAT_VIP_QQ_MEMBER - 1),	//qq会员
	PLAT_VIP_QQ_SMEMBER_MASK    = 1 << (PLAT_VIP_QQ_SMEMBER - 1),	//qq超级会员

};
enum RESET_RETINUE_CATEGORY
{
	RSET_RETINUE_LEVEL = 0,
	RSET_RETINUE_QUALITY = 1,
};
enum RESET_CHILD_CATEGORY
{
	RSET_CHILD_LEVEL = 0,
	RSET_CHILD_DESTINY = 1,
};
enum DRAGONBORN_RESERT_CATEGORY
{
	RESERT_DRAGONBORN_LEVE = 0,
	RESERT_DRAGONBORN_EVOLEVEL = 1,
	RESERT_DRAGONBORN_BREAKEVEL =2 ,
};
/*
class TimeStat
{
	struct timeval begin_tmp;
	struct timeval end_tmp;
	std::string msg;
public:
	TimeStat(const std::string & m) : msg(m) { gettimeofday(&begin_tmp, NULL); }
	~TimeStat()
	{
		gettimeofday(&end_tmp, NULL);
		int value = (end_tmp.tv_sec - begin_tmp.tv_sec)*1000000 + (end_tmp.tv_usec - begin_tmp.tv_usec);
		fprintf(stdout,"time=%d:%s:use=%d msc\n", (int)end_tmp.tv_sec, msg.c_str(), value);
	}
};
*/

enum TX_DISTRICT_ID
{
	WX_ANDROID	= 1,
	QQ_ANDROID	= 2,
	WX_IOS		= 3,
	QQ_IOS		= 4,
};

enum DBLOADROLE_REQUEST
{
	DBLOADROLE_REQUEST_LOADING,
	DBLOADROLE_REQUEST_LISTCONTACTS,
	DBLOADROLE_REQUEST_GETPROFILE,
	DBLOADROLE_REQUEST_HOMETOWN,
	DBLOADROLE_REQUEST_SECOND_HOMETOWN,
	DBLOADROLE_REQUEST_GET_ROLE_INFO,
	DBLOADROLE_REQUEST_ROLE_LIST,
	DBLOADROLE_REQUEST_CORPS_RACE_REWARD,
	DBLOADROLE_REQUEST_CAREER_SHOP,
};

enum RECHARGE_ACTIVITY_TYPE
{
	FIRST_RECHARGE_AWARD 	= 0,
	FIRST_RECHARGE_DOUBLE 	= 1,
	RECHARGE_FUND	 	= 2,
	RECHARGE_MONTH_CARD   	= 3,
	RECHARGE_TIME_LIMIT   	= 4,
	SECOND_RECHARGE		= 5,
	EACH_DAY_RECHARGE	= 6,
	ACCUMULATE_RECHARGE   	= 7,
	RECHARGE_TOTAL_TIME_LIMIT = 8,
	MULTI_RECHARGE		= 9,
	CONSUME_TIME_LIMIT	= 10,
	SERVER_LEVEL_EXP_FACTOR	= 11,
	CRAZY_CARD1				= 12,
	CRAZY_CARD2				= 13,//狂欢充值开关
	CRAZY_CARD3				= 14,
	CRAZY_CARD4				= 15,
	CRAZY_CARD5				= 16,
	SET_SERVER_LEVEL		= 17,//卡级等级
	SERVER_LEVELUP_DAYS		= 18,//卡级等级升级剩余天数
	ACHIEVEMENT_RECHARGE		= 19,
	CHECK_ORDERS			= 20,
	CHECK_ORDERS_SPECIAL	= 21,
	CHECK_ORDERS_COST_VERSION	= 22,
	CHECK_ORDERS_PRESENT_VERSION	= 23,
};

enum LOG_MONEY_TYPE
{
	LMT_GOLD = 0, // 金币
	LMT_FREECASH = 1, //点券
	LMT_BINDCASH = 2, // 钻石
	LMT_NONE = 4, // 未知类型
};

/* player switch 开关定义 */
enum PLAYER_SWITCH_TYPE
{
	// type == 1;
	// index == xxx;
	ROLE_NAME_SWITCH_TYPE  = 1, // type: 任务组功能开关
#define ROLE_NAME_SWITCH_ID_COND 1		// index: 新角色起名的条件开关编号
#define ROLE_NAME_SWITCH_ID_RESULT 2 	// index: 新角色起名的结果开关编号;
#define NEW_PLAYER_CLEAR_SWITCH_INDEX 8 // index : 清空新玩家装备、时装、技能、技能配置的开关索引
#define ROLE_FLYSWORD_SPEEDUP_ID_COND 10 // index: 玩家是否开启了场景座驾加速 | 赛车驾照开关索引
#define SKIP_NEWPLAYER_SCENE_SWITCH_ID    12    // index: 跳过新手村开关编号

	// type == 2;
	// index == xxx;
	GUANQIA_SWITCH_TYPE  = 2, // type: 关卡组功能开关
#define HUNT_DRAGON_POINT_SWITCH_INDEX 16 // index: 是否消耗猎龙点数开关索引

	// type == 3;
	// index == xxx;

	// type == 4;
	// index == xxx;
	PLAYER_SYSTEM_SWITCH_TYPE = 4, // type: 系统组功能开关
#define PLAYER_FACELIFT_SWITCH_ID 1 // index: 重新捏脸索引
#define FASHION_WEAPON_SWITCH_ID  2	 // index: 角色拟态的开关编号
#define ROLE_FLYSWORD_CAN_FLY_ID_COND 6		// index: 玩家是否开启了场景座驾加速
#define PLAYER_SYSTEM_SWITCH_INDEX_BODY_CHANGED 10 // index: 是否已经转换过体型
#define PLAYER_SYSTEM_SWITCH_INDEX_WEEK_CARD 39 // 周卡特权

	// type == 5;
	// index == xxx;
	PLAYER_WECHAT_SWITCH_TYPE  = 5, // type: 客户端微信聊天开关类型，这个值只允许客户端来修改

	// type == 100;
	// index == xxx;
	SERVER_SWITCH_TYPE = 100, // type: 服务器程序使用的开关类型，不允许在任务、关卡等地方修改
#define SERVER_SWITCH_INDEX_YESTERDAY_ACTIVE 1	// index: 昨日活跃玩家
#define SERVER_SWITCH_INDEX_RETINUE_ACTIVE_SSS_CONFIRMED 2 // index: 客户端是否清理过可发送状态的红点提示
#define SERVER_SWITCH_INDEX_SOLUTION 3 //index:  总方案功能是否开启
#define SERVER_SWITCH_INDEX_PUBG_SINGLE_MATCH 101 // index: 是否匹配过单人吃鸡
#define SERVER_SWITCH_INDEX_DRAGONBORN_HIDE 102 // 是否龙裔隐藏

	// type == 101;
	// index == xxx;
	PLAYER_CG_SWITCH_TYPE  = 101, // type: CG开关类型

	// type == 102;
	// index == xxx;
	PLAYER_ABTEST_SWITCH_TYPE = 102, // type:
#define PLAYER_ABTEST_INIT_INDEX  1 // index:

	// type == 103;
	// index == xxx;
	PLAYER_CAREER_STAR_TOPIC_TYPE = 103, // type: 明星身份，热搜激活id列表
};

struct role_reputation_node
{
	int modify_time;
	int value;
	role_reputation_node(int i = 0, int j = 0) : modify_time(i), value(j) {}
};

typedef std::map<unsigned short, role_reputation_node> REPUTATION_M;

template<typename T>
inline bool DoSaveReputation(const T *pReputation, GNET::Marshal::OctetsStream& os_normal, unsigned short& count,
                             GNET::Marshal::OctetsStream& os_period, unsigned short& count_period, int id_max, bool gs_repu, bool ds_repu)
{
	//默认按照数组处理
	if (!pReputation || id_max <= 0 || id_max >= GNET::REPUID_MAX)
	{
		return false;
	}
	for (int i = 0; i <= id_max; ++ i)
	{
		const T& node = *(pReputation + i);
		if (GNET::IsGsReputation(i) && !gs_repu)
		{
			continue;
		}
		else if (GNET::IsDeliverReputation(i) && !ds_repu)
		{
			continue;
		}
		if (!GNET::IsPeriodReputation(i))
		{
			++ count;
			os_normal << GNET::CompactUINT(i) << GNET::CompactSINT(node.value);
		}
		else if (!NeedClear(i, node.modify_time, time_func()))
		{
			++ count_period;
			os_period << GNET::CompactUINT(i) << node.modify_time << GNET::CompactSINT(node.value);
		}
	}
	return true;
}

inline bool DoSaveReputation(const REPUTATION_M *pReputation, GNET::Marshal::OctetsStream& os_normal, unsigned short& count,
                             GNET::Marshal::OctetsStream& os_period, unsigned short& count_period, int id_max, bool gs_repu, bool ds_repu)
{
	if (!pReputation)
	{
		return false;
	}
	auto it = pReputation->begin();
	for ( ; it != pReputation->end(); ++it)
	{
		if (GNET::IsGsReputation(it->first) && !gs_repu)
		{
			continue;
		}
		else if (GNET::IsDeliverReputation(it->first) && !ds_repu)
		{
			continue;
		}
		if (!GNET::IsPeriodReputation(it->first))
		{
			++ count;
			os_normal << GNET::CompactUINT(it->first) << GNET::CompactSINT(it->second.value);
		}
		else if (!NeedClear(it->first, it->second.modify_time, time_func()))
		{
			++ count_period;
			os_period << GNET::CompactUINT(it->first) << it->second.modify_time << GNET::CompactSINT(it->second.value);
		}
	}
	return true;
}


template<typename T>
inline bool SaveReputation(const T *pReputation, GNET::Octets& output, int id_max, bool gs_repu, bool ds_repu)
{
	output.clear();
	GNET::Marshal::OctetsStream os, os_normal, os_period;
	unsigned short count = 0;
	unsigned short count_period = 0;

	if (!DoSaveReputation(pReputation, os_normal, count, os_period, count_period, id_max, gs_repu, ds_repu))
	{
		return false;
	}

	unsigned char version = GNET::REPUTATION_VERSION;
	os.push_byte((char *)&version, sizeof(unsigned char));
	os.push_byte((char *)&count, sizeof(unsigned short));
	os.push_byte((char *)&count_period, sizeof(unsigned short));
	output.replace(os.begin(), os.size());
	output.insert(output.end(), os_normal.begin(), os_normal.size());
	output.insert(output.end(), os_period.begin(), os_period.size());
	return true;
}

template<typename MAP = REPUTATION_M, typename T = role_reputation_node>
inline bool LoadReputation(REPUTATION_M *pReputation, const char *p, size_t size, unsigned short& id_max, bool gs_repu, bool ds_repu)
{
	if (!pReputation || !p)
	{
		return false;
	}
	if (size < 5)
	{
		return true;
	}
	REPUTATION_M& reputation = *pReputation;
	if (*(unsigned char *)p == GNET::REPUTATION_VERSION) //version
	{
		p += sizeof(unsigned char);
		unsigned short count = *(unsigned short *)p;
		p += sizeof(unsigned short);
		unsigned short count_period = *(unsigned short *)p;
		p += sizeof(unsigned short);
		try
		{
			GNET::Marshal::OctetsStream os;
			os.push_byte(p, size - 5);
			for (unsigned short i = 0; i < count; ++i)
			{
				unsigned int id = 0;
				int value = 0;
				os >> GNET::CompactUINT(id) >> GNET::CompactSINT(value);
				if (GNET::IsGsReputation(id) && !gs_repu)
				{
					continue;
				}
				else if (GNET::IsDeliverReputation(id) && !ds_repu)
				{
					continue;
				}
				role_reputation_node& repu = reputation[id];
				repu.value = value;
				if (id_max < id)
				{
					id_max = id;
				}
			}
			for (unsigned short i = 0; i < count_period; ++i)
			{
				unsigned int id = 0;
				int last_mod = 0;
				int value = 0;
				os >> GNET::CompactUINT(id) >> last_mod >> GNET::CompactSINT(value);
				if (GNET::IsGsReputation(id) && !gs_repu)
				{
					continue;
				}
				else if (GNET::IsDeliverReputation(id) && !ds_repu)
				{
					continue;
				}
				if (NeedClear(id, last_mod, time_func()))
				{
					continue;
				}
				role_reputation_node& repu = reputation[id];
				repu.value = value;
				repu.modify_time = last_mod;
				if (id_max < id)
				{
					id_max = id;
				}
			}
		}
		catch (GNET::Marshal::Exception&)
		{
			GNET::Log::log(LOG_ERR, "Reputation Unmarshal");
			return false;
		}
	}
	return true;
}

template<typename MAP, typename T>
inline bool LoadReputation(MAP *pReputation, const char *p, size_t size, unsigned short& id_max, bool gs_repu, bool ds_repu)
{
	//默认按照数组处理
	if (!pReputation || !p)
	{
		return false;
	}
	if (size < 5)
	{
		return true;
	}
	if (*(unsigned char *)p == GNET::REPUTATION_VERSION) //version
	{
		p += sizeof(unsigned char);
		unsigned short count = *(unsigned short *)p;
		p += sizeof(unsigned short);
		unsigned short count_period = *(unsigned short *)p;
		p += sizeof(unsigned short);
		try
		{
			GNET::Marshal::OctetsStream os;
			os.push_byte(p, size - 5);
			for (unsigned short i = 0; i < count; ++i)
			{
				unsigned int id = 0;
				int value = 0;
				os >> GNET::CompactUINT(id) >> GNET::CompactSINT(value);
				if (GNET::IsGsReputation(id) && !gs_repu)
				{
					continue;
				}
				else if (GNET::IsDeliverReputation(id) && !ds_repu)
				{
					continue;
				}
				T& repu = pReputation[id];
				repu.value = value;
				if (id_max < id)
				{
					id_max = id;
				}
			}
			for (unsigned short i = 0; i < count_period; ++i)
			{
				unsigned int id = 0;
				int last_mod = 0;
				int value = 0;
				os >> GNET::CompactUINT(id) >> last_mod >> GNET::CompactSINT(value);
				if (GNET::IsGsReputation(id) && !gs_repu)
				{
					continue;
				}
				else if (GNET::IsDeliverReputation(id) && !ds_repu)
				{
					continue;
				}
				if (NeedClear(id, last_mod, time_func()))
				{
					continue;
				}
				T& repu = pReputation[id];
				repu.value = value;
				repu.modify_time = last_mod;
				if (id_max < id)
				{
					id_max = id;
				}
			}
		}
		catch (GNET::Marshal::Exception&)
		{
			GNET::Log::log(LOG_ERR, "Reputation Unmarshal");
			return false;
		}
	}
	return true;
}

#ifndef ZLLOG_AC
#define ZLLOG_AC(name,ac)   \
	char name[ac.size()+1];\
	sprintf(name,"%.*s",LOG_AC(ac));
#endif

#ifndef PRINT_OCTETS
#define PRINT_OCTETS(x)	(int)x.size(),(char*)x.begin()
#endif
#define TIMESTAMP_TO_LROLEID(timestamp)  (0xFFFFFFFF00000000 | (timestamp))
#define IS_TIMESTAMP_ROLEID(roleid) ((((int64_t)(roleid)) & 0xFFFFFFFF00000000) == 0xFFFFFFFF00000000)
#define ROLEID_TO_TIMESTAMP(roleid) (int)((roleid) & 0xFFFFFFFF)

//重发间隔不能小于3分钟，因为rpc超时也是3分钟
#define  FAIL_RESENT_TIMEOUT 			120

class RunOnProgrammerStart
{
	std::vector<std::function<void()>> _functions;
	std::atomic<bool> _run;
	RunOnProgrammerStart() {}
public:
	~RunOnProgrammerStart()
	{
	}
	static RunOnProgrammerStart& Instance()
	{
		static RunOnProgrammerStart _instance;
		return _instance;
	}
	bool InsertFunc(const std::function<void()>& func)
	{
		if (_run)
		{
			return false;
		}
		_functions.push_back(func);
		return true;
	}
	void Run()
	{
		bool old = false;
		if (!_run.compare_exchange_weak(old, true))
		{
			return;
		}
		std::for_each(_functions.begin(), _functions.end(),
		              [](std::function<void()>& func)
		{
			func();
		});
	}
};

#define RUN_AFTER_PROGRAM_START(func) \
	__attribute__((unused)) static bool ___program_start = RunOnProgrammerStart::Instance().InsertFunc([](){ func });

#ifdef USE_CONVEX
#define CONVEX_MAP(name) name::map##name

#define LIST_DATA_BEGIN(id_space, name, cfg) \
        for (auto &pair : CONVEX_MAP(name)) \
        { \
                const name& cfg = *(pair.second.get()); \
		unsigned int id; \
		id = pair.first; \

#define LIST_DATA_END }

#define DATACOPY(src, dst) \
	assert(sizeof(src)/sizeof(src[0]) >= dst.size()); \
	memset(src, 0, sizeof(src)); \
	for(size_t iconvex = 0; iconvex < dst.size(); iconvex++) \
	{ \
		src[iconvex] = dst[iconvex]; \
	}

#define VECTOR_CHECK(v, i) \
	if(i >= v.size()) \
	{ \
		break; \
	}

#else
#define CONVEX_MAP(name)

#define LIST_DATA_BEGIN(id_space, name, cfg) \
	{DATA_TYPE dt; \
        unsigned int id; \
        unsigned int size; \
        const void *ptr = data_man.get_first_data(id_space, id, dt, size); \
        for (; ptr; ptr = data_man.get_next_data(id_space, id, dt, size)) \
        { \
                if (dt == DT_##name) \
                { \
                        const name& cfg = *(const name *)ptr;

#define LIST_DATA_END \
		} \
	}}

#define DATACOPY(src, dst) static_assert(sizeof(src) == sizeof(dst), "size unmatch");\
	 memcpy(src, dst, sizeof(dst));
#define DATA_ASSERT(src, dst) static_assert(sizeof(src) == sizeof(dst), "size unmatch");

#define VECTOR_CHECK(v, i)

#endif

class StringUtils
{
public:
	static std::vector<std::string> split(const std::string& str, char sep)
	{
		std::vector<std::string> result;
		if (str.empty())
		{
			return result;
		}
		std::string strstr = str + sep;
		size_t size = strstr.size();

		size_t  pos = 0;
		size_t i = 0;
		while (i < size)
		{
			pos = strstr.find(sep, i);
			if (pos != std::string::npos)
			{
				std::string temp = strstr.substr(i, pos - i);
				if (!temp.empty())
				{
					result.push_back(temp);
				}
				i = pos + 1;
			}
			else
			{
				break;
			}
		}
		return result;
	}

	static void GetMapStr(const std::map<int, int>& temp_map, std::string& str)
	{
		if (temp_map.empty())
		{
			return;
		}

		std::stringstream ss;
		ss << "<";
		for (std::map<int, int>::const_iterator it = temp_map.begin(); it != temp_map.end(); ++it)
		{
			ss << "(" << it->first << ", " << it->second << ")";
		}
		ss << ">";

		str = ss.str();
	}

	template<typename T>
	static void GetVectorStr(const std::vector<T>& arr, std::string& str)
	{
		if (arr.empty())
		{
			return;
		}

		std::stringstream ss;
		ss << "<";
		for (int i = 0; i < arr.size(); ++i)
		{
			if (i > 0)
			{
				ss << ", ";
			}
			ss << arr[i];
		}
		ss << ">";

		str = ss.str();
	}

	template<typename T>
	static void GetSetStr(const std::set<T>& arr, std::string& str)
	{
		if (arr.empty())
		{
			return;
		}

		std::stringstream ss;
		ss << "<";
		for (auto it = arr.begin(); it != arr.end(); ++it)
		{
			if (it != arr.begin())
			{
				ss << ", ";
			}
			ss << *it;
		}
		ss << ">";

		str = ss.str();
	}

	template<typename T1, typename T2>
	static void GetMapStr(const std::map<T1, T2>& arr, std::string& str)
	{
		if (arr.empty())
		{
			return;
		}

		std::stringstream ss;
		ss << "<";
		for (auto it = arr.begin(); it != arr.end(); ++it)
		{
			ss << "(" << it->first << "," << it->second << ")";
		}
		ss << ">";

		str = ss.str();
	}

	static void GetMapTupleStr(const std::map<int64_t, std::tuple<int64_t, int>>& arr, std::string& str)
	{
		if (arr.empty())
		{
			return;
		}

		std::stringstream ss;
		ss << "<";
		for (auto it = arr.begin(); it != arr.end(); ++it)
		{
			ss << "(" << it->first << ", " << std::get<0>(it->second) << ", " << std::get<1>(it->second) << ")";
		}
		ss << ">";

		str = ss.str();
	}

	static void GetMapVectorStr(const std::map<int, std::vector<int>>& temp_map, std::string& str)
	{
		if (temp_map.empty())
		{
			return;
		}

		std::stringstream ss;
		ss << "<";
		for (auto it = temp_map.begin(); it != temp_map.end(); ++it)
		{
			std::string vec_str;
			GetVectorStr<int>(it->second, vec_str);
			ss << "(" << it->first << ", " << vec_str << ")";
		}
		ss << ">";

		str = ss.str();
	}

	static std::string ASCIIToUpper(std::string& str)
	{
		std::transform(str.begin(), str.end(), str.begin(), [](unsigned char c)
		{
			return std::toupper(c);
		});
		return str;
	}

	static std::string ASCIIToLower(std::string& str)
	{
		std::transform(str.begin(), str.end(), str.begin(), [](unsigned char c)
		{
			return std::tolower(c);
		});
		return str;
	}
};

template<typename T>
inline int64_t GetRechargeInfoCanUseCash(T& recharge_info)
{
	auto pRechargeInfoBase = recharge_info.mutable_recharge_info_base();
	return pRechargeInfoBase->cash_add() + pRechargeInfoBase->cash_present() - pRechargeInfoBase->cash_used();
}

template <typename Key, typename Cmp, typename Alloc>
bool contains(const std::set<Key, Cmp, Alloc>& s, const Key& k)
{
	return s.find(k) != s.end();
}

template <typename Key, typename Cmp, typename Alloc>
bool contains(const std::unordered_set<Key, Cmp, Alloc>& s, const Key& k)
{
	return s.find(k) != s.end();
}

template <typename Key, typename Value, typename Cmp, typename Alloc>
bool contains(const std::map<Key, Value, Cmp, Alloc>& m, const Key& k)
{
	return m.find(k) != m.end();
}

template <typename Key, typename Value, typename Cmp, typename Alloc>
bool contains(const std::unordered_map<Key, Value, Cmp, Alloc>& m, const Key& k)
{
	return m.find(k) != m.end();
}

template <class T>
bool contains(const std::vector<T>& vec, const T& value)
{
	return std::find(vec.begin(), vec.end(), value) != vec.end();
}

inline std::string StripName(const char *str, int length, const char *substitute = "&")
{
	if (NULL == str)
	{
		return "";
	}
	std::string strTemp = "";
	char special_char = '|';
	char special_char2 = '\r';
	char special_char3 = '\n';
	for (int i = 0; i < length; ++i)
	{
		char a = str[i];
		if (a == special_char || a == special_char2 || a == special_char3)
		{
			strTemp += substitute;
			continue;
		}
		strTemp += str[i];
	}
	return strTemp;
}

inline unsigned char URLToHex(unsigned char x)
{
	return  x > 9 ? x + 55 : x + 48;
}

inline std::string UrlEncode2(const std::string& str)
{
	size_t length = str.length();
	std::string strTemp = "";
	strTemp.reserve(length * 3); //%16禁止
	for (size_t i = 0; i < length; i++)
	{
		if (isalnum((unsigned char)str[i]) ||
		        (str[i] == '-') ||
		        (str[i] == '_') ||
		        (str[i] == '.') ||
		        (str[i] == '~'))
		{
			strTemp += str[i];
		}
		else if (str[i] == ' ')
		{
			strTemp += "+";
		}
		else
		{
			strTemp += '%';
			strTemp += URLToHex((unsigned char)str[i] >> 4);
			strTemp += URLToHex((unsigned char)str[i] % 16);
		}
	}//for
	return strTemp;
}

using UICCallback = std::function<void(bool, const GNET::Octets&)>;

#define TEAM_CAPACITY  5
#define RAID_CAPACITY  10
#define RAID_TEAM_MASK 0x40000000
inline unsigned int AddRaidMask(unsigned int teamid)
{
	return teamid | RAID_TEAM_MASK;
}
inline unsigned int RemoveRaidMask(unsigned int teamid)
{
	return teamid & 0x3FFFFFFF;
}
inline bool IsRaidTeam(unsigned int teamid)
{
	return (teamid & RAID_TEAM_MASK) != 0;
}
inline int GetTeamCapacity(unsigned int teamid)
{
	return IsRaidTeam(teamid) ? RAID_CAPACITY : TEAM_CAPACITY;
}

//头文件end
#endif
