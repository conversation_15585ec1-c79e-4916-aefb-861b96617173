import "net_common.proto";
package PB;
//namespace
//支持 SPEED(速度快，占用最大空间) CODE_SIZE LITE_RUNTIME(效率最高，且占用空间少，不支持反射)
//option optimize_for = LITE_RUNTIME;	//不支持反射，代码和性能会提高不少
option optimize_for = SPEED;

/////////////////////////////////////////////////////////////////////////////////////////////
//
//  数据库用数据结构 (一些内嵌式数据结构也写到这个吧)
//
//用来存盘的数据结构,必须在这个命名空间下
//WARING::命名规则
//所有能够自动获取的类型必须包含一个 "optional GPS_TYPE type   = 1[default = GUT xxx];" 
//default用来标明这个msg的类型
//必须不能重复一一对应，这样可以自动建立一个type <--> msg的对应表 ,

//修饰 GPS_TYPE 的范围
enum GUT_MAX_ID_BIT{
	GUT_MAX_ID      = 1024;     //TYPE类型不能大于1024,否则不能适用接口直接存储数据库
	GUT_MAX_ID_BITS     = 10;
}

enum GPS_TYPE {
	GUT_UNDEFIED		= 0;		//未定义
	GUT_TEST_DATA 		= 1;		//测试用数据结构
	GUT_CORPS_STRUCT	= 2;		//军团数据结构	xxx_corps
	GUT_CORPS_MEMBER	= 3;		//军团成员数据结构 xxx_corps_member
	GUT_FACEBOOK		= 4;		//玩家的common社会关系	facebook里面的字段
	GUT_PLAYER_MEMSSAGE	= 5;		//每个玩家都有的通用杂项数据 xxx_player
	GUT_DB_TABLE		= 6;		//数据库存储key_id的数据
	GUT_NATION		= 7;		//国家数据
	GUT_NATION_WAR		= 8;		//国战数据
	GUT_ROLE_PBINFO		= 9;		//角色用定义数据兼容扩展
	GUT_HERO_PBINFOS	= 10;		//heros的数据信息
	GUT_GREETING_DATA	= 11;		//祝福消息
	GUT_PLAYER_MISC		= 12;		//玩家的Misc数据
	GUT_CARD_DATA		= 13;		//卡牌数据
	GUT_BLACK_SHOP_DATA	= 14;		//黑市数据
	GUT_DUKE_DATA		= 15;		//官职数据
	GUT_CLIMBTOWER_SHOP_DATA	= 16;		//闯天关神秘商店数据
	GUT_ACCOUNT_DS_DATA	= 17;		//账号级别DS管理数据 -- UserInfo 不能保证所有RoleInfo数据未失效
	GUT_ACCOUNT_GS_DATA	= 18;		//账号级别GS管理数据 -- Role上可以保证同一时间只有一个Role
	GUT_GROUP_STRUCT	= 19;		//群组聊天群
	//GUT_MIDAS_MULTI_FAIL	= 20;		//米大师相关信息
	//GUT_MIDAS_PIXIU		= 21;		//貔貅
	GUT_SECURE_IDIP		= 22;
	GUT_TOP_REWARD		= 23;		//开服排行榜奖励
	GUT_DS_SAVE_PLAYER_DATA	= 24;		//ds存储更新的角色数据
	GUT_TALISMAN_DATA	= 25;		//法宝数据
	GUT_RETINUE_GROUP_DATA	= 26;		//随从组合数据
	GUT_RETINUE_DATA	= 27;		//随从及阵法数据
	GUT_PRACTICE_DATA	= 28;		//修炼数据
	GUT_DYN_GIFTBAG_DATA	= 29;		//动态礼包数据
	GUT_RED_ENVELOPE 	= 30;		//红包
	GUT_TP_SPOUSE_INFO	= 31;		//配偶相关信息
	GUT_TP_PET_INFO		= 32;		//排行榜宠物信息
	GUT_PLAYER_HOMETOWN	= 33;		//玩家家园信息
	GUT_TP_CACHE		= 34;		//排行榜数据缓存
	GUT_TENCENT_GIFT_DATA 	= 35; 		//应用宝领取礼包记录信息
	GUT_PLAYER_FARM		= 36;		//玩家家园信息
	GUT_PLAYER_PARK_DATA	= 37;		//玩家乐园信息
	GUT_PLAYER_SLAVE_DATA	= 38;		//玩家家园仆从信息
	GUT_ACCOUNT_DB_DATA	= 39;		//账号级别DB管理数据 -- UserInfo
	GUT_ROAMER_INFO		= 40;		//同步slaver数据
	GUT_FASHION_INFO	= 41;		//玩家时装信息
	GUT_PHOTO_INFO		= 42;		//玩家自定义头像信息
	GUT_GFRIEND_LIST_PBINFO = 43;	//GFriendList协议pbinfo
	GUT_TP_GUARD_INFO		= 44;	//排行榜守护灵信息
	GUT_INTIMATE_DATA	= 45;		//羁绊数据
	GUT_COLLECTION_DATA	= 46;		//手办收藏数据
	GUT_MAILALL_DATA	= 47;		//全服邮件的pb数据
	GUT_SIMPLE_STOCK	= 48;		//伙伴事件之股票数据
	GUT_ROLE_BASE_INFO	= 49;		//GRoleBase上的玩家数据
	GUT_MINIGAME_DATA	= 50;		//小游戏存盘数据
	GUT_TOPLIST_ADDON_DATA	= 51;	//排行榜addon_data
	GUT_SECT_STRUCT		= 52;		//新师徒数据
	GUT_PLAYER_DICE_CARD_DATA = 53;	//幸运翻牌数据
	GUT_MAILBOX_DATA	= 54;		//邮箱的pb数据
	GUT_ROLE_STATUS_INFO = 55;      //GRoleStatus上的玩家数据
	GUT_CENTER_SERVICE	= 56;	//中心服服务数据
	GUT_GAUCTION_DATA	= 57;		//真拍卖行的pb数据
	GUT_CENTER_ARENA_TEAM = 58;		//中心服组队竞技场数据
	// 59已废弃，千万别用
	GUT_PLAYER_TICKET_INFO = 60;	//彩票数据
    GUT_ELIMINATE_GROUP_INFO = 61;  //淘汰赛战队数据
	GUT_NEW_AUCTION_PBINFO	= 62;	// 真拍卖行附加数据
}

message db_gauction_data
{
	optional GPS_TYPE type					= 1		[ default = GUT_GAUCTION_DATA ];
	optional int32 longyu_id				= 2;
}

message db_center_arena_team_his_rank_t
{
	optional int32 save_ts = 1;
	repeated db_arena_group_ranks arena_group_ranks	= 2;
}

message arenagroup_single_rank_t
{
	optional int32 score = 1		[ default = 0 ];
	optional int32 changetime = 2;
	optional arenagroup_rank_member_t info = 3; 
}

message db_center_arean_team_single_rank
{
	optional int32 season_count = 1;
	repeated arenagroup_single_rank_t records = 2;//名人堂所需数据
}

message db_center_arena_team
{
	optional GPS_TYPE type					= 1		[ default = GUT_CENTER_ARENA_TEAM ];
	optional int32 last_calc_timestamp		= 2		[ default = 0 ];
	message zone_info_t
	{
		optional int32 zoneid				= 1;
		optional int32 server_open_time		= 2;
		optional int32 arena_zone			= 3;
		optional int32 new_server_open_time	= 4;
	}
	repeated zone_info_t zones				= 3;
	repeated db_center_arena_team_his_rank_t his_ranks = 4; //历史排行榜数据
	optional db_center_arean_team_single_rank single_rank = 5; 
}

message db_center_service
{
	optional GPS_TYPE type					= 1		[ default = GUT_CENTER_SERVICE ];
	optional int32 center_master_zoneid		= 2		[ default = 0 ];
	message center_service_t
	{
		optional bytes service_id			= 1;
		optional int32 center_zoneid		= 2;
	}
	repeated center_service_t services		= 3;
}

message db_mailbox_data
{
	optional GPS_TYPE type					= 1		[ default = GUT_MAILBOX_DATA ];
	optional bytes corps_auction_mails		= 2;
	optional bytes un_recycle_mails			= 3; // 定长不循环邮箱，满了以后再加邮件返回失败
	optional bytes bouquet_mails			= 4; // 甜蜜花园花束邮件
	optional bytes pray_mails				= 5; // 玩家祈愿邮件
}

message friend_daily_amity_info {
	optional int64 friend_roleid			= 1;	// 好友roleid
	optional int32 kill_monster_amity		= 2;	// 当前杀怪累计好友度
};

message friend_roommate_info_t
{
	optional int64 friend_roleid = 1;
	optional int32 occupy_ts     = 2; // 入住时间
}

message gfriend_list_pbinfo {
	optional GPS_TYPE type								= 1 [default = GUT_GFRIEND_LIST_PBINFO];
	optional int64 last_amity_update_time				= 2;	// 好友度最后更新时间
	repeated friend_daily_amity_info daily_amity_info	= 3;	// 所有好友的type累计好友度
	repeated blessing_history_t  blessing_records		= 4;	// 收花记录，最近10条
	repeated friend_roommate_info_t roommates = 5; // 住在自己家的好友 
};

message db_roamer_info {
	optional GPS_TYPE type          = 1     [ default = GUT_ROAMER_INFO ];
	optional roamer_ds_info info        = 2; 
	optional bool logout            = 3; 
}

message player_slave_data {
        optional GPS_TYPE type          = 1 [default = GUT_PLAYER_SLAVE_DATA];
        repeated player_slave slaves    = 2;
        repeated bless_record records   = 3;
}

message db_table {
	optional GPS_TYPE type		= 1 [default = GUT_DB_TABLE];
	repeated int64 keys		= 2;
}

message test_data {
	optional GPS_TYPE type		= 1 [default = GUT_TEST_DATA];
	repeated int64 params		= 2;
	optional int32 param2		= 3;
	optional bytes info		= 4;	
}

message corps_struct {
        optional GPS_TYPE type          = 1 [ default = GUT_CORPS_STRUCT ];
        optional corps_struct_data data = 2;
}

message corps_member {
        optional GPS_TYPE type          = 1 [ default = GUT_CORPS_MEMBER ];
        optional corps_member_info info = 2;
}
message group_struct {
	optional GPS_TYPE type		= 1 [ default = GUT_GROUP_STRUCT ];
	optional group_struct_data data	= 2;
}
message face_book {
	optional GPS_TYPE type		= 1 [ default = GUT_FACEBOOK ];
	optional facebook_str corps	= 2;
	optional int64 hometown_id	= 3;
	optional name_ruid eliminate_group = 4;
	optional int64 temporary_team_id = 5;
	optional int64 pdd_team_id       = 6;
	optional harmonious_struct  harmonious_node = 7;
    optional int64 contract_hometown_id = 8 [ default = 0 ];
	optional name_ruid eliminate_group_2 = 9;
    optional int64 honey_garden_id = 10;
    optional int64 team_recharge_id = 11;
	optional int64 pve_intimate_friend = 12;
	optional int64 zspace_id           = 13;
	optional name_ruid eliminate_group_3 = 14;
}
message eliminate_group_guess_data
{
    repeated eliminate_group_guess_data_t   records     = 1;
    optional int32                          timestamp   = 2 [ default = 0 ];
}

enum ROLE_TRADE_STATUS
{
	RTS_CANCEL_REGISTER 	= 101; // 正常状态/取消登记, 可自由登录
	RTS_UNDER_REVIWE	= 200; // 审核期, 已在角色交易侧登记
	RTS_PUBLIC_NOTICE	= 300; // 公示期, 不允许登录, 不允许购买, 只能被查看
	RTS_ON_SHELF		= 400; // 上架期, 不允许登录, 可以被购买
	RTS_EXAMINATION 	= 500; // 考察期, 允许登录, 不允许敏感操作
	RTS_EXAMINATION_FAILED	= 501; // 考察不通过, 角色回滚到交易前状态, 处于考察不通过的角色不允许登陆
	RTS_TRADE_SUCCESS 	= 600; // 考察通过, 交易流程完成
}

message db_role_base_info
{
	optional GPS_TYPE type				= 1	[ default = GUT_ROLE_BASE_INFO ];
	optional corps_mail_records corps_mail		= 2;	//玩家社团邮件	
	optional int32 forbid_name			= 3;	//禁止玩家显示名字
	message plat_vip_info_t
	{
		optional int32 startup_vip_type      = 1;
		optional int32 startup_vip_expiry_ts = 2;
		optional int32 member_vip_type       = 3;
		optional int32 member_vip_expiry_ts  = 4;
	}
	optional plat_vip_info_t plat_vip_info = 4; // 平台vip信息 用来判断是否需要更新排行榜中的数据
	repeated int32 arena_minigame_awarded_stage = 5; //下游戏队长，已经领取过的排名奖励
	message hometown_nosale_furniture_t
	{
		optional int32 object_type    = 1;
		optional int32 object_count   = 2;
	}
	repeated hometown_nosale_furniture_t hometown_nosale_furniture = 6; // 不可回收的家具
	optional friend_thanks_giving_record thanksgiving	= 7;
    optional eliminate_group_guess_data     guess_data  = 8;
	repeated hometown_nosale_furniture_t contract_hometown_nosale_furniture = 9; // 不可回收的家具
	optional int64 forbid_time			= 10;	//禁止玩家显示名字的期限
	repeated investigation_info_t finish_investigations = 11; //答过的问券
	optional ROLE_TRADE_STATUS trade_status             = 12; //交易状态
}

message db_role_status_info
{
	optional GPS_TYPE type          = 1 [default = GUT_ROLE_STATUS_INFO];
	repeated uint32 fashion_ext     = 2;   // 时装图案信息 
	optional int32 cur_title_int32	= 3;   // 32位的当前称号id
	repeated uint64 fashion_offset  = 4;   // 时装便宜信息 
}

message player_com_data {
	optional GPS_TYPE type			= 1 [ default = GUT_PLAYER_MEMSSAGE ];
}

message officer_t {
	required int64  roleid		= 1;
	required bytes  name		= 2;
	optional int32	officer_title	= 3;	//官员头衔
	optional int32  appoint_cd	= 4;	//任命冷却
	optional int32  gift		= 5;	//官员福利
}

message nation_data_t {
	required int32 id		= 1;	//国家ID
	required int32 alliance		= 2;	//盟国
	repeated officer_t officers	= 3;	//官员
	required officer_t king		= 4;	//国王
	required bytes announce		= 5;	//宣言
	required int64 power_today	= 6;	//今日国力
	required int64 power_yesterday	= 7;	//昨日国力
	required int64 money		= 8;	//国库
	required int32 population	= 9;	//人口
	required int32 alliance_cool	= 10;	//结盟冷却
	repeated int64 power_add	= 11;	//计入今日国力的角色
	optional int32 next_king_update_time = 12;	//下一次更新国王时间
	optional int32 next_gift_update_time = 13;	//下一次发放福利时间
	repeated int64 weak_gift_receive_role= 14;	//已经领取了弱国奖励的角色
}

message nation_msg_t {
	required int32 msg_type		= 1;	//消息类型
	required int64 param1		= 2;	//消息参数1
	required int64 param2		= 3;	//消息参数2
	required int32 life		= 4;	//消息有效期
}

message nation_data {
	optional GPS_TYPE type		= 1 	[ default = GUT_NATION ];
	repeated nation_data_t nations	= 2;	//各国家数据
	repeated nation_msg_t message	= 3;	//为国王保存的事件通知
	repeated nation_war_history history = 4;//国战历史
	optional int32 next_create_war_time = 5;//下次生成国战时间
	optional int32 gift_cool_day 	= 6;	//gift_cool_role数据有效日期
	repeated int64 gift_cool_role	= 7;	//当日已领取官职福利的角色
	optional int32 last_save_time	= 8;	//上一次存盘时间
}

message nationwar_nation_t {
	required int32 id		= 1;	//国家ID
	required int32 xuanzhan_times	= 2;	//累计被宣战次数，每周清空
	required int32 last_attack_time = 3;	//最近一次国战进攻时间
	required int32 last_defend_time = 4;	//最近一次国战防守时间
}
message nationwar_war_t {
	required int32 id		= 1;	//国战id
	required int32 attack_nation	= 2;	//进攻方国家id
	required int32 defend_nation	= 3;	//防守方国家id
	required int32 begin_time	= 4;	//开战时间
}

message nation_war_data {
	optional GPS_TYPE type			= 1 	[ default = GUT_NATION_WAR ];
	repeated nationwar_nation_t nation_list	= 2;	//国战各国家数据
	repeated nationwar_war_t war_list	= 3;	//国战各国战数据
	required int32 function_open_time	= 4;	//进攻方国家id
	required int32 war_id_cursor		= 5;	//国战id分配变量
	required int32 weekly_reset_lasttime	= 6;	//最近一次周重置时间
	optional int32 war_function_type	= 7;	//国战功能类型
}

message gs_role_mutable_data {
        optional GPS_TYPE type                  = 1     [ default = GUT_ROLE_PBINFO ];
        optional role_mutable_data data         = 2;
}

message hero_pbinfos {
	optional GPS_TYPE type			= 1 	[ default = GUT_HERO_PBINFOS ];
	optional int32 level			= 2;
	optional int32 exp			= 3;
	optional int32 prof_level		= 4;
	optional int32 prof_exp			= 5;
	optional int32 quality			= 6;
	optional int32 hero_type		= 7;
	optional bool  is_summoned		= 8;	//是否被召唤
	optional int32 fightcapacity		= 9;
	optional int32 train_level		= 10;	//培养等级，（废弃）
	optional int32 cur_surface		= 11;	//当前幻化外观模板ID
	repeated int32 own_surfaces		= 12;
	repeated dyn_surface dyn_surfaces	= 13;
	optional uint32 can_fly			= 14;	//能否飞行	TODO:可能会废弃，能不能飞是写在surface上的
	optional uint32 can_double_fly		= 15;
	optional int32 stored_exp		= 16;
	optional int32 active_licence_index	= 17;	//已经激活的执照索引（废弃）
	optional int32 total_speed_up_percent	= 18;	//增加的速度（废弃）
	repeated surface_info surfaces		= 19;	
	optional int32 first_active_surface	= 20;	//记录玩家第一个激活的幻化ID，用于作为玩家的默认幻化
}

message db_greeting_data {
	optional GPS_TYPE type			= 1 	[ default = GUT_GREETING_DATA ];
	required int32		info_seq	= 2;	//消息序列号
	repeated greeting_info 	greeting_list	= 3;	//祝福消息
}

message db_card_data {
	optional GPS_TYPE type			= 1 	[ default = GUT_CARD_DATA ];
	repeated card_info cards		= 2;	//卡牌
	repeated int32 card_suits		= 3;	//激活的套牌
}

message db_talisman_data {
	optional GPS_TYPE type				= 1 	[ default = GUT_TALISMAN_DATA ];
	repeated talisman_info talismans		= 2;	//法宝
	repeated talisman_combination_info combinations	= 3;	//法宝组合
}

message db_retinue_group_data {
	optional GPS_TYPE type				= 1 	[ default = GUT_RETINUE_GROUP_DATA];
	repeated retinue_group_info retinue_groups	= 2;	//法宝
}

message db_retinue_friend_info
{
	optional int32 retinue_id						= 1;
	optional int32 amity							= 2 [ default = 0];             //伙伴好友度
	optional retinue_chat cur_chat					= 3;                   //当前聊天
	repeated retinue_chat finish_chats				= 4;                   //已经完成的chatid
	optional int32 mood								= 5;                   //伙伴心情
	optional int32 mood_timestamp					= 6;                   //心情改变时间
	optional int32 take_gift_level					= 7;                   //从伙伴收礼完成的等级
	repeated retinue_diary_info unlock_diary		= 8;    //伙伴已解锁的日记
	optional int32 active_gift_count_today          = 9;
	optional int32 active_gift_timestamp            = 10;
	optional int32 take_gift_mask			= 11;  //替换伙伴收礼等级字段
}

message db_retinue_data {
	optional GPS_TYPE type				= 1	[ default = GUT_RETINUE_DATA];
	repeated retinue_info retinues		= 2;	//随从 
	optional retinue_formation_info formation= 3;	//当前的阵法
	repeated int32 formation_owned		= 4;	//激活过的阵法
	repeated retinue_task_info tasks	= 5;	//正在进行的任务
	repeated retinue_private_info privates	= 6;	//随从私有物
	optional int32 select_id			= 7;	//当前出战同伴
	optional int32 next_ex_task_time	= 8;	//下次紧急任务出现时间
	optional int32 ex_task_id			= 9;	//当前可接的紧急任务id
	optional retinue_task_info ex_task	= 10;	//已经接取的紧急任务
	optional int32 recruit_pay_total	= 11;	//付费招募付费总次数
	optional int32 recruit_pay_last		= 12;	//付费招募付费次数 抽到6星清0
	optional int32 ex_task_deadline		= 13;	//当前紧急任务过期时间
	optional int32 recruit_pay_today	= 14;	//付费招募今日次数
	optional int32 recruit_pay_tm       = 15;   //付费招募时间戳
	repeated retinue_private_info gifts	= 16;   //礼物数据
	repeated int32 trigger_events		= 17;	//所有激活过的事件key 不清除
	repeated int32 active_ss_status		= 18;	//已激活的朋友圈id
	repeated int32 active_npc_ss_status = 19;	//已激活的伙伴朋友圈id
	repeated retinue_ss_status_info finish_ss_status = 20;	//已完成的朋友圈(包含自己的和伙伴的)
	repeated retinue_ss_status_info cur_status = 21;//当前正在进行的朋友圈
	repeated int32 active_chats			= 22;   //当前激活的对话id
	repeated db_retinue_friend_info friends	= 23;	//好友数据
	optional int32 last_confirm_ss_notify_time = 24; //客户端最后一次确认朋友圈通知的时间
	optional int32 last_ss_notify_time = 25; //最后发送朋友圈红点通知的时间
	optional int32 recruit_free_total       = 26;   //伙伴招募免费总次数
	optional int32 recruit_pay_6_total      = 27;   //付费招募付费抽到6星总次数
	optional int32 recruit_10_mask		= 28;   //首次十连抽mask
	optional int32 assist_combat_unlock_mask = 29;	//解锁的辅战位
	repeated int32 assist_combat_retinues	= 30;	//辅战位伙伴
	optional int32 recruit_10_mask_new	= 31 [ default = 0 ];   //首次十连抽mask新
	repeated common_item_info special_retinues      = 32; //伙伴经验卡
	optional bytes recruit_10_mask_2	= 33;   //首次十连抽mask-2
}

message db_practice_data 
{
	optional GPS_TYPE type			= 1 	[ default = GUT_PRACTICE_DATA ];
	repeated practice_info practice_skills	= 2;	//修炼技能
}

message db_climbtower_shop_data
{
	optional GPS_TYPE type			= 1 	[ default = GUT_CLIMBTOWER_SHOP_DATA ];
	repeated climbtower_magic_shop_info climbtower_shops	= 2;	//闯天关神秘商店数据
}
message db_black_shop_data {
	optional GPS_TYPE type			= 1 	[ default = GUT_BLACK_SHOP_DATA ];
	repeated black_shop_info shops		= 2;	//黑市商店数据
}

message db_duke_data {
	optional GPS_TYPE type			= 1 	[ default = GUT_DUKE_DATA ];
	required int32 duke_level		= 2;
	required int32 duke_active_skill	= 3;
	required int32 reward_count		= 4;
}

message db_climb_tower_data {
	required int32 max_tower_lvl		= 1;	//最高挑战关卡
	required int32 cur_tower_lvl		= 2;	//当前挑战关卡
	required bool  is_auto_running		= 4;	//是否自动挑战中
	required int32 auto_begin_time		= 5;	//自动挑战开始时间
	required int32 auto_begin_lvl		= 6;	//自动挑战开始关卡
	required int32 auto_reward_begin	= 7;	//自动挑战奖励开始关卡
	required int32 auto_reward_end		= 8;	//自动挑战奖励结束关卡
	required int32 auto_cur_lvl		= 9;	//自动挑战当前关卡
	repeated level_shopid level4shopid	= 10;  //活动奖励的当前关卡的level 
}

message db_grc_gift_info {
	optional bool grc_flower_switch		= 1 [default = true];//默认开
	optional bool grc_manual_switch		= 2 [default = true];
}

message db_secure_idip_command {
	optional int32	c_status	= 1;
	optional int32	c_sn		= 2;
	optional int32	c_type		= 3;
	optional int64	c_para1		= 4;
	optional int64	c_para2		= 5;
	optional bytes	c_para3		= 6;
}
message db_secure_idip{
	optional GPS_TYPE type			= 1 [ default = GUT_SECURE_IDIP ];
	repeated db_secure_idip_command	c_list	= 2;
	optional int32	sn_number		= 3;
}

message db_account_ds_data {
	optional GPS_TYPE type			= 1 [default = GUT_ACCOUNT_DS_DATA ];
	optional int32 reserve			= 2;	//保证有数据
	optional db_grc_gift_info grc_info	= 3;
	optional bool ready			= 4 [default = false];
	optional db_recharge_info recharge_info = 5;
	optional db_bind_mobile_phone_info  bind_mobile_info = 6; 
	optional int32 tencent_gift_info 	= 7 [default = 0];
	optional db_rename_info rename_info 	= 8;
	optional int32 auction_verify_count		= 9;
	optional int32 plat_vip_type = 10; // 掩码
	optional int32 plat_vip_time_stamp = 11; // 平台启动vip 获得时的时间
	optional int32 plat_member_vip_expiry_ts = 12; // 平台会员vip 有效期
	optional bytes register_channel = 13; // 注册渠道
	optional bytes login_channel = 14; // 登录渠道
	optional int32 queue_client_abtest_result = 15; //0 未初始化 1 开 2 关
	optional int32 credit_value	= 16 [default = -1];
	optional int32 delete_time = 17 [default = 0];
}

message db_account_trade_role_history
{
	optional int64 role_id = 1;		//交易出去的角色id
	optional int64 transfer_timestamp = 2; 	//交易达成时的时间戳
	optional int64 backup_role_id = 3;	//交易出去的角色在交易时间的备份数据的roleid
}

message db_account_db_data {
	optional GPS_TYPE type			= 1 [default = GUT_ACCOUNT_DB_DATA ];
	optional db_refence_db_info refence_db_info = 2;
	repeated int64 delete_rolelist = 3;
	repeated db_account_trade_role_history trade_roles = 4; //交易角色的历史数据
}

message tencent_gift_data {
	optional GPS_TYPE type          = 1 [default = GUT_TENCENT_GIFT_DATA ];
	optional int32 tencent_gift_info = 2 [default = 0];
}

message account_key_value {
	enum KEY_TYPE {
		KT_FIRST_RECHARGE_REWARD_MASK   = 1;    //首冲奖励领取次数 
		KT_DAY_RECHARGE_REWARD_MASK = 2;    //每日充值奖励领取次数 
		KT_DAY_CONSUME_REWARD_MASK  = 3;    //每日消费领取次数 
		KT_WEEK_CARD_REWARD_MASK    = 4;    //周卡领取次数 
		KT_MONTH_CARD_REWARD_MASK   = 5;    //月卡领取次数 
		KT_DAY_CONSUME_COUNT_MASK   = 6;    //每日消费数量
		KT_DAY_FUND_REWARD_MASK		= 7;	//每日成长奖励
		KT_DAY_FUND_LOGIN_MASK		= 8;	//每日成长奖励登陆天数
		KT_TOTAL_CONSUME_COUNT_MASK = 9;	//总消费数量
		KT_VIP_EXP_BY_ITEM			= 10;	//通过使用物品获得的vip经验		
		KT_TIME_LIMIT_CONSUME_COUNT_MASK = 11; //限时累计消费声望
		KT_SUIXIN_REWARD_MASK		= 12;	//随心好礼每日领取奖励次数
		KT_OLDPLAYER_BACK_LOGIN_MASK = 	13; //老玩家回归专属活动账号累计登录天数
		KT_OLDPLAYER_BACK_SUIXIN_MASK	= 14; //老玩家回归随心购是否已领奖
		KT_CRAZY_CARD_REWARD_MASK1   	= 15;    //设置时间 新年狂欢卡1今天是否已领
		KT_CRAZY_CARD_REWARD_MASK2      = 16;    //设置时间 新年狂欢卡2今天是否已领
		KT_CRAZY_CARD_REWARD_MASK3      = 17;    //设置时间 新年狂欢卡3今天是否已领
		KT_CRAZY_CARD_REWARD_MASK4      = 18;    //设置时间 新年狂欢卡4今天是否已领
		KT_CRAZY_CARD_REWARD_MASK5      = 19;    //设置时间 新年狂欢卡5今天是否已领
		KT_REFENCE_GIFT_AWARD_COUNT_MASK= 20;    //领取的推广礼包个数
		KT_REFENCE_CASH_AWARD_MASK	= 21;    //领取的推广返利数量
		KT_PLAYER_STATUS_TIMESTAMP      = 22;   //玩家状态日志的时间戳
		KT_ZUL_HEARTBEAR_TIMESTAMP      = 23;   //祖龙心跳日志的时间戳
		KT_PRECREATE_SERVER_SHARE_COUNT = 24;	//预创建服上分享成功次数 用作 日服充值数转换 ！！！！

		KT_MAINTAIN_CAMPAIGN_REWARD1    = 50;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD2    = 51;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD3    = 52;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD4    = 53;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD5    = 54;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD6    = 55;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD7    = 56;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD8    = 57;   //维护礼包活动奖励领取次数 
		KT_MAINTAIN_CAMPAIGN_REWARD9    = 58;   //维护礼包活动奖励领取次数

		KT_TSS_GIFTS_1					= 60;	//TSS礼包领取次数
		KT_TSS_GIFTS_2					= 61;	//TSS礼包领取次数
		KT_TSS_GIFTS_3					= 62;	//TSS礼包领取次数
		KT_TSS_GIFTS_4					= 63;	//TSS礼包领取次数
		KT_TSS_GIFTS_5					= 64;	//TSS礼包领取次数
		KT_TSS_GIFTS_6					= 65;	//TSS礼包领取次数
		KT_TSS_GIFTS_7					= 66;	//TSS礼包领取次数
		KT_TSS_GIFTS_8					= 67;	//TSS礼包领取次数
		KT_TSS_GIFTS_9					= 68;	//TSS礼包领取次数
		KT_TSS_GIFTS_10					= 69;	//TSS礼包领取次数

		KT_TSS_LEVEL_FUND_REWARD_1		= 70;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_2		= 71;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_3		= 72;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_4		= 73;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_5		= 74;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_6		= 75;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_7		= 76;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_8		= 77;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_9		= 78;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_10		= 79;	//腾讯等级成长基金领取次数

		KT_TSS_LEVEL_FUND_REWARD_LEVEL_1	= 80;	//腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_2	= 81;	//腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_3	= 82;	//腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_4	= 83;	//腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_5	= 84;	//腾讯等级成长基金领取到的等级

		KT_MAIN_ROLE_ID                 = 100;  //账号上等级最高角色的roleid
		KT_MAIN_ROLE_LEVEL              = 101;  //账号上等级最高角色的等级

		KT_VIP_DAILY_GIFTBAG            = 102;  //vip每日礼包领取时间
		KT_VIP_WEEKLY_GIFTBAG           = 103;  //vip每周礼包领取时间

		KT_FRIEND_HELP_REWARD_1			= 105;	//好友助力领取阶段
		KT_FRIEND_HELP_REWARD_2			= 106;	//好友助力领取阶段
		KT_FRIEND_HELP_REWARD_3			= 107;	//好友助力领取阶段
		KT_FRIEND_HELP_REWARD_4			= 108;	//好友助力领取阶段
		KT_FRIEND_HELP_REWARD_5			= 109;	//好友助力领取阶段

		KT_TSS_GIFTS_11					= 110;	//TSS礼包领取次数
		KT_TSS_GIFTS_12					= 111;	//TSS礼包领取次数
		KT_TSS_GIFTS_13					= 112;	//TSS礼包领取次数
		KT_TSS_GIFTS_14					= 113;	//TSS礼包领取次数
		KT_TSS_GIFTS_15					= 114;	//TSS礼包领取次数
		KT_TSS_GIFTS_16					= 115;	//TSS礼包领取次数
		KT_TSS_GIFTS_17					= 116;	//TSS礼包领取次数
		KT_TSS_GIFTS_18					= 117;	//TSS礼包领取次数
		KT_TSS_GIFTS_19					= 118;	//TSS礼包领取次数
		KT_TSS_GIFTS_20					= 119;	//TSS礼包领取次数

		KT_PLAYER_AGE_INFO                              = 120;  //玩家年龄信息(日本用)

		KT_DAY_RECHARGE_GIFT_1			= 121;	//每日充值任选礼包1
		KT_DAY_RECHARGE_GIFT_2			= 122;	//每日充值任选礼包2
		KT_DAY_RECHARGE_GIFT_3			= 123;	//每日充值任选礼包3
		KT_DAY_RECHARGE_GIFT_4			= 124;	//每日充值任选礼包4
		KT_DAY_RECHARGE_GIFT_5			= 125;	//每日充值任选礼包5

		KT_TRIGGER_GIFT_PUBLIC_CD		= 130;	//触发奖励共享CD
		KT_TRIGGER_GIFT_LIMIT_1			= 131;	//触发奖励限制1
		KT_TRIGGER_GIFT_LIMIT_2			= 132;	//触发奖励限制2
		KT_TRIGGER_GIFT_LIMIT_3			= 133;	//触发奖励限制3
		KT_ACCOUNT_PURSUE                       = 134;  //账号追缴信息(点券)
		KT_TSS_FIRST_RECHARGE_2			= 135;	//腾讯首充2

		KT_RECHARGE_LIMIT_ACTIVITY		= 136;	//限时累计充值版本活动
		KT_RECHARGE_LIMIT_INDEX			= 137;	//限时累计充值领取奖励阶段
		KT_RECHARGE_LIMIT_BEGIN_VALUE	= 138;	//限时累计充值开始时候累计充值
		KT_FIRST_RECHARGE_DOUBLE_MASK		= 139;  //海外首充双倍奖励领取记录
		KT_FIRST_RECHARGE_DOUBLE_VERSON		= 140;	//海外首充双倍版本号

		KT_TSS_LEVEL_FUND_REWARD_LEVEL_6        = 141;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_7        = 142;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_8        = 143;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_9        = 144;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_10       = 145;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_11       = 146;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_12       = 147;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_13       = 148;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_14       = 149;  //腾讯等级成长基金领取到的等级
		KT_TSS_LEVEL_FUND_REWARD_LEVEL_15       = 150;  //腾讯等级成长基金领取到的等级
		KT_SPECIAL_WEEK_CARD                    = 151;  //周卡充值天数记录
		KT_SPECIAL_WEEK_CARD_REWARD             = 152;  //周卡每个账号只能领一次钻石记录
		KT_PC_LOGIN								= 153;	// pc登录奖励
		KT_LOTTERY_TRIGGER_GIFT_1				= 154;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_2				= 155;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_3				= 156;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_4				= 157;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_5				= 158;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_6				= 159;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_7				= 160;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_8				= 161;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_9				= 162;  //彩票抽奖触发礼包购买
		KT_LOTTERY_TRIGGER_GIFT_10				= 163;  //彩票抽奖触发礼包购买
		KT_MTDTR_ACTIVITY_ID_1                  = 164;  //商城触发礼包活动id
		KT_MTDTR_DAYS_1							= 165;  //商城触发礼包天数
		KT_MTDTR_RECEIVE_ID_1                      = 166;  //商城触发礼包领取索引
		KT_MTDTR_ACTIVITY_ID_2                  = 167;  //商城触发礼包活动id
		KT_MTDTR_DAYS_2							= 168;  //商城触发礼包天数
		KT_MTDTR_RECEIVE_ID_2                      = 169;  //商城触发礼包领取索引
		KT_MTDTR_ACTIVITY_ID_3                  = 170;  //商城触发礼包活动id
		KT_MTDTR_DAYS_3							= 171;  //商城触发礼包天数
		KT_MTDTR_RECEIVE_ID_3                      = 172;  //商城触发礼包领取索引
		KT_MTDTR_ACTIVITY_ID_4                  = 173;  //商城触发礼包活动id
		KT_MTDTR_DAYS_4							= 174;  //商城触发礼包天数
		KT_MTDTR_RECEIVE_ID_4                      = 175;  //商城触发礼包领取索引
		KT_MTDTR_ACTIVITY_ID_5                  = 176;  //商城触发礼包活动id
		KT_MTDTR_DAYS_5							= 177;  //商城触发礼包天数
		KT_MTDTR_RECEIVE_ID_5                      = 178;  //商城触发礼包领取索引
		KT_MTDTR_ACTIVITY_ID_6                  = 179;  //商城触发礼包活动id
		KT_MTDTR_DAYS_6							= 180;  //商城触发礼包天数
		KT_MTDTR_RECEIVE_ID_6                      = 181;  //商城触发礼包领取索引
		KT_MTDR_ACTIVITY_ID_1                  = 182;  //商城触发礼包活动id
		KT_MTDR_DAYS_1							= 183;  //商城触发礼包天数
		KT_MTDR_RECEIVE_ID_1                      = 184;  //商城触发礼包领取索引
		KT_MTDR_ACTIVITY_ID_2                  = 185;  //商城触发礼包活动id
		KT_MTDR_DAYS_2							= 186;  //商城触发礼包天数
		KT_MTDR_RECEIVE_ID_2                      = 187;  //商城触发礼包领取索引
		KT_MTDR_ACTIVITY_ID_3                  = 188;  //商城触发礼包活动id
		KT_MTDR_DAYS_3							= 189;  //商城触发礼包天数
		KT_MTDR_RECEIVE_ID_3                      = 190;  //商城触发礼包领取索引
		KT_MTDR_ACTIVITY_ID_4                  = 191;  //商城触发礼包活动id
		KT_MTDR_DAYS_4							= 192;  //商城触发礼包天数
		KT_MTDR_RECEIVE_ID_4                      = 193;  //商城触发礼包领取索引
		KT_MTDR_ACTIVITY_ID_5                  = 194;  //商城触发礼包活动id
		KT_MTDR_DAYS_5							= 195;  //商城触发礼包天数
		KT_MTDR_RECEIVE_ID_5                      = 196;  //商城触发礼包领取索引
		KT_MTDR_ACTIVITY_ID_6                  = 197;  //商城触发礼包活动id
		KT_MTDR_DAYS_6							= 198;  //商城触发礼包天数
		KT_MTDR_RECEIVE_ID_6                      = 199;  //商城触发礼包领取索引
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_1                  = 200;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_1                   = 201;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_1                   = 202;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_2                  = 203;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_2                   = 204;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_2                   = 205;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_3                  = 206;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_3                   = 207;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_3                   = 208;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_4                  = 209;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_4                   = 210;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_4                   = 211;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_5                  = 212;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_5                   = 213;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_5                   = 214;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_6                  = 215;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_6                   = 216;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_6                   = 217;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_7                  = 218;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_7                   = 219;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_7                   = 220;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_8                  = 221;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_8                   = 222;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_8                   = 223;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_9                  = 224;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_9                   = 225;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_9                   = 226;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_10                  = 227;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_10                   = 228;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_10                   = 229;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_11                  = 230;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_11                   = 231;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_11                   = 232;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_12                  = 233;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_12                   = 234;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_12                   = 235;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_13                  = 236;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_13                   = 237;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_13                   = 238;  //
		KT_RECHARGE_DAYS_LIMIT_ACTIVITY_14                  = 239;  //
		KT_RECHARGE_DAYS_LIMIT_INDEX_14                   = 240;  //
		KT_RECHARGE_DAYS_LIMIT_VALUE_14                  = 241;  //
		KT_TEAM_RECHARGE_BEGIN_VALUE					= 242; // 组队充值的初始累计充值记录
		KT_TEAM_RECHARGE_ACTIVITY						= 243; // 组队充值的活动id
		KT_TEAM_RECHARGE_COUNT							= 244; // 组队充值的队伍充值数
		KT_REFENCE_ACTIVITYID							= 245; // 当前推广活动id
		KT_REFENCE_GET_CALL_GIFT_AWARD_MASK				= 246; // 已领取的召回礼包档位数
		KT_REFENCE_GET_CALL_CASH_AWARD_MASK				= 247; // 已领取的召回返利数
		KT_REFENCE_USED_INVITE_PLAYER_SCORE				= 248; // 已经消费的邀请好友积分
		KT_MTDTR_DIRECT_RECHARGE_ROLEID_1                = 249; //商城触发累计礼包1付费玩家roleid
		KT_MTDTR_DIRECT_RECHARGE_ROLEID_2                = 250; 
		KT_MTDTR_DIRECT_RECHARGE_ROLEID_3                = 251; 
		KT_MTDTR_DIRECT_RECHARGE_ROLEID_4                = 252; 
		KT_MTDTR_DIRECT_RECHARGE_ROLEID_5                = 253; 
		KT_MTDTR_DIRECT_RECHARGE_ROLEID_6                = 254; 

		KT_MTDR_DIRECT_RECHARGE_ROLEID_1                = 255; //商城触发每日礼包1付费玩家roleid
		KT_MTDR_DIRECT_RECHARGE_ROLEID_2                = 256; 
		KT_MTDR_DIRECT_RECHARGE_ROLEID_3                = 257; 
		KT_MTDR_DIRECT_RECHARGE_ROLEID_4                = 258; 
		KT_MTDR_DIRECT_RECHARGE_ROLEID_5                = 259; 
		KT_MTDR_DIRECT_RECHARGE_ROLEID_6                = 260; 

        // 限时特典扩档：直购
        KT_MTDTR_ACTIVITY_ID_7                          = 261;  //商城触发礼包活动id
        KT_MTDTR_DAYS_7                                 = 262;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_7                           = 263;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_8                          = 264;  //商城触发礼包活动id
        KT_MTDTR_DAYS_8                                 = 265;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_8                           = 266;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_9                          = 267;  //商城触发礼包活动id
        KT_MTDTR_DAYS_9                                 = 268;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_9                           = 269;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_10                         = 270;  //商城触发礼包活动id
        KT_MTDTR_DAYS_10                                = 271;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_10                          = 272;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_11                         = 273;  //商城触发礼包活动id
        KT_MTDTR_DAYS_11                                = 274;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_11                          = 275;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_12                         = 276;  //商城触发礼包活动id
        KT_MTDTR_DAYS_12                                = 277;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_12                          = 278;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_13                         = 279;  //商城触发礼包活动id
        KT_MTDTR_DAYS_13                                = 280;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_13                          = 281;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_14                         = 282;  //商城触发礼包活动id
        KT_MTDTR_DAYS_14                                = 283;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_14                          = 284;  //商城触发礼包领取索引
        KT_MTDTR_ACTIVITY_ID_15                         = 285;  //商城触发礼包活动id
        KT_MTDTR_DAYS_15                                = 286;  //商城触发礼包天数
        KT_MTDTR_RECEIVE_ID_15                          = 287;  //商城触发礼包领取索引
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_7               = 288;  //商城触发每日礼包7付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_8               = 289;  //商城触发每日礼包8付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_9               = 290;  //商城触发每日礼包9付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_10              = 291;  //商城触发每日礼包10付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_11              = 292;  //商城触发每日礼包11付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_12              = 293;  //商城触发每日礼包12付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_13              = 294;  //商城触发每日礼包13付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_14              = 295;  //商城触发每日礼包14付费玩家roleid
        KT_MTDTR_DIRECT_RECHARGE_ROLEID_15              = 296;  //商城触发每日礼包15付费玩家roleid

        KT_MTDR_ACTIVITY_ID_7                           = 297;  //商城触发礼包活动id
        KT_MTDR_DAYS_7                                  = 298;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_7                            = 299;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_8                           = 300;  //商城触发礼包活动id
        KT_MTDR_DAYS_8                                  = 301;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_8                            = 302;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_9                           = 303;  //商城触发礼包活动id
        KT_MTDR_DAYS_9                                  = 304;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_9                            = 305;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_10                          = 306;  //商城触发礼包活动id
        KT_MTDR_DAYS_10                                 = 307;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_10                           = 308;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_11                          = 309;  //商城触发礼包活动id
        KT_MTDR_DAYS_11                                 = 310;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_11                           = 311;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_12                          = 312;  //商城触发礼包活动id
        KT_MTDR_DAYS_12                                 = 313;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_12                           = 314;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_13                          = 315;  //商城触发礼包活动id
        KT_MTDR_DAYS_13                                 = 316;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_13                           = 317;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_14                          = 318;  //商城触发礼包活动id
        KT_MTDR_DAYS_14                                 = 319;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_14                           = 320;  //商城触发礼包领取索引
        KT_MTDR_ACTIVITY_ID_15                          = 321;  //商城触发礼包活动id
        KT_MTDR_DAYS_15                                 = 322;  //商城触发礼包天数
        KT_MTDR_RECEIVE_ID_15                           = 323;  //商城触发礼包领取索引
        KT_MTDR_DIRECT_RECHARGE_ROLEID_7                = 324;  //商城触发每日礼包7付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_8                = 325;  //商城触发每日礼包8付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_9                = 326;  //商城触发每日礼包9付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_10               = 327;  //商城触发每日礼包10付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_11               = 328;  //商城触发每日礼包11付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_12               = 329;  //商城触发每日礼包12付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_13               = 330;  //商城触发每日礼包13付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_14               = 331;  //商城触发每日礼包14付费玩家roleid
        KT_MTDR_DIRECT_RECHARGE_ROLEID_15               = 332;  //商城触发每日礼包15付费玩家roleid
		
		// 开服特典
        KT_RODDT_ACTIVITY_ID_1                          = 333;  //直购礼包活动id
        KT_RODDT_DAYS_1                                 = 334;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_1                           = 335;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_1               = 336;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_2                          = 337;  //直购礼包活动id
        KT_RODDT_DAYS_2                                 = 338;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_2                           = 339;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_2               = 340;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_3                          = 341;  //直购礼包活动id
        KT_RODDT_DAYS_3                                 = 342;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_3                           = 343;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_3               = 344;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_4                          = 345;  //直购礼包活动id
        KT_RODDT_DAYS_4                                 = 346;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_4                           = 347;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_4               = 348;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_5                          = 349;  //直购礼包活动id
        KT_RODDT_DAYS_5                                 = 350;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_5                           = 351;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_5               = 352;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_6                          = 353;  //直购礼包活动id
        KT_RODDT_DAYS_6                                 = 354;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_6                           = 355;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_6               = 356;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_7                          = 357;  //直购礼包活动id
        KT_RODDT_DAYS_7                                 = 358;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_7                           = 359;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_7               = 360;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_8                          = 361;  //直购礼包活动id
        KT_RODDT_DAYS_8                                 = 362;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_8                           = 363;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_8               = 364;  //直购礼包付费玩家roleid
        KT_RODDT_ACTIVITY_ID_9                          = 365;  //直购礼包活动id
        KT_RODDT_DAYS_9                                 = 366;  //直购礼包天数
        KT_RODDT_RECEIVE_ID_9                           = 367;  //直购礼包领取索引
        KT_RODDT_DIRECT_RECHARGE_ROLEID_9               = 368;  //直购礼包付费玩家roleid

        KT_RODD_ACTIVITY_ID_1                           = 369;  //直购礼包活动id
        KT_RODD_DAYS_1                                  = 370;  //直购礼包天数
        KT_RODD_RECEIVE_ID_1                            = 371;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_1                = 372;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_2                           = 373;  //直购礼包活动id
        KT_RODD_DAYS_2                                  = 374;  //直购礼包天数
        KT_RODD_RECEIVE_ID_2                            = 375;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_2                = 376;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_3                           = 377;  //直购礼包活动id
        KT_RODD_DAYS_3                                  = 378;  //直购礼包天数
        KT_RODD_RECEIVE_ID_3                            = 379;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_3                = 380;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_4                           = 381;  //直购礼包活动id
        KT_RODD_DAYS_4                                  = 382;  //直购礼包天数
        KT_RODD_RECEIVE_ID_4                            = 383;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_4                = 384;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_5                           = 385;  //直购礼包活动id
        KT_RODD_DAYS_5                                  = 386;  //直购礼包天数
        KT_RODD_RECEIVE_ID_5                            = 387;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_5                = 388;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_6                           = 389;  //直购礼包活动id
        KT_RODD_DAYS_6                                  = 390;  //直购礼包天数
        KT_RODD_RECEIVE_ID_6                            = 391;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_6                = 392;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_7                           = 393;  //直购礼包活动id
        KT_RODD_DAYS_7                                  = 394;  //直购礼包天数
        KT_RODD_RECEIVE_ID_7                            = 395;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_7                = 396;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_8                           = 397;  //直购礼包活动id
        KT_RODD_DAYS_8                                  = 398;  //直购礼包天数
        KT_RODD_RECEIVE_ID_8                            = 399;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_8                = 400;  //直购礼包付费玩家roleid
        KT_RODD_ACTIVITY_ID_9                           = 401;  //直购礼包活动id
        KT_RODD_DAYS_9                                  = 402;  //直购礼包天数
        KT_RODD_RECEIVE_ID_9                            = 403;  //直购礼包领取索引
        KT_RODD_DIRECT_RECHARGE_ROLEID_9                = 404;  //直购礼包付费玩家roleid
		
		KT_TSS_LEVEL_FUND_REWARD_11		= 405;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_12		= 406;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_13		= 407;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_14		= 408;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_15		= 409;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_16		= 410;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_17		= 411;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_18		= 412;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_19		= 413;	//腾讯等级成长基金领取次数
		KT_TSS_LEVEL_FUND_REWARD_20		= 414;	//腾讯等级成长基金领取次数
		
		// 战力基金 免费档位索引
		KT_CPR_RANK_INDEX_1				= 415;
		KT_CPR_RANK_INDEX_2				= 416;
		KT_CPR_RANK_INDEX_3				= 417;
		KT_CPR_RANK_INDEX_4				= 418;
		KT_CPR_RANK_INDEX_5				= 419;
		KT_CPR_RANK_INDEX_6				= 420;
		KT_CPR_RANK_INDEX_7				= 421;
		KT_CPR_RANK_INDEX_8				= 422;
		KT_CPR_RANK_INDEX_9				= 423;
		KT_CPR_RANK_INDEX_10			= 424;
		
		// 战力基金 点券礼包索引
		KT_CPR_ACCEL_INDEX_1			= 425;
		KT_CPR_ACCEL_INDEX_2			= 426;
		KT_CPR_ACCEL_INDEX_3			= 427;
		KT_CPR_ACCEL_INDEX_4			= 428;
		KT_CPR_ACCEL_INDEX_5			= 429;
		KT_CPR_ACCEL_INDEX_6			= 430;
		KT_CPR_ACCEL_INDEX_7			= 431;
		KT_CPR_ACCEL_INDEX_8			= 432;
		KT_CPR_ACCEL_INDEX_9			= 433;
		KT_CPR_ACCEL_INDEX_10			= 434;

		// 战力基金 免费档位时间戳
		KT_CPR_RANK_TIMESTAMP_1			= 435;
		KT_CPR_RANK_TIMESTAMP_2			= 436;
		KT_CPR_RANK_TIMESTAMP_3			= 437;
		KT_CPR_RANK_TIMESTAMP_4			= 438;
		KT_CPR_RANK_TIMESTAMP_5			= 439;
		KT_CPR_RANK_TIMESTAMP_6			= 440;
		KT_CPR_RANK_TIMESTAMP_7			= 441;
		KT_CPR_RANK_TIMESTAMP_8			= 442;
		KT_CPR_RANK_TIMESTAMP_9			= 443;
		KT_CPR_RANK_TIMESTAMP_10		= 444;

		KT_DAY_RECHARGE_ACTIVITY_REWARD_MASK	= 445;
		KT_DAY_RECHARGE_ACTIVITY_REWARD_MASK1	= 446;
		PLACE_HOLD_FOR_JP				= 447;

		KT_TURNTALE_RECHARGE_LIMIT_ACTIVITY				= 448;	// 轮盘限时累计充值版本活动
		KT_TURNTALE_RECHARGE_LIMIT_INDEX				= 449;	// 轮盘限时累计充值领取奖励阶段
		KT_TURNTALE_RECHARGE_LIMIT_BEGIN_VALUE			= 450;	// 轮盘限时累计充值开始时候累计充值
		KT_TURNTALE_RECHARGE_LIMIT_ATTR_GET_MASK		= 451;	// 轮盘限时累计充值贡献奖励mask
		KT_TURNTALE_RECHARGE_LIMIT_DRAW_TIMES_MASK		= 452;	// 轮盘限时累计充值奖励抽奖次数mask
		KT_TURNTALE_DAY_RECHARGE_REWARD_MASK			= 453;	// 轮盘每日充值奖励领取次数
		KT_TURNTALE_RECHARGE_LIMIT_EXTRA_REPU_VALUE		= 454;	// 轮盘限时累计充值白银奖励额外抽奖次数
		KT_TURNTALE_RECHARGE_LIMIT_EXTRA_RECHARGE		= 455;	// 轮盘限时累计充值奖励抽奖次数mask
		KT_TURNTALE_RECHARGE_LAST_ADD_ATTR_RECHAGE_VAL	= 456;	// 轮盘已加过全服贡献的充值数

		KT_CASKET_RECHARGE_LIMIT_ACTIVITY				= 457;
		KT_CASKET_RECHARGE_LIMIT_INDEX					= 458;
		KT_CASKET_RECHARGE_LIMIT_BEGIN_VALUE			= 459;

		KT_DAILY_CONTINUE_RECHARGE_FINISH_DAYS			= 460;
		KT_DAILY_CONTINUE_RECHARGE_TODAY_BEGIN_COUNT	= 461;
		KT_DAILY_CONTINUE_RECHARGE_GET_REWARD_COUNT		= 462;
		KT_DAILY_CONTINUE_RECHARGE_REFRESH_TIME			= 463;
		KT_DAILY_CONTINUE_RECHARGE_ACTIVITY_ID			= 464;

		KT_MAGIC_GATHERING_BUY_SERVICE_ACTIVE			= 465;	// 魔力汇聚服务激活标记
		KT_MAGIC_GATHERING_BUY_SERVICE_PICK				= 466;	// 魔力汇聚奖励领取标记
		KT_MAGIC_GATHERING_BUY_SERVICE_ACTIVITY_ID		= 467;	// 魔力汇聚活动id
		KT_MAGIC_GATHERING_BUY_SERVICE_PICK_ROLE		= 468;	// 魔力汇聚活动领取礼包角色id
		KT_MAGIC_GATHERING_GLOBAL_PROGRESS_MASK			= 469;	// 魔力汇聚已领取全服进度档位

		KT_NEW_USER_ICE_BREAK_INDEX			= 470;	// 新用户付费破冰累计充值领取奖励阶段
		KT_NEW_USER_ICE_BREAK_BEGIN_VAL		= 471;	// 新用户付费破冰累计充值开始时候累计充值
		KT_NEW_USER_ICE_BREAK_ACTIVITY		= 472;	// 新用户付费破冰累计充值版本活动
		KT_NEW_USER_ICE_BREAK_RECHARGE_VAL	= 473;	// 新用户付费破冰累计充值已充值数
		KT_NEW_USER_ICE_BREAK_USED			= 474;	// 新用户付费破冰累计充值已使用点数

		KT_DAY_RECHARGE_ACTIVITY_REWARD_MASK1_1	= 475;

		KT_NEW_USER_ICE_BREAK_END_TIME		= 476;	// 新用户付费破冰累计充值结束时间

		KT_TSS_NEW_LEVEL_FUND_REWARD_LEVEL_1	= 477;	//新腾讯等级成长基金领取到的等级
		KT_TSS_NEW_LEVEL_FUND_REWARD_LEVEL_2	= 478;	//新腾讯等级成长基金领取到的等级
		KT_TSS_NEW_LEVEL_FUND_REWARD_LEVEL_3	= 479;	//新腾讯等级成长基金领取到的等级
		KT_TSS_NEW_LEVEL_FUND_REWARD_LEVEL_4	= 480;	//新腾讯等级成长基金领取到的等级
		KT_TSS_NEW_LEVEL_FUND_REWARD_LEVEL_5	= 481;	//新腾讯等级成长基金领取到的等级
		KT_TSS_NEW_LEVEL_FUND_REWARD_LEVEL_6	= 482;	//新腾讯等级成长基金领取到的等级
		KT_TSS_NEW_LEVEL_FUND_REWARD_LEVEL_7	= 483;	//新腾讯等级成长基金领取到的等级
		KT_TSS_NEW_LEVEL_FUND_REWARD_1			= 484;	//新腾讯等级成长基金领取次数
		KT_TSS_NEW_LEVEL_FUND_REWARD_2			= 485;	//新腾讯等级成长基金领取次数
		KT_TSS_NEW_LEVEL_FUND_REWARD_3			= 486;	//新腾讯等级成长基金领取次数
		KT_TSS_NEW_LEVEL_FUND_REWARD_4			= 487;	//新腾讯等级成长基金领取次数
		KT_TSS_NEW_LEVEL_FUND_REWARD_5			= 488;	//新腾讯等级成长基金领取次数
		KT_TSS_NEW_LEVEL_FUND_REWARD_6			= 489;	//新腾讯等级成长基金领取次数
		KT_TSS_NEW_LEVEL_FUND_REWARD_7			= 490;	//新腾讯等级成长基金领取次数

		KT_NEW_TSS_FIRST_RECHARGE_REWARD_ACTIVITY			= 491;	//新腾讯首充累计登录活动id
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_LAST_TIMESTAMP		= 492;	//新腾讯首充累计登录时间戳
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_DAYS				= 493;	//新腾讯首充累计登录登录天数
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_HAS_GET_INDEX		= 494;	//新腾讯首充累计登录已领取
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_ACTIVITY_2			= 495;	//新腾讯首充累计登录活动id
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_LAST_TIMESTAMP_2	= 496;	//新腾讯首充累计登录时间戳
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_DAYS_2				= 497;	//新腾讯首充累计登录登录天数
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_HAS_GET_INDEX_2	= 498;	//新腾讯首充累计登录已领取
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_PICK_ROLE_ID_2		= 499;	//新腾讯首充累计登录角色id
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_ACTIVITY_3			= 501;	//新腾讯首充累计登录活动id
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_LAST_TIMESTAMP_3	= 502;	//新腾讯首充累计登录时间戳
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_DAYS_3				= 503;	//新腾讯首充累计登录登录天数
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_HAS_GET_INDEX_3	= 504;	//新腾讯首充累计登录已领取
		KT_NEW_TSS_FIRST_RECHARGE_REWARD_PICK_ROLE_ID_3		= 505;	//新腾讯首充累计登录角色id
		KT_NEW_TRAVEL_TIME_OPEN_DAYS						= 506;	//故旅新时服务购买天数

		KT_CASKET_RECHARGE_LIMIT_OUTER_GET_TIMES		= 560;
		KT_CASKET_RECHARGE_LIMIT_OUTER_USE_TIMES		= 561;
		KT_CASKET_RECHARGE_LIMIT_INNER_GET_TIMES		= 562;
		KT_CASKET_RECHARGE_LIMIT_INNER_USE_TIMES		= 563;
		KT_CASKET_RECHARGE_LIMIT_KEY_GET_NUM			= 564;
		KT_CASKET_RECHARGE_LIMIT_KEY_USE_NUM			= 567;
		KT_CASKET_RECHARGE_LIMIT_GOODY_SHARE_TIMES		= 578;
		KT_CASKET_RECHARGE_LIMIT_EXTRA_RECHARGE			= 569;
		KT_CASKET_RECHARGE_LIMIT_EXTRA_OUTER_TIMES		= 570;
		KT_CASKET_RECHARGE_LIMIT_EXTRA_INNER_TIMES		= 571;
		KT_CASKET_RECHARGE_LIMIT_EXTRA_KEY_NUM			= 572;
		KT_CASKET_RECHARGE_LIMIT_EXTRA_GOODY_TIMES		= 573;
		KT_CASKET_RECHARGE_LIMIT_DAILY_LAST_RECHARGE	= 574;
		KT_CASKET_RECHARGE_LIMIT_LAST_RECHARGE			= 575;

		KT_NEW_MONTH_CARD_TODAY_GET_REWARD				= 576;
		KT_NEW_MONTH_CARD_END_TIME						= 577;
		KT_NEW_MONTH_CARD_OPEN_DAYS						= 568;
		KT_NEW_MONTH_CARD_DAYS_REWARD					= 579;
		KT_NEW_MONTH_CARD_EXTRA_REWARD					= 580;

		KT_COMEBACK_NEW_TRAVEL_TIME_PICK_ROLEID			= 581;

		KT_TRIGGER_ACHIEVEMENT_REWARD					= 582;

		KT_NEW_LEVEL_TRIGGER_GIFT					 	= 583;
		KT_NEW_LEVEL_TRIGGER_GIFT_2					 	= 584;
		KT_NEW_LEVEL_TRIGGER_GIFT_3					 	= 585;
		KT_NEW_LEVEL_TRIGGER_GIFT_4					 	= 586;
		KT_NEW_LEVEL_TRIGGER_GIFT_5					 	= 587;
		KT_NEW_LEVEL_TRIGGER_GIFT_6					 	= 588;
		KT_NEW_LEVEL_TRIGGER_GIFT_7					 	= 589;
		KT_NEW_LEVEL_TRIGGER_GIFT_8					 	= 590;
		KT_NEW_LEVEL_TRIGGER_GIFT_9					 	= 591;
		KT_NEW_LEVEL_TRIGGER_GIFT_10					= 592;

		//KT_TIME_LIMIT_REWARD_BEGIN_MASK = 1000;	//限时活动开始的ID
	};
	optional KEY_TYPE key			= 1;
	optional int64 value			= 2;
	optional int32 timestamp		= 3;
}

message db_account_gs_data {
	optional GPS_TYPE type			= 1 [default = GUT_ACCOUNT_GS_DATA ];
	repeated account_key_value pro_map					= 2;	//数据值
	optional db_recharge_info_base recharge_info_base	= 3;	//充值相关数据
}

message db_top_reward {
	optional GPS_TYPE type			= 1 [default = GUT_TOP_REWARD ];
	message player
	{
		optional int64 roleid	= 1;
		optional bytes name	= 2;
		optional bool reward	= 3 [default = false] ;
	}
	repeated player players			= 2;
	optional int32 start_time		= 3;
	optional int32 end_time			= 4;
	optional int32 tid			= 5;	//对应模板id
	optional int32 timestamp		= 6;	//时间戳校验
	enum STATUS
	{
		ST_ACTIVITY_OPEN	= 1;	//开启了
		ST_REWARD_TIME		= 2;	//发奖时间
		ST_CLOSED		= 3;	//未开启
	};
	optional STATUS status			= 7 [ default = ST_CLOSED ];	//状态
}

message greeting_investor_info
{
	optional int32	invest_type		= 1;
	optional int32	invest_value		= 2;
	optional int32	invest_time		= 3;
	optional int32	greet_tid		= 4;
	repeated player_id_name	investor_list	= 5;
}
message greeting_investee_info
{
	optional int32	invest_type		= 1;
	optional int32	invest_value		= 2;
	optional int32	invest_time		= 3;
	optional int32	greet_tid		= 4;
	optional player_id_name dst_role	= 5;
}
message greeting_invest_award
{
	optional int32	greet_tid	= 1;
	optional int32	award_tid	= 2;
	optional int32	award_count	= 3;
	optional int64	dst_roleid	= 4;
}

message crond_point {
	optional int32 type     = 1; 
	optional int32 last_mod_time    = 2; 
}

message crond_data {
	repeated crond_point data   = 1; 
}
message repu_sync_str {
	optional int32 repu_id          = 1; 
	optional int32 timestamp        = 2; 
	optional int64 inc_value        = 3;    //累计增加 
	optional int64 dec_value        = 4;    //立即减少 
	optional int64 remote_inc_value = 5;    //远端当前值 
	optional int64 remote_dec_value = 6; 
}

message new_repu_sync_str {
	optional int32 repu_id          = 1;
	optional int32 timestamp        = 2;
	optional int32 cur_value        = 3;
}

message repu_sync_man {
	repeated repu_sync_str records      = 1; 
	optional int32 timestamp        = 2; 
}
message special_property {
	optional int32 roam_zone        = 1;    //当前跨服到的服务器id
	optional bool forbid_login      = 2;    //是否禁止跨服登陆,标记之后将不能再跨服登陆
	optional int32 try_roam_time        = 3;    //尝试跨服的时间
	optional int32 cur_zone         = 4;    //当前所在线所属服务器
}
message friend_bless_record
{
	repeated int64 roleid			= 1;
	optional int32 timestamp		= 2;
}

message friend_thanks_giving_record
{
	optional int32 today_timestamp		= 1 [ default = 0 ];
	optional int32 cooldown_timestamp	= 2 [ default = 0 ];
	repeated int64 roleid_list		= 3;
	repeated thanks_giving_t give_data 	= 4;
	repeated thanks_giving_t recv_data	= 5;
}

message reliable_message_box_t {
	message message_t {
		optional int32 type		= 1;
		optional bytes data		= 2;
	}
	optional int64 begin_serial		= 1;
	repeated message_t  proto		= 2;
}
message xinshenghaoli_t
{
	message task_url_t
	{
		optional int32 task_id		= 1;
		optional bytes url			= 2;
	}
	repeated task_url_t task_url	= 1;
}

message ssp_recommend_info
{
	message history_info
	{
		optional int64 moment_id = 1;
		optional bytes moment_name = 2;
		optional int64 roleid = 3;
		optional int32 activity_id = 4;
		optional int32 top_id = 5;
		optional bool check_reward = 6;
	}
	repeated history_info history = 1;
	optional int32 recommend_count = 2;
	optional int32 recommend_tm = 3;
}

message db_ds_breed_info
{
	optional breed_info initiator_initiate_breed_info	= 1;	// 发起者 发起萌宠
	optional breed_info initiator_receive_breed_info	= 2;	// 发起者 接受萌宠
	repeated breed_info receiver_receive_breed_info		= 3;	// 接受者 接受萌宠
	optional int32 last_breed_cute_pet_id_receive		= 4;	// 接受者 上次繁育萌宠id
	optional int32 last_breed_gene_express				= 5;	// 接受者 上次繁育的萌宠的基因表达
	optional int32 last_breed_begin_time_receive		= 6;	// 接受者 上次繁育开始时间(接受)
	optional int32 last_breed_end_time_receive			= 7;	// 接受者 上次繁育结束时间(接受)
}

message single_bless_wall_info_list
{
	repeated single_bless_wall_info infos = 1;
}

message single_bless_wall_info_list_all
{
	repeated single_bless_wall_info_list lists = 1;
}

message goods_discount_list
{
	optional int32 activityid			= 1;
	repeated goods_discount_info infos	= 2;
}

message hundred_corps_guess_data
{
    repeated hundred_corps_guess_data_t   records     = 1;
    optional int32                        timestamp   = 2 [ default = 0 ];
}

message db_ds_save_player_data {
	optional GPS_TYPE type			= 1 	[ default = GUT_DS_SAVE_PLAYER_DATA ];
	optional int32 time_stamp		= 2	[ default = 0 ];
	repeated greeting_investor_info greet_investors	= 3;
	repeated greeting_investee_info greet_investees	= 4;
	repeated greeting_invest_award greet_awards	= 5;
	optional repu_sync_man repu_man         = 8;    //声望同步管理 
	optional special_property property      = 9;    //特殊属性数据 
	optional crond_data crond           = 10;   //ds定时刷新用数据 
	optional lbs_pos lbs_position           = 11;   //真实位置
	repeated int64 chat_group		= 12;	//聊天群
	optional reliable_message_box_t reliable_message_box = 13; 
	optional xinshenghaoli_t xinshenghaoli	= 14;	//新生好礼的url保存
	optional career_shop_scene_result career_shop_result = 15;
	optional int32 invite_friend_bindcash   = 16 [default = 0]; //招募好友此角色领的钻石数量
	optional int32 invite_friend_version	= 17 [default = 0];
	repeated player_rename_data_t old_names	= 18; // 曾用名
	optional int64 soul_intimate_title_roleid	= 19;
	optional uint32 pubg_hide_mask			= 20; //禁止别人查看我的吃鸡信息
	optional career_shop_group_data career_shop_group = 21;	//小店合伙人数据
	repeated int64 shop_partner_owner		= 22; // 都是谁的合伙人
	optional int32 ssp_compition_repu_tm		= 23;
	optional ssp_recommend_info ssp_recommend = 24;
	optional int32 temporary_team_repu_activity_today = 25; //考核小队当天已贡献的活跃度积分
	optional int32 temporary_team_repu_activity_tm	= 26;
	optional int32 temporary_team_login_activity_tm  = 27;
	optional db_ds_breed_info breed_info			= 28;	// 育宠达人繁育信息
	optional db_ds_pdd_team_info pdd_team			= 29;
	optional fashion_dress_bet_info fdbi			= 30;
	optional intimate_fashion_dress_infos ifdis		= 31;
	optional single_bless_wall_info_list_all sbw_lists	= 32;
	optional int64 honey_garden_id	= 33;
	optional goods_discount_list discount_list	= 34;
	optional restaurant_db_info restaurant_info = 35;	// 餐厅社交数据
	optional profile_player_action_and_model extar_player_show_info = 36;	// 玩家展示额外信息
	optional int32 hundred_corps_battle_local_reward_index = 37; //  百团本服阶段上次个人发放奖励积分
	optional hundred_corps_guess_data  guess_data = 38; // 百团大战竞猜数据
	optional int32 hundred_corps_battle_begin_time = 39 [default = 0];
	optional int32 hundred_corps_battle_assist_time = 40 [default = 0];
	optional int32 accept_anonymous_gift	= 41 [default = 0];	// 是否接受匿名送礼
	optional int32 reject_secret_love_msg	= 42 [default = 0];	// 是否接受好感留言
	optional int32 accept_social_space_anonymous_gift	= 43 [default = 0];	// 是否接受朋友圈匿名送礼
}

message restaurant_db_info{
	optional int32 timestamp		= 1;
	repeated int64 role_id		 	= 2;	// 偷过的玩家
}

message db_dyn_giftbag_data{
	optional GPS_TYPE type			= 1 	[ default = GUT_DYN_GIFTBAG_DATA ];
	repeated item_info items		= 2;	//物品数据, 需要根据参数生成物品 
	repeated item_data db_items		= 3;	//数据库格式物品数据，可以直接转成item
	repeated repu_info repus		= 4;	//声望数据
	optional int32 bind_money		= 5;	//绑定金币
	optional int32 bind_cash		= 6;	//绑定元宝
	optional int64 exp_inc			= 7;	//增加经验
	optional int32 vip_exp			= 8;	//增加vip经验
}
message db_tp_spouse_info
{
	optional GPS_TYPE type			= 1	[ default = GUT_TP_SPOUSE_INFO ];
	optional int64 roleid			= 2;
	optional bytes name			= 3;
}
message db_tp_pet_info
{
	optional GPS_TYPE type			= 1	[ default = GUT_TP_PET_INFO ];
	optional int32 pet_type			= 2;	
	optional int32 pet_tid			= 3;
	optional bytes pet_name			= 4;	
	optional bytes pet_bedge		= 5;	//宠物牌相关数据
}

message db_tp_guard_info
{
	optional GPS_TYPE type			= 1	[ default = GUT_TP_GUARD_INFO ];
	optional guard_client_data guard_data = 2;
}

message db_red_envelope{
	optional GPS_TYPE type			= 1 	[ default = GUT_RED_ENVELOPE ];
	optional red_envelope_info info		= 2;
}

message db_player_hometown{
	optional GPS_TYPE type			= 1 [ default = GUT_PLAYER_HOMETOWN ];
	repeated hometown_object hometown_server_objects_total	= 2;	//家园所有的服务器物体(存库使用)
	repeated hometown_object hometown_client_objects_total	= 3;	//家园所有的客户端物体(存库使用)
	repeated hometown_object hometown_server_objects	= 4;	//家园服务器召唤的物体(DS使用)
	repeated hometown_object hometown_client_objects	= 5;	//家园客户端自己显示的物体(DS使用)
	optional int32 last_clean				= 6 [ default = 0 ];	// 家园上次清洁时间
	optional bool has_hometown				= 7 [ default = false ];	// 家园是否开放
}
message db_tp_cache{
	optional GPS_TYPE type			= 1 [ default = GUT_TP_CACHE];
	optional int32 fight			= 2 [ default = 0];	//战斗力
}

message db_player_farm{
	optional GPS_TYPE type			= 1 [ default = GUT_PLAYER_FARM ];
	optional farm_object objects		= 2;
}

message db_player_park_data {
	optional GPS_TYPE type			= 1 	[ default = GUT_PLAYER_PARK_DATA ];
	optional int32 version			= 2	[ default = 0];// 版本号
	optional bool  double_status		= 3	[ default = false];	// 是否是双倍奖励
}

message db_vow_data {
	repeated vow_info vow_list	= 1;
}

// 幸运翻牌数据
message db_player_dice_card_data
{
	optional GPS_TYPE type					= 1		[ default = GUT_PLAYER_DICE_CARD_DATA ];
	repeated dice_card_data_t cards			= 2;
}

// 彩票声望版本
message ticket_repu_version_info
{
	optional int32 key								= 1;	// enum TICKET_REPU_VERSION_KEY
	optional int32 value							= 2;
}

// 活动彩票声望
message activity_ticket_repu_info
{
	optional int32 key								= 1;	// enum ACTIVITY_TICKET_VALUE_KEY
	optional int32 activity_id						= 2;
}

// 彩票数据
message db_player_ticket_info
{
	optional GPS_TYPE type							= 1		[ default = GUT_PLAYER_TICKET_INFO ];
	repeated video_game_info vg_info				= 2;	// 绘梨衣的游戏数据
	repeated ticket_repu_version_info trv_info		= 3;	// 彩票声望版本数据
	repeated dragon_house_info dh_info				= 4;	// 巨龙宝库数据
	repeated activity_ticket_repu_info atr_info		= 5;	// 活动彩票声望数据
	repeated pleasure_info pl_info                  = 6;    // 趣味夺宝数据库
	repeated easy_dice_info ed_info					= 7;	// 简易彩票数据
	repeated spring_info sp_info                    = 8;    // 春节彩票 
	repeated king_house_info kh_info				= 9;	// 王之宝库数据
	repeated god_explore_info ge_info				= 10;	// 神迹探索数据
	repeated little_card_info lc_info				= 11;	// 小彩票数据
	repeated team_recharge_info tr_info				= 12;	// 组队充值数据
	repeated lottery_sea_info ls_info				= 13;	// 星海密藏数据
	repeated lottery_ship_info lship_info			= 14;	// 冰海行动
	repeated self_select_reward_pool_info ssrp_info	= 15;	// 自选奖池
	repeated qxqy_info qx_info						= 16;	// 千寻奇遇
	repeated starry_night_info sn_info				= 17;	// 星夜漫彩
	repeated lottery_machine_info lm_info			= 18;	// 街头游戏机
	repeated holy_ghost_dice_info hg_info			= 19;	// 英灵彩票
	repeated bingo_info bg_info						= 20;	// 宾果彩票
	repeated draw_info dr_info						= 21;	// 绘梦星空彩票
	repeated share_box_info sb_info					= 22;	// 分享宝箱
	repeated casket_info ci_info					= 23;	// 缘金绮匣
	repeated mining_info mi_info					= 24;	// 挖矿
}

// 淘汰赛战队数据
message db_eliminate_group_info
{
    optional GPS_TYPE type                  = 1     [ default = GUT_ELIMINATE_GROUP_INFO ];
    repeated eliminate_fighter_t    fighters= 2;
    optional int32      fighter_timestamp   = 3;
	optional int32 eliminate_group_type		= 4;
}

//头像存盘结构
message db_photo_dbdata_t
{
	optional GPS_TYPE type 					= 1 [ default = GUT_PHOTO_INFO];
	repeated int32 photo_ids 				= 2;	//无限期头像列表
	repeated personal_photo_data_t timelimit_photo_ids 	= 3;	//有期限头像列表
	repeated int32 photo_decos 				= 4;	//无期限边框列表
	repeated personal_photo_data_t timelimit_photo_decos 	= 5;	//有限期边框列表
}

//用于存库的时装结构
message db_fashion_dbdata
{
        optional GPS_TYPE type = 1 [ default = GUT_FASHION_INFO];
        optional broadcast_fashion_data bdata = 2;
        optional personal_fashion_data  pdata = 3;
}

message db_player_collection
{
	optional GPS_TYPE type                  = 1 [ default = GUT_COLLECTION_DATA ];
	repeated collection_info data           = 2;
}

message db_longyu_collection
{
	message longyu_part_t
	{
		repeated int32 longyu_id = 1;
	}
	repeated longyu_part_t part					= 1; // 索引对应装备位索引
}

message db_longhun_collection
{
	message longhun_part_t
	{
		repeated int32 longhun_id = 1;
		repeated int32 longhun_expire_time = 2;
	}
	repeated longhun_part_t part                = 1; // 索引对应装备位索引
}

message db_equip_suit_collection
{
	message suit_part_t
	{
		repeated int32 suit_id = 1;
	}
	repeated suit_part_t part					= 1;	//索引对应装备位索引
}

message toplist_history_t
{
	optional int32 top_id		= 1;	// 排行榜id
	optional int32 high_rank	= 2;	// 历史最高排名
}

message toplist_week_award_t
{
	message toplist_t
	{
		optional int32 toplist_id = 1; // 排行榜id
	}
	repeated toplist_t toplists = 1;
	optional int32     last_flush_ts = 2;
}

// 预警数据
enum EARLY_WARNING_TYPE
{
	EARLY_WARNING_TYPE_NONE								= 0;
	EARLY_WARNING_TYPE_EXP								= 1;	// 每日经验预警
	EARLY_WARNING_TYPE_FACTOR_EXP						= 2;	// 每日周期性经验预警
	EARLY_WARNING_TYPE_COIN								= 3;	// 每日金币预警
	EARLY_WARNING_TYPE_BIND_CASH						= 4;	// 每日钻石预警
	EARLY_WARNING_TYPE_ACTIVITY_FACTOR_EXP				= 5;	// 每日活动周期性经验预警
};
message db_player_early_warning_data
{
	message db_common_frozen_data
	{
		optional EARLY_WARNING_TYPE type				= 1;    // 类型
		optional int64 total_num						= 2;	// 总冻结数量
	}
	repeated db_common_frozen_data common_frozen_data	= 1;	// 经验、周期性经验、金币、钻石冻结数据
	message db_item_data
	{
		optional int32 tid								= 1;	// 物品tid
		optional int32 daily_num						= 2;	// 物品计入预警值每日累计数据
	}
	repeated db_item_data item_data						= 2;	// 物品数据
}

// 玩家属性addon_id和属性组addon_group_id数据
message db_player_addon_and_group_data
{
	repeated int32 activate_addon_ids					= 1;	// 玩家激活的属性
	repeated int32 activate_addon_group_ids				= 2;	// 玩家激活的属性组
}
		
message db_adventure_task_data 
{
	message task_record
	{
		optional int32 taskid		= 1;
		optional int32 finish_time  = 2;
		optional int32 rank         = 3;
	}
	repeated task_record record							= 1;
}

message personality_mod_data_t
{
	message mod_type_data
	{
		repeated int32 data			= 1;
	}
	repeated mod_type_data	type_data					= 1;
}

message player_once_item_data_t
{
	repeated int32 itemtid  = 1; 
}

message player_book_data_t
{
	repeated uint32 book_tid  = 1;
}

message player_longyu_data_t
{
	repeated uint32 golden_longyu_by_compose  = 1;//融合得到的金色龙语
}

message simple_stock_price_data
{
	message stock_info
	{
		optional int32 stock_id = 1;
		repeated int32 prices = 2;
	}
	repeated stock_info stocks = 1;
	optional int32 open_time = 2;
	optional int32 expire_time = 3;
}

message simple_stock_price_data_gs
{
	optional int32 stock_id = 1 [ default = 0 ];
	repeated int32 prices = 2;
	optional int32 open_time = 3 [ default = 0 ];
	optional int32 expire_time = 4 [ default = 0 ];
}

message db_player_simple_stock{
	optional GPS_TYPE type		= 1     [ default = GUT_SIMPLE_STOCK ];
	optional int32 open_time	= 2;
	optional int32 expire_time	= 3;
	message stock_info
	{
		optional int32 stock_id = 1;
		optional int32 count = 2;
	}
	repeated stock_info stocks 	= 4;
	optional int32 total_buy	= 5; //买股票一共花费的钱数
	message trade_history
	{
		optional int32 stock_id = 1;
		optional int32 count = 2;
		optional int32 price = 3;
	}
	repeated trade_history trades	= 6;
	optional int32 cur_spend 	= 7; //当前买卖股票投入的钱数
	optional int32 active		= 8 [ default = 0 ]; //是否已经激活股市
	optional int32 notify_client_open = 9 [ default = 0 ];	//本次开盘是否已经通知客户端
}

message db_player_minigame_data{
	optional GPS_TYPE type          = 1     [ default = GUT_MINIGAME_DATA ];
	message mingame_t {
		optional int32 minigame_type = 1;
		optional int32 minigame_id = 2;
		optional minigame_info info = 3;
	}
	repeated mingame_t game_data = 2;
	optional int32 center_battle_type = 3;
	optional int32 battle_type = 4; 
	optional int32 game_type = 5;
	optional int32 game_id = 6;
	optional int32 self_progress = 7; 
	optional int32 other_progress = 8;
}

message db_player_achievement_recharge_data {
	message recharge_info {
		optional int32 config_id = 1;
		optional int32 end_timestamp = 2;
	}
	optional int32 achievement_recharge_version = 1;
	repeated recharge_info infos = 2;
}

message sect_struct
{
	optional GPS_TYPE type				= 1 [ default = GUT_SECT_STRUCT ];
	optional int64 id				= 2;	//玩家ID
	optional int64 master_id			= 3;	//玩家师父ID
	optional bool graduated				= 4;	//是否出师
	optional int32 last_break_time			= 5;	//上一次主动断绝师徒关系的时间
	optional int32 teach_value			= 6;	//传道值
	repeated homework_info works_1			= 7;	//师父布置的作业
	optional int32 assign_work_time_1		= 8;	//师父布置作业的时间
	repeated homework_info works_2			= 9;	//师父布置的作业
	optional int32 assign_work_time_2		= 10;	//师父布置作业的时间
	optional int32 logout_time			= 11;
	optional int32 join_time			= 12;	//拜师时间
	enum GROW_TYPE
	{
		GT_NULL			= 0;	//没有状态
		GT_YOUNG		= 1;	//初出茅庐
		GT_EXCELLED		= 2;	//战露头角
		GT_EXPERT		= 3;	//独当一面
		GT_STAR			= 4;	//未来之星
	}
	optional int32 grow_value			= 13;	//成长值
	optional int32 teach_value_cost			= 14;	//消耗的传道值
	optional int32 graduate_time			= 15;	//出师时间
	repeated int64 disciples			= 16;	//徒弟数量
	repeated int64 graduated_disciples		= 17;	//出师徒弟数量
	optional int32 level				= 18;	//等级
	optional int32 title_on_master			= 19;	//徒弟身上保存师父的称号
	optional int32 title_on_self			= 20;	//徒弟身上保存自己的称号
	optional int64 disciple_title_selected		= 21;	//师父选择生效的称号对应的的徒弟
	optional int32 partner_id			= 22;	//师父选择的助教ID
	repeated int32 homework_rewards         = 23;   //完成家庭作业的奖励
}

message player_corps_donation_t
{
	optional int32 itemid		= 1;
	optional int32 timestamp	= 2;
}

message player_prof_data_t
{
	optional int32 prof							= 1;
	optional player_stune_config_t stunt_data	= 2;
	optional player_talent_t talent_data		= 3;
	optional player_auto_combat_set_t auto_combat_set = 4;
	optional player_solution_t solution_data = 5; // 该职业的总体方案数据
	optional player_pureblooded_t pure_blooded_data	= 6;	//血装天赋数据
}

message db_mech
{
	optional int64 death_tm = 1;
	optional float hp_ratio = 2[ default = 1 ];
	optional int64 compose_tm = 3;
}

//继承者战斗系统存盘数据
message db_heir_control
{
	repeated db_heir_control_one heir_arr = 1;//阵亡后的继承者信息
	optional int64 disappear_id = 2;//由于主人死亡或切地图而收回的出战继承者
	optional float disappear_hp_ratio = 3[ default = 1 ];//由于主人死亡或切地图而收回的出战继承者生命
	repeated int64 battle_list = 4;//出战继承者列表，越靠前的越先出战，道具id
}

message player_emoji_data_t
{
	message player_emoji_t
	{
		optional int32 emoji_id			= 1;
		optional int32 emoji_timestamp	= 2;
	}
	repeated player_emoji_t emojis		= 1;
}

message player_child_data_t
{
	optional int32 summon_index	= 1;
	optional child_fashion_info fashion = 2;
	optional int32 default_fashion_version = 3 [default = 0];
	optional child_global_tour_info tour = 4;
}

message acr_role_full_data
{
	optional int64	photo_id				= 1;	// 头像
	optional int32	chat_bubble				= 2;	// 聊天框
	optional anonymous_chat_room_member_info info      = 3;   // 个人的外显数据
	optional bytes	role_room_name			= 4;	// 玩家在房间内的名字
}

message bookworm_data
{
	optional int32 lucky				= 1;	// 幸运值
	optional int32 exp					= 2;	// 经验值
	optional int32 lv					= 3;	// 等级
	repeated bookworm_lucky luckys		= 4;
}

message scrawl_fashion_data{
	optional int32 timestamp			= 1;
	optional int64 auther_id			= 2;
	optional int32 fashion_index		= 3;
}

message diamond_pursue_data
{
	optional bool is_pursue_status		= 1 [ default = false ]; // 是否处于追缴状态
	optional int32 pursue_count			= 2; // 剩余追缴数量
}

message alchemy_other_data{
	optional int32 light_effect_select	= 1;	// 外显选择
	repeated alchemy_slot slots			= 2;	// 槽位信息
}

message prof12_shadow_fashion_data{
	repeated uint64 fashion_index = 1;
	repeated int32 fashion_ext = 2;
	repeated uint64 fashion_offset = 3;
}

message bouquet_collect_data
{
	optional int32 tid					= 1;
	optional int32 timestamp			= 2;
}
message db_recharge_act
{
	repeated int32 unlock_id            = 1;
}

message longyu_solution_data
{
	optional int32 cur_index			= 1;
	repeated longyu_solution solutions	= 2;
}

message longhun_solution_data
{
	optional int32 cur_index			= 1;
	repeated longhun_solution solutions	= 2;
}

message retinue_solution_data
{
	optional int32 cur_index			= 1;
	repeated retinue_solution solutions	= 2;
}

message player_universal_data_t
{
	optional direct_lottery_t direct_lottery			= 1;
	optional bytes pata_data							= 2;
	optional bytes vip_info								= 3;
	optional player_stune_config_t stune_config			= 4; //废弃
	optional player_corps_attr_config_t corps_attr_config = 5;
	optional player_corps_cache_t corps_cache			= 6;
	optional db_fashion_dbdata fashion_data				= 7;
	optional bytes baby_fashion							= 8;
	optional db_photo_dbdata_t photo_dbdata				= 9;
	optional retrieve_info_t retrieve_info				= 10;
	optional bytes talisman_info						= 11;
	optional bytes retinue_group_info					= 12;
	optional bytes retinue_info							= 13;
	optional db_practice_data practice_data				= 14;
	optional bytes exp_lottery							= 15;
	optional db_player_park_data park_data				= 16;
	optional roam_mafia_info_t roam_mafia_info			= 17;
	optional roam_arena_group_info_t roam_arena_group_info = 18;
	optional bytes counter_data							= 19;
	optional bytes switch_data							= 20;
	optional db_player_enhance_data enhance_data		= 21;
	optional player_kotodama_t kotodama_data			= 22;
	optional player_talent_t talent_data				= 23; //废弃
	optional player_chatbox chatbox						= 24;
	repeated toplist_history_t toplist_history			= 25;
	optional player_pk_man_t pk_man 					= 26;
	optional player_task_sceneweather_data sceneweather = 27;
	optional plat_social_space_data ss_data				= 28;
	optional db_player_collection collection			= 29;
	optional db_player_early_warning_data early_warning_data = 30;
	optional db_longyu_collection longyu_collection		= 31;
	optional db_adventure_task_data adventure_task		= 32;
	optional personality_mod_data_t personality_mod		= 33;
	optional db_player_addon_and_group_data addon_and_group_data = 34;
 	optional player_once_item_data_t once_item_data		= 35;
	optional int64 reliable_message_serial				= 36;
	optional player_career_data career_data				= 37;
	optional player_book_data_t book					= 38;
	optional db_player_simple_stock simple_stock		= 39;
	optional player_corps_donation_t corps_donation		= 40;
	repeated player_prof_data_t	prof_datas				= 41; // 多职业，各个职业的配置数据
	optional db_player_minigame_data minigame_data		= 42;
	optional player_tetrahedron_t tetrahedron			= 43; // 魔盒 正四面体
	optional db_recharge_user_tss_sum_info recharge_tss_info	= 44;	// 充值服务
	optional db_player_dice_card_data card_data			= 45; // 幸运翻牌数据
	optional int32 longyu_compose_lucky_point			= 46; //龙语融合幸运值
	optional bool weapon_hide					= 47; //隐藏武器
	optional int32 longyu_compose_lucky_point2                      = 48; //龙语融合幸运值2
	optional int32 invite_friend_version				= 49;
	optional int32 invite_friend_bindcash				= 50;
	optional db_adventure_detective adventure_detective = 51;//侦探异闻
	optional db_mech mech				= 52;//机甲
	optional db_player_achievement_recharge_data achievement_recharge_info = 53;	//玩家成就充值数据
	optional int32 hp_before_leave_scene = 54;//离开场景前的生命值
	// 55已废弃，千万别用
	repeated player_heraldicae_data_t heraldicae		= 56;	//纹章石数据
	optional db_player_ticket_info ticket_info = 57;	//彩票数据
	optional player_emoji_data_t emoji_data				= 58;	//玩家表情数据
	optional db_equip_suit_collection equip_suit_collection		= 59;	//玩家套装数据
	optional player_longyu_data_t longyu				= 60;
	optional db_heir_control heir_control				= 61;
	optional player_child_data_t child_info				= 62;
	optional toplist_week_award_t toplist_week_award = 63; // 排行榜周奖励
    optional eliminate_group_info_t eliminate_group_info= 64;
	optional player_rune_new_info rune_new_info			= 65;	// 新符文数据
	optional player_overcook_info overcook				= 66;
	optional player_seven_crime_t seven_crime			= 67;//七宗罪数据
	optional player_breed_info breed_info				= 68;	// 育宠达人数据
	optional idip_forbid_info idip_forbid                           = 69;
	repeated draw_seq_info ds_info						= 70;	// 抽奖序列数据
	optional acr_role_full_data acr_data				= 71;	// 匿名聊天室数据
	optional bookworm_data bw_data						= 72;	// 书虫数据
	optional scrawl_fashion_data scrawl_data			= 73;	// 涂鸦时装数据 废弃
	repeated scrawl_fashion_data scrawl_data2			= 74;	// 涂鸦时装数据
    optional eliminate_group_info_t eliminate_group_2_info= 75;
	optional diamond_pursue_data dp_data				= 76;	// 钻石追缴数据
	optional alchemy_other_data alchemy_data			= 77;	// 炼金矩阵数据
	optional prof12_shadow_fashion_data shadow_fashion_data	= 78;	// 职业12 第二套时装
	repeated bouquet_collect_data bouquet_data			= 79;	// 花束收藏数据
	optional db_longhun_collection  longhun_collection  = 80;  
	optional restaurant_info sim_restaurant				= 81;	// 餐厅模拟经营数据
	optional lmfshop_db_info lmfshop_data				= 82;	// 明非特卖数据
	optional db_recharge_act recharge_act_data           = 83;   // 解锁付费动作
	optional db_fishing_data fishing_data           	= 84;   // 钓鱼数据
	optional longyu_solution_data longyu_solutions		= 85;   // 龙语方案
	optional retinue_solution_data retinue_solutions	= 86;   // 伙伴方案
	optional db_longwen_data longwen_data 				= 87;	// 龙纹数据
	optional db_alchemy_circle_data	alchemy_circle_data = 88;	// 炼金法阵数据
	optional eliminate_group_info_t eliminate_group_3_info = 89;
	optional pray_data_t pray_data						= 90;	// 朋友圈祈愿
	optional db_holy_ghost_data holy_ghost_data 	= 91; //英灵数据
	optional longhun_solution_data longhun_solutions	= 92;   // 龙魂方案
	optional db_townlet_data townlet_data 			= 93; //小镇相关个人养成数据
	optional db_dragon_mystique_data dragon_mystique_data 	= 94; //龙凝秘法数据
	optional db_player_personal_card_data personal_card_data = 95;	// 个人名片数据
	optional db_star_ghost_data star_ghost_data 	= 96; //星魂数据
	optional db_stellar_chart_data stellar_chart_data = 97; //星盘数据
	repeated virtual_grow_up_data grow_up_data		= 98; //战斗力缓存
}

message wish_tree_data_t
{
	optional int32 irrigate_count			= 1;
	repeated int32 fruits					= 2;
	optional bool harvested					= 3;
}

message db_player_misc
{
	optional GPS_TYPE type                          = 1 [ default = GUT_PLAYER_MISC ];
	optional int32 kill_monsters                    = 2 [ default = 0 ];
	//optional family_related_t family_related      = 3;
	optional int32 debug_run_speed                  = 4 [ default = 0 ];
	optional int32 trans_force_times                = 5 [ default = 0 ];
	optional int32 wine_config_id                   = 6 [ default = 0 ];
	optional int32 wine_remain_drink                = 7 [ default = 0 ];
	optional int32 forbidden_double_exp             = 8 [ default = 0 ];
	optional revive_data_t revive_data              = 9;
	optional resurrect_state_t resurrect_state      = 10;
	optional int32  prev_levelup_time_used          = 11;   //玩家上一次升级时间
	optional gp_multi_exp multi_exp                 = 12;   //双倍
	optional gp_refuse_fight refuse_fight           = 13;   //免战
	optional gp_change_prof change_prof				= 14;   //转职
	optional int64 change_prof_timestamp            = 15 [ default =0];//转职
	optional int32 lug_zone_id                      = 16 [ default = 0 ];   //记录最后一次所在大世界的zoneid
	optional bytes clock_data						= 17;
	optional bytes nation_escort                    = 18;
	optional wish_tree_data_t wish_tree_data		= 19;
	optional int64 last_hungry_prop_tick_timestamp	= 20; // 上次定时扣除饱食度的时间
	optional int32 bak_solution_index				= 21 [ default = -1 ]; // 进入预设场景前的总体方案备份
	optional uint32 last_scene_tag					= 22; // 上次退出场景
	optional int64 last_happy_prop_tick_timestamp	= 23; // 上次定时扣除愉悦度的时间
	optional int32 series_active_record				= 24; // 连续登陆记录，只记录满值和0
	optional gp_top_star_data top_star				= 25; // 人气巨星
	optional gp_career_shop_buyer_info career_shop_buyer = 26; // 小店购买
	optional gp_fight_capacity_rsp fight_cap_cache 	= 27; //战斗力缓存
}

message auction_warning_t
{
	message item_warning_t
	{
		optional int32 itemid		= 1;
		optional int32 count		= 2;
		optional int32 timestamp	= 3;
	};
	message total_waring_t
	{
		optional int64 price		= 1;
		optional int32 timestamp	= 2;
	};
	repeated item_warning_t item_warning = 1;
	repeated item_warning_t longyu_warning = 2;
	optional total_waring_t total_warning = 3;
};



message db_role_auction_t
{
	optional int32 level			= 1;
	optional int64 exp				= 2;
	optional int64 total_turnover	= 3;
	optional int64 daily_turnover	= 4;
	optional int64 yestoday_turnover = 5;
	optional int64 daily_exp		= 6;
	optional int32 last_exp_time	= 7;
	optional auction_warning_t warning = 8;
};

message db_material_bag_t {
	message db_material_t {
		optional int32 id			= 1;
		optional int32 pos			= 2;
		optional int32 count		= 3;
		optional int32 state		= 4;
	}
	optional int32 capacity				= 1;
	repeated db_material_t material		= 2;	
}

message db_transaction_data_t
{
	optional int32 repu_id			= 1;
	optional int32 repu_count		= 2;
}

message db_holy_ghost_inscription_bag_t
{
	message holy_ghost_inscription_t {
		optional int32 tid			= 1;
		optional int32 pos			= 2;
		optional int32 count		= 3;
		optional int32 state		= 4;
		optional bytes content		= 5;
	}
	repeated holy_ghost_inscription_t inscriptions	= 1;
}

message db_stellar_chart_bag_t
{
	message stellar_chart_t {
		optional int32 tid			= 1;
		optional int32 pos			= 2;
		optional int32 count		= 3;
		optional int32 state		= 4;
		optional bytes content		= 5;
	}
	repeated stellar_chart_t stellar_charts	= 1;
}

message db_alchemy_runestone_bag_t{
	message alchemy_runestone_t{
		optional int32 tid			= 1;
		optional int32 pos			= 2;
		optional int32 count		= 3;
		optional int32 state		= 4;
		optional bytes content		= 5;
	}
	repeated alchemy_runestone_t runestones			= 1;
}

message db_townlet_bag_t
{
	repeated item_data db_items		= 1;	//数据库格式物品数据，可以直接转成item
}

message db_townlet_pet_bag_t
{
	repeated item_data db_items		= 1;	//数据库格式物品数据，可以直接转成item
}

message db_townlet_data //小镇相关个人养成数据
{
	optional player_townlet_postcard postcard 		= 1; //收藏的明信片列表 (废弃)
	optional player_townlet_travel_data travel 		= 2; //来访数据(废弃)
	optional int32 postcard_auto_increment_id 		= 3; //明信片自增id
	optional townlet_history_info history 			= 4; //历史记录
    optional player_townlet_retinue_chat_data chat  = 5; //聊天数据
	optional player_townlet_travel_data_new travel_new = 6; //旅行数据
	optional player_townlet_postcard_new postcard_new = 7; //明信片列表
}

message db_player_personal_card_data
{
	optional int64 actived_cards = 1;	// 激活了的名片，是个掩码形式
	optional int32 selected_card = 2;		// 选择展示的名片，是索引
}

message db_role_pocket
{
	repeated int64 extend_money				= 1;
	optional db_role_auction_t auction		= 2;
	optional db_material_bag_t material_bag = 3;
	optional db_transaction_data_t transaction_data	= 4;
	optional db_dragonborn_bag_t dragonborn_bag = 5;
	optional db_dragonborn_bag_t dragonborn_depo_bag = 6;
	optional db_alchemy_runestone_bag_t alchemy_depo = 7;
	optional db_alchemy_runestone_bag_t alchemy_equip = 8;
	optional db_townlet_bag_t townlet_bag = 9;
	optional db_holy_ghost_inscription_bag_t holy_ghost_inscription_depo = 10;
	optional db_holy_ghost_inscription_bag_t holy_ghost_inscription_equip = 11;
	optional db_townlet_pet_bag_t townlet_pet_bag = 12;
	optional db_stellar_chart_bag_t stellar_chart_depo = 13;
	optional db_stellar_chart_bag_t stellar_chart_equip = 14;
}

message soul_intimate_data
{
	optional int32 active			= 1 [default = 0];
	message player_info
	{
		optional bytes leave_msg	= 1;
		optional int32 chat_background	= 2;
		optional int32 team_gfx		= 3;
		optional int32 teleport_time	= 4;
		optional int32 fruit_count		= 5; //收获过的果实总数量
		repeated int32 offline_msg_type	= 6; //已发送的离线消息类型
	}
	optional player_info player1            = 2;
	optional player_info player2            = 3;
	optional bytes name                     = 4;
	optional int32 icon			= 5;
	optional int32 level			= 6;
	optional int32 create_time		= 7;
	optional int32 seq			= 8;
	optional int32 red_award_level          = 9;
	repeated soul_interact interacts	= 10;
	optional soul_tree_data tree		= 11;
	repeated soul_tree_history tree_history = 12;
	optional int32 value			= 13;
	optional int32 vow_tm			= 14;
	message soul_vow
	{
		optional int32 npc_id	= 1;
		optional bytes vow1	= 2;
		optional bytes vow2	= 3;
		optional int32 tm	= 4;
	}
	repeated soul_vow vow_infos		= 15;
	repeated soul_child_info childs		= 16;
}
message db_player_intimate //羁绊数据
{
	optional GPS_TYPE type			= 1 [ default = GUT_INTIMATE_DATA ];
	message player_info
	{
		optional int64 roleid		= 1;
		optional bytes leave_msg	= 2;
		optional int32 last_logout_tm	= 3;
		optional int32 select_theme	= 4 [default = 1]; //当前选择的主题
	}
	optional int64 intimate_id              = 2;
	optional player_info player1            = 3;
	optional player_info player2            = 4;
	optional bytes name                     = 5;
	optional int32 value                    = 6;
	optional int32 create_time              = 7;
	optional int32 value_today              = 8;
	optional int32 value_modify_time        = 9;
	optional int32 red_award_level          = 10; //已经发过的红包等级
	repeated intimate_anniversary_info anni_data	= 11;
	optional int32 next_anniversary_id      = 12;
	optional int32 next_anniversary_time    = 13; //下次纪念日时间
	optional int32 seq			= 14 [default = 0];
	optional int32 value_week               = 15;
	optional soul_intimate_data soul	= 16;
	optional int32 soul_delete_time		= 17;
}

message db_config_struct
{
	optional int32 init_time = 1 [ default = 0 ];
	optional int32 open_time = 2 [ default = 0 ];
	optional int32 import_time = 3 [ default = 0 ];
	optional int32 save_time = 4 [ default = 0 ];
	optional int32 tx_district_id = 5 [ default = 0 ];
	optional int32 marriage_seq = 6 [ default = 0 ];
	optional int32 game_sn = 7 [ default = 0];
	optional int32 recharge_activity_disable = 8 [default = 126976]; //12 13 14 15 16 这几位默认置为关闭状态
	optional int32 server_level = 9 [ default = 0 ];
	optional int32 server_level_update_time = 10 [default = 0 ];
	optional int32 first_recharge_version = 11 [ default = 0 ];
	optional int32 recharge_total_version = 12 [ default = 0 ];
	optional int32 time_limit_consume_version = 13 [ default = 0 ];
	optional int32 server_level_exp_factor = 14 [ default = 0 ];
	optional int32 center_type = 15 [ default = 0 ];
	repeated int32 forbiditems = 16;
	message CampaignGMOpenItem
	{
		optional int32 campaignid = 1 [ default = 0 ];
		optional int32 opentime = 2 [ default = 0 ];
		optional int32 endtime = 3 [ default = 0 ];
	}
	repeated CampaignGMOpenItem gmopenitems = 17;
	repeated int32 zoneidlist = 18;
	optional int32 created_roles = 19 [ default = 0 ];
	optional int32 server_exp_level = 20 [ default = 0 ];
	optional int32 server_levelup_time = 21 [ default = 0 ];
	optional int32 created_users = 22 [ default = 0 ];
	optional int32 today_created_users = 23 [ default = 0 ];
	optional int32 today_created_users_timestamp = 24 [ default = 0];
	optional int32 achievement_recharge_version = 25 [ default = 0];
	optional int32 import_hometown_time = 26 [ default = 0 ];
	optional int32 soul_seq = 27 [ default = 0 ];
}

message db_user_pbinfo
{
	optional db_recharge_info recharge_info_backup		= 1;
}

message mailtoalldata_pbinfo
{
	optional int32 user_type = 1;
	optional int32 level_limit = 2 [ default = 0 ]; // 等级限制
	optional bytes version	= 4;	//客户端版本限制
}

message centerbattle_player_t
{
	optional int64 roleid		= 1;
	optional int32 zoneid		= 2;
	optional int32 param1		= 3;
	optional int32 param2 		= 4;
	optional int32 battle_type 	= 5;
	repeated int64 team_members = 6;
	repeated bytes member_name  = 7;
	repeated int32 member_prof  = 8;
	optional int64 idphoto      = 9;
	optional bytes name         = 10;
	optional int64 win_count    = 11;
}

message social_space_t
{
	optional int64 roleid = 1;
	optional int64 targetid = 2;
	optional int32 txnid = 3;
	optional int32 step_award = 4;
	optional int32 popularity = 5;
	optional int32 gift_count = 6;
	optional int32 toplist_id = 7;
	optional int64 album_id = 8;//相册扩容ID
	optional int32 status_id = 9;
	optional int32 op = 10;
	optional idip_info_t idip = 11;
	optional ipt_fashion_dress_op ifdo = 12;
	optional bool is_reward = 13;
	repeated ipt_fashion_dress_op vote_ifdo = 14;
}

message qq_push_info
{
    message info_t
    {
        optional int32 tid = 1;
        optional int32 next_push_time = 2;
    }
    repeated info_t save_infos = 1;
}

message item_gen_common_pb_t
{
	optional dragonborn_break_skill_info_t dragonborn_skills = 1;
	optional bouquet_info bouquet = 2;
}

message tss_data_t
{
	optional int32 msg_type = 1;					//信息类型，0 文字，1 语音
	optional string speech_original_voice_id = 2;	//语音的原始内容ffileid或url
	optional int32 msg_char_type = 3;				//点对点聊天类型，0非好友，1好友
	optional string recv_account = 4;				//接收方用户账号openid
	optional int32 recv_area_id = 5;				//接收方1:微信 2:手Q  3:游客
	optional int32 recv_plat_id = 6;				//接收方玩家平台id。 0 : IOS 1: andriod 2: PC 1000: 其它
	optional int32 recv_world_id = 7;				//接收方针对分区分服的游戏填写分区id。不分区则填写0
	optional int64 recv_role_id = 8;				//接收方角色id
	optional int64 target_id = 9;					//操作对象id(社团、天梯战队、羁绊等)
	optional int64 post_id = 10;					//留言ID
	optional int64 message_id = 11;					//寄语ID
}

/////////////////////////////////////////////////////////////////////////////////////////////
//
//  DS<->GS通讯类协议
//	DS<->Hub<->DS协议
//
//WARING::命名规则
//所有能够自动获取的类型必须包含一个 "optional INNER_PROTOCBUF_TYPE type          = 1 [default = IPT_xxx];" 
//default用来标明这个msg的类型
//必须不能重复一一对应，这样可以自动建立一个type <--> msg的对应表 ,

//服务器内部通讯协议

enum INNER_PROTOCBUF_TYPE {
	IPT_CREATE_CORPS		= 1;
	IPT_CORPS_FACEBOOK		= 2;
	IPT_LINE_REGISTER_INFO		= 3;
	IPT_MIRROR_OP			= 4;
	IPT_ZHAOJILING			= 5;	//召集令召集玩家
	IPT_SYNC_NATION_INFO		= 6;	//同步国家数据
	IPT_MODIFY_CORPS_DATA		= 7;	//修改帮派参数
	IPT_NATIONWAR_FIELD_MSG		= 8;	//同步国战消息
	IPT_LEVEL_CONTROL_MSG		= 9;	//levelctrl同步消息
	IPT_DELIVER_REWARD		= 10;
	IPT_SYNC_OFFICER_INFO		= 11;	//同步官员信息
	IPT_LONGJUMP_PLAYER		= 12;	//通知角色传送
	IPT_VIP_INFO			= 13;	//VIP信息变化
	IPT_ADD_FRIENDS			= 14;	//添加好友
	IPT_CONTRI_CORPS_MONEY		= 15;	//捐钱
	IPT_SYNC_PLAYER_INFO		= 16;	//同步玩家数据
	IPT_ROLE_UNSAVE_DATA		= 17;	//角色换线暂存数据
	IPT_NATION_WAR_ACHIEVEMENT	= 18;	//国战成就追求数据
	IPT_CORP_ATTRIBUTE		= 19;	//帮派属性
	IPT_CREATE_INSTNACE		= 20;	//副本创建参数
	IPT_GREETING_EVENT		= 21;	//好友祝福事件
	IPT_GREETING_AWARD		= 22;	//发放祝福奖励
	IPT_GREETING_AWARD_RESULT	= 23;	//反馈祝福奖励结果
	IPT_DELIVER_COMMON_REWARD	= 24;	//发放通用奖励
	IPT_BLESSING_INFO		= 25;	//送花协议
	IPT_LONGJUMP_SUCCEED		= 26;	//通知传送结果
	IPT_CORP_CAMP_FIRE		= 27;	//帮派篝火
	IPT_ASK_HELP			= 28;	//求救
	//IPT_REFRESH_MIDAS_BALANCE	= 29;	//刷新米大师余额
	//IPT_MIDAS_SAVE_AMT		= 30;	//通知GS米大师累计充值
	IPT_ACCOUNT_PROPERTY		= 31;	//账号公用数据
	IPT_GRC_RCV_GIFT		= 32;	//收取GRC好友礼物
	IPT_JOIN_NATION_WAR		= 33;	//参与国战
	IPT_SYNC_NATION_WAR_TYPE	= 34;	//同步国战阶段信息
	IPT_SERVER_INFO			= 35;	//服务器设置
	IPT_CLEAR_CARD			= 36;	//清卡牌
	IPT_GM_CMD			= 37;	//GM指令
	IPT_QUERY_TASK_STATUS		= 38;	//查询玩家任务
	IPT_PLAYER_COMBAT_STATE		= 39;	//战斗状态
	IPT_GRC_GET_SEND_GIFT_LIMIT	= 40;	//ds获取送礼物上限
	IPT_GRC_GET_SEND_GIFT_LIMIT_RE	= 41;
	IPT_GRC_GET_RCV_GIFT_LIMIT	= 42;	//ds获取发送礼物上限
	IPT_GRC_GET_RCV_GIFT_LIMIT_RE	= 43;
	IPT_GRC_UPDATE_FRIEND_COUNT	= 44;	//通知grc好友数量，发奖用
	IPT_CORPS_LEVEL			= 45;	//通知GS帮派级别
	IPT_UPDATE_GRC_LEVEL_PROGRESS	= 46;	//更新grc副本通过等级信息
	IPT_UPDATE_SECURE_IDIP		= 47;	//更新安全IDIP属性
	IPT_UPLOAD_QQ_INFO		= 48;	//向QQ平台上报信息
	IPT_RECHARGE_PLAT_VIP		= 49;	//DS转发给GS的续费信息
	IPT_CHARIOT_UPGRADE		= 50;	//GS发给DS的使用升级战车协议
	IPT_CHARIOT_UPGRADE_RE 		= 51;   //DS发给GS的使用帮会资金成功升级战车协议
	IPT_PLAYER_CORPS_INFO           = 52;   //DS发给GS玩家一些帮会信息（暂时就发了玩家进入帮派的时间）
	IPT_CHARIOT_RENT       		= 53;   //GS发给DS的领取战车协议
	IPT_CHARIOT_RENT_RESULT         = 54;   //DS发给GS的领取战车结果的协议
	IPT_ARENA_APPLY_CHECK		= 55;	//DS发给GS检查玩家竞技场报名
	IPT_ARENA_APPLY_CHECK_RESULT	= 56;	//DS发给GS检查玩家竞技场报名的结果
	IPT_ARENA_SEND_RESULT		= 57;	//GS发给DS战场结果
	IPT_PLAYER_FASHION_CHANGE	= 58;	//GS通知DS玩家时装信息变化
	IPT_MODIFY_GS_REPU		= 59;	//DS通知GS修改声望
	IPT_BLESSING_NOTIFY		= 60;	//GS通知DS玩家送花成功
	IPT_TEAM_TASK_EVENT		= 61;	//DS和GS之间通知队伍任务事件
	IPT_FAKE_AUCTION_OPEN		= 62;	//摆摊上架
	IPT_CHANGE_SYSTIME	= 63;	//修改系统时间
	IPT_FAKE_AUCTION_BUY		= 66;
	IPT_FAKE_AUCTION_BUY_RESULT     = 67;
	IPT_FAKE_AUCTION_BUY_ACK	= 68;
	IPT_PLAYER_ENTER_CORPS_BATTLE = 69;	//DS通知GS玩家请求进入帮派竞赛
	IPT_PLAYER_ENTER_CORPS_BATTLE_RESULT = 70;	//GS进入帮派竞赛的返回结果
	IPT_SEND_CORPS_BATTLE_RESULT = 71;	//GS通知DS帮派竞赛结果
	IPT_SCENE_LIMIT_PLATFORM	= 72;	//GS通知DS场景禁止组队平台
	IPT_SET_CORPS_MANIFESTO		= 73;	//GS通知DS设置帮派宣言
	IPT_REVENGE_BATTLE_APPLY	= 74;	//DS通知GS玩家有人发起仇杀约战
	IPT_REVENGE_BATTLE_APPLY_RESULT = 75;	//GS通知结果
	IPT_REVENGE_BATTLE_RESPONSE	= 76;	//玩家返回结果
	IPT_TRY_ENTER_REVENGE_BATTLE	= 77;	//尝试进入约战副本
	IPT_REVENGE_BATTLE_RESULT	= 78;	//约战副本结果
	IPT_ON_ENTER_REVENGE_BATTLE	= 79;	//进入约战副本后回调
	IPT_REVENGE_BATTLE_END		= 80;	//约战副本结束
	IPT_INC_VIP_EXP			= 81;	//增加VIP经验
	IPT_REGISTER_CLIENT     	= 82;   //DS->HUB注册
	IPT_REGISTER_CLIENT_RESPONSE    = 83;   //注册返回信息 HUB->DS
	IPT_HUB_PING            	= 84;   //DS<->HUB 定时更新信息
	IPT_GENERAL_PROTOC      	= 85;   //通用传输载体
	IPT_UPDATE_INFO         	= 86;   //DS<->HUB 更新国家信息
	IPT_CHANGE_ZONE         	= 87;   //跨服协议
	IPT_CHANGE_ZONE_RESPONSE    	= 88;   //跨服回应
	IPT_SECT_APPLY			= 89;	//带徒拜师
	IPT_INTIMATE_FRIEND_TITLE_UPDATE= 90;   //情缘称号通知
	IPT_INTIMATE_FRIEND_TITLE_SELECT= 91;   //使用情缘称号
	IPT_SECT_GRADUATE		= 92;	//出师
	IPT_SECT_TITLE_UPDATE		= 93;	//更新师徒称号
	IPT_CENTER_BATTLE_PLAYER_APPLY_CHECK	= 94;	//玩家申请中心战场的检查
	IPT_CENTER_BATTLE_PLAYER_APPLY_CHECK_RE	= 95;	//玩家申请中心战场的检查的返回
	IPT_CENTER_BATTLE_ROAM_PLAYER_APPLY	= 96;	//发送给跨服的玩家加入中心战场的申请
	IPT_CENTER_BATTLE_ROAM_PLAYER_APPLY_RE	= 97;	//发送给跨服的玩家加入中心战场的申请的返回
	IPT_CENTER_BATTLE_ROAM_JOIN_CHECK	= 98;	//中心服进入战场的状态检查
	IPT_CENTER_BATTLE_ROAM_JOIN_CHECK_RE	= 99;	//中心服进入战场的状态检查的返回
	IPT_CENTER_BATTLE_ROAM_JOIN_ASK		= 100;	//中心服发给源服通知玩家进入战场
	IPT_CENTER_BATTLE_ROAM_JOIN_ANSWER	= 101;	//玩家答复中心战场
	IPT_CENTER_BATTLE_ROAM_TELEPORT_PLAYER	= 102;	//中心服战场传送玩家进战场
	IPT_CENTER_BATTLE_ROAM_PLAYER_PUNISHED	= 103;	//中心服通知源服玩家被惩罚
	IPT_CENTER_BATTLE_ROAM_PLAYER_QUIT	= 104;	//玩家退出中心战场
	IPT_CENTER_BATTLE_ROAM_BATTLE_CLOSE	= 105;	//通知DS战场结束
	IPT_CHANGE_ID_PHOTO			= 106;  //设置玩家头像
	IPT_PLAYER_APPLY_WEDDING		= 107;	//玩家申请婚礼
	IPT_PLAYER_APPLY_WEDDING_RESULT		= 108;	//玩家申请婚礼的结果
	IPT_COUPLE_ENTER_WEDDING		= 109;	//夫妻进入婚礼场景
	IPT_UPDATE_ROAM_TOKEN			= 110;	//跨服更新token
	IPT_RENAME_UPDATE 			= 111; 	//改名后更新GS
	IPT_ILLEGAL_REPORT			= 112; //举报通知ds打印日志
	IPT_PARADING_NOTIFY			= 113;	//婚礼巡游通知
	IPT_PARADING_END_NOTIFY			= 114;	//婚礼巡游结束通知
	IPT_FLYSWORD_SURFACE_TOP_LEVEL		= 115;	//飞剑铸灵达到顶级通知
	IPT_SNATCH_MARRIAGE_BEGIN		= 116;	//抢亲开始通知
	IPT_SNATCH_MARRIAGE_END			= 117;	//抢亲结束通知
	IPT_FARM_UPDATE				= 118;	//玩家农场信息更新 
	IPT_ARENA_GROUP_INFO		= 119;	//通知GS战队信息
	IPT_ARENA_GROUP_CHANGE		= 120;	//通知DS更新战队积分
	IPT_CORPS_RACE_BET			= 121;	//玩家帮派赛跑下注
	IPT_CORPS_RACE_BET_RESULT		= 122;	//玩家帮派赛跑下注结果
	IPT_CORPS_RACE_BET_AWARD		= 123;	//玩家帮派赛跑下注奖励
	IPT_VOWS				= 124;	//玩家开启婚礼宣誓
	IPT_CORPS_CENTER_BATTLE_UPDATE	= 125;	//跨服帮派竞赛信息更新
	IPT_CORPS_CENTER_BATTLE_ENTER	= 126;	//玩家进入帮派跨服竞赛
	IPT_CORPS_CENTER_BATTLE_AWARD	= 127;	//玩家获取帮派跨服竞赛奖励
	IPT_CORPS_CENTER_BATTLE_AWARD_RE	= 128;	//玩家获取帮派跨服竞赛奖励
	IPT_OLDPLAYER_BACK_NOTICE_FRIEND = 129; //老玩家回归通知在线好友
	IPT_CORPS_SERVER_BATTLE_UPDATE	= 130;	//跨服服务器对战信息更新
	IPT_CHILD_MARRIAGE_CHECK_AMITY		= 131;	//童婚检查好友度
	IPT_CHILD_MARRIAGE_CHECK_AMITY_RESULT	= 132;	//童婚检查好友度结果
	IPT_CHILD_MARRIAGE_SUCCESS		= 133;	//童婚成功
	IPT_CORPS_SERVER_BATTLE_ENTER	= 134;	//玩家进入跨服服务器对战
	IPT_CHILD_DIVORCE_NOTIFY		= 135;	//童婚离婚邮件通知
	IPT_INVITE_COUPLE_TOUR			= 136;	//邀请娃娃亲出游
	IPT_REPLY_COUPLE_TOUR			= 137;	//回复娃娃亲出游
	IPT_CORPS_SERVER_BATTLE_AWARD   	= 138;  //跨服城战领取奖励
	IPT_CORPS_SERVER_BATTLE_AWARD_RE	= 139;	//跨服城战领取奖励返回结果
	IPT_PLAYER_CONSTELLATION_REWARD		= 140;	//玩家修改星宿信息
	IPT_H5_GAME_GET_REWARD			= 141;	//玩家领取H5游戏奖励
	IPT_PLAYER_SLAVE_UPDATE			= 142;	//玩家仆从信息更新
	IPT_SOCIAL_SPACE_OPERATION		= 143;	//玩家空间操作
	IPT_SOCIAL_SPACE_OPERATION_RE		= 144;	//玩家空间操作返回
	IPT_REMOTE_CALL					= 145;	//远程调用
	IPT_SERVICE_ALLOC_ZONE			= 146;	//申请中心服资源
	IPT_RUN_FOR_MASTER_CENTER		= 147;	//竞选中心服master
	IPT_REMOTE_CONTROL				= 148;	//远程数据同步获取
	IPT_PRE_CHANGE_WORLD			= 149;	//换线的检查
	IPT_PRE_CHANGE_WORLD_FAIL		= 150;	//换线失败
	IPT_DIS_CREATE_INSTANCE			= 151;	//中心服创建副本
	IPT_NOTIFY_INSTANCE_INFO		= 152;	//远程同步副本创建信息
	IPT_TRANSFER_GS_MSG				= 153;	//中转玩家的GS_IPT消息
	IPT_TRANSFER_NPT_MSG			= 154;  //中转玩家的NPT消息
	IPT_SERVICE_CARRIER				= 155;	//中心服中转发协议
	IPT_SET_COOLDOWN				= 156;	//设置冷却
	IPT_PUBSUB_OPERATION			= 157;	//布订阅操作
	IPT_PLAYER_CHANGE_SCENE			= 158;	//替代changescene.hpp
	IPT_GET_ROAMER_INFO				= 159;	//获取跨服玩家信息
	IPT_GET_PLAYER_PROFILE			= 160;	//DS<->DS获取玩家信息
	IPT_ADVENTURE_TASK_FINISH		= 161;	//通知DS玩家完成奇遇任务
	IPT_CHATBOX_SELECT				= 162;	//GS->DS通知玩家聊天框变化
	IPT_ROAM_CHAT_PUBLIC			= 163;	//DS->HUB 跨服狮吼转发
	IPT_DELIVER_TASK				= 164;	//DS->GS 发任务接口
	IPT_ROAM_TEAM_OP				= 165;	//DS->HUB->DS 跨服组队操作
	IPT_ROAM_TRANSFER_PROTOCOL		= 167;	//DS->HUB->DS 传输Protocol
	IPT_ROLE_SET_NAME_REQ			= 168;	//GS->DS 新角色起名请求
	IPT_ROLE_SET_NAME_REP			= 169;	//DS->GS 新角色起名结果
	IPT_ROLE_QUERY_NAME				= 170;	//GS->DS 进入场景以后如果没有名字，向DS查询一下
	IPT_HIGH_RANKS_REQ				= 171;	//GS->DS 请求需要维护历史最高排名排行榜的当前排名
	IPT_HIGH_RANKS_REP				= 172;	//DS->GS 返回需要维护历史最高排名排行榜的当前排名
	IPT_CENTER_BATTLE_DEBUG_SET_MAX_PLAYER_NUM	= 173;	//DS->HUB->DS debug设置跨服战场人数
	IPT_SYNC_TEAM_REPU				= 174;	//DS<->GS 同步队长声望
	IPT_SERVER_GET_BRIEF			= 175;	//DS->HUB->DS 中心服查询简要信息，以后有跨服社交可能会扩展
	IPT_SERVER_GET_BRIEF_RE			= 176;	//DS->HUB->DS 中心服查询简要信息的返回，以后有跨服社交可能会扩展
	IPT_INTIMATE_OPERATION                          = 177;  //羁绊操作
	IPT_INTIMATE_CREATE_NOTIFY                      = 178;
	IPT_AMITY_GRADE_SYNC                            = 179;  //amity每一档的好友数目变化通知GS
	IPT_INTIMATE_SYNC				= 180;	//通知GS羁绊列表
	IPT_SUMMON_CORPS_MEMBER				= 181;	//DS<->GS 随机召唤一个在线的社团玩家作为NPC
	IPT_CORPS_BASE_DATA_CHANGE			= 182;	//DS->GS 社团基地数据变化
	IPT_ROAM_SPEAK					= 183;	//高级送花跨服喊话
	IPT_GS_CHECK_BAD_WORD				= 184;	//DS<->GS，GS检查敏感词
	IPT_GRC_GET_ALL_RCV_GIFT_LIMIT                  = 185;   // 获取所有接收礼物类型的
	IPT_GRC_GET_ALL_SEND_GIFT_LIMIT                 = 186;  // 一键赠送礼物
	IPT_FAKE_AUCTION_REFRESH						= 187;	// GS->DS 店铺手动刷新
	IPT_PERSONALITY_MODIFY							= 188;	// DS->GS 人格修改
	IPT_IDIP_DATA									= 189;	// DS->GS idip存盘数据结果
	IPT_DELIVER_TITLE								= 190;	// DS->GS 发称号
	IPT_ADVENTURE_TASK_RANK                         = 191;  // DS->GS 奇遇任务排名
	IPT_RELIABLE_MESSAGE_INFORM						= 192;	// DS->GS 新的可靠消息
	IPT_RELIABLE_MESSAGE_CONFIRM					= 193;	// GS->DS 确认可靠消息
	IPT_RELIABLE_MESSAGE_REQUEST					= 194;  // GS->DS 请求可靠消息
	IPT_RELIABLE_MESSAGE_REPLY						= 195;  // GS->DS 返回可靠消息
	IPT_GS_CHECK_CARRACE_MATCHAPPLY					= 196;	// DS->GS 赛车gs检查匹配条件
	IPT_GS_CHECK_CARRACE_MATCHAPPLY_RET				= 197;	// GS->DS 赛车gs检查匹配条件的返回结果
	IPT_GS_CARRACE_INSTANCE_CREATED					= 198;  // GS->DS 赛车副本已经创建
	IPT_PLAYER_TRY_ENTERINSTANCE 					= 199;  // DS->GS 玩家尝试进入赛车副本
	IPT_CHANGE_MIRROR_TO_TEAM_LEADER_INVITE			= 200;	// GS->DS 队长发起队员切到队长镜像逻辑
	IPT_CHANGE_MIRROR_TO_TEAM_LEADER				= 201;	// DS->GS 队员跟随队长切到队长镜像逻辑
	IPT_CENTER_BATTLE_NOTIFY_CENTER_ZONEID			= 202;	// DS->HUB->DS 中心服通知源服中心服变化
	IPT_CENTER_BATTLE_TRANSFER_PLAYER				= 203;	// DS->HUB->DS 中心服通知新的中心服转移玩家匹配信息
	IPT_FAUCTION_LOAD_FROM_IP						= 204;	// DS->GS 从共享内存中加载假拍卖行数据
	IPT_UPDATE_PLAYER_APPEARANCE                            = 205; //更新玩家形象数据
	IPT_ON_GLODEN_INITMATE_ANNIVERSARY_TASK_FINISHED	= 206;	//羁绊纪念日任务完成通知
	IPT_IDIP_FORBID_PLAYER_FUNC						= 207;  // IDIP禁止某个玩家使用某个功能
	IPT_CAREER_MEDIA_UPLOAD_REQ						= 208;	// GS->DS 身份文件上传请求
	IPT_CAREER_MEDIA_UPLOAD_REP						= 209;	// DS->SS 身份文件上传结果
	IPT_UPDATE_COMPLICATED_DATA				= 210;
	IPT_AUCTION_CLOSE								= 211;	// DS->GS 拍卖行下架通知
	IPT_CENTER_BATTLE_CHECK_STATE					= 212;	// DS->HUB->DS 本服去中心服验证玩家状态
	IPT_CENTER_BATTLE_DEBUG_CLEAR_PLAYER			= 213;	// DS->HUB->DS debug清除玩家信息
	IPT_NIGHT_REWARD_ITEM_NOTIFY					= 214; // GS->DS 晚间发奖物品奖励通知
	IPT_UPDATE_CORP_RANKLIST						= 215;	// GS->DS 更新帮派排行榜
	IPT_CORP_RANKLIST_AWARD							= 216;	// GS->DS 更新帮派排行榜
	IPT_ON_TASK_FINISH					= 217;	// GS->DS 任务完成时向DS的通知
	IPT_NEW_SECT_GRADUATE_NOTIFY				= 218;	//新师徒系统毕业通知
	IPT_DS_ACHIEVEMENT					= 219;	// DS->GS 触发并通知GS更新成就
	IPT_NEW_SECT_PARTNER_OP					= 220;
	IPT_STAR_TOPIC_SHARE                = 221; // GS->DS 客户端分享热搜
	IPT_CORPS_INSTANCE_SCORE_INFORM				= 222;
	IPT_SYNC_FRIEND_HELP_DATA                               = 223;  // DS->GS 同步好友互助数据
	IPT_TOP_STAR_CHEER								= 224;	// GS->DS 人气巨星应援 
	IPT_SET_FORBID_NAME								= 225;	// 设置屏蔽玩家名字
	IPT_TOP_STAR_REPORT								= 226;	// DS->GS 人气巨星战报
	IPT_CENTER_BATTLE_ROAM_SERVER_APPLY			= 227;	//发送给跨服的加入中心战场的申请
	IPT_CENTER_BATTLE_ROAM_SERVER_APPLY_RE			= 228;	//发送给跨服的加入中心战场的申请
	IPT_INTIMATE_PARTY_BEGIN                        = 229;  // 羁绊派对开始
	IPT_CENTER_BATTLE_DEBUG					= 230;	//本服发给中心服的调试
	IPT_CENTER_BATTLE_SYNC_INFO				= 231;	//本服给中心服同步数据
	IPT_PLAYER_START_BOT					= 232;	//GS同步托管操作到DS
	IPT_CENTER_BATTLE_GROUP_GET_MEMBER_STATUS               = 233;  //中心管理服查询成员信息
	IPT_CENTER_BATTLE_GROUP_GET_MEMBER_STATUS_RE    	= 234;  //中心管理服查询成员信息的返回
	IPT_CAREER_SHOP_UPDATE_NOTIFY				= 235;	// gs->ds 小店数据更新通知
	IPT_CORPS_BATTLE2_SUB_BATTLE_INFO			= 236;	//gs->ds社团竞赛2子战场信息同步
	IPT_CLOSE_INSTACNE							= 237;	//ds->gs关闭副本
	IPT_DIAOXIANG_UPDATE						= 238;	// DS->GS 雕像更新
	IPT_CORPS_CENTER_BATTLE_LEAVE				= 239;	//玩家离开帮派跨服竞赛
	IPT_INVITE_FRIEND_REWARD_SYNC				= 240;
	IPT_CORPS_GARDEN_INFO						= 241;
	IPT_CORPS_GARDEN_OPERATION					= 242;
	IPT_CORPS_GARDEN_SYNC_STATE					= 243;
	IPT_CORPS_CENTER_BATTLE_CREATE_BATTLE		= 244;
	IPT_CORPS_CENTER_BATTLE_TRANS_PLAYER		= 245;
	IPT_CORPS_CENTER_BATTLE_ROAM_FINISH			= 246;
	IPT_PLAYER_HOMETOWN_OPERATION				= 247;	//家园操作 GS->DS
	IPT_PLAYER_HOMETOWN_OPERATION_RE			= 248;	//家园操作结果 DS->GS
	IPT_NEW_SECT_NOTIFY					= 249;
	IPT_CAREER_SHOP_ADD_HISTORY					= 250;
	IPT_CENTER_BATTLE_GROUP_GET_PLAYER_STATUS               = 251;  //中心管理服查询玩家信息
	IPT_CENTER_BATTLE_GROUP_GET_PLAYER_STATUS_RE    	= 252;  //中心管理服查询玩家信息的返回
	IPT_PROXY_DRAW_NOTIFY_TO_GS					= 253;	//代抽通知 DS->GS
	IPT_PROXY_DRAW_NOTIFY_TO_DS					= 254;	//代抽通知 GS->DS
	IPT_CENTER_BATTLE_PLAYER_NOTIFY				= 255;  //GS->DS 战场状态通知
	IPT_DAILY_FAKE_LOGIN_LOGOUT					= 256; // GS->DS 跨天在线的添加登录登出日志
	IPT_ARENA_MINIGAME_START					= 257; // DS->GS 小游戏匹配成功开战
	IPT_ARENA_MINIGAME_START_RESULT				= 258; // GS->DS 小游戏匹配成功开战结果
	IPT_ARENA_SEND_RESULT_COMMON				= 259; // GS->DS 发给DS 通用结算消息
	IPT_GUARD_BREED_NOTIFY						= 260;	//GS->DS 守护灵培育通知
	IPT_GUARD_MARRIAGE_CHECK					= 261;	//GS->DS 守护灵结婚检查
	IPT_GUARD_MARRIAGE_CHECK_RESULT				= 262;	//DS->GS 守护灵结婚检查结果
	IPT_LOG_NOTIFY								= 263; // GS->DS 发给DS 通用日志信息
	IPT_DELETE_ACCOUNT_CHECK					= 264; // DS<->GS 删除账号检查 邮件附件、假拍卖上架、拍卖行收益、临时包裹、小店收益
	IPT_DB_SET_DELETE_ACCOUNT					= 265; // DS->DB 注销账号操作
	IPT_USE_INVITE_CODE_POINT					= 266; // 邀请码积分兑换物品
	IPT_CHECK_SOUL_MATE						= 267;	// GS<->DS 检查灵魂伴侣
	IPT_LONGYU_UPGRADE						= 268;	//
	IPT_DELIVER_SOUL_TREE_HARVEST					= 269;	// DS->GS 誓约树收获
	IPT_CHECK_CORPS_FIGHT_CAPACITY_TOPLIST_RANK			= 270;	// GS<->DS 查询社团战斗力排名排行榜
	IPT_SYNC_CAREER_INFO						= 271; // GS->DS 同步角色身份数据
	IPT_GET_CAREER_SHOP_PARTNER_BONUS			= 272; // DS->GS 发放小店合伙人分红
	IPT_NPC_TEAM_FOLLOW_ENTER_INSTANCE				= 273;	//
	IPT_SOUL_CHILD_OP						= 274; //DS<->GS 原型期继承者操作
	IPT_SOUL_CHILD_SYNC					= 275; //DS->GS 同步原型期操作
	IPT_ELIMINATE_GROUP_INFO        =    276;   //DS->GS 通知GS战队信息
	IPT_LIMIT_LOTTERY_COUNT_NOTIFY	= 277; // GS->DS 限时抽奖 
    IPT_ELIMINATE_BATTLE_NOTIFY     = 278;
    IPT_ELIMINATE_BATTLE_DEBUG      = 279;
    IPT_ELIMINATE_GROUP_GRADE_CHANGE= 280;
    IPT_CORPS_ELIMINATE_KNOCKOUT_BATTLE_ENTER = 281;
    IPT_CORPS_ELIMINATE_KNOCKOUT_BATTLE_ENTER_RE = 282;
	IPT_SSP_VOTE						= 283;
	IPT_CENTER_BATTLE_READY_NOTIFY               = 284;  //中心服通知玩家准备状态
	IPT_CENTER_BATTLE_FAIR_BALANCE               = 285;  //中心管理服广播公平战平衡值
	IPT_ELIMINATE_GROUP_GET_PLAYER_STATUS        = 286;  //中心管理服查询玩家信息
	IPT_ELIMINATE_GROUP_GET_PLAYER_STATUS_RE     = 287;  //中心管理服查询玩家信息的返回
	IPT_SSP_VOTE_REFRESH                         = 288;  //朋友圈投票每周刷新 GS->DS
	IPT_ACCOUNT_PURSUE                           = 289;  //账号追缴
    IPT_EDIT_DIAOXIANG                           = 290;  //
    IPT_BROADCAST_DIAOXIANG                      = 291;  //
    IPT_ROAM_SEARCH_BRIEF                        = 292;  //
    IPT_ROAM_SEARCH_BRIEF_RE                     = 293;  //
    IPT_ELIMINATE_GROUP_REFRESH_STATUS           = 294;
    IPT_ELIMINATE_GROUP_REFRESH_STATUS_RE        = 295;
    IPT_ELIMINATE_GROUP_REFRESH_STATUS_BROADCAST = 296;
    IPT_DELIVER_CORPS_SEASON_REPU                = 297;
    IPT_CORPS_SEASON_NOTIFY                      = 298;
    IPT_DELIVER_CORPS_SEASON_MEMBER_REPU         = 299;
    IPT_ELIMINATE_GUESS                          = 300;
    IPT_CHILD_INFO_CHECK			= 301;
	IPT_CORPS_BOSS_SCORE_INFORM				= 302;
	IPT_TEMPORARY_TEAM_CREATE					= 303;
	IPT_TEMPORARY_TEAM_JOIN						= 304;
	IPT_TEMPORARY_TEAM_QUIT						= 305;
	IPT_TEMPORARY_TEAM_SAVE						= 306;
	IPT_TEMPORARY_TEAM_LOAD						= 307;
	IPT_REPU_CHANGE_INFORM						= 308;
	IPT_CENTER_BATTLE_MINIGAME                  = 309;
	IPT_BREED_OP								= 310;	// GS<->DS 育宠达人操作
	IPT_LIMIT_PLEASURE_COUNT_NOTIFY             = 311; // GS->DS 趣味夺宝
    IPT_SET_CORPS_CROWN                         = 312;
	IPT_PDD_TEAM_OP                             = 313; // GS<->DS 拼团玩法
	IPT_PDD_TEAM_DB_OP                          = 314; // DS<->DB 拼团玩法DB操作
    IPT_CENTER_CORPS_DUEL_REQUEST               = 315;
    IPT_CENTER_CORPS_DUEL_NOTIFY                = 316;  //
	IPT_HARMONIOUS_TASK_FINISH					= 317;  //	gs ->ds 良缘任务完成通知	
	IPT_DIVORCE_COOL_PERIOD                     = 318;  // 良缘 冷静期结束 解除关系 
	IPT_HARMONIOUS_OPERATION                    = 319;  // 良缘 操作
	IPT_CORPS_BLESS                             = 320;  // 社团祝福
	IPT_IDIP_SET_FACE_BOOK						= 321;	// IDIP修改facebook
	IPT_ARENA_CENTER_TEAM_BATTLE_NOTIFY         = 322;  // 跨服组队爆破行动中心服管理服通知ds消息
	IPT_BLESS_WALL_OP							= 323;  // 祝福墙
	IPT_FASHION_DRESS_OP						= 324;	// GS<->DS DS<->HUB 时装搭配大赛操作
	IPT_BDSG_OP									= 325;	// GS<->DS DS<->HUB 白帝神宫
	IPT_BDSG_OP_RE								= 326;	// DS<-HUB 白帝神宫
	IPT_BDSG_BATTLE_RESULT                      = 327;  //白帝神宫战场结果
	IPT_ROAM_FRIEND								= 328;	// 跨服好友相关
	IPT_LEAVE_INSTACE							= 329;	//玩家主动退出副本，gs->ds
	IPT_INTIMATE_FASHION_DRESS_OP				= 330;	// 羁绊时装搭配
	IPT_ANONYMOUS_CHAT_ROOM_OP                  = 331;  // 匿名聊天室
	IPT_ANONYMOUS_CHAT_ROOM_OP_RE               = 332;  // 匿名聊天室返回
	IPT_ROAM_ACR_OP								= 333;	// 匿名聊天跨服操作
	IPT_NATS_SERVICE							= 334;	// NATS服务
	IPT_NATS_CENTRAL_SERVICE					= 335;	// NATS中心服集群
	IPT_PVE_SOCIAL_RELATION_OP                  = 336;  // ds < -- > HUB  pve中心服 婚姻关系师徒关系
	IPT_CROSS_SERVER_TEAM_OP					= 337;	// 跨服队伍
	IPT_CROSS_SERVER_TEAM_OP_RE					= 338;	// 跨服队伍返回
	IPT_CROSS_SERVER_TEAM_NOTIFY				= 339;	// 跨服队伍通知
	IPT_GOD_EXPLORE_COUNT_NOTIFY				= 340;	// 神迹探险通知
	IPT_SCRAWL_PASTED_ALERT						= 341;	// 涂鸦涂墙共享内存更新通知 gs -> ds ->(广播)gs 
	IPT_SINGLE_BLESS_WALL_OP					= 342;	// 单人祝福墙op
	IPT_SINGLE_BLESS_WALL_OP_RE					= 343;	// 单人祝福墙op_re
	IPT_HONEY_GARDEN_OP							= 344;	// 甜蜜家园op
	IPT_HONEY_GARDEN_OP_RE						= 345;	// 甜蜜家园op_re
	IPT_TEAM_RECHARGE_OP						= 346;  // 组队累计充值op
	IPT_TEAM_RECHARGE_OP_RE						= 347;  // 组队累计充值op_re
	IPT_PVE_INTIMASTE_NATS_OP                   = 348;  // 跨服羁绊nats相关操作
	IPT_ROAM_BLESSING_INFO						= 349;	// 跨服送花
	IPT_ROAM_BLESSING_INFO_RE					= 350;	// 跨服送花re
	IPT_GOODS_DISCOUNT_OP						= 351;	// 社交砍价
	IPT_GOODS_DISCOUNT_OP_RE					= 352;	// 社交砍价re
	IPT_PVE_HARMONIOUS_NATS_OP                  = 353;  // 跨服羁绊nats相关操作
	IPT_BOUQUET_OP								= 354;  // 赠送花束
	IPT_BOUQUET_OP_RE							= 355;  // 赠送花束re
	IPT_CORPS_ALIGN_OP							= 356;  // 结盟op
	IPT_CORPS_ALIGN_OP_RE						= 357;  // 结盟re
	IPT_LONGHUN_UPGRADE                         = 358;  // 龙魂晋升
	IPT_LMFSHOP_OP								= 359;  // 路明非商店操作
	IPT_INVITE_PLAYER_ADD_DISNEY_CARD			= 360;	// 日服招募——迪士尼卡兑换
	IPT_SKATEBOARD_END_GAME_AWARD				= 361;	// 彩虹航线发奖
	IPT_ZSPACE_OPERATOR                         = 362;
	IPT_ZSPACE_OPERATOR_RE                      = 363;
	IPT_IDIP_MODIFY_CONTENT						= 364;  // ACE合规需求，修改文本内容
	IPT_IDIP_MODIFY_CONTENT_RE					= 365;  // ACE合规需求，修改文本内容
	IPT_ROAM_COMMUNITY_OP						= 366;	// 跨服社团op
	IPT_ROAM_COMMUNITY_OP_RE					= 367;	// 跨服社团op_re
	IPT_DELIVER_HUNDRED_CORPS_BATTLE_CORPS_REPU          = 368; // 发放百团大战声望
	IPT_NATS_PB									= 369; 	// 通过nats发的协议包一层
	IPT_HUNDRED_CORPS_BATTLE_OPERATOR           = 370; // hub—> ds ds -> hub
	IPT_HUNDRED_CORPS_BATTLE_DEBUG               = 371; 
	IPT_HUNDRED_GUESS                            = 372; 
	IPT_NATS_MESSAGE_GENERAL					= 373;	// 通用nats消息
	IPT_BROADCAST_PVE_ZONES						= 374;	// ds广播pve服务器消息给gs
	IPT_CENTER_BATTLE_ROAM_CREATE_FINISH_NOTIFY	= 375;	// 中心服战场开好通知
	IPT_SEND_CENTER_BATTLE_RESULT 				= 376;	// 战场结果通知ds
	IPT_PLAYER_WEEKLY_UPDATE					= 377;	// 玩家周更新
	IPT_MOUNT_SPACE_OPERATION					= 378;	// 极影空间操作
	IPT_MOUNT_SPACE_OPERATION_RE				= 379;	// 极影空间操作re
	IPT_ROLE_TRADE_CHECK					= 380;	// 检查角色交易条件
	IPT_LOTTERY_MACHINE_ADD_MONEY_NOTIFY		= 381;	// 街头游戏机抽奖通知DS
	IPT_CROSS_MULTI_PVP_MSG						= 382;	// 多人跨服战场
	IPT_CROSS_MULTI_PVP_MSG_RE					= 383;	// 多人跨服战场_re
	IPT_SET_CORPS_HUNDRED_CROWN					= 384;	// 社团联赛金冠
	IPT_PVE_ARENA_TEMPORARY_GROUP				= 385;	// pve临时群组
	IPT_ARENA_CENTER_SINGLE_BATTLE_NOTIFY         		= 386;  // 浩瀚实训竞技场中心服管理服通知ds消息
	IPT_DRESSUP_PARTY_OP						= 387;	// 换装舞会操作
	IPT_DRESSUP_PARTY_OP_RE						= 388;	// 换装舞会返回
	IPT_CHANGE_PROF_NOTIFY						= 389;	// 转职通知pve
	IPT_STATUE_GET_PALYERS_DATA					= 390;	// 获取玩家雕像所需数据
	IPT_STATUE_GET_PALYERS_DATA_RE				= 391;	// 获取玩家雕像所需数据RE
	IPT_PB_RPC									= 392;	// pb rpc调用
	IPT_AI_CHAT									= 393;	//ai 聊天
	IPT_NEW_AUCTION_ROAM						= 394;	// 跨服拍卖行
	IPT_NEW_AUCTION_ROAM_RE						= 395;	// 跨服拍卖行RE
	IPT_SHARE_BOX								= 396;	// 宝箱彩票
	IPT_SHARE_BOX_RE							= 397;	// 宝箱彩票RE
	IPT_MAGIC_GATHERING_OP						= 398;	// 
	IPT_MAGIC_GATHERING_OP_RE					= 399;	// 
	IPT_NATS_SERVER_MSG							= 400;	// nats功能协议
	IPT_BROADCAST_ALL_ZONE_FORBID_ACCOUNT		= 401;	// 全区全服封禁广播
	IPT_SEND_MAIL2ORIGINALLY_SERVER				= 402;	// 发送邮件到本服
}
message ipt_nats_server_msg
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_NATS_SERVER_MSG];
	enum MSG_TYPE
	{
		MSG_TYPE_MODIFY_REPUTATION = 1;
	}
	optional MSG_TYPE msg_type				= 2;
	repeated int64 params					= 3;
}
message ipt_pve_arena_temporary_group
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_PVE_ARENA_TEMPORARY_GROUP];
	enum OP_TYPE
	{
		OP_TYPE_UPDATE	= 1;
		OP_TYPE_CHAT 	= 2;
	}
	optional OP_TYPE op_type				= 2;
	optional int64 roleid 					= 3;
	optional bytes chat_data				= 4;
}
message ipt_broadcast_pve_zones
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_BROADCAST_PVE_ZONES ];
	message pve_zone_info
	{
		optional int32 zoneid = 1;
		optional float grade = 2;
	}
	repeated pve_zone_info pve_zones	= 2;
}
message ipt_nats_message_general
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_NATS_MESSAGE_GENERAL ];
	enum NATS_MESSAGE_GENERAL_TYPE
	{
		NMGT_BROADCAST_NORMAL = 1;              //所有普通服能收到的广播协议
		NMGT_BROADCAST_CENTER_NORMAL = 2;       //所有普通服加pve中心服能收到的广播协议
		NMGT_BROADCAST_CENTER = 3;              //所有中心服能收到的广播协议
		NMGT_GENERAL = 4;                       //没有限制的协议
	}
	optional NATS_MESSAGE_GENERAL_TYPE message_type = 2;
	optional int32 content_type = 3;
	optional bytes content  = 4;
	optional int32 center_manager_id = 5;             // 如果不为0，就和当前的中心管理服进行比较，不同就忽略本消息
	optional int32 zoneid = 6;
}
enum PVE_SOCIAL_RELATION_OP
{	
  PVE_ADD_HARMONIOUS = 1;
  PVE_FLASH_HARMONIOUS_TASK = 2;
  PVE_FINISH_HARMONIOUS_TASK = 3;
  PVE_SELECT_HARMONIOUS_TASK = 4;
  PVE_ADD_INTIMATE = 5;
  PVE_FINISH_NEW_SECT_HOMEWORK = 6;
  PVE_ABANDON_HARMONIOUS_TASK = 7;
  PVE_ADD_AMITY = 8;
}
message	role_harmonious_task
{
  optional int64 roleid  = 1;
  optional int32 generate_task_time = 2;
  repeated harmonious_task task_list  = 3;
}
message role_pve_harmonious_data
{
  optional bytes  spouse_name  = 1; 
  optional bytes  spouse_msg   = 2; 
  repeated role_harmonious_task role_task = 3;
}

message role_pve_intimate_extra_data
{
  optional int64 roleid  = 1; //id
  optional bytes name    = 2; //称号
  optional int32 value = 3 [ default = 0];//本周的羁绊值
  optional int32 value_today = 4 [ default = 0];//本周的羁绊值
  optional int32 value_week  = 5 [ default = 0];//当日的羁绊值
  optional int32 value_modify_time = 6[default = 0];
  optional int64 intimate_id = 7;//羁绊id
}
message role_pve_new_sect_data
{
  optional int64 master_id			= 1;	//玩家师父ID
  optional int32 title_on_master		= 2;	//徒弟身上保存师父的称号
  optional int32 title_on_self			= 3;	//徒弟身上保存自己的称号
  optional int64 disciple_title_selected	= 4;	//师父选择生效的称号对应的的徒弟
  optional int32 partner_id			= 5;	//师父选择的助教ID
}
message role_pve_social_relation_data
{
  optional  role_pve_harmonious_data  pve_harmonious_data = 1;
  repeated  role_pve_intimate_extra_data pve_intimate_extra_data = 2;
  optional  role_pve_new_sect_data pve_new_sect_data = 3;
}
message ipt_pve_social_relation_op
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_PVE_SOCIAL_RELATION_OP ];
	optional PVE_SOCIAL_RELATION_OP op    =  2;
	optional int64 zoneid  =    3 [ default = 0] ;
	optional int64 roleid1  =    4;
	optional int64 roleid2  =    5;
	optional int32 param1   =    6 [ default = 0] ;
	optional int32 param2   =    7 [ default = 0] ;
	repeated role_harmonious_task tasks =  8;
	optional bool result  = 9 [default = true] ;
}


message ipt_nats_central_service
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_NATS_CENTRAL_SERVICE ];
	enum NATS_CENTRAL_SERVICE_TYPE
	{
		NATS_CENTRAL_SERVICE_TYPE_UPDATE_TO_MAIN	= 1;	//像总控服上报信息
		NATS_CENTRAL_SERVICE_TYPE_UPDATE_FROM_MAIN	= 2;	//总控服向其余服更新
		NATS_CENTRAL_SERVICE_TYPE_UPDATE_TO_SERVICE	= 3;	//向服务广播控制服
	}
	optional NATS_CENTRAL_SERVICE_TYPE service_type = 2;
	optional bytes service_id						= 3;
	message central_service
	{
		optional bytes service_group		= 1;
		optional bytes controller_subject	= 2;
	}
	repeated central_service central_services		= 4;
	optional int32 is_main							= 5;
}

message ipt_nats_service
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_NATS_SERVICE ];
	enum NATS_SERVICE_TYPE
	{
		NATS_SERVICE_TYPE_REGISTER		= 1;	//注册服务
		NATS_SERVICE_TYPE_KEEPALIVE		= 2;	//保活
		NATS_SERVICE_TYPE_UNREGISTER	= 3;	//取消注册
	}
	optional NATS_SERVICE_TYPE service_type	= 2;
	optional string service_id				= 3;
	optional int64 cur_timestamp			= 4;
	optional int64 service_begin_timestamp	= 5;
	optional int64 elapse_ms				= 6;
	optional int64 ping_time				= 7;
}

message ipt_intimate_fashion_dress_op
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_INTIMATE_FASHION_DRESS_OP ];
	optional INTIMATE_FASHION_DRESS_OP op				= 2;
	optional int64 role_id								= 3;
	repeated fashion_detail fd							= 4;
}

message ipt_roam_friend
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ROAM_FRIEND];
	enum OP_TYPE
	{
		OT_NOTIFY_ADD_LIST		= 1;	//添加好友申请列表变化
		OT_NOTIFY_AGREE_LIST	= 2;	//同意好友申请列表变化
		OP_NOTIFY_REFUSED		= 3;	//拒绝好友申请
		OP_NOTIFY_ONLINE		= 4;	//跨服好友上线
		OP_NOTIFY_ONLINE_RE		= 5;	//跨服好友上线的返回
		OP_NOTIFY_OFFLINE		= 6;	//跨服好友下线
	}
	optional OP_TYPE op_type			= 2;
	optional name_ruid name_ruids		= 3;
}
message bdsg_role_reward
{
  optional int64 role_id = 1;
  optional int32 reward_level = 2;
  optional int32 player_score = 3;
  optional int32 team_score = 4;
}
message ipt_bdsg_battle_result {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_BDSG_BATTLE_RESULT];
	required int32 battle_id	= 2;
	repeated bdsg_role_reward  role_reward = 3;
}
enum ARENA_CENTER_TEAM_BATTER_TYPE
{
  AR_SPEAK = 1 ; // 喊话广播
  AR_ACTIVTY_BEGIN =2; //活动开启
  AR_SYNC_TOP_LIST =3; //同步排行榜
  AR_ACTIVTY_END   =4;  //活动结束
  AR_ACTIVTY_INVITE_TICKET =5; //活动产生的邀请函
  AR_EXTRAL_REWARD  = 6;
}
enum ARENA_CENTER_TEAM_SPEAK_TYPE
{
  AR_FINAL_SPEAK = 1; //最终喊话
  AR_CONSECUTIVE_WIN_SPEAK = 2;//连胜喊话
  AR_END_CONSECUTIVE_WIN_SPEAK =3;//终结连胜喊话
}
message arena_speak_player_info
{	
	repeated name_roleid_pair role	= 1;
	optional int32 win_count        = 2; //连胜次数
	optional int32 speak_id         = 3; //喊话id
	optional ARENA_CENTER_TEAM_SPEAK_TYPE speak_type = 4;//喊话类型
}
message ipt_arena_center_team_battle_notify
{
   optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ARENA_CENTER_TEAM_BATTLE_NOTIFY ];
   optional ARENA_CENTER_TEAM_BATTER_TYPE ar_type = 2;
   optional int32 battle_type   = 3;
   optional int32 prof			= 4;
   optional int32 level_section	= 6;
   optional int32 tpn_id        = 7;
   optional int32 list_type   = 8;
   optional arena_speak_player_info speak_info = 9;
   repeated arena_search_info list  = 10;  //排行榜及其奖励相关
}
message ipt_idip_set_face_book
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_IDIP_SET_FACE_BOOK ];
	enum SET_TYPE
	{
		ST_CORPS = 1;	//帮派数据
		ST_HOMETOWN = 2;	//家园数据
		ST_PDD_ID  = 3 ; //
		ST_ELIMINATE_ID = 4;//
		ST_TEMPORARY_TEAM_ID = 5;
	}
	optional SET_TYPE set_type = 2;
	optional int64 corps_id = 3;
	optional int64 hometown_id = 4;
	optional int64 roleid = 5;
	optional int64 param = 6;
	optional int64 param2 = 7;
}

message bdsg_team_info
{
	optional int64 team_id			= 1;
	repeated name_roleid_pair nrp	= 2;
}

message ipt_bdsg_op
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_BDSG_OP ];
	optional BDSG_OP op								= 2;
	optional int32 ret								= 3;
	repeated int64 role_ids							= 4;
	optional int32 debug_period						= 5;
	optional int32 manager_center_zone_id			= 6;
	optional int32 target_center_zone_id			= 7;
	optional int32 match_id							= 8;
	optional int32 battle_inst_tid					= 9;
	optional int32 battle_inst_id					= 10;
	repeated bdsg_team_info bti						= 11;
	optional int32 team_id							= 12;
	optional int32 debug_match_team_max_threshold	= 13;
	optional int32 debug_match_team_max_size		= 14;
	optional int32 debug_battle_create_per_tick		= 15;
}

message ipt_bdsg_op_re
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_BDSG_OP_RE ];
	optional BDSG_OP op								= 2;
	optional int32 ret								= 3;
	repeated int64 role_ids							= 4;
	optional int32 match_id							= 5;
	optional int64 team_id							= 6;
	optional int32 battle_inst_status				= 7;
	optional int32 battle_inst_tid					= 8;
	optional int32 battle_inst_id					= 9;
	optional int32 roam_zone_id						= 10;
	optional BDSP_ROLE_STATUS role_status			= 11;
	optional ipt_bdsg_battle_result battle_result	= 12;
}

message ipt_leave_instance
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_LEAVE_INSTACE ];
	optional int32 inst_tid							= 2;
	optional int32 inst_id							= 3;
}

message ipt_bless_wall_op
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_BLESS_WALL_OP ];
	optional BLESS_WALL_OP op		= 2;
	optional int64 target_role		= 3;
	optional int32 msgid			= 4;
	optional bytes msg				= 5;
}

message ipt_fashion_dress_op
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_FASHION_DRESS_OP ];
	optional FASHION_DRESS_OP op					= 2;
	optional fashion_dress_info fdi					= 3;
	optional int32 scheme_score						= 4;
	optional fashion_dress_vote_info fdvi			= 5;
	optional int32 txnid							= 6;
	optional int32 retcode							= 7;
	optional int32 cur_period						= 8;
	optional int32 cur_atmosphere					= 9;
	optional fashion_dress_solicit_votes_info fdsvi	= 10;
	optional fashion_dress_bet_info fdbi			= 11;
}

message ipt_corps_bless
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CORPS_BLESS ];
	optional int64 target_role		= 2;
	optional int32 msgid			= 3;
	optional bytes msg			= 4;
}

message ipt_harmonious_operation
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_HARMONIOUS_OPERATION ];
	enum harmonious_op
	{
		HAR_QUERY = 1;	 // 请求 
		HAR_QUERY_RE = 2;  // 返回
	}	
	enum harmonious_op_type
	{
		HAR_QUERY_POS = 1;
		HAR_QUERY_AND_DELIVER = 2;
		HAR_CHECK_ABANDON_TASK =3; // 检查和放弃任务
		HAR_CHECK_DELIVER_TASK =4; // 检查和发放任务
		HAR_COOLPERIOD_DIVORCE = 5; //冷静期结束通知ds
		HAR_GS_PVE_MARRY_CHECK = 6; //
		HAR_GS_PVE_RENAME_CHECK = 7;
		HAR_GS_PVE_DIVORCE_SINGLE = 8;
		HAR_GS_PVE_DIVORCE_DOUBLE = 9;
		HAR_UPDATE_MARRIED_STATE = 10;
	}
	optional int64 target           = 2;
	optional int32 retcode          = 3;
	optional harmonious_op op		= 4;
	optional harmonious_op_type op_type = 5;
	optional player_pos_data  player_pos = 6;
	repeated int32	task_id_list = 7 ;  //操作任务列表 
	optional bool  initiative_abandon  = 8;  //是否主动放弃
	optional bytes name           = 9; 
	optional int32 id             = 10;
	optional int32 money          = 11;
	optional int32 harmonious_type = 12[default = 0];
}
message ipt_divorce_cool_period
{
	optional INNER_PROTOCBUF_TYPE type                              = 1 [ default = IPT_DIVORCE_COOL_PERIOD ];
    optional	bool is_remove  = 2;     
	optional	int64 groom_id  = 3;
    optional	int64 bride_id  = 4;
}
message ipt_harmonious_task_finish
{
	optional INNER_PROTOCBUF_TYPE type                              = 1 [ default = IPT_HARMONIOUS_TASK_FINISH ];
	optional int32 roleid  = 2 ;
	optional int32 task_id = 3 ; 
}
message ipt_pdd_team_op
{
	optional INNER_PROTOCBUF_TYPE type                              = 1 [ default = IPT_PDD_TEAM_OP ];
	enum pdd_team_op
	{
		PTO_CREATE = 1;
		PTO_JOIN = 2;
		PTO_CREATE_RESULT = 11;
		PTO_JOIN_RESULT = 12;
		PTO_QUIT_RESULT = 13;
	}
	optional pdd_team_op op_type	= 2;
	optional int64 target_roleid	= 3;
	optional int32 team_type	= 4;
	optional int32 txnid		= 5;
	optional int32 retcode		= 6;
	optional int32 cost		= 7;
	optional int64 team_id          = 8;
}

message ipt_pdd_team_db_op
{
	optional INNER_PROTOCBUF_TYPE type                              = 1 [ default = IPT_PDD_TEAM_DB_OP ];
	enum team_db_op
	{
		TDO_CREATE = 1;
		TDO_JOIN = 2;
		TDO_QUIT = 3;
		TDO_SAVE = 4;
		TDO_LOAD = 5;
		TDO_REWARD = 6;
		TDO_TIMEOUT = 7;
	}
	optional team_db_op db_op	= 2;
	optional int64 roleid           = 3;
	optional int64 target_roleid	= 4;
	optional int64 pdd_team_id 	= 5;
	optional db_pdd_team pdd_team = 6;
	repeated db_pdd_team pdd_teams = 7;
	optional int32 item_id		= 8;
	optional int32 item_count	= 9;
	optional int32 txnid		= 10;
	optional bool role_online       = 11;
	optional int32 param		= 12;
}

message ipt_breed_op
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_BREED_OP ];
	optional BREED_OP op							= 2;
	optional int64 target_role_id					= 3;
	optional int32 ret								= 4;
	optional bool is_initiater						= 5;
	optional breed_info initiator_breed_info		= 6;
	optional breed_info receiver_breed_info			= 7;
	optional breed_baodi_info bbi					= 8;
}
message ipt_center_corps_duel_request
{
    optional INNER_PROTOCBUF_TYPE           type                = 1 [ default = IPT_CENTER_CORPS_DUEL_REQUEST ];
    enum REQUEST_TYPE
    {
        REQUEST_INFO            = 1;    //请求
        REQUEST_DUEL            = 2;    //请求决斗
        REQUEST_UPLOAD          = 3;
        REPLY_DUEL              = 4;    //
        REQUEST_ENTER           = 5;
        DUEL_SWITCH_CLOSE       = 6;
        REQUEST_CREATE_DUEL     = 7;
        REQUEST_ROAM_TRANS      = 8;
    }
    optional REQUEST_TYPE                   request             = 2;
    optional int64                          roleid              = 3;
    optional int32                          zoneid              = 4;
    optional int32                          src_zoneid          = 5;
    optional int64                          invitee_corps_id    = 6;    //请求决战的对方社团ID
    optional int64                          inviter_corps_id    = 7;
    optional corps_duel_info                inviter_corps_info  = 8;    //请求决斗的时候，发送给对方社团自己的社团数据
    optional int32                          reply               = 9;
    repeated corps_duel_info                infos               = 10;
    optional int64                          corps_id            = 11;
    optional int32                          battle_id           = 12;
}
message ipt_center_corps_duel_notify
{
    optional INNER_PROTOCBUF_TYPE           type                = 1 [ default = IPT_CENTER_CORPS_DUEL_NOTIFY ];
    enum NOTIFY_TYPE
    {
        UPLOAD_INFO             = 1;
        NOTIFY_INFO             = 2;
        NOTIFY_DUEL             = 3;
        NOTIFY_REPLY            = 4;    //
        NOTITY_ERROR            = 5;
        NOTIFY_CREATE_DUEL      = 6;
        NOTIFY_DUEL_CLOSE       = 7;
        NOTIFY_SWITCH_CLOSE     = 8;
        NOTIFY_ROAM_CREATE_DUEL = 9;
    }
    optional NOTIFY_TYPE                    notify              = 2;
    optional int32                          result              = 3;
    optional int64                          roleid              = 4;
    repeated corps_duel_info                infos               = 5;
    optional int32                          zoneid              = 6;
    optional int64                          invitee_corps_id    = 7;
    optional int64                          inviter_corps_id    = 8;
    optional corps_duel_info                inviter_corps_info  = 9;
    optional int32                          reply               = 10;
    optional int32                          battle_id           = 11;
    optional int64                          corps_id            = 12;
	optional int32							manager_zoneid		= 13;
}
message ipt_repu_change_inform
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_REPU_CHANGE_INFORM ];
	optional int32 repuid			= 2;
	optional int32 cur_value		= 3;
	optional int32 offset			= 4;
}
message ipt_set_corps_crown
{
    optional INNER_PROTOCBUF_TYPE   type        = 1 [ default = IPT_SET_CORPS_CROWN ];
    optional int64                  roleid      = 2;
    optional int32                  crown       = 3;
    optional int32                  duration    = 4;
}
message ipt_set_corps_hundred_crown
{
    optional INNER_PROTOCBUF_TYPE   type        = 1 [ default = IPT_SET_CORPS_HUNDRED_CROWN ];
    optional int64                  roleid      = 2;
    optional int32                  crown       = 3;
    optional int32                  duration    = 4;
}
message ipt_temporary_team_join
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_TEMPORARY_TEAM_JOIN ];
	optional int64 roleid					= 2;
	optional int64 target_roleid			= 3;
	optional int64 temporary_team_id		= 4;
	optional db_temporary_team temporary_team = 5;
}

message ipt_temporary_team_quit
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_TEMPORARY_TEAM_QUIT ];
	optional int64 roleid					= 2;
	optional int64 temporary_team_id		= 3;
	optional db_temporary_team temporary_team = 4;
}

message ipt_temporary_team_save
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_TEMPORARY_TEAM_SAVE ];
	repeated db_temporary_team temporary_team = 2;
}

message ipt_temporary_team_load
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_TEMPORARY_TEAM_LOAD ];
	optional int64 roleid					= 2;
	optional int64 temporary_team_id		= 3;
	optional db_temporary_team temporary_team = 4;
}

message ipt_temporary_team_create
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_TEMPORARY_TEAM_CREATE ];
	optional int32 temporary_team_version   = 2;
	optional int64 roleid                                   = 3;
	optional db_temporary_team temporary_team = 4;
}

message child_show_info
{
	optional bytes child_name               = 1;
	repeated fashion_detail child_fashions   = 2;
	optional bytes parent_name		= 3;
}

message ipt_child_info_check
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CHILD_INFO_CHECK ];
	optional int64 roleid			= 2;
	optional uint64 child_guid	 	= 3;
	optional int32 check_type		= 4;
	//返回数据
	optional int32 retcode			= 5;
	optional child_show_info data		= 6;
}

message ipt_account_pursue
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ACCOUNT_PURSUE ];
	optional bytes account                  = 2;
	optional int32 money_type               = 3;
	optional int32 count                    = 4;
	optional int32 tm                       = 5;
	optional idip_info_t idip               = 6;
	optional int32 op_type			= 7;
}

message ipt_corps_season_notify
{
    optional INNER_PROTOCBUF_TYPE           type            = 1 [ default = IPT_CORPS_SEASON_NOTIFY ];
    enum CORPS_SEASON_NOTIFY_TYPE
    {
        CSNT_SEASON_INDEX           = 1;    //中心管理服向本服通知赛季变化
        CSNT_GET_SEASON             = 2;    //本服向中心服请求赛季索引
        CSNT_UPDATE_ROAM_TOPLIST    = 3;    //本服向中心服上报排行榜信息
        CSNT_SYNC_ROAM_TOPLIST      = 4;    //中心服将跨服排行榜同步给本服
        CSNT_DEBUG_SYNC_ROAM        = 5;    //调试命令本服强制同步跨服排行榜
    }
    optional CORPS_SEASON_NOTIFY_TYPE       notify_type     = 2;
    optional int32                          season_index    = 3;
    optional bool                           season_index_set= 4; 
    optional int32                          zoneid          = 5;
    repeated corps_season_toplist_item_t    rank            = 6;    //当CSNT_UPDATE_ROAM_TOPLIST时，这里只有一个元素，当CSNT_SYCN_ROAM_TOPLIST，则会同步整个列表
    optional corps_season_toplist_t         toplist         = 7;    //当CSNT_SEASON_INDEX时，会将中心服的整个排行榜更新给游戏服一份
}

message ipt_deliver_corps_season_member_repu
{
    optional INNER_PROTOCBUF_TYPE           type        = 1 [ default = IPT_DELIVER_CORPS_SEASON_MEMBER_REPU ];
    optional int64                          roleid      = 2;
    optional int64                          value       = 3;
    optional int32                          timestamp   = 4;
}

message ipt_deliver_corps_season_repu
{
    optional INNER_PROTOCBUF_TYPE           type        = 1 [ default = IPT_DELIVER_CORPS_SEASON_REPU ];
    enum INCR_TYPE
    {
        IT_PLAYER           = 1;
        IT_CORPS            = 2;
    }
    optional INCR_TYPE                      incr_type   = 2;
    optional CORPS_SEASON_ACTIVITY_TYPE     act_type    = 3;
    optional int32                          sub_type    = 4;
    optional int64                          self_id     = 5;
    repeated int64                          target_ids  = 6;
    repeated bytes                          target_names= 7;
    optional int64                          repu_id     = 8;
    optional int64                          repu_val    = 9;
    optional int32                          task_id     = 10 [ default = 0 ];
    optional int32                          param       = 11 [ default = 0 ];
}

message ipt_deliver_hundred_corps_battle_corps_repu
{
    optional INNER_PROTOCBUF_TYPE           type        = 1 [ default = IPT_DELIVER_HUNDRED_CORPS_BATTLE_CORPS_REPU ];
    enum INCR_TYPE
    {
        IT_PLAYER           = 1;
        IT_CORPS            = 2;
    }
    optional INCR_TYPE                      incr_type   = 2;
    optional CORPS_SEASON_ACTIVITY_TYPE     act_type    = 3;
    optional int32                          sub_type    = 4;
    optional int64                          self_id     = 5;
    repeated int64                          target_ids  = 6;
    repeated bytes                          target_names= 7;
    optional int64                          repu_id     = 8;
    optional int64                          repu_val    = 9;
    optional int32                          task_id     = 10 [ default = 0 ];
    optional int32                          param       = 11 [ default = 0 ];
}


message ipt_edit_diaoxiang
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_EDIT_DIAOXIANG ];
    optional int64  roleid              = 2;
    optional int32  diaoxiang_id        = 3;
    optional edit_diaoxiang_data_t data = 4;
    optional int32  src_zoneid          = 5;
}

message ipt_eliminate_group_refresh_status_broadcast
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ELIMINATE_GROUP_REFRESH_STATUS_BROADCAST ];
    optional int64  roleid              = 2;
    optional int32  diaoxiang_id        = 3;
    optional bytes  diaoxiang_status    = 4;
    optional int32  src_zoneid          = 5;
	optional int32  manager_zoneid		= 6;
}

message ipt_broadcast_diaoxiang
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_BROADCAST_DIAOXIANG ];
    optional int64  roleid              = 2;
    optional int32  diaoxiang_id        = 3;
    optional bytes  diaoxiang_status    = 4;
    optional int32  src_zoneid          = 5;
}

message ipt_eliminate_guess
{
    optional INNER_PROTOCBUF_TYPE   type        = 1 [ default = IPT_ELIMINATE_GUESS ];
    optional int64                  roleid      = 2;
    optional int64                  guess       = 3;
    optional int32                  order_index = 4;
    optional ELIMINATE_BATTLE_STATE state       = 5;
    optional int64                  cost        = 6;
    optional int32                  txnid       = 7;
    optional int32                  retcode     = 8;
}

message ipt_eliminate_group_refresh_status
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ELIMINATE_GROUP_REFRESH_STATUS ];
	optional int64 get_player_roleid		= 2 [ default = 0 ];
    repeated int32 get_player_diaoxiang_ids	= 3 ;
}

message ipt_eliminate_group_refresh_status_re
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ELIMINATE_GROUP_REFRESH_STATUS_RE ];
	optional bytes status					= 2;
}

message ipt_roam_search_brief
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ROAM_SEARCH_BRIEF ];
    optional int64  request_roleid          = 2;
	repeated name_ruid name_ruids		    = 3;
    optional int32  src_zoneid              = 4;
}

message ipt_roam_search_brief_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ROAM_SEARCH_BRIEF_RE ];
    optional int64 request_roleid           = 2;
	repeated name_ruid request_name_ruids	= 3;
	repeated role_brief role_briefs         = 4;
}
message ipt_centerbattle_fair_balance
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_FAIR_BALANCE ];
	message balance_t
	{
		optional int32 key = 1;
		optional int32 val = 2;
	}
	repeated balance_t balance = 2;
}

message ipt_ssp_vote
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SSP_VOTE ];
	optional int64 id			= 2;
	optional int32 value			= 3;
	optional int32 txnid			= 4;
	optional int32 retcode			= 5;
	optional int32 vote_type		= 6;
	optional int64 target_roleid		= 7;
}

message ipt_ssp_vote_refresh
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SSP_VOTE_REFRESH ];
}

message ipt_soul_child_op
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SOUL_CHILD_OP ];
	enum OP_TYPE
	{
		OT_CHOOSE = 1;
		OT_CHOOSE_RESULT = 2;
		OT_CULTURE_HISTORY = 3;
		OT_DELIVERY = 4;
		OT_DELIVERY_RESULT = 5;
		OT_CHILD_UPGRADE = 6;
		OT_TEAM_CHECK = 7;
		OT_SEND_DELETE = 8;
	}
	optional OP_TYPE op			= 2;
	optional int64 target_roleid		= 3;
	optional int64 choose_roleid		= 4;
	optional int64 intimate_id		= 5;
	optional uint64 child_id		= 6;
	optional int32 param			= 7;
	optional child_progress_history history = 8;
	optional int32 txnid			= 9;
	optional int32 retcode			= 10;
}

message ipt_soul_child_sync
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SOUL_CHILD_SYNC ];
	optional soul_child_info child		= 2;
}
message ipt_eliminate_battle_debug
{
    optional INNER_PROTOCBUF_TYPE type              = 1 [ default = IPT_ELIMINATE_BATTLE_DEBUG ];
    enum ELIMINATE_BATTLE_DEBUG_TYPE
    {
        EBDT_DEBUG          = 1;    //开启debug模式
        EBDT_STATE          = 2;    //设置状态
        EBDT_FORCE_ZONE     = 3;    //强制设置zoneid
        EBDT_CLEAR_RESULT   = 4;    //清空比赛结果重赛
        EBDT_CHAMPION       = 5;    //强制设置冠军
    }
    optional ELIMINATE_BATTLE_DEBUG_TYPE debug_type = 2;
    optional int64                      param1      = 3;
    optional int64                      param2      = 4;
    optional int64                      param3      = 5;
    optional int64                      param4      = 6;
    optional bytes                      data1       = 7;
    optional bytes                      data2       = 8;
}
message ipt_eliminate_group_grade_change
{
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_ELIMINATE_GROUP_GRADE_CHANGE];
	optional int64 eliminate_group_id	= 2;
	optional int32 battle_result        = 3;
	optional int32 grade_change         = 4;
}
message ipt_corps_eliminate_knockout_battle_enter
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CORPS_ELIMINATE_KNOCKOUT_BATTLE_ENTER ];
    optional int64 eliminate_group_id   = 2 [ default = 0 ];
    optional int32 watch_order_index    = 3;
}
message ipt_corps_eliminate_knockout_battle_enter_re
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CORPS_ELIMINATE_KNOCKOUT_BATTLE_ENTER_RE ];
    optional int32 result               = 2;
    optional int32 order_index          = 3;
    optional int32 watch_player_num     = 4;
    optional bool is_watch_battle       = 5 [ default = false ];
}
message eliminate_champion_status_t
{
    optional int64 roleid           = 1;
    optional bytes status           = 2;
}
message ipt_eliminate_battle_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ELIMINATE_BATTLE_NOTIFY ];
	enum ELIMINATE_BATTLE_NOTIFY_TYPE
	{
		EBNT_STATE 		= 1;
		EBNT_UPDATE 		= 2;
		EBNT_SCORE_RESULT 	= 3;		//这个状态仅限于从中心服GS向中心服DS通知跨服积分赛结果
		EBNT_MATCH		= 4;
		EBNT_BATTLE_END		= 5;
		EBNT_CONNECT_REFRESH 	= 6;
		EBNT_INST_CREATE 	= 7;
		EBTE_BATTLE_RESULT 	= 8;
		EBNT_KNOCKOUT_RESULT 	= 9;		//这个状态仅限于从中心服GS向中心服DS通知跨服淘汰赛的结果
		EBNT_SCORE_BATTLE_RESULT= 10;		//中心服向源服通知结果
		EBNT_WARMUP_END		= 11;		//热身赛结束
        EBNT_GET_STATE      = 12;
        EBNT_NOTIFY_CHAMPION= 13;           //同时产生淘汰赛冠军
        EBNT_CHAMPION_STATUS= 14;           //本服上报冠军战队status
        EBNT_BROADCAST_CHAMPION=15;         //中心服将冠军战队数据广播给所有的游戏服
		EBNT_IDIP_SET_SCORE		= 16;
		EBNT_IDIP_RE_MATCH		= 17;
		EBNT_IDIP_RENAME		= 18;
		EBNT_IDIP_REFRESH_BATTLE_INFO   = 19;   //IDIP刷新中心服battleinfo到普通服
	}
	optional ELIMINATE_BATTLE_NOTIFY_TYPE notify_type 	= 2;
	optional int32 eliminate_battle_state		= 3;
	optional int32 eliminate_battle_state_set		= 4;
	repeated corps_battle_info eliminate_battle_infos 	= 5;
	repeated corps_battle_order eliminate_battle_orders= 6;
	optional int32 creator_win			= 7;
	repeated int64 eliminate_creator			= 8;
	repeated int64 eliminate_target			= 9;
	optional int32 battle_inst_tid			= 10;
	optional int32 battle_inst_id			= 11;
    repeated eliminate_battle_candidate_guess candidate_guess = 12;
    optional int32 zoneid                   = 13;
    optional int32 score_change_creator     = 14;
    optional int32 score_change_target      = 15;
    optional int64 eliminate_id_creator     = 16;
    optional int64 eliminate_id_target      = 17;
    optional int64 eliminate_champion       = 18;
    repeated eliminate_champion_status_t champion_list = 19; 
	optional int64 group_id					= 20;
	optional int32 score					= 21;
}

message ipt_get_career_shop_partner_bonus			
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_GET_CAREER_SHOP_PARTNER_BONUS ];
	optional int32 money					= 2;
	optional int32 bonus_rank				= 3;
}

message ipt_sync_career_info						
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SYNC_CAREER_INFO ];
	message career_info
	{
		optional int32 career_type		= 1;
		optional int32 level			= 2;
	}
	repeated career_info career				= 2;
	repeated int32 research_cookbook		= 3;
	optional bool onlogin					= 4;
}

message ipt_npc_team_follow_enter_instance
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_NPC_TEAM_FOLLOW_ENTER_INSTANCE ];
	optional int32 teamid			= 2;
	optional int32 npc_leader_tid		= 3;
	optional int32 inst_tid			= 4;	//副本模板
	optional int32 inst_id			= 5;	//副本id
}

message ipt_deliver_soul_tree_harvest
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_DELIVER_SOUL_TREE_HARVEST ];
	repeated common_item_info item_list	= 2;
	optional int64 targetroleid	= 3;
	optional bool check = 4;
}

message ipt_check_corps_fight_capacity_toplist_rank
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CHECK_CORPS_FIGHT_CAPACITY_TOPLIST_RANK ];
	optional int64 roleid			= 2;
	optional int64 rank			= 3;
	optional int64 rank_roleid		= 4;
	optional int32 inst_tid         	= 5;	//副本tid
	optional int32 inst_id          	= 6;	//副本ID
	optional int32 lineid           	= 7;	//所在的GS
	optional uint32 scene_tag		= 8;
	optional uint32 mirror_id   		= 9;
}

message ipt_use_invite_code_point
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_USE_INVITE_CODE_POINT ];
	optional int32 service_id               = 2;
	optional int32 item_id                  = 3;
	optional int32 item_count               = 4;
	optional int32 repu_id                  = 5;
	optional int32 repu_value               = 6;
	optional int32 common_limit_id          = 7;
	optional int32 txnid                    = 8;
	optional int32 retcode                  = 9;
}

message ipt_db_set_delete_account
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_DB_SET_DELETE_ACCOUNT ];
	optional int32 is_delete				= 2; // 0 撤销删除， 1 删除
	optional bytes account					= 3;
	optional int32 delete_time				= 4;
}

message ipt_delete_account_check
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_DELETE_ACCOUNT_CHECK ];
	optional int32 retcode					= 2 [ default = 0];
	optional int64 trigger_roleid			= 3; // 要检查的角色id
	repeated int64 roleids_to_check			= 4; // 还未检查的角色id
	optional bytes account					= 5;
	optional int32 stage					= 6; // 检查的阶段, 1 客户端检查, 2 服务器身份验证前检查
}

message ipt_longyu_upgrade
{
	optional INNER_PROTOCBUF_TYPE type              = 1 [ default = IPT_LONGYU_UPGRADE ];
	optional int64 roleid				= 2;
	optional int32 base_longyu_id			= 3;
	optional int32 upgrade_longyu_id		= 4;
}
message ipt_longhun_upgrade
{
	optional INNER_PROTOCBUF_TYPE type              = 1 [ default = IPT_LONGHUN_UPGRADE ];
	optional int64 roleid				= 2;
	optional int32 base_longhun_id			= 3;
	optional int32 upgrade_longhun_id		= 4;
}


message ipt_check_soul_mate
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CHECK_SOUL_MATE ];
	enum CHECK_TYPE
	{
		CT_PARADING	= 1;	//双人巡游
	}
	optional CHECK_TYPE	check_type		= 2;
	optional int64 		applicant		= 3;
	optional int64 		spouse			= 4;
	optional int32 		retcode			= 5;
}

message ipt_log_notify
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_LOG_NOTIFY ];
	enum LOG_NOTIFY_TYPE
	{
		LNT_DROP_LIMIT = 1; // 掉落达到限次
	}
	optional LOG_NOTIFY_TYPE log_type = 2;

	message drop_limit_t
	{
		optional int32 limit_id = 1;
		optional int32 cur_circle = 2;
		optional int32 droped_count = 3;
	}
	optional drop_limit_t drop_data = 3;
}

message ipt_guard_marriage_check
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_GUARD_MARRIAGE_CHECK ];
	optional int64 initiator		= 2;
	optional int64 responder		= 3;
}

message ipt_guard_marriage_check_result
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_GUARD_MARRIAGE_CHECK_RESULT ];
	optional int32 result			= 2;
	optional int64 initiator		= 3;
	optional int64 responder		= 4;
}

message ipt_guard_breed_notify
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_GUARD_BREED_NOTIFY ];
	optional int64 spouse_owner				= 2;
	optional int32 guard_id					= 3;
	optional int32 breed_time				= 4;
}

enum CENTER_BATTLE_NOTIFY_TYPE
{
	CENTER_BATTLE_PLAYER_GAMEOVER		= 1;
	CENTER_BATTLE_PLAYER_PROF			= 2;
	CENTER_BATTLE_PLAYER_NEAR_DEATH		= 3;
	CENTER_BATTLE_PLAYER_RECONNECT		= 4;
	CENTER_BATTLE_PLAYER_MARK_MAP		= 5;
}

message ipt_arena_send_result_common
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_ARENA_SEND_RESULT_COMMON ];
	optional int32 creator_win				= 2;
	optional int32 battle_type				= 3;
	optional int32 battle_index				= 4;
}

message ipt_arena_minigame_start_result
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_ARENA_MINIGAME_START_RESULT ];
	optional int32 ret						= 2;
	optional int32 battle_type				= 3;
	optional int32 battle_index				= 4;
}

message ipt_arena_minigame_start
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_ARENA_MINIGAME_START ];
	optional int64 player1					= 2;
	optional int64 player2					= 3;
	optional int32 battle_type				= 4;
	optional int32 battle_index				= 5;
}

message ipt_daily_fake_login_logout
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_DAILY_FAKE_LOGIN_LOGOUT ];
}

message ipt_center_battle_player_notify
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CENTER_BATTLE_PLAYER_NOTIFY ];
	optional int32 inst_tid					= 2;
	optional int32 inst_id					= 3;
	optional int32 notify_type				= 4;
	optional int64 param					= 5;
}

message ipt_proxy_draw_notify_to_gs
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_PROXY_DRAW_NOTIFY_TO_GS ];
	optional int64 role_id					= 2;	// 代抽玩家
	optional bytes player_name				= 3;	// 代抽玩家名字
	optional PROXY_TYPE proxy_type			= 4;	// 代抽类型
	optional DRAW_TYPE draw_type			= 5;	// 抽奖类型
	optional int32 begin_time				= 6;	// 代抽开始时间戳
}

message ipt_proxy_draw_notify_to_ds
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_PROXY_DRAW_NOTIFY_TO_DS ];
	optional proxy_draw_result result		= 2;	// 代抽结果
}

message ipt_center_battle_group_get_player_status
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_GROUP_GET_PLAYER_STATUS ];
	optional int64 get_player_roleid		= 2 [ default = 0 ];
	optional int32 get_player_diaoxiang_id	= 3 [ default = 0 ];
	optional int32 get_player_zoneid		= 4 [ default = 0 ];
}

message ipt_center_battle_group_get_player_status_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_GROUP_GET_PLAYER_STATUS_RE ];
	optional bytes status					= 2;
}

message ipt_eliminate_group_get_player_status
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ELIMINATE_GROUP_GET_PLAYER_STATUS ];
	optional int64 get_player_roleid		= 2 [ default = 0 ];
	optional int32 get_player_diaoxiang_id	= 3 [ default = 0 ];
}

message ipt_eliminate_group_get_player_status_re
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ELIMINATE_GROUP_GET_PLAYER_STATUS_RE ];
	optional bytes status					= 2;
}

message ipt_career_shop_add_history
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CAREER_SHOP_ADD_HISTORY	];
	optional career_shop_history history	= 2;
	optional bool is_public					= 3;
}

message ipt_player_hometown_operation_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_PLAYER_HOMETOWN_OPERATION_RE ];
	optional db_hometown_operation_t operation = 2;
	optional int32 txnid					= 3;
	optional int32 retcode					= 4;
	optional csp_hometown_operation_t csp_operation = 5;
	optional int32 inst_id = 6;
	optional int64 hometown_owner = 7;
	optional npt_hometown_roommate_op_res roommate_operation = 8;
}

message csp_hometown_operation_t
{
	enum OP_TYPE
	{
		OP_TYPE_GIVE_GIFT              = 1;
		OP_TYPE_STEP                   = 2;
		OP_TYPE_VISITOR                = 3;
		OP_TYPE_QUERY_HOMETOWN_INFO    = 4;
	};
	
	optional OP_TYPE op_type            = 1;
	optional hometown_csp_req_info csp_req_info = 2;
	optional int32 popularity				= 3; //增加的人气值
	optional int32 speak_id					= 4;//送礼触发的喊话ID
	optional int32 speak_level				= 5;//送礼触发的喊话范围
}

message db_hometown_operation_t
{
	enum OP_TYPE
	{
		OP_TYPE_BUY                          = 1;
		OP_TYPE_OBJECT_BUY                   = 2;
		OP_TYPE_OBJECT_SELL                  = 3;
		OP_TYPE_OBJECT_PUT                   = 4;
		OP_TYPE_OBJECT_CANCEL                = 5;
		OP_TYPE_OBJECT_SPLIT                 = 6;
		OP_TYPE_OBJECT_BUY_AND_PUT           = 7;
		OP_TYPE_OBJECT_CANCEL_AND_SELL       = 8;
		OP_TYPE_OBJECT_EXTEND                = 9;
		OP_TYPE_UPDATE_RELATION              = 10;
		OP_TYPE_SELL                         = 11;
		OP_TYPE_MOVE                         = 12;
		OP_TYPE_OBJECT_UPDATE_OBJ_DATA       = 13;
		OP_TYPE_OBJECT_DEMOLISH              = 14;
		OP_TYPE_OBJECT_MULTI_PUT             = 15;
		OP_TYPE_OBJECT_MULTI_CANCEL          = 16;
		OP_TYPE_OBJECT_MULTI_UPDATE_OBJ_DATA = 17;
		OP_TYPE_OBJECT_ADD_BY_IDIP           = 18;
		OP_TYPE_OBJECT_DEL_BY_IDIP           = 19;
		OP_TYPE_OBJECT_MULTI_OPERATION       = 20;
		OP_TYPE_PARK                         = 21;
		OP_TYPE_RENEW                        = 22;
		OP_TYPE_UNPARK                       = 23;
		OP_TYPE_GRID_OPERATION               = 24;
		OP_TYPE_GRID_DEMOLISH                = 25;
		OP_TYPE_PARTY_END                    = 26;
		OP_TYPE_PARTY_AWARD                  = 27;
		OP_TYPE_SWITCH_SCHEME                = 28;
		OP_TYPE_UNLOCK_SCHEME                = 29;
		OP_TYPE_SET_SCHEME_NAME              = 30;
		OP_TYPE_UPLOAD_DESIGN_DATA           = 31;
		OP_TYPE_APPLY_DESIGN_DATA            = 32;
		OP_TYPE_RECOMMEND_DESIGN             = 33;
		OP_TYPE_PREVIEW_DESIGN               = 34;
		OP_TYPE_CHILD_ALLOC_ROOM             = 35;
		OP_TYPE_CHILD_CANCEL_ROOM            = 36;
        OP_TYPE_CONTRACT_BUY                 = 37;
        OP_TYPE_CONTRACT_SELL                = 38;
		OP_TYPE_CONTRACT_OBJECT_BUY_AND_PUT  = 39;
        OP_TYPE_CONTRACT_OBJECT_CANCEL_AND_SELL = 40;
		OP_TYPE_CONTRACT_OBJECT_SELL         = 41;
		OP_TYPE_CONTRACT_OBJECT_DEMOLISH     = 42;
		OP_TYPE_CONTRACT_PREVIEW_DESIGN      = 43;
		OP_TYPE_CONTRACT_RENEW               = 44;
        OP_TYPE_CONTRACT_DIVORCE             = 45;
		OP_TYPE_CONTRACT_GRID_DEMOLISH       = 46;
		OP_TYPE_CONTRACT_GRID_OPERATION      = 47;
        OP_TYPE_CONTRACT_BUTLER_EMPLOY       = 48;  //对方玩家雇佣管家通知
	};
	optional OP_TYPE op_type            = 1;
	optional int32 zoneid               = 2;
	optional int32 level_three          = 3;
	optional int32 level_two            = 4;
	optional int32 level_one            = 5;
	optional gp_hometown_op object_op	= 6; // 对家园中object进行操作的参数

	message hometown_sell_info
	{
		optional int64 bind_money	        = 1 [ default = 0 ];	// 奖励绑定钱
		optional int32 bind_cash	        = 2 [ default = 0 ];	// 奖励绑定元宝
		optional int64 spouse_bind_money	= 3 [ default = 0 ];	// 奖励绑定钱
		optional int32 spouse_bind_cash	    = 4 [ default = 0 ];	// 奖励绑定元宝
        optional bool force_single_sell     = 5 [ default = false ];    // 是否为强制单人卖家园
        optional bool divorce_sell          = 6 [ default = false ];    // 是否为离婚卖家园
        message obj_info
        {
            optional int32 obj_type = 1 [ default = 0 ];
            optional int32 count    = 2 [ default = 0 ];
        }
        repeated obj_info target_infos      = 7;
	}
	optional hometown_sell_info sell_info = 7;  
	
	message hometown_move_info
	{
		optional int32 move_type    = 1;
		optional int64 bind_money	= 2 [ default = 0 ];	// 奖励绑定钱
		optional int32 bind_cash	= 3 [ default = 0 ];	// 奖励绑定元宝
		optional int32 target_zoneid               = 4;
		optional int32 target_level_three          = 5;
		optional int32 target_level_two            = 6;
		optional int32 target_level_one            = 7;
		optional int64 spouse_bind_money	       = 8 [ default = 0 ];	// 奖励绑定钱
		optional int32 spouse_bind_cash	           = 9 [ default = 0 ];	// 奖励绑定元宝
	}
	optional hometown_move_info move_info = 8;

	message hometown_object_count_info
	{
		optional int32 object_type		= 1;
		optional int32 object_count		= 2;
        optional int32 master_count     = 3;
        optional int32 spouse_count     = 4;
	}
	repeated hometown_object_count_info count_info = 9;
	optional int64 operation_sequence  = 10;

	message hometown_repu_info
	{
		optional int32 repu_id      = 1;
		optional int32 repu_value   = 2;
        optional int32 master_value = 3;
        optional int32 spouse_value = 4;
	}
	repeated hometown_repu_info repu_info = 11;
	optional int32 guard_furniture_type = 12;
	
	message hometown_tlog_info
	{
		optional int32 cost_type = 1;
		optional int32 cost_value = 2;
		optional int32 repu_id = 3;
		optional int32 repu_value = 4;
	}
	optional hometown_tlog_info tlog_info = 13;
	optional bool do_demolish = 14 [ default = false];
	repeated gp_hometown_op_res.unpark_t unparks = 15; // OP_TYPE_UNPARK
	
	message hometown_party_info
	{
		optional int32  party_type  = 1;	
		optional int32  party_level = 2;	
		optional int32  award_item_id = 3; 
	}
	optional hometown_party_info party_info =  16; 

	optional int32 grid_operation_sequence = 17;

    optional int64 contract_roleid          = 18;
    optional bool is_master                 = 19;
	optional bool do_contract_demolish      = 20 [ default = false];
	
}

message ipt_player_hometown_operation
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_PLAYER_HOMETOWN_OPERATION];
	optional db_hometown_operation_t operation	= 2;
	optional int32 txnid					= 3;
	optional bytes rdata					= 4;
	optional csp_hometown_operation_t csp_operation = 5;
	// OT_UPLOAD_DESIGN_DATA param1:activityId
	message hometown_extra_param{
		optional int32 param1				= 1;
	}
	optional hometown_extra_param extra_param	= 6;
}

message ipt_new_sect_notify
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_NEW_SECT_NOTIFY];
	optional int64 roleid			= 2;
	optional int64 master			= 3;
}

message ipt_corps_garden_info
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CORPS_GARDEN_INFO ];
	optional int32 instid					= 2;
	optional corps_garden_info garden		= 3;		
}

message ipt_corps_garden_operation					
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CORPS_GARDEN_OPERATION ];
	optional int32 instid					= 2;
	optional CORPS_GARDEN_OPERATION op		= 3;		
	optional int32 region					= 4;
	optional int32 param1					= 5;
	optional int32 param2					= 6;
	optional int32 server_param1			= 7;
	optional int32 server_param2			= 8;
}

message ipt_corps_garden_sync_state					
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CORPS_GARDEN_SYNC_STATE	];
	optional int32 instid					= 2;
	message state_t
	{
		optional int32 region	= 1;
		optional int32 field	= 2;
		optional int32 state	= 3;
	}
	repeated state_t state					= 3;
	optional int32 begin_time				= 4;
}

message corps_center_battle3_info
{
	optional int64 id			= 1;
	optional bytes name			= 2;
	optional int32 badge		= 3;
	optional int32 score		= 4;
}
message ipt_corps_center_battle_create_battle
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CORPS_CENTER_BATTLE_CREATE_BATTLE ];
	optional int32 retcode 			= 2 [ default = 0 ];
	optional int32 manager_center_zone_id	= 3;
	optional int32 target_center_zone_id	= 4;
	optional int32 battle_tid		= 5;
	optional int32 battle_order_index	= 6;
	optional int64 corps_id_1		= 7;
	optional bytes corps_name_1		= 8;
	optional int32 corps_badge_1		= 9;
	optional int32 corps_score_1		= 10;
	optional int64 corps_id_2		= 11;
	optional bytes corps_name_2		= 12;
	optional int32 corps_badge_2		= 13;
	optional int32 corps_score_2		= 14;
	optional int32 battle_id		= 15;
	optional int32 battle_type		= 16;
	repeated corps_center_battle3_info corps_info = 17;
    repeated int64 corps_season_repus   = 18;
	optional int32 assist_score_1       = 19;
	optional int32 assist_score_2       = 20;
}

message ipt_corps_center_battle_trans_player
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CORPS_CENTER_BATTLE_TRANS_PLAYER ];
	optional int32 battle_id		= 2;
	optional int32 battle_tid		= 3;
	optional int32 center_battle_type		= 4;
	optional int64 roleid			= 5;
    optional int64 corps_id			= 6;
	optional int32 battle_type		= 7;
}

message corps_battle3_result_info
{
	optional int64 corps_id		= 1;
	optional int64 score		= 2;
	optional int32 rank			= 3;
}
message ipt_corps_center_battle_roam_finish
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CORPS_CENTER_BATTLE_ROAM_FINISH ];
	optional int32 src_zoneid		= 2;
	optional int32 battle_order_index	= 3;
	optional int32 creator_win 		= 4;
	optional int32 param			= 5;
	optional int32 param2			= 6;
	optional int32 battle_type		= 7;
	repeated corps_battle3_result_info corps_battle3_result = 8;	//社团竞赛3结果
}

message ipt_invite_friend_reward_sync
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_INVITE_FRIEND_REWARD_SYNC ];
	optional int32 version 			= 2;
	optional int32 role_total_bindcash	= 3;
}

message ipt_corps_center_battle_leave
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_CENTER_BATTLE_LEAVE ];
	optional int64 corps_id = 2;
	optional int32 battle_type = 3;
}

message ipt_diaoxiang_update
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_DIAOXIANG_UPDATE ];
    optional int64 roleid                       = 2;
	optional int32 diaoxiang_id					= 3 [ default = 0 ];
    optional edit_diaoxiang_data_t data         = 4;
}

message ipt_close_instance
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_CLOSE_INSTACNE ];
	repeated int32 inst_ids						= 2;
	optional int32 wait_time					= 3;
}

message ipt_corps_battle2_sub_battle_info
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CORPS_BATTLE2_SUB_BATTLE_INFO ];
	optional int32 battle_id				= 2;	//战场id
	optional int32 corps_one_score			= 3;	//社团1分数
	optional int32 corps_two_score			= 4;	//社团2分数
}

message ipt_player_start_bot
{
        optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_PLAYER_START_BOT ];
	optional bool start_guaji                       = 2;
	optional bool quit_mode                         = 3;    //是否开启静默模式
	optional bool half_quit_mode                    = 4;    //半静默模式
}

message ipt_career_shop_update_notify
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CAREER_SHOP_UPDATE_NOTIFY ];
	optional int64 roleid					= 2;
	optional int64 self_version				= 3;
	optional int64 public_version			= 4;
	optional int32 instid					= 5;
	optional bool refresh					= 6;
	optional int64 group_version			= 7;
}

message ipt_center_battle_group_get_member_status
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_GROUP_GET_MEMBER_STATUS ];
	optional int64 arena_group_id			= 2 [ default = 0 ];
	optional int32 get_member_zoneid		= 3 [ default = 0 ];
	optional int32 is_pve_arena_group		= 4 [ default = 0 ];
}

message ipt_center_battle_group_get_member_status_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_GROUP_GET_MEMBER_STATUS_RE ];
	repeated bytes status					= 2;
}

message ipt_center_battle_sync_info
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_SYNC_INFO ];
	optional int32 server_open_time			= 2 [ default = 0 ];
	optional int32 center_battle_type		= 3 [ default = 0 ];
	optional db_center_arena_team arena_team	= 4;
	enum SYNC_INFO_TYPE
	{
		SYNC_INFO_TYPE_SERVER_INFO			= 1;	//本服通知中心管理服服务器开服时间
		SYNC_INFO_TYPE_SERVER_INFO_RE		= 2;	//中心管理服通知本服战区信息
		SYNC_INFO_TYPE_FORCE_REFRESH		= 3;	//本服通知中心管理服强制刷新战区
		SYNC_INFO_TYPE_ARENA_GROUP			= 4;	//本服通知中心管理服战队信息
		SYNC_INFO_TYPE_ARENA_GROUP_RANKS	= 5;	//中心管理服通知本服战队排名
		SYNC_INFO_TYPE_ARENA_GROUP_ONE_RANK	= 6;	//中心管理服通知本服某战队积分变化
		SYNC_INFO_TYPE_GET_MEMBER_STATUS	= 7;	//中心管理服去普通服拉取玩家角色信息
		SYNC_INFO_TYPE_GET_MEMBER_STATUS_RE	= 8;	//中心管理服去普通服拉取玩家角色信息的返回
		SYNC_INFO_TYPE_CLIENT_GET_MEMBER_STATUS = 9;	//普通服去中心管理服拉取玩家角色信息
		SYNC_INFO_TYPE_CLIENT_GET_MEMBER_STATUS_RE	= 10;	//普通服去中心管理服拉取玩家角色信息的返回
		SYNC_INFO_TYPE_SET_ARENA_ZONE_CAPACITY = 11;	//设置中心管理服战区的战队容量
		SYNC_INFO_TYPE_DEBUG_SERVER_INFO	= 12;	//debug命令，插入一些假的服务器，用于测试战区
		SYNC_INFO_TYPE_SET_RANK_COUNT		= 13;	//debug命令，改变排行榜人数
		SYNC_INFO_TYPE_GET_PLAYER_STATUS	= 14;	//中心服去普通服获取单个玩家的角色信息
		SYNC_INFO_TYPE_GET_PLAYER_STATUS_RE	= 15;	//中心服去普通服获取单个玩家的角色信息的返回
		SYNC_INFO_TYPE_CLIENT_GET_PLAYER_STATUS	= 16;	//普通服去中心服获取单个玩家的角色信息
		SYNC_INFO_TYPE_CLIENT_GET_PLAYER_STATUS_RE	= 17;	//普通服去中心服获取单个玩家的角色信息的返回
		SYNC_INFO_TYPE_SERVER_HIS_RANKS		= 18; //中心管理服通知本服历史排行榜信息
		SYNC_INFO_TYPE_SERVER_INFO_BROADCAST	= 19;	//中心服通知所有本服战区信息
		SYNC_INFO_TYPE_ARENA_GROUP_NAME		= 20;	//本服通知中心管理服战队改名
		SYNC_INFO_TYPE_SERVER_LEVEL			= 21;	//本服通知中心管理服服务器等级
		SYNC_INFO_TYPE_SERVER_LEVEL_RE		= 22;	//本服通知中心管理服服务器等级RE
		SYNC_INFO_TYPE_ASK_STATE			= 23;	//本服询问中心服状态
		SYNC_INFO_TYPE_SYNC_STATE			= 24;	//中心服通知本服对状态
		SYNC_INFO_TYPE_INC_SYNC_STATE		= 25;	//中心服通知本服对状态
	}
	optional SYNC_INFO_TYPE info_type		= 5;
	optional name_ruid arena_group_info		= 6;
	optional int32 arena_group_grade		= 7 [ default = 0 ];
	optional int32 arena_group_zone			= 8 [ default = 0 ];
	optional db_arena_group_ranks arena_group_ranks	= 9;
	optional int32 arena_group_rank_timestamp	= 10 [ default = 0 ];
	optional int32 arena_group_zone_timestamp	= 11 [ default = 0 ];
	optional int64 arena_group_id			= 12 [ default = 0 ];
	repeated bytes arena_group_member_status	= 13;
	repeated int32 support_zones			= 14;
	repeated int64 get_member_arena_group_ids	= 15;
	repeated bytes get_member_status		= 16;
	optional int32 get_member_zoneid		= 17;
	optional int32 arena_zone_capacity		= 18;
	message debug_server_info
	{
		optional int32 zoneid		= 1;
		optional int32 server_open_time	= 2;
	}
	repeated debug_server_info debug_servers	= 19;
	optional int32 debug_rank_count			= 20;
	optional int64 get_player_status_roleid	= 21;
	optional int32 get_player_status_diaoxiang_id	= 22;
	optional int32 get_player_zoneid		= 23;
	optional bytes get_player_status        = 24;
	optional arenagroup_rank_t arenagroup_data = 25; //跨服天梯队伍成员列表
	optional int32 server_level				= 26;
	message order_info
	{
		optional int32 order_index		= 1;
		optional int64 order_one		= 2;
		optional int64 order_two		= 3;
		optional int64 create_battle_time = 4;
		optional int32 battle_state		= 5;
		optional int32 battle_zoneid	= 6;
		optional int32 battle_instid	= 7;
		optional int32 player_num_one	= 8;
		optional int32 player_num_two	= 9;
		optional int32 winner			= 10;
		optional int32 order_one_param	= 11;
		optional int32 order_two_param	= 12;
		message punish_time
		{
			optional int64 roleid	= 1;
			optional int64 timestamp = 2;
		}
		repeated punish_time punishs	= 13;
	}
	repeated order_info orders				= 27;	// 对阵表
	optional int32 cur_state				= 28;
	optional order_info order_inc_info		= 29;	// 对阵表
}

message ipt_intimate_party_begin
{
       optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_INTIMATE_PARTY_BEGIN ];
       optional int64 applicant                = 2;    
       optional int64 target                   = 3;
       optional int32 scene_tag                = 4;
}

message ipt_top_star_report
{
	message star_info
	{
		optional int32 star_id	= 1;
		optional int32 cheer	= 2;
	}
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_TOP_STAR_REPORT ];
	optional star_info total_top		= 2;
	repeated star_info daily_rank		= 3;
}

message ipt_set_forbid_name
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_SET_FORBID_NAME ];
	optional int32 forbid_name			= 2;
}

message ipt_top_star_cheer
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_TOP_STAR_CHEER ];
	optional int32 star_id				= 2;
	optional int32 count				= 3;
	optional int32 daily_cheer			= 4;
	optional int32 total_cheer			= 5;
	optional int32 star_daily_cheer		= 6;
	optional int32 star_total_cheer		= 7;
	optional int32 most_support_star	= 8;
}

message ipt_star_topic_share
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_STAR_TOPIC_SHARE ];
	optional string topics				= 2; // 客户端随机好的完整热手榜单，服务器只负责转发
}

message ipt_center_battle_debug
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_DEBUG ];
	enum DEBUG_TYPE
	{
		SET_CORPS_BATTLE_TIME		= 1;
		CLEAR_CORPS_BATTLE		= 2;
		SET_CORPS_BATTLE_SET_LOADING	= 3;
		SET_CORPS_BATTLE_FINISH_LOADING	= 4;
		SET_CORPS_WIN_COUNT		= 5;
	}
	optional DEBUG_TYPE	debug_type	= 2;
	optional int64		param		= 3;
	optional int64		param2		= 4;
	optional int64		param3		= 5;
	optional int64		param4		= 6;
	optional int64		param5		= 7;
	optional bytes		data1		= 8;
	optional bytes		data2		= 9;
}

message ipt_sync_friend_help_data
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SYNC_FRIEND_HELP_DATA ];
	optional int32 version                  = 2;
	message help_data
	{
		optional int32 id = 1;
		optional int32 count = 2;
	}
	repeated help_data help_counts          = 3;
}

message ipt_ds_achievement
{
	enum ACHIEVEMENT_TYPE {
		STATE_ACHIEVEMENT = 0;
		EVENT_ACHIEVEMENT = 1;
	}
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_DS_ACHIEVEMENT ];
	optional ACHIEVEMENT_TYPE achievement_type	= 2;
	optional int32 condition_index	= 3;
	repeated int32 params	= 4;
}
message ipt_new_sect_partner_op
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_NEW_SECT_PARTNER_OP ];
	enum SECT_PARTNER_OP_TYPE
	{
		HIRE_PARTNER	= 1;
		FIRE_PARTNER	= 2;
	}
	optional SECT_PARTNER_OP_TYPE	op	= 2;
	optional int64			roleid	= 3;
	optional int32			partner	= 4;
}

message ipt_corps_boss_score_inform
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_BOSS_SCORE_INFORM ];
	optional int64 corps_id			= 2;
	optional int32 score1			= 3;	
	optional int32 score2			= 4;	
}

message ipt_corps_instance_score_inform
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_INSTANCE_SCORE_INFORM ];
	optional int64 corps_id			= 2;
	optional int32 score			= 3;	
}
message ipt_on_task_finish
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_ON_TASK_FINISH ];
	optional int64 roleid			= 2;
	optional int32 task_id			= 3;	
	optional int32 finish_count		= 4;	
}
message ipt_new_sect_graduate_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_NEW_SECT_GRADUATE_NOTIFY ];
	enum NOTIFY_TYPE
	{
		NT_SELF_GRADUATE	= 1;
		NT_DISCIPLE_GRADUATE	= 2;
	}
	optional NOTIFY_TYPE notify_type	= 2;
	optional int64 roleid			= 3;	
	optional int32 param			= 4;
}
message ipt_corp_ranklist_award	{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CORP_RANKLIST_AWARD	];
	optional int32 ranklist_id				= 2;
}

message ipt_update_corp_ranklist {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_UPDATE_CORP_RANKLIST ];
	optional int32 ranklist_id				= 2;
}

message pleasure_ip_t
{
	optional int32 campaign_end_time  = 1;
	optional int32 all_draw_count = 2;
	message role_t
	{
		optional int64 roleid = 1;		
		optional int32 draw_count = 2;
	}
	repeated role_t roles = 3;
}


message limit_lottery_ip_t
{
	optional int32 campaign_end_time  = 1;
	optional int32 cur_state = 2;
	optional int32 cur_state_end_time = 3;
	optional int32 inner_round_count = 4;
	optional limit_lottery_reward reward = 5;
	optional int32 inner_count_period = 6; 
	optional int32 inner_count_add = 7; 
	optional int32 next_add_inner_ts = 8; 
	message role_t
	{
		optional int64 roleid    = 1;
		optional int32 out_round_count    = 2;
		optional int32 get_reward    = 3;
	}
	repeated role_t role_lottery  = 9;
}

message ipt_limit_pleasure_count_notify
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_LIMIT_PLEASURE_COUNT_NOTIFY ];
	optional int64 roleid                           = 2;
	optional int32 count                = 3;
}

message ipt_limit_lottery_count_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_LIMIT_LOTTERY_COUNT_NOTIFY ];
	optional int64 roleid				= 2;
	optional int32 lottery_type			= 3;
	optional int32 count                = 4;
}

message ipt_god_explore_count_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_GOD_EXPLORE_COUNT_NOTIFY ];
	optional int64 roleid				= 2;
	optional int32 add_count			= 3; // 增加boss伤害
	optional int32 set_level			= 4; // 层
	optional int32 set_level_count		= 5; // 层抽奖次数
}

message ipt_night_reward_item_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_NIGHT_REWARD_ITEM_NOTIFY ];
	optional int64 roleid				= 2;
	optional int32 item_id				= 3;
	optional int32 lottery_id			= 4;
}

message ipt_center_battle_debug_clear_player
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_DEBUG_CLEAR_PLAYER ];
	optional int32 center_battle_type	= 2;
	optional int32 battle_type			= 3;
	optional int64 roleid				= 4;
}

message ipt_center_battle_check_state
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CENTER_BATTLE_CHECK_STATE ];
	optional int32 center_battle_type	= 2;
	optional int32 battle_type			= 3;
	optional int64 roleid				= 4;
	optional int32 retcode					= 5;
	optional int32 to_client			= 6;
}

message ipt_auction_close
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_AUCTION_CLOSE ];
	optional int32 item_id					= 2;
	optional int32 item_num					= 3;
	optional int32 price					= 4;
	optional bytes extra_data				= 5;
}

message ipt_update_complicated_data
{
	optional INNER_PROTOCBUF_TYPE type = 1 [ default = IPT_UPDATE_COMPLICATED_DATA ];
	optional role_complicated_data_collection collection = 2;
	optional role_complicated_data_fashion_beauty_info fashion_beauty_data = 3;
	optional role_complicated_data_baby_fashion_beauty_info baby_fashion_beauty_data = 4;
	optional role_complicated_data_cute_pet_fashion_beauty_info cute_pet_fashion_beauty_data = 5;
}

message ipt_career_media_upload_req							
{
	optional INNER_PROTOCBUF_TYPE type = 1 [ default = IPT_CAREER_MEDIA_UPLOAD_REQ ];
	optional bytes url			= 2;
	optional int32 career_level = 3;
	optional int32 weight		= 4;
}

message ipt_career_media_upload_rep							
{
	optional INNER_PROTOCBUF_TYPE type = 1 [ default = IPT_CAREER_MEDIA_UPLOAD_REP ];
	optional int32 retcode		= 2;
}

message ipt_idip_forbid_player_func
{
	optional INNER_PROTOCBUF_TYPE type = 1 [ default = IPT_IDIP_FORBID_PLAYER_FUNC ];
	repeated int32 forbid_funcs = 2; // 被禁用的开关
	repeated int32 permit_funcs = 3; // 禁用后又开启的开关
	message forbid_info
	{
		optional int32 id = 1;
		optional bool forbid = 2;
	}
	repeated forbid_info forbid_task = 4;
	repeated forbid_info forbid_instance = 5;
	repeated forbid_player_func_info forbid_func_infos = 6;	//被禁用的开关（用于取代2）
}
message ipt_on_gloden_initmate_anniversary_task_finished
{
	optional INNER_PROTOCBUF_TYPE type              = 1 [ default = IPT_ON_GLODEN_INITMATE_ANNIVERSARY_TASK_FINISHED ];
	optional int64 roleid				= 2;		//玩家roleid
	optional int32 task_id				= 3;		//任务id
	optional int32 gold_anni_id			= 4;		//纪念日id
	optional int64 target_id			= 5;		//对方的roleid
}
message ipt_update_player_appearance
{
	optional INNER_PROTOCBUF_TYPE type              = 1 [ default = IPT_UPDATE_PLAYER_APPEARANCE ];
	optional bytes data                             = 2;
}

message ipt_fauction_load_from_ip						
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_FAUCTION_LOAD_FROM_IP ];
}

message ipt_change_mirror_to_team_leader_invite
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CHANGE_MIRROR_TO_TEAM_LEADER_INVITE ];
	optional int32 team_id					= 2;
}
message ipt_change_mirror_to_team_leader
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CHANGE_MIRROR_TO_TEAM_LEADER ];
	optional int32 leader_mid				= 2;
}

// IDIP_DATA_TYPE_STATE单开关数据类型
enum IDIP_DATA_TYPE_FORBID_STATE_TYPE
{
	IDIP_DATA_TYPE_FORBID_STATE_NOUSE						= 1;	// 待用
}


message ipt_idip_data
{
	optional INNER_PROTOCBUF_TYPE type						= 1 [ default = IPT_IDIP_DATA ];
	optional IDIP_DATA_TYPE data_type						= 2;	// 数据类型
	message func_switch_data
	{
		optional int32 func_code							= 1;	// 功能码
		optional bool open									= 2;	// 是否打开
	}
	repeated func_switch_data func_switch					= 3;	// 改变的功能开关
	message forbid_data
	{
		optional int32 id									= 1;	// 具体功能id
		optional bool forbid								= 2;	// 是否禁止
	}
	repeated forbid_data forbid_state						= 4;	// 禁止单开关，id为IDIP_DATA_TYPE_FORBID_STATE_TYPE
	repeated forbid_data forbid_item_use					= 5;	// 禁止使用的物品，id为item_tid
	repeated forbid_data forbid_item_inc					= 6;	// 禁止增加的物品，id为item_tid
	repeated forbid_data forbid_item_gift					= 7;	// 禁止赠送的物品(不改变物品本身属性)，id为item_tid
	repeated forbid_data forbid_item_deal					= 8;	// 禁止交易的物品(不改变物品本身属性)，id为item_tid
	repeated forbid_data forbid_addon						= 9;	// 禁止玩家属性，id为addon_id
	repeated forbid_data forbid_addon_group					= 10;	// 禁止玩家属性组，id为addon_group_id
	repeated forbid_data forbid_retrieve					= 11;	// 禁止找回奖励，id为activity_id
	repeated forbid_data forbid_npc_shop					= 12;	// 禁止npc随身商店，id为shop_id
	message forbid_npc_shop_item_data
	{
		optional int32 shop_id								= 1;	// npc随身商店shop_id
		optional bool forbid								= 2;	// 是否禁止
		optional int32 item_tid								= 3;	// 物品item_tid
	}
	repeated forbid_npc_shop_item_data forbid_npc_shop_item	= 13;	// 禁止npc随身商店物品
	repeated forbid_data forbid_enter_scene					= 14;	// 禁止玩家进入场景，id为scene_tag
	repeated forbid_data forbid_repu_inc					= 15;	// 禁止增加的声望，id为声望id
	repeated forbid_data forbid_repu_dec					= 16;	// 禁止减少的声望，id为声望id
	repeated forbid_data forbid_mall_goods					= 17;	// 禁止商城购买的物品，id为goods_id
	repeated forbid_data forbid_common_use_limit			= 18;	// 禁止通用限次模版(禁止=使用次数达上限)，id为limit_id
	repeated forbid_data forbid_matter						= 19;	// 禁止矿物刷出并杀掉已有，id为matter_tid
	repeated forbid_data forbid_skill						= 20;	// 禁止技能使用，id为skill_id
	repeated forbid_data forbid_drop_data					= 21;	// 禁止掉落表，id为table_id
	repeated forbid_data forbid_task_del					= 22;	// 删除已经接的任务，id为任务id
	repeated forbid_data forbid_task						= 23;	// 禁止任务，并不删除已经接的，id为任务id
	repeated forbid_data forbid_npc							= 24;	// 禁止npc刷出并杀掉已有，id为npc_tid
	repeated forbid_data forbid_instance					= 25;	// 禁止副本，id为instance_id
	message forbid_npc_service_data
	{
		optional int32 service_id							= 1;	// 服务id
		optional bool forbid								= 2;	// 是否禁止
		optional int32 service_para							= 3;	// 服务参数
	}
	repeated forbid_npc_service_data forbid_npc_service		= 26;	// 禁止npc服务
	repeated forbid_data forbid_reward						= 27;	// 禁止奖励模版，id为reward_tid
	message forbid_scene_spawn_data
	{
		optional int32 spawn_id								= 1;	// 控制器id
		optional int32 scene_tag							= 2;	// 场景scene_tag
		optional bool forbid								= 3;	// 是否禁止
	}
	repeated forbid_scene_spawn_data forbid_scene_spawn		= 28;	// 禁止场景控制器
	message forbid_c2s_protocol_data
	{
		optional int32 protocol								= 1;	// c2s协议id
		optional bool forbid								= 2;	// true为禁止，false为设置状态
		optional bytes valid_state							= 3;	// 处理协议状态
		optional bytes invalid_state						= 4;	// 不处理协议状态
	}
	repeated forbid_c2s_protocol_data forbid_c2s_protocol	= 29;	// 禁止C2S协议，id为c2s协议id
	message debug_para_data
	{
		optional int32 debug_para_server_level				= 1;	// 服务器等级
	}
	optional debug_para_data debug_para						= 30;	// 一些idip更改过需要存盘的数据
	repeated forbid_data forbid_mount_unlock				= 31;	// 禁止座驾解锁, id为mount_tid
	repeated forbid_data forbid_mount_produce				= 32;	// 禁止座驾制造, id为surface_tid
	repeated forbid_data forbid_mount_paint_color			= 33;	// 禁止座驾喷涂, id为surface_tid
	repeated forbid_data forbid_mount_activate_color		= 34;	// 禁止座驾解锁颜色, id为surface_tid
	repeated forbid_data forbid_mount_select_fashion		= 35;	// 禁止座驾改装, id为surface_tid
	repeated forbid_data forbid_fashion_unlock				= 36;	// 禁止解锁指定时装
	repeated forbid_data forbid_fashion_paint				= 37;	// 禁止染色指定时装
	repeated forbid_data forbid_fashion_activate_color		= 38;	// 禁止指定时装激活颜色
	repeated forbid_data forbid_corps_set_badge				= 39;	// 禁止设置指定社团徽章
	message forbid_corps_func_data
	{
		optional int64 corps_id								= 1;	// 社团id
		repeated int32 forbid_funcs							= 2;	// 该社团被禁用的功能码列表
		repeated int32 permit_funcs							= 3;	// 解禁的团功能码列表
	}
	repeated forbid_corps_func_data forbid_corps_func		= 40;	// 禁止某社团的某功能
	repeated forbid_data forbid_put_in_depository			= 41;	// 禁止将某物品存入仓库
	repeated forbid_data forbid_take_out_depository			= 42;	// 禁止将某物品从仓库中取出
	repeated forbid_data forbid_career_level_up				= 43;	// 禁止对指定id的个人身份进行升级
	repeated forbid_data forbid_career_cook_produce			= 44;	// 禁止使用指定食谱进行烹饪
	repeated forbid_data forbid_career_learn_cookbook		= 45;	// 禁止学习指定id的食谱
	repeated forbid_data forbid_career_unlock_cookbook		= 46;	// 禁止解锁指定id的食谱
	message forbid_player_func_data
	{
		optional int64 roleid								= 1;	// 角色id
		repeated int32 forbid_funcs							= 2;	// 被禁止的功能码
		repeated int32 permit_funcs							= 3;	// 解禁的功能码
		repeated forbid_player_func_info forbid_func_infos	= 4;	// 被禁止的功能码（取代forbid_funcs）
	}
	repeated forbid_player_func_data forbid_player_func		= 47;	// 禁止指定玩家指定某功能
	message forbid_mini_game_data
	{
		optional int32 game_type							= 1;
		optional int32 game_id								= 2;
		optional bool forbid								= 3;
	}
	repeated forbid_mini_game_data forbid_mini_game			= 48;	// 禁止运行指定id的小游戏
	repeated forbid_data forbid_interact_templ				= 49;	// 禁止某个tid的交互模板
	repeated forbid_data forbid_display_top_list			= 50;	// 禁止客户端显示某个排行榜
	repeated forbid_data forbid_reward_top_list				= 51;	// 禁止对某个排行榜发奖
	repeated forbid_data forbid_mount_train					= 52;	// 禁止指定座驾进化
	repeated forbid_data forbid_career_freeze				= 53;	// 禁止指定身份冻结
	repeated forbid_data forbid_task2                                       = 54;
	message replace_addon_data
	{
		optional int32 addon_id								= 1;
		optional int32 replace_addon_id						= 2;
	}
	repeated replace_addon_data replace_addon				= 55;	// 替换附加属性
	repeated forbid_data forbid_fake_auction_buy			= 56;	// 禁止从假拍卖行购买指定id的物品
	repeated forbid_data forbid_auction_trade				= 57;	// 禁止拍卖行指定id物品的交易
	repeated forbid_data forbid_auction_longyu_trade		= 58;	// 禁止拍卖行指定id龙语的交易
	repeated forbid_data forbid_career						= 59;	// 禁止指定id的身份
	repeated forbid_data forbid_auction_recharge_limit		= 60;	// 解除拍卖行指定物品的购买充值限制
	repeated forbid_data forbid_auction_longyu_recharge_limit = 61;	// 解除拍卖行指定物品的购买充值限制
	repeated forbid_data forbid_hometown_furniture          = 62;	// 禁止家园家具
	repeated forbid_data forbid_mount_effect				= 63;	// 禁止指定座驾焕彩
}

// idip不需要存盘数据ds同步给gs
message ipt_gm_cmd
{
	optional INNER_PROTOCBUF_TYPE type						= 1	[ default = IPT_GM_CMD ];
	optional uint64 role_id									= 2;
	enum GM_CMD_TYPE
	{
		GCT_TRIGGER_SPAWN									= 1;
		GCT_RELOAD                                          = 2;
		GCT_KICK_PLAYER				= 3;
		GCT_TCMALLOC_HEAP_OP 			= 4;
	};
	required GM_CMD_TYPE cmd_type							= 3;
	repeated int64 params									= 4;
	optional bytes stringparams								= 5;
}

message ipt_personality_modify							
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_PERSONALITY_MODIFY ];
	optional int32 modify_type				= 2;
	optional int32 delta					= 3 [ default = 1];
}

message idip_info_t
{
	optional int32 cmdid = 1;
	optional int32 seqid = 2;
	optional bytes servicename = 3;
	optional int32 sendtime = 4;
	optional int32 version = 5;
	optional bytes authenticate = 6;
	optional int32 web_sid  = 7;
	optional int32 xid = 8;
	optional int32 proxyid = 9;
}

message ipt_query_task_status
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_QUERY_TASK_STATUS ];
	optional idip_info_t idip			= 2;
	optional int32 op				= 3; // 0 查询完成状态 1 完成任务
	optional int32 task_id				= 4;
	optional int32 status				= 5;
	optional int32 retcode				= 6;
}

message ipt_fake_auction_refresh
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_FAKE_AUCTION_REFRESH ];
}

message ipt_gs_check_bad_word
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_GS_CHECK_BAD_WORD ];
	optional int64 roleid				= 2;
	enum CHECK_TYPE
	{
		CHECK_PET_NAME	= 1;	//检查宠物名
		CHECK_GIT_MSG	= 2;	//检查礼物寄语
		CHECK_CHILD_NAME= 3;	//检查孩子名字
		CHECK_GUARD_NAME= 4;	//守护灵改名
		CHECK_SKILL_NAME= 5;	//检查技能组合
		CHECK_TALENT_NAME= 6;	//检查言灵组合？天赋组合？
		CHECK_AUTO_COMBAT_SET_NAME= 7;//检查辅助战斗方案名字
		CHECK_CAREER_SHOP_NAME = 8;	//小店改名
		CHECK_CAREER_SHOP_SLOGAN = 9;	//小店改宣传语
		CHECK_ADVENTURE_DETECTIVE= 10;  //侦探异闻的标记
        	CHECK_HOMETOWN_WELCOME_TIP = 11;//家园欢迎语
		CHECK_BREED_CUTE_PET_NAME = 12;	//育宠达人萌宠改名
        	CHECK_HOMETOWN_WELCOME_TIP_UPDATE = 13;//家园欢迎语更新
		CHECK_HOMETOWN_GIFT_MSG		= 14; //家园礼物寄语
		CHECK_DYNAMIC_GIFT_MSG		= 15; //朋友圈礼物寄语
		CHECK_SOLUTION				= 16; //方案名
		CHECK_FASHION_SCHEME		= 17; //衣橱时装方案名
		CHECK_LONGYU_SOLUTION		= 18; //龙语方案名
		CHECK_PARTNER_SOLUTION		= 19; //伙伴方案名
		CHECK_LONGHUN_SOLUTION		= 20; //龙魂方案名
	}
	optional CHECK_TYPE check_type		 	= 3;
	optional bytes msg				= 4;
	optional int32 param				= 5;
	optional bytes other_data			= 6;
	optional int32 result				= 7;
}
message ipt_summon_corps_member
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_SUMMON_CORPS_MEMBER ];
	optional int64 corps_id				= 2;	//社团ID
	optional int32 dir				= 3;	//召唤出的NPC的朝向
	optional a3d_pos pos              		= 4;	//NPC出生位置
	optional int32 inst_tid         		= 5;	//社团基地的副本tid
	optional int32 inst_id          		= 6;	//社团基地的副本ID
	optional int32 lineid           		= 7;	//社团基地所在的GS
	optional bytes script_path			= 8;	//加载脚本路径
	optional int64 member				= 9;	//DS所选中的玩家id
	optional bool is_rival				= 10;	//是否是对手玩家
	optional int32 title_id				= 11;	//设置称号
	optional bool master				= 12;	//是否召唤团长
	optional int32 player_npc_type		= 13;	//player_npc的类型，创建时给player_npc加掩码，通知客户端
}
message ipt_corps_base_data_change
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CORPS_BASE_DATA_CHANGE ];
	optional int64 corps_id				= 2;
	optional int32 inst_id				= 3;
	optional int32 support				= 4;
}

message soul_child_info
{
	optional int64 child_id  = 1;
	optional int32 progress = 2;
	repeated child_progress_history historys = 3;
	optional int32 health_check_tm = 4;
}

message ipt_intimate_sync
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_INTIMATE_SYNC ];
	repeated int64 partner_id		= 2;
	repeated int64 intimate_ids		= 3;
	repeated int64 soul_roleids		= 4;
	repeated soul_child_info childs		= 5;
}
message ipt_amity_grade_sync
{
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_AMITY_GRADE_SYNC ];
        message grade_info
        {
                optional int32 limit    = 1; //每档阈值
                optional int32 count    = 2; //好友数
        }
        repeated grade_info info        = 2;
}

message ipt_intimate_create_notify
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_INTIMATE_CREATE_NOTIFY ];
	optional int64 targetid                 = 2;
	optional a3d_pos pos			= 3;
	optional int32 intimate_type		= 4; //0 羁绊 1伴侣
}

enum SOUL_INTERACT_TYPE
{
	SIT_QUERY_STATUS = 1;
	SIT_QUERY_AND_TELEPORT = 2;
	SIT_QUERY_AND_TELEPORT_RE = 3;
	SIT_SEND_MISS = 4;
}

message ipt_intimate_operation
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_INTIMATE_OPERATION ];
	optional gp_intimate_op data            = 2;
	optional int32 txnid                    = 3;
	optional int32 retcode                  = 4;
	optional SOUL_INTERACT_TYPE interact_type = 5;
	message player_data
	{
		optional a3d_pos pos            = 1;
		optional int32 mirror_id        = 2;
		optional int32 scene_tag        = 3;
		optional int32 status           = 4;
		optional gp_object_buff buff	= 5;
	}
	optional player_data player		= 6;
}	

message ipt_server_get_brief
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_SERVER_GET_BRIEF ];
	optional int32 search_type			= 2;
	repeated name_ruid name_ruids		= 3;
}

message ipt_server_get_brief_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_SERVER_GET_BRIEF_RE ];
	optional int32 search_type			= 2;
	repeated role_brief role_briefs = 3;
}

message ipt_sync_team_repu
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_SYNC_TEAM_REPU ];
	optional int32 repu_id					= 2;
	optional int32 repu_value				= 3;
}

message ipt_center_battle_debug_set_max_player_num
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_DEBUG_SET_MAX_PLAYER_NUM ];
	optional int32 center_battle_type	= 2;
	optional int32 battle_type			= 3;
	optional int32 max_player_num		= 4;
}

message ipt_high_ranks_req {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_HIGH_RANKS_REQ ];
}

message ipt_high_ranks_rep {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_HIGH_RANKS_REP ];
	message top_info_t {
		optional int32 topid	= 1;
		optional int32 rank		= 2;
	}
	repeated top_info_t top_info			= 2;
}

message ipt_role_set_name_req {	
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ROLE_SET_NAME_REQ ];
	optional bytes name						= 2;
	optional bytes roledata					= 3;
}

message ipt_role_set_name_rep {	
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ROLE_SET_NAME_REP ];
	optional bytes name						= 2;
	optional int32 retcode					= 3;
}

message ipt_role_query_name	{	
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ROLE_QUERY_NAME ];
}

message ipt_roam_transfer_protocol
{
	optional INNER_PROTOCBUF_TYPE	type	= 1 [ default = IPT_ROAM_TRANSFER_PROTOCOL ];
	optional bytes data				= 2;
	optional int64 roleid			= 3;
	optional int32 to_client		= 4;
};

message ipt_roam_team_op
{
	optional INNER_PROTOCBUF_TYPE	type	= 1 [ default = IPT_ROAM_TEAM_OP ];
	enum ROAM_TEAM_OP {
		ROAM_TEAM_OP_APPLY		= 1;	//发起跨服组队
		ROAM_TEAM_OP_APPLY_RE	= 2;	//跨服组队申请的结果
		ROAM_TEAM_OP_INVITE		= 3;	//邀请组队
		ROAM_TEAM_OP_INVITE_ANSWER	=  4;	//邀请组队的答复
		ROAM_TEAM_OP_INVITE_RE	= 5;	//邀请组队的结果
		ROAM_TEAM_OP_LEAVE		= 6;	//退出队伍
		ROAM_TEAM_OP_ABDICATE 	= 7;	//换队长
		ROAM_TEAM_OP_REFRESH	= 8;	//通知本服刷新一次队伍
	}
	optional ROAM_TEAM_OP op		= 2;
	optional int64 applicant		= 3;
	optional int64 roleid			= 4;
	optional bytes name				= 5;
	optional int32 isinvite			= 6;
	optional int32 result			= 7;
	optional int32 assign_rule		= 8;
	optional int32 level			= 9;
	optional int32 profession		= 10;
	optional int32 lineid			= 11;
	optional int64 leader			= 12;
	optional int32 reason			= 13;
}

message ipt_roam_chat_public
{
	optional INNER_PROTOCBUF_TYPE	type	= 1 [ default = IPT_ROAM_CHAT_PUBLIC ];
	optional bytes			data	= 2;
	optional int32			normal	= 3;
}

message ipt_chatbox_select
{
	optional INNER_PROTOCBUF_TYPE	type	= 1 [ default = IPT_CHATBOX_SELECT ];
	optional int32 chatbox_type		= 2;
	optional int32 index			= 3;
}

message get_player_profile_struct {
	required int64 roleid           = 2;    // dst_roleid;
	enum GET_PROFILE_MASK {         //如果只是获取部分信息，使用type减少协议大小
		GET_ALL_PROFILE = 0x0000;   //所有
		GET_SNS     = 0x0001;   //sns
		GET_EQUIP   = 0x0002;   //equip 
		GET_PROPERTY    = 0x0004;   //protperty
	}    
	optional uint32 get_profile_mask    = 3 [ default = 0 ]; 
	optional bytes snsinfo          = 4;    // rpcalls.xml SnsInfo
	optional gs_role_mutable_data property = 5;    // 人物属性
	optional bytes equipments       = 6;    // 装备数据 rpcalls.xml GRoleInventoryVector
	optional other_player_profile others    = 7;    // 附加的其他玩家数据
}

message ipt_get_player_profile {
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_GET_PLAYER_PROFILE ];
	optional int64 roleid           = 2; 
	optional int64 target           = 3; 
	optional get_player_profile_struct data = 4; 
}

message ipt_get_roamer_info
{
	optional INNER_PROTOCBUF_TYPE   type    = 1 [ default = IPT_GET_ROAMER_INFO ];
	enum OP_TYPE
	{    
		OP_REQ  = 0; //请求
		OP_RES  = 1; //回应
	}    
	optional int64      roleid  = 2; //角色ID
	optional roamer_ds_info roamer  = 3; //角色信息
	optional OP_TYPE    op_type = 4 [ default = OP_REQ ]; //操作类型
	optional int32      retcode = 5; 
}

message ipt_player_change_scene {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_PLAYER_CHANGE_SCENE ];
	optional int64 roleid               = 2; 
	optional int32 world_tid            = 3; 
	optional int32 scene_tag            = 4; 
	optional int32 mid              = 5; 
	optional int32 lineid               = 6; 
	optional int32 instid               = 7; 
	optional bool logout                = 8; 
	optional float x					= 9;
	optional float y					= 10;
	optional float z					= 11;
}

message ipt_pubsub_operation {
	enum PUBSUB_OPER_TYPE
	{
		SUB =   1;  //订阅
		UNSUB   =   2;  //取消订阅
		MSG =   3;  //发送消息
		DEL =   4;  //删除主题
	}
	message TOPIC{
		required int64 id   = 1;
		required GPS_TYPE type  = 2;

	}
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_PUBSUB_OPERATION];
	required PUBSUB_OPER_TYPE operation_type= 2;
	required TOPIC topic            = 3;
	optional bytes msg          = 4;
};

message ipt_set_cooldown
{
	optional  INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SET_COOLDOWN ];
	optional  int32 id               = 2; //cooldown的标示ID
	optional  int32 last             = 3; //持续时间
}

message ipt_service_carrier {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SERVICE_CARRIER ];
	repeated int64 roleid               = 2; 
	optional int32 msg_type             = 3; 
	optional bytes msg              = 4; 
	required bytes service              = 5;    //服务id
	optional uint64 op              = 6;    //操作类型
	optional int64 param                = 7; 
}

message ipt_transfer_npt_msg {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_TRANSFER_NPT_MSG ];
	optional NET_PROTOCBUF_TYPE msg_type        = 2;
	optional bytes msg              = 3;
	repeated int64 roleid               = 4;
}

message ipt_transfer_gs_msg {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_TRANSFER_GS_MSG ];
	optional INNER_PROTOCBUF_TYPE msg_type      = 2; 
	optional bytes msg              = 3; 
	repeated int64 roleid               = 4; 
}

message ipt_notify_instance_info
{
	enum OP_TYPE{
		CREATE  = 1;
		DESTROY = 2;
	}
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_NOTIFY_INSTANCE_INFO ];
	optional int32 inst_tid         = 2;
	optional int32 inst_id          = 3;
	optional int32 lineid           = 4;
	optional int32 create_time      = 5;
	optional int32 category         = 6;
	optional bytes uuid         = 7;
	optional OP_TYPE oper           = 8;
	optional int32 name_seq         = 9;
	optional create_instance_transfer_info param = 10;
	repeated int32 params			= 11;
}

message ipt_dis_create_instance {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_DIS_CREATE_INSTANCE ];
	optional bytes uuid             = 2; 
	optional int32 world_tid            = 3; 
	optional int32 mode             = 4; 
	optional enter_instance_config config       = 5;    //服务id
	optional int32 res_lineid           = 6;    //操作类型
	optional int32 res_retcode          = 7; 
	optional int32 res_inst_id          = 8; 
	optional int32 name_seq             = 9; 
	required bytes service              = 10;
}

message ipt_pre_change_world {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_PRE_CHANGE_WORLD ];
	optional int64 roleid			= 2;
	optional int32 pjt              	= 3;
	optional int32 scene_tag           	= 4;
	optional int32 mid        	        = 5;
	optional a3d_pos pos              	= 6;
	optional bool team_jump             	= 7;
	optional uint32 timestamp           	= 8;
	optional int32 inst_tid             	= 9;
	optional int32 join_type            	= 10;
	optional int32 inst_mode            	= 11;
	optional int32 inst_id              	= 12;
	optional bytes inst_data            	= 13;
	repeated int64 members              	= 14;
	optional int32 pcw_type             	= 15;
	optional int32 retcode              	= 16;
	optional bool create_team           	= 17;
	optional int32 cur_inst_id          	= 18;   //校验当前的位置是否变化
	optional int32 cur_scene_tag            = 19;
	optional int32 cur_mirror_id            = 20;
	optional int32 dst_zoneid           	= 21;
	optional roamer_ds_info roamer          = 22;   //跨服用中转信息
	optional bool can_help			= 23;	//可以求助
	optional bool multi_jump		= 24 [ default = false ];	//是否多人绑定跳转
}

message ipt_pre_change_world_fail {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_PRE_CHANGE_WORLD_FAIL ];
	optional int32 retcode              = 2;
}

message ipt_remote_control {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_REMOTE_CONTROL ];
	optional int32 dst_zone             = 2; 
	optional int32 src_zone             = 3; 
	enum OPERATION {
		OT_GET      = 0;    //获取数据镜像
		OT_GET_LOCK = 1;    //获取数据控制权
		OT_WRITE    = 2;    //写回数据
		OT_SYNC     = 3;    //同步数据
		OT_RPC      = 4;    //远程调用函数
		OT_FORCE    = 5;    //强制远程更新slaver数据
		OT_FORCE_GET    = 6;    //强制从远程拉取数据
	};   
	optional uint64 version             = 4; 
	optional int32 send_timestamp           = 5; 
	//optional bytes service_name           = 6;
	optional GPS_TYPE msg_type          = 7; 
	optional bytes msg              = 8; 
	optional int32 retcode              = 9; 
	optional uint64 object_id           = 10;
	optional int32 lock_timestamp           = 11;
	optional OPERATION op               = 12;
	optional bytes func_name            = 13;
	optional int32 recieve_timetamp         = 14;
}

message service_data {
	required bytes service      = 1;
	optional int32 center_zone  = 2;
	optional uint32 mask        = 3;
	optional int32 timestamp    = 4;
}

message distribute_service_data {
	repeated service_data services          = 1;
}

message ipt_run_for_master_center {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_RUN_FOR_MASTER_CENTER ];
	optional bool support               = 2;
	optional int32 from_master          = 3;
	optional int32 run_timestamp            = 4;
}

message ipt_service_alloc_zone {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_SERVICE_ALLOC_ZONE ];
	repeated service_data info          = 2; 
	optional bool from_master           = 3; 
}

message ipt_remote_call
{
	optional INNER_PROTOCBUF_TYPE type	= 1     [ default = IPT_REMOTE_CALL ];
	enum OBJECT_TYPE
	{
		option allow_alias = true;

		OBJECT_TYPE_NULL = 0;

		//GS的IMP对象
		OBJECT_TYPE_GS_BEGIN = 1;
		OBJECT_TYPE_GS_GPLAYER_IMP = 1;
		OBJECT_TYPE_GS_GNPC_IMP = 2;
		OBJECT_TYPE_GS_GMATTER_IMP = 3;
		OBJECT_TYPE_GS_GSUBOBJECT_IMP = 4;
		OBJECT_TYPE_GS_GSCENE_IMP = 5;
		OBJECT_TYPE_GS_GWORLD_IMP = 6;
		
		OBJECT_TYPE_GS_GIMP = 100;
		//GS的非IMP对象
		OBJECT_TYPE_GS_END = 9999;

		//DS的对象
		OBJECT_TYPE_DS_BEGIN = 10000;
		OBJECT_TYPE_DS_ROLEINFO = 10000;
		OBJECT_TYPE_DS_SHM_TEST = 10001;
		OBJECT_TYPE_DS_END = 19999;

		//DL的对象
		OBJECT_TYPE_DL_BEGIN = 20000;
		OBJECT_TYPE_DL_SHM_TEST = 20000;
		OBJECT_TYPE_DL_END = 29999;

		//DB的对象
		OBJECT_TYPE_DB_BEGIN = 30000;
		OBJECT_TYPE_DB_END = 39999;
	}
	optional bytes func_name		= 2;
	optional bytes data				= 3;
	optional int32 id				= 4 [ default = 0 ];
	optional int64 self_id			= 5 [ default = 0 ];
	optional OBJECT_TYPE self_type	= 6 [ default = OBJECT_TYPE_NULL ];
	optional int64 object_id			= 7 [ default = 0 ];
	optional OBJECT_TYPE object_type	= 8 [ default = OBJECT_TYPE_NULL ];
	optional bool dealed			= 9 [ default = false ];
	optional int32 priority			= 10 [ default = 0 ];
	optional int32 result			= 11 [ default = 0 ];
}

message ipt_social_space_operation
{
	optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_SOCIAL_SPACE_OPERATION ];
	optional SOCIAL_SPACE_OP_TYPE op		= 2;
	optional social_space_content content 	= 3;
	optional int32 txnid					= 4 [ default = 0 ];
	optional plat_social_space_data data	= 5; //空间装扮数据
	optional int32 popularity				= 6; //增加的人气值
	optional bool step_award				= 7; //踩空间能否获得奖励
	message ss_retinue_new_status
	{
		optional int32 status_id = 1;
		optional int32 npc_id = 2;
		optional string ext1 = 3;
		optional string ext2 = 4;
		optional int64 moment_id = 5;
	}
	optional ss_retinue_new_status status_info = 8;//发送伙伴朋友圈的数据
	optional int32 speak_id					= 9; //送礼触发的喊话ID
	optional int32 speak_level				= 10;//送礼触发的喊话范围
}

message ipt_social_space_operation_re
{
	optional INNER_PROTOCBUF_TYPE type	= 1     [ default = IPT_SOCIAL_SPACE_OPERATION_RE ];
	optional SOCIAL_SPACE_OP_TYPE op	= 2;
	optional int32 retcode				= 3;
	optional int32 txnid				= 4;
	optional bool step_award			= 5;
	optional int32 popularity			= 6; //增加的人气值
	optional int32 gift_count			= 7; //礼物数
	optional int64 target				= 8;
	message retinue_status_re
	{
		optional int32 status_id = 1;
		optional int64 moment_id = 2;
	}
	optional retinue_status_re	status_info = 9; //发伙伴朋友圈返回的信息
}

message ipt_h5_game_get_reward
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_H5_GAME_GET_REWARD ];
	optional int64 roleid             	= 2;
	optional int32 reward_id		= 3;
	optional int32 repu_cost		= 4;
	optional int32 limit_id			= 5;
}
message ipt_player_slave_update
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PLAYER_SLAVE_UPDATE ];
	enum UPDATE_TYPE
	{
		UPDATE_TOTAL	= 1;
		UPDATE_FAVOR	= 2;
	}
	optional UPDATE_TYPE update		= 2;
	optional player_slave_data slaves	= 3;
	optional int64 roleid			= 4;
}

message ipt_player_constellation_reward
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_PLAYER_CONSTELLATION_REWARD ];
	optional int32 task_id                  = 2;
}

message ipt_corps_server_battle_award
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [default = IPT_CORPS_SERVER_BATTLE_AWARD ];
	optional int32 award_type		= 2;
}
message ipt_corps_server_battle_award_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1 [default = IPT_CORPS_SERVER_BATTLE_AWARD_RE ];
	enum CORPS_SERVER_BATTLE_AWARD_RE {
		CSBAR_OK   = 0;    //成功
		CSBAR_BATTLE_NOT_TIME       = 1;    //比赛未结束
		CSBAR_BATTLE_NOT_JOIN	    = 2;
	}
	optional int32 ret                      = 2;
	optional int32 award_type               = 3;
	optional int32 award_id			= 4;
	optional int32 city_index		= 5;
	optional int32 pos			= 6;
	optional int32 battle_result            = 7;
}

message ipt_child_divorce_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CHILD_DIVORCE_NOTIFY ];
	optional int64 roleid			= 2;
	optional int64 inlaws			= 3;
	optional uint64 guid			= 4;
	optional uint64 spouse_guid		= 5;
}

message ipt_invite_couple_tour
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_INVITE_COUPLE_TOUR ];
	optional int64 init			= 2;
	optional int64 resp			= 3;
	optional uint64 uid			= 4;
}

message ipt_reply_couple_tour
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_REPLY_COUPLE_TOUR ];
	optional int64 init			= 2;
	optional int64 resp			= 3;
	optional uint64 uid			= 4; //被邀请
	optional int32 result			= 5;
	optional int32 destination		= 6;
	optional uint64 timestamp		= 7;
	optional int32 use_time			= 8;
	optional child_show_info child_info	= 9;
}

message ipt_child_marriage_success
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CHILD_MARRIAGE_SUCCESS ];
	optional int64 initiator		= 2;
	optional int64 responder		= 3;
	optional uint64 init_guid		= 4;
	optional bytes init_name		= 5;
	optional uint64 resp_guid		= 6;
	optional bytes resp_name		= 7;
}
message ipt_child_marriage_check_amity
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CHILD_MARRIAGE_CHECK_AMITY ];
	optional int64 initiator		= 2;
	optional int64 responder		= 3;
	optional int32 amity_limit		= 4;
}
message ipt_child_marriage_check_amity_result
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CHILD_MARRIAGE_CHECK_AMITY_RESULT ];
	optional int32 result			= 2;
	optional int64 initiator		= 3;
	optional int64 responder		= 4;
}
message ipt_corps_server_battle_enter
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_SERVER_BATTLE_ENTER ];
	optional int32 city_index = 2;
}

message ipt_corps_server_battle_update
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_SERVER_BATTLE_UPDATE ];
	enum CORPS_SERVER_BATTLE_UPDATE_TYPE
	{
		CSBUT_MATCH_RESULT = 1;
		CSBUT_BATTLE_BEGIN = 2;
		CSBUT_ONE_BATTLE_RESULT = 3;
		CSBUT_BATTLE_RESULT = 4;
	};
	optional CORPS_SERVER_BATTLE_UPDATE_TYPE update_type = 2;
	optional int32 city_index	= 3;
	optional int32 battle_result = 4;
	optional int32 target_zoneid = 5;
	repeated int32 battle_time	= 6;
}

message ipt_oldplayer_back_notice_friend
{
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_OLDPLAYER_BACK_NOTICE_FRIEND ];
	optional int64 roleid       = 2;
}

message ipt_corps_center_battle_enter
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_CENTER_BATTLE_ENTER ];
	optional int64 corps_id = 2;
	optional int32 battle_type = 3;
}
message ipt_corps_center_battle_update
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_CENTER_BATTLE_UPDATE ];
	enum CORPS_CENTER_BATTLE_UPDATE_TYPE
	{
		CCBUT_MATCH_RESULT = 1;
		CCBUT_BATTLE_BEGIN = 2;
		CCBUT_BATTLE_RESULT = 3;
        CCBUT_SYNC_SCORE = 4;
        CCBUT_SYNC_REPU  = 5;
	};
	optional int32 update_type = 2;
	repeated corps_battle_order orders	= 3;
	optional int32 param			= 4;
	optional int32 param2			= 5;
	optional int32 zoneid			= 6;
	optional int32 battle_type		= 7;
    optional int64 roleid           = 8 [ default = 0 ];
    optional bytes name             = 9;
    optional int64 damage           = 10 [ default = 0 ];
    optional int64 corps_id         = 11 [ default = 0 ];
    optional int32 season_repu      = 12 [ default = 0 ];
}
message ipt_corps_center_battle_award 
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [default = IPT_CORPS_CENTER_BATTLE_AWARD ];
}
message ipt_corps_center_battle_award_re 
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [default = IPT_CORPS_CENTER_BATTLE_AWARD_RE ];
	enum CORPS_CENTER_BATTLE_AWARD_RE {
		AR_OK	= 0;	//成功
		AR_JOIN_TIME_LIMIT	= 1;	//学徒或者加入帮派不满
		AR_BATTLE_NOT_END	= 2;	//比赛未结束
		AR_BATTLE_NOT_APPLY	= 3;	//帮派未报名
		AR_BATTLE_NOT_MATCH	= 4;	//未开始匹配或未匹配上
	}
	optional int32 ret			= 2;
	optional int32 award			= 3;
	optional int32 brave_award		= 4;
}
message ipt_vows
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_VOWS ];
	optional int64 roleid			= 2;
	optional int64 spouse			= 3;
}
message ipt_corps_race_bet
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_RACE_BET];
	optional int64 roleid			= 2;
	optional int32 bet_choice		= 3;
	optional int32 bet_index		= 4;
	optional int32 bet_type			= 5;
	optional int32 bet_cost			= 6;
}
message ipt_corps_race_bet_result
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_RACE_BET_RESULT];
	optional int32 retcode			= 2;
	optional int64 roleid			= 3;
	optional int32 bet_choice 		= 4;
	optional int32 bet_index		= 5;
}

message ipt_corps_race_bet_award
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_CORPS_RACE_BET_AWARD];
	optional corps_race_bet_award award	= 2;
}
message ipt_arena_group_change
{
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_ARENA_GROUP_CHANGE];
	optional int64 arena_group_id	= 2;
	optional int32 battle_result = 3;
	optional int32 grade_change = 4;
}
message ipt_arena_group_info
{
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_ARENA_GROUP_INFO];
	optional int64 arena_group_id	= 2;
	optional int32 arena_group_grade = 3;
	optional int32 last_month_arena_group_grade = 4;
}
message ipt_eliminate_group_info
{
	optional INNER_PROTOCBUF_TYPE type  = 1 [ default = IPT_ELIMINATE_GROUP_INFO];
	optional int64 eliminate_group_id   = 2; 
	optional int32 eliminate_group_grade = 3; 
	optional int32 eliminate_group_type = 4;
	optional int64 eliminate_group_2_id   = 5; 
	optional int32 eliminate_group_2_grade = 6; 
	optional int64 eliminate_group_3_id   = 7; 
	optional int32 eliminate_group_3_grade = 8; 
}
message ipt_snatch_marriage_end
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_SNATCH_MARRIAGE_END];
	optional int64 roleid			= 2;
	optional int64 target			= 3;
	optional int64 target_spouse		= 4;
	optional int32 snatch_type		= 5;
	optional int32 result			= 6;
}
message ipt_snatch_marriage_begin
{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_SNATCH_MARRIAGE_BEGIN];
	optional int64 roleid			= 2;
	optional int64 target			= 3;
	optional int64 target_spouse		= 4;
	optional int32 snatch_type		= 5;
	optional float pos_x			= 6;
	optional float pos_y			= 7;
	optional float pos_z			= 8;
}
message ipt_flysword_surface_top_level
{
	optional INNER_PROTOCBUF_TYPE type 	= 1 [ default = IPT_FLYSWORD_SURFACE_TOP_LEVEL];
	optional int64 roleid			= 2;
	optional int32 surface_id		= 3;
}
message ipt_illegal_report {
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_ILLEGAL_REPORT ];
	optional int64 report_roleid		= 2;
	optional int64 target_roleid		= 3;
	optional int32 illegal_id			= 4; 
	optional int32 report_type			= 5; // 举报类型，1 菜单举报，2 朋友圈举报 ，99为其他
	optional int32 report_subtype		= 6;// 菜单举报子类型，1 言论不适，2 发布广告 ，3 无理由谩骂，4 使用外怪，5线下交易，99为其他
											// 朋友圈举报子类型，1 言论不适，2 发布广告 ，3 诈骗信息，99为其他
	optional string report_desc			= 7; // 举报说明
	optional string target_content		= 8; // 要举报的文本内容
	optional string target_piclist		= 9; // 要举报的图片列表,多张图片URL用逗号隔开
	optional int64 moment_id			= 10; // 举报的朋友圈id
	optional int32 moment_id_type		= 11; // 举报的朋友圈id类型 1 朋友圈状态ID， 2 朋友圈评论ID, 3 留言ID
	optional int64 title_id				= 12; // 举报的日记id
}

message ipt_couple_enter_wedding {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_COUPLE_ENTER_WEDDING];
	optional int64 applicant		= 2;
	optional int64 target			= 3;
	optional int32 scene_tag		= 4;
}
message ipt_parading_end_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PARADING_END_NOTIFY];
	optional int64 applicant 		= 2;
	optional int64 spouse			= 3;
	optional int32 parading_type		= 4;
}
message ipt_parading_notify
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PARADING_NOTIFY];
	optional int64 applicant		= 2;
	optional int64 spouse			= 3; 
	optional int32 parade_type		= 4;
	optional a3d_pos pos			= 5;
}
message ipt_player_apply_wedding {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PLAYER_APPLY_WEDDING];
	optional int64 applicant                = 2;
	optional int64 target                   = 3;
	optional int32 scene_tag                = 4;
	optional int32 op_type					= 5;
	optional int32 party_type				= 6;
}

message ipt_player_apply_wedding_result	{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PLAYER_APPLY_WEDDING_RESULT];
	optional int32 result               = 2; 
	optional int64 applicant            = 3;
	optional int64 target	         	= 4;
	optional int32 party_type	        = 5;
	optional bytes leader_vote_content  = 6;
	optional bytes target_vote_content  = 7;
}

message ipt_change_id_photo {
		optional INNER_PROTOCBUF_TYPE type		= 1		[ default = IPT_CHANGE_ID_PHOTO];
		required int32  photoid             	= 2;
}

message ipt_sect_title_update {
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_SECT_TITLE_UPDATE ];
        optional int64 cur_disciple             = 2;
}

message ipt_inc_vip_exp {
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_INC_VIP_EXP ];
        optional uint32 vip_exp                 = 2;
}

message ipt_sect_graduate {
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_SECT_GRADUATE ];
        optional int64 roleid	                = 2;
	optional int64 master			= 3;
}
message ipt_fake_auction_open {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_FAKE_AUCTION_OPEN ];
	optional uint32 itemid                  = 2;
}

message ipt_fake_auction_buy {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_FAKE_AUCTION_BUY ];
	required uint32 auctionid               = 2;
	required uint32 item_id                 = 3;
	optional uint32 count                   = 4;
	required uint32 price                   = 5;
	required int32 txnid                   = 6;
}

message ipt_fake_auction_buy_result {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_FAKE_AUCTION_BUY_RESULT ];
	required int32 retcode                 = 2;
	optional int32 txnid                   = 3;
	required uint32 auctionid               = 4;
	required uint32 itemid                  = 5;
	required uint32 price                   = 6;
	required uint32 count                   = 7;
}

message ipt_fake_auction_buy_ack {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_FAKE_AUCTION_BUY_ACK ];
	required uint32 retcode                 = 2;
	required uint32 auctionid               = 3;
	required uint32 itemid                  = 4;
	required uint32 count                   = 5;
}

message ipt_create_corps {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CREATE_CORPS ];
	required int32 txnid			= 2;
	required bytes name			= 3;
	optional bytes recruitment_manfest	= 4;	//社团招聘宣言
	optional bytes recruitment_url          = 5;    //社团招聘图片
	optional int32 badge			= 6;	//社团徽章
	optional int32 support_side		= 7;	//社团拥护组织
	optional int32 retcode			= 8;	//ds -> gs发送的时候通知错误信息
}

message ipt_corps_facebook {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CORPS_FACEBOOK ];
	optional facebook_str corps		= 2;
	optional bool init			= 3;
}

message ipt_scene_info {
	required uint32 scene_tag		= 1;
	repeated ipt_mirror_info mirrors	= 2;
	optional uint32 capacity		= 3;
	optional bool is_nautral		= 4;	//是否是中立区
	optional uint32 default_size		= 5 [default = 0];	//默认最小大小,如果当前场景人数小于这个数，将使用这个数字判断镜像数量
	optional bool each_gs			= 6 [default = false];	//true表示ds不处理此场景的开闭，完全由gs开启1个镜像
	optional bool is_novice			= 7;	//是否是新手场景
	optional bool is_not_allow_mirror = 8 [default = false];	//true 禁止开启镜像
}

message ipt_line_register_info {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_LINE_REGISTER_INFO ];
	required uint32 lineid			= 2;
	repeated ipt_scene_info scenes		= 3;
	optional bytes ip           = 4; 
	repeated int32 provder_ports        = 5; 
	optional int32 gamedb_ports     = 6; 
}

//镜像管理与操作
message ipt_mirror_op {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_MIRROR_OP ];
	required uint32 scene_tag		= 2;
	required ipt_mirror_info mirror		= 3;
	optional uint32 lineid			= 4;
}

message ipt_zhaojiling {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ZHAOJILING ];
	optional zhaojiling_info baseinfo	= 2;
}

message ipt_sync_nation_info {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SYNC_NATION_INFO ];
	required sint32 nation_officer		= 2;	//国家职务
}
message ipt_nationwar_field_msg {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_NATIONWAR_FIELD_MSG ];
	required sint32 war_id			= 2;	//国战id
	required sint32 msg_type		= 3;	//消息类型
	required int64 param1			= 4;	//消息参数1
	required int64 param2			= 5;	//消息参数2
	required int64 param3			= 6;	//消息参数3
}

	//mode 定义
	//FCDT_MONEY              = 0,    //帮派资金
	//FCDT_AUCTIONPOINT       = 1,    //竞标点 暂时废弃
	//FCDT_CONSTRUCTION       = 2,    //帮派建设度
	//FCDT_WELFARE_EXP        = 3,    //当日福利经验累计 暂时废弃
	//FCDT_REPU_MODIFY	  = 4,	  // repu
message ipt_modify_corps_data {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_MODIFY_CORPS_DATA ];
	required modify_corps_data modify_data	= 2;
}

message ipt_level_control_msg {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_LEVEL_CONTROL_MSG ];
	required int32 msg_type			= 2;
	optional int32 src_nation		= 3;
	optional int32 src_scene_tag 		= 5;
	optional int32 src_mirrorid		= 11 [ default = 255 ];
	optional int32 dst_nation		= 6;
	optional int32 dst_scene_tag		= 7;
	repeated int32 params			= 8;
	optional int32 world_id			= 9;	//副本ID
	optional int32 world_tid        = 10;   //副本TID
}
message ipt_deliver_task
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_DELIVER_TASK ];
	required int32 task_id			= 2;	//任务ID
}
message ipt_deliver_reward {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_DELIVER_REWARD ];
	required int32 id			= 2;	//奖励id
	required int32 rtype			= 3;	//类型
	repeated float ratios			= 4;	//倍率
	optional int32 limit_tid		= 5;	//奖励的通用限制模板
	optional func_info_t fi		= 6; // 奖励模板发放原因 
}

message ipt_sync_officer_info {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SYNC_OFFICER_INFO ];
	required int32 scene_tag		= 2;	//场景id
	required float pos_x			= 3;	//场景位置
	required float pos_y			= 4;	//场景位置
	required float pos_z			= 5;	//场景位置
}

message ipt_longjump_player {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_LONGJUMP_PLAYER ];
	required int32 dst_nation		= 2;	//目标国家
	required int32 dst_scene_tag		= 3;	//目标场景
	required float pos_x			= 4;	//场景位置
	required float pos_y			= 5;	//场景位置
	required float pos_z			= 6;	//场景位置
	required int32 jump_fee			= 7;	//传送费用
	required int32 jump_money_type		= 8;	//费用货币类型
	optional int32 jump_type		= 9	[ default = 0 ];	//传送类型
}

message ipt_vip_info {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_VIP_INFO ];
	optional uint32 vip_level		= 2;	//vip level
	optional bool hide			= 3;
	optional bool hide_name                 = 4;
}

message ipt_add_friends {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ADD_FRIENDS ];
	required int64 friend_id		= 2;	//好友id
	required int32 friend_count		= 3;	//还有数量
}

message ipt_contri_corps_money {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CONTRI_CORPS_MONEY ];
	required int64 money			= 2;	//捐钱
	required uint64 corps_id		= 3;
	optional int32 retcode			= 4;
	optional int32 ctype			= 5;
	required int32 txnid			= 6;	//捐钱
}

message ipt_sync_player_info {
	optional INNER_PROTOCBUF_TYPE type		= 1	[ default = IPT_SYNC_PLAYER_INFO ];
	//optional int32 family_id			= 2;
	optional uint64 corps_id			= 3;
	optional int32 level				= 4;
	optional int32 prof				= 5;
	optional int64 corps_contri			= 6;
	optional int64 corps_contri_total		= 7;
	optional int64 corps_contri_cur_week		= 8;
	optional uint32 fightcapacity			= 9;
	optional other_player_profile_from_gs pro	= 10;
	optional uint32 duke_level			= 11;
	optional bool fashion				= 12;		//是否显示时装
	optional int32 body_size			= 13;
	optional int32 faceid				= 14;
	optional int32 star_gfx_suit_star		= 15;
	optional int64 idphoto				= 16;
	optional int32 gender				= 17;
	optional int32 race				= 18;
	optional int32 enhance_gfx_suit_level		= 19;
	optional int32 gem_gfx_suit_level   		= 20;
	optional int32 personal_card		= 21;
}

message ipt_role_unsave_data {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ROLE_UNSAVE_DATA ];
	message hero_info {
		repeated bool subscribe	= 1;
	};
	optional hero_info heros		= 2;
	message bot_info {
		optional bool bot_acitve	= 1;
		optional int64 bot_timestamp	= 2;
		optional bool question		= 3;
		optional int32 quiet	= 4;	//是否进入静默模式
		optional int32 half_quit    = 5;    //半退出模式
	}
	optional bot_info bot			= 3;
	message online_addons {
		repeated bytes addons	= 1;
	}
	message corps_skill_addons {
		repeated online_addons level_addons	= 1;	//等级 0 - 4
		optional bytes other_data		= 2;
		optional int32 jointime			= 3;	//加入帮派时间 
		optional int32 lastweek_pos		= 4;	//上周帮派职务 
		optional int32 fighter_pos		= 5;	//勇士编号
	}
	optional corps_skill_addons corp_addon	= 4;
	optional db_account_ds_data account_ds_property=5; 
	optional uint32 last_scene_tag		= 6;	// 这里不用了，放到了db_player_misc.last_scen_tag 
	optional db_account_db_data account_db_property=7; 
	optional gp_small_bag_download_resource small_bag = 8;
	message playback_info {
		optional int64 begin_tm = 1;
		optional int32 node_idx = 2;
		optional int32 task_idx = 3;
		optional int32 scene_id = 4;
		optional float pos_x = 5;
		optional float pos_y = 6;
		optional float pos_z = 7;
		optional bool send_begin = 8;
	}
	optional playback_info playback = 9;
	optional ipt_proxy_draw_notify_to_gs proxy_draw_info = 10;
}

message ipt_nation_war_achievement {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_NATION_WAR_ACHIEVEMENT ];
	required int64 event			= 2;
	required int64 param1			= 3;
	required int64 param2			= 4;
}

message ipt_corp_attribute {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CORP_ATTRIBUTE ];
	optional corp_attribute	attr		= 2;
}

message chess_robot_t {
	optional int64 chess_robot_roleid  = 1;
	optional int64 chess_robot_idphoto = 2;
}

message ipt_create_instance {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CREATE_INSTNACE ];
	optional uint64 roleid			= 2;
	optional uint32 creator_prof		= 3;
	optional uint32 creator_level		= 4;
	optional uint32 creators_count		= 5;
	optional uint32 creator_nation		= 6;
	optional uint32 teamid			= 7;
	optional uint64 corps_id		= 8;
	optional uint32 native_zoneid		= 9;
	optional a3d_pos pos			= 10;
	optional uint32 inst_tid		= 11;
	optional int32 inst_id			= 12;
	optional uint32 instance_mode		= 13;
	optional uint64 player_npc_id		= 14;
	repeated int32 params			= 15;
	optional uint32 scene_tag		= 16;
	repeated uint64 creators		= 17;
	repeated name_roleid_pair members	= 18;
	optional bytes create_data		= 19;
	optional enter_instance_config config   = 20;   //创建的信息
	optional int32 corps_support		= 21;	//社团拥护组织
	optional bytes player_npc_name		= 22;
	optional int64 player_npc_idphoto	= 23;
	optional int32 player_npc_level		= 24 [default = 0];
	optional int64 fightcapacity		= 25;
	optional int64 sys_roleid		= 26;
	repeated chess_robot_t chess_robot_info = 27;
    repeated int64 param64s         = 28;
	repeated bdsg_team_info bti			= 29;
	optional int32 has_ai_players	= 30; // 彩虹航线存在机器人玩家
}

message ipt_blessing_info {
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_BLESSING_INFO ];
		optional blessing_info info             = 2;
		optional int32 txnid                  	= 3;         
		optional int32 retcode                  = 4;        
		optional int32 is_idip_modify			= 5;
}

message ipt_blessing_notify {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_BLESSING_NOTIFY ];
	required uint64 src_role		= 2;
	required uint64 dst_role		= 3;
	required uint32 item_count		= 4;
}

message ipt_team_task_event {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_TEAM_TASK_EVENT];
	optional int64 teamid			= 2;
	optional bytes event_data		= 3;
}

message ipt_scene_limit_platform {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SCENE_LIMIT_PLATFORM];
	required uint64 roleid			= 2;
}

message ipt_set_corps_manifesto{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SET_CORPS_MANIFESTO];
	optional uint64 corps_id 		= 2;
	optional int32  txnid			= 3;
	optional bytes  content             	= 4;	//content
	optional int32  retcode             	= 5;	//retcode
}

message ipt_revenge_battle_apply{
	optional INNER_PROTOCBUF_TYPE type		= 1	[ default = IPT_REVENGE_BATTLE_APPLY];
	required int64	inviter_id			= 2;
	required int64 invitee_id			= 3;
	required int32	required_level			= 4;
	required battle_player_info inviter_info	= 5;
	required int32	timeout				= 6;
	required int32	txnid				= 7;
}

message ipt_revenge_battle_apply_result{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_REVENGE_BATTLE_APPLY_RESULT];
	required int32 retcode			= 2;
	required int64 inviter_id		= 3;
	optional bytes	inviter_name		= 4;
	optional int64 invitee_id		= 5;
	optional bytes	invitee_name		= 6;
	optional int32	txnid			= 7;
	optional battle_player_info invitee_info= 8;
}

message ipt_revenge_battle_response{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_REVENGE_BATTLE_RESPONSE];
	required int32 result			= 2;
	required int64 inviter_id		= 3;
	required int64 invitee_id		= 4;
}

message ipt_try_enter_revenge_battle{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_TRY_ENTER_REVENGE_BATTLE];
	required int64 inviter_id		= 2;
	optional int64 invitee_id		= 3;
}

message ipt_revenge_battle_result{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_REVENGE_BATTLE_RESULT];
	enum battle_result{
		BR_NORMAL	= 0;	//正常有结果的仇杀约战
		BR_DRAW		= 1;	//平局结果
	}
	required battle_result	result		= 2;
	required int64 		inviter_id	= 3;
	required int64 		invitee_id	= 4;
	optional int64 		loser_id	= 5;
}

message ipt_revenge_battle_end{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_REVENGE_BATTLE_END];
	required int64 		inviter_id	= 2;
	required int64 		invitee_id	= 3;
}

message ipt_sect_apply{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SECT_APPLY];
	required int64 master			= 2;
	optional int32 master_title		= 3;
	required int64 disciple			= 4;
	optional int32 disciple_title		= 5;
	required int64 roleid			= 6;
	optional int32 amity_value		= 7;
}

message ipt_on_enter_revenge_battle{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ON_ENTER_REVENGE_BATTLE];
	required bool is_inviter		= 2;
	required int64 inviter_id 		= 3;
	required int64 invitee_id		= 4;
}

message ipt_player_enter_corps_battle {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PLAYER_ENTER_CORPS_BATTLE];
	required int32 battle_type	= 2;	//战场类型
	required int32 battle_index	= 3;	//战场索引
	required int32 battle_tid	= 4;	//战场模板
	required int32 battle_id	= 5;	//战场id
}

message ipt_player_enter_corps_battle_result {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PLAYER_ENTER_CORPS_BATTLE_RESULT];
	required int32 battle_type	= 2;	//战场类型
	required int32 battle_index = 3;	//战场索引
	required int32 battle_tid	= 4;	//战场模板
	required int32 battle_id	= 5;	//战场id
	required int32 result	= 6;	//结果
}

message ipt_send_corps_battle_result {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_SEND_CORPS_BATTLE_RESULT ];
	required int32 battle_id        = 2;
	required int32 creator_win      = 3;            //创建者是否胜利(0 失败 1胜利 2平局)
	required int64 param            = 4;            //参数
	optional int64 param2           = 5 [default = 0];              //参数2
	optional int64 target_id        = 6 [default = 0];              //可能是社团id，可能是玩家的roleid
	repeated corps_battle3_result_info corps_battle3_result = 7;    //社团竞赛3结果
}

message ipt_longjump_succeed {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_LONGJUMP_SUCCEED ];
	optional uint64 roleid			= 2;
	optional int32 jump_type		= 3;
}

message ipt_greeting_event {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_GREETING_EVENT ];
	optional uint32 event_type		= 2;
	optional uint32 event_param1		= 3;
	optional uint32 event_param2		= 4;
}

message ipt_greeting_award {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_GREETING_AWARD ];
	optional uint32 greeting_seq		= 2;
	optional uint32 award_type		= 3;
	optional uint32 award_tid		= 4;
}

message ipt_greeting_award_result {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_GREETING_AWARD_RESULT ];
	optional uint32 greeting_seq		= 2;
	optional uint32 award_type		= 3;
	optional uint32 award_tid		= 4;
	optional uint32 award_result		= 5;
	optional uint32 use_diamond		= 6;
}

message ipt_deliver_common_reward {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_DELIVER_COMMON_REWARD ];
	optional uint32 reward_tid		= 2;
	optional uint32 reward_count		= 3	[ default = 1 ];
	optional func_info_t fi		= 4; // 奖励模板发放原因 
}

message ipt_corp_camp_fire {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CORP_CAMP_FIRE ];
	optional int32 fire_type		= 2;	//1-3
	optional int32 txnid			= 3;
	optional int32 retcode			= 4;
}

message ipt_ask_help {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ASK_HELP ];
	optional ask_help_info info		= 2;
};

message ipt_account_property {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ACCOUNT_PROPERTY ];
	optional db_account_ds_data ds_property	= 2;
	optional db_account_db_data db_property	= 3;
};

message ipt_grc_rcv_gift{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_GRC_RCV_GIFT ];
	required uint64 role_id			= 2;
	required int32 	gift_kind		= 3;
	required int32	gift_count		= 4;
};

message ipt_grc_get_rcv_gift_limit{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_GRC_GET_RCV_GIFT_LIMIT];
	optional int32 gift_type                        = 2;                    // 接收礼物类型
	optional int64 serialid                         = 3;                    // 编号
	optional int32 max_receive_times_everyday       = 4;    // 该类礼物每天最多可收的次数
	optional int32 remain_times                     = 5;                    // 剩余次数
	required bytes from								= 6;
}

message ipt_grc_get_rcv_gift_limit_re{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_GRC_GET_RCV_GIFT_LIMIT_RE];
	required int32	giftType		= 2;
	required int64	giftCount		= 3;
	required int32	limit			= 4;
	required int64	timestamp               = 5;
	required bytes	from			= 6;
	optional int64	serialid        = 7;                    // 编号
}

message ipt_grc_get_send_gift_limit{            //收到客户端的grc礼物请求，gs填入次数返回给ds
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_GRC_GET_SEND_GIFT_LIMIT];
	optional string to_openid                       = 2;            // 接收人openid
	optional int32  gift_type                       = 3;            // 礼物类型
	optional int32  gift_count                      = 4;            // 礼物数量
	optional int32  max_send_times_everyday = 5;    // 该类礼物每天最多可赠送的次数
	optional int32  remain_times                    = 6;    // 剩余次数
};

message ipt_grc_get_send_gift_limit_re{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_GRC_GET_SEND_GIFT_LIMIT_RE];
	required int32	giftType		= 2;
	required int64	giftCount		= 3;
	required int32 limit			= 4;
	required bytes	to			= 5;
};

message ipt_grc_update_friend_count{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_GRC_UPDATE_FRIEND_COUNT];
	required int32	friend_count		= 2;
}

//TODO
message ipt_grc_get_all_rcv_gift_limit
{
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_GRC_GET_ALL_RCV_GIFT_LIMIT];
        optional int32 gift_type                        = 2;                    // ds要获取的礼物类型限制, 不填则获取全部
        repeated ipt_grc_get_rcv_gift_limit limits      = 3;    // 各类礼物每天最多可接收的次数
}

message ipt_grc_get_all_send_gift_limit
{
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_GRC_GET_ALL_SEND_GIFT_LIMIT];
        optional int32 gift_type                        = 2;                    // ds要获取的礼物类型限制，不填则获取全部
        repeated ipt_grc_get_send_gift_limit limits     = 3;    // 各类礼物每天最多可赠送的次数
}

message ipt_join_nation_war{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_JOIN_NATION_WAR ];
	optional uint64 role_id			= 2;
	optional uint32 nation_war_mode		= 3;	
	optional uint32 nation_war_id		= 4;	
};

message ipt_sync_nation_war_type{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SYNC_NATION_WAR_TYPE ];
	optional uint32 nation_war_mode		= 2;	
};

message ipt_server_info {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SERVER_INFO ];
	optional uint32 zoneid          = 2;
	optional uint32 district_id     = 3;
	optional uint32 areaid          = 4;
	repeated uint32 zoneids         = 5;
	optional uint32 server_level_opentime   = 6;    //计算服务器卡级等级的开服时间（受IDIP影响，可能与server_open_time不同）
	optional uint32 server_level_exp_factor	= 7;    //服务器等级经验加成系数
	optional uint32 server_open_time		= 8;	//服务器开服时间
	optional uint32 server_exp_level		= 9;	//服务器经验等级
};

message ipt_register_client {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_REGISTER_CLIENT ];
	optional int32 zoneid               = 2;
	optional bytes edition              = 3;
	optional int32 areaid               = 4;
}

message ipt_register_client_response {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_REGISTER_CLIENT_RESPONSE ];
	optional int32 hudid                = 2;
}

message hub_broadcast_str {
	optional int32 zoneid               = 1;
	optional int32 last_ping_ms         = 2;
	optional int32 areaid               = 3;
	repeated int32 merged_zone          = 4;
	optional int32 send_time			= 5;
	repeated int32 provder_ports            = 6;
	optional int32 gamedb_ports         = 7;
	optional bytes ip               = 8;
	optional int32 center_id            = 9;
	optional bool center_master         = 10;
	optional distribute_service_data dsd        = 11;
	optional float cpu_load             = 12;
	optional bool random_service		= 13;
}

message ipt_hub_ping {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_HUB_PING ];
	optional int32 hub_id               = 2;
	optional int32 zoneid               = 3;
	optional int32 send_time            = 4;
	optional int32 hub_recive_time          = 5;
	repeated bytes broadcast_info           = 6;
	optional bytes selfip               = 7;
	optional int32 area_id              = 8;
}

message ipt_farm_update	{
	optional INNER_PROTOCBUF_TYPE type	= 1 [ default = IPT_FARM_UPDATE ] ;
	optional int64 id			= 2;
	optional farm_object farm		= 3;
	optional int32 level_up			= 4;	//农场作物是否升级了，如果是，则需要通知农场主人
}

message db_roam_friend {
	required int64 roleid       = 1;
	optional int32 amity        = 2;
	optional int32 fighting_capacity        = 3;
	optional int32 status   = 4;
}

message db_roam_friend_state {
	required int64 roleid       = 1;
	optional uint32 cur_zoneid      = 2;
	optional uint32 level       = 3;
	optional uint32 profession      = 4;
	optional bytes signature        = 5;
	optional uint32 mask    = 6;
	optional uint32 world_tid   = 7;
	optional uint32 scene_tag   = 8;
	optional uint32 mirror_id   = 9;
	optional uint64 idphoto		= 10;
}

message db_roam_friend_group
{
	optional uint32 id			= 1;
	optional bytes name			= 2;
}

enum DB_ROAM_FRIENDS_OP
{
	ROAM_FRIENDS_INFORM_ONLINE      = 1;
	ROAM_FRIENDS_INFORM_STATUS      = 2;
	ROAM_FRIENDS_INFORM_OFFLINE     = 3;
};

message db_roam_friends {
	required DB_ROAM_FRIENDS_OP op_type             = 1;
	repeated db_roam_friend roam_friend         = 2;
	repeated db_roam_friend_state roam_friend_state = 3;
	repeated db_roam_friend_group roam_friend_group = 4;
}

enum DB_ROAM_CORP_OP
{
	ROAM_CORP_GET_CORP_ID       	= 1;
	ROAM_CORP_GET_CORP_DATA     	= 2;
	ROAM_CORP_SENT_MESSAGE		= 3;
	ROAM_CORP_CHAT_ROOM_OPEN	= 4;
	ROAM_CORP_CHAT_ROOM_JOIN	= 5;
	ROAM_CORP_CHAT_ROOM_NOTIFY	= 6;
	ROAM_CORP_CHAT_ROOM_LEAVE	= 7;
	ROAM_CORP_CHAT_ROOM_QUIT	= 8;
	ROAM_CORP_CHAT_ROOM_KICKOUT	= 9;
	ROAM_CORP_CHAT_ROOM_INVITE	= 10;
	ROAM_CORP_CHAT_ROOM_INVITED	= 11;
	ROAM_CORP_CHAT_ROOM_AGREE	= 12;
	ROAM_CORP_CONTRI_CHANGE		= 13;
	ROAM_CORP_CONTRI_MANAGER_BONUS_CHANGE		= 14;
};

message db_roam_corp {
	required DB_ROAM_CORP_OP op_type                = 1;
	optional int64 corp_id              		= 2;
	optional corps_struct corp_data     		= 3;
	repeated corps_member corp_members      	= 4;
	repeated corps_chat_room_member chat_room	= 5;	//聊天室数据
	repeated int64 microphone_list			= 6;	//开麦列表
	optional int32 op_result			= 7;	
	optional npt_corps_appoint	msg		= 8;	//
	optional int64 param				= 9;
}

enum DB_ROAM_CHAT_TYPE
{
	ROAM_CHAT_FRIEND        = 1;
	ROAM_CHAT_CORP      = 2;
	ROAM_CHAT_GROUP		= 3;
}

message db_roam_chat {
	required DB_ROAM_CHAT_TYPE type     = 1;
	optional bytes chat_msg             = 2;
}

message db_roam_intimate {
	optional int32 type				= 1;
	optional role_complicated_data_intimate data 	= 2;
	optional role_complicated_data_golden_intimate golden_data 	= 3;
}

message ipt_general_protoc {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_GENERAL_PROTOC ];
	optional repu_sync_str repu_str         = 2;    //同步声望用
	optional bool kick_out              = 3;    //标记跨服踢人用
	optional int32 player_roam_login        = 4;    //校验玩家跨服登陆的状态
	optional db_roam_friends roam_friends   = 5;    //同步跨服好友信息
	optional db_roam_corp roam_corp = 6;    //同步跨服帮派信息
	optional db_roam_chat roam_chat = 7;    //跨服聊天转发
	optional new_repu_sync_str new_repu_str		= 8;	//通知声望变化
	optional db_roam_intimate roam_intimate = 9;	//同步羁绊数据
	optional npt_group_notify roam_group = 10; //同步聊天群组
}

message ds_roleinfo
{
	message Equipment
	{    
		optional int32 id           = 1; 
		optional int32 pos          = 2; 
		optional int32 count		= 3; 
		optional bytes data         = 4; 
		optional int32 state        = 5; 
		optional int32 expire_date  = 6; 
	}    
	optional int64 roleid           = 1; //角色ID
	optional int32 gender           = 2; //性别
	optional int32 profession       = 3; //职业
	optional bytes name             = 4; //名字
	optional bytes alias            = 5; //别名
	optional bytes appearance       = 6; //自定义外观
	optional int32 level            = 7; //等级
	optional int32 create_time      = 8; //创建时间 
	optional int32 logout_time      = 9; //登出时间
	optional int32 delete_time      = 10; //删除时间
	repeated Equipment equipment    = 11; //装备数据
	optional gs_role_mutable_data role_mutable_data = 12; //gs易变数据
}

message ipt_change_zone {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CHANGE_ZONE ];
	optional int64 roleid               = 2;
	optional bytes roledata             = 3;
	optional int32 zoneid               = 4;
	optional bytes ds_roledata          = 5;
	optional db_ds_save_player_data ds_roledata2    = 6;
	optional bytes account              = 7;
	optional int32 world_tid			= 8;
	optional int32 scene				= 9;
	optional db_roam_token token		= 10;
	optional int32 roam_global_world	= 11; //是否进入跨服大地图
	optional int32 db_timestamp			= 12;
	optional center_battle_roam_teleport_player_param_t param = 13;
}

message ipt_change_zone_response {
	optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_CHANGE_ZONE_RESPONSE ];
	optional int32 retcode              = 2;
	optional int32 dst_zone             = 3;
	optional int64 roleid               = 4;
}

message ipt_clear_card {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CLEAR_CARD ];
	optional uint64 role_id                 = 2;
};

message ipt_intimate_friend_title_update {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_INTIMATE_FRIEND_TITLE_UPDATE];
	optional int64 friendid			= 2;	
	optional int32 op			= 3;	
};

message ipt_intimate_friend_title_select {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_INTIMATE_FRIEND_TITLE_SELECT];
	optional name_roleid_pair friend	= 2;	//好友名
	optional int32 param			= 3;	
};

message ipt_player_combat_state {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PLAYER_COMBAT_STATE ];
	optional int32 combat			= 2;
};

message ipt_corps_level {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_CORPS_LEVEL ];
	optional int32 value			= 2;	
};

message ipt_update_grc_level_progress {
	optional INNER_PROTOCBUF_TYPE type 	= 1	[ default = IPT_UPDATE_GRC_LEVEL_PROGRESS];
	optional int64 roleid			= 2;
	optional int32 instance			= 3;
	optional int32 progress			= 4;
};

message ipt_update_secure_idip {
	optional INNER_PROTOCBUF_TYPE type 	= 1	[ default = IPT_UPDATE_SECURE_IDIP];
	optional int64 roleid			= 2;
	required bytes new_secure_idip		= 3;	
}

message ipt_upload_qq_info {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_UPLOAD_QQ_INFO ];
	repeated int32 itype			= 2;
	repeated int32 bcover			= 3;
	repeated bytes data			= 4;
	repeated int32 expire			= 5;	
};

message ipt_recharge_plat_vip {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_RECHARGE_PLAT_VIP];
	required int32 viptypeMsk		= 2;
}

message ipt_chariot_upgrade {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CHARIOT_UPGRADE];
	required int32 chariottid		= 2;
	required int64 index			= 3;
	required int32 up_type			= 4;
}

message ipt_chariot_upgrade_re {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CHARIOT_UPGRADE_RE];
	required int32 up_type			= 2;	
}

message ipt_player_corps_info {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_PLAYER_CORPS_INFO];
	required int32 join_time                = 2;
	optional int32 lastweek_pos             = 3;
	optional int32 fighter_pos             	= 4;
}

message ipt_rent_chariot {
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CHARIOT_RENT];
        required int32 tid	                = 2;
	required int32 index			= 3;
}

message ipt_rent_chariot_result {
        optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CHARIOT_RENT_RESULT];
	required int32 result			= 2;
	required int32 tid                   	= 3;
	required int32 level                  	= 4;
}

message ipt_arena_apply_check {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ARENA_APPLY_CHECK];
	required int32 battle_type   		= 2;
	required int32 battle_stub_type   	= 3;
	required int32 battle_tid   		= 4;
	required int32 level_min   			= 5;
	required int32 level_max   			= 6;
	required int32 player_count 		= 7;
}

message ipt_arena_apply_check_result {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ARENA_APPLY_CHECK_RESULT];
	required int32 result			= 2;
	required uint64 roleid			= 3;
	required int32 battle_type   		= 4;
}

message ipt_arena_send_result {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ARENA_SEND_RESULT];
	required int32 battle_id		= 2;
	required int32 creator_win		= 3;
}

message ipt_player_fashion_change {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_PLAYER_FASHION_CHANGE];
	message change_info
	{
		optional int32 fashion_type = 1;
		optional int64 data = 2;;
	}
	repeated change_info fashion		= 2;
	repeated change_info fashion_ext    = 3;
	repeated change_info fashion_offset = 4;
}

message ipt_modify_gs_repu {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_MODIFY_GS_REPU];
	optional int32 index		= 2		[ default = 0];
	optional int32 value		= 3		[ default = 0];
	optional int32 code			= 4		[ default = 0];
	optional int64 code_arg		= 5		[ default = 0];
}
message webshop_goods_info {
	required bytes s_store_id			= 1;	
	required bytes s_goods_id			= 2;	
	required int32 price				= 3;	
	required int32 gain_diamond			= 4;	
	required int32 limit_activity_id		= 5;	
	required int32 is_month_card			= 6;	
	required int32 month_card_expiration_days	= 7;	
	required bytes month_card_id			= 8;	
	required int32 config_tid			= 9;
	required int32 fund_id				= 10;
	required int32 time_limit_type		= 11;	//限时类型(1每日 2每周)
	//required int32 time_limit_award_id	= 12;	//限时奖励模板ID
	required int32 first_recharge_award	= 13;	//是否可以领取首冲奖励
	required int32 first_recharge_double = 14;	//首冲双倍
	required int32 vip_exp = 15;
	required int32 second_recharge_award = 16;	//是否可以领取次充奖励
	required int32 time_limit_activity_id = 17;	//限时活动ID
	required int32 extra_gain_diamond = 18;	//额外赠送钻石(只给米大师pc调试使用)
	required int32 tss_recharge_service_id = 19;	//腾讯充值服务id
	optional int32 tss_recharge_service_type	= 20;	//腾讯充值服务类型
	optional int32 tss_recharge_service_duration_days	= 21;	//腾讯充值服务持续天数
};

message ipt_center_battle_player_apply_check {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_PLAYER_APPLY_CHECK ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int32 team_members			= 4;
}

message ipt_center_battle_player_apply_check_re {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_PLAYER_APPLY_CHECK_RE ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int32 param1				= 4;
	optional int32 param2				= 5;
	optional int32 ret				= 6;
}

message pubg_team_battle_data_t
{
	message team_member_t
	{
		optional bytes name			= 1;
		optional int32 match_prof	= 2;
	}
	repeated team_member_t member		= 1;
}

message ipt_center_battle_roam_player_apply {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_PLAYER_APPLY ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int32 param1				= 4;
	optional int32 param2				= 5;
	repeated int64 team_members			= 6;
	optional int64 roleid				= 7;
	optional bytes data					= 8;
}

message ipt_center_battle_roam_player_apply_re {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_PLAYER_APPLY_RE ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int32 average_wait_time	= 4;
	optional int32 ret			= 5;
	optional int64 roleid			= 6;
}

message ipt_center_battle_roam_server_apply
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_SERVER_APPLY ];
	optional int32 center_battle_type	= 2;
	optional int32 battle_type		= 3;
	optional bytes data			= 4;
	optional int64 corps_id			= 5;
	optional int32 server_level		= 6;
	optional int32 capacity_score		= 7;	//社团战斗力评分
    optional int64 corps_season_repu    = 8;    //
}

message ipt_center_battle_roam_server_apply_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_SERVER_APPLY_RE ];
	optional int32 center_battle_type	= 2;
	optional int32 battle_type		= 3;
	optional int64 corps_id			= 4;
	optional int32 ret			= 5;
	optional bool is_new			= 6;
}

message ipt_center_battle_roam_join_check {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_JOIN_CHECK ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int64 roleid			= 4;
}

message ipt_center_battle_roam_join_check_re {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_JOIN_CHECK_RE ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int32 ret			= 4;
	optional int64 roleid		= 5;
}

message ipt_center_battle_roam_join_ask {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_JOIN_ASK ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int64 roleid			= 4;
}

message ipt_center_battle_minigame {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_MINIGAME ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	enum MINI_STATE 
	{
		START = 1;
		SYNC = 2;
		RESULT = 3;
		CHAT = 4;
		TIMEOUT = 5;
	};
	optional  MINI_STATE stat  = 4;
	optional  int64 player1    = 5;
	optional  int64 player2    = 6;
	optional  int32 game_type  = 7;
	optional  int32 game_id    = 8;
	optional  int64 roleid     = 9;
	optional  bytes data	   = 10;
	optional  int32 stop_type   = 11;
	optional  int32 ret = 12; 
	optional  int32 progress = 13; 
}

message ipt_center_battle_ready_notify {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_READY_NOTIFY ];
	optional int32 center_battle_type               = 2;
	optional int32 battle_type                      = 3;
	repeated roam_ready_info roles          = 4;
}


message ipt_center_battle_roam_join_answer {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_JOIN_ANSWER ];
	optional int32 center_battle_type		= 2;
	optional int32 agree			= 3;
	optional int64 roleid			= 4;
}

message center_battle_roam_teleport_player_param_t
{
	optional int32 point_index		= 1;	// 圣殿骑士团据点战据点索引
	optional int64 roam_communityid	= 2;	// 圣殿骑士团id
}

message ipt_center_battle_roam_teleport_player {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_TELEPORT_PLAYER ];
	optional int32 center_battle_type		= 2;
	optional int64 roleid				= 3;
	optional int32 center_zoneid			= 4;
	optional int32 world_tid				= 5;
	optional int32 world_id					= 6;
	optional int32 retcode				= 7 [ default = 0 ];
	optional int32 level_section			= 8;
	optional int32 battle_type				= 9;
	optional center_battle_roam_teleport_player_param_t param = 10;
}

message ipt_center_battle_roam_player_punished {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_PLAYER_PUNISHED ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int32 punish_time			= 4;
	optional int64 roleid				= 5;
}

message ipt_center_battle_roam_player_quit {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_PLAYER_QUIT ];
	optional int32 center_battle_type		= 2;
	optional int32 battle_type			= 3;
	optional int32 tag				= 4;
	optional int32 ret				= 5;
	optional int64 roleid			= 6;
}

message ipt_center_battle_roam_battle_close {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CENTER_BATTLE_ROAM_BATTLE_CLOSE ];
	required int32 inst_tid = 2;
	required int32 inst_id = 3;
	optional int32 inst_param1 = 4;
	optional int32 inst_param2 = 5;
	optional int32 inst_param3 = 6;
	optional int32 category    = 7;
}

message db_roam_token {
	required bytes home_account		= 1;
	required int64 inner_login_token			= 2;
	optional bytes platid 	= 3;
	optional bytes realplatid 	= 4;
	optional bytes device_category 	= 5;
};

message ipt_update_roam_token {
	optional INNER_PROTOCBUF_TYPE type		= 1	[ default = IPT_UPDATE_ROAM_TOKEN ];
	repeated db_roam_token roam_token_list	= 2;
}

message ipt_rename_update {
        optional INNER_PROTOCBUF_TYPE type              = 1     [ default = IPT_RENAME_UPDATE ];
        required int64 roleid                           = 2;
        required bytes new_rolename                     = 3;
}

message ipt_adventure_task_finish {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ADVENTURE_TASK_FINISH ];
	optional int32 taskid					= 2;
}

message ipt_deliver_title {
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_DELIVER_TITLE ];
	optional int32 tid                      = 2;
	optional bytes data						= 3;
}

message ipt_adventure_task_rank {
       optional INNER_PROTOCBUF_TYPE type   = 1     [ default = IPT_ADVENTURE_TASK_RANK ];
       optional int32 taskid				= 2;
       optional int32 finish_time           = 3;    
       optional int32 rank                  = 4;
}

message ipt_reliable_message_inform {
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_RELIABLE_MESSAGE_INFORM ];
	optional int64 begin_serial				= 2;
	optional int32 proto_count				= 3;
}

message ipt_reliable_message_reply {
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_RELIABLE_MESSAGE_REPLY ];
	optional reliable_message_box_t box		= 2;
}

message airacerinfo
{
	optional int32 aitid 	= 1;
	optional int32 cartid 	= 2;
}
message carrace_pve_instanceparams {
	repeated airacerinfo pveaiinfo = 1;
	optional int64 roleid 	= 2;
	optional int32 cartid 	= 3;
}

message ipt_gs_check_carrace_matchapply { //ds to gs
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_GS_CHECK_CARRACE_MATCHAPPLY ];
    optional int32 mode 					= 2; //match mode
    optional int32 matchtypetid 			= 3; //tid in carrace_match.lua
	optional int32 car_tid = 4;
}

message ipt_center_battle_notify_center_zoneid {
    optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_CENTER_BATTLE_NOTIFY_CENTER_ZONEID ];
	repeated int64 roleids						= 2;
	optional int32 center_battle_type			= 3 [ default = 0 ];
	optional int32 battle_type					= 4 [ default = 0 ];
	optional int32 zoneid						= 5 [ default = 0 ];
	optional int32 param1						= 6 [ default = 0 ];
	optional int32 param2						= 7 [ default = 0 ];
	optional int32 param3						= 8 [ default = 0 ];
}

message ipt_center_battle_transfer_player {
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_CENTER_BATTLE_TRANSFER_PLAYER ];
	repeated centerbattle_player_t players	= 2;
	optional int32 center_battle_type			= 3 [ default = 0 ];
	optional int32 battle_type                  = 4 [ default = 0 ];
	optional int32 level_section				= 5 [ default = 0 ];
	optional int32 team_member_count_1			= 6 [ default = 0 ];
	optional int32 team_member_count_2			= 7 [ default = 0 ];
	optional int64 team_leader_1				= 8 [ default = 0 ];
	optional int64 team_leader_2				= 9 [ default = 0 ];
	repeated int32 team_player_count			= 10;
	optional int32 param4						= 11 [ default = 0 ];
	optional int32 increase_robot_count			= 12 [ default = 0 ];
	repeated int32 params						= 13;
}

message ipt_gs_check_carrace_matchapply_ret { //gs to ds
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_GS_CHECK_CARRACE_MATCHAPPLY_RET ];
	optional int32 result 					= 2; //0 success -1 err
	repeated airacerinfo aiinfo 			= 3; //ai
	optional int64 roleid 					= 4; //applyer
	optional int32 roomtickcount			= 5; //in config
	optional int32 matchmode 				= 6; //0 pve, 1 pvp
	optional int32 matchtypetid 			= 7; //tid in carrace_match.lua
	optional int32 car_tid = 8;
}

message ipt_gs_carrace_instance_created { //gs to ds
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_GS_CARRACE_INSTANCE_CREATED ];
	optional int32 instance_tid 			= 2;
	optional int32 instance_id 				= 3;
}

message ipt_player_try_enterinstance {
	optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_PLAYER_TRY_ENTERINSTANCE ];
	optional int64 roleid 					= 2;
	optional int32 instance_tid 			= 3;
	optional int32 instance_id 				= 4;	
} 

message ipt_reliable_message_confirm {
	enum CONFIRM_TYPE
	{
		CONFIRM_NORMAL		= 1;
		CONFIRM_ONLOGIN		= 2;
		CONFIRM_ONLOGOUT	= 3;
	};
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_RELIABLE_MESSAGE_CONFIRM ];
	optional int64 serial					= 2;
	optional CONFIRM_TYPE confirm_type		= 3;

}

message ipt_reliable_message_request {
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_RELIABLE_MESSAGE_REQUEST ];
	optional int64 serial					= 2;
}

message player_guard_essence // 角色所有守护灵的持久化数据
{
	repeated guard_essence guards = 1;
	repeated guard_slot_essence slots = 2;
	optional int32 summon_guard_id = 3;
	optional guard_star_essence star = 4;
	optional guard_house_essence house = 5;
}

message player_guard_toplist
{
	optional guard_client_data max_guard = 1;
}

message career_shop_toplist
{
	optional bytes name		= 1;
	optional bytes slogan	= 2;
}

message ipt_roam_speak
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_ROAM_SPEAK ];
	optional int32 zoneid_src		= 2;
	optional int64 src_roleid		= 3;
	optional bytes src_name			= 4;
	optional int64 dst_roleid		= 5;
	optional bytes dst_name			= 6;
	optional int32 channel			= 7;
	optional bytes msg			= 8;
	optional int32 spk_id			= 9;
	optional bytes speak_data		= 10;
}

message ipt_change_systime
{
	optional INNER_PROTOCBUF_TYPE type = 1 [ default = IPT_CHANGE_SYSTIME ];
	optional int32 new_time = 2;
}
//玩家和双生体之间的消息
message player_twin
{
	enum OP_TYPE {
		DELETE = 0;//删除双生体
		SYNC_POS = 1;//双生体同步位置
		CAST_SKILL = 2;//双生体释放技能
		BIND = 3;//双生体绑定
		UNBIND = 4;//双生体解绑
		SYNC_TALENT = 5;//同步天赋给双生体
		SYNC_BLOOD_TALENT = 6;//同步血装天赋给双生体
		CAST_SKILL_AT_POS = 7;//双生体瞬移到目标点释放技能
		FEED_BACK_SKILL = 8;//技能过程反馈技能
	};
	optional OP_TYPE type = 1;
	optional float x = 2;
	optional float y = 3;
	optional float z = 4;
	optional int32 dir = 5;
	optional float speed = 6;
	optional int32 skill_id = 7;
	optional int32 skill_level = 8;
	optional int64 target_id = 9;
	repeated int32 talents = 10;
	repeated int32 blood_talent_id = 11;
	repeated int32 blood_talent_lv = 12;
	message filter_info
	{
		optional int32 id = 1;
		optional int32 lv = 2;
		optional int32 tm = 3;
		optional int64 rid= 4;
	};
	repeated filter_info filter = 13;
	message feed_back_info
	{
		optional int32 skill_id = 1;
		optional int32 skill_level = 2;
		repeated int64 target_id = 3;
	};
	repeated feed_back_info feed_back = 14;
}

//玩家和机甲之间的消息
message player_mech
{
	enum OP_TYPE {
		DELETE = 0;//删除机甲
		SYNC_POS = 1;//机甲同步位置
		CAST_SKILL = 2;//机甲释放技能
		BIND = 3;//机甲绑定
		UNBIND = 4;//机甲解绑
		SYNC_TALENT = 5;//同步天赋给机甲
		SYNC_BLOOD_TALENT = 6;//同步血装天赋给机甲
		CAST_SKILL_AT_POS = 7;//机甲瞬移到目标点释放技能
		FEED_BACK_SKILL = 8;//技能过程反馈技能
		ENTER_COMBAT = 9;//进入战斗状态
		LEAVE_COMBAT = 10;//离开战斗状态
		SELECT_TARGET = 11;//选择目标
		CANCEL_TARGET = 12;//取消目标
		COMMON_INFO = 13;//同步一些杂乱的数据
		IM_DEAD = 14;//死亡
		ON_CURE = 15;//受到治疗
		FIGHT_BACK_LIST = 16;//反击列表
		BE_FIGHT_BACK_LIST = 17;//被反击列表
		SHOW_PROP = 18;//显示机甲的属性
	};
	optional OP_TYPE type = 1;
	optional float x = 2;
	optional float y = 3;
	optional float z = 4;
	optional int32 dir = 5;
	optional float speed = 6;
	optional int32 skill_id = 7;
	optional int32 skill_level = 8;
	optional int64 target_id = 9;
	repeated int32 talents = 10;
	repeated int32 blood_talent_id = 11;
	repeated int32 blood_talent_lv = 12;
	message filter_info
	{
		optional int32 id = 1;
		optional int32 lv = 2;
		optional int32 tm = 3;
		optional int64 rid= 4;
	};
	repeated filter_info filter = 13;
	message feed_back_info
	{
		optional int32 skill_id = 1;
		optional int32 skill_level = 2;
		repeated int64 target_id = 3;
	};
	repeated feed_back_info feed_back = 14;
	message common_info
	{
		optional bool pvp_protect_flag = 1;
		optional int32 team_new_type = 2;
		optional int64 team_id = 3;
		optional int64 mafia_id = 4;
		optional int32 faction = 5;
		optional int32 pk_setting = 6;
		optional int32 pk_value = 7;
		optional int32 duel_new_type = 8;
		optional int64 duel_id = 9;
		optional int32 region_setting = 10;
		optional int64 mentor_id = 11;
		repeated int64 intimate_list = 12;
		optional uint64 fashion_gfx_modify_mask = 13;
		optional int32 common_skill_level = 14;
	};
	optional common_info common = 15;
	repeated int64 fight_back_list = 16;
	repeated int64 be_fight_back_list = 17;
}

//玩家和假人之间的消息
message player_faker
{
	enum OP_TYPE {
		DELETE = 0;//删除假人
		SET_POS = 1;
		CAST_SKILL = 2;//假人释放技能
		SYNC_TALENT = 5;//同步天赋给假人
		SYNC_BLOOD_TALENT = 6;//同步血装天赋给假人
		CAST_SKILL_AT_POS = 7;//假人瞬移到目标点释放技能
		FEED_BACK_SKILL = 8;//技能过程反馈技能
		COMMON_INFO = 13;//同步一些杂乱的数据
		IM_DEAD = 14;//死亡
		ON_CURE = 15;//受到治疗
		FIGHT_BACK_LIST = 16;//反击列表
		BE_FIGHT_BACK_LIST = 17;//被反击列表
		SHOW_PROP = 18;//显示假人的属性
		SET_MODE = 19;
	};
	optional OP_TYPE type = 1;
	optional float x = 2;
	optional float y = 3;
	optional float z = 4;
	optional int32 dir = 5;
	optional float speed = 6;
	optional int32 skill_id = 7;
	optional int32 skill_level = 8;
	optional int64 target_id = 9;
	repeated int32 talents = 10;
	repeated int32 blood_talent_id = 11;
	repeated int32 blood_talent_lv = 12;
	message filter_info
	{
		optional int32 id = 1;
		optional int32 lv = 2;
		optional int32 tm = 3;
		optional int64 rid= 4;
	};
	repeated filter_info filter = 13;
	message feed_back_info
	{
		optional int32 skill_id = 1;
		optional int32 skill_level = 2;
		repeated int64 target_id = 3;
	};
	repeated feed_back_info feed_back = 14;
	message common_info
	{
		optional bool pvp_protect_flag = 1;
		optional int32 team_new_type = 2;
		optional int64 team_id = 3;
		optional int64 mafia_id = 4;
		optional int32 faction = 5;
		optional int32 pk_setting = 6;
		optional int32 pk_value = 7;
		optional int32 duel_new_type = 8;
		optional int64 duel_id = 9;
		optional int32 region_setting = 10;
		optional int64 mentor_id = 11;
		repeated int64 intimate_list = 12;
		optional uint64 fashion_gfx_modify_mask = 13;
		optional int32 common_skill_level = 14;
	};
	optional common_info common = 15;
	repeated int64 fight_back_list = 16;
	repeated int64 be_fight_back_list = 17;
	message mode_info
	{
		optional bool is_move = 1;
		optional int64 target_id = 2;
		repeated int32 skill_id = 3;
		repeated int32 skill_level = 4;
		repeated int32 buff_skill_id = 5;
		optional int32 end_skill_id = 6;
		optional int32 end_skill_level = 7;
	};
	optional mode_info mode = 18;
}

//玩家和继承者之间的消息
message player_heir
{
	enum OP_TYPE {
		DELETE = 0;//删除机甲
		SYNC_POS = 1;//机甲同步位置
		CAST_SKILL = 2;//机甲释放技能
		BIND = 3;//机甲绑定
		UNBIND = 4;//机甲解绑
		SYNC_TALENT = 5;//同步天赋给机甲
		SYNC_BLOOD_TALENT = 6;//同步血装天赋给机甲
		CAST_SKILL_AT_POS = 7;//机甲瞬移到目标点释放技能
		FEED_BACK_SKILL = 8;//技能过程反馈技能
		ENTER_COMBAT = 9;//进入战斗状态
		LEAVE_COMBAT = 10;//离开战斗状态
		SELECT_TARGET = 11;//选择目标
		CANCEL_TARGET = 12;//取消目标
		COMMON_INFO = 13;//同步一些杂乱的数据
		IM_DEAD = 14;//死亡
		ON_CURE = 15;//受到治疗
		FIGHT_BACK_LIST = 16;//反击列表
		BE_FIGHT_BACK_LIST = 17;//被反击列表
		SHOW_PROP = 18;//显示机甲的属性
		FASHION_CHANGE = 19;//时装有变化
		MARRIAGE_CHANGE = 20; //结婚信息变化
		OBJECT_STATE = 21;// object_state
		OBJECT_STATE2 = 22;// object_state2
	};
	optional OP_TYPE type = 1;
	optional float x = 2;
	optional float y = 3;
	optional float z = 4;
	optional int32 dir = 5;
	optional float speed = 6;
	optional int32 skill_id = 7;
	optional int32 skill_level = 8;
	optional int64 target_id = 9;
	repeated int32 talents = 10;
	repeated int32 blood_talent_id = 11;
	repeated int32 blood_talent_lv = 12;
	message filter_info
	{
		optional int32 id = 1;
		optional int32 lv = 2;
		optional int32 tm = 3;
		optional int64 rid= 4;
	};
	repeated filter_info filter = 13;
	message feed_back_info
	{
		optional int32 skill_id = 1;
		optional int32 skill_level = 2;
		repeated int64 target_id = 3;
	};
	repeated feed_back_info feed_back = 14;
	message common_info
	{
		optional bool pvp_protect_flag = 1;
		optional int32 team_new_type = 2;
		optional int64 team_id = 3;
		optional int64 mafia_id = 4;
		optional int32 faction = 5;
		optional int32 pk_setting = 6;
		optional int32 pk_value = 7;
		optional int32 duel_new_type = 8;
		optional int64 duel_id = 9;
		optional int32 region_setting = 10;
		optional int64 mentor_id = 11;
		repeated int64 intimate_list = 12;
		optional uint64 fashion_gfx_modify_mask = 13;
		optional int32 common_skill_level = 14;
		optional int64 roam_communityid = 15;
	};
	optional common_info common = 15;
	repeated int64 fight_back_list = 16;
	repeated int64 be_fight_back_list = 17;
	repeated fashion_detail fashion = 18;
	optional uint64 spouse_guid = 19;
	optional bytes spouse_name = 20;
	message object_state_info
	{
		optional uint64 mask = 1;
		optional bool have = 2;
	};
	optional object_state_info object_state = 21;
}
//玩家和分身之间的消息
message player_replisome
{
	enum OP_TYPE {
		DELETE = 0;//删除
		SYNC_POS = 1;//同步位置
		CAST_SKILL = 2;//体释放技能
		BIND = 3;//体绑定 
		UNBIND = 4;//解绑
		SYNC_TALENT = 5;//同步天赋
		SYNC_BLOOD_TALENT = 6;//同步血装天赋
		CAST_SKILL_AT_POS = 7;//瞬移到目标点释放技能
		FEED_BACK_SKILL = 8;//技能过程反馈技能
	};
	optional OP_TYPE type = 1;
	optional float x = 2;
	optional float y = 3;
	optional float z = 4;
	optional int32 dir = 5;
	optional float speed = 6;
	optional int32 skill_id = 7;
	optional int32 skill_level = 8;
	optional int64 target_id = 9;
	repeated int32 talents = 10;
	repeated int32 blood_talent_id = 11;
	repeated int32 blood_talent_lv = 12;
	message filter_info
	{
		optional int32 id = 1;
		optional int32 lv = 2;
		optional int32 tm = 3;
		optional int64 rid= 4;
	};
	repeated filter_info filter = 13;
	message feed_back_info
	{
		optional int32 skill_id = 1;
		optional int32 skill_level = 2;
		repeated int64 target_id = 3;
	};
	repeated feed_back_info feed_back = 14;
}

message player_dog
{
	enum OP_TYPE {
		DELETE = 0;//删除
		SYNC_POS = 1;//同步位置
		CAST_SKILL = 2;//体释放技能
		BIND = 3;//体绑定 
		UNBIND = 4;//解绑
		SYNC_TALENT = 5;//同步天赋
		SYNC_BLOOD_TALENT = 6;//同步血装天赋
		CAST_SKILL_AT_POS = 7;//瞬移到目标点释放技能
		FEED_BACK_SKILL = 8;//技能过程反馈技能
	};
	optional OP_TYPE type = 1;
	optional float x = 2;
	optional float y = 3;
	optional float z = 4;
	optional int32 dir = 5;
	optional float speed = 6;
	optional int32 skill_id = 7;
	optional int32 skill_level = 8;
	optional int64 target_id = 9;
	repeated int32 talents = 10;
	repeated int32 blood_talent_id = 11;
	repeated int32 blood_talent_lv = 12;
	message filter_info
	{
		optional int32 id = 1;
		optional int32 lv = 2;
		optional int32 tm = 3;
		optional int64 rid= 4;
	};
	repeated filter_info filter = 13;
	message feed_back_info
	{
		optional int32 skill_id = 1;
		optional int32 skill_level = 2;
		repeated int64 target_id = 3;
	};
	repeated feed_back_info feed_back = 14;
}



//社团拍卖
message corps_auction_result_t
{
	optional int64 corps_id		= 1;
	repeated int64 player		= 2; 
	optional bool is_win		= 3;
};

message corps_auction_state_t
{
	optional CORPS_AUCTION_STATE state	= 1 [ default = CORPS_AUCTION_IDLE ];
	optional int32 activity_id			= 2;
	optional int32 begin_time			= 3;
};

message corps_auction_item_t
{
	message role_record_t
	{
		optional int64 roleid			= 1;
		optional int32 withdraw_price	= 2;
	};
	optional int32 item_id				= 1;
	optional bytes item_content			= 2;
	optional int32 base_price			= 3;
	optional int32 buyout_price			= 4;
	optional CORPS_AUCTION_ITEM_STATE state	= 5;
	optional int32 current_price		= 6;
	optional int64 current_role			= 7;
	optional bytes current_role_name	= 8;
	repeated role_record_t record		= 9;
	optional int32 high_quality			= 10;
	optional int32 begin_time			= 11;
};


message corps_auction_award_t
{
	optional int64 roleid				= 1;
	optional int32 bind_cash			= 2;
};

message corps_auction_t
{
	message bonus_player
	{
		optional int64 roleid			= 1;
		optional int32 bonus_factor		= 2;
	};
	optional int64 auction_id			= 1;
	optional int64 corps_id				= 2;
	optional bytes corps_name			= 3;
	repeated bonus_player player		= 4;
	repeated corps_auction_item_t item	= 5;
	optional CORPS_AUCTION_STATE processed_state = 6;
	repeated corps_auction_award_t award_queue = 7;
	optional int32 total_price			= 8;
	optional int32 total_factor			= 9;
	optional bool is_win				= 10;
	optional int32 activity_id			= 11;
	optional int64 corps_repu			= 12;
};

message topstar_ip_t
{
	message toplist_t
	{
		message star_t
		{
			optional int32 star_id	= 1;
			optional int64 cheer	= 2;
		}
		optional int32 topid		= 1;
		repeated star_t star		= 2;
	}
	repeated toplist_t toplist		= 1; 
}

message hometown_instance_control_t
{
	enum Cmd
	{
		CMD_DEMOLISH                = 1;
		CMD_UPDATE_RELATION         = 2;
		CMD_DEBUG                   = 3;
		CMD_OBJECT_PUT              = 4;
		CMD_OBJECT_CANCEL           = 5;
		CMD_OBJECT_UPDATE           = 6;
		CMD_OBJECT_MULTI_OPERATION  = 7;
		CMD_WORKER_REPAIR           = 8;
		CMD_INTERACT_ADD_DAMAGE     = 9;
		CMD_WORKER_CAN_BE_CALLED    = 10;
		CMD_HOMETOWN_RENEW          = 11;
		CMD_CLEANING_ASSESS         = 12;
		CMD_ACTION_CLEAN            = 13;
		CMD_ACTION_REPAIR           = 14;
		CMD_GRID_DEMOLISH           = 15;
		CMD_PARTY_PUT_FOOD          = 16;
		CMD_GRID_OPERATION          = 17;
		CMD_SET_PREVIEW_DESIGN_ID   = 18;
		CMD_UPDATE_PASSWORD_DOOR    = 19;
		CMD_CONTRACT_CLEANING_ASSESS= 20;
        CMD_CONTRACT_DEMOLISH       = 21;
        CMD_CONTRACT_CANCEL         = 22;
		CMD_CONTRACT_RENEW          = 23;
		CMD_CONTRACT_GRID_DEMOLISH  = 24;
	}

	optional Cmd cmd = 1;

	// CMD_DEMOLISH
	message demolish_t
	{
	}
	optional demolish_t demolish = 2;

	// CMD_UPDATE_RELATION
	message update_relation_t
	{
	}
	optional update_relation_t update_relation = 3;

	// CMD_DEBUG
	message debug_t
	{
		enum Type
		{
			TYPE_PRINT_OBJECT_POSITION = 1;
			TYPE_CLEAN_ALL_OBJECT      = 2;
		}
		optional Type type   = 1;
		optional int32 param = 2;
	}
	optional debug_t debug = 4;

	// CMD_OBJECT_PUT
	message object_put_t
	{
		optional int32 obj_type  = 1;
		optional int32 put_index = 2;
		optional int64 put_param = 3;
	}
	optional object_put_t object_put = 5;

	// CMD_OBJECT_CANCEL
	message object_cancel_t
	{
		optional int32 obj_type  = 1;
		optional int32 put_index = 2;
	}
	optional object_cancel_t object_cancel = 6;

	// CMD_OBJECT_UPDATE
	message object_update_t
	{
		optional int32 obj_type  = 1;
		optional int32 put_index = 2;
	}
	optional object_update_t object_update = 7;

	// CMD_OBJECT_MULTI_OPERATION
	message object_multi_operation_t
	{
		message operation_t
		{
			optional int32 obj_type  = 2;
			optional int32 put_index = 3;
			optional int64 put_param = 4;
		}
		repeated operation_t puts    = 1;
		repeated operation_t cancels = 2;
		repeated operation_t updates = 3;
	}
	optional object_multi_operation_t object_multi_operation = 8;

	// CMD_WORKER_REPAIR
	message worker_repair_t
	{
		optional int32 obj_type  = 1;
		optional int32 put_index = 2;
	}
	optional worker_repair_t worker_repair = 9;

	// CMD_INTERACT_ADD_DAMAGE
	message interact_add_damage_t
	{
		optional int32 put_index = 1;
	}
	optional interact_add_damage_t interact_add_damage = 10;

	// CMD_WORKER_CAN_BE_CALLED -> no param

	// CMD_CLEANING_ASSESS
	message cleaning_assess_t
	{
		optional bool do_reward = 1; // 是否发奖
	}
	optional cleaning_assess_t cleaning_assess = 11;

	// CMD_ACTION_CLEAN
	message action_clean_t
	{
		optional int32 obj_type  = 1; // 0表示地面
		optional int32 put_index = 2;
	}
	optional action_clean_t action_clean = 12;

	// CMD_ACTION_REPAIR
	message action_repair_t
	{
		optional int32 obj_type  = 1;
		optional int32 put_index = 2;
	}
	optional action_repair_t action_repair = 13;

	// CMD_GRID_DEMOLISH -> no param

	// CMD_PARTY_PUT_FOOD
	message party_put_food_t
	{
		optional int32 matter_id = 1;
		optional a3d_pos matter_pos = 2;
		optional int32 matter_dir = 3;
	}
	optional party_put_food_t party_put_food = 14;

	// CMD_GRID_OPERATION
	message grid_operation_t
	{
		optional int32 floor_index = 1; // 0|1|2|3
		message operation_t
		{
			optional int32 obj_type    = 1;
			repeated int32 pos_indexes = 2;
		}
		repeated operation_t cancels = 2;
		repeated operation_t puts    = 3;
	}
	optional grid_operation_t grid_operation = 15;

	// CMD_SET_PREVIEW_DESIGN_ID
	message set_preview_design_id_t
	{
		optional int64 design_id = 1;
	}
	optional set_preview_design_id_t set_preview_design_id = 16;

	// CMD_UPDATE_PASSWORD_DOOR
	message update_password_door_t
	{
		optional int32 obj_type  = 1;
		optional int32 put_index = 2;
	}
	optional update_password_door_t update_password_door = 17;
}
message hometown_instance_response_t
{
	optional int32 retcode = 1;
	optional hometown_instance_control_t.Cmd cmd = 2;

	// CMD_WORKER_CAN_BE_CALLED -> no param
	// CMD_CLEANING_ASSESS
	message cleaning_assess_t
	{
		optional int32 level        = 1; // 评价等级
		optional int32 reward_money = 2; // 奖励家园代币
		optional int32 reward_tid   = 3; // 奖励模板
		optional bool  do_reward               = 4; // 是否发奖
		optional int32 ground_dirty_point      = 5; // 地面脏度
		optional int32 dirty_furniture_count   = 6; // 变脏家具数量
		optional int32 damaged_furniture_count = 7; // 损坏家具数量
		optional int32 hometown_scale          = 8; // 家园规模
	}
	optional cleaning_assess_t cleaning_assess = 3;
}

// google云翻译
message GoogleCloudTranslateArg {
        repeated string src_text                        = 1;
        optional string dst_lang                        = 2;
        optional int64 role_id                          = 3;
        optional int32 translate_index          = 4;
}

message GoogleCloudTranslateRes {
        optional string src_lang                        = 1;
        repeated string dst_text                        = 2;
}

message ipt_anonymous_chat_room_op
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ANONYMOUS_CHAT_ROOM_OP];
    optional ANONYMOUS_CHAT_ROOM_OP op      = 2;    // 操作类型
	// ACRO_CREATE_ROOM
	optional int32 theme					= 3;	// 主题
	optional int32 role_max_type			= 4;	// 最大人数类型
	optional int32 time_len_type			= 5;	// 时长类型
	optional bytes name						= 6;	// 房间名
	optional bytes passwd					= 7;	// 密码
	repeated bytes tags						= 8;	// 标签
	optional anonymous_chat_room_member_info role_info = 9;		// 个人详细数据
	//optional uint32 txnid					= 14;	// 用下声明的txnid
	//optional bytes role_name				= 18;	// 用下面声明的role_name

	// ACRO_ENTER_ROOM
	optional uint64 room_id					= 10;	// 房间号
	optional bool is_enter_room				= 11;	// 进入频道的同时是否进入房间
	//optional bytes passwd					= 7;	// 密码，使用已声明的passwd
	//optional anonymous_chat_room_member_info role_info = 9;		// 用上面已经声明过的role_info
	//optional bytes role_name				= 18;	// 用下面声明的role_name

	// ACRO_CHANGE_ACR
	//optional uint64 room_id				= 10;    // 用上面声明过的 room_id
	//optional bool is_enter_room			= 11;	// 用上面声明过的is_enter_room
	optional uint64 target_room_id			= 12;   // 要切换到的房间id
	optional bytes target_room_passwd		= 13;   // 要切换到的房间密码
	//optional bytes role_name				= 18;	// 用下面声明的role_name

	// ACRO_ADD_TIME
	//optional uint64 room_id           = 9;    // 用上面声明过的 room_id
	//optional int32 time_len_type		= 5;	// 用上面声明过的 time_len_type

	// ACRO_SET_DISPLAY_DATA
	//optional anonymous_chat_room_member_info role_info = 9;       // 用上面已经声明过的role_info
	//optional int64 photo_id                 = 15;   // 头像
	//optional int32 chat_bubble              = 16;   // 聊天气泡


	// 后加的放这里
	optional int32 txnid					= 14;	// 事务id
	optional int64 photo_id					= 15;	// 头像
	optional int32 chat_bubble				= 16;	// 聊天气泡
	repeated int64 target_role_list			= 17;	// 跨服广播中的目标列表
	optional bytes role_name				= 18;	// 玩家名字

	// ACRO_HEARTBEAT
}

message ipt_anonymous_chat_room_op_re
{
    optional INNER_PROTOCBUF_TYPE type      = 1 [ default = IPT_ANONYMOUS_CHAT_ROOM_OP_RE];
    optional ANONYMOUS_CHAT_ROOM_OP op      = 2;    // 操作类型
	// ACRO_ENTER_ROOM
	optional int32 retcode					= 3;    // 错误码
	optional bool is_enter_room				= 4;	// 进入频道的同时是否进入房间
	repeated int64 roleid_list				= 5;	// 房间角色id列表
	optional anonymous_chat_room_brief_info room_info   = 6;    // 当前房间信息
	optional int64 role_id					= 7;	// 角色id
	//optional bytes role_room_name			= 13;	// 用下面声明的role_room_name

	// ACRO_CREATE_ROOM
	//optional int32 retcode				= 3;	// 用上面声明过的retcode
	//optional bool is_enter_room			= 4;    // 进入频道的同时是否进入房间
	//repeated int64 roleid_list			= 5;    // 用上面声明过的roleid_list
	//optional anonymous_chat_room_brief_info room_info		= 6;    // 用上面声明过的room_info
	//optional uint32 txnid                 = 12;   // 用下面声明的txnid
	//optional bytes role_room_name			= 13;	// 用下面声明的role_room_name

	// ACRO_SYNC_STATUS
	//optional bool is_enter_room			= 4;    // 用上面声明的is_enter_room
	//repeated int64 roleid_list			= 5;    // 用上面声明的roleid_list
	//optional anonymous_chat_room_brief_info room_info		= 6;    // 用上面声明的room_info
	repeated bytes tags						= 8;    // 高热度标签
	//optional int64 role_id				= 7;	// 用上面声明的role_id
	optional int64 photo_id					= 9;	// 头像
	optional int32 chat_bubble				= 10;	// 聊天气泡
	optional anonymous_chat_room_member_info role_info		= 11;	// 个人的外显数据

	// ACRO_CHANGE_ACR
	//optional int32 retcode				= 3;    // 用上面声明过的retcode
	//optional bool is_enter_room			= 4;    // 用上面声明的is_enter_room
	//repeated int64 roleid_list			= 5;    // 用上面声明过的roleid_list
	//optional anonymous_chat_room_brief_info room_info     = 6;    // 用上面声明过的room_info
	//optional bytes role_room_name			= 13;	// 用下面声明的role_room_name

	// ACRO_ADD_TIME
	//optional int32 retcode            = 3;    // 用上面声明过的retcode
	//optional uint64 room_id           = 9;    // 用上面声明过的 room_id
	//optional int32 time_len_type		= 5;	// 用上面声明过的 time_len_type
	//optional anonymous_chat_room_brief_info room_info     = 6;    // 用上面声明过的room_info

	// 后加的放这里
	optional int32 txnid					= 12;	// 事务id
	optional bytes role_room_name			= 13;	// 玩家在房间的名字
}

message acr_room_full_data
{
	optional int64	creator_id				= 1;	// 创建者id
	optional int32	zone_id					= 2;	// 服务器zone
	optional bytes	passwd					= 3;	// 密码
	optional int32	role_max_type			= 4;	// 最大人数类型
	optional anonymous_chat_room_brief_info brief_info	= 5;	// 同步给客户端的房间信息
}

message acr_member_room_data
{
	optional int64	role_id					= 1;	// 角色id
	optional bool   is_enter_room			= 2;    // 进入频道的同时是否进入房间
	optional bool   is_special_role			= 3;    // 是否带有特殊标签
	optional bool   is_open_voice			= 4;    // 是否开启实时语音
	optional int32  words_speak_num			= 5;    // 文字发言数量
	optional int32  voice_speak_num			= 6;    // 语言发言数量
	optional int32  index					= 7;    // 房间内的索引
	optional int32	active_time				= 8;	// 上次心跳时间
	optional int32	enter_time				= 9;	// 进入房间时间
}

enum ROAM_ACR_OP
{
	ROAM_ACR_OP_CHAT            = 1;    // 匿名聊天
	ROAM_ACR_OP_TIME_OUT        = 2;    // 房间到期
    ROAM_ACR_OP_ROOM_CLOSE      = 3;    // 房间被关闭
    ROAM_ACR_OP_MEMB_ENTER      = 4;    // 有人进入
    ROAM_ACR_OP_MEMB_LEAVE      = 5;    // 有人退出
    ROAM_ACR_OP_SYNC_ROOM_INFO	= 6;    // 同步房间信息
}
message ipt_roam_acr_op
{
	optional INNER_PROTOCBUF_TYPE   type    = 1 [ default = IPT_ROAM_ACR_OP ];
    optional ROAM_ACR_OP op                 = 2;
    message target_params
    {
        optional int64 role_id          = 1;
        optional bool is_enter_room     = 2;
		optional acr_member_room_data memb_data = 3;
    }
    repeated target_params role_list        = 3;
    optional bytes data                     = 4;
	optional acr_room_full_data	room_data	= 5;
}

message god_explore_ip_t
{
	// 神迹探险层数据全局记录
	message level_data
	{
		optional int32 level				= 1;
		optional int32 total_draw_count		= 2;
		message role_t
		{
			optional int64 role_id			= 1;
			optional int32 draw_count		= 2;
			//optional int32 last_reward_time = 3;
		}
		repeated role_t role_list			= 3;
	}
	repeated level_data level_list			= 1;
	optional int32 campaign_end_time		= 2;
	optional int32 last_reward_time			= 3;
}

message god_explore_boss_ip_t
{
    optional int32 campaign_end_time  = 1;
    optional int32 all_draw_count = 2;
    message role_t
    {
        optional int64 roleid = 1;
        optional int32 draw_count = 2;
    }
    repeated role_t roles = 3;
	optional int32 campaign_idx = 4;
	optional int32 last_reward_time			= 5;
}

message ipt_cross_server_team_op
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CROSS_SERVER_TEAM_OP ];
	optional CROSS_SERVER_TEAM_OP op        = 2;

	// CROSS_SERVER_TEAM_OP_INVITE
	//optional int64 sponsor_id				= 3;	// 申请者roleid
	//optional bytes sponsor_name			= 4;	// 申请者名称
	//optional int64 target_id				= 5;	// 目标队伍队长的roleid
	//optional int32 param1					= 6;    // 是否是邀请：1邀请，0申请


	optional int64 sponsor_id               = 3;    //
	optional bytes sponsor_name             = 4;    //
	optional int64 target_id                = 5;    //
	optional int32 param1					= 6;    //
	optional int32 param2	                = 7;	//
	optional int32 source_zoneid            = 8;	//

}

message ipt_cross_server_team_op_re
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CROSS_SERVER_TEAM_OP_RE ];
	optional CROSS_SERVER_TEAM_OP op        = 2;

	// CROSS_SERVER_TEAM_OP_INVITE
	//optional int64 sponsor_id				= 4;	// 邀请者roleid
	//optional int32 param1                 = 5;	// 是否是邀请：1邀请，0申请
	//optional int32 param2                 = 6;	// 是否同意
	//optional int32 ret_code				= 3;    // 错误码

	optional int32 ret_code					= 3;    // 错误码
	optional int64 sponsor_id               = 4;    //
	optional int32 param1	                = 5;	//
	optional int32 param2	                = 6;	//
	optional int64 target_id                = 7;    //
}

message ipt_cross_server_team_notify
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_CROSS_SERVER_TEAM_NOTIFY ];
	optional CROSS_SERVER_TEAM_NOTIFY op	= 2;	// 操作类型

	// CROSS_SERVER_TEAM_NOTIFY_ENTER
	//optional int64 sponsor_id				= 3;	// 入队者
	//optional cross_server_team_data cst_data = 5; // 跨服队伍数据

	// CROSS_SERVER_TEAM_NOTIFY_NEW_MEMBER
	//repeated int64 member_ids				= 4;	//
	//optional cross_server_team_member_client_data member_data = 6;

	// CROSS_SERVER_TEAM_NOTIFY_LEAVE
	//repeated int64 member_ids				= 4;	//
	//optional cross_server_team_member_client_data member_data = 6;

	// CROSS_SERVER_TEAM_NOTIFY_CHANGE_LEADER
	//repeated int64 member_ids				= 4;	//
	//optional cross_server_team_member_client_data member_data = 6;

	// CROSS_SERVER_TEAM_NOTIFY_KICK
	//repeated int64 member_ids				= 4;	//
	//optional cross_server_team_member_client_data member_data = 6;

	// CROSS_SERVER_TEAM_NOTIFY_DISSOLUTION
	//repeated int64 member_ids				= 4;	//
	//optional cross_server_team_member_client_data member_data = 6;

	// CROSS_SERVER_TEAM_NOTIFY_DISSOLUTION
	//repeated int64 member_ids				= 4;	//
	//optional cross_server_team_member_client_data member_data = 6;
	//optional cross_server_team_member_client_data member_old_data = 7;	//

	// CROSS_SERVER_TEAM_NOTIFY_CHAT
	//repeated int64 members_id				= 4;	//
    //optional bytes data                   = 8;	//

	// CROSS_SERVER_TEAM_NOTIFY_CALL_MEMBER
	//repeated int64 member_ids				= 4;	//
	//optional cross_server_team_member_client_data member_data = 6;

	optional int64 sponsor_id				= 3;	//
	repeated int64 member_ids				= 4;
	optional cross_server_team_data cst_data = 5; // 跨服队伍数据
	optional cross_server_team_member_client_data member_data = 6;	//
	optional cross_server_team_member_client_data member_old_data = 7;	//
    optional bytes data                     = 8;	//
	optional int64 timestamp				= 9;
	optional int64 target_id				= 10;
	optional int32 param1	                = 11;	//
	optional int32 param2	                = 12;	//
}

message cross_server_team_member_data
{
	optional cross_server_team_member_client_data client_data	= 1;	// 客户端需要的数据
	optional int32 heartbeat				= 2;	// 心跳的时间戳
	optional int32 leader_index				= 3;	// 队长继承索引，0表示为队长
}

message ipt_scrawl_pasted_alert{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_SCRAWL_PASTED_ALERT ];
	optional int32 key					= 2;//矿模板ID
}

message ipt_single_bless_wall_op
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_SINGLE_BLESS_WALL_OP ];
	optional SINGLE_BLESS_WALL_OP op			= 2;	// 操作类型
	optional SINGLE_BLESS_WALL_TYPE sbw_type	= 3;	// 许愿墙类型
	optional int32 msg_idx						= 4;	// 祝福语ID：配置索引
	optional bytes msg							= 5;	// 祝福语：自编辑
	optional int32 txnid						= 6;	// 事务id
}

message ipt_single_bless_wall_op_re
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_SINGLE_BLESS_WALL_OP_RE ];
	optional SINGLE_BLESS_WALL_OP op			= 2;	// 操作类型
	optional int32 retcode						= 3;	// 错误码
	optional SINGLE_BLESS_WALL_TYPE sbw_type	= 4;	// 许愿墙类型
	optional single_bless_wall_info blessinfo	= 5;	//
	optional int32 txnid						= 6;	// 事务id
}

message ipt_honey_garden_op
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_HONEY_GARDEN_OP ];
	optional HONEY_GARDEN_OP op					= 2;	// 操作类型
	optional int64 apply_index					= 3;	// 创建请求在ds上的索引
	repeated int64 owners						= 4;	// 成员
	optional int64 leader						= 5;	// 管理者
	optional bytes name							= 6;	// 花园名字
	optional int32 param1						= 7;	// 针对不同的op有不同的含义
	optional int32 param2						= 8;	// 针对不同的op有不同的含义
	optional int32 txnid						= 9;	// 事务id
	optional int64 garden_id					= 10;	// 花园id
	optional int64 roleid						= 11;	//
	optional int64 target_roleid				= 12;	//
}

message ipt_honey_garden_op_re
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_HONEY_GARDEN_OP_RE ];
	optional HONEY_GARDEN_OP op					= 2;	// 操作类型
	optional int32 retcode						= 3;	// 错误码
	optional int64 apply_index					= 4;	// 创建请求在ds上的索引
	optional honey_garden_data hg_data			= 5;	// 花园数据
	optional int32 param1						= 6;	// 针对不同的op有不同的含义
	optional int32 param2						= 7;	// 针对不同的op有不同的含义
	optional int32 txnid						= 8;	// 事务id
	optional int64 roleid						= 9;	//
	optional bytes name							= 10;	//
	optional int64 target_roleid				= 11;	//
}

message ipt_team_recharge_op
{
	optional INNER_PROTOCBUF_TYPE type          = 1 [ default = IPT_TEAM_RECHARGE_OP ];
	optional TEAM_RECHARGE_OP op                = 2;
	optional int64 roleid						= 3;
	optional bytes name							= 4;
	optional int64 approve_roleid				= 5;
	optional int64 id							= 6;
	optional int32 team_recharge				= 7;
	optional int32 activeid						= 8;
	repeated int64 ids							= 9;
	repeated team_recharge_data tr_datas		= 10;
	optional int64 leave_id						= 11; // 离开了哪一个队伍
	optional int32 try_times					= 12; // 
}

message ipt_team_recharge_op_re
{
	optional INNER_PROTOCBUF_TYPE type          = 1 [ default = IPT_TEAM_RECHARGE_OP_RE ];
	optional TEAM_RECHARGE_OP op				= 2;
	optional int32 retcode						= 3;
	optional int64 roleid						= 4;
	optional team_recharge_data tr_data			= 5;
	optional int64 approve_roleid				= 6;
	optional int64 id							= 7;
	optional team_recharge_member_data member	= 8;
	repeated int64 ids							= 9;
	repeated team_recharge_data tr_datas		= 10;
}
enum INTIMATE_NATS_OP
{
	ITNO_NONE           = 0;
	ITNO_CREATE_BRORDCAST     = 1; //结成广播
	ITNO_SELECT_THEME = 2; 
	ITNO_MESSAGE_MODIFY = 3; 
	ITNO_ANNI_MESSAGE_MODIFY = 4;
	ITNO_FORCE_DEL_MAIL = 5;
	ITNO_DEL_INTIMATE = 6;
}

message ipt_pve_intimate_nats_op
{
	optional INNER_PROTOCBUF_TYPE type          = 1 [ default = IPT_PVE_INTIMASTE_NATS_OP ];
	optional INTIMATE_NATS_OP op                = 2;
	optional int64  roleid_1  =3 ;
	optional bytes  name_1    =4 ;
	optional int64  roleid_2  =5;
	optional bytes  name_2    =6;
	optional bytes intimate_name = 7;
	optional int64 seq   =8;
	optional int32 param = 9;
	optional bytes param2 = 10;
}
enum HARMONIOUS_NATS_OP
{
	HNO_NONE           = 0;
	HNO_CREATE_BRORDCAST     = 1; //结成广播
	HNO_SELECT_THEME = 2; 
	HNO_MESSAGE_MODIFY = 3; 
	HNO_ANNI_MESSAGE_MODIFY = 4;
	HNO_SIGLE_DEL_MAIL = 5;
	HNO_DEL_HARMONIOUS = 6;
	HNO_UPDATE_TASK    = 7;
}


message ipt_pve_harmonious_nats_op
{
	optional INNER_PROTOCBUF_TYPE type          = 1 [ default = IPT_PVE_HARMONIOUS_NATS_OP ];
	optional HARMONIOUS_NATS_OP op                = 2;
	optional int64  roleid_1  =3 ;
	optional bytes  name_1    =4 ;
	optional int64  roleid_2  =5;
	optional bytes  name_2    =6;
	optional bytes  name = 7;
	optional int64 seq   =8;
	optional int32 param = 9;
	optional bytes param2 = 10;
	repeated harmonious_task   daily_task = 11;
}



message ipt_roam_blessing_info
{
    optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ROAM_BLESSING_INFO ];
	optional ipt_blessing_info info			= 2;
}

message ipt_roam_blessing_info_re
{
    optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ROAM_BLESSING_INFO_RE ];
	optional ipt_blessing_info info			= 2;
	optional int32 retcode					= 3;
}

message ipt_goods_discount_op
{
    optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_GOODS_DISCOUNT_OP ];
	optional GOODS_DISCOUNT_OP op			= 2;
	// GD_DISCOUNT
	optional int32 goodsid					= 3;
	optional int64 target_roleid			= 4;
	optional int32 ticketid					= 5;
	// 以下字段为gs发往ds时所需
	optional int32 txnid					= 6;
	//optional int32 retcode					= 11;

	// GD_BUY
	//optional int32 goodsid					= 3;
	//optional int32 txnid					= 6;
	optional int32 discount_price			= 7;
	optional bytes rdata					= 8;
	// 以下字段为发往db时所需
	message ret_ticket
	{
		optional int64 roleid		= 1;
		optional int32 ticketid		= 2;
		optional int32 num			= 3;
	}
	repeated ret_ticket roles_ret			= 9;
	optional int64 roleid					= 10;
	// 以下字段为gs发往ds时所需
	optional int32 retcode					= 11;
}

message ipt_goods_discount_op_re
{
    optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_GOODS_DISCOUNT_OP_RE ];
	optional GOODS_DISCOUNT_OP op			= 2;
	optional int32 retcode					= 3;

	// GD_DISCOUNT
	optional int32 goodsid					= 4;
	optional int64 target_roleid			= 5;
	optional int32 ticketid					= 6;
	optional int32 txnid					= 7;
	optional goods_discount_info goods_info	= 8;

	// GD_BUY
	//optional int32 goodsid					= 3;
	//optional int32 txnid					= 6;
	//optional goods_discount_info goods_info	= 8;
	optional int32 discount_price			= 9;
}

message ipt_bouquet_op
{
    optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_BOUQUET_OP ];
	optional BOUQUET_OP op					= 2;

	// BOUQUET_OP_SEND
	optional int32 index					= 3;
	optional int32 tid						= 4;
	optional int64 dst_roleid				= 5;
	optional bytes bless_words				= 6;
	optional int32 txnid					= 7;
}


message ipt_bouquet_op_re
{
    optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_BOUQUET_OP_RE ];
	optional BOUQUET_OP op					= 2;
	optional int32 retcode					= 3;

	// BOUQUET_OP_SEND
	optional int32 index					= 4;
	optional int32 tid						= 5;
	optional int64 dst_roleid				= 6;
	optional bytes bless_words				= 7;
	optional int32 txnid					= 8;
}

message ipt_corps_align_op
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CORPS_ALIGN_OP ];
	optional CORPS_ALIGN_OP op				= 2;
	optional int64 corps_id					= 3;
	optional int64 target_corps_id			= 4;
	optional int64 timestamp				= 5;
	optional int32 agree					= 6;
}

message ipt_corps_align_op_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_CORPS_ALIGN_OP_RE ];
	optional CORPS_ALIGN_OP op				= 2;
	optional int32 retcode 					= 3;
}

message lmfshop_db_data
{
	optional int32 activityid							= 1;
	repeated lmfshop_role_data roles_data				= 2;
	optional lmfshop_last_award_data last_award_data	= 3;
}

message ipt_lmfshop_op
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_LMFSHOP_OP ];
	optional LMFSHOP_OP op					= 2;
	optional int32 cost 					= 3;
	optional int32 total_cost 				= 4;
}

message ipt_invite_player_add_disney_card
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_INVITE_PLAYER_ADD_DISNEY_CARD ];
	repeated bytes convert_cards			= 2;
}


message ipt_skateboard_end_game_award
{
	optional INNER_PROTOCBUF_TYPE type      		= 1     [ default = IPT_SKATEBOARD_END_GAME_AWARD ];
	optional int32 itemid							= 2;
}

message  ipt_zspace_operator
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ZSPACE_OPERATOR ];
	optional ZSPACE_OP  op                  = 2; 
	optional int64      roleid              = 3;
	optional int32      wall_id             = 4;
	optional int32      floor_id            = 5;
}
message  ipt_zspace_operator_re
{
	optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ZSPACE_OPERATOR_RE];
	optional ZSPACE_OP  op                  = 2; 
	optional int32      ret_code            = 3;
}

enum IDIP_MODIFY_CONTENT_OP
{
	IMCO_MODIFY_NONE						= 0;
	IMCO_MODIFY_GANENXIANGCEJIYU			= 1;
	IMCO_MODIFY_FRIENDGROUPNAME				= 2;
	IMCO_MODIFY_MODIFY_GROUP_NAME			= 3;
	IMCO_MODIFY_HOMETOWN_WELCOME_TIP		= 4;
	IMCO_MODIFY_MAX							= 5;
}

message ipt_idip_modify_content
{
	optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_IDIP_MODIFY_CONTENT ];
	optional IDIP_MODIFY_CONTENT_OP op		= 2;
	optional int64 roleid					= 3;
	optional bytes content					= 4;
	optional int64 param					= 5;
}

message ipt_idip_modify_content_re
{
	optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_IDIP_MODIFY_CONTENT_RE ];
	optional IDIP_MODIFY_CONTENT_OP op		= 2;
	optional int64 roleid					= 3;
	optional bytes content					= 4;
	repeated int64 recv_roleids				= 5;
	optional int64 param					= 6;
}

message ipt_roam_community_op
{
	optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_ROAM_COMMUNITY_OP ];
	optional ROAM_COMMUNITY_OP op			= 2;

	// RCO_CREATE
	optional bytes name						= 3;
	optional int32 icon						= 4;
	optional bytes announce					= 5;
	optional int32 txnid					= 6;

	// hub转发-- 废弃
	optional bytes content					= 7;
	repeated int64 targetid					= 8;

	// RCO_GET_AWARD
	optional int32 index					= 9;
	optional int32 itemid					= 10;

	// RCO_EVENT
	optional int32 his_type					= 11;
	optional int64 param1					= 12;
	optional int64 param2					= 13;

	// RCO_VOTE
	optional int32 resource_num				= 14;
	optional int64 roleid					= 15;
	optional int64 communityid				= 16;
	optional int32 point_index				= 17;

	// RCO_ENHANCE_DEFENSE
	//optional int32 resource_num				= 14;
	//optional int64 roleid					= 15;
	//optional int32 point_index				= 17;

	// RCO_GM_SET_BELONG
	//optional int64 communityid				= 16;
	//optional int32 point_index				= 17;

	// RCO_GM_RESET_SEASON

	// RCO_TRY_ADD_RESOURCE
	//optional int64 param1					= 12;
	//optional int64 param2					= 13;
}

message ipt_roam_community_op_re
{
	optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_ROAM_COMMUNITY_OP_RE ];
	optional ROAM_COMMUNITY_OP op			= 2;
	optional int32 retcode					= 3;

	// RCO_CREATE
	optional bytes name						= 4;
	optional int32 icon						= 5;
	optional bytes announce					= 6;
	optional int32 txnid					= 7;
	optional roam_community_data data		= 8;
	optional int64 uniqueid					= 9;

	// RCO_VOTE
	optional int32 resource_num				= 10;
	optional int64 roleid					= 11;
	optional int64 communityid				= 12;
	optional int32 point_index				= 13;
	optional int32 total_vote 				= 14;

	// RCO_ENHANCE_DEFENSE
	//optional int32 resource_num				= 10;
	//optional int64 roleid					= 11;
	//optional int32 point_index				= 13;
	optional int32 total_defense			= 15;
}

message ipt_nats_pb
{
	optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_NATS_PB ];
	optional int32 msg_type					= 2;
	optional bytes content					= 3;
}

enum HUNDRED_CORPS_BATTLE_NOTIFY_TYPE
{
	HCBNT_MATCH_RESULT		= 1;
	HCBNT_BATTLE_BEGIN = 2;
	HCBNT_BATTLE_RESULT = 3;
    HCBNT_SYNC_BATTLE_INFO = 4;
	HCBNT_UPLOAD_ASSIAST_SCORE =5;
	HCBNT_SYNC_PERSONAL_SCORE = 6;
	HCBNT_SYNC_PERSONAL_TOP = 7;
	HCBNT_SYNC_CORPS_TOP    = 8;
	HCBNT_IDIP_SET_BATTLE_SCORE = 9;
	HCBNT_IDIP_SET_WIN_COUNT   = 10;
	HCBNT_IDIP_CLEAR_DATA      = 11;
	HCBNT_IDIP_CREATE_ORDER    = 12;
	HCBNT_IDIP_CREATE_ONE_ORDER= 13;
	HCBNT_IDIP_CLEAR_ORDER     = 14;
	HCBNT_IDIP_CLEAR_HUNDRED_BATTLE_INFO = 15;


}
message ipt_hundred_corps_battle_operator
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_HUNDRED_CORPS_BATTLE_OPERATOR ];

	optional HUNDRED_CORPS_BATTLE_NOTIFY_TYPE notify_type 	= 2;
	optional int32 battle_state		= 3;
	repeated corps_battle_info battle_infos 	= 4;
	repeated corps_battle_order battle_orders= 5;
	optional int32 creator_win			= 6;
	optional int32 battle_inst_tid			= 7;
	optional int32 battle_inst_id			= 8;
	optional int32 battle_type              = 10;
	optional int32 zoneid                  = 11;
	optional int64 roleid                   = 12;
	optional int64 corpsid                  = 13;
	message role_info
	{
		optional int64	 roleid   = 1;
		optional int64   value    = 2;
		optional int32   idphoto  = 3;
		optional bytes   name     = 4;
	} 
	 repeated role_info role_infos  = 14;
	optional int32 value      = 15;
	optional int64 corpsid_2  = 16;
	optional int32 manager_center_zone_id = 17;
}
enum HUNDRED_CORPS_BATTLE_DEBUG
{
	HCBD_MODIFY_TIME = 1;
	HCBD_CREATE_ORDER = 2;
	HCBD_CLEAR_DATA = 3;
	HCBD_CHEAT_TOP_DATA = 4;
}
message ipt_hundred_corps_battle_debug
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_HUNDRED_CORPS_BATTLE_DEBUG ];
	optional HUNDRED_CORPS_BATTLE_DEBUG  notify_type = 2;
	optional int32 param1             = 3;
	optional int64 roleid             = 4;
	optional bytes name         	  = 5;
	optional int32 win_count          = 6;
	optional int32 battle_score       = 7;
	optional int32 top_type            = 8;
	optional int32 manager_center_zone_id = 9;
}
message ipt_hundred_guess
{
    optional INNER_PROTOCBUF_TYPE   type        = 1 [ default = IPT_HUNDRED_GUESS ];
    optional int64                  roleid      = 2;
    optional int64                  guess       = 3;
    optional int32                  order_index = 4;
    optional HUNDRED_CORPS_BATTLE_STATE state       = 5;
    optional int32                  cost        = 6;
    optional int32                  txnid       = 7;
    optional int32                  retcode     = 8;
}

message ipt_center_battle_roam_create_finish_notify {
    optional INNER_PROTOCBUF_TYPE type		= 1     [ default = IPT_CENTER_BATTLE_ROAM_CREATE_FINISH_NOTIFY ];
	optional int32 inst_id 						= 2 [ default = 0 ];
	optional int32 center_battle_type			= 3 [ default = 0 ];
	optional int32 battle_type                  = 4 [ default = 0 ];
	optional int32 battle_id					= 5 [ default = 0 ];
	optional int32 param4						= 6 [ default = 0 ];
	optional int32 zoneid						= 7 [ default = 0 ];
	optional int32 ret							= 8 [ default = 0 ];
}

message ipt_send_center_battle_result {
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_SEND_CENTER_BATTLE_RESULT ];
	optional int32 category				= 2;
	optional int64 winner				= 3;
	optional int64 loser				= 4;
	optional int32 param				= 5;
	optional int32 param2				= 6;
	optional int32 instid				= 7;
}

message player_login_param
{
	optional int64 roam_communityid		= 1;
}

message ipt_player_weekly_update
{
	optional INNER_PROTOCBUF_TYPE type	= 1	[ default = IPT_PLAYER_WEEKLY_UPDATE ];
	required int32 param				= 2;
	required int32 param2				= 3;
}

message ipt_mount_space_operation
{
	optional INNER_PROTOCBUF_TYPE type		= 1	[ default = IPT_MOUNT_SPACE_OPERATION ];
	optional MOUNT_SPACE_OP op				= 2;
	optional int64 roleid					= 3;
}

message ipt_mount_space_operation_re
{
	optional INNER_PROTOCBUF_TYPE type		= 1	[ default = IPT_MOUNT_SPACE_OPERATION_RE ];
	optional MOUNT_SPACE_OP op				= 2;
	optional int32 ret_code					= 3;
}

enum ROLE_TRADE_REQ_TYPE
{
	RTRT_REQ_BRIEF_INFO	= 1; // 请求简要信息
	RTRT_REQ_DETAIL_INFO	= 2; // 请求详细信息
	RTRT_REQ_CHECK_SELL	= 3; // 请求检查出售条件
}

message ipt_role_trade_check
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_ROLE_TRADE_CHECK ];
	optional ROLE_TRADE_REQ_TYPE op_type 		= 2; // 请求类型
	optional int32 serial_id 			= 3; // 序列号
	optional int32 retcode				= 4 [ default = 0];
	optional int64 roleid				= 5; // 角色id
	optional string seller_account			= 6; // 出售方账号
	optional string buyer_account			= 7; // 购买方账号
	repeated int32 cw_list 				= 8; // 不通过原因
	optional bytes json_data			= 9; // json 结构marshal之后的值
	optional role_trade_detail_info detail_info	= 10; //详细信息用pb存, 单独处理
	optional int32 part 				= 11; //详细信息的part
}

message ipt_lottery_machine_add_money_notify
{
	optional INNER_PROTOCBUF_TYPE type		= 1 [ default = IPT_LOTTERY_MACHINE_ADD_MONEY_NOTIFY ];
	optional int32 draw_times				= 2 [ default = 0];
}

message ipt_cross_multi_pvp_msg
{
	optional INNER_PROTOCBUF_TYPE type		= 1	[ default = IPT_CROSS_MULTI_PVP_MSG ];
	optional CROSS_MULTI_PVP_OP op			= 2;
	optional int32 zoneid					= 3;
	optional int32 server_level				= 4;
}

message ipt_cross_multi_pvp_msg_re
{
	optional INNER_PROTOCBUF_TYPE type		= 1	[ default = IPT_CROSS_MULTI_PVP_MSG_RE ];
	optional CROSS_MULTI_PVP_OP op			= 2;
	optional int32 ret_code					= 3;
}

enum ARENA_CENTER_SINGLE_OP_TYPE
{
  ARCS_ACTIVTY_BEGIN 		= 1; //活动开启
  ARCS_SYNC_TOP_LIST 		= 2; //同步排行榜
  ARCS_ACTIVTY_END   		= 3;  //活动结束
  ARCS_ACTIVTY_INVITE_TICKET 	= 4; //活动产生的邀请函
  ARCS_ACTIVTY_ALL_REWARD 	= 5; //触发全服奖励
}

message ipt_arena_center_single_battle_notify
{
   optional INNER_PROTOCBUF_TYPE type      = 1     [ default = IPT_ARENA_CENTER_SINGLE_BATTLE_NOTIFY ];
   optional ARENA_CENTER_SINGLE_OP_TYPE ar_type = 2;
   optional int32 battle_type   	= 3;
   optional int32 prof			= 4;
   optional int32 level_section		= 5;
   optional int32 tpn_id        	= 6;
   optional int32 list_type   		= 7;
   repeated arena_search_info list  	= 8;  //排行榜及其奖励相关
}

message ipt_dressup_party_op
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_DRESSUP_PARTY_OP ];
	optional DRESSUP_PARTY_OP op					= 2;
	optional dressup_party_dress_info dpdi 			= 3;
	optional int32 scheme_score						= 4;
	optional int64 targetid							= 5;
	optional int32 dance_score						= 6;
	optional int32 txnid							= 7;
	optional int32 add_score						= 8;
	optional int32 speakid							= 9;
	optional int32 item_tid							= 10;
	optional int32 item_count						= 11;
	optional bytes item_name						= 12;
	optional int32 beauty_point						= 13;
	repeated int32 qte_seq							= 14;
	optional int32 anonymous						= 15;
}

message ipt_dressup_party_op_re
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_DRESSUP_PARTY_OP_RE ];
	optional DRESSUP_PARTY_OP op					= 2;
	optional int32 retcode							= 3;
	optional int64 targetid							= 4;
	optional int32 dance_score						= 5;
	optional int32 txnid							= 6;
	optional int32 item_tid							= 7;
	optional int32 item_count						= 8;
	optional int64 item_sender						= 9;
	optional int32 beauty_point						= 10;
	repeated int32 qte_seq							= 11;
}

message ipt_change_prof_notify
{
	optional INNER_PROTOCBUF_TYPE type				= 1 [ default = IPT_CHANGE_PROF_NOTIFY];
	optional int64 roleid						= 2;
	optional int32 old_prof						= 3; //老职业
	optional int32 new_prof						= 4; //新职业
}


message ipt_statue_get_players_data
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_STATUE_GET_PALYERS_DATA ];
	message player_data
	{
		optional int64 roleid		= 1;
		optional int32 dx_idx		= 2;
	}
	repeated player_data datas					= 2; //新职业
}

message ipt_statue_get_players_data_re
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_STATUE_GET_PALYERS_DATA_RE];
	message player_data
	{
		optional int64 roleid		= 1;
		optional int32 dx_idx		= 2;
		optional bytes status		= 3;
	}
	repeated player_data datas					= 2; //新职业
}

message ipt_pb_rpc
{
	optional INNER_PROTOCBUF_TYPE type = 1 [default = IPT_PB_RPC];
	enum PROTO_DIRECTION
	{
		GS_TO_DS        = 0;
		DS_TO_GS        = 1;
		//DS_TO_DS        = 2;//hub
	}
	enum SUB_MSG_TYPE
	{
		AI_CHAT = 1; //ai聊天
	}
	optional int32 sub_type      = 2;
	optional bytes msg_info      = 3;
	optional PROTO_DIRECTION dir = 4;
	optional int64 xid           = 5;
	optional int64 lineid        = 6;
	optional int32 retcode       = 7;
	optional int64 roleid        = 8 [ default = 0]; 
}

message ipt_ai_chat
{
	optional INNER_PROTOCBUF_TYPE type 	= 1 [default = IPT_AI_CHAT];
	optional int64 roleid				= 2;
	optional int32 retinue_id			= 3;
	optional bytes aibot_id				= 4;
	optional bytes aibot_token 			= 5;
	optional bytes chat_content 		= 6;
	optional bool is_positive 			= 7;
	optional int32 retcode 				= 8;
}

message ipt_new_auction_roam
{
	optional INNER_PROTOCBUF_TYPE type 			= 1 [default = IPT_NEW_AUCTION_ROAM];
	optional GAUCTION_ROAM_OP op				= 2;
	optional bytes auctionitem					= 3;
	optional int64 roleid						= 4;
	optional int64 auctionid					= 5;
	optional bytes role_name					= 6;
	optional bytes req							= 7;
}

message ipt_new_auction_roam_re
{
	optional INNER_PROTOCBUF_TYPE type 			= 1 [default = IPT_NEW_AUCTION_ROAM_RE];
	optional GAUCTION_ROAM_OP op				= 2;
	optional int32 ret_code						= 3;
	optional bytes auctionitem					= 4;
	optional int64 roleid						= 5;
	optional int64 auctionid					= 6;
	optional bytes role_name					= 7;
	optional bytes res							= 8;
}

message ipt_share_box
{
	optional INNER_PROTOCBUF_TYPE type 			= 1 [default = IPT_SHARE_BOX ];
	optional SHARE_BOX_OP op					= 2;
	optional int32 box_idx						= 3;
}

message ipt_share_box_re
{
	optional INNER_PROTOCBUF_TYPE type 			= 1 [default = IPT_SHARE_BOX_RE ];
	optional SHARE_BOX_OP op					= 2;
	optional int32 ret_code						= 3;
}

message ipt_magic_gathering_op
{
	optional INNER_PROTOCBUF_TYPE type 			= 1 [default = IPT_MAGIC_GATHERING_OP ];
	optional MAGIC_GATHERING_OP op  			= 2;
	optional magic_gathering_process_info info	= 3;
	optional int64 roleid						= 4;
}

message ipt_magic_gathering_op_re
{
	optional INNER_PROTOCBUF_TYPE type 			= 1 [default = IPT_MAGIC_GATHERING_OP_RE ];
	optional MAGIC_GATHERING_OP op  			= 2;
	optional int32 ret_code						= 3;
}

message all_zone_forbid_account
{
        optional bytes account_id = 1;
        optional int32 type       = 2;
        optional int32 createtime = 3;
        optional int32 time       = 4;
        optional bytes reason     = 5;
}

message ipt_broadcast_all_zone_forbid_account
{
    optional INNER_PROTOCBUF_TYPE type           = 1 [ default = IPT_BROADCAST_ALL_ZONE_FORBID_ACCOUNT ];
    optional all_zone_forbid_account forbid_info = 2;
}

message db_stellar_chart_data
{
	optional int32 select_spirit_id = 1;
}

message mail2originally_tlrmr_reward
{
	optional int64 role_id				= 1;
	optional int32 rank					= 2;
	repeated common_item_info item_list	= 3;
}

message ipt_send_mail2originally_server
{
	optional INNER_PROTOCBUF_TYPE type			= 1 [ default = IPT_SEND_MAIL2ORIGINALLY_SERVER ];
	optional int32 top_id						= 2;
	optional int32 mail_id						= 3;
	repeated mail2originally_tlrmr_reward rewards		= 4;;
}
