#ifndef _EFFECT_IMP_H
#define _EFFECT_IMP_H
#include <cstdio>
#include <math.h>
#include <vector>

#include "stateeffect.h"
#include "skill.h"
#include "exprule.h"
#include "object.h"
#include "prop_op.h"
#include "EC_CompGeo2D.h"


//FIXME: 可存盘效果只添加不修改, 万一有修改应当修改skill_version让所有存盘buff失效(用于开发时)

//TODO: 瞬时效果叠加, 比如1层100伤害, 2层200伤害这种?
//TODO: Save/Load对_times没有处理, 保存的叠加buff读取时会丢弃叠加层数


//可叠加效果的_data需一致, 因为经常*_times
//SkillWrapper初始化必须先于filter_man.Load
//FIXME: 不支持叠加的内容可以通过_times限制, 但怎么限制同时多个不同状态包呢? 靠策划? 恐怕还是得靠计数
//FIXME: 只允许出现在一个状态包中的效果: ESRatio/ESLimit/ESDisable/alias/morph/noPush/invisible/fuzzy/invinc/flitOnWater/rush/rushOnMount/leaveWeak/limitWeak/activate_sutra/ice/change_color2/change_size2

namespace WMSKILL
{

class empty: public StateEffect
{
public:
	empty()
	{
		_flag = FLAG_INSTANT;
	}
};

#define MAKE_INSTANT_EFFECT(type, name) \
class Modify##name: public StateEffect \
{ \
public: \
	Modify##name() { _flag = FLAG_INSTANT; } \
	void TakeEffect(object_interface & parent) \
	{ \
		Inc_##name(parent, (type)_data); \
		++_times; \
	} \
	void UndoEffect(object_interface & parent) \
	{ \
		for (short i = 0; i < _times; ++i) \
		{ \
			Dec_##name(parent, (type)_data); \
		} \
	} \
};

#define MAKE_ENH_EFFECT(type, name) \
class Modify##name: public StateEffect \
{ \
public: \
	Modify##name() { _flag = FLAG_DOSETUP; } \
	void TakeEffect(object_interface & parent) \
	{ \
		Inc_##name(parent, (type)_data); \
		++_times; \
	} \
	void UndoEffect(object_interface & parent) \
	{ \
		for (short i = 0; i < _times; ++i) \
		{ \
			Dec_##name(parent, (type)_data); \
		} \
	} \
	void OnChangePolyCount(object_interface & parent, int change_count) override \
	{ \
		if (change_count > 0) \
		{ \
			Inc_##name(parent, change_count * (type)_data); \
			_times += change_count; \
		} \
		else if (change_count < 0) \
		{ \
			int dec_count = std::min(-change_count, (int)_times); \
			if (dec_count > 0) \
			{ \
				Dec_##name(parent, dec_count * (type)_data); \
				_times -= dec_count; \
			} \
		} \
	} \
};

	MAKE_INSTANT_EFFECT(int, XP)//ModifyXP
	MAKE_INSTANT_EFFECT(int, pointDamRedu)//ModifypointDamRedu

	MAKE_ENH_EFFECT(int64_t, pointHP)//ModifypointHP
	MAKE_ENH_EFFECT(int, pointMP)//ModifypointMP

	MAKE_ENH_EFFECT(int, scaleHP)//ModifyscaleHP
	MAKE_ENH_EFFECT(int, scaleMP)//ModifyscaleMP
	MAKE_ENH_EFFECT(int, scaleMP2)//ModifyscaleMP2

	MAKE_ENH_EFFECT(int, pointPhyAtk)//ModifypointPhyAtk
	MAKE_ENH_EFFECT(int, scalePhyAtk)//ModifyscalePhyAtk

	MAKE_ENH_EFFECT(int, pointMagAtk)//ModifypointMagAtk
	MAKE_ENH_EFFECT(int, scaleMagAtk)//ModifyscaleMagAtk

	MAKE_ENH_EFFECT(int, pointPhyDef)//ModifypointPhyDef
	MAKE_ENH_EFFECT(int, scalePhyDef)//ModifyscalePhyDef

	MAKE_ENH_EFFECT(int, pointMagDef)//ModifypointMagDef
	MAKE_ENH_EFFECT(int, scaleMagDef)//ModifyscaleMagDef

	MAKE_ENH_EFFECT(int, pointElementalDmg1)//ModifypointElementalDmg1
	MAKE_ENH_EFFECT(int, pointElementalDef1)//ModifypointElementalDef1
	
	MAKE_ENH_EFFECT(int, pointElementalDmg2)//ModifypointElementalDmg2
	MAKE_ENH_EFFECT(int, pointElementalDef2)//ModifypointElementalDef2
	
	MAKE_ENH_EFFECT(int, pointElementalDmg3)//ModifypointElementalDmg3
	MAKE_ENH_EFFECT(int, pointElementalDef3)//ModifypointElementalDef3
	
	MAKE_ENH_EFFECT(int, pointElementalDmg4)//ModifypointElementalDmg4
	MAKE_ENH_EFFECT(int, pointElementalDef4)//ModifypointElementalDef4

	MAKE_ENH_EFFECT(float, pointRunSpeed)//ModifypointRunSpeed
	MAKE_ENH_EFFECT(float, scaleRunSpeed)//ModifyscaleRunSpeed

	MAKE_ENH_EFFECT(int, pointCritLevel)//ModifypointCritLevel
	MAKE_ENH_EFFECT(int, pointCritResLevel)//ModifypointCritResLevel
	MAKE_ENH_EFFECT(int, pointPierceLevel)//ModifypointPierceLevel
	MAKE_ENH_EFFECT(int, pointStun)//ModifypointStun
	MAKE_ENH_EFFECT(int, pointStunRes)//ModifypointStunRes

	MAKE_ENH_EFFECT(int, pointCritRatio)//ModifypointCritRatio
	MAKE_ENH_EFFECT(int, pointCritRatioRes)//ModifypointCritRatioRes

	MAKE_ENH_EFFECT(int, pointDamAdd)//ModifypointDamAdd
	MAKE_ENH_EFFECT(int, pointDamAdd2)//ModifypointDamAdd2
	MAKE_ENH_EFFECT(int, pointDamAdd3)//ModifypointDamAdd3
	MAKE_ENH_EFFECT(int, pointDamAdd4)//ModifypointDamAdd4
	MAKE_ENH_EFFECT(int, pointDamAdd5)//ModifypointDamAdd5
	MAKE_ENH_EFFECT(int, pointDamAdd6)//ModifypointDamAdd6
	MAKE_ENH_EFFECT(int, pointDamAdd7)//ModifypointDamAdd7
	MAKE_ENH_EFFECT(int, pointDamAdd8)//ModifypointDamAdd8
	MAKE_ENH_EFFECT(int, pointDamAdd9)//ModifypointDamAdd9
	MAKE_ENH_EFFECT(int, pointDamAdd10)//ModifypointDamAdd10
	MAKE_ENH_EFFECT(int, pointDamAdd11)//ModifypointDamAdd11
	MAKE_ENH_EFFECT(int, pointDamAdd13)//ModifypointDamAdd13
	MAKE_ENH_EFFECT(int, pointDamAdd12)//ModifypointDamAdd12
	MAKE_ENH_EFFECT(int, pointDamAdd14)//ModifypointDamAdd14
	MAKE_ENH_EFFECT(int, pointDamAdd15)//ModifypointDamAdd15
	MAKE_ENH_EFFECT(int, pointDamAdd16)//ModifypointDamAdd16
	MAKE_ENH_EFFECT(int, pointDamAdd17)//ModifypointDamAdd17
	MAKE_ENH_EFFECT(int, pointDamAdd18)//ModifypointDamAdd18
	MAKE_ENH_EFFECT(int, pointDamAdd19)//ModifypointDamAdd19
	MAKE_ENH_EFFECT(int, pointDamAdd20)//ModifypointDamAdd20

	MAKE_ENH_EFFECT(int, pointDamRedu2)//ModifypointDamRedu2
	MAKE_ENH_EFFECT(int, pointDamRedu3)//ModifypointDamRedu3
	MAKE_ENH_EFFECT(int, pointDamRedu4)//ModifypointDamRedu4
	MAKE_ENH_EFFECT(int, pointDamRedu5)//ModifypointDamRedu5
	MAKE_ENH_EFFECT(int, pointDamRedu6)//ModifypointDamRedu6
	MAKE_ENH_EFFECT(int, pointDamRedu7)//ModifypointDamRedu7
	MAKE_ENH_EFFECT(int, pointDamRedu8)//ModifypointDamRedu8
	MAKE_ENH_EFFECT(int, pointDamRedu9)//ModifypointDamRedu9
	MAKE_ENH_EFFECT(int, pointDamRedu10)//ModifypointDamRedu10
	MAKE_ENH_EFFECT(int, pointDamRedu11)//ModifypointDamRedu11
	MAKE_ENH_EFFECT(int, pointDamRedu13)//ModifypointDamRedu13
	MAKE_ENH_EFFECT(int, pointDamRedu12)//ModifypointDamRedu11
	MAKE_ENH_EFFECT(int, pointDamRedu14)//ModifypointDamRedu14
	MAKE_ENH_EFFECT(int, pointDamRedu15)//ModifypointDamRedu15
	MAKE_ENH_EFFECT(int, pointDamRedu16)//ModifypointDamRedu16
	MAKE_ENH_EFFECT(int, pointDamRedu17)//ModifypointDamRedu17
	MAKE_ENH_EFFECT(int, pointDamRedu18)//ModifypointDamRedu18
	MAKE_ENH_EFFECT(int, pointDamRedu19)//ModifypointDamRedu19
	MAKE_ENH_EFFECT(int, pointDamRedu20)//ModifypointDamRedu20
	MAKE_ENH_EFFECT(int, pointSkillCDAdd)//ModifypointSkillCDAdd

	MAKE_ENH_EFFECT(int, pointHit)//ModifypointHit
	MAKE_ENH_EFFECT(int, pointEvade)//ModifypointEvade

	MAKE_ENH_EFFECT(int, pointNpcDamAdd)//ModifypointNpcDamAdd
	MAKE_ENH_EFFECT(int, pointNpcDamRedu)//ModifypointNpcDamRedu
	MAKE_ENH_EFFECT(int, pointNpcDamRedu2)//ModifypointNpcDamRedu2

	MAKE_ENH_EFFECT(int, pointCDRedu)//ModifypointCDRedu
	MAKE_ENH_EFFECT(int, pointPierceInc)//ModifypointPierceInc

	MAKE_ENH_EFFECT(int, pointEvadeExtraRatio)//ModifypointEvadeExtraRatio
	MAKE_ENH_EFFECT(int, pointHitExtraRatio)//ModifypointHitExtraRatio
	MAKE_ENH_EFFECT(int, pointHealAdd)//ModifypointHealAdd
	MAKE_ENH_EFFECT(int, pointCrit)//ModifypointCrit
	MAKE_ENH_EFFECT(int, pointCritRes)//ModifypointCritRes
	MAKE_ENH_EFFECT(int, pointPsychokinesis)//ModifypointPsychokinesis
	MAKE_ENH_EFFECT(int, pointPierce)//ModifypointPierce
	MAKE_ENH_EFFECT(int, pointPsychokinesisLevel)//ModifypointPsychokinesisLevel
	MAKE_ENH_EFFECT(int, pointCDReduLevel)//ModifypointCDReduLevel

	MAKE_ENH_EFFECT(int, pointPhyDamAdd)//ModifypointPhyDamAdd
	MAKE_ENH_EFFECT(int, pointPhyDamRedu)//ModifypointPhyDamRedu
	MAKE_ENH_EFFECT(int, pointMagDamAdd)//ModifypointMagDamAdd
	MAKE_ENH_EFFECT(int, pointMagDamRedu)//ModifypointMagDamRedu
	MAKE_ENH_EFFECT(int, pointDeadlyAtk)//ModifypointDeadlyAtk
	MAKE_ENH_EFFECT(int, pointMP3Recover)//ModifypointMP3Recover

	MAKE_ENH_EFFECT(int, pointSlowRedu)//ModifypointSlowRedu
	MAKE_ENH_EFFECT(int, pointSealRedu)//ModifypointSealRedu
	MAKE_ENH_EFFECT(int, pointGroundRedu)//ModifypointGroundRedu
	MAKE_ENH_EFFECT(int, pointAirRedu)//ModifypointAirRedu
	MAKE_ENH_EFFECT(int, pointFrozenRedu)//ModifypointFrozenRedu
	MAKE_ENH_EFFECT(int, pointStoneRedu)//ModifypointStoneRedu
	MAKE_ENH_EFFECT(int, pointFearRedu)//ModifypointFearRedu
	MAKE_ENH_EFFECT(int, pointDeludedRedu)//ModifypointDeludedRedu

	MAKE_ENH_EFFECT(int, pointElementalDamRedu1)//ModifypointElementalDamRedu1
	MAKE_ENH_EFFECT(int, pointElementalDamRedu2)//ModifypointElementalDamRedu2
	MAKE_ENH_EFFECT(int, pointElementalDamRedu3)//ModifypointElementalDamRedu3
	MAKE_ENH_EFFECT(int, pointElementalDamRedu4)//ModifypointElementalDamRedu4

	MAKE_ENH_EFFECT(int, warm)//Modifywarm
	MAKE_ENH_EFFECT(int, cool)//Modifycool

	MAKE_ENH_EFFECT(int, pointMP4)//ModifypointMP4
	MAKE_ENH_EFFECT(int, scaleMP4)//ModifyscaleMP4
	MAKE_ENH_EFFECT(int, pointMP4Recover)//ModifypointMP4Recover
	MAKE_ENH_EFFECT(int, scaleMP4Recover)//ModifyscaleMP4Recover
	MAKE_ENH_EFFECT(int, MP4RecoverVariable)//ModifyMP4RecoverVariable
	MAKE_ENH_EFFECT(int, pointTimeFreezeRedu)//ModifypointTimeFreezeRedu
	MAKE_ENH_EFFECT(int, pointSilentRedu)//ModifypointSilentRedu

	MAKE_ENH_EFFECT(int, pointAttackSpeed)//ModifypointAttackSpeed
	MAKE_ENH_EFFECT(int, pointAttackRange)//ModifypointAttackRange
	MAKE_ENH_EFFECT(int, pointHitAddMP)//ModifypointHitAddMP
	MAKE_ENH_EFFECT(int, pointBeHitAddMP)//ModifypointBeHitAddMP

	MAKE_ENH_EFFECT(int, pointOppositeSexDamAdd)//ModifypointOppositeSexDamAdd
	MAKE_ENH_EFFECT(int, pointOppositeSexDamRedu)//ModifypointOppositeSexDamRedu
	MAKE_ENH_EFFECT(int, pointSameSexDamAdd)//ModifypointSameSexDamAdd
	MAKE_ENH_EFFECT(int, pointSameSexDamRedu)//ModifypointSameSexDamRedu

	MAKE_ENH_EFFECT(int, pointPierceReduLevel)//ModifypointPierceReduLevel
	MAKE_ENH_EFFECT(int, pointPierceRedu)//ModifypointPierceRedu

	MAKE_ENH_EFFECT(int, pointPVPLevel)//ModifypointPVPLevel
	MAKE_ENH_EFFECT(int, pointPVPResLevel)//ModifypointPVPResLevel
	MAKE_ENH_EFFECT(int, pointChaoticRedu)//ModifypointChaoticRedu

	MAKE_ENH_EFFECT(int, pointVersatilityRating)//ModifypointVersatilityRating
	MAKE_ENH_EFFECT(int, pointShieldRedu)//ModifypointShieldRedu

class ModifyHolyGhostMP: public StateEffect
{
private:
	void Modify(object_interface& parent, int value)
	{
		int old_mp = Get_HolyGhostMP(parent);
		if (value == 0)
		{
			return;
		}
		if (old_mp <= 0 && value < 0)
		{
			return;
		}
		if (old_mp >= 1000 && value > 0)
		{
			return;
		}
		int new_mp = old_mp + value;
		if (new_mp < 0)
		{
			new_mp = 0;
		}
		else if (new_mp > 1000)
		{
			new_mp = 1000;
		}
		parent.SetPropertyByIndex(GPROP_INDEX(HolyGhostMP), new_mp, new_mp, (int64_t)new_mp, (double)new_mp);
		parent.PropertyUpdateWithoutNotify();
	}
public:
	ModifyHolyGhostMP() { _flag = FLAG_INSTANT; }
	void TakeEffect(object_interface& parent)
	{
		Modify(parent, _data);
		_times ++;
	}
	void UndoEffect(object_interface& parent)
	{
		Modify(parent, -_times * (int)_data);
		_times = 0;
	}
};


	MAKE_ENH_EFFECT(int, pointSpecialDamAdd)//ModifypointSpecialDamAdd
	MAKE_ENH_EFFECT(int, pointSpecialDamRedu)//ModifypointSpecialDamRedu

//回复MP, 处于时空锁定状态时无效
//_data 外参 回复MP,可以为负
class IncMP_withoutTimeControl: public StateEffect
{
public:
	IncMP_withoutTimeControl()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!parent.IsTypeMaskExist(TYPEMASK_FAKE_REVIVE))
		{
			Inc_MP(parent, (int)_data);
		}
	}
};

MAKE_ENH_EFFECT(int, pvpDamAdd)//ModifypvpDamAdd
MAKE_ENH_EFFECT(int, pvpDamRedu)//ModifypvpDamRedu

MAKE_ENH_EFFECT(int, pvpDamAdd2)//ModifypvpDamAdd2
MAKE_ENH_EFFECT(int, pvpDamRedu2)//ModifypvpDamRedu2

	MAKE_ENH_EFFECT(int, pointDizzyAttenuated)//ModifypointDizzyAttenuated
	MAKE_ENH_EFFECT(int, pointSilentAttenuated)//ModifypointSilentAttenuated

//移动到子物体处
//内参 _param 子物体tid
//外参 _data 是否杀死子物体
class jump2SubobjPos : public StateEffect
{
public:
	jump2SubobjPos()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.Jump2SubobjPos(_param, _data > 1e-5);
	}
};

//进入疾行
class enterPelt : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (_times == 0)
		{
			parent.EnterPelt();
		}
		_times ++;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.LeavePelt();
		_times = 0;
	}
};

//让施法者对释放技能
//_data 外参 技能id
class castSkillSelf : public StateEffect
{
private:
	XID attacker;
public:
	castSkillSelf() { _flag = FLAG_INSTANT | FLAG_DOSETUP; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) 
	{
		if (skill->GetAttackerInfo())
		{
			attacker = skill->GetAttackerInfo()->attacker;
		}
	}
	void TakeEffect(object_interface& parent)
	{
		parent.CastSkillSelf(attacker, (int)_data);
	}
};

//我被给我加这个buff的人攻击时，让他对我释放一个技能
//_data 外参 技能id
class attackerCastSkill2Me : public StateEffect
{
private:
	XID _attacker;
	uint64_t _tk = 0;
public:
	attackerCastSkill2Me() { _flag = FLAG_DOSETUP; }
	filter_eventmask_t GetEventMask() {return filter::FM_ADJUST_DAMAGE;}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill->GetAttackerInfo())
		{
			_attacker = skill->GetAttackerInfo()->attacker;
		}
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (msg.skill_id == (int)_data)
		{
			return;
		}
		if (msg.attacker_info.attacker != _attacker)
		{
			return;
		}
		uint64_t now_tk = object_interface::GetTick();
		if (now_tk - _tk < 20)
		{
			return;
		}
		_tk = now_tk;
		parent.CastSkill2Me(_attacker, (int)_data);
	}
};

//我被给我加这个buff的人攻击时，让他对我释放一个技能
//_data 外参 技能id
//_data2 外参2 概率，千分之一
class attackerCastSkill2Me_rate : public attackerCastSkill2Me
{
public:
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (object_interface::Rand() * 1000 > _data2)
		{
			return;
		}
		attackerCastSkill2Me::AdjustDamage(parent, dmg, attacker, msg, attack_flag);
	}
};

//定时让施法者对目标释放技能
//_param 内参 触发间隔
//_data 外参 技能id
class persistCastSkill2Me : public StateEffect
{
private:
	XID attacker;
	uint64_t _next_tk = 0;
public:
	persistCastSkill2Me() { _flag = FLAG_DOSETUP; }
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) 
	{
		if (skill->GetAttackerInfo())
		{
			attacker = skill->GetAttackerInfo()->attacker;
		}
		_next_tk = object_interface::GetTick() + SECOND_TO_TICK(_param);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		uint64_t now_tk = object_interface::GetTick();
		if (_next_tk > now_tk)
		{
			return;//cd中
		}
		parent.CastSkill2Me(attacker, (int)_data);
		_next_tk = now_tk + SECOND_TO_TICK(_param);
	}
};

//根据位移扣血
//_data 外参 每米扣血量
//_param 内参 每秒距离上限，米
class hurtByMove : public StateEffect
{
private:
	bool _valid = false;
	attacker_info_t _atk_info;//真正的攻击者
	int _skill_id = 0;
	A3DVECTOR3 _last_pos;
private:
	void _CheckMove(object_interface& parent)
	{
		if (!_valid)
		{
			return;
		}
		const A3DVECTOR3& cur_pos = parent.GetPos();
		float dis = sqrtf(squared_distance(_last_pos, cur_pos));
		if (dis < 0.1f)
		{
			return;
		}
		if (dis > _param)
		{
			dis = _param;
		}
		int64_t dmg = _data * dis;
		parent.AsyncHurtSelf(_atk_info, dmg, _skill_id, false);
		_last_pos = cur_pos;
	}
public:
	hurtByMove()
	{
		_flag = FLAG_DOSETUP;
		memset(&_atk_info, 0, sizeof(_atk_info));
	}
	virtual filter_eventmask_t GetEventMask() override { return filter::FM_HEARTBEAT; }
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override 
	{
		_skill_id = skill->GetId();
		_last_pos = player.GetPos();
		if (skill->GetAttackerInfo())
		{
			_valid = true;
			_atk_info = *skill->GetAttackerInfo();
		}
	}
	virtual void Heartbeat(object_interface& parent, int tick) override //tick表示本次间隔几秒
	{
		_CheckMove(parent);
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		_CheckMove(parent);
	}
};

//指定姿态下伤害提高
//_param 内参 0=正常姿态，1=变身姿态
//_data 外参 系数, 千分之一
class addDmgByStance : public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		if (_param == 1)
		{
			if (!parent.IsTypeMaskExist(TYPEMASK_STANCE))
			{
				return;
			}
		}
		msg.attack_damage_add += _data;//skill_damage_rate有可能在目标身上重新计算
	}
};
//下n次技能伤害提高
//_param 内参 次数
//_data 外参 系数, 千分之一
class addDmgByTurn : public StateEffect
{
private:
	char _skill_stage = -1;
	char _num = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			msg.attack_damage_add += _data;//skill_damage_rate有可能在目标身上重新计算
			return;//双生体释放的技能直接生效，不消耗次数
		}
		if (!parent.IsManualDamageSkill(msg.skill_id))
		{
			return;
		}
		if (_skill_stage < 0 || _skill_stage >= msg.attack_stage)
		{
			_num ++;
		}
		_skill_stage = msg.attack_stage;
		if (_num <= _param)
		{
			msg.attack_damage_add += _data;//skill_damage_rate有可能在目标身上重新计算
			if (_num == _param && _skill_stage >= parent.GetSkillWrapper().GetPerformCount(msg.skill_id) - 1)
			{
				_filter->SetDelete();
			}
		}
		else if (_filter)
		{
			_filter->SetDelete();
		}
	}
};

//下n次技能附加暴击率
//_param 内参 次数
//_data 外参  附加暴击率，千分之一
class addCritByTurn : public StateEffect
{
private:
	char _skill_stage = -1;
	char _num = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			msg.skill_crit += _data;
			return;//双生体释放的技能直接生效，不消耗次数
		}
		if (!parent.IsManualDamageSkill(msg.skill_id))
		{
			return;
		}
		if (_skill_stage < 0 || _skill_stage >= msg.attack_stage)
		{
			_num ++;
		}
		_skill_stage = msg.attack_stage;
		if (_num <= _param)
		{
			msg.skill_crit += _data;
			if (_num == _param && _skill_stage >= parent.GetSkillWrapper().GetPerformCount(msg.skill_id) - 1)
			{
				_filter->SetDelete();
			}
		}
		else if (_filter)
		{
			_filter->SetDelete();
		}
	}
};

//本次次技能伤害提高
//_param 内参 触发间隔,秒
//_data 外参 系数,千分之一
//_data2 外参 概率,千分之一
class addDmgNow : public StateEffect
{
private:
	int _skill_id = 0;
	char _skill_stage = -1;
	uint64_t _last_tick = 0;
	bool _in_one_skill = false;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (_skill_id != msg.skill_id || _skill_stage >= msg.attack_stage)
		{
			_in_one_skill = false;
		}
		uint64_t now_tick = parent.GetTick();
		if (!_in_one_skill)
		{
			if (now_tick - _last_tick <= SECOND_TO_TICK(_param))
			{
				return;
			}
			if (object_interface::Rand() * 1000 > _data2)
			{
				return;
			}
			if (!parent.IsManualDamageSkill(msg.skill_id))
			{
				return;
			}
		}
		msg.attack_damage_add += _data;//skill_damage_rate有可能在目标身上重新计算
		_skill_stage = msg.attack_stage;
		_skill_id = msg.skill_id;
		_last_tick = now_tick;
		if (!_in_one_skill)
		{
			_in_one_skill = true;
		}
	}
};

//战斗状态 n秒没有造成伤害，释放技能
//_param 内参 时间
//_data 外参 技能id
class combatNotDoDmgSkill : public StateEffect
{
private:
	uint64_t _next_tk = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		uint64_t now_tk = object_interface::GetTick();
		if (_next_tk > 0 && _next_tk > now_tk)
		{
			return;//cd中
		}
		if (parent.GetCombatNotDoDmgTm() >= _param)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
			_next_tk = now_tk + SECOND_TO_TICK(_param);
		}
	}
};

//战斗状态 n秒没有受到伤害，释放技能
//_param 内参 时间
//_data 外参 技能id
class combatNotBeHurtSkill : public StateEffect
{
private:
	uint64_t _next_tk = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		uint64_t now_tk = object_interface::GetTick();
		if (_next_tk > 0 && _next_tk > now_tk)
		{
			return;//cd中
		}
		if (parent.GetCombatNotBeHurtTm() >= _param)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
			_next_tk = now_tk + SECOND_TO_TICK(_param);
		}
	}
};

//自己造成的伤害与挨打者的距离相关
//_param 内参 初始系数
//_data 外参 距离系数
class distanceAttack : public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		//此时无法判断距离，先写入数据，在挨打者收到消息时处理
		msg.distance_dmg_rate = _data;
		msg.distance_dmg_first = _param;
	}
};
class distanceAttack2 : public distanceAttack
{
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		//此时无法判断距离，先写入数据，在挨打者收到消息时处理
		msg.distance_dmg_rate2 = _data;
		msg.distance_dmg_first2 = _param;
	}
};


//自己受到的伤害与攻击者的距离相关
//_param 内参 最大距离
//_data 外参 系数
class distanceBeHurt: public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		float dis = sqrtf(horizontal_distance(parent.GetPos(), msg.attacker_info.pos));
		if (dis > _param)
		{
			dis = _param;
		}
		dmg *= 1 + dis * _data;
	}
};

//修改自己受到的伤害
//_data 外参 系数 千分之一
class adjustDamage: public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		dmg *= 1 + _data / 1000;
	}
};

//根据外参(_data)增加或减少mp3上限和mp3
class ModifyPointMaxMP3 : public StateEffect
{
private:
	void Modify(object_interface& parent, int value)
	{
		if (Get_MP3(parent) + value < 0)
		{
			parent.SetPropertyByIndex(GPROP_INDEX(MP3), 0, 0, 0, 0);//不能扣成负的
		}
		else
		{
			parent.ModifyPropertyByIndex(GPROP_INDEX(MP3), value, value, (int64_t)value, (double)value);
		}
		parent.ModifyPropertyByIndex(GPROP_INDEX(pointMP3), value, value, (int64_t)value, (double)value);
		parent.PropertyUpdateWithoutNotify();
	}
public:
	ModifyPointMaxMP3()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		Modify(parent, _data);
		_times ++;
	}
	void UndoEffect(object_interface& parent)
	{
		Modify(parent, -_times * (int)_data);
		_times = 0;
	}
};

//根据外参(_data)增加或减少mp3上限
class ModifyPointMaxMP3_Only : public StateEffect
{
private:
	void Modify(object_interface& parent, int value)
	{
		parent.ModifyPropertyByIndex(GPROP_INDEX(pointMP3), value, value, (int64_t)value, (double)value);
		parent.PropertyUpdateWithoutNotify();
	}
public:
	ModifyPointMaxMP3_Only() {}
	void TakeEffect(object_interface& parent)
	{
		Modify(parent, _data);
		_times ++;
	}
	void UndoEffect(object_interface& parent)
	{
		Modify(parent, -_times * _data);
		_times = 0;
	}
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			Modify(parent, change_count * _data);
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				Modify(parent, -dec_count * _data);
				_times -= dec_count;
			}
		}
	}
};

//根据外参(_data)增加或减少血上限和血量
class ModifyPointMaxHP : public StateEffect
{
private:
	void Modify(object_interface& parent, float value)
	{
		if (Get_HP(parent) + value < 1)
		{
			parent.SetPropertyByIndex(GPROP_INDEX(HP), 1, 1, 1, 1);
		}
		else
		{
			parent.ModifyPropertyByIndex(GPROP_INDEX(HP), value, value, (int64_t)value, (double)value);
		}
		parent.ModifyPropertyByIndex(GPROP_INDEX(pointHP), value, value, (int64_t)value, (double)value);
		parent.PropertyUpdateWithoutNotify();
	}
public:
	ModifyPointMaxHP()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		Modify(parent, _data);
		_times ++;
	}
	void UndoEffect(object_interface& parent)
	{
		Modify(parent, -_times * (int)_data);
		_times = 0;
	}
};

//根据外参(_data)千分比增加或减少血上限和血量
class ModifyScaleMaxHP : public StateEffect
{
private:
	void Modify(object_interface& parent, float value)
	{
		int64_t oldhp = Get_HP(parent);
		int64_t oldmax = Get_maxHP(parent);
		parent.ModifyPropertyByIndex(GPROP_INDEX(scaleHP), value, value, (int64_t)value, (double)value);
		parent.PropertyUpdateWithoutNotify();//更新最大生命
		int64_t hp = oldhp + Get_maxHP(parent) - oldmax;
		if (hp < 1)
		{
			hp = 1;
		}
		parent.SetPropertyByIndex(GPROP_INDEX(HP), hp, hp, (int64_t)hp, (double)hp);
		parent.PropertyUpdateWithoutNotify();
	}
public:
	ModifyScaleMaxHP()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		Modify(parent, _data);
		_times ++;
	}
	void UndoEffect(object_interface& parent)
	{
		Modify(parent, -_times * (int)_data);
		_times = 0;
	}
};

//不能把生命扣到1以下
class ModifyHP: public StateEffect
{
public:
	ModifyHP()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (Get_HP(parent) + (int64_t)_data < 1)
		{
			parent.SetPropertyByIndex(GPROP_INDEX(HP), 1, 1, 1, 1);
		}
		else
		{
			parent.ModifyPropertyByIndex(GPROP_INDEX(HP), (int)_data, _data, (int64_t)_data, (double)_data);
		}
		parent.PropertyUpdateAndNotify();
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		int val = _data * _times;
		if (Get_HP(parent) - val < 1)
		{
			parent.SetPropertyByIndex(GPROP_INDEX(HP), 1, 1, 1, 1);
		}
		else
		{
			parent.ModifyPropertyByIndex(GPROP_INDEX(HP), -val, -val, (int64_t)(-val), (double)(-val));
		}
		parent.PropertyUpdateAndNotify();
	}
};

class DamReduNoInstance: public StateEffect 
{ 
	public: 
		DamReduNoInstance() {} 
		void TakeEffect(object_interface & parent) 
		{ 
			Inc_pointDamRedu(parent, (int)_data); 
			++_times; 
		} 
		void UndoEffect(object_interface & parent) 
		{ 
			Dec_pointDamRedu(parent, (int)_data * _times); 
		} 
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			Inc_pointDamRedu(parent, change_count * _data);
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				Dec_pointDamRedu(parent, dec_count * _data);
				_times -= dec_count;
			}
		}
	}
};

//ModifyDamRedu效果是可以累加的，这个是不能累加的
class DamReduOnce: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (_times == 0)
		{
			Inc_pointDamRedu(parent, (int)_data);
			++_times;
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_times == 1)
		{
			Dec_pointDamRedu(parent, (int)_data);
			--_times;
		}
	}
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _times;
		return true;
	}

	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _times;
		return true;
	}
};

//ModifyscaleRunSpeed 不受减速抵抗影响，
//ModifyscaleRunSpeedWithRedu 会受减速效果影响
class ModifyscaleRunSpeedWithRedu: public StateEffect
{
public:
	ModifyscaleRunSpeedWithRedu()
	{
		_flag = FLAG_DOSETUP;
	}
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_SLOW;    //这时_data还未赋值
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_scaleRunSpeed(parent, (float)_data);
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		for (short i = 0; i < _times; ++i)
		{
			Dec_scaleRunSpeed(parent, (float)_data);
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			Inc_scaleRunSpeed(parent, change_count * _data);
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				Dec_scaleRunSpeed(parent, dec_count * _data);
				_times -= dec_count;
			}
		}
	}
};

class SetSpeedMin: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.SetSpeedMin(_data);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetSpeedMin(0);
	}
};

class SetSpeedMax: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.SetSpeedMax(_data);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetSpeedMax(0);
	}
};

///////////////////////////////////////// 控制效果开始 ////////////////////////////////////////////////
class smallControl: public StateEffect
{
protected:
	int _skill_id;
	unsigned char _skill_stage;

public:
	smallControl() : _skill_id(0), _skill_stage(0)
	{
		_flag = FLAG_DOSETUP;
	}

	bool CanSave()
	{
		return false;
	}
	/*
	filter_typemask_t GetTypeMask()
	{
		if (_param == CONTROLSTATE_SUSPEND)
			return TYPEMASK_SUSPEND;
		else if (_param == CONTROLSTATE_COLLAPSE)
			return TYPEMASK_COLLAPSE;
		else if (_param == CONTROLSTATE_TUMBLE)
			return TYPEMASK_TUMBLE;
		else
			return 0;
	}
	int GetDispelMask()
	{
		if (_param == CONTROLSTATE_SUSPEND)
			return TYPEMASK_SUSPEND_DISPEL;
		else if (_param == CONTROLSTATE_COLLAPSE)
			return TYPEMASK_COLLAPSE_DISPEL;
		else if (_param == CONTROLSTATE_TUMBLE)
			return TYPEMASK_TUMBLE_DISPEL;
		else
			return 0;
	}
	int GetImmuneMask()
	{
		if (_param == CONTROLSTATE_SUSPEND)
			return TYPEMASK_SUSPEND_IMMUNE;
		else if (_param == CONTROLSTATE_COLLAPSE)
			return TYPEMASK_COLLAPSE_IMMUNE;
		else if (_param == CONTROLSTATE_TUMBLE)
			return TYPEMASK_TUMBLE_IMMUNE;
		else
			return 0;
	}
	*/
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) 
	{
		_skill_id = skill->GetId();
		_skill_stage = skill->GetAttackSkillStage();
	}
};

//1 击晕
class dizzy: public smallControl
{
public:
	dizzy() {}
	filter_typemask_t GetTypeMask() { return TYPEMASK_DIZZ_STATE; }

	void TakeEffect(object_interface& parent)
	{
		//int time = _filter->GetTimeParam();
		//__SKPRINTF("dizzy::TakeEffect: type=%d,time=%d,weakness=%.2f;\n", _param, _filter->GetTimeParam(), parent.GetProperty().GetCurMP(2));
		parent.SetIdleSeal(true);
		//parent.BreakAction();//先去掉
		parent.SendDizzyMsg();
		//parent.StopQinggong(true);
		//parent.StopQinggong(false);
		//parent.SmallCtrlNotify(time, _param, _skill_id, _skill_stage, false);
		//parent.GetSkillWrapper().SetControlEvade(parent, _param, true, true, time);
		//parent.GetSkillWrapper().SetEffectWeakpoint(true);
		//parent.OnSmallControl();
	}
	void UndoEffect(object_interface& parent)
	{
		//__SKPRINTF("dizzy::UndoEffect;\n");
		//parent.GetSkillWrapper().SetEffectWeakpoint(false);
		//parent.GetSkillWrapper().SetControlEvade(parent, _param, false, true, 0);
		//parent.SmallCtrlNotify(0, 0, 0, 0, _filter->GetEndReason()==filter::ER_JIEYUN);
		parent.SetIdleSeal(false);
	}
};

//2 定身
class stop: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return (TYPEMASK_ROOT);
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_times) //状态叠加无意义, 不用Save
		{
			parent.SetRootSeal(true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetRootSeal(false);
	}
};

//3 封印, 沉默
class silent: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_SILENT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.SetSilentSeal(true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetSilentSeal(false);
	}
};

class silent_absolute: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_SILENT_ABSOLUTE;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.SetSilentAbsoluteSeal(true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetSilentAbsoluteSeal(false);
	}
};

//封印, 沉默, 针对龙裔技能
class silent_dragon: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.SetSilentDragonbornSeal(true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetSilentDragonbornSeal(false);
	}
};

class chain: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_CHAIN;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.UpdateObjectState2(gobject::STATE2_CHAIN, true, true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_CHAIN, false, true);
	}
};
class Chaotic : public StateEffect //混乱效果
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_CHAOTIC;
	}
	void TakeEffect(object_interface& parent)
	{
		do
		{
			if (_times)
			{
				break;
			}
			if (parent.IsPlayerClass())
			{
				break;
			}
			parent.SetRootSeal(true);
			parent.SetSilentSeal(true);
			parent.BreakAction();

		}
		while (0);

		parent.UpdateObjectState2(gobject::STATE2_CHAOTIC, true, true);
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_CHAOTIC, false, true);
		if (!parent.IsPlayerClass())
		{
			parent.SetRootSeal(false);
			parent.SetSilentSeal(false);
		}
	}
};
class Chaotic2 : public Chaotic //混乱效果
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_CHAOTIC2;
	}
	void TakeEffect(object_interface& parent)
	{
		do
		{
			if (_times)
			{
				break;
			}
			if (parent.IsPlayerClass())
			{
				break;
			}
			parent.SetRootSeal(true);
			parent.SetSilentSeal(true);
			parent.BreakAction();

		}
		while (0);

		parent.UpdateObjectState2(gobject::STATE2_CHAOTIC2, true, true);
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_CHAOTIC2, false, true);
		if (!parent.IsPlayerClass())
		{
			parent.SetRootSeal(false);
			parent.SetSilentSeal(false);
		}
	}
};

//4 驱散, 需要考虑个人buff
class dispelFilter: public StateEffect
{
	ruid_t from_rid = 0;
public:
	dispelFilter()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
	}

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) 
	{
		from_rid = skill->GetAttackerRid();
	}
	void TakeEffect(object_interface& parent)
	{
		parent.RemoveFilter((int)_data + FILTERID_BEGIN, from_rid);
	}
};

//驱散控制
class explode: public StateEffect
{
public:
	explode()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		//parent.ClearSpecFilter2(TYPEMASK_DISPEL_CTRL, 4/*filter::ER_JIEYUN*/);
	}
};

class AttackExplode : public StateEffect
{
	bool _active;
public:
	AttackExplode() : _active(false) {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		if (_active)
		{
			_active = false;
			parent.RemoveFilter((int)_data + FILTERID_BEGIN);
		}
	}
	void TakeEffect(object_interface& parent)
	{
		_active = true;
	}
};

//睡眠
class Sleep: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return (TYPEMASK_SLEEP);
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_times) //状态叠加无意义, 不用Save
		{
			parent.SetIdleSeal(true);
			parent.SendDizzyMsg();
			parent.AttachSleepBuff();
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetIdleSeal(false);
		parent.DeattachSleepBuff();
	}
};

//冰箱
class idle: public smallControl
{
	//int _skill_id; dup member
	bool _cleared;
public:
	idle(): _cleared(false) {}
	//filter_typemask_t GetTypeMask() { return (smallControl::GetTypeMask() | TYPEMASK_ROOT); }
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_OVERWHELMING;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		if (msg.skill_id == _skill_id)
		{
			return;
		}
		ClearEffect(parent);
	}

	//void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) 
	//{
	//	_skill_id = skill->GetId();
	//}

	void TakeEffect(object_interface& parent)
	{
		parent.SetRootSeal(true);
		parent.SetOverwhelmingMode(true);
		//parent.SetIdleSeal(true);
		//parent.SetIdleSeal(true); 需要错误提示
		parent.SealDiet(true);
		parent.ClearNextAction();
	}

	void ClearEffect(object_interface& parent)
	{
		if (_cleared)
		{
			return;
		}
		_cleared = true;
		parent.SetOverwhelmingMode(false);
		parent.SetRootSeal(false);
		//parent.SetIdleSeal(false);
	}

	void UndoEffect(object_interface& parent)
	{
		ClearEffect(parent);
		parent.SealDiet(false);
	}

	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _cleared;
		return true;
	}

	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _cleared;
		return true;
	}
};

class incWineExp: public StateEffect
{
public:
	incWineExp() { }

	void TakeEffect(object_interface& parent)
	{
	}
public:
};
//回血
class heal: public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	bool active = false;
	int _skill_id = 0;
public:
	heal()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	} //FIXME: 瞬时效果不叠加

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
	}

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_KILL_ENEMY;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!active) //
		{
			parent.BeCure(attacker, atk_info, _data, false, true, _skill_id);
			//parent.IncHP(_data, true);
			active = true;
		}
		/*
		float h = _data * (1 + 0.001f * parent.GetSkillWrapper().GetHealRatio());
		__PRINTF("heal add HP %f\n",h);
		parent.IncHP(h, true); //FIXME: 无关任何仇恨逻辑
		*/
	}
	//useless
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << active;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> active;
		return true;
	}

};

//按比例扣血
//外参_data：比率
class scaleDecHP : public StateEffect
{
	bool valid = false;
	attacker_info_t atk_info;//真正的攻击者
	int _skill_id = 0;
public:
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		if (skill->GetAttackerInfo())
		{
			valid = true;
			atk_info = *skill->GetAttackerInfo();
		}
	}
	scaleDecHP()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}
	void TakeEffect(object_interface& parent)
	{
		if (!valid)
		{
			return;
		}
		float cur_hp = parent.GetHP();
		float max_hp = parent.GetProperty().GetHPMax();
		if (cur_hp > 0 && _data > 0.f)
		{
			int64_t s = _data * max_hp;//变为当前值的百分比
			parent.AsyncHurtSelf(atk_info, s, _skill_id, false);
		}
	}
};

//按比率扣血, 无视无敌
//外参_data：比率
class scaleDecHP_IgnoreInvincible : public StateEffect
{
	attacker_info_t atk_info;//真正的攻击者
	int _skill_id = 0;
public:
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
		else
		{
			attack_msg attack;
			player.FillAttackMsg(player.GetID(), attack);
			atk_info = attack.attacker_info;
		}
	}
	scaleDecHP_IgnoreInvincible()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}
	void TakeEffect(object_interface& parent)
	{
		int64_t cur_hp = parent.GetHP();
		int64_t max_hp = parent.GetProperty().GetHPMax();
		if (cur_hp > 0 && _data > 0.f)
		{
			int64_t s = _data * max_hp;
			parent.AsyncHurtSelf(atk_info, s, _skill_id, true);
		}
	}
};

//把血量设置为某个比率
//_data 外参 比率
class SetSelfHP : public StateEffect
{
public:
	SetSelfHP()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.SetHP(parent.GetProperty().GetHPMax() * _data);
	}
};

//把血量设置为某个值
//_data 外参 值
class setHP : public StateEffect
{
public:
	setHP()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.SetHP(_data);
	}
};

//按比率扣血
class DecSelfHP : public StateEffect
{
protected:
	int _skill_id = 0;
public:
	DecSelfHP()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
	}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
	}
	void TakeEffect(object_interface& parent)
	{
		int64_t cur_hp = parent.GetHP();
		int64_t max_hp = parent.GetProperty().GetHPMax();
		if (cur_hp > 1 && _data > 0.f)
		{
			int64_t s = _data * max_hp;//变为当前值的百分比
			parent.AsyncHurt(parent.GetID(), s, _skill_id, true);
		}
	}
};

//按比率扣血，不会致死
class DecSelfHPNoDie : public StateEffect
{
protected:
	int _skill_id = 0;
	attacker_info_t atk_info;
public:
	DecSelfHPNoDie()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
	}
	void TakeEffect(object_interface& parent)
	{
		int64_t cur_hp = parent.GetHP();
		int64_t max_hp = parent.GetProperty().GetHPMax();
		if (cur_hp > 1 && _data > 0.f)
		{
			int64_t s = _data * max_hp;//变为当前值的百分比
			if (s >= cur_hp)
			{
				s = cur_hp - 1;
			}
			parent.BeHurt(parent.GetID(), atk_info, s, _skill_id);
		}
	}
};

//按数值扣血
class Hurt : public StateEffect
{
protected:
	bool has_atk = false;
	XID attacker;
	attacker_info_t atk_info;
	int _skill_id = 0;
public:
	Hurt()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			has_atk = true;
			atk_info = *skill->GetAttackerInfo();
		}
	}
	void TakeEffect(object_interface& parent)
	{
		if (has_atk)
		{
			//必定不是由BlessSelf触发，可以同步扣血
			parent.BeHurt(attacker, atk_info, _data, _skill_id);
		}
		else
		{
			parent.AsyncHurt(parent.GetID(), _data, _skill_id, false);
		}
	}
};

//按数值扣血(走战斗公式)
//_data 扣血量
//_param 内参1（低32位) 代表是否物理or魔法
class Hurt2 : public Hurt
{
protected:
	attack_msg _atk_msg = {};
public:
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			has_atk = true;
			atk_info = *skill->GetAttackerInfo();
		}
		if (atk_msg)
		{
			_atk_msg = *atk_msg;
		}
	}
	void TakeEffect(object_interface& parent) override
	{
		if (has_atk)
		{
			//必定不是由BlessSelf触发，可以同步扣血
			float final_damage = parent.GetSkillWrapper().SimpleAttackDamage(parent, _param & 0x0000FFFF, _data, _atk_msg);
			parent.BeHurt(attacker, atk_info, final_damage, _skill_id);
		}
		else
		{
			parent.AsyncHurt(parent.GetID(), _data, _skill_id, false);
		}
	}
};

//按数值扣血，不致死
//_data外参，多少血
class HurtNoDie : public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	int _skill_id = 0;
public:
	HurtNoDie()
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
	}
	void TakeEffect(object_interface& parent)
	{
		int64_t cur_hp = parent.GetHP();
		if ((int64_t)_data >= cur_hp)
		{
			_data = cur_hp - 1;
		}
		//不会死，可以同步扣血
		parent.BeHurt(attacker, atk_info, (int)_data, _skill_id);
	}
};

//7 持续治疗
class persistHeal: public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	int _elapse_time = 0;
	int _skill_id = 0;
public:
	persistHeal()
	{
		_flag = FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
	}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		++_times;
	}
	///////////////////////////////////////////////////////////////////////////////////////////
	//
	//	状态包持续时间和心跳次数的关系
	//
	//	(普通)状态包持续时间应该是1000的整数倍，小于1000的部分完全没有意义
	//	状态包持续时间并不严格，立刻加上，然后在心跳中检查是否结束，timeout>=cur_t时结束
	//	由于Heartbeat先于结束检查，所以无论持续时间如何，总会至少心跳1次
	//
	//	由于持续时间以时钟为准，而心跳以tick为准，所以持续时间和心跳次数的关系并不能确切对应
	//
	//	FIXME: 这是不是有点坑啊？除了能保证心跳1次，剩下的都没保证
	//	       而且1次因为"_elapse_time>interval"的存在，还不会真正生效
	//
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		int interval = (int)(_param * 0.001);
		_elapse_time += tick;
		if (_elapse_time > interval) //FIXME: 为何是">", 而不是">="?
		{
			float heal = _data * (1 + 0.001f * parent.GetSkillWrapper().GetHealRatio()) * _times;
			parent.BeCure(attacker, atk_info, heal, false, true, _skill_id);
			//parent.IncHP(heal, true);
			_elapse_time -= interval;
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				_times -= dec_count;
			}
		}
	}
public:
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _elapse_time;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _elapse_time;
		return true;
	}
};
class persistDecHPToDie: public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	int _elapse_time = 0;
	int _skill_id = 0;
public:
	persistDecHPToDie()
	{
		_flag = FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
	}

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}

	void TakeEffect(object_interface& parent)
	{
		++_times;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		int interval = (int)(_param * 0.001);
		_elapse_time += tick;
		if (_elapse_time >= interval) //FIXME: 为何是">", 而不是">="?
		{
			if (_data > 0.f)
			{
				parent.BeHurt(attacker, atk_info, _data * _times, _skill_id);
			}
			_elapse_time -= interval;
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				_times -= dec_count;
			}
		}
	}
	virtual bool Save(archive & ar)
	{
		StateEffect::Save(ar);
		ar << _elapse_time;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _elapse_time;
		return true;
	}
	void SettlePersist(object_interface& parent, float rate)
	{
		int interval = (int)(_param * 0.001);
		if (interval <= 0)
		{
			return;
		}
		int turn = _filter->GetTimeLeft() / interval;
		if (turn <= 0)
		{
			return;
		}
		if (_data > 0.f)
		{
			float s = _data * _times * turn * rate;
			parent.BeHurt(attacker, atk_info, s, _skill_id);
		}
	}
};


class persistDecHP: public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	int _elapse_time = 0;
	int _skill_id = 0;
public:
	persistDecHP()
	{
		_flag = FLAG_DOSETUP;
		memset(&atk_info, 0, sizeof(atk_info));
	}

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
	}

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}

	void TakeEffect(object_interface& parent)
	{
		++_times;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		int interval = (int)(_param * 0.001);
		_elapse_time += tick;
		if (_elapse_time > interval) //FIXME: 为何是">", 而不是">="?
		{
			float cur_hp = parent.GetHP();
			if (cur_hp > 1 && _data > 0.f)
			{
				float s = _data * _times < (cur_hp - 1) ? _data * _times : (cur_hp - 1); //变为当前值的百分比
				parent.BeHurt(attacker, atk_info, s, _skill_id);
			}
			_elapse_time -= interval;
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				_times -= dec_count;
			}
		}
	}
	virtual bool Save(archive & ar)
	{
		StateEffect::Save(ar);
		ar << _elapse_time;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _elapse_time;
		return true;
	}
	void SettlePersist(object_interface& parent, float rate)
	{
		int interval = (int)(_param * 0.001);
		if (interval <= 0)
		{
			return;
		}
		int turn = _filter->GetTimeLeft() / interval;
		if (turn <= 0)
		{
			return;
		}
		float cur_hp = parent.GetHP();
		if (cur_hp > 1 && _data > 0.f)
		{
			float s = _data * _times * turn * rate;
			s = s < (cur_hp - 1) ? s : (cur_hp - 1);
			parent.BeHurt(attacker, atk_info, s, _skill_id);
		}
	}
};

//结算持续效果，针对persistDecHP和persistDecHPToDie两个技能效果
//_param 内参 目标状态包的id
//_data 外参 倍率，千分之一
//_data2 外参2 是否驱散目标状态包
class settlePersist : public StateEffect
{
private:
	ruid_t _from_rid = 0;
public:
	settlePersist() { _flag = FLAG_INSTANT | FLAG_DOSETUP; }
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter, const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		_from_rid = skill->GetAttackerRid();
	}
	virtual void TakeEffect(object_interface& parent) override
	{
		parent.SettlePersist(_from_rid, _param + FILTERID_BEGIN, _data / 1000, _data2 > 0);
	}
};

class DotToDie: public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	int _elapse_time = 0;
	int _skill_id = 0;
	attack_msg _atk_msg = {};
public:
	DotToDie() {_flag=FLAG_DOSETUP;memset(&atk_info,0,sizeof(atk_info));}

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{			
			atk_info = *skill->GetAttackerInfo();
		}
		if (atk_msg)
		{
			_atk_msg = *atk_msg;
		}
	}

	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }

	void TakeEffect(object_interface& parent)
	{
		++_times;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		int interval = (int)((_param & 0xFFFF) * 0.001);
		_elapse_time += tick;
		if (_elapse_time >= interval) //FIXME: 为何是">", 而不是">="?
		{
			if (_data > 0.f)
			{
				float final_damage = parent.GetSkillWrapper().SimpleAttackDamage(parent, _param >> 32, _data * _times, _atk_msg);
				parent.BeHurt(attacker, atk_info, final_damage, _skill_id);
			}
			_elapse_time -= interval;
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count)
	{
		if (change_count > 0)
		{
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				_times -= dec_count;
			}
		}
	}
	virtual bool Save(archive & ar)
	{
		StateEffect::Save(ar);
		ar << _elapse_time;
		return true;
	}
	virtual bool Load(archive & ar)
	{
		StateEffect::Load(ar);
		ar >> _elapse_time;
		return true;
	}
};

class Dot: public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	int _elapse_time = 0;
	int _skill_id = 0;
	attack_msg _atk_msg = {};
public:
	Dot() {_flag=FLAG_DOSETUP;memset(&atk_info,0,sizeof(atk_info));}

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{			
			atk_info = *skill->GetAttackerInfo();
		}
		if (atk_msg)
		{
			_atk_msg = *atk_msg;
		}
	}

	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }

	void TakeEffect(object_interface& parent)
	{
		++_times;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		int interval = (int)((_param & 0xFFFF) * 0.001);
		_elapse_time += tick;
		if (_elapse_time > interval) //FIXME: 为何是">", 而不是">="?
		{
			float cur_hp = parent.GetHP();
			if (cur_hp > 1 && _data > 0.f)
			{
				float final_damage = parent.GetSkillWrapper().SimpleAttackDamage(parent, _param >> 32, _data * _times, _atk_msg);
				float s = final_damage < (cur_hp - 1) ? final_damage : (cur_hp - 1);//变为当前值的百分比
				parent.BeHurt(attacker, atk_info, s, _skill_id);
			}
			_elapse_time -= interval;
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count)
	{
		if (change_count > 0)
		{
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				_times -= dec_count;
			}
		}
	}

	virtual bool Save(archive & ar)
	{
		StateEffect::Save(ar);
		ar << _elapse_time;
		return true;
	}
	virtual bool Load(archive & ar)
	{
		StateEffect::Load(ar);
		ar >> _elapse_time;
		return true;
	}
	void SettlePersist(object_interface& parent, float rate)
	{
		int interval = (int)(_param * 0.001);
		if (interval <= 0)
		{
			return;
		}
		int turn = _filter->GetTimeLeft() / interval;
		if (turn <= 0)
		{
			return;
		}
		float cur_hp = parent.GetHP();
		if (cur_hp > 1 && _data > 0.f)
		{
			float s = _data *_times * turn * rate;
			s = s < (cur_hp - 1) ? s : (cur_hp - 1);
			parent.BeHurt(attacker, atk_info, s, _skill_id);
		}
	}
};

//8 免疫 ?
class immune: public StateEffect
{
public:
	filter_typemask_t GetTypeMask() { return (_param & 277815) == 277815 ? (TYPEMASK_SUPPER) : 0; }//免疫所有即为霸体
	void TakeEffect(object_interface& parent)
	{
		if (_param == 0)
		{
			return;
		}
		//	_param=TYPEMASK_COLLAPSE|TYPEMASK_ROOT;
		uint64_t param = (uint64_t)_param | TYPEMASK_TWINING;
		/*param |= TYPEMASK_CHAOTIC;
		param |= TYPEMASK_CHAOTIC2; 不能免疫*/
		parent.GetSkillWrapper().ModifyImmuneMask(param, true);
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		if (_param == 0)
		{
			return;
		}
		//	_param=TYPEMASK_COLLAPSE|TYPEMASK_ROOT;
		uint64_t param = (uint64_t)_param | TYPEMASK_TWINING;
		for (int i = _times; i > 0; --i)
		{
			parent.GetSkillWrapper().ModifyImmuneMask(param, false);
		}
	}
};

//9 无敌 ?
class invincible: public StateEffect
{
	bool _active;
public:
	invincible(): _active(false) { }
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_OVERWHELMING;
	}

	void TakeEffect(object_interface& parent)
	{
		if (!_active)
		{
			parent.SetOverwhelmingMode(true);
			_active = true;
		}
	}

	void UndoEffect(object_interface& parent)
	{
		if (_active)
		{
			parent.SetOverwhelmingMode(false);
			_active = false;
		}
	}
public:
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _active;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _active;
		return true;
	}
};

class invincible2: public invincible
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_OVERWHELMING_2;
	}
};

class removeSelf: public StateEffect
{
	bool _cleared;
public:
	removeSelf(): _cleared(false) {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG | filter::FM_SUBOBJ_ATTACK;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		ClearEffect(parent);
	}
	void TranslateSendSubObjAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{

		ClearEffect(parent);
	}

	void TakeEffect(object_interface& parent)
	{
	}

	void ClearEffect(object_interface& parent)
	{
		if (_cleared)
		{
			return;
		}
		_cleared = true;
		parent.RemoveFilter(_filter->GetID());
	}

	void UndoEffect(object_interface& parent)
	{
		ClearEffect(parent);
	}

	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _cleared;
		return true;
	}

	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _cleared;
		return true;
	}
};



class invincibleAtRevive: public StateEffect
{
	bool _cleared;
public:
	invincibleAtRevive(): _cleared(false) {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG | filter::FM_SUBOBJ_ATTACK;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		ClearEffect(parent);
		//	__PRINTF("invincibleAtRevive TranslateSendAttack====\n");
	}
	void TranslateSendSubObjAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{

		ClearEffect(parent);
		//__PRINTF("invincibleAtRevive TranslateSendAttack====\n");
	}

	void TakeEffect(object_interface& parent)
	{
		parent.SetOverwhelmingMode(true);
		//__PRINTF("invincibleAtRevive TakeEffect====\n");
	}

	void ClearEffect(object_interface& parent)
	{
		if (_cleared)
		{
			return;
		}
		_cleared = true;
		parent.SetOverwhelmingMode(false);
		parent.RemoveFilter(_filter->GetID());
	}

	void UndoEffect(object_interface& parent)
	{
		ClearEffect(parent);
	}

	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _cleared;
		return true;
	}

	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _cleared;
		return true;
	}
};
//10 modifyPhyAttack
class modifyPhyAttack : public StateEffect
{
	bool _active;
public:
	modifyPhyAttack(): _active(false) { }

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_active)
		{
			_active = true;
		}
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (_active)
		{
			if (dmg == 0 || msg.skill_damage_type != DAMAGE_PHY)
			{
				return;
			}
			dmg = dmg * ( 1 + _data);
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_active)
		{
			_active = false;
		}
	}
public:
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _active;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _active;
		return true;
	}
};

//11 resistDeadlyAttack
class resistDeadlyAttack: public StateEffect
{
	bool _active;
public:
	resistDeadlyAttack(): _active(false) { }

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (_active)
		{
			if (_data > 0)
			{
				if (--_data == 0)
				{
					_active = false;
				}
			}
			if (dmg > parent.GetHP())
			{
				//抵御一次致死攻击
				dmg = parent.GetHP() <= 1 ? 0 : parent.GetHP() - 1;
				attack_flag |= ATTACK_FLAG_STRONG; //FIXME: 可以再细分下
			}
		}
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_active)
		{
			_active = true;
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_active)
		{
			_active = false;
		}
	}
public:
	virtual bool Save(archive& ar)
	{
		/*	StateEffect::Save(ar);
			ar << _active << _data;*/
		return true;
	}
	virtual bool Load(archive& ar)
	{
		/*	StateEffect::Load(ar);
			ar >> _active >> _data;*/
		return true;
	}
};

//12 护盾，外参(_data)是可吸收的伤害总量
class damageShield: public StateEffect
{
protected:
	bool _active;
protected:
	virtual void OnAbsorbDamage(object_interface& parent) {}
public:
	damageShield(): _active(false) { _flag=FLAG_DOSETUP; }
	
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		int heal_add = Get_curHealAdd(player);
		_data = _data * (1 + (float)heal_add / 1000.f);
	}

	virtual filter_eventmask_t GetEventMask() override { return filter::FM_ADJUST_DAMAGE | filter::FM_REDUCE_SHIELD; }
	virtual filter_typemask_t GetTypeMask() override { return TYPEMASK_DAMAGE_SHIELD; }

	virtual void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag) override
	{
		if (_active)
		{
			if (_data > 0)
			{
				int64_t cur_dmg = (int64_t)dmg;
				if (_data >= cur_dmg)
				{
					_data -= cur_dmg;
					cur_dmg = 0;
					attack_flag |= ATTACK_FLAG_SHIELD;
				}
				else
				{
					cur_dmg -= _data;
					_data = 0;
					//盾暴了不发送吸收
				}
				dmg = cur_dmg;
				OnAbsorbDamage(parent);
			}
			if (_data <= 0)
			{
				UndoEffect(parent);
			}
		}
	}
	virtual void ReduceShieldInstant(object_interface& parent, int value) override
	{
		if (!_active) 
		{
			return;
		}
		//value 千分比
		_data *= 1.0f + (float)value / 1000.f;
		if (_data <= 0)
		{
			UndoEffect(parent);
		}
	}
	virtual void TakeEffect(object_interface& parent) override
	{
		if (!_active)
		{
			_active = true;
			int redu_value = Get_curShieldRedu(parent);
			_data *= 1.0f + (float)redu_value / 1000.f;
			if (_data <= 0)
			{
				UndoEffect(parent);
			}
		}
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_active)
		{
			_active = false;
			parent.RemoveFilter(_filter->GetID());
		}
	}
public:
	virtual bool Save(archive& ar) override
	{
		StateEffect::Save(ar);
		ar << _active << _param;
		return true;
	}
	virtual bool Load(archive& ar) override
	{
		StateEffect::Load(ar);
		ar >> _active >> _param;
		return true;
	}
};

//护盾，外参(_data)是可吸收的伤害总量, 内参(_param)是盾破时释放的技能
class damageShieldSkill: public damageShield
{
public:
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_active)
		{
			_active = false;
			parent.RemoveFilter(_filter->GetID());
			parent.GetSkillWrapper().BuffSkill_subobj(parent, _param);
		}
	}
};
//护盾，外参(_data)是可吸收的伤害总量, 内参(_param)是盾破时释放的技能 外参2是buff消失时释放的技能id不包括（护盾破裂)
class damageShieldSkill2: public damageShield
{
public:
	damageShieldSkill2(): damageShield(), _shieldState(false) {};
	virtual void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag) override
	{
		if (_active)
		{
			if (_data > 0)
			{
				int64_t cur_dmg = (int64_t)dmg;
				if (_data >= cur_dmg)
				{
					_data -= cur_dmg;
					cur_dmg = 0;
					attack_flag |= ATTACK_FLAG_SHIELD;
				}
				else
				{
					cur_dmg -= _data;
					_data = 0;
					//盾暴了不发送吸收
				}
				dmg = cur_dmg;
				OnAbsorbDamage(parent);
			}
			if (_data <= 0)
			{
				_shieldState = true;
				UndoEffect(parent);
				parent.GetSkillWrapper().BuffSkill_subobj(parent, _param);
			}
		}
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_active)
		{
			_active = false;
			parent.RemoveFilter(_filter->GetID());
			if (!_shieldState)
			{
				parent.GetSkillWrapper().BuffSkill_subobj(parent, _data2);
			}
		}
	}
	virtual bool Save(archive& ar) override
	{
		damageShield::Save(ar);
		ar << _shieldState;
		return true;
	}
	virtual bool Load(archive& ar) override
	{
		damageShield::Load(ar);
		ar >> _shieldState;
		return true;
	}
protected:
	bool _shieldState ; //护盾状态
};


//护盾，外参(_data)是可吸收的伤害总量, 内参(_param)是盾破时(用伙伴属性)释放的技能
class damageShieldRetinueSkill: public damageShield
{
	int skill_id = 0;
public:
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		damageShield::Setup(player, skill, filter, caster_pos, atk_msg);
		skill_id = skill->GetId();
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_active)
		{
			_active = false;
			parent.RemoveFilter(_filter->GetID());
			if (parent.CanRetinueActiveBuffSkill(skill_id))
			{
				parent.SetCastSkillFlag(RETINUE_CAST_SKILL);
				parent.SetRetinueActiveBuffSkill((int)_param);
				parent.GetSkillWrapper().BuffSkill_subobj(parent, _param);
				parent.SetCastSkillFlag(0);
				parent.SetRetinueActiveBuffSkill(0);
				parent.OnRetinueActiveBuffSkill(skill_id);
			}
		}
	}
};

//护盾，外参(_data)是可吸收的伤害总量, 内参(_param)是盾破时(用伙伴属性)释放的技能
class damageShieldRetinueSkillNoCd: public damageShield
{
	int skill_id = 0;
public:
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		damageShield::Setup(player, skill, filter, caster_pos, atk_msg);
		skill_id = skill->GetId();
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_active)
		{
			_active = false;
			parent.RemoveFilter(_filter->GetID());
			parent.SetCastSkillFlag(RETINUE_CAST_SKILL);
			parent.SetRetinueActiveBuffSkill((int)_param);
			parent.GetSkillWrapper().BuffSkill_subobj(parent, _param);
			parent.SetCastSkillFlag(0);
			parent.SetRetinueActiveBuffSkill(0);
		}
	}
};


//护盾，外参(_data)是可吸收的伤害总量, 内参(_param)是盾吸收伤害时释放的技能
class damageShieldSkill_Absorb: public damageShield
{
protected:
	virtual void OnAbsorbDamage(object_interface& parent) override
	{
		parent.GetSkillWrapper().BuffSkill(parent, _param);
	}
};

//护盾，外参(_data)是可吸收的伤害总量, 内参(_param)是盾破时(用龙裔属性)释放的技能
class damageShieldDragonbornSkill: public damageShield
{
	int skill_id = 0;
	int skill_level = 0;
public:
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		damageShield::Setup(player, skill, filter, caster_pos, atk_msg);
		skill_id = skill->GetId();
		skill_level = skill->GetLevel();
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_active)
		{
			_active = false;
			parent.RemoveFilter(_filter->GetID());
			if (parent.CanDragonbornActiveBuffSkill(skill_id))
			{
				parent.SetCastSkillFlag(DRAGONBORN_CAST_SKILL);
				//parent.SetRetinueActiveBuffSkill((int)_param);
				parent.GetSkillWrapper().BuffSkill_subobj_with_level(parent, _param, skill_level);
				parent.SetCastSkillFlag(0);
				//parent.SetRetinueActiveBuffSkill(0);
				//parent.OnRetinueActiveBuffSkill(skill_id);
			}
		}
	}
};

// 一段时间内累计受到百分比生命值伤害时触发技能,
// 外参1(_data):判定时间/毫秒; 外参2(_data2):损失生命值千分比; 内参(_param):技能id
class accumDamageSkill: public StateEffect
{
private:
	struct _tmp
	{
		int64_t now_tick	= 0;
		int64_t damage 		= 0;
	};
	std::list<_tmp> _damage_list = {};
	int _cd = 145000;
	int64_t _last_effect_tick = 0;
public:
	accumDamageSkill()
	{
		_flag = FLAG_INSTANT;
	}
	virtual filter_eventmask_t GetEventMask() override
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	virtual void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag) override
	{
		int64_t now_tick = object_interface::GetTick();
		if (_last_effect_tick != 0 && _last_effect_tick +  MILLISEC_TO_TICK(_cd) > now_tick)
		{
			return;
		}
		if (_data2 <= 0 || _data <= 0 || _param <= 0 || dmg < 0)
		{
			return;
		}

		int ticks = MILLISEC_TO_TICK(_data);
		unsigned int cur_dmg = (unsigned int)dmg;
		_tmp t;
		t.now_tick = now_tick;
		t.damage = cur_dmg;
		_damage_list.push_back(t);
		//计算最近ticks时间内的累计伤害
		auto iter_to_del = _damage_list.begin();
		int64_t accum_damage = 0;
		for (auto iter = _damage_list.begin(); iter != _damage_list.end(); iter++)
		{
			if (iter->now_tick + ticks < now_tick)
			{
				iter_to_del = iter;
			}
			else
			{
				accum_damage += iter->damage;
			}
		}
		//累计伤害超过最大生命值_data百分比触发
		if (parent.GetHPMax() * _data2 / 1000 <= accum_damage)
		{
			//parent.RemoveFilter(_filter->GetID());
			parent.GetSkillWrapper().BuffSkill_subobj(parent, _param);
			_last_effect_tick = now_tick;
		}
		//删掉过期的伤害
		_damage_list.erase(_damage_list.begin(), iter_to_del);
	}
public:
	virtual bool Save(archive& ar) override
	{
		StateEffect::Save(ar);
		ar << _data << _data2 << _param;
		return true;
	}
	virtual bool Load(archive& ar) override
	{
		StateEffect::Load(ar);
		ar >> _data >> _data2 >> _param;
		return true;
	}
};

//百分比吸收伤害回血护盾
//外参1(_data) : 可吸收的伤害总量, 0 为无上限
//外参2(_data2) : 可吸收的伤害百分比
//内参(_param) : 回血比例, 0 为不回复
class percentDamageShield: public StateEffect 
{
	bool _active;
	int _max_redu_value = 0;
	XID _attacker;
	attacker_info_t _atk_info;
	int _skill_id = 0;

public:
	percentDamageShield(): _active(false) { _flag=FLAG_DOSETUP; }

	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		_max_redu_value = _data2;
		_skill_id = skill->GetId();
		_attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{			
			_atk_info = *skill->GetAttackerInfo();
		}
	}
	virtual filter_eventmask_t GetEventMask() override { return filter::FM_BEFORE_ADJUST_DAMAGE; }
	virtual filter_typemask_t GetTypeMask() override { return TYPEMASK_DAMAGE_SHIELD; }

	virtual void BeforeAdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag) override
	{
		if (!_active)
		{
			return;
		}
		unsigned int cur_dmg = (unsigned int)dmg;
		// comment since always true
		//if (_data2 >= 0 || _data2 <= 1000)
		{
			unsigned int redu_dmg = cur_dmg * _data2 / 1000; //减伤
			if (_max_redu_value > 0) //有上限
			{
				if (_data > redu_dmg)
				{
					_data -= redu_dmg;
				}
				else
				{
					redu_dmg = _data;
					_data = 0;
				}
				if (_data <= 0)
				{
					//盾爆
					UndoEffect(parent);
				}
				//attack_flag |= ATTACK_FLAG_SHIELD;
			}
			dmg = cur_dmg - redu_dmg;
			if (redu_dmg > 0 && _param > 0 && _param <= 1000)
			{
				parent.BeCure(_attacker, _atk_info, redu_dmg * _param / 1000, false, true, _skill_id);
			}
		}
	}
	virtual void TakeEffect(object_interface& parent) override
	{
		if (!_active) 
		{
			_active = true;
		}
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_active)
		{
			_active = false;
			parent.RemoveFilter(_filter->GetID());
		}
	}
};

//吸收治疗，外参(_data)是可吸收的治疗总量
class costTreat: public StateEffect
{
public:
	virtual filter_eventmask_t GetEventMask() override { return filter::FM_TREAT; }
	virtual void BeTreat(float& value) override
	{
		if (_data > value)
		{
			_data -= value;
			value = 0;
		}
		else
		{
			value -= _data;
			_data = 0;
		}
		if (_data <= 1e-6 && _filter)
		{
			_filter->SetDelete();
		}
	}
};

//格挡, 减少受到的伤害
//_data 外参 每次减少多少伤害
//_data2 外参2 该效果生效几次，0=不限次数
class damageBlock : public StateEffect
{
public:
	damageBlock() {}
	virtual filter_eventmask_t GetEventMask() override
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	virtual void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag) override
	{
		if (_data2 != 0)
		{
			_data2--;
			if (_data2 <= 0 && _filter)
			{
				_filter->SetDelete();
			}
		}
	}
	virtual void TakeEffect(object_interface& parent) override
	{
		if (_times == 0)
		{
			parent.AddDamageBlock(_data);
		}
		++_times;
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		parent.AddDamageBlock(-_data);
	}
};

//13 dodgePointAttack
class dodgePointAttack : public StateEffect
{
	bool _active;
public:
	dodgePointAttack(): _active(false) { }

	void TakeEffect(object_interface& parent)
	{
		if (!_active)
		{
			parent.GetSkillWrapper().DodgePointAttack(true);
			_active = true;
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_active)
		{
			parent.GetSkillWrapper().DodgePointAttack(false);
			_active = false;
		}
	}
public:
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _active << _param;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _active >> _param;
		return true;
	}
};

//14
class BurnMP: public StateEffect
{
public:
	BurnMP()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_data <= 0)
		{
			return;
		}
		parent.DecMP(_data);
	}
};

//累积消耗一定mp后，释放技能
//_param 内参 多少mp
//_data 外参 技能id
class UseMP_CastSkill: public StateEffect
{
	int _count = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_USE_MP;
	}
	void UseMP(object_interface& parent, int value)
	{
		if (value < 0)
		{
			_count = 0;
		}
		else
		{
			_count += value;
			if (_count >= _param)
			{
				_count -= _param;
				parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
			}
		}
		__SKPRINTF("skill_effect UseMP, use=%d, count=%d,", value, _count);
	}
};

//持续回复MP
//_param 内参 触发间隔, 秒
//_data 外参 每次回复MP,可以为负
class PersistIncMP: public StateEffect
{
	int _tk = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		++_times;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		_tk += tick;
		if (_tk >= _param)
		{
			_tk -= _param;
			int value = _times * _data;
			if (value > 0)
			{
				parent.IncMP(value);
			}
			else if (value < 0)
			{
				parent.DecMP(-value);
			}
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				_times -= dec_count;
			}
		}
	}
};

class child_skill: public StateEffect
{
	//unsigned int _elapse_time;

public:
	child_skill()/*: _elapse_time(0)*/ {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG | filter::FM_SUBOBJ_ATTACK;
	}

	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		if (_param > 100)
		{
			return;
		}
		if (!SkillWrapper::TestCoolTime(parent, (int)_data))
		{
			return;
		}
		//孩子技能公共CD，千万别改！！！！会死循环否则
		if (!parent.TestCoolDown(74))
		{
			return;
		}
		parent.SetCoolDown(74, 2000);
		if (object_interface::Rand() * 100 <= _param)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
		}
	}
};


//15
class buff_skill: public StateEffect
{
	unsigned int _elapse_time;

public:
	buff_skill(): _elapse_time(0) {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG | filter::FM_HEARTBEAT;
	}

	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		if (_param > 100)
		{
			return;
		}
		if (!SkillWrapper::TestCoolTime(parent, (int)_data))
		{
			return;
		}
		if (object_interface::Rand() * 100 <= _param)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
		}
	}
	void Heartbeat(object_interface& parent, int tick)
	{
		if (_param < 1000)
		{
			return;
		}
		_elapse_time += tick;
		if (_elapse_time > _param / 1000)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
			_elapse_time -= _param / 1000;
		}
	}
	bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _elapse_time;
		return true;
	}
	bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _elapse_time;
		return true;
	}
};

//16
class morph: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.BeginTransform((int)_data);
		}
		_times++;
	}
	void UndoEffect(object_interface& parent)
	{
		if (parent.GetTransformTid() == (int)_data)
		{
			parent.EndTransform();
		}
	}
};

//让天赋/血统失效
class closeTalent: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.CloseTalent();
			//parent.UpdateObjectState2(gobject::STATE2_FORBID_PASSIVE_TALENT, true, false);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.OpenTalent();
		//parent.UpdateObjectState2(gobject::STATE2_FORBID_PASSIVE_TALENT, false, false);
	}
};

class noCtrl: public StateEffect //无视控制(优先级最高)
{
	void TakeEffect(object_interface& parent);
	void UndoEffect(object_interface& parent);
};

class immuneCtrl: public StateEffect //免控(可以被无视免控破掉)
{
	void TakeEffect(object_interface& parent);
	void UndoEffect(object_interface& parent);
};

class dispel: public StateEffect
{
public:
	dispel()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		parent.ClearSpecFilter2(_param, filter::ER_JIEYUN); //驱散一类，并非针对单个状态。
		//parent.ClearSpecFilter2(TYPEMASK_TWINING, filter::ER_JIEYUN); //驱散一类，并非针对单个状态。
	}
};

//延时一帧驱散
//受击解控时使用，避免受击时加控制和解控同一帧发生导致无法通知客户端控制结束
class delay_dispel: public StateEffect
{
public:
	delay_dispel()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		parent.ClearSpecFilter2_Delay(_param, filter::ER_JIEYUN); //驱散一类，并非针对单个状态。
	}
};

class dispel_64: public StateEffect
{
public:
	dispel_64()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		uint64_t mask = (uint64_t)_data | (uint64_t)_data2 << 32;
		parent.ClearSpecFilter2(mask, filter::ER_JIEYUN); //驱散一类，并非针对单个状态。
	}
};

class die: public StateEffect
{
public:
	die()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		parent.Die();
	}
};

//buff到期时死亡
//_param 内参，是否不受伤害
class dieDelay: public StateEffect
{
private:
	int _no_dot_num = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return _param > 0 ? filter::FM_ADJUST_DAMAGE : 0;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_param > 0)
		{
			parent.SetNoDot(1);//不受dot
			_no_dot_num ++;
		}
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetNoDot(-_no_dot_num);
		if (_filter->GetEndReason() == filter::ER_TIMEOUT)
		{
			parent.Die(0, true);
		}
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		dmg = 0;//不受直接伤害
	}
};

//设置死亡续航的概率
//_data 外参，概率 [0, 1]
class fakeReviveRate: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.SetFakeReviveRate(_data);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetFakeReviveRate(1);
	}
};

class AddFilterTimeOnAttack: public StateEffect
{
	int add_times;
public:
	AddFilterTimeOnAttack() : add_times(INT_MAX) { }

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		if (msg.skill_id <= 0 || add_times >= (int)nearbyint(_data) || msg.helpful)
		{
			return;
		}
		add_times ++;
		_filter->SetTimeParam(_filter->GetTimeLeft() * 1000 + _param);
		_filter->ResetTimeout();
	}

	void TakeEffect(object_interface& parent)
	{
		add_times = 0;
	}

	void UndoEffect(object_interface& parent)
	{
		add_times = INT_MAX;
	}

	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << add_times;
		return true;
	}

	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> add_times;
		return true;
	}
};

class AddFilterTimeOnBeat: public StateEffect
{
	int add_times;
public:
	AddFilterTimeOnBeat() : add_times(INT_MAX) { }

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (msg.skill_id <= 0 || add_times >= (int)nearbyint(_data) || msg.helpful)
		{
			return;
		}
		add_times ++;
		_filter->SetTimeParam(_filter->GetTimeLeft() * 1000 + _param);
		_filter->ResetTimeout();
	}

	void TakeEffect(object_interface& parent)
	{
		add_times = 0;
	}

	void UndoEffect(object_interface& parent)
	{
		add_times = INT_MAX;
	}

	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << add_times;
		return true;
	}

	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> add_times;
		return true;
	}
};

class NextSkillDamageRate : public StateEffect
{
	bool active;
	int skill_action_id;
public:
	NextSkillDamageRate() : active(false), skill_action_id(0) { }

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG | filter::FM_HEARTBEAT | filter::FM_SUBOBJ_ATTACK;
	}
	//这个是新加的,给子物体加的
	void TranslateSendSubObjAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.skill_id <= 0 || msg.action_id == 0)
		{
			return;
		}
		if (skill_action_id == 0)
		{
			skill_action_id = msg.action_id;
			if (_param == 0) //给自己加buff下次生效,给别人加当次生效
			{
				return;
			}
		}
		if (active && skill_action_id != 0 && skill_action_id != msg.action_id) //其他技能进来
		{
			return ;
		}
		skill_action_id = msg.action_id;
		ActiveEffect(msg);
	}

	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		if (msg.skill_id <= 0 || msg.action_id == 0 || (skill_action_id != 0 && skill_action_id == msg.action_id))
		{
			return;
		}
		if (skill_action_id == 0)
		{
			skill_action_id = msg.action_id;
			if (_param == 0) //给自己加buff下次生效,给别人加当次生效
			{
				return;
			}
		}
		else if (active) //已经用过一次了，不能再用了
		{
			parent.RemoveFilter(_filter->GetID());
			return;
		}
		skill_action_id = msg.action_id;
		ActiveEffect(msg);//使用这个buff效果
	}
	void ActiveEffect(attack_msg& msg)
	{
		//要乘以以前的倍率
		msg.skill_damage_rate *= _data;//伤害倍率
		active = true; //新的action挂载这个buff了
	}

	void Heartbeat(object_interface& parent, int tick)
	{
		if (active && (skill_action_id != 0) && (parent.GetActionID() != skill_action_id))
		{
			parent.RemoveFilter(_filter->GetID());
		}
	}

	void TakeEffect(object_interface& parent)//注意，这个第一个action执行完了就挂载了
	{
		//active = true;
	}

	void UndoEffect(object_interface& parent)
	{
		active = false;
		skill_action_id = 0;
	}

	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << active << skill_action_id;
		return true;
	}

	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> active >> skill_action_id;
		return true;
	}
};

class EscortSpeed : public StateEffect
{
public:
	EscortSpeed()
	{
		_flag = FLAG_INSTANT;
	}
	virtual void TakeEffect(object_interface& parent)
	{
		parent.ChangeEscortSpeed((int)_data);
	}
};

class instantSkill: public StateEffect
{
public:
	instantSkill()
	{
		_flag |= FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.GetSkillWrapper().BuffSkill(parent, (int)_param, _data > 1.0f ?  (unsigned int)_data : 1);
	}
};

class instantSkill_subobj: public StateEffect
{
public:
	instantSkill_subobj()
	{
		_flag |= FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.GetSkillWrapper().BuffSkill_subobj_with_level(parent, (int)_param, _data > 1.0f ?  (unsigned int)_data : 1);

	}
};

class instantSkill_retinue_subobj: public StateEffect
{
	int skill_id = 0;
public:
	instantSkill_retinue_subobj()
	{
		_flag |= FLAG_INSTANT | FLAG_DOSETUP;
	}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		skill_id = skill->GetId();
	}
	void TakeEffect(object_interface& parent)
	{
		if (parent.CanRetinueActiveBuffSkill(skill_id))
		{
			parent.SetCastSkillFlag(RETINUE_CAST_SKILL);
			parent.SetRetinueActiveBuffSkill((int)_param);
			parent.GetSkillWrapper().BuffSkill_subobj(parent, (int)_param);
			parent.SetCastSkillFlag(0);
			parent.SetRetinueActiveBuffSkill(0);
			parent.OnRetinueActiveBuffSkill(skill_id);
		}
	}
};

//内参  技能id
class instantSkill_detach: public StateEffect
{
public:
	void UndoEffect(object_interface& parent)
	{
		parent.GetSkillWrapper().BuffSkill(parent, (int)_param, _data > 1.0f ?  (unsigned int)_data : 1);
	}
};
class instantSkill_detach_subobject: public StateEffect
{
public:
	void UndoEffect(object_interface& parent)
	{
		parent.GetSkillWrapper().BuffSkill_subobj_with_level(parent, (int)_param, _data > 1.0f ?  (unsigned int)_data : 1);
	}
};

class fixeddamage : public StateEffect
{
	bool _active;
public:
	fixeddamage(): _active(false) { }

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_active)
		{
			_active = true;
		}
		parent.SetNoDot(1);
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (_active)
		{
			if (_data == 0.0f)
			{
				return;
			}
			if (msg.attacker_info.pet_master.IsValid())
			{
				dmg = 1.0f;
				return;
			}
			int skill_level = msg.skill_level < 70 ? 70 : msg.skill_level;
			float level_fixed = 5768.0f / (55.5 * skill_level + 69);
			dmg = (msg.skill_physic_damage + msg.skill_magic_damage) * msg.skill_damage_rate / _data * parent.GetHPMax() * 0.01f * level_fixed;
			if (dmg <= 0.0f)
			{
				dmg = 1.0f;
			}
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_active)
		{
			_active = false;
		}
		parent.SetNoDot(-1);
	}
public:
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _active;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _active;
		return true;
	}
};

//反弹伤害
class antidamage : public StateEffect
{
	bool _active = false;
	int64_t _max = 0;
	int _skill_id = 0;
public:
	antidamage()
	{
		_flag = FLAG_DOSETUP;
	}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
	}

	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_active)
		{
			_active = true;
			_max = (int64_t)(0.001f *_param* parent.GetHPMax());
		}
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (_active && msg.attacker_info.attacker.IsSelectable())
		{
			//wangyan & changsong
			int64_t feedback = _data;
			if (feedback > 0 && _max > 0)
			{
				_max -= feedback;
				if (_max < 0)
				{
					feedback += _max;
				}
				parent.AsyncHurt(msg.attacker_info.attacker, feedback, _skill_id, false);
			}
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_active)
		{
			_active = false;
			_max = _data;
		}
	}
public:
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _active << _max;
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _active >> _max;
		return true;
	}
};

class stance: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_STANCE;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.SetStance(_param);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetStance(0);
	}
};

class reset_cd: public StateEffect
{
public:
	reset_cd()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		if ((int)_data != 0 && (int)_data != -1)
		{
			parent.ClrCoolDown((int)_data + COOLINGID_BEGIN);
		}
	}
};

class set_cd: public StateEffect
{
public:
	set_cd()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		if ((int)_param != 0 && (int)_param != -1)
		{
			parent.SetCoolDown((int)_param + COOLINGID_BEGIN, (int)_data);
		}
	}
};

//在buff被驱散时设置cd，考虑冷却缩减
//_param 内参cdid
//_data 外参时间，毫秒
class set_cd_when_detach: public StateEffect
{
public:
	void UndoEffect(object_interface& parent)
	{
		parent.SetCoolDownByEffectimp((int)_param + COOLINGID_BEGIN, (int)_data);
	}
};
//设置cd 一定会受到冷却缩减的影响
class set_cd_reduce: public StateEffect
{
public:
	set_cd_reduce()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		if ((int)_param != 0 && (int)_param != -1)
		{
			parent.SetCoolDownByEffectimp((int)_param + COOLINGID_BEGIN, (int)_data);
		}
	}
};

//在buff失效或被驱散时，造成伤害
//_data 外参，伤害量
class hurt_when_detach : public StateEffect
{
private:
	bool _valid = false;
	attacker_info_t _atk_info;//真正的攻击者
	int _skill_id = 0;
public:
	hurt_when_detach()
	{
		_flag = FLAG_DOSETUP;
		memset(&_atk_info, 0, sizeof(_atk_info));
	}
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override 
	{
		_skill_id = skill->GetId();
		if (skill->GetAttackerInfo())
		{
			_valid = true;
			_atk_info = *skill->GetAttackerInfo();
		}
	}
	virtual void TakeEffect(object_interface& parent) override
	{
		++_times;
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		if (_valid)
		{
			parent.AsyncHurtSelf(_atk_info, _data * _times, _skill_id, false);
		}
	}
	void OnChangePolyCount(object_interface & parent, int change_count) override
	{
		if (change_count > 0)
		{
			_times += change_count;
		}
		else if (change_count < 0)
		{
			int dec_count = std::min(-change_count, (int)_times);
			if (dec_count > 0)
			{
				_times -= dec_count;
			}
		}
	}
};

class cost_cd: public StateEffect
{
public:
	cost_cd()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		if ((int)_param > 0 && (int)_data > 0)
		{
			parent.CostCoolDown((int)_param + COOLINGID_BEGIN, (int)_data);
		}
	}
};

class add_cd: public StateEffect
{
public:
	add_cd() { _flag = FLAG_INSTANT; }

	void TakeEffect(object_interface& parent)
	{
		if ((int)_param > 0 && (int)_data > 0)
		{
			parent.AddCoolDown((int)_param+COOLINGID_BEGIN, (int)_data);
		}
	}
};

//重置激活的技能方案中的技能的cd
class clr_active_skill_cd: public StateEffect
{
public:
	clr_active_skill_cd()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		parent.ClrActiveSkillCoolDown();
	}
};

//重置激活的技能方案中的技能和言灵技能的cd
class clr_manual_skill_cd: public StateEffect
{
public:
	clr_manual_skill_cd()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		parent.ClrManualSkillCoolDown();
	}
};

//锁定(暂停)技能方案里的技能和言灵技能的cd
//其实就是延长它们的到期时间
//_data 外参 毫秒
class pause_manual_skill_cd: public StateEffect
{
public:
	filter_typemask_t GetTypeMask() { return TYPEMASK_PAUSE_SKILL_CD; }
	pause_manual_skill_cd() { _flag = FLAG_INSTANT; }
	void TakeEffect(object_interface& parent)
	{
		int rate = Get_curTimeFreezeRedu(parent);
		if (object_interface::Rand() * 1000 >= rate)
		{
			parent.PauseManualSkillCoolDown(_data);
		}
	}
};

//还原pause_manual_skill_cd
class stop_pause_manual_skill_cd: public StateEffect
{
public:
	stop_pause_manual_skill_cd()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.StopPauseManualSkillCoolDown();
	}
};

//按比例缩减冷却时间，影响冷却缩减属性能影响的技能
//_data 外参 比例，千分之一
class scale_skill_cd: public StateEffect
{
public:
	scale_skill_cd()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		parent.ScaleSkillCoolDown(_data / 1000.0);
	}
};

//按比例缩减冷却时间, 影响除本技能外的其它技能方案中的技能和言灵技能
//_data 外参 比例，千分之一
//_param 内参 比例，最少降低多少毫秒
class scale_manual_skill_cd: public StateEffect
{
private:
	int _skill_id = 0;
public:
	scale_manual_skill_cd() { _flag = FLAG_INSTANT | FLAG_DOSETUP; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill)
		{
			_skill_id = skill->GetId();
		}
	}
	void TakeEffect(object_interface& parent)
	{
		parent.ScaleManualSkillCoolDown(_data / 1000.0, _param, _skill_id);
	}
};

//让该职业可由玩家升级的技能进入cd
//如果已经在cd,重置为满cd
//要考虑冷却缩减
class active_upgrade_skill_cd: public StateEffect
{
public:
	active_upgrade_skill_cd()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		parent.ActiveUpgradeSkillCoolDown();
	}
};

//延长或缩短buff_level(叠加规则)
//_param 内参 buffid
//_data 增加（减少的层数)正数增加 负数减少，减少的层数必定大于0
class modify_buff_level: public StateEffect
{
public:
	modify_buff_level()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		int level = _data;
		if ((int)_param > 0 && level != 0)
		{
			parent.ModifyFitler((int)_param + FILTERID_BEGIN, CTRL_MODIFY_BUFFLEVEL, &level, sizeof(level), 0);
		}
	}
};

//延长或缩短buff持续时间
//_param 内参 buffid
//_data 外参 时间，毫秒
class add_buff_tm: public StateEffect
{
public:
	add_buff_tm()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		int ms = _data;
		if ((int)_param > 0 && ms != 0)
		{
			parent.ModifyFitler((int)_param + FILTERID_BEGIN, CTRL_ADD_TM, &ms, sizeof(ms), 0);
		}
	}
};

//延长或缩短霸体持续时间
class add_supper_buff_tm : public StateEffect
{
public:
	add_supper_buff_tm()
	{
		_flag = FLAG_INSTANT;
	}

	void TakeEffect(object_interface& parent)
	{
		if (!parent.IsTypeMaskExist(TYPEMASK_SUPPER))
		{
			return;
		}

		int ms = -_data;
		if ( ms != 0)
		{
			parent.ModifyFitler(0, CTRL_ADD_TM, &ms, sizeof(ms), TYPEMASK_SUPPER);
		}
	}
};

class mp_full: public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}

	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (parent.GetMPMax() == parent.GetMP())
		{
			parent.SetMP(0);
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
		}
	}
};

//mp满触发一个技能
//_data 外参 技能id
//_param 内参 cd 秒
class mp_full_persist: public StateEffect
{
	uint64_t _next_tk = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}

	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (parent.GetMPMax() == parent.GetMP())
		{
			uint64_t now_tk = object_interface::GetTick();
			if (_next_tk > 0 && _next_tk > now_tk)
			{
				return;
			}
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
			_next_tk = now_tk + SECOND_TO_TICK(_param);
		}
	}
};

class mp2_full: public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}

	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (parent.GetMP2Max() == parent.GetMP2())
		{
			parent.SetMP2(0);
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
		}
	}
};

//在圆形区域内，把角色拉向或推离圆心
class drag_point: public StateEffect
{
	A3DVECTOR3 _caster_pos;//施法点
	float _speed = 0;//米/秒，正为推，负为拉
	float _dis_min = 0;//圆心半径，在圆心上不受力
	float _dis_max = 0;//圆的半径
	float _body_size = 0;//自己的bodysize+施法者的bodysize
	bool _is_valid = false;
private:
	bool NeedWaitCurAction(object_interface& parent)
	{
		int cur_skill = parent.GetCurSkillId();//正在释放的技能
		return cur_skill > 0 && SkillWrapper::CanCastInDrag(cur_skill);
	}
public:
	drag_point() { _flag = FLAG_DOSETUP;}
	filter_typemask_t GetTypeMask() { return TYPEMASK_DRAG; }
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		const attacker_info_t *att_info = skill->GetAttackerInfo();
		const Perform *const perform = skill->GetPerform();
		if (att_info == NULL || perform == NULL)
		{
			return;
		}
		_caster_pos = caster_pos;
		_speed = _data;//外参
		_body_size = player.GetBodySize() + att_info->body_size;
		_dis_min = _body_size + 0.5;
		_dis_max = perform->GetAffectRadius(*skill) + _body_size + 1;
		_is_valid = abs(_speed) > 0.1 && _dis_min < _dis_max;
		__SKPRINTF("drag_point setup valid=%d,speed=%f,dis_min=%f,dis_max=%f,pos=(%f,%f,%f),body_size=%f\n",
		           _is_valid, _speed, _dis_min, _dis_max, _caster_pos.x, _caster_pos.y, _caster_pos.z, _body_size);
	}
	void TakeEffect(object_interface& parent)
	{
		if (_is_valid)
		{
			if (!NeedWaitCurAction(parent))
			{
				parent.BreakAction();
			}
			parent.ClearNextAction();
			parent.SetDragSkillAddSpeed(abs(_speed));
			parent.SendClientDragPoint(_speed, _caster_pos, _dis_min - _body_size, _dis_max - _body_size, _body_size - parent.GetBodySize());
		}
		__SKPRINTF("drag_point take effect\n");
	}
	void UndoEffect(object_interface& parent)
	{
		if (_is_valid)
		{
			parent.SendClientDragRemove();
			parent.SetDragSkillAddSpeed(0);
		}
		__SKPRINTF("drag_point undo effect\n");
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (parent.IsPlayerClass() && !parent.IsObjectStateSet(gobject::STATE_LOST_CONNECT | gobject::STATE_IN_AUTO_COMBAT))
		{
			return;//在线的玩家由客户端模拟，服务器只处理离线玩家和npc
		}
		if (parent.IsImmuneMove())
		{
			return;
		}
		if (!_is_valid)
		{
			return;
		}
		if (NeedWaitCurAction(parent))
		{
			return;
		}

		A3DVECTOR3 direction = parent.GetPos() - _caster_pos;
		direction.y = 0;
		float dis = direction.Normalize();//与圆心的距离
		float old_dis = dis;
		if (dis - _dis_min < 0.1)
		{
			return;
		}
		if (dis > _dis_max)
		{
			return;
		}
		dis += _speed * tick;
		if (dis > _dis_max)
		{
			dis = _dis_max;
		}
		else if (dis < _dis_min)
		{
			dis = _dis_min;
		}
		if (abs(dis - old_dis) < 0.1)
		{
			return;
		}
		A3DVECTOR3 dest_pos = _caster_pos + dis * direction;//新位置
		unsigned short _;
		parent.SkillMove(dest_pos, _, false, true, true, CHECK_Y_DIFF_STRICT);
		parent.SendClientObjectMove(dest_pos, a3dvector_to_dir(direction), abs(_speed), false);

#ifdef SK_STD_OUTPUT
		A3DVECTOR3 tmp2 = parent.GetPos() - _caster_pos;
		__SKPRINTF("drag_point heartbeat tick=%d,pos=(%f,%f,%f),移动长度=%f,距离圆心=%f\n",
		           tick, dest_pos.x, dest_pos.y, dest_pos.z, dis - old_dis, tmp2.Magnitude());
#endif
	}
};

//在矩形区域内，把角色拉向或推离底边
class drag_line: public StateEffect
{
	A3DVECTOR3 _caster_pos;//施法点
	float _speed = 0;//米/秒，正为推，负为拉
	float _dis_min = 0;//与底边的距离小于该距离时不受力
	float _dis_max = 0;//与底边的距离大于该距离时不受力
	A3DVECTOR3 _dir;//施法方向
	float _width = 0;//矩形宽度的一半
	float _body_size = 0;//自己的bodysize+施法者的bodysize
	bool _is_valid = false;

	//生效区域的四个顶点
	A3DVECTOR3 _rect_pos1;
	A3DVECTOR3 _rect_pos2;
	A3DVECTOR3 _rect_pos3;
	A3DVECTOR3 _rect_pos4;

	geo2D::TLINESEG _line;//底边
private:
	bool NeedWaitCurAction(object_interface& parent)
	{
		int cur_skill = parent.GetCurSkillId();//正在释放的技能
		return cur_skill > 0 && SkillWrapper::CanCastInDrag(cur_skill);
	}
	float Multiply(A3DVECTOR3& p1, A3DVECTOR3& p2, A3DVECTOR3& p0)
	{
		return (p1.x - p0.x) * (p2.z - p0.z) - (p1.z - p0.z) * (p2.x - p0.x);
	}
	bool IsInRange(A3DVECTOR3& p)
	{
		return Multiply(p, _rect_pos1, _rect_pos2) * Multiply(p, _rect_pos4, _rect_pos3) < 0
		       && Multiply(p, _rect_pos4, _rect_pos1) * Multiply(p, _rect_pos3, _rect_pos2) < 0;
	}
public:
	drag_line() { _flag = FLAG_DOSETUP;}
	filter_typemask_t GetTypeMask() { return TYPEMASK_DRAG; }
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		const attacker_info_t *att_info = skill->GetAttackerInfo();
		const Perform *const perform = skill->GetPerform();
		if (att_info == NULL || perform == NULL)
		{
			return;
		}
		_caster_pos = caster_pos;
		_speed = _data;//外参
		_body_size = player.GetBodySize() + att_info->body_size;
		_dir = skill->GetAttackerInfo()->dir;
		_dis_min = _body_size + 0.5;
		_width = perform->GetAffectRadius(*skill) + _body_size;
		_dis_max = perform->GetAffectLength(*skill) + _body_size;
		_is_valid = abs(_speed) > 0.1 && _dis_min < _dis_max && _width > 0.1;

		if (_is_valid)
		{
			//计算生效范围
			A3DVECTOR3 d90(-_dir.z, 0, _dir.x);//逆时针旋转90度
			_rect_pos1 = _caster_pos + (_dir * _dis_min) + _width * d90;
			_rect_pos2 = _rect_pos1 + _dis_max * _dir;
			_rect_pos3 = _rect_pos2 - _width * 2 * d90;
			_rect_pos4 = _rect_pos3 - _dis_max * _dir;

			_line.s.x = _rect_pos1.x;
			_line.s.y = _rect_pos1.z;
			_line.e.x = _rect_pos4.x;
			_line.e.y = _rect_pos4.z;
		}

		__SKPRINTF("drag_line setup valid=%d,speed=%f,dis_min=%f,dis_max=%f,pos=(%f,%f,%f),wid=%f,dir=(%f,%f,%f),body_size=%f\n",
		           _is_valid, _speed, _dis_min, _dis_max, _caster_pos.x, _caster_pos.y, _caster_pos.z, _width, _dir.x, _dir.y, _dir.z, _body_size);
	}
	void TakeEffect(object_interface& parent)
	{
		if (_is_valid)
		{
			if (!NeedWaitCurAction(parent))
			{
				parent.BreakAction();
			}
			parent.ClearNextAction();
			parent.SetDragSkillAddSpeed(abs(_speed));
			int dir = a3dvector_to_dir(_speed > 0 ? _dir : -_dir);
			parent.SendClientDragLine(abs(_speed), _caster_pos, dir,
			                          _width - _body_size, _dis_min - _body_size, _dis_max - _body_size, _body_size - parent.GetBodySize());
		}
		__SKPRINTF("drag_line take effect\n");
	}
	void UndoEffect(object_interface& parent)
	{
		if (_is_valid)
		{
			parent.SendClientDragRemove();
			parent.SetDragSkillAddSpeed(0);
		}
		__SKPRINTF("drag_line undo effect\n");
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (parent.IsPlayerClass() && !parent.IsObjectStateSet(gobject::STATE_LOST_CONNECT | gobject::STATE_IN_AUTO_COMBAT))
		{
			return;//在线的玩家由客户端模拟，服务器只处理离线玩家和npc
		}
		if (parent.IsImmuneMove())
		{
			return;
		}
		if (!_is_valid)
		{
			return;
		}

		A3DVECTOR3 cur_pos = parent.GetPos();
		if (!IsInRange(cur_pos))
		{
			return;
		}
		if (NeedWaitCurAction(parent))
		{
			return;
		}

		APointF apoint(cur_pos.x, cur_pos.z);
		float dis_src = geo2D::ptoldist(apoint, _line);//与底边的距离
		float dis = dis_src + tick * _speed;//移动后的距离
		if (dis > _dis_max)
		{
			dis = _dis_max;
		}
		else if (dis < _dis_min)
		{
			dis = _dis_min;
		}
		dis -= dis_src;
		if (abs(dis) < 0.01)
		{
			return;
		}

		A3DVECTOR3 dest_pos = cur_pos + dis * _dir;//新位置
		unsigned short _;
		parent.SkillMove(dest_pos, _, false, true, true, CHECK_Y_DIFF_STRICT);
		parent.SendClientObjectMove(dest_pos, a3dvector_to_dir(_dir), _speed, false);

#ifdef SK_STD_OUTPUT
		APointF apoint2(dest_pos.x, dest_pos.z);
		float dis2 = geo2D::ptoldist(apoint2, _line);
		__SKPRINTF("drag_line heartbeat tick=%d,x=%f,y=%f,z=%f,dis=%f\n",
		           tick, dest_pos.x, dest_pos.y, dest_pos.z, dis2);
#endif

	}
};

//恐惧，掉头逃跑
class fear: public StateEffect
{
	A3DVECTOR3 _direction;
private:
	void DoMove(object_interface& parent)
	{
		if (parent.IsImmuneMove())
		{
			return;
		}
		if (_filter->GetTimeLeft() <= 1)
		{
			return;
		}
		parent.BreakAction();
		parent.ClearNextAction();

		A3DVECTOR3 cur_pos = parent.GetPos();
		float speed = parent.GetRunSpeed() * 0.5;
		A3DVECTOR3 dest_pos = cur_pos + _direction * speed;
		unsigned short _;
		parent.SkillMove(dest_pos, _, true, true, false, CHECK_Y_DIFF_STRICT);
		parent.SendClientObjectMove(dest_pos, a3dvector_to_dir(_direction), speed, true);
	}
public:
	fear() {}
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_FEAR | TYPEMASK_STUN;
	}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_FEAR, true, true);
		parent.SetDietSeal(true);
		parent.SetRootSeal(true);
		parent.SetTurnSeal(true);
		_direction = parent.GetDirection() * -1.f;
		DoMove(parent);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.BreakAction();
		parent.ClearNextAction();
		parent.SetDietSeal(false);
		parent.SetRootSeal(false);
		parent.SetTurnSeal(false);
		parent.UpdateObjectState2(gobject::STATE2_FEAR, false, true);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		DoMove(parent);
	}
};

//恐惧，掉头逃跑
class fearCanRedu: public fear
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_FEAR_CAN_REDU | TYPEMASK_STUN;
	}
};

//魅惑，不受控制的向施法者移动
//外参 _data 期望移动到施法者位置的时间，毫秒
class deludedCanRedu: public StateEffect
{
private:
	XID _target;
private:
	void DoMove(object_interface& parent)
	{
		if (!_target.IsValid())
		{
			return;
		}
		if (parent.IsImmuneMove())
		{
			return;
		}
		if (_filter->GetTimeLeft() <= 1)
		{
			return;
		}
		parent.BreakAction();
		parent.ClearNextAction();

		A3DVECTOR3 target_pos;
		int64_t target_hp = 0;
		int64_t target_max_hp = 0;
		if (!parent.CheckRangeAndGetInfo(_target, 200 * 200, target_pos, target_hp, target_max_hp))
		{
			return;
		}
		A3DVECTOR3 cur_pos = parent.GetPos();
		A3DVECTOR3 direction = target_pos - cur_pos;
		float dis = direction.Normalize();
		float speed = dis / std::max(_data * 0.001, 0.1);
		speed = std::min(speed, parent.GetRunSpeed());

		A3DVECTOR3 dest_pos = cur_pos + direction * speed;
		unsigned short _;
		parent.SkillMove(dest_pos, _, true, true, false, CHECK_Y_DIFF_STRICT);
		parent.SendClientObjectMove(dest_pos, a3dvector_to_dir(direction), speed, true);
	}
public:
	deludedCanRedu() {_flag = FLAG_DOSETUP;}
	filter_typemask_t GetTypeMask() { return TYPEMASK_DELUDED_CAN_REDU | TYPEMASK_STUN; }
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill->GetAttackerInfo())
		{
			_target = skill->GetAttackerInfo()->attacker;
		}
	}
	void TakeEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_DELUDED, true, true);
		parent.SetDietSeal(true);
		parent.SetRootSeal(true);
		parent.SetTurnSeal(true);
		DoMove(parent);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.BreakAction();
		parent.ClearNextAction();
		parent.SetDietSeal(false);
		parent.SetRootSeal(false);
		parent.SetTurnSeal(false);
		parent.UpdateObjectState2(gobject::STATE2_DELUDED, false, true);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		DoMove(parent);
	}
};

class add_mp: public StateEffect
{
public:
	add_mp()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_data <= 0)
		{
			return;
		}
		parent.IncMP(_data);
	}
};

//增加mp,支持负数， 但不会减到0以下
//_data 外参
class modify_mp: public StateEffect
{
public:
	modify_mp()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_data > 0)
		{
			parent.IncMP(_data);
		}
		else if (_data < 0)
		{
			parent.DecMP(-_data);
		}
	}
};

//扣mp,再给施法者加mp(按实际扣除量)
//_data 外参
class suck_mp: public StateEffect
{
private:
	XID _attacker;
public:
	suck_mp() { _flag = FLAG_INSTANT | FLAG_DOSETUP; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill->GetAttackerInfo())
		{
			_attacker = skill->GetAttackerInfo()->attacker;
		}
	}
	void TakeEffect(object_interface& parent)
	{
		if (_data > 0)
		{
			int m = parent.GetMP();
			parent.DecMP(_data);
			m -= parent.GetMP();
			if (m > 0)
			{
				parent.SendTargetAddMP(_attacker, m);
			}
		}
	}
};

//支持负数， 但不会减到0以下
class add_mp2: public StateEffect
{
public:
	add_mp2()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_data > 0)
		{
			parent.IncMP2(_data);
		}
		else if (_data < 0)
		{
			parent.DecMP2(-_data);
		}
	}
};

//支持负数， 但不会减到0以下
//外参
class add_mp3: public StateEffect
{
public:
	add_mp3()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_data > 0)
		{
			parent.IncMP3(_data);
		}
		else if (_data < 0)
		{
			parent.DecMP3(-_data);
		}
	}
};

//支持负数， 但不会减到0以下
//外参
class add_mp4: public StateEffect
{
public:
	add_mp4()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_data > 0)
		{
			parent.IncMP4(_data);
		}
		else if (_data < 0)
		{
			parent.DecMP4(-_data);
		}
	}
};

//连续攻击同一个目标，增加技能伤害和暴击率
//_param 内参 最大累加层数
//_data 外参 伤害提升千分比
//_data2 外参 暴击率提升千分比
class persistAttackSameTarget : public StateEffect
{
private:
	ruid_t _last_target = 0;
	char _num = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (!target.IsValid())
		{
			return;
		}
		if (_last_target == 0 || _last_target != target.id)
		{
			_last_target = target.id;
			_num = 0;
			parent.RemoveBuff(2231, 0);
			return;
		}
		if (_num < _param)
		{
			++_num;
		}
		msg.attack_damage_add += _num * _data;//skill_damage_rate有可能在目标身上重新计算
		msg.skill_crit += _num * _data2;
		if (_num >= 5 && !parent.HasBuff(2231))
		{
			parent.GetSkillWrapper().BuffSkill(parent, 4210);
		}
	}
};

//提高自走棋npc的mp恢复速度
//_data 外参 提升千分比
class chessMPRecover: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.AddChessMPRecover(_data);
		_times++;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.AddChessMPRecover(-_data * _times);
	}
};

class invisible: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (_times == 0)
		{
			parent.UpdateObjectState(gobject::STATE_INVISIBLE, true, true);
		}
		_times++;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState(gobject::STATE_INVISIBLE, false, true);
	}
};

//隐身，不可被选中
class invisibleNoChoose: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_INVISIBLE_NO_CHOOSE | TYPEMASK_SKILL_DISPEL;
	}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_SWITCH_MAP_DISP;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_times == 0)
		{
			parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE, true, true);
		}
		_times++;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE, false, true);

		if (_filter && _filter->GetEndReason() == filter::ER_SKILL_START)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
		}
	}
};

//隐身不可选中，并且免疫247控制状态
class invisibleNoChoose_immune : public invisibleNoChoose
{
public:
	filter_typemask_t GetImmuneMask()
	{
		return 247;
	}
};

//自己的invisibleNoChoose失效
class invisibleNoChooseInvalid: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (_times == 0)
		{
			parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE_INVALID, true, true);
		}
		_times++;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE_INVALID, false, true);
	}
};

//无视别人的invisibleNoChoose
class seeNoChoose: public StateEffect
{
	/*public:
		void TakeEffect(object_interface& parent)
		{
			if (_times == 0)
			{
				parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE_INVALID, true, true);
			}
			_times++;
		}
		void UndoEffect(object_interface& parent)
		{
			parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE_INVALID, false, true);
		}*/
};

//不可被选中(不隐身)
//_data 外参 因为释放技能被驱散时释放的技能id
class noChoose: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_SKILL_DISPEL;
	}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_SWITCH_MAP_DISP;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_times == 0)
		{
			parent.UpdateObjectState2(gobject::STATE2_NO_CHOOSE, true, true);
		}
		_times++;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_NO_CHOOSE, false, true);

		if (_filter && _filter->GetEndReason() == filter::ER_SKILL_START)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
		}
	}
};

//烟雾效果
//_data 外参 烟雾半径，米
class fog: public StateEffect
{
private:
	A3DVECTOR3 _center_pos;
	time_t _attack_tm = 0;
private:
	void AddFog(object_interface& parent)//加上隐身
	{
		parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE, true, true);
	}
	void RemoveFog(object_interface& parent)//清掉隐身
	{
		parent.UpdateObjectState(gobject::STATE_INVISIBLE_NO_CHOOSE, false, true);
	}
public:
	fog() {_flag = FLAG_DOSETUP;}
	filter_eventmask_t GetEventMask() {return filter::FM_SWITCH_MAP_DISP | filter::FM_TRANS_SEND_MSG | filter::FM_HEARTBEAT | filter::FM_MOVE;}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_center_pos = caster_pos;
		_data *= _data;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (!parent.IsManualDamageSkill(msg.skill_id))
		{
			return;
		}
		RemoveFog(parent);
		_attack_tm = object_interface::GetSysTime();
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		bool in_range = squared_distance(parent.GetPos(), _center_pos) <= _data;
		bool timeout = object_interface::GetSysTime() - _attack_tm >= 1;
		if (in_range && timeout)
		{
			AddFog(parent);
		}
		else if (!in_range)//防止不通过move到达烟雾外，比如瞬移
		{
			RemoveFog(parent);
		}
	}
	void Move(object_interface& parent)
	{
		bool in_range = squared_distance(parent.GetPos(), _center_pos) <= _data;
		in_range ? AddFog(parent) : RemoveFog(parent);
	}
	void TakeEffect(object_interface& parent)
	{
		AddFog(parent);
	}
	void UndoEffect(object_interface& parent)
	{
		RemoveFog(parent);
	}
};

// 返控
// 内参: _param技能id
// 外参: _data触发概率
class unControl : public StateEffect
{
public:
	unControl()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.GetSkillWrapper().SetUnControlRate(_data);
		parent.GetSkillWrapper().SetUnControlSkill(_param);
	}

	void UndoEffect(object_interface& parent)
	{
		parent.GetSkillWrapper().SetUnControlRate(0);
		parent.GetSkillWrapper().SetUnControlSkill(0);
	}
};



// 延迟爆炸
// 内参_param承受的伤害次数
// 外参_data反馈的技能
class DelayBoomSkill : public StateEffect
{
protected:
	XID _target; //反馈目标
	int total = 0;

public:
	DelayBoomSkill()
	{
		_flag = FLAG_DOSETUP;
	}

	filter_eventmask_t GetEventMask() {return filter::FM_ADJUST_DAMAGE;}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill->GetAttackerInfo())
		{
			_target = skill->GetAttackerInfo()->attacker;
		}
		player.SetDelayBoomProess(_param);
	}

	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		if (_target != msg.attacker_info.attacker)
		{
			return;
		}

		_param -= 1;
		if (_param <= 0)
		{
			parent.CastSkill2Me(_target, _data);
			_filter->SetDelete();
		}

		parent.SetDelayBoomProess(_param == 0 ? -1 : _param);
	}

	void UndoEffect(object_interface& parent)
	{
		parent.SetDelayBoomProess(0);
	}
};

// 延迟攻击
// 内参_param反馈的技能
class DelayAttackSkill : public StateEffect
{
protected:
	XID _target; //反馈目标
	int total = 0;

public:
	DelayAttackSkill()
	{
		_flag = FLAG_DOSETUP;
	}

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill->GetAttackerInfo())
		{
			_target = skill->GetAttackerInfo()->attacker;
		}
		player.SetDelayAttackProess(object_interface::GetSysTime());
	}

	void UndoEffect(object_interface& parent)
	{
		parent.CastSkill2Me(_target, (int)_param);
		parent.SetDelayAttackProess(0);
	}
};

// 禁用言灵
class kotodama_silent: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.SetKotodamaSeal(true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetKotodamaSeal(false);
	}
};

//向对我造成伤害的目标反馈技能
//内参_param是对单个目标的间隔，毫秒
//外参_data是反馈的技能
class feedbackSkill: public StateEffect
{
protected:
	XID _target;//反馈目标
	int _interval;//单个目标的反馈间隔
	std::multimap<uint64_t, ruid_t> _tm2uid;//<反馈时间，uid>
	std::set<ruid_t> _uidlist;
public:
	feedbackSkill() : _interval(0)
	{
		_flag = FLAG_INSTANT | FLAG_DOSETUP;
	}
	filter_eventmask_t GetEventMask() {return filter::FM_ADJUST_DAMAGE;}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_interval = MILLISEC_TO_TICK(_param);
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		_target = msg.attacker_info.attacker;
		//__SKPRINTF("feedbackSkill AdjustDamage tar=%ld\n", _target.id);
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_target.IsValid() || _target.IsSubobject())
		{
			return;
		}

		//删除过期的记录
		uint64_t now = object_interface::GetTick();
		while (!_tm2uid.empty())
		{
			auto it = _tm2uid.begin();
			if (now - it->first < _interval)
			{
				break;
			}
			_uidlist.erase(it->second);
			_tm2uid.erase(it);
		}

		ruid_t uid = _target.id;
		if (_uidlist.find(uid) != _uidlist.end())
		{
			return;//仍存的记录必定没过期（还处于反馈间隔）
		}

		parent.GetSkillWrapper().BuffSkill_target(parent, (int)_data, 1, _target);
		_target.Clear();

		//添加记录
		_uidlist.insert(uid);
		_tm2uid.insert(std::make_pair(now, uid));
		//__SKPRINTF("feedbackSkill TakeEffect tar=%ld skill=%d interval=%d\n", uid, (int)_data, _param);
	}
};

//向对我攻击的目标反馈技能
//内参_param是对单个目标的间隔，毫秒
//外参_data是反馈的技能
class feedbackSkillAttacker: public feedbackSkill
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_BE_ATTACKED;
	}
	void BeAttacked(object_interface& parent, const attacker_info_t& info)
	{
		if (horizontal_distance(parent.GetPos(), info.pos) <= 4 * 4)
		{
			_target = info.attacker;
		}
	}
};

//自己和目标的距离大于阈值，驱散自己
//无法驱散个人buff
//外参：目标ID.newtype
//内参：距离，米
class keep_dis: public StateEffect
{
	XID _target;
	int _self_filter_id = 0;
public:
	keep_dis() { _flag = FLAG_DOSETUP;}
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		MakeXIDFromNewID(_target, (int)_data);
		_self_filter_id = filter->GetID();
		_param *= _param;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (!parent.CheckRange(_target, _param))
		{
			//parent.GetSkillWrapper().BuffSkill(parent, _param);
			parent.RemoveFilter(_self_filter_id);//驱散自己
		}
	}
};

//自己和目标的距离大于阈值，驱散自己
//可以驱散个人buff,距离要加上双方的body_size
//外参：目标ID.newtype
//外参2：驱散自己时，让对我加该buff的人对我释放的技能id
//      时间到释放的技能 * 100000 + 超出距离释放的技能
//内参：距离，米
class keep_dis2: public StateEffect
{
	XID _target;
private:
	void _CheckRange(object_interface& parent)
	{
		__SKPRINTF("keep_dis2 Heartbeat _target.newtype=%d check range=%d\n", _target.newtype, parent.CheckRangeAndBodySize(_target, _param));
		if (!parent.CheckRangeAndBodySize(_target, _param))
		{
			_filter->SetDelete();//驱散自己
			int skill_id = _data2 % 100000;
			if (skill_id > 0)
			{
				parent.CastSkill2Me(_target, skill_id);
			}
		}
	}
public:
	keep_dis2() { _flag = FLAG_DOSETUP;}
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT | filter::FM_MOVE; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		MakeXIDFromNewID(_target, (int)_data);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		_CheckRange(parent);
	}
	void Move(object_interface& parent)
	{
		_CheckRange(parent);
	}
	void UndoEffect(object_interface& parent)
	{
		if (_filter->GetEndReason() == filter::ER_TIMEOUT)
		{
			int skill_id = _data2 / 100000;
			if (skill_id > 0)
			{
				parent.CastSkill2Me(_target, skill_id);
			}
		}
	}
};

// 增加饱食度
class IncHungry : public StateEffect
{
public:
	IncHungry()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_hungry(parent, (int)_data);
	}
};

// 增加愉悦度
class IncHappy : public StateEffect
{
public:
	IncHappy()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_happy(parent, (int)_data);
	}
};

//一段时间里持续击中当前目标，对自己释放技能
//_param 内参 技能id
//_data 外参 持续时间，秒
//_data2 外参 两次击中的最大间隔时间，秒
class persistAttackTarget : public StateEffect
{
private:
	ruid_t _last_target = 0;
	uint64_t _last_tk = 0;//上次击中的时间
	uint64_t _first_tk = 0;//第一次击中的时间
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TAR_HIT;
	}
	void AttackTarget(object_interface& parent, const XID& target)
	{
		uint64_t now_tk = object_interface::GetTick();
		if (_last_target != target.id || now_tk - _last_tk > TICK_PER_SECOND * _data2)
		{
			//切换目标或者间隔时间太长，重新计算
			_last_target = target.id;
			_first_tk = _last_tk = now_tk;
			return;
		}
		if (now_tk - _first_tk >= TICK_PER_SECOND * _data)
		{
			parent.GetSkillWrapper().BuffSkill(parent, _param);
			_last_target = 0;
			_first_tk = _last_tk = 0;
			return;
		}
		_last_tk = now_tk;
	}
};

//一段时间里持续持续受到攻击，对自己释放技能
//_param 内参 技能id
//_data 外参 持续时间，秒
//_data2 受到攻击次数
class persistBeAttacked : public StateEffect
{
private:
	uint64_t _first_tk = 0;//第一次击中的时间
	int _num = 0;//受到攻击次数
	uint64_t _skill_tk = 0;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_BE_ATTACKED;
	}
	void BeAttacked(object_interface& parent, const attacker_info_t& info)
	{
		_num ++;
		uint64_t now_tk = object_interface::GetTick();
		if (_first_tk == 0)
		{
			_first_tk = now_tk;
			return;
		}
		if (now_tk - _first_tk >= TICK_PER_SECOND * _data)
		{
			_first_tk = 0;
			_num = 0;
			return;
		}
		if (_num >= _data2)
		{
			if (now_tk - _skill_tk >= 60 * TICK_PER_SECOND)
			{
				parent.GetSkillWrapper().BuffSkill(parent, _param);
				_skill_tk = now_tk;//给技能加冷却
			}
			_first_tk = 0;
			_num = 0;
		}
	}
};

//被多个敌人攻击时，触发技能，脱战清空敌人列表
//_param 内参 技能id
//_data 外参 概率，千分之一
//_data2 敌人数量
class beAttackedByManyEnemy : public StateEffect
{
private:
	std::set<ruid_t> _enemy_list;
	uint64_t _skill_tk = 0;
public:
	virtual filter_eventmask_t GetEventMask() override
	{
		return filter::FM_BE_ATTACKED | filter::FM_LEAVE_COMBAT;
	}
	virtual void BeAttacked(object_interface& parent, const attacker_info_t& info) override
	{
		_enemy_list.insert(info.attacker.id);
		if (_enemy_list.size() < _data2)
		{
			return;
		}
		uint64_t now_tk = object_interface::GetTick();
		if (now_tk - _skill_tk < TICK_PER_SECOND * 60)
		{
			return;
		}
		if (object_interface::Rand() * 1000 > _data)
		{
			return;
		}
		_skill_tk = now_tk;
		parent.GetSkillWrapper().BuffSkill(parent, _param);
	}
	virtual void LeaveCombat(object_interface& parent) override
	{
		_enemy_list.clear();
	}
};

//下一个释放的技能方案里的技能，如果冷却时间大于N, 就不进入冷却
//生效一次后删除该buff
//_param 内参 最低冷却时间，毫秒
class nextSkillWithoutCD : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.NextSkillWithoutCD(_param, _filter->GetID());
	}
	void UndoEffect(object_interface& parent)
	{
		parent.NextSkillWithoutCD(0, 0);
	}
};

//如果目标具有某种typemask,对它的伤害增加
//_data 外参 伤害比率，千分之一
//_param 内参 typemask 低位
class addDamageByTypeMask : public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		uint64_t mask = (uint64_t)_param;
		if (mask <= 0 || _data <= 0)
		{
			return;
		}
		for (int i = 0; i < ATTACK_TYPEMASK_COUNT; ++i)
		{
			if (msg.attack_typemask[i] == 0 || msg.attack_typemask[i] == mask)//相等的掩码项，伤害比率可以累加
			{
				msg.attack_typemask[i] = mask;
				msg.attack_typemask_damage_add[i] += _data;
				break;
			}
		}
	}
};

//持续修改属性:点数修正物理伤害放大系数
//_data 外参 初始变化值
//_data2 外参 每秒修改值
class persistModifypointPhyDamAdd: public StateEffect
{
protected:
	int _change_value = 0;
public:
	persistModifypointPhyDamAdd() {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_pointPhyDamAdd(parent, (int)_data);
		_change_value += (int)_data;
	}
	void UndoEffect(object_interface& parent)
	{
		Dec_pointPhyDamAdd(parent, _change_value);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		Inc_pointPhyDamAdd(parent, (int)_data2);
		_change_value += (int)_data2;
	}
};

//持续修改属性:点数修正法术伤害放大系数
//_data 外参 初始变化值
//_data2 外参 每秒修改值
class persistModifypointMagDamAdd: public StateEffect
{
protected:
	int _change_value = 0;
public:
	persistModifypointMagDamAdd() {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_pointMagDamAdd(parent, (int)_data);
		_change_value += (int)_data;
	}
	void UndoEffect(object_interface& parent)
	{
		Dec_pointMagDamAdd(parent, _change_value);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		Inc_pointMagDamAdd(parent, (int)_data2);
		_change_value += (int)_data2;
	}
};

//持续修改属性:点数修正基础物理伤害减免系数
//_data 外参 初始变化值
//_data2 外参 每秒修改值
class persistModifypointPhyDamRedu: public StateEffect
{
protected:
	int _change_value = 0;
public:
	persistModifypointPhyDamRedu() {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_pointPhyDamRedu(parent, (int)_data);
		_change_value += (int)_data;
	}
	void UndoEffect(object_interface& parent)
	{
		Dec_pointPhyDamRedu(parent, _change_value);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		Inc_pointPhyDamRedu(parent, (int)_data2);
		_change_value += (int)_data2;
	}
};

//持续修改属性:点数修正法术伤害减免系数
//_data 外参 初始变化值
//_data2 外参 每秒修改值
class persistModifypointMagDamRedu: public StateEffect
{
protected:
	int _change_value = 0;
public:
	persistModifypointMagDamRedu() {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_pointMagDamRedu(parent, (int)_data);
		_change_value += (int)_data;
	}
	void UndoEffect(object_interface& parent)
	{
		Dec_pointMagDamRedu(parent, _change_value);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		Inc_pointMagDamRedu(parent, (int)_data2);
		_change_value += (int)_data2;
	}
};

//持续修改属性:千分比修正奔跑速度
//_data 外参 初始变化值
//_data2 外参 每秒修改值
class persistModifyscaleRunSpeed: public StateEffect
{
protected:
	int _change_value = 0;
public:
	persistModifyscaleRunSpeed() {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		Inc_scaleRunSpeed(parent, (int)_data);
		_change_value += (int)_data;
	}
	void UndoEffect(object_interface& parent)
	{
		Dec_scaleRunSpeed(parent, _change_value);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		Inc_scaleRunSpeed(parent, (int)_data2);
		_change_value += (int)_data2;
	}
};

//带buff的人死亡时，加buff的人向死者位置释放技能
//_data 外参 技能id
class attackerCastSkillAtHere : public StateEffect
{
private:
	XID _attacker;
public:
	attackerCastSkillAtHere() { _flag = FLAG_DOSETUP; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill->GetAttackerInfo())
		{
			_attacker = skill->GetAttackerInfo()->attacker;
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_filter->GetEndReason() == filter::ER_DEATH || parent.IsDead())
		{
			parent.CastSkillAtHere(_attacker, (int)_data);
		}
	}
};

//双生体分离
class twinDetach : public StateEffect
{
public:
	twinDetach() {}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.RemoveFilter(TWIN_BIND_FILTER_ID + FILTERID_BEGIN, 0);
		parent.TwinDetach(true);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.TwinDetach(false);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		bool has = false;
		if (parent.GetTwinSquaredDistance(has) > 20 * 20 || !has)
		{
			_filter->SetDelete();
		}
	}
};

//双生体释放技能
//_data 外参 技能id
//_data2 外参2 技能等级
class twinSkill : public StateEffect
{
public:
	twinSkill()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.TwinSkill(_data, _data2);
	}
};

//双生体瞬移到目标处，然后释放技能
//_data 外参 技能id
//_data2 外参2 技能等级
class twinJumpSkill : public StateEffect
{
	A3DVECTOR3 _target_pos;
public:
	twinJumpSkill() { _flag = FLAG_INSTANT | FLAG_DOSETUP; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_target_pos = skill->GetTargetPos();
	}
	void TakeEffect(object_interface& parent)
	{
		parent.TwinSkillAtPos(_data, _data2, _target_pos);
	}
};

//双生体瞬移到跟随点
class twinJumpBack : public StateEffect
{
public:
	twinJumpBack()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.TwinJumpBack();
	}
};

//双生体瞬移到跟随点并跟随玩家
//客户端要将双生体和玩家绑定
class twinBind : public StateEffect
{
public:
	twinBind() {}
	void TakeEffect(object_interface& parent)
	{
		parent.RemoveFilter(TWIN_DETACH_FILTER_ID + FILTERID_BEGIN, 0);
		parent.TwinBind(true);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.TwinBind(false);
	}
};

//共生，契约，成对出现在两个玩家身上
//切地图不清除，切完图后重新添加
//自己身上的不是个人buff，目标身上的是个人buff
//外参 _data2 共生对象的newid
class coexist: public StateEffect
{
	bool _is_caster = false;//是不是技能的释放者
	XID _target_xid;
	char _out_range_turn = 0;
	time_t _check_tm = 0;
	int _skill_id = 0;//添加该buff的技能id
public:
	coexist() { _flag = FLAG_DOSETUP; }
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		_check_tm = object_interface::GetSysTime();
	}
	void TakeEffect(object_interface& parent)
	{
		if (_target_xid.id > 0)
		{
			if (_is_caster)
			{
				parent.CoexistDelayAdd(_target_xid, _filter->GetTimeout());//切地图后生效，双方的newid变了，重新添加一次
			}
			_filter->SetDelete();
		}
		else
		{
			MakeXIDFromNewID(_target_xid, _data2);
			if (_filter->RawGetFromRid() == parent.GetID().id)
			{
				_is_caster = true;
				parent.GetSkillWrapper().SetCoexistTarget(_target_xid);
			}
		}
		__SKPRINTF("coexist TakeEffect buff_id=%d,self_rid=%ld other_rid=%ld, is_caster=%d\n",
		           _filter->GetID() - FILTERID_BEGIN, parent.GetID().id, _target_xid.id, _is_caster);
	}
	void UndoEffect(object_interface& parent)
	{
		int filter_id = _is_caster ? TWIN_COEXIST_BUFF_A_ID : TWIN_COEXIST_BUFF_B_ID;
		parent.RemoveFilter(filter_id + FILTERID_BEGIN, 0);
		int reason = _filter->GetEndReason();
		__SKPRINTF("coexist UndoEffect buff_id=%d, self_rid=%ld, reason=%d\n",
		           _filter->GetID() - FILTERID_BEGIN, parent.GetID().id, reason);
		if (_is_caster)
		{
			parent.GetSkillWrapper().SetCoexistTarget(XID());
		}
		if (reason != filter::ER_SWITCH_MAP && reason != filter::ER_COEXIST && reason != filter::ER_TIMEOUT)
		{
			int remove_buff_id = _is_caster ? TWIN_COEXIST_OTHER_BUFF_ID : TWIN_COEXIST_SELF_BUFF_ID;
			parent.CoexistEnd(_target_xid, remove_buff_id + FILTERID_BEGIN);//让对方把自己身上的契约buff删掉
		}
	}
	void Heartbeat(object_interface& parent, int tick)
	{
		//不在同一个地图或距离过远，删除buff， 一秒判断一次
		A3DVECTOR3 target_pos;
		int64_t target_hp = 0;
		int64_t target_max_hp = 0;
		if (!parent.CheckRangeAndGetInfo(_target_xid, 200 * 200, target_pos, target_hp, target_max_hp))
		{
			_out_range_turn ++;
			if (_out_range_turn > 3)
			{
				_filter->SetDelete();
			}
			return;
		}
		_out_range_turn = 0;

		bool all_alive = target_hp > 0 && !parent.IsDead();
		bool is_near = horizontal_distance(parent.GetPos(), target_pos) <= 11 * 11;

		//里面的逻辑消耗较大，五秒判断一次
		time_t now = object_interface::GetSysTime();
		if (now - _check_tm >= 5)
		{
			_check_tm = now;

			//敌友关系可能发生变化
			if (all_alive && !parent.CanBless(_target_xid))
			{
				_filter->SetDelete();
				return;
			}

			//生命百分比高的人给对方输血
			if (_is_caster && all_alive && is_near && parent.HasTalent(TWIN_COEXIST_HP_TALENT_ID, _skill_id))
			{
				float diff = parent.RatioHp() - (float)target_hp / target_max_hp;
				if (diff > 0.01)
				{
					parent.GetSkillWrapper().BuffSkill_target(parent, TWIN_COEXIST_HP_SKILL_ID, 1, _target_xid);//我给对方输血
				}
				else if (diff < -0.01)
				{
					parent.CastSkill2Me(_target_xid, TWIN_COEXIST_HP_SKILL_ID);//对方给我输血
				}
			}
		}

		//额外的buff, 该buff在玩家身上是唯一的
		if (_is_caster)
		{
			bool should_have = all_alive && is_near && parent.HasTalent(TWIN_COEXIST_BUFF_TALENT_ID, _skill_id);
			if (parent.HasBuff(TWIN_COEXIST_BUFF_A_ID))
			{
				if (!should_have)
				{
					parent.GetSkillWrapper().BuffSkill_target(parent, TWIN_COEXIST_DEL_BUFF_SKILL_ID, 1, _target_xid);//把buff驱散掉
				}
			}
			else
			{
				if (should_have)
				{
					parent.GetSkillWrapper().BuffSkill_target(parent, TWIN_COEXIST_ADD_BUFF_SKILL_ID, 1, _target_xid);//把buff加上
				}
			}
		}
	}
	virtual bool Save(archive& ar)
	{
		StateEffect::Save(ar);
		ar << _is_caster << _target_xid.id;//xid.newtype切场景后会变化，不用存盘
		return true;
	}
	virtual bool Load(archive& ar)
	{
		StateEffect::Load(ar);
		ar >> _is_caster >> _target_xid.id;
		MakeNewIDFromXID(_target_xid, _target_xid);//重新计算xid.newtype
		return true;
	}
};

//向有coexist技能效果的目标释放技能
//内参_param是技能id
//外参_data距离
class skill2coexist : public StateEffect
{
public:
	skill2coexist()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		const XID& target = parent.GetSkillWrapper().GetCoexistTarget();
		if (target.IsValid() && parent.CheckRange(target, _data * _data))
		{
			parent.GetSkillWrapper().BuffSkill_target(parent, _param, 1, target);
		}
	}
};

//向给我加buff的目标释放技能
//外参 _data 技能id
//外参2 _data2 技能等级
class skillBack : public StateEffect
{
private:
	XID _target;
public:
	skillBack(){_flag = FLAG_INSTANT | FLAG_DOSETUP;}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		if (skill->GetAttackerInfo())
		{
			_target = skill->GetAttackerInfo()->attacker;
		}
	}
	void TakeEffect(object_interface& parent)
	{
		if (_target.IsValid())
		{
			parent.GetSkillWrapper().BuffSkill_target(parent, _data, std::max(1, _data2), _target);
		}
	}
};

//变形，该效果对npc的处理和morph不一样
//外参_data变身模板id
//TYPEMASK_SILENT是为了让免疫沉默的npc不会被变形
class changeModel : public StateEffect
{
private:
	int attacker_newid = 0;
public:
	changeModel() { _flag = FLAG_DOSETUP; }
	filter_typemask_t GetTypeMask() { return TYPEMASK_CHANGE_MODEL | TYPEMASK_SILENT_ABSOLUTE | TYPEMASK_SILENT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		attacker_newid = skill->GetAttackSource().newtype;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_times == 0)
		{
			parent.ChangeModel((int)_data, attacker_newid);
			parent.SetSilentAbsoluteSeal(true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		if (parent.GetTransformTid() == (int)_data)
		{
			parent.ResetModel();
			parent.SetSilentAbsoluteSeal(false);
		}
	}
};

//如果我离双生体在一定距离内，提高我的伤害
//_param 内参 距离，米
//_data 外参 伤害比例，千分之一
class addDmgByTwin : public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
		{
			return;
		}
		bool has = false;
		if (parent.GetTwinSquaredDistance(has) <= _param * _param || !has)
		{
			msg.attack_damage_add += _data;//skill_damage_rate有可能在目标身上重新计算
		}
	}
};

//禁止增加hp
class banAddHP : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.UpdateObjectServerState(gobject::SERVER_STATE_BAN_ADD_HP, true);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectServerState(gobject::SERVER_STATE_BAN_ADD_HP, false);
	}
};

//进入濒死状态，目前只在吃鸡玩法中使用
class nearDeath : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_NEAR_DEATH, true, true);
		parent.NearDeath(true);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_NEAR_DEATH, false, true);
		parent.NearDeath(false);
	}
};

//锁血
////_data 外参 最大生命的千分之几
class setMinHP: public StateEffect
{
public:
	void TakeEffect(object_interface & parent)
	{
		int64_t a = Get_maxHP(parent) * _data / 1000;
		if (a <= 0)
		{
			a = 1;
		}
		parent.SetMinHP(a);
	}
	void UndoEffect(object_interface & parent)
	{
		parent.SetMinHP(0);
	}
};

//机甲释放技能
//_data 外参 技能id
//_data2 外参2 技能等级
class mechSkill : public StateEffect
{
public:
	mechSkill()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.MechSkill(_data, _data2);
	}
};

//机甲和玩家合体
//_data 外参 合体结束后释放技能id
class mechCompose : public StateEffect
{
private:
	bool _active = false;
public:
	mechCompose() {}
	void TakeEffect(object_interface& parent)
	{
		_active = parent.MechCompose();
		if (!_active)
		{
			_filter->SetDelete();
		}
	}
	void UndoEffect(object_interface& parent)
	{
		if (_active)
		{
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data);
			parent.MechDecompose(_filter->GetEndReason() == filter::ER_SWITCH_MAP);
			parent.DelayCastSkill2Me(parent.GetID(), _data2, 1);//10毫秒后
		}
	}
};

//机甲瞬移, 然后释放技能
//_param 内参 技能id
//_data 外参 与主人的距离,米
//_data2 外参2 角度，主人朝向为0，顺时针为正
class mechJumpSkill : public StateEffect
{
public:
	mechJumpSkill()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.MechJumpSkill(_data2, _data, _param);
	}
};

//机甲释放技能, 要为该技能指定位置
//_param 内参 技能id
//_data 外参 与主人的距离,米
//_data2 外参2 角度，主人朝向为0，顺时针为正
class mechSkillToPos : public StateEffect
{
public:
	mechSkillToPos()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.MechSkillToPos(_data2, _data, _param);
	}
};

//机甲向主人释放技能的位置释放技能
//_data 外参 技能id
class mechSkillToPos2 : public StateEffect
{
private:
	A3DVECTOR3 pos;
public:
	mechSkillToPos2() { _flag = FLAG_INSTANT | FLAG_DOSETUP; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		pos = skill->GetTargetPos();
	}
	void TakeEffect(object_interface& parent)
	{
		parent.MechSkillToPos2(pos, _data);
	}
};

//根据护盾的比例提高我的伤害
//_data 外参 系数
class shieldAddDmg : public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		int64_t max = Get_maxShield(parent);
		if (max <= 0)
		{
			return;
		}
		int64_t cur = Get_shield(parent);
		int rate = cur * 1000 * _data / max;
		msg.attack_damage_add += rate;//skill_damage_rate有可能在目标身上重新计算s
	}
};

//根据护盾的比例减少我的受到的伤害
//_data 外参 系数
class shieldCostDmg: public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_ADJUST_DAMAGE;
	}
	void AdjustDamage(object_interface& parent, float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag)
	{
		int64_t max = Get_maxShield(parent);
		if (max <= 0)
		{
			return;
		}
		int64_t cur = Get_shield(parent);
		float rate = (max - cur) * _data / max;
		dmg *= 1 - rate;
	}
};

//护盾低于千分之x时伤害提升千分之y
//_data 外参 护盾千分比
//_data2 外参2 伤害千分比
class shieldAddDmg2 : public StateEffect
{
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		int64_t max = Get_maxShield(parent);
		if (max <= 0)
		{
			return;
		}
		int64_t cur = Get_shield(parent);
		if (cur * 1000 / max > _data)
		{
			return;
		}
		msg.attack_damage_add += _data2;//skill_damage_rate有可能在目标身上重新计算s
	}
};

//玩家释放特定技能若干次后，驱散自己
//_param 内参 技能id
//_data 外参 次数
//_data2 外参2 技能id
class dispelSelfBySkillTurn : public StateEffect
{
private:
	char _skill_stage = -1;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_TRANS_SEND_MSG;
	}
	void TranslateSendAttack(object_interface& parent, const XID& target, attack_msg& msg)
	{
		if (msg.skill_id != _param && msg.skill_id != _data2)
		{
			return;
		}
		if (_skill_stage < 0 || _skill_stage >= msg.attack_stage)
		{
			if (_data > 0)
			{
				--_data;
				_filter->SetClientData(0, _data);
				_filter->UpdateBuff();
			}
		}
		_skill_stage = msg.attack_stage;
		if (_data <= 0)
		{
			_filter->SetDelete();
		}
	}
};

//创建一个向前移动的假人
class createFakerMoveForward : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.CreateFakerMoveForward(_filter->GetTimeParam() / 1000 + 3);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.DeleteFakerMoveForward();
	}
};

//创建一个原地释放技能的假人
class createFakerStandSkill : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.CreateFakerStandSkill(_filter->GetTimeParam() / 1000 + 3);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.DeleteFakerStandSkill();
	}
};

//和向前移动的假人交换位置
class swapPosWithFakerMove : public StateEffect
{
public:
	swapPosWithFakerMove()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.SwapPosWithFakerMove();
	}
};

//和原地释放技能的假人交换位置
class swapPosWithFakerStand : public StateEffect
{
public:
	swapPosWithFakerStand()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.SwapPosWithFakerStand();
	}
};

//以一定频率记录hp，pos和dir
//最多保存10条记录
//_data 外参 第1条和第10条记录的时间间隔，毫秒
class recordHpPosDir : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.RecordHpPosDir(_data > 1000 ? _data : 1000);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.RecordHpPosDir(0);
	}
};

//用最早的记录来恢复hp，pos和dir
//_param 内参 能否让血量变少
//_data 外参 pos和dir是否生效
class useHpPosDir : public StateEffect
{
public:
	useHpPosDir()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.UseHpPosDir(_param > 0, _data > 0);
	}
};

//只在吃鸡模式可用，传送到安全区中的随机位置
class pubgSafeRandTeleport : public StateEffect
{
public:
	pubgSafeRandTeleport() { _flag = FLAG_INSTANT; }
	void TakeEffect(object_interface& parent)
	{
		parent.PubgSafeRandTeleport();
	}
};

//对主人释放一个瞬发技能
//_data 外参 技能id
//_data2 外参2 技能等级
class skill2Master : public StateEffect
{
public:
	skill2Master()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		int id = _data;
		int level = _data2;
		if (id > 0 && level > 0 && parent.GetMaster().IsValid())
		{
			parent.GetSkillWrapper().InstantSkill(parent, id, level, 0, WMSKILL::CAST_PRIOR_ID, parent.GetMaster(), A3DVECTOR3(), 0);
		}
	}
};

//自身的一些增减伤属性不生效
class ignoreSameDamageProp: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		parent.SetIgnoreSomeDamageProp(IGNORE_RATIO_REDU | IGNORE_RATIO_ADD, 0);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetIgnoreSomeDamageProp(IGNORE_INVALID);
	}
};
//自身的一些增减伤属性百分比失效  外参保留的百分比  ignoreSameDamageProp 废弃
class ignorePrecntDamageProp: public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if ((_param & IGNORE_RATIO_ADD || _param & IGNORE_RATIO_REDU))
		{
			parent.SetIgnoreSomeDamageProp(_param, _data);
		}
	}
	void UndoEffect(object_interface& parent)
	{
		parent.ClrIgnoreSomeDamageProp();
	}
};


//原地站立一段时间后，锁定技能cd，移动则解除锁定
//_data 外参 站立时间，秒
class standPauseManualSkillCD : public StateEffect
{
private:
	time_t _stand_tm = 0;
public:
	standPauseManualSkillCD() { _flag = FLAG_DOSETUP;}
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT | filter::FM_MOVE; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_stand_tm = object_interface::GetSysTime();
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (object_interface::GetSysTime() - _stand_tm >= _data)
		{
			parent.PauseManualSkillCoolDown(1010);
		}
	}
	void Move(object_interface& parent)
	{
		_stand_tm = object_interface::GetSysTime();
	}
};

//与 symbiotic_b 成对出现在两个角色身上，一个消失后另一个也跟着消失
//外参 _data2, 拥有 symbiotic_b 的对象的newid
//只应该有一个
class symbiotic_a : public StateEffect
{
	XID _target_xid;
	bool _first_tick = true;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_filter)
		{
			_target_xid.id = _filter->RawGetFromRid();//不是个人buff
			MakeNewIDFromXID(_target_xid, _target_xid);
		}
		parent.AddSymbioticNewID_A(_target_xid.newtype);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.RemoveSymbioticNewID_A(_target_xid.newtype);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (_first_tick)
		{
			_first_tick = false;
			return;//第一次心跳不检查，防止对方还来不及加buff
		}
		if (_filter == NULL)
		{
			return;
		}
		if (!parent.CheckRange(_target_xid, 9999 * 9999))
		{
			_filter->SetDelete();//对方离线或者不在同一个地图
			return;
		}
		parent.CheckTargetHasSymbioticB(_target_xid, _filter->GetID());//询问对方是否有我的信息
	}
};

//与 symbiotic_a 成对出现在两个角色身上，一个消失后另一个也跟着消失
//外参 _data2, 拥有 symbiotic_a 的对象的newid
class symbiotic_b : public StateEffect
{
	XID _target_xid;
	bool _first_tick = true;
public:
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_HEARTBEAT;
	}
	void TakeEffect(object_interface& parent)
	{
		if (_filter)
		{
			_target_xid.id = _filter->GetFromRid();
			MakeNewIDFromXID(_target_xid, _target_xid);
		}
		parent.AddSymbioticNewID_B(_target_xid.newtype);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.RemoveSymbioticNewID_B(_target_xid.newtype);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		if (_first_tick)
		{
			_first_tick = false;
			return;//第一次心跳不检查，防止对方还来不及加buff
		}
		if (_filter == NULL)
		{
			return;
		}
		if (!parent.CheckRange(_target_xid, 9999 * 9999))
		{
			_filter->SetDelete();//对方离线或者不在同一个地图
			return;
		}
		parent.CheckTargetHasSymbioticA(_target_xid, _filter->GetID());//询问对方是否有我的信息
	}
};
//让施法者对自己释放技能
//外参 _data 技能id
//外参2 _data2 技能等级
class CastSkill2Me : public StateEffect
{
private:
	XID attacker;
public:
	CastSkill2Me() { _flag = FLAG_INSTANT | FLAG_DOSETUP; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) 
	{
		if (skill->GetAttackerInfo())
		{
			attacker = skill->GetAttackerInfo()->attacker;
		}
	}
	void TakeEffect(object_interface& parent)
	{
		int skill_id = (int)_data;
		int skill_level = _data2 > 0 ? (int)_data2 : 1; // 默认等级1
		parent.CastSkill2Me(attacker, skill_id, skill_level);
	}
};
//一定时间内生命值减少超过一定数量时对自己释放技能
//内参秒数 
//外参1百分比
//外参2技能id
class PercentDamageCastSkill2Me  : public StateEffect
{
private:
	int start_time = 0;
	std::vector<int> percent_hp;
public:
	PercentDamageCastSkill2Me() { _flag = FLAG_DOSETUP; }
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) 
	{
	   start_time = (int)GNET::Timer::GetTime();
	   int percent = player.GetPercetHP();
	   if(percent_hp.empty())
	   {
	     percent_hp.push_back(percent);
	   }
	   else
	   {
		 percent_hp[0] = percent;
	   }
	   __PRINTF("PercentageDamageCastSkill2Me::Setup::id=%ld:now =%d:percent_hp=%d:size=%zu\n",player.GetID().id,start_time,percent,percent_hp.size());
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		int now =  (int)GNET::Timer::GetTime();
		int diff = (now - start_time) % ((int)_param + 1);
		if((int)_param >= 0 && percent_hp.size() > (int)_param )
		{
		  percent_hp[diff] = parent.GetPercetHP();
		  int end = (diff + 1) % (_param +1 );
		  int diff_percent = percent_hp[diff]  -  percent_hp[end];
		  if(diff_percent >= (int)_data)
		  {
			parent.GetSkillWrapper().BuffSkill(parent, (int)_data2);
			__PRINTF("PercentageDamageCastSkill2Me::Heartbeat::castskill::id=%ld:diff_percent =%d:percent_hp=%d:size=%zu::skill_id=%d\n",parent.GetID().id,diff_percent,  parent.GetPercetHP(),percent_hp.size(),(int)_data2);
		  }
		}
		else if(percent_hp.size() <= (int)_param )
		{
		  percent_hp.push_back(parent.GetPercetHP());
		}
		__PRINTF("PercentageDamageCastSkill2Me::Heartbeat::id=%ld:percent_hp=%d:size=%zu\n",parent.GetID().id,parent.GetPercetHP(),percent_hp.size());
	}
};

//牢笼效果 
//_data 外参 牢笼半径，米
class cage: public StateEffect
{
private:
	A3DVECTOR3 _center_pos;
public:
	cage() {_flag = FLAG_DOSETUP;}
	filter_eventmask_t GetEventMask() {return filter::FM_SWITCH_MAP_DISP | filter::FM_HEARTBEAT | filter::FM_MOVE | filter::FM_WEAK ;}
	filter_typemask_t GetTypeMask() { return  TYPEMASK_CAGE; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_center_pos = caster_pos;
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		bool in_range = horizontal_distance(parent.GetPos(), _center_pos) <= (float)_data * (float)_data;
		if (!in_range)
		{
			parent.SwapPosWithCagePos();
		}
	}
	void Move(object_interface& parent)
	{
		bool in_range = horizontal_distance(parent.GetPos(), _center_pos) <= (float)_data * (float)_data;
		if (!in_range)
		{
			parent.SwapPosWithCagePos();
		}
	}
	void TakeEffect(object_interface& parent)
	{
		parent.SetCage(true, _center_pos, (float)_data);
	}
	void UndoEffect(object_interface& parent)
	{
		parent.SetCage();
	}
};
//致死效果
class deatheffect: public StateEffect
{
protected:
	XID attacker;
	attacker_info_t atk_info;
	int _elapse_time = 0;
	int _skill_id = 0;
public:
	deatheffect() {_flag = FLAG_INSTANT | FLAG_DOSETUP;memset(&atk_info,0,sizeof(atk_info));}
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_skill_id = skill->GetId();
		attacker = skill->GetAttackSource();
		if (skill->GetAttackerInfo())
		{
			atk_info = *skill->GetAttackerInfo();
		}
	}
	void TakeEffect(object_interface& parent)
	{
	   float per_hp = parent.RatioHp();
	   float damage = parent.GetHPMax();
	   if(per_hp >= 1e-6 && per_hp <= 0.300f && parent.IsPlayerClass())
	   {
		 parent.BeHurt(attacker, atk_info, damage, _skill_id,true,true);
	   }
	}
};
//职业10分身释放技能
//_data 外参 技能id
//_data2 外参2 高16位 技能等级  低16 位伤害比例 (0~2000)
class replisomeSkill : public StateEffect
{
public:
	replisomeSkill()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		int skill_level = _data2 & 0xffff0000;
		int ratio = _data2 & 0xffff;
		parent.SetRelisomeDamRatio(ratio);
		parent.ReplisomeSkill(_data, skill_level);
	}
};
//职业10 释放分身
//_data 外参 个数
class createreplisome : public StateEffect
{
	bool _replisome[PLAYER_REPLISOME_MAX];
public:
	createreplisome()
	{
		_flag = FLAG_INSTANT;
		memset(_replisome, 0, sizeof(bool)*PLAYER_REPLISOME_MAX);
	}
	void TakeEffect(object_interface& parent)
	{
		for (unsigned int i = 0; i < (unsigned int)_data && i <  PLAYER_REPLISOME_MAX ; ++i)
		{
			unsigned int index = parent.TryCreateReplisome();
			if (index < PLAYER_REPLISOME_MAX )
			{
				_replisome[index] = true;
				__PRINTF("effect::createreplisome index =%d\n", index);
			}
		}
	}
	void UndoEffect(object_interface& parent)
	{
		for (unsigned int i = 0; i <  PLAYER_REPLISOME_MAX ; ++i)
		{
			if (_replisome[i])
			{
				__PRINTF("effect::trydeletereplisome index =%d\n", i);
				parent.TryDeleteReplisome(i);
				_replisome[i] = false;
			}
		}
	}
};
//驱散霸体 buff生效期间不能加上霸体
class dispel_immune: public StateEffect
{
public:
	filter_typemask_t GetTypeMask() { return TYPEMASK_DISPEL_SUPPER; }
	filter_typemask_t GetDispelMask() { return (_param & 277815) == 277815 ? TYPEMASK_SUPPER : 0; } //
	void TakeEffect(object_interface& parent)
	{
		if (_param == 0)
		{
			return;
		}
		//调整免疫掩码
		parent.GetSkillWrapper().ModifyImmuneMask(TYPEMASK_SUPPER, true);
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		if (_param == 0)
		{
			return;
		}
		for (int i = _times; i > 0; --i)
		{
			parent.GetSkillWrapper().ModifyImmuneMask(TYPEMASK_SUPPER, false);
		}
	}

};
//禁止获取MP1MP4
class forbid_MP_1_4_obtain: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_FORBID_MP_1_4 ;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.UpdateObjectState2(gobject::STATE2_FORBID_MP_1_4_OBTAIN, true, false);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_FORBID_MP_1_4_OBTAIN, false, false);
	}
};
//移动到最远子物体处
//内参 _param 子物体tid
//外参 _data 是否杀死子物体
class Jump2FarthestSubobjPos : public StateEffect
{
public:
	Jump2FarthestSubobjPos()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface& parent)
	{
		parent.Jump2FarthestSubobjPos(_param, _data > 1e-5, _data2);
	}
};

//缠绕 体型需要在成功概率判断
class Twining: public StateEffect
{
public:
	filter_typemask_t GetTypeMask()
	{
		return TYPEMASK_TWINING;
	}
	filter_eventmask_t GetEventMask()
	{
		return filter::FM_SWITCH_MAP_DISP;
	}
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.UpdateObjectState2(gobject::STATE2_TWINING, true, true);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_TWINING, false, true);
	}
};

class forbid_passive_longyu : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.UpdateObjectState2(gobject::STATE2_FORBID_PASSIVE_LONGYU, true, false);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_FORBID_PASSIVE_LONGYU, false, false);
	}
};
class IntervalsMaxDamage : public StateEffect
{
	int64_t intervals_damage;
	int last_tick;
public:
	IntervalsMaxDamage(): intervals_damage(0), last_tick(0) { _flag = FLAG_DOSETUP; }
	filter_eventmask_t GetEventMask() {return filter::FM_FINAL_ADJUST_DAMAGE;}
	void AdjustIntervalsDamage(object_interface& parent, float& damage)
	{
		uint64_t now_tk = object_interface::GetTick();
		if (now_tk - last_tick >  MILLISEC_TO_TICK((int)_data2))
		{
			intervals_damage = 0;
			last_tick = now_tk;
		} 
		int64_t max_hp =  parent.GetProperty().GetHPMax();
		float im_hp = max_hp * _data;

		if ((intervals_damage + damage) >=  im_hp)
		{
			damage = im_hp  -  intervals_damage >= 0 ? im_hp  -  intervals_damage : 0;
			intervals_damage = im_hp;
		}
		else
		{
			intervals_damage  += damage;
		}
	}
	void FinalAdjustDamage(object_interface& parent, const XID& attacker, float& damage)
	{
		AdjustIntervalsDamage(parent, damage);
	}
};
/*禁用伙伴主战辅战技能*/
class forbid_retinue_combat_skill : public StateEffect
{
public:
	void TakeEffect(object_interface& parent)
	{
		if (!_times)
		{
			parent.UpdateObjectState2(gobject::STATE2_FORBID_RETINUE_SKILL, true, false);
		}
		++_times;
	}
	void UndoEffect(object_interface& parent)
	{
		parent.UpdateObjectState2(gobject::STATE2_FORBID_RETINUE_SKILL, false, false);
	}
};

class CreateDog : public StateEffect
{
public:
	CreateDog() {}
	void TakeEffect(object_interface& parent)
	{
		__PRINTF("steffect::CreateDog\n");
		parent.TryCreateDog(_data);
	}
	void UndoEffect(object_interface& parent)
	{
		__PRINTF("steffect::deleteDog \n");
		parent.TryDeleteDog();
	}
};

//销毁子物体
class DistroySubobject: public StateEffect
{
public:
	DistroySubobject() {}
	void TakeEffect(object_interface& parent)
	{
		__PRINTF("DistroySubobject::TakeEffect %ld\n", _param);
		parent.TryRecallSubobject(_param, 1);
	}
};

//召回子物体到主人身边
class RecallSubobject : public StateEffect
{
public:
	RecallSubobject() {}
	void TakeEffect(object_interface& parent)
	{
		__PRINTF("RecallSubobject::TakeEffect %ld\n", _param);
		parent.TryRecallSubobject(_param, 0);
	}
	void UndoEffect(object_interface& parent)
	{
		__PRINTF("RecallSubobject::UndoEffect %ld\n", _param);
		parent.TryRecallSubobject(_param, 1);
	}
};

//记录数据
//内参 _param: 记录数据类型 (0 : 伤害; 1 : 血量)
class RecordData : public StateEffect
{
public:
	RecordData() {}
	void TakeEffect(object_interface& parent)
	{
		__PRINTF("RecordData::TakeEffect %ld\n", _param);
		if (_filter)
		{
			parent.StartRecordData(_param, _filter->GetFromRid());
		}
	}
	void UndoEffect(object_interface& parent)
	{
		__PRINTF("RecordData::UndoEffect %ld\n", _param);
		if (_filter)
		{
			parent.FinishRecordData(_param, _filter->GetFromRid());
		}
	}
};

//清除buff历史数据
class ClearBuffHistory: public StateEffect
{
public:
	ClearBuffHistory()
	{
		_flag |= FLAG_INSTANT;
	}
    void TakeEffect(object_interface & parent)
    {
	__PRINTF("ClearBuffHistory::TakeEffect \n");
	parent.ClearBuffHistory();
    }
};

//削弱身上已有护盾
//是否瞬发: 是
//内参 _param 
//外参 _data 千分比
class ReduceShield : public StateEffect
{
public:
	ReduceShield()
	{
		_flag = FLAG_INSTANT;
	}
	void TakeEffect(object_interface & parent)
	{
		parent.ReduceShieldInstant(_data);
	}
};

//卷协效果 (传送回释放者位置)
//外参 _data 多长时间后传送回(单位 ms)
class trans_back: public StateEffect
{
private:
	XID _attacker;
	uint64_t _trans_tk = 0; //传送tick时间
public:
	trans_back() {_flag = FLAG_DOSETUP;}
	filter_eventmask_t GetEventMask() { return filter::FM_HEARTBEAT; }
	filter_typemask_t GetTypeMask() { return TYPEMASK_TRANS_BACK; }
	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter, const A3DVECTOR3& caster_pos, const attack_msg *atk_msg)
	{
		_attacker = skill->GetAttackerInfo()->attacker;
		_trans_tk = object_interface::GetTick() + MILLISEC_TO_TICK(_data);
	}
	void Heartbeat(object_interface& parent, int tick) //tick表示本次间隔几秒
	{
		uint64_t now_tk = object_interface::GetTick();
		if (_trans_tk > now_tk)
		{
			return;
		}
		//传送
		do
		{
			A3DVECTOR3 apos;
			unsigned short scene_tag, adir;
			float body_size;
			bool is_dead;
			if (!parent.QueryObject(_attacker, apos, scene_tag, body_size, adir, is_dead))
			{
				break;
			}
			if (!parent.IsPlayerClass())
			{
				break;
			}
			if (parent.IsImmuneMove())
			{
				break;
			}
			//if (NeedWaitCurAction(parent))
			//{
			//	break;
			//}
			if (scene_tag != parent.GetSceneTag())
			{
				break;
			}
			parent.BreakAction(); //中断其他动作
			A3DVECTOR3 direction = parent.GetPos() - apos;
			unsigned short _;
			if (parent.SkillMove(apos, _, false, false, false, CHECK_Y_DIFF_STRICT))
			{
				parent.SendClientObjectMove(apos, a3dvector_to_dir(direction), 0xFFFF, true);
			}

			__SKPRINTF("trans_back TakeEffect atk_id=%ld, apos=(%f,%f,%f)\n",
				_attacker.id, apos.x, apos.y, apos.z);
		}
		while(false);
		
		//删除buff
		parent.RemoveFilter(_filter->GetID());
	}
};

class forbid_buff : public StateEffect
{
	int forbid_serial_id = 0;
	public:
	filter_typemask_t GetTypeMask() { return TYPEMASK_FORBID_BUFF; }
	void TakeEffect(object_interface& parent)
	{    
		if (!forbid_serial_id)
		{    
			forbid_serial_id = parent.GetSkillWrapper().ForbidBuff(parent, _data);
		}    
	}    
	void UndoEffect(object_interface& parent)
	{    
		if (forbid_serial_id)
		{
			parent.GetSkillWrapper().UnForbidBuff(parent, forbid_serial_id);
			forbid_serial_id = 0;
		}
	}    
};

//根据位移释放技能
//_data 外参 技能id
//_data2 外参 技能等级, 默认为1
//_param 内参 每移动多少米释放一次
class castSkillByMove : public StateEffect
{
private:
	A3DVECTOR3 _last_pos;
	float _distance_accumulated = 0.f;
	bool _allow_accumulate = false;
private:
	void _CheckMove(object_interface& parent)
	{
		const A3DVECTOR3& cur_pos = parent.GetPos();
		float dis = sqrtf(squared_distance(_last_pos, cur_pos));
		if (dis > 1e-6)
		{
			_distance_accumulated += dis;
			_last_pos = cur_pos;
		}

		int skill_id = (int)_data;
		if (skill_id <= 0)
		{
			return;
		}

		float trigger_distance = _param;
		if (trigger_distance < 1e-6) // 防止配置错误导致死循环
		{
			return;
		}
		
		int skill_level = _data2 > 1.0f ? (unsigned int)_data2 : 1;

		if (_allow_accumulate)
		{
			// 允许累计移动距离，直到达到触发距离
			while (_distance_accumulated >= trigger_distance)
			{
				parent.GetSkillWrapper().BuffSkill(parent, skill_id, skill_level);
				_distance_accumulated -= trigger_distance;
			}
		}
		else
		{
			if (_distance_accumulated >= trigger_distance)
			{
				parent.GetSkillWrapper().BuffSkill(parent, skill_id, skill_level);
				_distance_accumulated = 0.f;
			}
		}
	}

public:
	castSkillByMove()
	{
		_flag = FLAG_DOSETUP;
	}
	virtual filter_eventmask_t GetEventMask() override { return filter::FM_HEARTBEAT; }
	virtual void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter,const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override 
	{
		_last_pos = player.GetPos();
		_distance_accumulated = 0.f;
		_allow_accumulate = false;
	}
	virtual void Heartbeat(object_interface& parent, int tick) override //tick表示本次间隔几秒
	{
		_CheckMove(parent);
	}
	virtual void UndoEffect(object_interface& parent) override
	{
		_CheckMove(parent);
	}
	/*
	virtual bool Save(archive& ar) override
	{
		StateEffect::Save(ar);
		ar << _distance_accumulated;
		return true;
	}
	virtual bool Load(archive& ar) override
	{
		StateEffect::Load(ar);
		ar >> _distance_accumulated;
		return true;
	}
	*/
};

// 施加在敌人身上的挑战标记debuff
// _data 外参 减伤千分比（负值为增伤）
class markChallengeFromCaster : public StateEffect
{
private:
	XID _caster_id;  // 施法者ID
	uint64_t _last_heartbeat_tick = 0;

public:
	markChallengeFromCaster() { _flag = FLAG_DOSETUP; }

	filter_eventmask_t GetEventMask() override { return filter::FM_HEARTBEAT; }

	void Setup(object_interface& player, WMSKILL::Skill* skill, StateFilter* filter, const A3DVECTOR3& caster_pos, const attack_msg *atk_msg) override
	{
		// 从技能信息中获取施法者ID
		if (skill->GetAttackerInfo())
		{
			_caster_id = skill->GetAttackerInfo()->attacker;
		}
	}

	void TakeEffect(object_interface& parent) override
	{
		if (_caster_id.IsValid() && _filter)
		{
			// 通知施法者将我加入挑战列表
			parent.NotifyChallengeTargetAdd(_caster_id, _filter->GetStateId(), _data);
		}
		_last_heartbeat_tick = object_interface::GetTick();
	}

	void UndoEffect(object_interface& parent) override
	{
		if (_caster_id.IsValid())
		{
			// 通知施法者将我从挑战列表移除
			parent.NotifyChallengeTargetRemove(_caster_id);
		}
		_last_heartbeat_tick = 0;
	}

	void Heartbeat(object_interface& parent, int tick) override
	{
		const uint64_t now_tick = object_interface::GetTick();
		if (now_tick - _last_heartbeat_tick >= MILLISEC_TO_TICK(1000))
		{
			if (_caster_id.IsValid() && _filter)
			{
				// 每秒发送保活消息给施法者
				parent.NotifyChallengeTargetHeartbeat(_caster_id, _filter->GetStateId(), _data);
			}
			_last_heartbeat_tick = now_tick;
		}
	}
};

}//end namespace

#endif

