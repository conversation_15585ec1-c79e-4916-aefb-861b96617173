
#include <string>
#include <list>
#include <vector>
#include <map>
#include <queue>
#include <random>

#include <ASSERT.h>
#include "glog.h"
#include "staterule.h"
#include "player.h"
#include "skill.h"
#include "skillwrapper.h"
#include "debugprint.h"
#include "statefilter.h"
#include "effectimp.h"
#include "object.h"
#include "slog.h"
#if 0 //FIXME
#include "statefilter238.h"
#include "statefilter239.h"
#include "statefilter240.h"
#include "statefilter241.h"
#include "statefilter285.h"
#include "statefilter274.h"
#include "statefilter275.h"
#include "statefilter276.h"
#include "statefilter277.h"
#include "statefilter278.h"
#include "statefilter279.h"
#include "statefilter3.h"
#endif
#include "debugtools.h"
#include "mppc.h"
#include "prop_op.h"
#include "controlfilter.h"
#include "funcswitchmgr.h"

bool debug_print = true;

void __ENABLE_SKPRINTF(bool e)
{
	debug_print = e;
}

#if 0
inline float Second2Weakness(object_interface& player)
{
	float weakness_gen =  player.GetProperty()._cur_prop.mp_gen[2][1];
	if (weakness_gen < 1)
	{
		return SECOND_WEAKNESS_RATE;
	}
	if (weakness_gen > 10000)
	{
		return 10000;
	}
	return weakness_gen;
	return SECOND_WEAKNESS_RATE;
}
#endif

namespace WMSKILL
{
void AttackFeedbackManager::RemoveExpired()
{
	//__SKPRINTF("AttackFeedbackManager::RemoveExpired, feedback_map.size()=%u\n", feedback_map.size());
	//前100条怎么也过期了
	if (feedback_map.size() <= 50)
	{
		return;
	}
	unsigned int max = feedback_map.rbegin()->first;
	if (max <= 100)
	{
		return;
	}
	max -= 100;
	FeedbacksMap::iterator it = feedback_map.begin();
	while (it != feedback_map.end())
	{
		if (it->first >= max)
		{
			break;
		}
		feedback_map.erase(it++);
	}
}

bool AttackFeedbackManager::CheckFeedback(unsigned int seq, const int *type, std::vector<XID> *src)
{
	auto it = feedback_map.find(seq);
	if (it == feedback_map.end())
	{
		return false;
	}

	Feedbacks& fbs = it->second;
	for (auto it2 = fbs.begin(), ie2 = fbs.end(); it2 != ie2; ++it2)
	{
		for (int i = 0; i < FEEDBACK_SKILL_COUNT; i++)
		{
			if (it2->mask[i] & type[i])
			{
				src[i].push_back(it2->id);
			}
		}
	}
	feedback_map.erase(it);
	RemoveExpired();
	return true;
}

SkillWrapper::SkillWrapper()
{
	active_skill = 0;
	// 状态相关
	state_mask = 0;
	memset(state_count, 0, sizeof(state_count));
	immune_mask = 0;
	memset(immune_count, 0, sizeof(immune_count));
	status_mask = 0;
	hit_cur = 0;
	hit_inform = 0;
	hit_timeout_tick = 0;
	//control_state = 0;
	//control_end = 0;
	//st4_control = false;
	//suspend = false;
	attack_seq = 0;
	//perform_seq = 0;
	has_perform_weakpoint = false;

	no_control_counter = 0;
	immune_control_counter = 0;
	immune_control_except_hit_back_counter = 0;
	immune_control_except_force_counter = 0;
	immune_control_except_weak_counter = 0;
	immune_control_except_full_pozhan_counter = 0;
	immune_control_except_in_action_counter = 0;
	immune_control_except_in_action_except = 0;
	immune_control_except_no_force_counter = 0;

	stun_timestamp = 0;
	control_time = 0;
	control_type = 0;
	prev_control_type = 0;
	control_begin_tick = 0;
	is_in_zhuatou_control = false;
	control_origin = 0;
	control_can_modify_tick1 = 0;
	control_can_modify_tick2 = 0;
	control_move = false;
	control_need_inform_client = false;
	control_dir = 0;
	zhuatou_need_feedback = false;

	dodge_all_counter = 0;
	dodge_point_attack = 0;
	dmg_to_force_ratio = 0;

	chu_pozhan_timeout = false;

	no_damage_counter = 0;

	parry_yingzhi = 0;

	no_back_counter = 0;

	// 数值效果调整
	heal_ratio = 0;
	energy_ratio = 0;
	rage_ratio = 0;
	pozhan_add_delta = 0;
	pozhan_add_limit = 0;
	// 临时变量
	last_move_tick = object_interface::GetTick();
	move_attacker = false;
	xp_level = 0;
	//feedback_grab = false;
	//throw_type = 0;
	//throw_dist = 0;
	//throw_time = 0;
	//throw_to = false;

	_isWithoutCD = false;
	replisome_damage_ratio = 1000;
	_record_data.clear();
}

SkillWrapper::~SkillWrapper()
{
	if (active_skill)
	{
		delete active_skill;
		active_skill = 0;
	}
	for (StorageMap::iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		delete it->second;
	}
	for (std::set<Skill *>::iterator it = releaseset.begin(); it != releaseset.end(); ++it)
	{
		delete (*it);
	}
}

//char SkillWrapper::PrepareSkill(object_interface& player, int id, int level, int item_id, int item_where, int item_index, bool do_charge, bool force)
//{
//	__SKPRINTF("SkillWrapper::PrepareSkill: id=%d,level=%d,item_id=%d,item_where=%d,item_index=%d,force=%d\n",
//	           id,level,item_id,item_where,item_index,force);
//	if (!active_skill)
//		active_skill = new Skill();
//	if(item_id>0 || player.IsNPCClass() || player.IsPetClass() || force)
//	{
//		if (!SkillFactory::Copy(*active_skill, id, level))
//		{
//			delete active_skill;
//			active_skill = 0;
//			return REASON_UNKNOWN;
//		}
//	}
//	else
//	{
//		StorageMap::iterator it = skillmap.find(id);
//		if(it==skillmap.end())
//		{
//			delete active_skill;
//			active_skill = 0;
//			return REASON_UNKNOWN;
//		}
//		//FIXME: 等级检查，base_level
//		if (!SkillFactory::Copy(*active_skill, id, it->second->GetLevel()))
//		{
//			delete active_skill;
//			active_skill = 0;
//			return REASON_UNKNOWN;
//		}
//	}
//	active_skill->Attach(player, CONTEXT_PERFORM);
//	if (active_skill->GetType()==TYPE_ASSIST || active_skill->GetType()==TYPE_PASSIVE)
//		return REASON_UNKNOWN;
//	//{
//	//	//可能发生移动了
//	//	last_move_tick = object_interface::GetTick();
//	//	move_attacker = false;
//	//	//断连招
//	//	//ClearCombo(player);
//	//	ClearHit(player);
//	//}
//	return active_skill->Prepare(item_id, item_where, item_index, do_charge);
//}

void SkillWrapper::TriggerSubobj(object_interface& subobj, const XID& trigger_target, const subobj_env& env, attack_msg& msg, const std::set<ruid_t>& exclude, std::vector<XID>& hit_list)
{
	std::vector<int> target_list;
	TriggerSubobj(target_list, subobj, trigger_target, env, msg, exclude, hit_list);
}

void SkillWrapper::TriggerSubobj(std::vector<int>& target_list, object_interface& subobj, const XID& trigger_target, const subobj_env& env, attack_msg& msg/*会修改*/, const std::set<ruid_t>& exclude, std::vector<XID>& hit_list)
{
	__SKPRINTF("SkillWrapper::TriggerSubobj:subobj_pid=%d:skill_id=%d\n", msg.subobj_pid, msg.skill_id);
	//NOTE: 这里用到的过程属性只能是常量部分, 否则必须通过env带过来
	const Perform *p = SkillFactory::GetSubobjPerform(msg.subobj_pid);
	if (!p)
	{
		return;
	}

	//尝试创建孙物体
	ThrowSubObj(subobj, msg, trigger_target.IsValid() ? trigger_target : env.prior_target);

	bool need_feedback = false;
	if (env.seq > 0 && env.seq <= env.seq_max)
	{
		memcpy(msg.feedback_type, env.feedback_type, sizeof(msg.feedback_type));
		memcpy(msg.feedback_arg, env.feedback_arg, sizeof(msg.feedback_arg));
		msg.seq = env.seq;
		need_feedback = true;
	}

	//NOTE: 子物体需要设置一些本身属性, 可能还有更多
	msg.attacker_info.pos = subobj.GetPos();
	msg.attacker_info.body_size = subobj.GetBodySize();

	int max_coverage = env.max_coverage;
	if (max_coverage == 0)
	{
		max_coverage = MAX_COVERAGE;
	}
	else if (max_coverage > MAX_COVERAGE)
	{
		if (object_interface::CanSkillBeyondMaxCoverage(msg.skill_id))
		{
			max_coverage = std::min(BEYOND_MAX_COVERAGE, max_coverage);
		}
		else
		{
			max_coverage = MAX_COVERAGE;
		}
	}
	int delay_time = 0;
	if (p->delay_mode == DELAY_NORMAL)
	{
		delay_time = p->delay_time;
	}

	const A3DVECTOR3& self_pos = subobj.GetPos();
	ruid_t team_id = 0;
	if (env.skill_type == TYPE_BLESS_TEAM_PRIO || env.skill_type == TYPE_TREAT_TEAM_PRIO)
	{
		team_id = msg.attacker_info.team.id;
	}

	//float self_size = subobj.GetBodySize();
	switch (p->range_type)
	{
		__SKPRINTF("SkillWrapper::TriggerSubobj::Enchant:subobj_pid=%d:skill_id=%d:range_type=%d\n", msg.subobj_pid, msg.skill_id, p->range_type);
	/*
			case RANGE_TARGET_POINT:
			{
				if (!target.IsValid())
					return;
				A3DVECTOR3 tpos;
				float tsize;
				unsigned short scene_tag, dir;
				bool is_dead;
				if (!subobj.QueryObject(target, tpos, scene_tag, tsize, dir, is_dead))
					return;
				if (scene_tag != subobj.GetSceneTag())
					return;
				float d2 = squared_distance(self_pos, tpos);
				float range = msg.subobj_max_range + self_size + tsize;
				if (d2 < range * range)
					subobj.Enchant(target, msg, delay_time);
				subobj.SendSubobjectTakeEffect(msg.subobj_pid, 0, tpos, 1, &target, msg.run_advance);
				return;
			}
			case RANGE_TARGET_BALL:
			{
				//和技能过程保持一致
				A3DVECTOR3 tpos = target_pos;
				float tsize = 0;
				if (target.IsValid())
				{
					unsigned short scene_tag, dir;
					bool is_dead;
					if (!subobj.QueryObject(target, tpos, scene_tag, tsize, dir, is_dead))
						return;
					if (scene_tag != subobj.GetSceneTag())
						return;
					float d2 = squared_distance(self_pos, tpos);
					float range = msg.subobj_max_range + self_size + tsize;
					if (d2 < range * range)
						subobj.RegionEnchant1(tpos, msg.subobj_affect_radius, msg, max_coverage, &target, delay_time);
				}
				else
					subobj.RegionEnchant1(tpos, msg.subobj_affect_radius, msg, max_coverage, 0, delay_time);
				subobj.SendSubobjectTakeEffect(msg.subobj_pid, 0, tpos, 0, 0, msg.run_advance);
				return;
			}
	*/
	case RANGE_SELF_LINE:
	{
		subobj.RegionEnchant2(target_list, self_pos, subobj.GetDirection(), env.affect_radius, env.affect_length,
		                      msg, max_coverage, hit_list, &env.prior_target,
		                      IsDamageDelay(p->delay_mode), delay_time, team_id, exclude);
		subobj.SendSubobjectTakeEffect(msg.subobj_pid, self_pos, hit_list.size(), (const XID *)&*hit_list.begin(),
		                               p->range_type, env.affect_radius, env.affect_radius2, env.affect_length, env.affect_angle);
		break;
	}
	case RANGE_SELF_SECTOR:
	{
		subobj.RegionEnchant3(target_list, self_pos, subobj.GetDirection(), __cosf((int)env.affect_angle),
		                      env.affect_radius, msg, max_coverage, hit_list, &env.prior_target,
		                      IsDamageDelay(p->delay_mode), delay_time, team_id, exclude);
		subobj.SendSubobjectTakeEffect(msg.subobj_pid, self_pos, hit_list.size(), (const XID *)&*hit_list.begin(),
		                               p->range_type, env.affect_radius, env.affect_radius2, env.affect_length, env.affect_angle);
		break;
	}
	case RANGE_SELF_BALL:
	{
		subobj.RegionEnchant1(target_list, self_pos, env.affect_radius, msg, max_coverage, hit_list, &env.prior_target,
		                      IsDamageDelay(p->delay_mode), delay_time, false, team_id, exclude);
		subobj.SendSubobjectTakeEffect(msg.subobj_pid, self_pos, hit_list.size(), (const XID *)&*hit_list.begin(),
		                               p->range_type, env.affect_radius, env.affect_radius2, env.affect_length, env.affect_angle);
		break;
	}
	case RANGE_SELF_RECT:
	{
		A3DVECTOR3 pos = self_pos;
		pos -= env.affect_length / 2 * subobj.GetDirection();
		subobj.RegionEnchant2(target_list, pos, subobj.GetDirection(), env.affect_radius, env.affect_length,
		                      msg, max_coverage, hit_list, &env.prior_target,
		                      IsDamageDelay(p->delay_mode), delay_time, team_id, exclude);
		subobj.SendSubobjectTakeEffect(msg.subobj_pid, pos, hit_list.size(), (const XID *)&*hit_list.begin(),
		                               p->range_type, env.affect_radius, env.affect_radius2, env.affect_length, env.affect_angle);
		break;
	}
	/*
			case RANGE_OWNER_BALL:
			case RANGE_OWNER_LINE: //TODO
			{
				XID& t = msg.attacker_info.attacker;
				if (!t.IsValid())
					return;
				A3DVECTOR3 tpos;
				unsigned short scene_tag, dir;
				float tsize;
				bool is_dead;
				if (!subobj.QueryObject(t, tpos, scene_tag, tsize, dir, is_dead))
					return;
				if (scene_tag != subobj.GetSceneTag())
					return;
				float d2 = squared_distance(self_pos, tpos);
				float range = self_size + tsize + msg.subobj_max_range;
				if (d2 < range * range)
				{
					if (p->range_type == RANGE_OWNER_BALL)
						subobj.RegionEnchant1(tpos, msg.subobj_affect_radius, msg, max_coverage, 0, delay_time); //FIXME: 给主人?
					else
					{
						A3DVECTOR3 v = tpos - self_pos;
						v.Normalize();
						subobj.RegionEnchant2(self_pos, v, msg.subobj_affect_radius, sqrtf(d2), msg, max_coverage, 0, delay_time);
					}
				}
				subobj.SendSubobjectTakeEffect(msg.subobj_pid, 0, tpos, 0, 0, msg.run_advance);
				return;
			}
			case RANGE_SELF_BALL_RAND:
			{
				subobj.RegionEnchant1(self_pos, msg.subobj_affect_radius, msg, max_coverage, (target.IsValid() ? &target : 0), delay_time, true);
				subobj.SendSubobjectTakeEffect(msg.subobj_pid, 0, self_pos, 0, 0, msg.run_advance);
				return;
			}
			case RANGE_TO_TARGET_LINE:
			{
				A3DVECTOR3 tpos = target_pos;
				if (target.IsValid())
				{
					unsigned short scene_tag, dir;
					float tsize = 0;
					bool is_dead;
					if (!subobj.QueryObject(target, tpos, scene_tag, tsize, dir, is_dead))
						return;
					if (scene_tag != subobj.GetSceneTag())
						return;
				}
				A3DVECTOR3 dir = tpos-self_pos;
				dir.y = 0;
				float len = dir.Magnitude()+0.1; //FIXME: 防止位置重合，有必要吗?
				if (dir.Normalize() != 0)
					subobj.SetDirection(dir, true);
				if (len > msg.subobj_affect_length)
					len = msg.subobj_affect_length;
				subobj.RegionEnchant2(self_pos, subobj.GetDirection(), msg.subobj_affect_radius, len,
				                      msg, max_coverage, (target.IsValid() ? &target : 0), delay_time);
				subobj.SendSubobjectTakeEffect(msg.subobj_pid, 0, self_pos, 0, 0, msg.run_advance);
				return;
			}
	*/
	case RANGE_SELF_LINE_REACHABLE:
	{
		A3DVECTOR3 dest_pos = self_pos + subobj.GetDirection() * env.affect_length;
		A3DVECTOR3 stop_pos;
		subobj.GetLastReachablePos(self_pos, dest_pos, stop_pos, CHECK_Y_DIFF_STRICT, false);
		A3DVECTOR3 offset = stop_pos - self_pos;
		offset.y = 0;
		float real_length = offset.Magnitude();
		if (real_length > 0.1)
		{
			subobj.RegionEnchant2(target_list, self_pos, subobj.GetDirection(), env.affect_radius, real_length,
			                      msg, max_coverage, hit_list, &env.prior_target,
			                      IsDamageDelay(p->delay_mode), delay_time, team_id, exclude);
		}
		subobj.SendSubobjectTakeEffect(msg.subobj_pid, self_pos, hit_list.size(), (const XID *)&*hit_list.begin(),
		                               p->range_type, env.affect_radius, env.affect_radius2, real_length, env.affect_angle);
		break;
	}
	case RANGE_SELF_RING:
	{
		subobj.RegionEnchant4(target_list, self_pos, env.affect_radius, env.affect_radius2, msg, max_coverage, hit_list, &env.prior_target,
		                      IsDamageDelay(p->delay_mode), delay_time, false, team_id, exclude);
		subobj.SendSubobjectTakeEffect(msg.subobj_pid, self_pos, hit_list.size(), (const XID *)&*hit_list.begin(),
		                               p->range_type, env.affect_radius, env.affect_radius2, env.affect_length, env.affect_angle);
		break;
	}
	}

	//技能回馈逻辑
	if (need_feedback)
	{
		checkfeedback_msg cmsg;
		memset(&cmsg, 0, sizeof(cmsg));
		cmsg.seq = msg.seq;
		need_feedback = false;
		for (int i = 0; i < FEEDBACK_SKILL_COUNT; ++i)
		{
			int type = env.feedback_type[i];
			int skill = env.feedback_skill[i];
			int rate = env.feedback_rate[i];
			if (type <= 0 || skill <= 0 || rate <= 0)
			{
				continue;
			}
			if (object_interface::Rand() * 1000 > rate)
			{
				continue;
			}
			need_feedback = true;
			cmsg.type[i] = type;
			cmsg.skill[i] = skill;
			cmsg.target[i] = env.feedback_target[i];
		}
		if (need_feedback)
		{
			int delay_tick = 2 + MILLISEC_TO_TICK(delay_time);//tick1目标收到消息，tick2我收到返回，tick3我收到feedback
			subobj.SendCheckAttackFeedBack(msg.attacker_info.attacker, cmsg, delay_tick);
		}
	}
}

bool SkillWrapper::BeEnchanted(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, int aoe_count, attack_msg& msg)
{
	__SKPRINTF("SkillWrapper::BeEnchanted: target=%ld, src=%ld, skill_id=%d, subobj_pid=%d, aoe_count=%d\n", target.GetID().id,
	           src.id, msg.skill_id, msg.subobj_pid, aoe_count);

	Skill skill;
	if (!SkillFactory::Copy(skill, msg.skill_id, msg.skill_level))
	{
		return false;
	}
	skill.Attach(target, CONTEXT_VICTIM);
	skill.SetAttackMessage(&msg, src); //FIXME: msg
	skill.SetDistance(msg.distance);
	skill.SetChargeRatio(msg.charge_ratio);
	skill.SetLastMovedDistance(msg.last_moved_distance);
	if (aoe_count > 0)
	{
		skill.SetAOECount(aoe_count);
	}
	SetSkillTalent(&skill, msg.skill_modify);

	if (active_skill)
	{
		active_skill->Attach(target, CONTEXT_VICTIM);
	}

	int type = skill.GetType();
	switch (type)
	{
	case TYPE_ATTACK:
	case TYPE_XP:
	case TYPE_NORMAL:
		return BeAttacked(target, src, src_pos, msg, skill);
	case TYPE_TREAT_TEAM_PRIO:
		return BeTreated(target, src, src_pos, msg, skill);
	case TYPE_SUB_CURSE:
		if (msg.subobj_pid > 0)
		{
			return BeCursed(target, src, src_pos, msg, skill);
		}
		return BeAttacked(target, src, src_pos, msg, skill);
	case TYPE_BLESS:
	case TYPE_BLESS_TEAM_PRIO:
		return BeBlessed(target, src, src_pos, msg, skill);
	case TYPE_BLESS_PLAYER:
		if (target.IsPlayerClass() || target.IsPlayerNpc())
		{
			return BeBlessed(target, src, src_pos, msg, skill);
		}
		return false;
	case TYPE_CURSE:
	case TYPE_CURSE_INVINCIBLE:
		return BeCursed(target, src, src_pos, msg, skill);
	case TYPE_SUB_ATTACK:
		if (msg.subobj_pid > 0)
		{
			return BeAttacked(target, src, src_pos, msg, skill);
		}
		return BeCursed(target, src, src_pos, msg, skill);
	default:
		return false;
	}
	return false;
}

bool SkillWrapper::UpgradeAllSkill(object_interface& player, const std::set<int>& canUpgrade, std::map<int, int>& result)
{
	bool ret = false;
	for (StorageMap::iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		for (int i = 0; i < 255; i++)
		{
			Skill *sk = it->second;
			if (canUpgrade.find(sk->GetId()) == canUpgrade.end())
			{
				break;
			}
			if (sk->GetBaseLevel() == 0)
			{
				break;
			}
			if (sk->GetMask() & MASK_ADDON_SKILL)
			{
				break;    // never learn ADDON skill
			}
			int level_to = sk->GetBaseLevel() + 1;
			if (sk->GetStub()->CanLearn(player, level_to) != 0)
			{
				break;
			}
			sk->GetStub()->DoLearnCost(player, level_to);
			sk->SetLevel(level_to, sk->GetAddonLevel());
			sk->SetMask(0);
			//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();
			RefreshPassiveSkill(player, sk);
			result[it->first] = sk->GetBaseLevel();
			ret = true;
		}
	}
	//这里可能会学会新的技能
	if (ret)
	{
		std::map<int, int> branch_result;
		for (std::map<int, int>::iterator it = result.begin(); it != result.end(); ++it)
		{
			player.OnSkillLearnTo(it->first, it->second, false, &branch_result);
		}
		for (std::map<int, int>::iterator it = branch_result.begin(); it != branch_result.end(); ++it)
		{
			result[it->first] = it->second;
		}
	}
	RefreshSkillCapacity(player);
	return ret;
}

int SkillWrapper::Learn(int id, object_interface& player, bool is_upgrade)
{
	Skill *sk = 0;
	int level_to = 1;
	StorageMap::iterator it = skillmap.find(id);
	if (it != skillmap.end())
	{
		sk = it->second;
		if (is_upgrade && sk->GetBaseLevel() == 0)
		{
			return -1;
		}
		if (sk->GetMask() & MASK_ADDON_SKILL)
		{
			return -1;    // never learn ADDON skill
		}
		level_to = sk->GetBaseLevel() + 1;
		if (sk->GetStub()->CanLearn(player, level_to) != 0)
		{
			return -1;
		}
	}
	else
	{
		if (is_upgrade)
		{
			return -1;
		}
		const SkillStub *stub = SkillFactory::GetStub(id);
		if (!stub || stub->CanLearn(player, level_to) != 0)
		{
			return -1;
		}
		sk = SkillFactory::Create(id, level_to);
		if (!sk)
		{
			return -1;
		}
		skillmap[id] = sk;
	}
	sk->GetStub()->DoLearnCost(player, level_to);
	sk->SetLevel(level_to, sk->GetAddonLevel());
	sk->SetMask(0);
	//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();
	RefreshPassiveSkill(player, sk);
	//GLog::formatlog("skill", "learn_skill:player_id=%ld:skill_id=%d:skill_level=%d", player.GetID().id, id, level_to);
	player.OnSkillLearnTo(id, level_to);
	RefreshSkillCapacity(player);
	return sk->GetBaseLevel();
}

int SkillWrapper::__LearnToLevel(int id, int level_to)
{
	//FIXME: 啥都不检查
	Skill *sk = 0;
	StorageMap::iterator it = skillmap.find(id);
	if (it != skillmap.end())
	{
		sk = it->second;
		if (!sk || sk->GetMask() & MASK_ADDON_SKILL)
		{
			return -1;    // never learn ADDON skill
		}
	}
	else
	{
		sk = SkillFactory::Create(id, level_to);
		if (!sk)
		{
			return -1;
		}
		skillmap[id] = sk;
	}
	sk->SetLevel(level_to, sk->GetAddonLevel());
	sk->SetMask(0);
	return sk->GetBaseLevel();
}

int SkillWrapper::LearnToLevel(int id, object_interface& player, int level_to)
{
	//FIXME: 啥都不检查
	int ret = __LearnToLevel(id, level_to);
	if (ret >= 0)
	{
		StorageMap::iterator it = skillmap.find(id);
		ASSERT(it != skillmap.end());
		Skill *sk = it->second;
		ASSERT(sk);
		RefreshPassiveSkill(player, sk);
		GLog::formatlog("skill", "learn_skill:LearnToLevel::player_id=%ld:skill_id=%d:skill_level=%d", player.GetID().id, id, level_to);
		player.OnSkillLearnTo(id, level_to);
		RefreshSkillCapacity(player);
	}
	return ret;
}
void SkillWrapper::RefreshSkillCapacity(object_interface& player)
{
	_refresh_skill_capacity = true;
}

void SkillWrapper::DoRefreshSkillCapacity(object_interface& player)
{
	Skill skill;
	skill.ResetPlayer();
	skill.Attach(player, CONTEXT_QUERY);
	int64_t capacity = 0;
	StorageMap::iterator it = skillmap.begin();
	for (; it != skillmap.end(); ++it)
	{
		Skill *sk = it->second;
		int cap = player.GetSkillCapacity(sk->GetId(), sk->GetBaseLevel());
		__SKPRINTF("SkillWrapper::Calc_SkillFightingCapacity: player:%ld, skill_id:%d, level:%d, cap:%d\n", player.GetID().id, sk->GetId(), sk->GetBaseLevel(), cap);
		capacity += cap;
		/*
		if (sk->GetType() == TYPE_XP) continue;
		for(size_t i = 0; i < sk->PerformCount(); i ++)
		{
			const Perform* pf = sk->GetPerform(i);
			if(!pf) continue;
			//先计算一下保存数值
			skill.ResetCalculateData(); //防止多个过程干扰
			pf->Calculate(skill);
			//TODO:倍率不算么？
			if(pf->damage_type == DAMAGE_PHY)
			{
				capacity += skill.GetPhyDamage() * 10;
			}
			else if(pf->damage_type == DAMAGE_MAG)
			{
				capacity += skill.GetMagDamage() * 10;
			}
		}
		*/
	}
	capacity += player.GetTalentCapacity();
	capacity += player.GetPureBloodedCapacity();
	__SKPRINTF("SkillWrapper::Set_SkillFightingCapacity: player:%ld, capacity:%ld, talent_cap:%d, pureblooded_cap:%d\n",
	           player.GetID().id, capacity, player.GetTalentCapacity(), player.GetPureBloodedCapacity());
	Set_SkillFightingCapacity(player, capacity);
}

bool SkillWrapper::IsInstant(int id)
{
	const SkillStub *stub = SkillFactory::GetStub(id);
	if (!stub)
	{
		return false;
	}
	return stub->IsInstant();
}

bool SkillWrapper::IsDodgeSkill(int id)
{
	return StateRule::GetInstance().IsDodgeSkill(id);
}

//char SkillWrapper::Run(object_interface& player, int& interval, int& next_interval, float charge_ratio)
//{
//	__SKPRINTF("SkillWrapper::Run\n");
//	if(!active_skill)
//	{
//		player.ClearBufSkill();
//		return RUNRET_UNKNOWN_ERR;
//	}
//	if(active_skill->IsTheEnd())
//	{
//		//active_skill->ResetRunAdvance();
//		return RUNRET_END;
//	}
//	active_skill->Attach(player, CONTEXT_PERFORM);
//
//	if (!active_skill->IsStarted())
//	{
//		// 再次检查
//		char ret = CheckCasting(player, active_skill, false);
//		if (ret != 0) {
//			__SKPRINTF("SkillWrapper::Run: error while CheckCasting, retcode=%d\n", ret);
//			player.ClearBufSkill();
//			return RUNRET_UNKNOWN_ERR;
//		}
//		// Start与Run采用不同的返回值体系，要做转换
//		if(ret==0)
//			OnSkillCast(player, active_skill);
//		else if (ret==REASON_OUT_OF_RANGE)
//		{
//			player.ClearBufSkill();
//			ret = RUNRET_OUTOF_RANGE;
//		}
//		else
//		{
//			player.ClearBufSkill();
//			ret = RUNRET_UNKNOWN_ERR;
//		}
//		return ret;
//	}
//
//	char ret = active_skill->Run(interval, next_interval);
//	__SKPRINTF("SkillWrapper::Run: id=%d, interval=%d, next_interval=%d\n", active_skill->GetId(), interval, next_interval);
//	if (ret != RUNRET_END)
//		//active_skill->ResetRunAdvance();
//	if (ret != 0 && ret != RUNRET_RESET_TIMER && ret != RUNRET_END)
//		player.ClearBufSkill();
//	return ret;
//}

char SkillWrapper::CheckCasting(object_interface& player, Skill *skill, bool check_range, bool check_cooldown)
{
	int state_buff = skill->GetStub()->state_mask_buff;
	if (state_buff > 0 && player.HasBuff(state_buff))
	{
		//可以释放
	}
	else
	{
		filter_typemask_t mask = skill->GetStub()->state_mask_premise;
		int mode = skill->GetStub()->state_mask_premise_mode;
		if (mode == STATE_PREMISE_ALL)
		{
			//前提条件，满足所有的掩码
			if (mask != 0 && (GetStateMask() & mask) != mask)
			{
				return REASON_INCORRECT_STATE;
			}
		}
		else if (mode == STATE_PREMISE_ANYONE)
		{
			//前提条件，满足任意一个的掩码
			if (mask != 0 && !(GetStateMask() & mask))
			{
				return REASON_INCORRECT_STATE;
			}
		}
		//限制条件，满足某一个条件
		mask = skill->GetStub()->state_mask_taboo;
		if (mask != 0 && (GetStateMask() & mask) != 0)
		{
			return REASON_INCORRECT_STATE;
		}
	}
	//需要某一个buff可用  可以用于buff_next_skill
	unsigned short need_buff_id = skill->GetStub()->need_buff_id;
	if (need_buff_id > 0 && !player.HasBuff(need_buff_id))
	{
		return REASON_INCORRECT_STATE;
	}
	//存在某个buff不可以释放技能
	const std::vector<int>& limit_skill = skill->GetStub()->buff_limit_skill;
	for (auto& buff_id : limit_skill)
	{
		if (buff_id > 0 && player.HasBuff(buff_id))
		{
			return REASON_INCORRECT_STATE;
		}
	}
	//判定历史buff(元素球)
	if (!player.CheckBuffHistory(skill->GetId()))
	{
		return REASON_INCORRECT_STATE;
	}
	char ret = skill->CheckCasting();
	if (ret)
	{
		return ret;
	}
	if (check_cooldown && !TestCoolTime(player, skill->GetId()))
	{
		return REASON_COOLDOWN;
	}
	if (check_range)
	{
		ret = skill->CheckRange();
		if (ret)
		{
			return REASON_OUT_OF_RANGE;
		}
	}
	return 0;
}

char SkillWrapper::InstantSkill(object_interface& player, int id, const XID& target)
{
	//if (!active_skill)
	//	return REASON_UNKNOWN;
	//active_skill->SetTarget(target);
	//active_skill->Attach(player, CONTEXT_PERFORM);

	//char ret = CheckCasting(player, active_skill, false);
	//if (ret != 0) {
	//	__SKPRINTF("SkillWrapper::InstantSkill: error while CheckCasting, retcode=%d\n", ret);
	//	return ret;
	//}

	//if (active_skill->InstantRun())
	//	OnSkillCast(player, active_skill);
	return 0;
}

char SkillWrapper::InstantSkill(object_interface& player, int id, const A3DVECTOR3& target)
{
	//if (!active_skill)
	//	return REASON_UNKNOWN;
	//active_skill->SetTarget(target);
	//active_skill->Attach(player, CONTEXT_PERFORM);

	//char ret = CheckCasting(player, active_skill, false);
	//if (ret != 0) {
	//	__SKPRINTF("SkillWrapper::InstantSkill: error while CheckCasting, retcode=%d\n", ret);
	//	return ret;
	//}

	//if (active_skill->InstantRun()) {
	//	OnSkillCast(player, active_skill);
	//}
	return 0;
}
/*不增加新技能版本
bool SkillWrapper::OnChangeProf(object_interface& player,const std::vector<int>& old_skill,const std::vector<int>& new_skill)
{
	auto old_it = old_skill.begin();
	bool has_old = (old_it != old_skill.end());
	for(auto new_it = new_skill.begin();new_it != new_skill.end();++new_it)
	{
		int level_to = 1;
		if(has_old)
		{
			StorageMap::iterator it2 = skillmap.find(*old_it);
			if(it2 != skillmap.end())
			{
				Skill * old = it2->second;
				if(old)
				{
					level_to = old->GetBaseLevel();
				}
				else
				{
					++old_it;
					has_old = (old_it != old_skill.end());
					continue;
				}
			}
			else
			{
				++old_it;
				has_old = (old_it != old_skill.end());
				continue;
			}
		}
		StorageMap::iterator it = skillmap.find(*new_it);
		Skill *sk = NULL;
		if (it != skillmap.end())
		{
			sk = it->second;
			if (sk)
			{
				if (sk->GetMask() & MASK_ADDON_SKILL)
					sk = NULL;
			}
		}
		else
		{
			sk = SkillFactory::Create(*new_it, level_to);
			if (sk)
			{
				skillmap[*new_it] = sk;
			}
		}

		if(sk)
		{
			sk->SetLevel(level_to, sk->GetAddonLevel());
			sk->SetMask(0);
		}

		if(has_old)
		{
		      ++old_it;
		      has_old = (old_it != old_skill.end());
		}
	}
	return true;
}
*/
bool SkillWrapper::OnChangeProf(object_interface& player, int old_prof, const std::vector<int>& old_skill, const std::vector<int>& new_skill)
{
	auto old_it = old_skill.begin();
	auto new_it = new_skill.begin();
	auto bak_level_it = skill_level_bak.begin();
	bool has_bak_level = (bak_level_it != skill_level_bak.end());

	std::map<int/*skill_id*/, int/*map*/> _old_skill_info;
	std::map<int/*skill_id*/, int/*map*/> _new_skill_info;

	auto GetLearnedBaseLevel = [this](int skill_id)
	{
		auto it2 = skillmap.find(skill_id);
		if (it2 != skillmap.end())
		{
			Skill *old = it2->second;
			if (old)
			{
				return old->GetBaseLevel();
			}
		}
		return 0;

	};
	for (; new_it != new_skill.end() && old_it != old_skill.end(); ++old_it, ++new_it)
	{
		// 获取技能等级
		int old_skill_id = *old_it;
		int level_to = GetLearnedBaseLevel(old_skill_id);
		if (level_to > 0)
		{
			_old_skill_info[old_skill_id] = level_to;
		}
		else
		{
			level_to = 1;
		}
		// 备份技能等级
		if (has_bak_level)
		{
			*bak_level_it = level_to;
			++ bak_level_it;
			has_bak_level = (bak_level_it != skill_level_bak.end());
		}
		else
		{
			skill_level_bak.push_back(level_to);
		}
		// 新技能
		int new_skill_id = *new_it;
		int ret = __LearnToLevel(new_skill_id, level_to);
		if (ret > 0)
		{
			_new_skill_info[new_skill_id] = level_to;
		}
	}
	while (new_it != new_skill.end())
	{
		// 继续学习新技能
		int new_skill_id = *new_it;
		int level_to =  1; // 默认等级
		if (has_bak_level)
		{
			// 从备份等级恢复
			level_to = *bak_level_it;
			++ bak_level_it;
			has_bak_level = (bak_level_it != skill_level_bak.end());
		}
		int ret = __LearnToLevel(new_skill_id, level_to);
		if (ret > 0)
		{
			_new_skill_info[new_skill_id] = level_to;
		}
		++new_it;
	}
	while (old_it != old_skill.end())
	{
		// 旧职业的技能多于新职业的，要继续备份一下等级
		int old_skill_id = *old_it;
		int old_level = GetLearnedBaseLevel(old_skill_id);
		_old_skill_info[old_skill_id] = old_level;
		//更新技能备份等级
		if (has_bak_level)
		{
			*bak_level_it = old_level;
			++ bak_level_it;
			has_bak_level = (bak_level_it != skill_level_bak.end());
		}
		else
		{
			skill_level_bak.push_back(old_level);
		}
		++old_it;
	}

	SLOG(TRACE, "skill_change_prof")
	.P("roleid", player.GetID().id)
	.P("old_prof", old_prof)
	.P("new_prof", player.GetProf())
	.P("old_skill", _old_skill_info)
	.P("new_skill", _new_skill_info);
	return true;
}
bool SkillWrapper::OnResetProfession(object_interface& player)
{
	return true;
}

void SkillWrapper::ForEachSkill(object_interface& player, const std::function<void(object_interface& player, Skill* skill)>& func)
{
	if (!func)
	{
		return;
	}
	for (StorageMap::iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		func(player, it->second);
	}
}

bool SkillWrapper::IsPassiveSkillOf(object_interface& player, Skill* skill)
{
	if (!skill || skill->GetType() != TYPE_PASSIVE)
	{
		return false;
	}
	if (skill->GetBaseLevel() <= 0)
	{
		return false;
	}
	if (((1 << player.GetProperty().GetProf()) & skill->GetProfessionMask()) == 0)
	{
		return false;
	}
	return true;
}

void SkillWrapper::ExtractPassiveSkillInfo(Skill* skill, int& skill_id, int& level, int& functional_label)
{
	if (!skill)
	{
		return;
	}
	skill_id = skill->GetId();
	level = skill->GetLevel();
	functional_label = skill->GetFunctionalLabel();
}

void SkillWrapper::OnLevelUp(object_interface& player)
{
	//__SKPRINTF("SkillWrapper::OnLevelUp.\n");
	LoadInnateSkills(player);
	CheckSkill(player);
	for (StorageMap::iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		RefreshPassiveSkill(player, it->second);
	}
	RefreshSkillCapacity(player);
}

bool SkillWrapper::EventReset(object_interface& player, bool only_talent, unsigned int functional_label_whitelist_mask)
{
	for (StorageMap::iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		auto can_refresh_f = [functional_label_whitelist_mask](object_interface& player, Skill* skill)
		{
			if (!skill || !skill->GetStub())
			{
				return false;
			}
			bool can_refresh = true;
			auto functional_label = skill->GetStub()->functional_label;
			if (functional_label > 0)
			{
				// 有功能标签的技能，初步认为不能重置
				can_refresh = false;

				if (((1 << (functional_label - 1)) & functional_label_whitelist_mask) != 0x0)
				{
					// 但是该技能正好位于白名单中，那么允许重置
					can_refresh = true;
				}
			}
			if (can_refresh)
			{
				player.DebugNotifyClientResetSkill([&]()
						{
						std::stringstream ss;
						ss << "被动技能(id=" << skill->GetId() << ")重置";
						return ss.str();
						});
			}
			else
			{
				player.DebugNotifyClientResetSkill([&]()
						{
						std::stringstream ss;
						ss << "被动技能(id=" << skill->GetId() << ")重置时跳过(label=" << functional_label << ")";
						return ss.str();
						});
			}
			return can_refresh;
		};
		RefreshPassiveSkill(player, it->second, only_talent, can_refresh_f);
	}
	RefreshSkillCapacity(player);
	return true;
}

bool SkillWrapper::PassiveSkillTakeEffect(object_interface& player, int skill_id, int level)
{
	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, level));
	if (!skill)
	{
		return false;
	}
	if (skill->GetType() != TYPE_PASSIVE)
	{
		return false;
	}
	skill->Attach(player, CONTEXT_PASSIVE);
	//if ( need_active && !skill->player->IsSkillActived(id))
	//      return false;
	//if (skill->NeedSutraActive() && !player.IsSutraSkillActive(skill_id))
	//	return false;
	skill->TakeEffect();
	return true;
}

bool SkillWrapper::PassiveSkillUndoEffect(object_interface& player, int skill_id, int level)
{
	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, level));
	if (!skill)
	{
		return false;
	}
	if (skill->GetType() != TYPE_PASSIVE)
	{
		return false;
	}
	skill->Attach(player, CONTEXT_PASSIVE);
	skill->UndoEffect();
	return true;
}

#ifdef SK_STD_OUTPUT
static void PrintSkillMap(const std::string& prefix, object_interface& player, std::map<int, Skill *>& skillmap)
{
	stringstream ss;
	for (auto it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		ss << it->first << ":";
	}
	__SKPRINTF("%s player:%ld 技能序列:%.*s\n", prefix.c_str(), player.GetID().id, (int)ss.str().size(), ss.str().c_str());
}
#endif

void SkillWrapper::LoadDatabase(object_interface& player, archive& ar)
{
	for (StorageMap::iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		delete it->second;
	}
	skillmap.clear();

	static std::set<unsigned short> limited_longhun_skill{8135, 8168, 8184};
	if (ar.size() > 0)
	{
		// load from database
		unsigned int size = 0;
		char version;
		ar >> version; //TODO: version check
		for (ar >> size; size > 0; --size)
		{
			unsigned short id;
			unsigned char level;
			ar >> id;
			ar >> level;

			if (GET_FUNC_SWITCH(kFuncCodeClearLimitedLonghunSkill))
			{
				if (limited_longhun_skill.find(id) != limited_longhun_skill.end())
				{
					continue;
				}
			}

			Skill *sk = SkillFactory::Create(id, level);
			if (!sk)
			{
				continue;
			}
			sk->SetLevel(level, 0);
			skillmap[id] = sk;

			//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();
		}
		if (version >= 2)
		{
			skill_level_bak.clear();
			int64_t bak_size = 0;
			for (ar >> bak_size; bak_size > 0; --bak_size)
			{
				unsigned char level;
				ar >> level;
				skill_level_bak.push_back(level);
			}
		}
	}

	for (StorageMap::iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		player.OnSkillLearnTo(it->first, it->second->GetBaseLevel(), false);
	}

#ifdef SK_STD_OUTPUT
	PrintSkillMap("before load innate skills", player, skillmap);
#endif

	LoadInnateSkills(player);
	CheckSkill(player);
	LoadInnateSkills(player); //again, 防止CheckSkill中删掉某些高等级的天生技能，但这些技能的低等级却是有效的
	CheckSkill(player);

#ifdef SK_STD_OUTPUT
	PrintSkillMap("after load innate skills", player, skillmap);
#endif
}

void SkillWrapper::StoreDatabase(object_interface& player, archive& ar )
{
	unsigned int size = 0;
	ar << (char)TALENT_VERSION;
	for (StorageMap::const_iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		if (it->second->GetMask() == 0 && it->second->GetBaseLevel())
		{
			size++;
		}
	}
	ar << size;
	for (StorageMap::const_iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		if (it->second->GetMask() == 0 && it->second->GetBaseLevel())
		{
			ar << (unsigned short)(*it).first;
			ar << (char)(*it).second->GetBaseLevel();
			//GLog::formatlog("skill", "save_skill:player_id=%ld:skill_id=%d:skill_level=%d", player.GetID().id, (*it).first, (*it).second->GetBaseLevel());
		}
	}
	ar << (int64_t)skill_level_bak.size();
	for (auto level : skill_level_bak)
	{
		ar << level;
	}
}

void SkillWrapper::StorePartial(object_interface& player, archive& ar )
{
	unsigned short size = 0;
	for (StorageMap::const_iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		if (it->second->GetBaseLevel() && it->second->GetMask() == 0)
		{
			size++;
		}
	}
	ar << size;
	for (StorageMap::const_iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		if (it->second->GetBaseLevel() && it->second->GetMask() == 0)
		{
			ar << (unsigned short)((*it).first);
			ar << (char)((*it).second->GetBaseLevel());
			//GLog::formatlog("skill", "client_skill:player_id=%ld:skill_id=%d:skill_level=%d", player.GetID().id, (*it).first, (*it).second->GetBaseLevel());
		}
	}
}

void SkillWrapper::LoadInnateSkills(object_interface& player)
{
	int prof = player.GetProf();

	const std::vector<int>& vi = SkillFactory::GetInnateSkillID(prof);
	for (std::vector<int>::const_iterator it = vi.begin(); it != vi.end(); ++it)
	{
		int skill_id = *it;
		const SkillStub *stub = SkillFactory::GetStub(skill_id);
		if (stub && skillmap.find(skill_id) == skillmap.end())
		{
			//不会就从1级开始学, 后面靠升级
			Skill *sk = SkillFactory::Create(skill_id, 1);
			if (!sk)
			{
				continue;
			}
			sk->SetBaseLevel(1);
			skillmap[skill_id] = sk;
			//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();
		}
	}
}

void SkillWrapper::GetSkillLevels(std::map<int, int>& skills) const	// skill_id -> level
{
	//FIXME: 对于base_level==0的情况要处理
	StorageMap::const_iterator it = skillmap.begin();
	for (; it != skillmap.end(); ++it)
	{
		skills[it->first] = (*it).second->GetLevel();
	}
}
int SkillWrapper::GetSkillLevel(int id) const
{
	//FIXME: 对于base_level==0的情况要处理
	StorageMap::const_iterator it = skillmap.find(id);
	if (it != skillmap.end())
	{
		return (*it).second->GetLevel();
	}
	return 0;
}

int SkillWrapper::GetSkillBaseLevel(int id) const
{
	StorageMap::const_iterator it = skillmap.find(id);
	if ( it != skillmap.end() )
	{
		return (*it).second->GetBaseLevel();
	}
	return 0;
}

int SkillWrapper::GetJudgeType(int id)
{
	const SkillStub *stub = SkillFactory::GetStub(id);
	if (!stub)
	{
		return -1;
	}
	return stub->judge_type;
}

char SkillWrapper::GetType( int id)
{
	const SkillStub *stub = SkillFactory::GetStub(id);
	if (!stub)
	{
		return -1;
	}
	return stub->type;
}

bool SkillWrapper::IsDragonbornSkill(int id)
{
	const SkillStub *stub = SkillFactory::GetStub(id);
	if (!stub)
	{
		return false;
	}
	return stub->origin == ORIGIN_DRAGONBORN;
}

bool SkillWrapper::IsPlayerSkill(int skill_id)
{
	const SkillStub *stub = SkillFactory::GetStub(skill_id);
	if (!stub)
	{
		return false;
	}
	return stub->origin != ORIGIN_PET && stub->origin != ORIGIN_RETINUE;
}

//存在一些技能，base_level==0, 仅有addon_level, 这种技能不能使用，仅用于保存addon等级
int SkillWrapper::Upgrade(int id, unsigned int level, object_interface& player)
{
	Skill *sk = 0;
	StorageMap::iterator it = skillmap.find(id);
	if (it != skillmap.end())
	{
		sk = it->second;
	}
	else
	{
		sk = SkillFactory::Create(id, 0);
		if (!sk)
		{
			return -1;
		}
		skillmap[id] = sk;
	}
	sk->SetLevel(sk->GetBaseLevel(), sk->GetAddonLevel() + level);
	//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();

	player.SendClientSkillAddon(id, sk->GetAddonLevel());
	RefreshPassiveSkill(player, sk);
	RefreshSkillCapacity(player);
	player.OnSkillAddon(id, level, true);
	return 0;
}

int SkillWrapper::Degrade(int id, unsigned int level, object_interface& player)
{
	StorageMap::iterator it = skillmap.find(id);
	if (it != skillmap.end())
	{
		Skill *sk = it->second;
		ASSERT(sk->GetAddonLevel() >= (int)level);
		sk->SetLevel(sk->GetBaseLevel(), sk->GetAddonLevel() - level);
		//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();

		player.SendClientSkillAddon(id, it->second->GetAddonLevel());
		RefreshPassiveSkill(player, sk);
		RefreshSkillCapacity(player);
		player.OnSkillAddon(id, level, false);
		return 0;
	}
	return -1;
}

int SkillWrapper::InsertSkill(int id, unsigned int level, object_interface& player)
{
	ASSERT(level > 0);
	Skill *sk = 0;
	StorageMap::iterator it = skillmap.find(id);
	if (it != skillmap.end())
	{
		sk = it->second;
		if (sk->GetBaseLevel()) //两个装备不能同时insert同一技能
		{
			return -1;
		}
	}
	else
	{
		sk = SkillFactory::Create(id, 0);
		if (!sk)
		{
			return -1;
		}
		skillmap[id] = sk;
	}
	sk->SetLevel(level, sk->GetAddonLevel()); //装备附加技能会使用base_level
	//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();

	sk->SetMask(MASK_ADDON_SKILL);
	player.SendClientExtraSkill(id, sk->GetBaseLevel()); //FIXME:
	RefreshPassiveSkill(player, sk);
	RefreshSkillCapacity(player);
	return 0;
}

int SkillWrapper::RemoveSkill(int id, unsigned int level, object_interface& player, bool force)
{
	StorageMap::iterator it = skillmap.find(id);
	if (it != skillmap.end())
	{
		Skill *sk = it->second;
		if (force || (sk->GetMask() & (MASK_ADDON_SKILL | MASK_DYNAMIC_SKILL)))
		{
			/*
			//bool is_dynamic_skill = sk->GetMask() & MASK_DYNAMIC_SKILL;
			sk->SetLevel(0, sk->GetAddonLevel());
			sk->SetMask(0);
			if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();

			player.SendClientExtraSkill(id, 0);
			//驱除被动效果
			if (sk->GetType()==TYPE_PASSIVE && ((1<<player.GetProperty().GetProf()) & sk->GetProfessionMask()))
			{
				unique_ptr<Skill> skill = SkillFactory::Create(id, sk->GetLevel());
				if (skill)
				{
					skill->Attach(player, CONTEXT_PASSIVE);
					skill->UndoEffect();
				}
			}
			if (sk->GetAddonLevel() == 0)
			*/
			if ( __ClearBaseLevel(id, sk, player))
			{
				skillmap.erase(it);
				//delete sk; //没有delete, 防止技能使用过程中被Remove
				releaseset.insert(sk);//这里已经修复了
			}
			RefreshSkillCapacity(player);
			return 0;
		}
	}
	return -1;
}

void SkillWrapper::SendAllExtraAddonSkill(object_interface& player) const
{
	//实际上是解决协议顺序问题，装备初始化的时候会添加附加技能，但此时客户端尚未初始化完毕，
	//发送player_skill_addon/player_extra_skill协议会被丢掉
	//所以选择在发送skill_data后再次发送所有player_skill_addon/player_extra_skill
	for (StorageMap::const_iterator it = skillmap.begin(); it != skillmap.end(); ++it)
	{
		Skill *sk = it->second;
		if (sk->GetMask() & MASK_ADDON_SKILL)
		{
			ASSERT(sk->GetBaseLevel() > 0);
			player.SendClientExtraSkill(sk->GetId(), sk->GetBaseLevel());
		}
		if (sk->GetAddonLevel() > 0)
		{
			player.SendClientSkillAddon(sk->GetId(), sk->GetAddonLevel());
		}
	}
}

void SkillWrapper::SendStateMask(object_interface& player) const
{
	player.SendClientSkillNotify(SNOTIFY_UPDATEMASK, state_mask);
}

int SkillWrapper::CastRune(int skill_id, int level, int cooldown_id, object_interface& player)
{
	__SKPRINTF("SkillWrapper::CastRune: skill_id=%d, level=%d, cooldown_id=%d\n", skill_id, level, cooldown_id);

	//如果没有指定cooldown_id则查找技能对应cooldown_id
	if (cooldown_id == 0 || cooldown_id == -1)
	{
		const SkillStub *s = SkillFactory::GetStub(skill_id);
		if (!s)
		{
			return -1;
		}
		cooldown_id = s->cooldown_id;
	}

	if (cooldown_id != 0 && cooldown_id != -1)
	{
		if (!player.TestCoolDown(cooldown_id + COOLINGID_BEGIN))
		{
			return -1;
		}
	}

	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, level));
	if (!skill)
	{
		return -1;
	}
	skill->Attach(player, CONTEXT_VICTIM);
	skill->SetCastSelf();
	//if (skill->NeedSutraActive() && !player.IsSutraSkillActive(skill_id))
	//	return -1;
	int _;
	skill->StateAttack(NULL, _);

	if (cooldown_id != 0 && cooldown_id != -1)
	{
		player.SetCoolDown(cooldown_id + COOLINGID_BEGIN, skill->GetCooldownTime());
	}
	player.OnSkillCast(skill_id, XID());

	return 0;
}

int SkillWrapper::Forget(bool all, object_interface& player)
{
	//FIXME: 暂时实现, 用于调试命令
	for (StorageMap::iterator it = skillmap.begin(), ie = skillmap.end(); it != ie; ++it)
	{
		if (it->second->GetMask() || it->second->GetBaseLevel() == 0)
		{
			continue;
		}
		const SkillStub *stub = SkillFactory::GetStub(it->first);
		if (!stub || (!all && !(stub->profession_mask & (1U << player.GetProperty().GetProf()))))
		{
			continue;
		}
		if (stub->type == TYPE_PASSIVE)
		{
			unique_ptr<Skill> skill(SkillFactory::Create(it->first, it->second->GetLevel()));
			if (skill)
			{
				skill->Attach(player, CONTEXT_PASSIVE);
				skill->UndoEffect();
			}
		}
		it->second->SetBaseLevel(0);
		it->second->SetLevel(0, it->second->GetBaseLevel());
		//if (it->second->GetType() == TYPE_XP) xp_level = it->second->GetLevel();
	}
	return 0;
}

void SkillWrapper::SetSkillTalent(Skill *skill, const int *list)
{
	for (int i = 0; i < SKILL_TALENT_COUNT; i++)
	{
		skill->SetTalent(i, list[i]);
	}
}

bool SkillWrapper::IsPlayerAttacker(const attacker_info_t& ainfo)
{
	return ainfo.attacker_mode & attacker_info_t::AM_PLAYER_NPC || ainfo.attacker.IsPlayer();
}

#define BETWEEN(a, min, max) \
	if (a < min) \
	{ \
		a = min; \
	} \
	else if (a > max) \
	{ \
		a = max; \
	}


//def:防御 attack_pierce:破甲 creature_dmg:角色攻击力
static inline float CalcDef(float def, float attack_pierce, int creature_dmg)
{
	BETWEEN(def, 0, 3 * creature_dmg);
	def = def * (1.0f - attack_pierce);
	return def;
}

static inline float NormalDamage(float dmg, int creature_dmg, float def, int chess_damage_arg)
{
	if (chess_damage_arg > 0)
	{
		if (chess_damage_arg + def < 1e-5)
		{
			return dmg;
		}
		return dmg * chess_damage_arg / (chess_damage_arg + def);
	}
	float a = creature_dmg + 2 * def;
	if (a < 1e-5)
	{
		return 0.f;
	}
	return dmg * creature_dmg / a;
}

//creature_dmg:角色攻击力 skill_dmg:技能攻击力 skill_ratio:技能伤害系数 def:防御 elemental_ratio:元素伤害系数
static inline float CalcDamage1(int base_creature_dmg, int creature_dmg, int64_t skill_dmg, int skill_ratio, float def, float elemental_ratio, int chess_damage_arg = 0)
{
	float dmg = (base_creature_dmg * 0.001f * skill_ratio + skill_dmg) * elemental_ratio;
	return NormalDamage(dmg, creature_dmg, def, chess_damage_arg);
}

static const float DAMAGE_WEAK_RATIO = 0.33;

//攻击者对防御力过低的玩家造成的伤害要降低一些
//creature_dmg:角色攻击力 skill_dmg:技能攻击力 skill_ratio:技能伤害系数 base_def:原始防御 attack_pierce:破甲
static inline float CalcDamage_weak(int base_creature_dmg, int creature_dmg, int64_t skill_dmg, int skill_ratio, float base_def, float attack_pierce)
{
	float dmg_src = base_creature_dmg * 0.001f * skill_ratio + skill_dmg;
	float def = CalcDef(base_def, attack_pierce, creature_dmg);
	float dmg = NormalDamage(dmg_src, creature_dmg, def, 0);

	float x0 = base_creature_dmg * DAMAGE_WEAK_RATIO;
	float def_x0 = CalcDef(x0, attack_pierce, creature_dmg);
	float dmg_x0 = NormalDamage(dmg_src, creature_dmg, def_x0, 0);

	return (dmg - dmg_x0) * 0.1 + dmg_x0;
}

//dmg:CalcDamage1的伤害 creature_dmg_add:角色伤害增加 dmg_redu:角色伤害减少
static inline float CalcDamage2(float dmg, int creature_dmg_add, int64_t dmg_redu)
{
	float ratio = 1.0f + creature_dmg_add * 0.001f - dmg_redu * 0.001f;
	if (ratio < 1e-5)
	{
		return 0.f;
	}
	return dmg * ratio;
}

static inline float GetSevenCrimeDamageRate(int crime_add, int crime_redu)
{
	crime_redu = std::min(crime_redu, crime_add * 6);
	int crime = crime_add + crime_redu;
	if (crime > 0)
	{
		return 2.0f * crime_add / crime;
	}
	return 1.0f;
}

float SkillWrapper::SimpleAttackDamage(object_interface& victim, int skill_damage_type, int damage_value, attack_msg tmp_msg)
{
	int _, __ = 0; //无实际用处
	if (skill_damage_type == 0)
	{
		tmp_msg.skill_damage_type = DAMAGE_PHY;
		tmp_msg.skill_physic_damage = damage_value;
	}
	else
	{
		tmp_msg.skill_damage_type = DAMAGE_MAG;
		tmp_msg.skill_magic_damage = damage_value;
	}
	tmp_msg.skill_damage_rate = 1;	//技能伤害倍率
	tmp_msg.attack_pierce = 0;		//破甲
	tmp_msg.skill_physic_ratio = 0;
	tmp_msg.skill_magic_ratio = 0;
	for (int i = 0; i < 4; i ++)
	{
		tmp_msg.skill_element_damage[i] = 0;
		tmp_msg.skill_element_ratio[i] = 0;
	}
	tmp_msg.attacker_info.attacker_mode = 0;
	tmp_msg.attack_crit = 0;		//暴击
	tmp_msg.skill_crit = 0;
	for (int i = 0; i < ATTACK_TYPEMASK_COUNT; ++i)
	{
		tmp_msg.attack_typemask[i] = 0;
	}
	return AttackDamage(victim, A3DVECTOR3(), tmp_msg, _, __, (int)ORIGIN_LEARN);
}

float SkillWrapper::AttackDamage(object_interface& victim, const A3DVECTOR3& src_pos, const attack_msg& msg, int& flag, int& feedback, int skill_origin)
{
	/*
	 * 无属性原伤 = (攻击力*物理或法术技能倍率+物理或法术技能固定伤害)*技能总伤害倍率
	 * 主元素原伤 = (主元素攻击*主元素伤害倍率+主元素技能固定伤害)*技能伤害总倍率
	 * 副元素原伤 = (副元素攻击力*副元素伤害倍率+副元素技能固定伤害)*技能伤害总倍率
	 *
	 * 无属性伤害 = 无属性原伤*(攻击者(攻击)/(攻击者(攻击)+2*被攻击者(防御))
	 * 元素属性伤害 = 元素原伤*(攻击者(元素攻击)/(攻击者(元素攻击)+2*被攻击者(对应元素防御))
	 *
	 * 减免前总伤害 = ∑(属性伤害*(1-被攻击者某属性伤害减免率+攻击者某属性伤害加深率)）+ 无属性伤害*(1-被攻击者无属性伤害减免率+攻击者无属性伤害加深率)
	 * 减免后总伤害 = 减免前总伤害*(1+攻击者伤害加深-被攻击者伤害减免+攻击者特殊伤害加深
	 *
	 * 暴击伤害 = 减免后总伤害*(1+攻击者暴击伤害加深-被攻击者暴击伤害减免)
	 *
	 */

	int pierce_inc = msg.attack_pierce_inc;
	BETWEEN(pierce_inc, -1000, 1000);
	float attack_pierce = (float)msg.attack_pierce * (1 + (float)pierce_inc * 0.001f) * 0.001f;
	float pierce_redu = Get_curPierceRedu(victim) * 0.001f;
	attack_pierce -= pierce_redu;
	BETWEEN(attack_pierce, 0, 0.95);

	//总伤害
	float total_dmg = 0.f;
	float _ignore_damage_prop_ratio = GetIgnoreReduDamagePropRatio();

	//物理伤害
	if (msg.skill_damage_type == DAMAGE_PHY)
	{
		float dmg = 0;
		float base_def = Get_curPhyDef(victim);
		if (base_def < msg.base_attack_physic * DAMAGE_WEAK_RATIO && victim.NeedDamageWeak())
		{
			dmg = CalcDamage_weak(msg.base_attack_physic, msg.attack_physic, msg.skill_physic_damage, msg.skill_physic_ratio, base_def, attack_pierce);
		}
		else
		{
			float def = CalcDef(base_def, attack_pierce, msg.attack_physic);
			dmg = CalcDamage1(msg.base_attack_physic, msg.attack_physic, msg.skill_physic_damage, msg.skill_physic_ratio, def, 1.f, _chess_damage_arg);
		}
		int64_t dmg_redu = Get_PhyDamRedu(victim) *  _ignore_damage_prop_ratio;
		total_dmg += CalcDamage2(dmg, msg.attack_physic_damage_add, dmg_redu);
	}
	//法术伤害
	else if (msg.skill_damage_type == DAMAGE_MAG)
	{

		float dmg = 0;
		float base_def = Get_curMagDef(victim);
		if (base_def < msg.base_attack_magic * DAMAGE_WEAK_RATIO && victim.NeedDamageWeak())
		{
			dmg = CalcDamage_weak(msg.base_attack_magic, msg.attack_magic, msg.skill_magic_damage, msg.skill_magic_ratio, base_def, attack_pierce);
		}
		else
		{
			float def = CalcDef(base_def, attack_pierce, msg.attack_magic);
			dmg = CalcDamage1(msg.base_attack_magic, msg.attack_magic, msg.skill_magic_damage, msg.skill_magic_ratio, def, 1.f, _chess_damage_arg);
		}
		int64_t dmg_redu = Get_MagDamRedu(victim) * _ignore_damage_prop_ratio;
		total_dmg += CalcDamage2(dmg, msg.attack_magic_damage_add, dmg_redu);
	}

	//四种元素伤害 (注: 元素伤害未投入使用)
	float elemental_ratio[4] = {0.5f, 0.5f, 0.5f, 0.5f};
	if (msg.attack_element_type >= 1 && msg.attack_element_type <= 4)
	{
		elemental_ratio[msg.attack_element_type - 1] = 1.0f;
	}
	int elemental_def[4] = {Get_curElementalDef1(victim), Get_curElementalDef2(victim), Get_curElementalDef3(victim), Get_curElementalDef4(victim)};
	int elemental_redu[4] = {0, 0, 0, 0};
	elemental_redu[0] = _ignore_damage_prop_ratio * Get_ElementalDamRedu1(victim);
	elemental_redu[1] = _ignore_damage_prop_ratio * Get_ElementalDamRedu2(victim);
	elemental_redu[2] = _ignore_damage_prop_ratio * Get_ElementalDamRedu2(victim);
	elemental_redu[3] = _ignore_damage_prop_ratio * Get_ElementalDamRedu3(victim);
	for (int i = 0; i < 4; i ++)
	{
		float def = CalcDef(elemental_def[i], attack_pierce, msg.attack_element_damage[i]);
		float dmg = CalcDamage1(msg.attack_element_damage[i], msg.attack_element_damage[i], msg.skill_element_damage[i], msg.skill_element_ratio[i], def, elemental_ratio[i], _chess_damage_arg);
		total_dmg += CalcDamage2(dmg, msg.attack_element_damage_add[i], elemental_redu[i]);
	}

	total_dmg *= msg.skill_damage_rate;//技能最终伤害比率
	if (!victim.PercentHp(25))
	{
		//灭杀，对生命低于25%的目标增伤
		total_dmg *= 1 + msg.attack_deadly_damage_add * 0.001f;
	}

	float heir_rate = 1;
	if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_HEIR)
	{
		heir_rate -= Get_heirDamRedu(victim) * 0.001f;
	}
	if (victim.IsHeir())
	{
		heir_rate += msg.heir_dam_add * 0.001f;
	}
	total_dmg *= heir_rate;
	//计算总伤害倍率
	float dmg_ratio = 1;

	dmg_ratio *= 1 + msg.attack_damage_add * 0.001f - Get_DamRedu(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add2 * 0.001f - Get_DamRedu2(victim) * _ignore_damage_prop_ratio * 0.001f;
	//dmg_ratio *= 1 + msg.attack_damage_add3 * 0.001f - Get_DamRedu3(victim) * 0.001f; // 延后处理
	dmg_ratio *= 1 + msg.attack_damage_add4 * 0.001f - Get_DamRedu4(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add5 * 0.001f - Get_DamRedu5(victim)  * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add6 * 0.001f - Get_DamRedu6(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add7 * 0.001f - Get_DamRedu7(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add8 * 0.001f - Get_DamRedu8(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add9 * 0.001f - Get_DamRedu9(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add10 * 0.001f - Get_DamRedu10(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add11 * 0.001f - Get_DamRedu11(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add13 * 0.001f - Get_DamRedu13(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add12 * 0.001f - Get_DamRedu12(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add14 * 0.001f - Get_DamRedu14(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add15 * 0.001f - Get_DamRedu15(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add16 * 0.001f - Get_DamRedu16(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add17 * 0.001f - Get_DamRedu17(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add18 * 0.001f - Get_DamRedu18(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add19 * 0.001f - Get_DamRedu19(victim) * _ignore_damage_prop_ratio * 0.001f;
	dmg_ratio *= 1 + msg.attack_damage_add20 * 0.001f - Get_DamRedu20(victim) * _ignore_damage_prop_ratio * 0.001f;

	if (IsPlayerAttacker(msg.attacker_info))
	{
		if (victim.IsPlayerClass() || victim.IsPlayerNpc())
		{
			//玩家攻击玩家，计算破魂和罡体
			int pvp_res_level = Get_curPVPResLevel(victim);
			float pvp_dmg_ratio = 1 + ((float)(msg.pvp_level - pvp_res_level)) / (2000 + std::max(msg.pvp_level, pvp_res_level));
			BETWEEN(pvp_dmg_ratio, 0.5f, 1.5f);
			dmg_ratio *= pvp_dmg_ratio;

			//
			dmg_ratio *= 1 + (msg.pvp_dam_add - Get_pvpDamRedu(victim)) / 1000.f;
			dmg_ratio *= 1 + (msg.pvp_dam_add2 - Get_pvpDamRedu2(victim)) / 1000.f;

			//性别伤害
			if (msg.attacker_info.is_male == victim.IsMale())
			{
				dmg_ratio *= 1 + msg.attack_same_sex_damage_add * 0.001f - Get_curSameSexDamRedu(victim) * _ignore_damage_prop_ratio * 0.001f;
			}
			else
			{
				dmg_ratio *= 1 + msg.attack_opposite_sex_damage_add * 0.001f - Get_curOppositeSexDamRedu(victim) * _ignore_damage_prop_ratio * 0.001f;
			}
		}
		else
		{
			//玩家攻击npc，有额外增伤
			dmg_ratio *= 1 + msg.attack_npc_damage_add * 0.001f;
		}
	}
	else if (victim.IsPlayerClass() || victim.IsPlayerNpc() || victim.IsMech())
	{
		//npc攻击玩家，有额外减伤
		dmg_ratio *= (1 - Get_curNpcDamRedu(victim) * 0.001f) * (1 - Get_curNpcDamRedu2(victim) * 0.001f);
	}
	total_dmg *= dmg_ratio;

	//计算暴击
	float crit_rate = 0.0f;
	if (skill_origin == ORIGIN_SEVEN_CRIME)
	{
		crit_rate = 200.0f + msg.skill_crit - victim.GetCrimeSacrificeAddSkillCritRedu();//七宗罪技能的暴击概率单独判断
		total_dmg *= GetSevenCrimeDamageRate(msg.attack_seven_crime_add, Get_curSevenCrimeRedu(victim));
	}
	else if (skill_origin == ORIGIN_SEVEN_CRIME_BUFF_CAST)
	{
		crit_rate = 200.0f + msg.skill_crit - victim.GetCrimeSacrificeAddBuffSkillCritRedu();//七宗罪技能的暴击概率单独判断
		total_dmg *= GetSevenCrimeDamageRate(msg.attack_seven_crime_add, Get_curSevenCrimeRedu(victim));
	}
	else
	{
		crit_rate = msg.attack_crit + msg.skill_crit - Get_curCritRes(victim);
	}
	float random_number = object_interface::Rand() * 1000.0f;
	if (random_number < crit_rate)
	{
		float crit_ratio = msg.attack_crit_ratio * 0.001f - Get_curCritRatioRes(victim) * 0.001f;
		BETWEEN(crit_ratio, 1.0f, 2.5f);
		total_dmg *= crit_ratio;
		flag |= ATTACK_FLAG_CRIT;
		feedback |= FEEDBACK_CRIT;
	}

	//typemask增伤
	float typemask_rate = 1;
	for (int i = 0; i < ATTACK_TYPEMASK_COUNT; ++i)
	{
		if (msg.attack_typemask[i] == 0)
		{
			break;
		}
		if (msg.attack_typemask[i] & state_mask)
		{
			typemask_rate += msg.attack_typemask_damage_add[i] / 1000.f;
		}
	}
	total_dmg *= typemask_rate;

	//全能属性增减伤
	int victim_versatility_rating = Get_curVersatilityRating(victim);
	total_dmg *= (1.0f + msg.attack_versatility_rating / (62500.0f + msg.attack_versatility_rating)) * (1.0f - victim_versatility_rating / (50000.f + victim_versatility_rating));

	//特殊增减伤
	int special_attack_damage_redu = Get_SpecialDamRedu(victim);
	total_dmg *= 1.0f + msg.special_attack_damage_add * 0.001f - special_attack_damage_redu * 0.001f;

	//主人增伤
	total_dmg *= 1.0f + msg.master_attack_damage_add * 0.001f;

	float final_dmg = total_dmg * (msg.attack_damage_rate + 1000) * 0.001f;

	float replisome_rate = 1;
	if ( msg.replisome_dam_ratio > 0 && msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_REPLISOME)
	{
		replisome_rate = msg.replisome_dam_ratio * 0.001f;
	}
	final_dmg *= replisome_rate;

	// 挑战减伤逻辑 - 在最终伤害计算前添加
	{
		// 检查攻击者是否在我的挑战列表中
		int damage_reduce = 0;
		if (HasChallengeTarget(msg.attacker_info.attacker.id, damage_reduce))
		{
			// 将damage_reduce数值clamp到-9000~1000
			BETWEEN(damage_reduce, -9000, 1000);
			final_dmg *= (1.0f - (float)damage_reduce / 1000.0f);
		}
	}

	if (final_dmg < 1)
	{
		final_dmg = 1;
	}
	return final_dmg;
}

float SkillWrapper::CalcTreat(object_interface& target, const attack_msg& msg, int& flag)
{
	float total_point = 0.f;

	//物理伤害
	if (msg.skill_damage_type == DAMAGE_PHY)
	{
		float dmg = CalcDamage1(msg.base_attack_physic, msg.attack_physic, msg.skill_physic_damage, msg.skill_physic_ratio, 0/*def*/, 1.0f);
		total_point += CalcDamage2(dmg, msg.attack_physic_damage_add, 0);
	}
	//法术伤害
	else if (msg.skill_damage_type == DAMAGE_MAG)
	{
		float dmg = CalcDamage1(msg.base_attack_magic, msg.attack_magic, msg.skill_magic_damage, msg.skill_magic_ratio, 0/*def*/, 1.0f);
		total_point += CalcDamage2(dmg, msg.attack_magic_damage_add, 0);
	}

	total_point *= msg.skill_damage_rate;//技能最终伤害比率

	//计算总伤害倍率
	float dmg_ratio = 1 + msg.attack_damage_add * 0.001f;
	total_point *= dmg_ratio;

	//计算暴击
	float crit_rate = msg.attack_crit + msg.skill_crit;
	float random_number = object_interface::Rand() * 1000.0f;
	if (random_number < crit_rate)
	{
		float crit_ratio = msg.attack_crit_ratio * 0.001f;
		BETWEEN(crit_ratio, 1.0f, 2.5f);
		total_point *= crit_ratio;
		flag |= ATTACK_FLAG_CRIT;
	}

	float final_point = total_point * (msg.attack_damage_rate + 1000) * 0.001f;
	if (final_point < 0)
	{
		final_point = 0;
	}
	return final_point;
}

filter_typemask_t SkillWrapper::CalculateMask(filter_typemask_t mask, bool add, int *count)
{
	filter_typemask_t middle = 1;
	filter_typemask_t value = 0;
	for (int i = 0; i < 64; ++i)
	{
		if (mask & middle)
		{
			if (add)
			{
				count[i] += 1;
			}
			else
			{
				if (count[i] > 0)
				{
					count[i] -= 1;
				}
			}
		}

		if (count[i] > 0)
		{
			value += middle;
		}

		middle <<= 1;
	}

	//__SKPRINTF("SkillWrapper::CalculateMask: this=%ld mask=%d add=%d count= %d %d %d %d %d\n",
	//			(long)this, mask, add, count[0], count[1], count[2], count[3], count[4]);
	return value;
}

//bool SkillWrapper::BigControlCanContinue(int time) const
//{
//	return (GetBigControl() != CONTROLSTATE_TUMBLE && GetBigControl() != CONTROLSTATE_TUMBLE2
//	        && (int64_t)object_interface::GetTick() < control_end);
//	        //&& ((int64_t)object_interface::GetTick()+MILLISEC_TO_TICK(time + 2000)) <= control_end);
//}

void SkillWrapper::_CheckControlMaskChange(object_interface& oif, filter_typemask_t old_mask, filter_typemask_t new_mask)
{
	old_mask &= CONTROL_TYPEMASK;
	new_mask &= CONTROL_TYPEMASK;
	if (old_mask && !new_mask)
	{
		oif.OnControlMaskRemove();
	}
	else if (!old_mask && new_mask)
	{
		oif.OnControlMaskAdd();
	}
}

filter_typemask_t SkillWrapper::_RawModifyStateMask(filter_typemask_t mask, bool add)
{
	state_mask = CalculateMask(mask, add, state_count);
	return state_mask;
}

filter_typemask_t SkillWrapper::ModifyStateMask(object_interface& oif, filter_typemask_t mask, bool add)
{
	filter_typemask_t old_mask = state_mask;
	filter_typemask_t new_mask = _RawModifyStateMask(mask, add);
	_CheckControlMaskChange(oif, old_mask, new_mask);
	return new_mask;
}

filter_typemask_t SkillWrapper::ChangeStateMask(object_interface& oif, filter_typemask_t mask_remove, filter_typemask_t mask_add)
{
	filter_typemask_t old_mask = state_mask;
	_RawModifyStateMask(mask_remove, false);
	filter_typemask_t new_mask = _RawModifyStateMask(mask_add, true);
	_CheckControlMaskChange(oif, old_mask, new_mask);
	return new_mask;
}

void SkillWrapper::ModifyImmuneMask(filter_typemask_t mask, bool add)
{
	//__SKPRINTF("OnModifyImmuneMask(%08x, %s)\n", mask, (add ? "add" : "rm"));
	immune_mask = CalculateMask(mask, add, immune_count);
}

int SkillWrapper::GetRatio(int rawratio) const
{
	int ratio = rawratio;
	if (ratio < -900)
	{
		return -900;
	}
	if (ratio > 3000)
	{
		return 3000;
	}
	return ratio;
}

bool SkillWrapper::NpcPassiveSkill(int id, object_interface& player, int level)
{
	__SKPRINTF("SkillWrapper::NpcPassiveSkill: id=%d, level=%d\n", id, level);
	unique_ptr<Skill> skill(SkillFactory::Create(id, level));
	if (!skill)
	{
		return false;
	}
	skill->Attach(player, CONTEXT_PASSIVE);
	int _;
	skill->StateAttack(NULL, _);
	player.OnSkillCast(id, XID());
	return true;
}

int SkillWrapper::OnSkillEnd(object_interface& player, bool _break, PersistMoveInfo& pmove)
{
	__SKPRINTF("SkillWrapper::OnSkillEnd: skill_id=%d\n", (active_skill ? active_skill->GetId() : 0));
	if (active_skill)
	{
		//__SKPRINTF("SkillWrapper::OnSkillEnd: id=%d\n",active_skill->GetId());
		//active_skill->Attach(player, CONTEXT_PERFORM);
		//active_skill->ResetAimData();
		//active_skill->ResetMoveSpeed();
		//active_skill->ResetImmuneStateMask(this);
		//player.ClearSpecFilter2(TYPEMASK_1PERFORM);
		active_skill->Attach(player, CONTEXT_PERFORM);
		active_skill->OnSkillEnd(_break, pmove);
		//return active_skill->GetRunAdvance();
		_target.id = INVALID_OBJECT_ID;
		return 0;
	}
	return 0;
}

void SkillWrapper::OnSkillCast(object_interface& player, Skill *skill)
{
	char stype = skill->GetType();
	if (stype == TYPE_ATTACK || stype == TYPE_CURSE || stype == TYPE_XP || stype == TYPE_NORMAL || stype == TYPE_SUB_CURSE || stype == TYPE_CURSE_INVINCIBLE)
	{
		player.EnterCombatState();
	}
	player.OnSkillCast(skill->GetId(), skill->GetPriorTarget());
}

Skill *SkillWrapper::FindSkill(int id)
{
	StorageMap::iterator it = skillmap.find(id);
	if (it == skillmap.end())
	{
		return NULL;
	}
	return it->second;
}

int SkillWrapper::GetSkillPerformTimes(int skill_id, std::vector<int>& times)
{
	StorageMap::iterator it = skillmap.find(skill_id);
	if (it == skillmap.end())
	{
		return -1;
	}
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return -2;
	}
	return ss->GetPerformTimeList(*it->second, times);
}

float SkillWrapper::GetSkillMaxRange(int skill_id)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return 0;
	}
	return ss->max_range;
}

float SkillWrapper::GetSkillMinRange(int skill_id)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return 0;
	}
	return ss->min_range;
}

int SkillWrapper::GetSkillTimeType(int skill_id)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return -1;
	}
	return ss->time_type;
}

bool SkillWrapper::GetSkillRange(int skill_id, float& min, float& max)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return false;
	}
	min = ss->min_range;
	max = ss->max_range;
	return true;
}

bool SkillWrapper::OnForgetProfession(object_interface& player)
{
	return true;
}

//void SkillWrapper::SetControlEvade(object_interface& player, int state, bool set, bool not_grab, int time)
//{
//#ifndef NEW_PROP
//	///////////////////////////////////////////////////////////////
//	//
//	//	击飞:
//	//
//	//	1, 抓投外的击飞, 浮空一段时间后会自动落地
//	//	2, 击飞填的时间不同, 判定针对闪避和浮空时间也会不同(高空)
//	//	3, 抓投一定不会跑到高空
//	//
//
//	suspend = false;
//	SetInTheAir(false);
//
//	switch(state)
//	{
//		case CONTROLSTATE_SUSPEND:
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(0),0);
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(1),1.0);
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(2),0);
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(3),0);
//			if (set && not_grab)
//			{
//				bool low = (time<StateRule::GetInstance().GetLowFloatThreshold());
//				int t = 0;
//				if (low)
//					t = StateRule::GetInstance().GetLowFloatSuspendTime();
//				else
//				{
//					t = StateRule::GetInstance().GetSuspendTime();
//					SetInTheAir(true);
//				}
//				suspend = true;
//				player.ResetSuspendTimer(MILLISEC_TO_TICK(t));
//			}
//			break;
//
//		case CONTROLSTATE_TUMBLE:
//		case CONTROLSTATE_TUMBLE2:
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(0),1.0);
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(1),0);
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(2),0);
//			player.EnhanceOverlayJudgementEvade(set,JUDGEMENT_TYPE(3),0);
//			break;
//	}
//#endif
//}
//
//void SkillWrapper::StartBigControl(object_interface& player, int state, int64_t end, bool st4, int time)
//{
//	if(state!=control_state)
//	{
//		if(control_state)
//			SetControlEvade(player,control_state,false,true,0);
//		SetControlEvade(player,state,true,true,time);
//	}
//	control_state = state;
//	control_end = end;
//	st4_control = st4;
//}
//void SkillWrapper::ChangeBigControl(object_interface& player, int state, bool st4, int time)
//{
//	ChangeBigControl(player, state, time);
//	st4_control = st4;
//}
//void SkillWrapper::ChangeBigControl(object_interface& player, int state, int time)
//{
//	if(state!=control_state)
//	{
//		if(control_state)
//			SetControlEvade(player,control_state,false,true,0);
//		SetControlEvade(player,state,true,true,time);
//	}
//	control_state = state;
//}
//void SkillWrapper::StopBigControl(object_interface& player)
//{
//	//投技与大控制可并行，需要检查是否存在投技
//	if (!IsGrabbed())
//		SetControlEvade(player,control_state,false,true,0);
//	control_state = 0;
//	control_end = 0;
//	st4_control = false;
//}

/*
void SkillWrapper::AddPozhan(object_interface& player, int delta)
{
	//__SKPRINTF("SkillWrapper::AddWeak, weak=%f/%f, delta=%f\n", player.GetProperty().GetCurMP(2),
	//           player.GetProperty().GetMaxMP(2), delta);
	delta += pozhan_add_delta;
	if (delta < 0)
		return;
	if (pozhan_add_limit > 0 && delta > pozhan_add_limit)
		delta = pozhan_add_limit;
	//if (!InBigControl())
	//	player.IncMP(MP_TYPE(2), delta); //FIXME
#if 0
	{
		player.IncMP(MP_TYPE(2), delta);
		float w = player.GetProperty().GetCurMP(2);
		float maxw = player.GetProperty().GetMaxMP(2);
		if (maxw - w < 0.01)
		{
			//破绽满，进入 出破绽 状态
			StateFilter* f = new StateFilter285(player);
			f->SetTimeParam(StateRule::GetInstance().GetTimeFor285());
			f->SetSucceedParam(100);
			//FIXME: 简单起见，出破绽状态直接调用gs的AddFilter加上，叠加规则固定为RULE_WEAK
			f->SetMask(f->GetMask()|filter::FM_WEAK|filter::FM_REMOVE_ON_DEATH|filter::FM_NOSAVE);
			player.AddFilter(f);
		}
	}
#endif
}
*/

#if 0
bool SkillWrapper::CompleteCharge(int id)
{
	if (!active_skill || active_skill->GetId() != id)
	{
		return false;
	}
	return active_skill->StopCharge(0);
}

bool SkillWrapper::IsCasting()
{
	return (active_skill ? active_skill->IsCasting() : false);
};
#endif

void SkillWrapper::RemoteCtrlSubobjectDisappeared(object_interface& player, int param)
{
	if (param)
	{
		return;    // 是技能模板主动清除子物体的
	}
}

//void SkillWrapper::SetGrabbed()
//{
//	status_mask |= STATUS_GRABBED;
//	feedback_grab = false;
//	throw_type = 0;
//	throw_dist = 0;
//	throw_time = 0;
//	throw_to = false;
//}
//
//void SkillWrapper::ClearGrabbed()
//{
//	status_mask &= ~STATUS_GRABBED;
//}
//
//void SkillWrapper::SetGrabThrowInfo(int type, float dist, int time, bool throw_to, const A3DVECTOR3& pos)
//{
//	throw_type = type;
//	throw_dist = dist;
//	throw_time = time;
//	this->throw_to = throw_to;
//	throw_pos = pos;
//}
//
//void SkillWrapper::GetGrabThrowInfo(int& type, float&dist, int& time, bool& throw_to, A3DVECTOR3& pos) const
//{
//	type = throw_type;
//	dist = throw_dist;
//	time = throw_time;
//	throw_to = this->throw_to;
//	pos = throw_pos;
//}
//
void SkillWrapper::GrabEnemy(const XID& enemy)
{
	//__SKPRINTF("SkillWrapper::GrabEnemy\n");
	if (active_skill)
	{
		active_skill->GrabEnemy(enemy);
	}
}

void SkillWrapper::MoveAttacker(const A3DVECTOR3& pos, uint64_t tick)
{
	if (last_move_tick >= tick)
	{
		return;
	}
	move_attacker = true;
	move_attacker_pos = pos;
}

void SkillWrapper::MoveUpdate()
{
	last_move_tick = object_interface::GetTick();
	move_attacker = false;
}

#if 0
void SkillWrapper::SuspendTimerTimeout(object_interface& player)
{
	//__SKPRINTF("SkillWrapper::SuspendTimerTimeout\n");
	if (suspend)
	{
		SetControlEvade(player, CONTROLSTATE_TUMBLE, true, true, 0);
	}
}
#endif

void SkillWrapper::NewAttackFeedback(object_interface& oif, const XID& who, const feedback_msg& msg)
{
	if (msg.source_type == FEEDBACK_SOURCE_TWIN)
	{
		twin_feedbacks.AddFeedback(msg);
	}
	else if (msg.source_type == FEEDBACK_SOURCE_MECH)
	{
		mech_feedbacks.AddFeedback(msg);
	}
	else if (msg.source_type == FEEDBACK_SOURCE_REPLISOME)
	{
		replisome_feedbacks.AddFeedback(msg);
	}
	else
	{
		feedbacks.AddFeedback(msg);
	}
	if (msg.control_type && msg.control_time > 0 && msg.hit_add > 0)
	{
		UpdateHit(oif, msg.hit_add, msg.control_time, false);
	}
	if (!(msg.mask[0] & FEEDBACK_MISS) && msg.force_add > 0)
	{
		oif.IncMP(msg.force_add);
	}
	if ((msg.mask[0] & FEEDBACK_HIT) && msg.action_id == oif.GetActionID() && active_skill)
	{
		active_skill->AddHitArr(who.id);
	}
}

void SkillWrapper::CheckAttackFeedback(object_interface& oif, const checkfeedback_msg& msg)
{
	std::vector<XID> src[FEEDBACK_SKILL_COUNT];//每种反馈类型对应的触发者

	if (msg.source_type == FEEDBACK_SOURCE_REPLISOME)
	{
		if (!replisome_feedbacks.CheckFeedback(msg.seq, msg.type, src))
		{
			return;
		}
		oif.ReplisomeFeedBackSkill(msg, src);
		return;
	}
	if (msg.source_type == FEEDBACK_SOURCE_TWIN)
	{
		if (!twin_feedbacks.CheckFeedback(msg.seq, msg.type, src))
		{
			return;
		}
		oif.TwinFeedBackSkill(msg, src);
		return;
	}
	if (msg.source_type == FEEDBACK_SOURCE_MECH)
	{
		if (!mech_feedbacks.CheckFeedback(msg.seq, msg.type, src))
		{
			return;
		}
		oif.MechFeedBackSkill(msg, src);
		return;
	}
	if (!feedbacks.CheckFeedback(msg.seq, msg.type, src))
	{
		return;
	}
	for (int i = 0; i < FEEDBACK_SKILL_COUNT; ++i)
	{
		if (msg.skill[i] > 0)
		{
			for (size_t j = 0; j < src[i].size(); ++j)
			{
				const XID& target = msg.target[i] == 0 ? src[i][j] : oif.GetID();
				InstantSkill(oif, msg.skill[i], src[i].size()/*skill_level*/, 0, WMSKILL::CAST_PRIOR_ID, target, A3DVECTOR3(), 0);
			}
		}
	}
}

void SkillWrapper::BuffSkill(object_interface& player, int id, int level)
{
	__BuffSkill(player, id, level, _target, false);
}

void SkillWrapper::BuffSkill_target(object_interface& player, int id, int level, const XID& tar)
{
	__BuffSkill(player, id, level, tar, false);
}

void SkillWrapper::BuffSkill_subobj(object_interface& player, int id)
{
	__BuffSkill(player, id, 1, _target, true);
}

void SkillWrapper::BuffSkill_subobj_with_level(object_interface& player, int id, int level)
{
	__BuffSkill(player, id, level, _target, true);
}

void SkillWrapper::__BuffSkill(object_interface& player, int id, int level, const XID& tar, bool gen_subobj)
{
	__SKPRINTF("BuffSkill(): id=%d level=%d\n", id, level);

	Skill skill;
	if (!SkillFactory::Copy(skill, id, level))
	{
		return;
	}
	//调整目标
	const Perform *perform = skill.GetPerform(0);
	if (!perform)
	{
		return;
	}

	XID target = tar;
	switch (perform->range_type)
	{
	case RANGE_TARGET_POINT:
	case RANGE_TARGET_BALL:
	{
		if (!target.IsValid())
		{
			target = player.GetCurTarget();
			if (!target.IsValid())
			{
				return;
			}
		}
		A3DVECTOR3 target_pos;
		unsigned short scene_tag, dir;
		float body_size;
		bool is_dead;
		if (!player.QueryObject(target, target_pos, scene_tag, body_size, dir, is_dead))
		{
			return;
		}
		XID new_id;
		MakeNewIDFromXID(target, new_id);
		skill.SetPriorTarget(new_id);
		skill.SetTargetPos(target_pos, body_size);
	}
	break;
	case RANGE_SELF_POINT:
	case RANGE_SELF_LINE:
	case RANGE_SELF_SECTOR:
	case RANGE_SELF_BALL:
	case RANGE_SELF_RING:
	{
		skill.SetTargetPos(player.GetPos(), player.GetBodySize());
		target = player.GetID();
	}
	break;
	default:
		return;
	}
	skill.Attach(player, CONTEXT_PERFORM);
	skill.CheckLevelDepend();
	if (CheckCasting(player, &skill, false))
	{
		return;
	}
	/*需要先通知客户端再释放技能*/
	player.OnBuffSkillCast(id, target);

	skill.BuffSkill(gen_subobj);
}

void SkillWrapper::RefreshPassiveSkill(object_interface& player, Skill *skill, bool only_talent, const std::function<bool(object_interface&, Skill *)>& extra_cond)
{
	if (IsPassiveSkillOf(player, skill))
	{
		if (only_talent && !player.CheckProfTalentSkill(skill->GetId())) //只刷新天赋技能, 防止刷新其他技能出bug
		{
			return;
		}

		if (extra_cond && !extra_cond(player, skill))
		{
			return;
		}

		unique_ptr<Skill> sk(SkillFactory::Create(skill->GetId(), skill->GetLevel()));
		if (sk)
		{
			sk->Attach(player, CONTEXT_PASSIVE);
			//if (sk->NeedSutraActive() && !player.IsSutraSkillActive(sk->GetId()))
			//	return;
			sk->UndoEffect(); //升级时
			sk->TakeEffect();
		}
	}
}

void SkillWrapper::OnStaminaExhaust(object_interface& player)
{
	//if (active_skill && active_skill->GetId()==3591 && active_skill->perform_running && IsCharge(active_skill->perform_running->type))
	//{
	//	//蓄力戒备技能
	//	if (active_skill->StopCharge(CER_EXHAUST))
	//	{
	//		player.StopCharge(active_skill->GetId());
	//	}
	//}
}

void SkillWrapper::ReloadChanges()
{
	//DebugTools::GetInstance().ReloadChanges();
}

int SkillWrapper::LoadChanges(const char *encode_code, size_t encode_code_len)
{
#if 0
#define BUF_SZ	0x100000
	unsigned char *buf = new unsigned char[BUF_SZ];
	int len = BUF_SZ;
	if (GNET::mppc::uncompress2(buf, &len, (const unsigned char *)encode_code, encode_code_len))
	{
		delete[] buf;
		return 2; //DT_ERR_UNCOMPRESS
	}
	if (len >= 0x100000)
	{
		return 2;
	}
	buf[len] = '\0';
	bool ret = DebugTools::GetInstance().LoadChanges((const char *)buf);
	delete[] buf;
	return ret;
#endif
	return 0;
}

bool SkillWrapper::IsTargetBeHit(object_interface& target, attack_msg& msg)
{
	// 保留既有逻辑
	if (dodge_all_counter > 0)
	{
		return false;
	}
	unsigned int can_be_hit_mask = target.GetCanBeHitMask();
	if ((msg.hit_mask_type == HIT_MASK_TYPE_FULL) && ((msg.hit_mask & can_be_hit_mask) != can_be_hit_mask))
	{
		return false;
	}
	if ((msg.hit_mask & can_be_hit_mask) == 0)
	{
		return false;
	}

	//判断是否命中
	int hit_level = msg.attack_hit;
	int evade_level = Get_curEvade(target);
	int hit_add_evade = hit_level + evade_level;
	float evade = 0.1f;
	if (0 != hit_add_evade)
	{
		evade = ((float)(evade_level - hit_level)) / hit_add_evade + 0.1f;
	}
	//由双方命中等级和闪避等级计算出来的闪避率在0-0.33之间
	BETWEEN(evade, 0.f, 0.33f);

	//根据双方额外命中率和技能命率中和额外闪避率修正的最终闪避率在0-0.33之间
	evade += (Get_curEvadeExtraRatio(target) - msg.skill_hit - msg.attack_hit_add) * 0.001f;
	//BETWEEN(evade, 0.f, 0.33f);

	return object_interface::Rand() > evade;
}

bool SkillWrapper::BeAttacked(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill)
{
	///////////////////////////////////////////////////////////////////////////////////////
	//
	//	被攻击时序
	//
	//	1, 是否命中
	//	2, 是否破招
	//	3, ModifyTarget
	//	4, 施加控制
	//	5, 施加状态攻击
	//	6, 计算并施加伤害和位移
	//	7, 反馈
	//

	//fprintf(stderr, "SkillWrapper::BeAttacked, target=%d, src=%d, skill_id=%d\n", target.GetID().newtype, src.newtype, msg.skill_id);
	__SKPRINTF("SkillWrapper::BeAttacked : target=%ld, src=%ld, skill_id=%d, subobj_pid=%d\n", target.GetID().id,
	           src.id, msg.skill_id, msg.subobj_pid);

	int attack_flag = 0;
	int feedback = 0;
	feedback_msg fbmsg;
	memset(&fbmsg, 0, sizeof(fbmsg));
	int feedback_arg_mask[FEEDBACK_SKILL_COUNT];
	memset(feedback_arg_mask, 0, sizeof(feedback_arg_mask));

	bool is_target_be_hit = IsTargetBeHit(target, msg);
	if (!is_target_be_hit)
	{
		attack_flag |= ATTACK_FLAG_MISS;
		feedback |= FEEDBACK_MISS;
	}

	const Perform *perform = 0;
	if (msg.subobj_pid)
	{
		perform = SkillFactory::GetSubobjPerform(msg.subobj_pid);
	}
	else
	{
		perform = skill.GetPerform(msg.attack_stage);
	}
	if (!perform)
	{
		return false;
	}

	if (perform->range_type == RANGE_TARGET_POINT)
	{
		if (dodge_point_attack > 0)
		{
			attack_flag |= ATTACK_FLAG_MISS;
			feedback |= FEEDBACK_MISS;
		}
	}
	if (attack_flag & ATTACK_FLAG_MISS)
	{
		target.DoDamage(src, msg, 0, attack_flag, A3DVECTOR3(0, 0, 0), 0, 0, 0);
	}
	else
	{
		if (target.IsInWeak())
		{
			attack_flag |= ATTACK_FLAG_INHIBITED;
		}

		feedback |= FEEDBACK_HIT;
		if (msg.attacker_info.cur_target == target.GetID())
		{
			feedback |= FEEDBACK_TAR_HIT;
		}

		bool hit_back = false;
		A3DVECTOR3 v = target.GetPos();
		v -= src_pos;
		v.y = 0;
		if (!no_back_counter && v.Normalize() > 0)
		{
			if (DotProduct(v, target.GetDirection()) > __cosf(StateRule::GetInstance().GetHitBackHalfAngle()))
			{
				hit_back = true;
			}
		}
		skill.SetHitBack(hit_back);

		perform->ModifyTarget(skill, msg);

		for (int i = 0; i < FEEDBACK_SKILL_COUNT; ++i)
		{
			if (msg.feedback_type[i] & FEEDBACK_HIT_CONTROL && state_mask & msg.feedback_arg[i])
			{
				feedback_arg_mask[i] |= FEEDBACK_HIT_CONTROL;
			}
			if (msg.feedback_type[i] & FEEDBACK_HIT_BUFF && target.IsFilterExist(msg.feedback_arg[i] + FILTERID_BEGIN, msg.attacker_info.attacker.id))
			{
				feedback_arg_mask[i] |= FEEDBACK_HIT_BUFF;
			}
		}

		//不管怎样都要判断下immune, 因为格挡时immune还会影响伤害...
		bool immune = IsImmuneControl(msg, skill, (attack_flag & ATTACK_FLAG_INHIBITED), src_pos, hit_back);
		if (!immune)
		{
			AddControlFilter(src, msg, skill, (attack_flag & ATTACK_FLAG_INHIBITED), src_pos, hit_back, perform);

			for (int i = 0; i < FEEDBACK_SKILL_COUNT; ++i)
			{
				if (msg.feedback_type[i] & FEEDBACK_HIT_CONTROL_NOW && state_mask & msg.feedback_arg[i])
				{
					ControlInfo info;
					if (!StateRule::GetInstance().GetControlInfo(msg.control_type, info))
					{
						continue;
					}
					if (info.type_mask & msg.feedback_arg[i])
					{
						feedback_arg_mask[i] |= FEEDBACK_HIT_CONTROL_NOW;
					}
				}
			}
		}

		//if (immune && target.IsObjectStateSet(gobject::STATE_IN_PARRY)) //因为格挡导致控制没加上，则产生格挡硬直
		/*if (immune && parry_yingzhi > 0) //因为格挡导致控制没加上，则产生格挡硬直
		{
			AddParryYingzhi(msg, skill, (attack_flag & ATTACK_FLAG_INHIBITED), src_pos);
		}*/

		skill.StateAttack(perform, attack_flag);

		float damage = AttackDamage(target, src_pos, msg, attack_flag, feedback, skill.GetOrigin());
		if (perform->can_share)
		{
			int share_num = skill.GetAOECount();
			if (share_num > 1)
			{
				damage /= share_num;
			}
		}

		target.FilterBeforeAdjustDamage(damage, src, msg, attack_flag);

		//if(msg.is_xp_skill) damage *= target.GetXPSkillRate();
		//伤害可能被格挡
		if (_damage_block > 0)
		{
			float real_damage = damage - _damage_block;
			float min_damage = damage * 0.5f;
			if (real_damage >= min_damage)
			{
				damage = real_damage;
			}
			else
			{
				damage = min_damage;
			}
			attack_flag |= ATTACK_FLAG_PARRY;
		}
		target.FilterAdjustDamage(damage, src, msg, attack_flag);

		//距离与伤害的关系
		if (abs(msg.distance_dmg_first) > 0 || fabs(msg.distance_dmg_rate) > 1e-5)
		{
			float dis = sqrtf(horizontal_distance(target.GetPos(), src_pos));
			if (dis > 10)
			{
				dis = 10;
			}
			damage *= 1 + msg.distance_dmg_first / 1000.f + dis * msg.distance_dmg_rate;
		}

		if (abs(msg.distance_dmg_first2) > 0 || fabs(msg.distance_dmg_rate2) > 1e-5)
		{
			float dis = sqrtf(horizontal_distance(target.GetPos(), src_pos));
			if (dis > 10)
			{
				dis = 10;
			}
			damage *= 1 + msg.distance_dmg_first2 / 1000.f + dis * msg.distance_dmg_rate2;
		}

		/*if (immune && target.IsObjectStateSet(gobject::STATE_IN_PARRY) && dmg_to_force_ratio > 0)
		{
			//不用管当前蓝剩余多少, 吸收所有伤害(无视类型)F
			int force_cost = (int)((damage) * dmg_to_force_ratio / 1000);
			target.DecMP(force_cost);

			attack_flag |= ATTACK_FLAG_SHIELD;
			damage = 0;
			normal_damage = 0;
			element_damage = 0;
		}*/

		if (control_type && control_need_inform_client)
		{
			attack_flag |= ATTACK_FLAG_CTRL;
			if (control_move)
			{
				attack_flag |= ATTACK_FLAG_CTRL_MOVE;
			}
			else if (!prev_control_type)
			{
				attack_flag |= ATTACK_FLAG_SYNC_POS; //开始进控制，并且控制自身不产生位移时需要同步下服务器位置
				control_move_pos = target.GetPos();
			}
			if (control_dir)
			{
				attack_flag |= ATTACK_FLAG_CTRL_DIR;
			}
			if (control_origin == CONTROL_ORIGIN_WEAK)
			{
				attack_flag |= ATTACK_FLAG_CTRL_WEAK;
			}
			else if (control_origin == CONTROL_ORIGIN_POZHAN)
			{
				attack_flag |= ATTACK_FLAG_CTRL_POZHAN;
			}

			fbmsg.control_type = control_type;
			fbmsg.control_time = control_time;

			control_need_inform_client = false;
		}

		if (no_damage_counter > 0)
		{
			damage = 0;
		}

		int final_damage = target.DoDamage(src, msg, damage, attack_flag, control_move_pos, prev_control_type, control_type, control_time);
		//至此, final_damage 已经算过防御减伤, 扣除能量盾, 且判定过npc配置中的固定伤害配置, 是真正造成的伤害
		if (final_damage > 0)
		{
			feedback |= FEEDBACK_DAMAGE;
			auto iter = _record_data.find((int)RECORD_TYPE_DAMAGE);
			if (iter != _record_data.end())
			{
				for (auto& kv : iter->second)
				{
					kv.second += final_damage;
				}
			}
		}
		if (target.IsDead()) //FIXME: 濒死
		{
			//feedback |= FEEDBACK_KILL;
			if (target.IsPlayerClass() || target.IsPlayerNpc())
			{
				feedback |= FEEDBACK_KILL_PLAYER;
			}
			else
			{
				feedback |= FEEDBACK_KILL_NPC;
			}
		}
		if (control_type && zhuatou_need_feedback)
		{
			feedback |= FEEDBACK_ZHUATOU;
			zhuatou_need_feedback = false;
		}
	}

	if (!skill.NeedFeedback())
	{
		return true;
	}
	static const int NO_FEEDBACK = attacker_info_t::AM_GUARD | attacker_info_t::AM_RETINUE | attacker_info_t::AM_DRAGONBORN;
	if (msg.attacker_info.attacker_mode & NO_FEEDBACK)
	{
		return true;//不需要返回守护灵/伙伴/龙裔的攻击
	}

	for (int i = 0; i < FEEDBACK_SKILL_COUNT; ++i)
	{
		fbmsg.mask[i] = feedback | feedback_arg_mask[i];
	}
	fbmsg.action_id = msg.action_id;
	fbmsg.seq = msg.seq;
	fbmsg.hit_add = msg.hit_add;
	fbmsg.force_add = msg.force_add;
	fbmsg.id = target.GetID();
	if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_TWIN)
	{
		fbmsg.source_type = FEEDBACK_SOURCE_TWIN;
	}
	else if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_MECH)
	{
		fbmsg.source_type = FEEDBACK_SOURCE_MECH;
	}
	else if (msg.attacker_info.attacker_mode & attacker_info_t::AM_PLAYER_REPLISOME)
	{
		fbmsg.source_type = FEEDBACK_SOURCE_REPLISOME;
	}

	if (src.IsSubobject() || fbmsg.source_type > 0)
	{
		target.SendAttackFeedBack(msg.attacker_info.attacker, fbmsg);
	}
	else
	{
		target.SendAttackFeedBack(src, fbmsg);
	}

	//status_mask &= ~STATUS_MASK_VICTIM; // 清除技能目标判定计算过程中设定的各种标志
	return true;
}

bool SkillWrapper::BeTreated(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill)
{
	__SKPRINTF("SkillWrapper::BeTreated: target=%ld, src=%ld, skill_id=%d, subobj_pid=%d\n", target.GetID().id,
	           src.id, msg.skill_id, msg.subobj_pid);
	int attack_flag = 0;
	const Perform *perform = 0;
	if (msg.subobj_pid)
	{
		perform = SkillFactory::GetSubobjPerform(msg.subobj_pid);
	}
	else
	{
		perform = skill.GetPerform(msg.attack_stage);
	}
	if (!perform)
	{
		return false;
	}

	perform->ModifyTarget(skill, msg);
	AddControlFilter(src, msg, skill, false, src_pos, false, perform);
	skill.StateAttack(perform, attack_flag);

	float treat_point = CalcTreat(target, msg, attack_flag);

	if (control_type && control_need_inform_client)
	{
		attack_flag |= ATTACK_FLAG_CTRL;
		if (control_move)
		{
			attack_flag |= ATTACK_FLAG_CTRL_MOVE;
		}
		else if (!prev_control_type)
		{
			attack_flag |= ATTACK_FLAG_SYNC_POS; //开始进控制，并且控制自身不产生位移时需要同步下服务器位置
			control_move_pos = target.GetPos();
		}
		if (control_dir)
		{
			attack_flag |= ATTACK_FLAG_CTRL_DIR;
		}
		if (control_origin == CONTROL_ORIGIN_WEAK)
		{
			attack_flag |= ATTACK_FLAG_CTRL_WEAK;
		}
		else if (control_origin == CONTROL_ORIGIN_POZHAN)
		{
			attack_flag |= ATTACK_FLAG_CTRL_POZHAN;
		}
		control_need_inform_client = false;
	}

	if (treat_point || control_type)
	{
		target.DoTreat(src, msg, treat_point, attack_flag, control_move_pos, prev_control_type, control_type, control_time);
	}
	return true;
}

bool SkillWrapper::BeBlessed(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill)
{
	__SKPRINTF("SkillWrapper::BeBlessed: target=%ld, src=%ld, skill_id=%d, subobj_pid=%d\n", target.GetID().id,
	           src.id, msg.skill_id, msg.subobj_pid);

	bool ret = false;
	const Perform *perform = 0;
	if (msg.subobj_pid)
	{
		perform = SkillFactory::GetSubobjPerform(msg.subobj_pid);
	}
	else
	{
		perform = skill.GetPerform(msg.attack_stage);
	}
	if (!perform)
	{
		return false;
	}

	//允许玩家对自己释放控制
	if (AddControlFilter(src, msg, skill, false, src_pos, false, perform))
	{
		if (control_need_inform_client)
		{
			target.SendControlInfo(msg, prev_control_type, control_type, control_time);
			control_need_inform_client = false;
		}
	}

	int _;
	ret = skill.StateAttack(perform, _);
	target.DoEnchant(src, msg, 0, 0);
	/*
	if (msg.skill_aggro > 0)
	{
		target.AddAggroToEnemy(src, msg.skill_aggro);
	}
	*/
	status_mask &= ~STATUS_MASK_VICTIM; //FIXME: 清除技能目标判定计算过程中设定的各种标志
	return ret;
}

bool SkillWrapper::BeCursed(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill)
{
	__SKPRINTF("SkillWrapper::BeCursed: target=%ld, src=%ld, skill_id=%d, subobj_pid=%d\n", target.GetID().id,
	           src.id, msg.skill_id, msg.subobj_pid);

	//if (target.IsNPCClass() && (status_mask&STATUS_INVINC))
	//	return false;
	//if (msg.target_point && (status_mask&STATUS_FUZZY))
	//	return false;

	bool ret = false;
	const Perform *perform = 0;
	if (msg.subobj_pid)
	{
		perform = SkillFactory::GetSubobjPerform(msg.subobj_pid);
	}
	else
	{
		perform = skill.GetPerform(msg.attack_stage);
	}
	if (!perform)
	{
		return false;
	}

	perform->ModifyTarget(skill, msg);

	//诅咒允许释放控制
	if (AddControlFilter(src, msg, skill, false, src_pos, false, perform))
	{
		if (control_need_inform_client)
		{
			target.DoDamage(src, msg, 0, ATTACK_FLAG_CTRL, A3DVECTOR3(0, 0, 0), prev_control_type, control_type, control_time);
			control_need_inform_client = false;
		}
	}

	int _;
	ret = skill.StateAttack(perform, _);
	target.DoEnchant(src, msg, 0, 0);
	/*
	if (msg.skill_aggro > 0)
	{
		target.AddAggro(src, msg.skill_aggro, msg.attacker_info);
	}
	*/
	status_mask &= ~STATUS_MASK_VICTIM; // 清除技能目标判定计算过程中设定的各种标志
	return ret;
}

void SkillWrapper::Verify()
{
	SkillFactory::Verify();
}

int SkillWrapper::GetPerform0StaminaCost(object_interface& player, int skill_id)
{
	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, 1));
	if (!skill)
	{
		return -1;
	}
	skill->Attach(player, CONTEXT_PERFORM);
	const Perform *p = skill->GetPerform(0);
	if (!p)
	{
		return -1;
	}
	return (int)p->StaminaCost(*skill);
}

//bool SkillWrapper::GetLearnLimit(int skill_id, int level_to, int& max_level, int& player_level_require, int& point_per_level)
//{
//	const SkillStub *stub = SkillFactory::GetStub(skill_id);
//	if (!stub)
//		return false;
//	if (level_to<1 || level_to>stub->max_learn)
//		return false;
//	if (stub->origin != ORIGIN_LEARN)
//		return false;
//	max_level = stub->max_level;
//	player_level_require = stub->GetLevelRequest(level_to);
//	point_per_level = 1; //FIXME: 等策划提
//	return true;
//}

void SkillWrapper::ResetCooldown(object_interface& player, int skill_id)
{
	//FIXME: 这种表达式真麻烦, 组团处理下
	int level = 0;
	StorageMap::iterator it = skillmap.find(skill_id);
	if (it == skillmap.end() || it->second->GetBaseLevel() == 0)
	{
		return;
	}
	level = it->second->GetLevel();

	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, level));
	if (!skill)
	{
		return;
	}
	skill->Attach(player, CONTEXT_OTHER);
	skill->ResetCooldown();
}

bool SkillWrapper::TestCoolTime(object_interface& player, int id)
{
	const SkillStub *s = SkillFactory::GetStub(id);
	if (s)
	{
		if (s->cooldown_id == 0 || s->cooldown_id == -1)
		{
			return true;
		}
		if (player.IsPlayerClass())
		{
			player.PrintCoolDown(s->cooldown_id + COOLINGID_BEGIN);
		}

		if (!player.TestCoolDown(s->cooldown_id + COOLINGID_BEGIN))
		{
			return false;
		}
	}
	return true;
}

bool SkillWrapper::IsSameCD(int skill_1, int skill_2)
{
	const SkillStub *s1 = SkillFactory::GetStub(skill_1);
	const SkillStub *s2 = SkillFactory::GetStub(skill_2);
	return s1 && s2 && s1->cooldown_id == s2->cooldown_id;
}

bool SkillWrapper::GetSkillInfo(int skill_id, char& forecast_type, char& cast_prior)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return false;
	}
	forecast_type = ss->forecast_type;
	cast_prior = ss->cast_prior;
	return true;
}

bool SkillWrapper::GetSkillInfo(char& forecast_type, char& cast_prior) const
{
	if (!active_skill)
	{
		return false;
	}
	forecast_type = active_skill->GetStub()->forecast_type;
	cast_prior = active_skill->GetStub()->cast_prior;
	return true;
}

bool SkillWrapper::CanEndNow() const
{
	if (!active_skill || !active_skill->perform_running)
	{
		return false;
	}
	return true;
	//客户端懒得做了，服务器配合屏蔽功能
	//return active_skill->perform_running->can_end_now;
}

bool SkillWrapper::CanCharge(object_interface& player, int& max_charge_time)
{
	if (!active_skill || !active_skill->perform_running)
	{
		return false;
	}
	active_skill->Attach(player, CONTEXT_QUERY);
	return active_skill->CanCharge(max_charge_time);
}

void SkillWrapper::ThrowSubObj(object_interface& player, attack_msg& msg, const XID& target)
{
	// FIXME: 需要一个空技能执行无主过程段
	unique_ptr<Skill> skill(SkillFactory::Create(msg.skill_id, msg.skill_level));
	if (!skill)
	{
		return;
	}
	skill->Attach(player, CONTEXT_PERFORM);
	skill->SetPriorTarget(target);
	skill->ThrowSubObj(msg);
}

bool SkillWrapper::CanMoveWhenCasting() const
{
	if (!active_skill || !active_skill->perform_running)
	{
		return false;
	}
	return active_skill->perform_running->can_move;
	//服务器不写死了，太恶心了。客户端看着办吧
	// 和xom一样写死吧
	//return object_interface::CanMoveSkill(active_skill->GetId());
}
bool SkillWrapper::CanCancel(object_interface& player, int perform_id) const
{
	if (active_skill)
	{
		active_skill->Attach(player, CONTEXT_QUERY);
		return active_skill->Cancel(perform_id);
	}
	return false;
}

char SkillWrapper::PrepareSkill(object_interface& player, int skill_id, int level, int item_id, int item_where, int item_index, bool force)
{
	if (!active_skill)
	{
		active_skill = new Skill();
	}
	bool succ = false;
	if (item_id > 0 || player.IsNPCClass() || player.IsPetClass())
	{
		succ = SkillFactory::Copy(*active_skill, skill_id, level);
	}
	else if (force || player.IsTransfromAndActiveSkill(skill_id))
	{
		succ = SkillFactory::Copy(*active_skill, skill_id, level);
		active_skill->SetCheckHeight(false);
	}
	else
	{
		StorageMap::iterator it = skillmap.find(skill_id);
		if (it != skillmap.end() && it->second->GetBaseLevel() > 0)
		{
			succ = SkillFactory::Copy(*active_skill, skill_id, it->second->GetLevel());
		}
	}

	if (!succ || !CanCastByClient(active_skill->GetType()))
	{
		delete active_skill;
		active_skill = NULL;
		return REASON_UNKNOWN;
	}
	active_skill->Attach(player, CONTEXT_PERFORM);
	return active_skill->Prepare(item_id, item_where, item_index);
}

char SkillWrapper::StartSkill(object_interface& player, unsigned char flags, const XID& target, const A3DVECTOR3& target_pos,
                              const A3DVECTOR3& self_pos, int& interval, int& next_interval, PersistMoveInfo& pmove, float target_body_size, std::vector<int>& target_list, const A3DVECTOR3& subobj_pos)
{
	if (!active_skill)
	{
		return REASON_UNKNOWN;
	}
	A3DVECTOR3 final_target_pos = target_pos;
	if (target.IsValid())
	{
		bool visible = false;
		A3DVECTOR3 tpos;
		if (!CheckAndGetTargetPos(player, target, tpos, &visible))
		{
			return REASON_INVALID_TARGET;
		}
		if (!visible && !active_skill->CanCastToInvisible())
		{
			return REASON_INVISIBLE;
		}
		if ((flags & CAST_PRIOR_MASK) == CAST_PRIOR_ID)
		{
			active_skill->SetFixPosFlag(true);
			final_target_pos = tpos;
		}
	}
	active_skill->SetTargetPos(final_target_pos, target_body_size);
	active_skill->SetPriorTarget(target);
	active_skill->SetSubobjPos(subobj_pos);
	if (flags & CAST_SELF_POS)
	{
		active_skill->SetSelfPosByClient(self_pos);
	}
	active_skill->Attach(player, CONTEXT_PERFORM);
	active_skill->CheckLevelDepend();
	char ret = CheckCasting(player, active_skill, true);
	if (ret != 0)
	{
		return ret;
	}
	_target = target;
	ret = active_skill->Start(interval, next_interval, pmove, target_list, player);
	if (ret == 0)
	{
		OnSkillCast(player, active_skill);
		player.SetLastSkill(active_skill->GetId());
	}
	return ret;
}

char SkillWrapper::Run(object_interface& player, int& interval, int& next_interval, float charge_ratio, PersistMoveInfo& pmove, std::vector<int>& target_list)
{
	if (!active_skill)
	{
		return RUNRET_UNKNOWN_ERR;
	}
	if (active_skill->IsTheEnd())
	{
		return RUNRET_END;
	}
	active_skill->Attach(player, CONTEXT_PERFORM);
	return active_skill->Run(interval, next_interval, charge_ratio, pmove, target_list, player);
}

void SkillWrapper::noControl(bool b)
{
	if (b)
	{
		no_control_counter++;
	}
	else
	{
		no_control_counter--;
	}
}

void SkillWrapper::ImmuneControl(bool b)
{
	if (b)
	{
		immune_control_counter++;
	}
	else
	{
		immune_control_counter--;
	}
}

void SkillWrapper::ImmuneControlExceptHitBack(bool b)
{
	if (b)
	{
		immune_control_except_hit_back_counter++;
	}
	else
	{
		immune_control_except_hit_back_counter--;
	}
}

void SkillWrapper::ImmuneControlExceptForce(bool b)
{
	if (b)
	{
		immune_control_except_force_counter++;
	}
	else
	{
		immune_control_except_force_counter--;
	}
}

void SkillWrapper::ImmuneControlExceptWeak(bool b)
{
	if (b)
	{
		immune_control_except_weak_counter++;
	}
	else
	{
		immune_control_except_weak_counter--;
	}
}

void SkillWrapper::ImmuneControlExceptFullPozhan(bool b)
{
	if (b)
	{
		immune_control_except_full_pozhan_counter++;
	}
	else
	{
		immune_control_except_full_pozhan_counter--;
	}
}

void SkillWrapper::ImmuneControlExceptInAction(object_interface& player, bool b)
{
	if (b)
	{
		immune_control_except_in_action_counter++;
		immune_control_except_in_action_except = player.GetActionID();
	}
	else
	{
		immune_control_except_in_action_counter--;
	}
}

void SkillWrapper::ImmuneControlExceptNoForce(bool b)
{
	if (b)
	{
		immune_control_except_no_force_counter++;
	}
	else
	{
		immune_control_except_no_force_counter--;
	}
}

void SkillWrapper::SetInControl(object_interface& player, int time, char type, char origin, bool move, const A3DVECTOR3& dest_pos, char dir, bool zhuatou, bool need_check)
{
	uint64_t now = object_interface::GetTick();

	if (need_check)
	{
		stun_timestamp = object_interface::GetSysTime();
	}
	control_time = time;
	prev_control_type = control_type;
	control_type = type;
	is_in_zhuatou_control = zhuatou;
	if (!prev_control_type)
	{
		control_begin_tick = now;
		control_origin = origin;
		if (control_origin == CONTROL_ORIGIN_WEAK)
		{
			control_can_modify_tick1 = control_begin_tick + MILLISEC_TO_TICK(CONTROL_CAN_MODIFY_TICK1_WEAK);
		}
		else if (control_origin == CONTROL_ORIGIN_POZHAN)
		{
			control_can_modify_tick1 = control_begin_tick + MILLISEC_TO_TICK(CONTROL_CAN_MODIFY_TICK1_POZHAN);
		}
		else if (control_origin == CONTROL_ORIGIN_PARRY_YINGZHI)
		{
			control_can_modify_tick1 = control_begin_tick + MILLISEC_TO_TICK(CONTROL_CAN_MODIFY_TICK1_PARRY_YINGZHI);
		}
		else
		{
			control_can_modify_tick1 = control_begin_tick + MILLISEC_TO_TICK(CONTROL_CAN_MODIFY_TICK1_NORMAL);
		}
	}
	if (now <= control_can_modify_tick1)
	{
		control_can_modify_tick2 = now + MILLISEC_TO_TICK(time);
	}
	else
	{
		float p1, p2;
		StateRule::GetInstance().GetControlTimeFactor(p1, p2);
		ASSERT(isfinite(p1) && p1 > 0 && isfinite(p2) && p2 > 0);
		/*if (player.IsObjectStateSet(gobject::STATE_IN_CHUPOZHAN))
			//control_can_modify_tick2出现负增长也没关系
		{
			control_can_modify_tick2 = now + MILLISEC_TO_TICK(time) - (now - control_can_modify_tick1) / p1;
		}
		else*/
		{
			control_can_modify_tick2 = now + MILLISEC_TO_TICK(time) - (now - control_can_modify_tick1) / p2;
		}
	}
	control_move = move;
	control_move_pos = dest_pos;
	control_dir = dir;
}

void SkillWrapper::SetInformClient()
{
	control_need_inform_client = true;
}

void SkillWrapper::SetFeedbackZhuatou()
{
	zhuatou_need_feedback = true;
}

void SkillWrapper::EndControl(object_interface& player, int reason)
{
	control_time = 0;
	control_type = 0;
	prev_control_type = 0;
	control_begin_tick = 0;
	is_in_zhuatou_control = false;
	control_origin = 0;
	control_move = false;
	control_dir = 0;
	control_need_inform_client = false;
	zhuatou_need_feedback = false;
	if (reason == filter::ER_JIEYUN)
	{
		player.SendControlEnd(CONTROL_END_BY_JIEYUN, 0, 0);
	}
	else if (reason != filter::ER_DEATH)
	{
		player.SendControlEnd(0, 0, 0);
	}
	//破绽控制结束，需要结束出破绽状态
	/*if (player.IsObjectStateSet(gobject::STATE_IN_CHUPOZHAN) && chu_pozhan_timeout)
	{
		chu_pozhan_timeout = false;
		//player.SetMPPozhan(0);
		player.UpdateObjectState(gobject::STATE_IN_CHUPOZHAN, false, true);
	}*/
}

int SkillWrapper::AdjustControlTime(int time) const
{
	return time; //TODO: 等策划规则
}

//如果攻击带控制，是否能免疫掉
bool SkillWrapper::IsImmuneControl(attack_msg& msg, Skill& skill, bool inhibited, const A3DVECTOR3& direct_attacker_pos, bool hit_back)
{
	object_interface& oif = skill.GetPlayer().GetObject();

	//存在弱点||不在使用技能||(无弱点&&在使用技能&&(强制控制||背刺)) => 产生控制
	//bool use_skill = (active_skill && active_skill->perform_running);

	if (!msg.can_add_in_immue_control)
	{
		//免疫控制的技能效果
		if (immune_control_counter > 0)
		{
			if (immune_control_except_hit_back_counter > 0 && hit_back) {}
			else if (immune_control_except_force_counter > 0 && msg.is_force_control) {}
			else if (immune_control_except_weak_counter > 0 && inhibited) {}
			//else if (immune_control_except_full_pozhan_counter > 0 && oif.IsObjectStateSet(gobject::STATE_IN_CHUPOZHAN)) {}
			//else if (immune_control_except_in_action_counter>0 && use_skill && skill.GetPlayer().GetObject().GetActionID()!=immune_control_except_in_action_except) {}
			//else if (immune_control_except_in_action_counter>0 && oif.HasAction()) {}
			else if (immune_control_except_no_force_counter > 0 && Get_MP(oif) == 0) {}
			else
			{
				return true;
			}
		}

		//一些pvp属性对控制的免疫
		if ((oif.IsPlayerClass() || oif.IsPlayerNpc()) && IsPlayerAttacker(msg.attacker_info))
		{
			int rate = 0;
			switch (msg.control_type)
			{
			case CONTROL_TYPE_AIR:
			case CONTROL_TYPE_AIR_BIG:
				rate = Get_curAirRedu(oif);
				break;
			case CONTROL_TYPE_GROUND:
				rate = Get_curGroundRedu(oif);
				break;
			case CONTROL_TYPE_FROZEN:
				rate = Get_curFrozenRedu(oif);
				break;
			case CONTROL_TYPE_STONE:
				rate = Get_curStoneRedu(oif);
				break;
			case CONTROL_TYPE_TIME:
				rate = Get_curTimeFreezeRedu(oif);
				break;
			default:
				rate = 0;
				break;
			}
			if (object_interface::Rand() * 1000 < rate)
			{
				return true;
			}
		}
	}
	return false;
}

void SkillWrapper::CalcStunTime(object_interface& oif, int& time, int attack_stun)
{
	int res_stun = 0;//硬直抵抗
	float _f = .0f;
	int64_t _i = 0;
	double _d = 0;
	oif.GetPropertyByIndex(GPROP_INDEX(curStunRes), res_stun, _f, _i, _d);
	if (res_stun > 0)
	{
		if (attack_stun < 0)
		{
			attack_stun = 0;
		}
		else if (attack_stun > res_stun)
		{
			attack_stun = res_stun;
		}
		time = time * (res_stun + attack_stun) / (res_stun + res_stun);
	}
	int tm_min = oif.IsPlayerClass() || oif.IsPlayerNpc() ? 600 : 400;
	if (time < tm_min)
	{
		time = 0;
	}
}

void SkillWrapper::CalcDizzyTime(object_interface& oif, int& c_time)
{
	int tm_now = object_interface::GetSysTime();
	if (tm_now - _last_dizzy_tm > 30)
	{
		_dizzy_turn = 0;
		_last_dizzy_tm = tm_now;//新的递减周期
	}
	AttenuatControlTime(Get_curDizzyAttenuated(oif), c_time, _dizzy_turn);
	++ _dizzy_turn;
}

void SkillWrapper::CalcSilentTime(object_interface& oif, int& c_time)
{
	int tm_now = object_interface::GetSysTime();
	if (tm_now - _last_silent_tm > 30)
	{
		_silent_turn = 0;
		_last_silent_tm = tm_now;//新的递减周期
	}
	AttenuatControlTime(Get_curSilentAttenuated(oif), c_time, _silent_turn);
	++ _silent_turn;
}

void SkillWrapper::AttenuatControlTime(int rate, int& c_time, int c_count)
{
	if (rate < 0 || rate > CONFIG_RATIO || c_count < 0)
	{
		return;
	}
	static std::map<int, float> cfg =
	{
		{0, 1.0f},
		{1, 0.8f},
		{2, 0.5f},
	};
	auto iter = cfg.find(c_count);
	if (iter == cfg.end()) //第四次及以上免疫
	{
		c_time = 0;
	}
	else
	{
		c_time *= (iter->second - rate / CONFIG_RATIO);
	}
}

//FIXME: 仅仅判断是否满破绽，按照设计，破绽控制中一直满破绽
bool SkillWrapper::AddControlFilter(const XID& src, attack_msg& msg, Skill& skill, bool inhibited, const A3DVECTOR3& direct_attacker_pos, bool hit_back, const Perform *perform)
{

	object_interface& oif = skill.GetPlayer().GetObject();
	//控制类型不存在
	if (!msg.control_type)
	{
		return false;
	}
	//免疫控制计数，目前没用，留着吧
	if (no_control_counter > 0)
	{
		return false;
	}
	//被刺技能中，强控，逻辑，暂时删除
	//存在弱点||不在使用技能||(无弱点&&在使用技能&&(强制控制||背刺)) => 产生控制
	//bool use_skill = (active_skill && active_skill->perform_running);
	//bool do_control = false;
	//if (inhibited)
	//	do_control = true;
	//else if (!use_skill)
	//	do_control = true;
	//else if (msg.is_force_control || hit_back)
	//	do_control = true;
	//if (!do_control)
	//	return false;
	//暂时不做体型转换，做的话交给策划
	int new_control_type = msg.control_type;
	/*
	if (oif.GetBodySizeType() == 2) //对于巨型怪，不能击飞
	{
		if (new_control_type == 3 || new_control_type == 4 || new_control_type == 8) //FIXME: 硬编码, 再有修改就放给策划
		{
			new_control_type = 1;
		}
	}
	*/
	ControlInfo info;
	//读取控制配置
	if (!StateRule::GetInstance().GetControlInfo(new_control_type, info))
	{
		return false;
	}
	if (oif.IsNPCClass() && new_control_type == CONTROL_TYPE_FALL_INTO_PICTURE)
	{
		return false;
	}
	//-1表示使用过程参数
	if (info.time < 0)
	{
		info.time = msg.control_time;
	}
	//单位是否可以被控制,递减冷却自身属性检测，在这里一起做了
	if (info.need_check)
	{
		int stun_cd = 0;
		float _f = .0f;
		int64_t _i = 0;
		double _d = 0;
		oif.GetPropertyByIndex(GPROP_INDEX(curStunCD), stun_cd, _f, _i, _d);
		if (stun_timestamp + (time_t)stun_cd / 1000 > oif.GetSysTime())
		{
			return false;
		}
		CalcStunTime(oif, info.time, msg.attack_stun);
	}

	//眩晕/沉默时间衰减
	if ((info.type_mask & TYPEMASK_DIZZ) || (info.type_mask & TYPEMASK_DIZZ_STATE))
	{
		CalcDizzyTime(oif, info.time);
	}
	else if ((info.type_mask & (TYPEMASK_SILENT | TYPEMASK_SILENT_ABSOLUTE)) && !(info.type_mask & TYPEMASK_CHANGE_MODEL)) //排除变身
	{
		CalcSilentTime(oif, info.time);
	}

	//反弹控制
	int rate = oif.GetSkillWrapper().GetUnControlRate();
	if (object_interface::Rand() * 1000 < rate)
	{
		int skill_id = oif.GetSkillWrapper().GetUnControlSkill();
		oif.GetSkillWrapper().BuffSkill_target(oif, (int)skill_id, 1, msg.attacker_info.attacker);
		return false;
	}
	//这里会关闭免控
	if (!oif.CanBeControl(new_control_type, info.control_move_dis_ratio, info.control_move_dis_extra, info.time, info.need_check))
	{
		oif.DecSP(perform->GetSPDec(skill));
		return false;
	}
	if (new_control_type > 0 && new_control_type != msg.control_type)
	{
		//重读取控制配置
		if (!StateRule::GetInstance().GetControlInfo(new_control_type, info))
		{
			return false;
		}
		//怪物硬直条结束，本次控制替换为配置的控制，不应该没时间
		if (info.time < 0)
		{
			return false;
		}

	}

	if (GetImmuneMask() & info.type_mask)
	{
		return false;
	}
	//小于最小控制时间，认为没有意义
	if (info.time < MIN_CONTROL_TIME)
	{
		return false;
	}
	if (info.distance < 0)
	{
		info.distance = msg.control_move_distance;
	}
	//没有位移在要死的状态就不处理了
	if (info.distance < 0 && oif.IsDying())
	{
		return false;
	}
	info.distance = info.distance * info.control_move_dis_ratio + info.control_move_dis_extra;
	if (info.distance < 1e-5)
	{
		info.distance = 0.f;
	}
	ControlFilter *filter = new ControlFilter(oif, msg.attacker_info.attacker, direct_attacker_pos, msg.attacker_info.body_size, msg.attacker_info.dir, msg, src);
	filter->SetType(new_control_type);
	filter->SetTypeMask(info.type_mask);
	filter->SetTime(info.time);
	filter->SetMoveTime(perform->control_move_time);
	filter->SetNeedCheck(info.need_check);
	filter->SetDetachTick(info.detach_limit);
	if (info.distance > 0)
	{
		switch (msg.control_move_type)
		{
		case CONTROL_MOVE_KNOCKBACK:
			filter->SetKnockbak(info.distance, true, false, false);
			break;

		case CONTROL_MOVE_PULL:
			filter->SetPull(info.distance, true, false);
			break;

		case CONTROL_MOVE_KNOCKBACK2:
			filter->SetKnockbak(info.distance, false, false, false);
			break;

		case CONTROL_MOVE_PULL2:
			filter->SetPull(info.distance, false, false);
			break;

		case CONTROL_MOVE_PULL3:
			filter->SetPull(info.distance, false, true);
			break;

		case CONTROL_MOVE_KNOCKBACK3:
			filter->SetKnockbak(info.distance, true, true, false);
			break;

		case CONTROL_MOVE_KNOCKBACK4:
			filter->SetKnockbak(info.distance, true, false, true);
			break;
		}
		filter->SetMoveDirType(msg.control_move_dir_type);
	}
	filter->SetDir(msg.control_dir);
	filter->SetNextControl(info.next_control);

	filter->SetPriority(info.priority, info.add_time_for_priority);
	filter->SetCanBeHitMask(info.can_be_hit_mask);
	//放这里，别用，抓投，暂不维护
	if (msg.is_zhuatou_control)
	{
		filter->SetZhuatou();
	}
	/*
	if (inhibited)
	{
		filter->SetOrigin(CONTROL_ORIGIN_WEAK);
	}
	*/
	/*
	if (oif.IsObjectStateSet(gobject::STATE_IN_CHUPOZHAN))
	{
		filter->SetOrigin(CONTROL_ORIGIN_POZHAN);
	}
	*/

	if (control_type)
	{
		//TODO:先让任何时间都可以接
		/*
		if (object_interface::GetTick() >= control_can_modify_tick1 && object_interface::GetTick() >= control_can_modify_tick2)
		{
			delete filter;
			return false;
		}
		*/
		__SKPRINTF("debug_control: modity control filer, old_control_type=%d, new_control_type=%d\n", control_type, new_control_type);
		bool r = oif.ModifyFitler(FI_CONTROL_FILTER, CTRL_MODIFY, filter, sizeof(ControlFilter), 0);
		delete filter;
		return r;
	}
	oif.AddFilter(filter);

	return true;
}

void SkillWrapper::DodgeAll(bool b)
{
	if (b)
	{
		dodge_all_counter++;
	}
	else
	{
		dodge_all_counter--;
	}
}
void SkillWrapper::DodgePointAttack(bool b)
{
	if (b)
	{
		dodge_point_attack++;
	}
	else
	{
		dodge_point_attack--;
	}
}

void SkillWrapper::OnPozhanFull(object_interface& player)
{
	//这里不能修改任何属性
	/*if (player.IsObjectStateSet(gobject::STATE_IN_CHUPOZHAN))
	{
		return;
	}
	chu_pozhan_timeout = false;
	player.UpdateObjectState(gobject::STATE_IN_CHUPOZHAN, true, true);*/
	player.ResetChuPozhanTimer(MILLISEC_TO_TICK(CHU_POZHAN_TIME));
}

void SkillWrapper::OnChuPozhanTimeout(object_interface& player)
{
	/*if (player.IsObjectStateSet(gobject::STATE_IN_CHUPOZHAN))
	{
		if (control_type)
		{
			chu_pozhan_timeout = true;
		}
		else
		{
			chu_pozhan_timeout = false;
			//player.SetMPPozhan(0);
			player.UpdateObjectState(gobject::STATE_IN_CHUPOZHAN, false, true);
		}
	}*/
}

bool SkillWrapper::AddParryYingzhi(attack_msg& msg, Skill& skill, bool inhibited, const A3DVECTOR3& direct_attacker_pos)
{
	object_interface& oif = skill.GetPlayer().GetObject();

	//if (oif.HasAction()) return false;

	A3DVECTOR3 dir = oif.GetPos() - direct_attacker_pos;
	dir.y = 0;
	if (dir.Normalize() < 1e-5)
	{
		dir = -1 * oif.GetDirection();
	}
	A3DVECTOR3 dest_pos = oif.GetPos() + 1 * dir;

	return oif.StartParryYingzhi(dest_pos); //TODO: 异步?
}

unsigned int SkillWrapper::GetSkillHitMask(int skill_id)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return 0;
	}
	return ss->first_hit_mask;
}

int SkillWrapper::GetHit(object_interface& oif)
{
	if (hit_cur == 0) //只可能从 >0 => 0, 不可能反过来
	{
		return 0;
	}
	UpdateHit(oif, 0, 0, false);
	return hit_cur;
}

void SkillWrapper::UpdateHit(object_interface& oif, int add, int time, bool inform)
{
	int64_t now = object_interface::GetTick();
	if (hit_cur > 0 && now > hit_timeout_tick)
	{
		hit_cur = 0;
		hit_inform = -1; //inform==true时强制通知(防止本来是N, 归零add后又为N而不通知的情况, 这会导致hit_timeout_tick不通知)
	}
	if (add > 0)
	{
		hit_cur += add;
		if (time > 3000)
		{
			time = 3000;    //连击有效时间每次最多加3000ms
		}
		int64_t t = now + MILLISEC_TO_TICK(time);
		if (t > hit_timeout_tick)
		{
			hit_timeout_tick = t;
		}
	}
	if (inform && hit_cur != hit_inform)
	{
		int delta = hit_timeout_tick - now;
		if (delta < 0)
		{
			delta = 0;
		}
		oif.SendClientSelfHit(hit_cur, TICK_TO_MILLISEC(delta));
		hit_inform = hit_cur;
	}
}

void SkillWrapper::OnHeartbeat(object_interface& oif)
{
	UpdateHit(oif, 0, 0, true);
	if (_refresh_skill_capacity)
	{
		DoRefreshSkillCapacity(oif);
		_refresh_skill_capacity = false;
	}

	// 定期清理过期的挑战目标
	CleanupExpiredChallengeTargets();
}

void SkillWrapper::noDamage(bool b)
{
	if (b)
	{
		no_damage_counter++;
	}
	else
	{
		no_damage_counter--;
	}
}

void SkillWrapper::ParryYingzhi(bool b)
{
	if (b)
	{
		parry_yingzhi++;
	}
	else
	{
		parry_yingzhi--;
	}
}

void SkillWrapper::CheckSkill(object_interface& player)
{
	int level = player.GetLevel();
	int prof = player.GetProf();

	StorageMap::iterator it = skillmap.begin();
	while (it != skillmap.end())
	{
		Skill *sk = it->second;
		int base_level = sk->GetBaseLevel();
		int reqlv = sk->GetLevelRequest(base_level);
		unsigned int prof_mask = sk->GetProfessionMask();
		if (reqlv > level || !(prof_mask & (1 << prof)))
		{
			//GLog::formatlog("skill", "check_skill:player_id=%ld:skill_id=%d:player_level=%d:base_level=%d:req_level=%d:prof=0x%x:prof_mask=0x%x,addon_level=%d",
			//                player.GetID().id, it->first, level, base_level, reqlv, 1 << prof, prof_mask, sk->GetAddonLevel());

			if (__ClearBaseLevel(it->first, sk, player))
			{
				//等级或职业不满足，也可能是策划调整，技能丢了就行
				StorageMap::iterator it_bak = it;
				++it;
				skillmap.erase(it_bak);
				//delete sk; //FIXME: 没有delete, 防止技能使用过程中被Remove, 容我想想
				releaseset.insert(sk);
			}
			else
			{
				++it;
			}
		}
		else
		{
			++it;
		}
	}
	//RefreshSkillCapacity(player);
}

bool SkillWrapper::__ClearBaseLevel(int id, Skill *sk, object_interface& player)
{
	sk->SetLevel(0, sk->GetAddonLevel());
	sk->SetMask(0);
	//if (sk->GetType() == TYPE_XP) xp_level = sk->GetLevel();

	player.SendClientExtraSkill(id, 0);
	//驱除被动效果
	if (sk->GetType() == TYPE_PASSIVE && ((1 << player.GetProperty().GetProf()) & sk->GetProfessionMask()))
	{
		unique_ptr<Skill> skill(SkillFactory::Create(id, sk->GetLevel()));
		if (skill)
		{
			skill->Attach(player, CONTEXT_PASSIVE);
			skill->UndoEffect();
		}
	}
	return (sk->GetAddonLevel() == 0);
}


void SkillWrapper::noBack(bool b)
{
	if (b)
	{
		no_back_counter++;
	}
	else
	{
		no_back_counter--;
	}
}

bool SkillWrapper::CanCtrlEndCast(int skill_id)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return false;
	}
	return ss->ctrl_end_cast;
}

bool SkillWrapper::CanCastInMask(int skill_id, int mask)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return false;
	}
	return ss->cancast_in_qinggong & mask;
}

bool SkillWrapper::CanCastInSilent(int skill_id)
{
	return CanCastInMask(skill_id, SCS_MASK_SILENT);
}

bool SkillWrapper::CanCastInDrag(int skill_id)
{
	return CanCastInMask(skill_id, SCS_MASK_DRAG);
}

bool SkillWrapper::CanCastInWater(int skill_id)
{
	return CanCastInMask(skill_id, SCS_MASK_WATER);
}

bool SkillWrapper::CanCastInChain(int skill_id)
{
	return !CanCastInMask(skill_id, SCS_MASK_CHAIN);//有该掩码说明不能释放
}

void SkillWrapper::LoadBytesMove(const char *bytes)
{
	const char *beg = bytes;
	while (*beg != '\0')
	{
		//get key
		while (!isdigit(*beg) && *beg != '.' && *beg != '-' && *beg != '\0')
		{
			beg++;
		}
		if (*beg == '\0')
		{
			break;
		}
		const char *end = beg;
		while (isdigit(*end) || *end == '.' || *end == '-')
		{
			end++;
		}
		std::string key(beg, end - beg);
		//get value
		beg = end;
		while (!isdigit(*beg) && *beg != '.' && *beg != '-' && *beg != '\0')
		{
			beg++;
		}
		if (*beg == '\0')
		{
			break;
		}
		end = beg;
		while (isdigit(*end) || *end == '.' || *end == '-')
		{
			end++;
		}
		std::string value(beg, end - beg);
		//set
		const SkillStub *ss = SkillFactory::GetStub(atoi(key.c_str()));
		if (ss)
		{
			ss->SetBytesMove(strtof(value.c_str(), 0));
		}
		//next
		beg = end;
	}
}

char SkillWrapper::InstantSkill(object_interface& player, int skill_id, int level, int stage, unsigned char flags, const XID& target, const A3DVECTOR3& target_pos,
                                bool force, int *delay_tm, bool skill_dispel_filter)
{
	//TODO: no force
	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, level));
	if (!skill)
	{
		return REASON_UNKNOWN;
	}
	A3DVECTOR3 final_target_pos = target_pos;
	if (target.IsValid())
	{
		bool visible = false;
		A3DVECTOR3 tpos;
		if (!CheckAndGetTargetPos(player, target, tpos, &visible))
		{
			return REASON_INVALID_TARGET;
		}
		if (!visible && !skill->CanCastToInvisible())
		{
			return REASON_INVISIBLE;
		}
		if ((flags & CAST_PRIOR_MASK) == CAST_PRIOR_ID)
		{
			final_target_pos = tpos;
		}
	}
	//weylan:skill每次重新生成的,所以不能重复设置cooldown
	//是否跳过第一段由外层逻辑保证
	skill->GetCooldownSet() = (stage != 0);
	skill->SetTargetPos(final_target_pos, 0);
	skill->SetPriorTarget(target);
	skill->Attach(player, CONTEXT_PERFORM);
	skill->CheckLevelDepend();
	char ret = CheckCasting(player, skill.get(), true, (stage == 0));
	if (ret != 0)
	{
		//printf("player:%ld skill:%d check failed:%d\n",player.GetID().id, skill_id, ret);
		return ret;
	}
	ret = skill->InstantRun(stage, delay_tm, skill_dispel_filter);
	_msg_delay_tm = skill->GetMsgDelayTm();
	return ret;
}

void SkillWrapper::FillAttackMsg4Tanqiang(attack_msg& msg, int skill_id, int level, object_interface& player)
{
	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, level));
	if (!skill)
	{
		return;
	}
	skill->Attach(player, CONTEXT_PERFORM);
	skill->FillAttackMsg4Tanqiang(msg);
}

unsigned int SkillWrapper::GetSkillActiveMask(int skill_id)
{
	const SkillStub *ss = SkillFactory::GetStub(skill_id);
	if (!ss)
	{
		return 0;
	}
	return ss->active_mask;
}

void SkillWrapper::GetSkillCD(object_interface& player, const std::set<int>& skill, std::map<int, int>& cd)
{
	for (auto it = skill.begin(); it != skill.end(); it++)
	{
		int tm = 0;
		int cdid = SkillFactory::GetSkillCDIDAndTm(player, *it, tm);
		if (cdid > 0 && tm > 0)
		{
			cd[cdid + COOLINGID_BEGIN] = tm;
		}
	}
}

void SkillWrapper::GetSkillCD(std::set<int>& skill, std::set<int>& cd)
{
	for (auto it = skill.begin(); it != skill.end(); it++)
	{
		int cdid = SkillFactory::GetSkillCDID(*it);
		if (cdid > 0)
		{
			cd.insert(cdid + COOLINGID_BEGIN);
		}
	}
}

int SkillWrapper::GetSkillCD(int skill)
{
	return COOLINGID_BEGIN + SkillFactory::GetSkillCDID(skill);
}

bool CalcSubobjCreatePosDir(const calc_subobj_create_pos_t& args, get_pos_fun_t& get_grabbed_pos, get_pos_fun_t& get_target_pos, A3DVECTOR3& pos, A3DVECTOR3& dir)
{
	bool has_target_pos = false;
	A3DVECTOR3 target_pos;
	A3DVECTOR3 cdir;//生成时的方向
	if (SubobjFromSelf(args.ctype))
	{
		pos = args.self_pos;
		cdir = args.self_dir;
	}
	else if (SubobjFromTarget(args.ctype))
	{
		if (!get_target_pos(target_pos))
		{
			return false;
		}
		has_target_pos = true;

		if (args.ctype == SUBOBJ_CTYPE_ZHUATOU_TARGET)
		{
			if (!get_grabbed_pos(pos))
			{
				return false;
			}
		}
		else
		{
			pos = target_pos;
		}
		cdir = args.self_pos - target_pos;
	}
	else if (args.ctype == SUBOBJ_CTYPE_CLIENT_TARGET)
	{
		pos = args.client_subobj_pos;
		if (!get_target_pos(target_pos))
		{
			return false;
		}
		has_target_pos = true;
		cdir = target_pos - args.self_pos;
		dir = cdir;
	}
	else
	{
		return false;
	}
	cdir.y = 0;
	if (cdir.Normalize() < 1e-5)
	{
		cdir = args.self_dir;    //随便取个方向
	}
	//加上偏移后的生成位置
	if (args.angle != 0)
	{
		object_interface::RotateVector(cdir, __sinf(args.angle), __cosf(args.angle));
	}
	pos += cdir * args.distance;

	//飞行方向
	if (args.ctype == SUBOBJ_CTYPE_SELF_TARGET || args.ctype == SUBOBJ_CTYPE_FROM_TARGET_2)
	{
		if (!has_target_pos)
		{
			if (!get_target_pos(target_pos))
			{
				return false;
			}
		}
		dir = target_pos - pos;
	}
	else if (args.ctype == SUBOBJ_CTYPE_SELF_EMIT)
	{
		dir = cdir;
	}
	else if (args.ctype == SUBOBJ_CTYPE_FROM_TARGET)
	{
		dir = args.self_pos - pos;
	}
	else if (args.ctype == SUBOBJ_CTYPE_SELF_DIR)
	{
		dir = args.self_dir;
	}
	dir.y = 0;
	if (dir.Normalize() < 1e-5)
	{
		dir = args.self_dir;    //随便取个方向
	}
	return true;
}

void SkillWrapper::Debug_RemoveSkill(object_interface& player, int skill_id)
{
	StorageMap::iterator it = skillmap.find(skill_id);
	if (it == skillmap.end())
	{
		return;
	}
	releaseset.insert(it->second);
	skillmap.erase(it);
	EventReset(player);
}

void SkillWrapper::RemoveAllSkill(object_interface& player)
{
	for (auto& a : skillmap)
	{
		releaseset.insert(a.second);
	}
	skillmap.clear();
	RefreshSkillCapacity(player);
}

bool SkillWrapper::CanFakeRevive()
{
	if (state_mask & TYPEMASK_FAKE_REVIVE)
	{
		return object_interface::Rand() < _fake_revive_rate;
	}
	return false;
}

bool SkillWrapper::CheckTypeMask(filter_typemask_t mask) const
{
	return (state_mask & mask);
}

bool SkillWrapper::IsControlImmuneMove(int control_type)
{
	return StateRule::GetInstance().IsControlImmuneMove(control_type);
}

bool SkillWrapper::CanCasting(object_interface& player, int skill_id, int skill_level)
{
	unique_ptr<Skill> skill(SkillFactory::Create(skill_id, skill_level));
	if (!skill)
	{
		return false;
	}
	skill->Attach(player, CONTEXT_PERFORM);
	skill->CheckLevelDepend();
	return CheckCasting(player, skill.get(), false, true) == 0;
}

int SkillWrapper::GetDependLevel(int skill_id, int skill_level, int player_level)
{
	const SkillStub *stub = SkillFactory::GetStub(skill_id);
	if (!stub)
	{
		return skill_level;
	}
	int sk = stub->level_depend_skill;
	if (sk == -1)
	{
		return min(player_level, stub->max_level);//依赖于玩家等级
	}
	if (sk > 0)
	{
		return min(GetSkillLevel(sk), stub->max_level);//依赖于其它技能等级
	}
	return skill_level;
}

void SkillWrapper::SetCoexistTarget(const XID& xid)
{
	_coexist_target = xid;
}

const XID& SkillWrapper::GetCoexistTarget() const
{
	return _coexist_target;
}

int SkillWrapper::GetPerformCount(int skill)
{
	const SkillStub *ss = SkillFactory::GetStub(skill);
	if (!ss)
	{
		return 0;
	}
	return ss->PerformCount();
}

//根据恐惧周期内受到恐惧次数，计算恐惧时间
int SkillWrapper::CalcFearReduTm(int tm)
{
	int tm_now = object_interface::GetSysTime();
	if (tm_now - _last_fear_tm > 20)
	{
		_fear_turn = 0;
		_last_fear_tm = tm_now;//新的递减周期
	}
	else
	{
		if (_fear_turn == 1)
		{
			tm = tm * 0.5;
		}
		else
		{
			tm = 1;
		}
	}
	++ _fear_turn;
	return tm;
}

//根据魅惑周期内受到魅惑次数，计算魅惑时间
int SkillWrapper::CalcDeludedReduTm(int tm)
{
	int tm_now = object_interface::GetSysTime();
	if (tm_now - _last_deluded_tm > 20)
	{
		_deluded_turn = 0;
		_last_deluded_tm = tm_now;//新的递减周期
	}
	else
	{
		if (_deluded_turn == 1)
		{
			tm = tm * 0.5;
		}
		else
		{
			tm = 1;
		}
	}
	++ _deluded_turn;
	return tm;
}

void SkillWrapper::NextSkillWithoutCD(int min_ms, int filter_id)
{
	_skill_without_cd_min_ms = min_ms;
	_skill_without_cd_filter_id = filter_id;
}

bool SkillWrapper::CheckWithoutCD(object_interface& player, int skill_id, int cd_ms)
{
	if (_skill_without_cd_min_ms <= 0)
	{
		return false;
	}
	if (cd_ms < _skill_without_cd_min_ms)
	{
		return false;
	}
	if (!player.IsRuneActiveSkill(skill_id))
	{
		return false;
	}

	player.RemoveFilter(_skill_without_cd_filter_id);
	_skill_without_cd_min_ms = 0;
	_skill_without_cd_filter_id = 0;
	return true;
}

void SkillWrapper::AddSymbioticNewID_A(int newid)
{
	_buff_symbiotic_newid_a.clear();
	_buff_symbiotic_newid_a.insert(newid);
}

void SkillWrapper::RemoveSymbioticNewID_A(int newid)
{
	_buff_symbiotic_newid_a.erase(newid);
}

void SkillWrapper::AddSymbioticNewID_B(int newid)
{
	_buff_symbiotic_newid_b.insert(newid);
}

void SkillWrapper::RemoveSymbioticNewID_B(int newid)
{
	_buff_symbiotic_newid_b.erase(newid);
}

bool SkillWrapper::CheckSymbioticA(int newid)
{
	return _buff_symbiotic_newid_a.find(newid) != _buff_symbiotic_newid_a.end();
}

bool SkillWrapper::CheckSymbioticB(int newid)
{
	return _buff_symbiotic_newid_b.find(newid) != _buff_symbiotic_newid_b.end();
}

void SkillWrapper::StartRecordData(int data_type, uint64_t rid)
{
	if (rid == 0)
	{
		return;
	}
	_record_data[data_type][rid] = 0;
}

void SkillWrapper::FinishRecordData(int data_type, uint64_t rid)
{
	if (rid == 0)
	{
		return;
	}
	auto iter = _record_data.find(data_type);
	if (iter == _record_data.end())
	{
		return;
	}
	auto iter_1 = iter->second.find(rid);
	if (iter_1 == iter->second.end())
	{
		return;
	}
	iter->second.erase(iter_1);
}
uint64_t SkillWrapper::GetRecordData(int data_type, uint64_t rid)
{
	if (rid == 0)
	{
		return 0;
	}
	auto iter = _record_data.find(data_type);
	if (iter == _record_data.end())
	{
		return 0;
	}
	auto iter_1 = iter->second.find(rid);
	if (iter_1 == iter->second.end())
	{
		return 0;
	}
	return iter_1->second;
}
void SkillWrapper::FillSkillLevelMap(std::map<int, int>& m)
{
	for (auto& kv : skillmap)
	{
		if (kv.second)
		{
			m.insert(std::make_pair(kv.first, kv.second->GetLevel()));
		}
	}
}

int SkillWrapper::ForbidBuff(object_interface& player, int count)
{
	const std::set<int>& all_passive_longyu = player.GetForbidBuffPool();
	std::vector<int> forbid_list = {};
	for (auto& buff_id : all_passive_longyu)
	{
		forbid_list.push_back(buff_id);
	}
	if (count <= 0 || forbid_list.empty())
	{
		return 0;
	}
	if (count < forbid_list.size())
	{
		// 随机打乱
		std::random_device rd;
		std::mt19937 g(rd());
		std::shuffle(forbid_list.begin(), forbid_list.end(), g);
		forbid_list.resize(count);
	}
	_all_forbid_buffs.insert(std::make_pair(++_forbid_buff_serail_id, forbid_list));
	for (auto buff_id : forbid_list)
	{
		_forbid_buff_count[buff_id]++;
	}
	//打日志供测试
	std::stringstream ss;
	ss << "ForbidBuff: All forbiden longyu buff: ";
	for (auto& kv : _forbid_buff_count)
	{
		ss << kv.first << " : " << kv.second << "; ";
	}
	player.DebugSay(ss.str());
	return _forbid_buff_serail_id;
}

void SkillWrapper::UnForbidBuff(object_interface& player, int serial_id)
{
	auto iter = _all_forbid_buffs.find(serial_id);
	if (iter == _all_forbid_buffs.end())
	{
		return;
	}
	auto& forbid_list = iter->second;
	for (auto buff_id : forbid_list)
	{
		auto f_iter = _forbid_buff_count.find(buff_id);
		if (f_iter == _forbid_buff_count.end())
		{
			continue;
		}
		f_iter->second--;
		if (f_iter->second <= 0)
		{
			_forbid_buff_count.erase(f_iter);
		}
	}
	_all_forbid_buffs.erase(iter);
	//打日志供测试
	std::stringstream ss;
	ss << "UnForbidBuff: All forbiden longyu buff: ";
	for (auto& kv : _forbid_buff_count)
	{
		ss << kv.first << " : " << kv.second << "; ";
	}
	player.DebugSay(ss.str());
}

bool SkillWrapper::IsBuffForbid(int buff_id)
{
	auto iter = _forbid_buff_count.find(buff_id);
	if (iter == _forbid_buff_count.end())
	{
		return false;
	}
	if (iter->second <= 0)
	{
		//never
		return false;
	}
	return true;
}

void SkillWrapper::AddChallengeTarget(ruid_t target_id, int buff_id, int damage_reduce)
{
	ChallengeTargetInfo info;
	info.buff_id = buff_id;
	info.damage_reduce = damage_reduce;
	// 额外2秒保底时间，防止消息延迟导致的提前清理
	info.expire_tick = object_interface::GetTick() + MILLISEC_TO_TICK(2000);

	_challenge_targets[target_id] = info;
}

void SkillWrapper::RemoveChallengeTarget(ruid_t target_id)
{
	_challenge_targets.erase(target_id);
}

bool SkillWrapper::HasChallengeTarget(ruid_t target_id, int& out_damage_reduce) const
{
	auto it = _challenge_targets.find(target_id);
	if (it != _challenge_targets.end())
	{
		out_damage_reduce = it->second.damage_reduce;
		return true;
	}
	return false;
}

void SkillWrapper::CleanupExpiredChallengeTargets()
{
	const uint64_t now_tick = object_interface::GetTick();

	// 每秒清理一次，避免频繁操作
	if (now_tick - _last_challenge_cleanup_tick < MILLISEC_TO_TICK(1000))
	{
		return;
	}

	_last_challenge_cleanup_tick = now_tick;

	// 清理过期条目
	auto it = _challenge_targets.begin();
	while (it != _challenge_targets.end())
	{
		if (now_tick > it->second.expire_tick)
		{
			it = _challenge_targets.erase(it);
		}
		else
		{
			++it;
		}
	}
}

} //end namespace

