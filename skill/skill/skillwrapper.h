#ifndef __SKILL_SKILLWRAPPER_H
#define __SKILL_SKILLWRAPPER_H

#include <functional>
#include <map>
#include <unordered_map>
#include <vector>
#include <set>

#include "common/types.h"
#include "common/base_wrapper.h"
#include "object_interface.h"
#include "a3dvector.h"
#include "abaseoctets.h"

namespace WMSKILL
{
#define TALENT_VERSION			2
#define MAX_COVERAGE			32
#define BEYOND_MAX_COVERAGE		512
//#define SECOND_WEAKNESS_RATE    20
//忽略某些减伤 
enum IGNORE_PORP 
{	
  IGNORE_INVALID = 0, //不生效
  IGNORE_RATIO_ADD    = 0x1, //增伤
  IGNORE_RATIO_REDU   =  0x2,// 减伤
};

enum ERROR_START_SKILL
{
	REASON_OUT_OF_RANGE     = 1,  //距离太远
	REASON_HAS_ACTION       = 2,  //在其他action中
	REASON_IN_SEAL_MODE     = 3,  //沉默状态
	REASON_MOVING           = 4,  //移动中
	REASON_INVISIBLE        = 5,  //处于隐身状态
	REASON_COOLDOWN         = 6,  //技能冷却中
	REASON_INCORRECT_STATE  = 7,  //前提状态条件不满足
	REASON_INVALID_TARGET   = 8,  //错误的目标类型
	REASON_INVALID_DIR      = 9,  //错误的朝向
	REASON_FORCE_LACK       = 10, //内力不够
	REASON_RAGE_LACK        = 11, //怒气不够
	REASON_MP4_LACK       	= 12, //MP4不够
	REASON_STAMINA_LACK     = 13, //体力不够
	REASON_ITEM_LACK        = 14, //缺少物品
	REASON_INVALID_WEAPON   = 15, //错误的武器类型
	REASON_IN_COMBAT        = 16, //不能用于战斗状态
	REASON_UNKNOWN          = 17, //特殊未知错误
	REASON_NOT_MOVABLE      = 18, //非移动释放技能
	REASON_STANCE			= 21, //姿态不符合要求且不能自动切换, 比如在轻功中
	REASON_SUTRA			= 22, //需要在心法中激活才能使用
	REASON_RUSH				= 23, //加速跑中不能使用
	REASON_MOUNT_CANT		= 24, //骑乘状态不能使用
	REASON_MOUNT_MUST		= 25, //只能在骑乘状态使用
	REASON_FORBIDDEN_QTE	= 26, //禁止施放QTE 
	REASON_SILENT			= 27, //沉默中
	REASON_IDIP_FORBID		= 28, //idip禁止技能使用
	REASON_ORDER			= 29, //释放顺序错误
	REASON_TARGET_BORN_PROTECT= 30,//目标处于出生保护中
	REASON_HOLY_GHOST_MP_LACK = 31,//英灵mp不够
	REASON_REPU_LIMIT_ERR 	= 32,//声望限制条件不满足
};

enum RETCODE_RUN_SKILL
{
	RUNRET_RESET_TIMER      = 1, //不停止技能执行，修改下个技能段定时器到期时间
	RUNRET_OUTOF_RANGE      = 2, //超出射程，停止技能执行
	RUNRET_UNKNOWN_ERR      = 3, //未知错误，停止技能执行
	RUNRET_END              = 4, //技能所有过程段已执行完毕
};

//attack_msg.feedback_mask 取值
enum
{
	FEEDBACK_HIT		= 0x0001, //反馈击中消息给攻击者,是否CURSE也需要反馈？ FEEDBACK_HIT<=>!FEEDBACK_MISS
	FEEDBACK_DAMAGE 	= 0x0002, //反馈伤血消息给攻击者 真正伤血了，不管何种类型的血
	FEEDBACK_KILL_PLAYER= 0x0004, //反馈杀人消息给攻击者 FIXME: 濒死,player,not NPC
	FEEDBACK_MISS		= 0x0008, //反馈未击中消息给攻击者
	FEEDBACK_ZHUATOU	= 0x0010, //反馈抓投成功消息给攻击者,无须设置,总是反馈
	FEEDBACK_KILL_NPC	= 0x0020, //反馈杀死怪物的消息给攻击者mFIXME: 濒死
	FEEDBACK_TAR_HIT    = 0x0040, //反馈击中选中的目标消息给攻击者
	FEEDBACK_CRIT    	= 0x0080, //反馈暴击消息给攻击者
	FEEDBACK_HIT_CONTROL= 0x0100, //反馈击中已被控制的目标消息给攻击者
	FEEDBACK_HIT_BUFF	= 0x0200, //反馈击中拥有某个buff的目标消息给攻击者
	FEEDBACK_HIT_CONTROL_NOW=0x0400, //反馈击中被该过程造成控制的目标消息给攻击者


	//FEEDBACK_PARRY	= 0x20, //攻击被格挡
	//FEEDBACK_KILL		= 0x04, //反馈杀人消息给攻击者 FIXME: 濒死
};

//object_be_attacked.flags
//object_be_enchanted.flags
enum
{
	//ATTACK_FLAG_MISS_1		= 0x00000001, //上闪避(攻击上面时不能命中)
	//ATTACK_FLAG_MISS_2		= 0x00000002, //下闪避
	//ATTACK_FLAG_MISS_3		= 0x00000004, //纵(刺)闪避
	//ATTACK_FLAG_MISS_4		= 0x00000008, //横(扫)闪避
	ATTACK_FLAG_INHIBITED		= 0x00000010, //被克制
	//ATTACK_FLAG_BREAK		= 0x00000020, //被打断
	ATTACK_FLAG_DEBUFF_RESIST	= 0x00000040, //DEBUFF被抗性消灭掉了
	ATTACK_FLAG_IMMUNE		= 0x00000100, //免疫攻击
	ATTACK_FLAG_MISS		= 0x00000200, //未命中
	ATTACK_FLAG_CRIT		= 0x00000400, //此次攻击是爆击
	ATTACK_FLAG_SHIELD		= 0x00000800, //此次攻击被吸收
	ATTACK_FLAG_PARRY		= 0x00001000, //格挡
	ATTACK_FLAG_BROKEN_PARRY	= 0x00002000, //破防
	//ATTACK_FLAG_TUMBLE		= 0x00004000, //击倒
	ATTACK_FLAG_CTRL_MOVE        	= 0x00008000, //位移(因控制而产生的, 比如击退)
	ATTACK_FLAG_CTRL		= 0x00010000, //被控制
	//ATTACK_FLAG_HUIXIN		= 0x00010000, //会心
	//ATTACK_FLAG_DIYU		= 0x00020000, //抵御
	ATTACK_FLAG_SYNC_POS		= 0x00040000, //同步位置(位置并非控制导致)
	ATTACK_FLAG_CTRL_DIR		= 0x00080000, //控制改变了朝向
	ATTACK_FLAG_CTRL_WEAK		= 0x00100000, //弱点控制
	ATTACK_FLAG_CTRL_POZHAN		= 0x00200000, //破绽控制
	ATTACK_FLAG_PO_FANG		= 0x00400000, //破防
	ATTACK_FLAG_STRONG		= 0x00800000, //不屈
	ATTACK_FLAG_SEQUENCE	= 0x01000000, //闪电链
	ATTACK_FLAG_TREAT		= 0x02000000, //治疗
	ATTACK_FLAG_PET			= 0x04000000, //该操作来自攻击者的宠物/双生体/机甲
};

//SkillWrapper::GetType返回值
//技能分类模板: 技能类型  SkillStub::type
enum
{
	TYPE_ATTACK		= 0, //主动攻击
	TYPE_BLESS		= 1, //主动祝福
	TYPE_CURSE		= 2, //主动诅咒
	TYPE_PASSIVE		= 3, //被动技能
	TYPE_ASSIST		= 4, //辅助技能, 仅用来保存数据, 不能释放
	TYPE_BLESS_TEAM_PRIO= 5, //队友优先祝福
	TYPE_XP			= 7, //XP技能
	TYPE_NORMAL		= 8, //普攻溅射技能
	TYPE_SUB_CURSE		= 9, //子物体诅咒，主体技能主动攻击
	TYPE_TREAT_TEAM_PRIO= 10, //队友优先治疗
	TYPE_BLESS_PLAYER	= 11, //主动玩家祝福,目标为玩家或玩家镜像，不能对npc释放
	TYPE_CURSE_INVINCIBLE= 12, //主动诅咒, 可穿透无敌
	TYPE_SUB_ATTACK= 13, //子物体主动攻击,本体诅咒
};

inline bool CanCastByClient(int t)
{
	switch (t)
	case TYPE_ATTACK:
	case TYPE_BLESS:
	case TYPE_BLESS_PLAYER:
	case TYPE_CURSE:
	case TYPE_XP:
	case TYPE_NORMAL:
	case TYPE_SUB_CURSE:
	case TYPE_BLESS_TEAM_PRIO:
	case TYPE_TREAT_TEAM_PRIO:
	case TYPE_CURSE_INVINCIBLE:
	case TYPE_SUB_ATTACK:
		return true;
	return false;
}

//SkillWrapper::status_mask
enum
{
	STATUS_CASTING		= 0x00000001, //吟唱中
	STATUS_PARRYING		= 0x00000002, //戒备中
	STATUS_INHIBITED	= 0x00010000, //被克制了
	STATUS_GRABBED		= 0x01000000, //被投技抓住了
	STATUS_INVINC		= 0x02000000, //boss invinc中
	STATUS_FUZZY		= 0x04000000, //模糊状态中(点攻击伤害失效)
	STATUS_MASK_CASTER	= 0x0000FFFF, //攻击方状态掩码
	STATUS_MASK_VICTIM	= 0x00FF0000, //被攻击者状态掩码
};

enum
{
	CONTROLSTATE_SUSPEND  = 1, //击飞
	CONTROLSTATE_COLLAPSE = 2, //击溃
	CONTROLSTATE_TUMBLE   = 3, //击倒
	CONTROLSTATE_TUMBLE2  = 4, //破绽控制结束附加击倒 FIXME: 这个逻辑目前已经没有了
	CONTROLSTATE_MASK     = 0x0f,

	CONTROL_KNOCKBACK     = 0x40, //击退, 方便客户端播放动作
	CONTROL_PULLBACK      = 0x80, //拉回
};

enum
{
	PERFORM_FLAGS_TARGET_LIST	= 0x01, //击中目标列表
	PERFORM_FLAGS_MOVE		= 0x02, //有发生位移
	PERFORM_FLAGS_DIR		= 0x04, //有朝向变化
	PERFORM_FLAGS_PERSIST_MOVE	= 0x08, //(发生位移但)非瞬移
	PERFORM_FLAGS_DISPLAYE		= 0x10,	//需要广播，有动作
	PERFORM_FLAGS_TIME_CHANGED	= 0x20,	//perform时间有变化
	PERFORM_FLAGS_1ST_PERFORM	= 0x40, //这是第1个Perform(如果是蓄力的释放段也会置上该掩码)
	PERFORM_FLAGS_SEND_LEVEL	= 0x80, //需要发送技能等级

};
//技能本体
//状态前提条件模式 -> state_mask_premise_mode
enum 
{	
  STATE_PREMISE_ALL = 0, //满足所有的条件掩码可以释放 
  STATE_PREMISE_ANYONE = 1,//满足任意一个条件掩码可以释放
  STATE_PREMISE_NULL = 2, //跳过判断
};

enum
{
	//各种type_mask, 状态包中与战斗相关的属性
	//编辑器填的，不要乱改定义
	//技能状态或效果附加的掩码

	TYPEMASK_STUN	= 0x00000001LL,	//硬直
	TYPEMASK_AIR	= 0x00000002LL,	//浮空
	TYPEMASK_GROUND	= 0x00000004LL,	//倒地
	TYPEMASK_DIZZ	= 0x00000008LL,	//技能段-眩晕

	TYPEMASK_FROZEN	= 0x00000010LL,	//冰冻
	TYPEMASK_STONE	= 0x00000020LL,	//石化
	TYPEMASK_TIME   = 0x00000040LL, //时空锁定
	TYPEMASK_CHANGE_MODEL= 0x00000080LL, //变形

	TYPEMASK_ROOT	= 0x00000100LL,	//定身 stop
	TYPEMASK_SLOW	= 0x00000200LL,	//减速 ModifyscaleRunSpeedWithRedu
	TYPEMASK_FEAR 	= 0x00000400LL,	//恐惧
	TYPEMASK_FEAR_CAN_REDU= 0x00000800LL,//恐惧，可递减

	TYPEMASK_CHAIN 	= 0x00001000LL,	//束缚
	TYPEMASK_DELUDED_CAN_REDU = 0x00002000LL,	//魅惑，可递减
	TYPEMASK_SILENT = 0x00004000LL,	//沉默 silent
	TYPEMASK_TIME_WITH_OVERWHELMING = 0x00008000LL, //时空锁定+无敌

	TYPEMASK_SUPPER		= 0x00010000LL,	//霸体 immune
	TYPEMASK_KOTODAMA	= 0x00020000LL,	//伙伴言灵cd互斥
	TYPEMASK_DRAG 		= 0x00040000LL,	//拖拽 drag_point drag_line
	TYPEMASK_FAKE_REVIVE	= 0x00080000LL,   //死后续航

	TYPEMASK_INVISIBLE_NO_CHOOSE = 0x00100000LL,	//隐身,不能被人选中 invisibleNoChoose
	TYPEMASK_SLEEP		= 0x00200000LL,   //睡眠
	TYPEMASK_STANCE		= 0x00400000LL,   //姿态
	TYPEMASK_DIZZ_STATE	= 0x00800000LL,   //技能效果-眩晕
	TYPEMASK_DISPEL_SUPPER 	= 0x01000000LL,   //驱散霸体, 且一段时间内不可加上霸体(dispel_immune)
	TYPEMASK_PAUSE_SKILL_CD	= 0x02000000LL,   //锁定(暂停)技能方案里的技能和言灵技能的cd
	
	TYPEMASK_OVERWHELMING	= 0x04000000LL,   //无敌

	TYPEMASK_1PERFORM				= 0x08000000LL,	//过程段持续状态, 生命周期为该过程段
	TYPEMASK_STOP					= 0x10000000LL,	//执行主动位移技能时驱散
	TYPEMASK_END_THROW				= 0x20000000LL,	//结束投技后的控制
	TYPEMASK_SKILL_DISPEL			= 0x40000000LL,	//执行技能时驱散
	TYPEMASK_MOVE_DISPEL			= 0x80000000LL,	//(普通)移动时驱散

	//------------------------------------------------------------------
	TYPEMASK_TEMPERATURE= 0x100000000LL,	//温度和湿度
	TYPEMASK_WEATHER	= 0x200000000LL,	//天气
	TYPEMASK_BX 		= 0x400000000LL,	//暴血
	TYPEMASK_ATTACK_1 	= 0x800000000LL,	//特殊普攻限制，大剑，枪

	TYPEMASK_SILENT_ABSOLUTE = 0x1000000000LL,      //沉默 silent_must
	TYPEMASK_HAS_MECH 		= 0x2000000000LL,      //拥有机甲
	TYPEMASK_FORBID_MP_1_4  = 0x4000000000LL ,     //禁止获取mp1 mp4 
	TYPEMASK_TWINING		= 0x8000000000LL ,     //缠绕 
	TYPEMASK_CAGE			= 0x10000000000LL,     //牢笼
	TYPEMASK_CHAOTIC        = 0x20000000000LL,     //混乱效果
	TYPEMASK_CHAOTIC2        = 0x40000000000LL,     //混乱效果2
    TYPEMASK_SEVEN_CRIME_DEBUFF = 0x80000000000LL, // 七戒技能的Debuff
	TYPEMASK_OVERWHELMING_2	= 0x100000000000LL,   //无敌2
	TYPEMASK_DAMAGE_SHIELD	= 0x200000000000LL,   //护盾
	TYPEMASK_TRANS_BACK		= 0x40000000000LL,    //卷协（一段时间后传送到释放者实时位置）
	TYPEMASK_FORBID_BUFF	= 0x80000000000LL,    //禁止触发buff
};

//所有代表控制的状态掩码，在TYPEMASK_SUPPER以前但是不包括TYPEMASK_CHANGE_MODEL和TYPEMASK_SILENT
static const filter_typemask_t CONTROL_TYPEMASK = (0x000FFFFLL & ~(TYPEMASK_CHANGE_MODEL | TYPEMASK_SILENT)) ;

//千分比底数
const float CONFIG_RATIO = 1000.0f;

enum
{
	CAST_PRIOR_ID	= 0x0, //ID优先
	CAST_PRIOR_POS	= 0x1, //POS优先
	CAST_PRIOR_MASK	= 0x1,

	CAST_SELF_POS	= 0x2, //客户端指定了位移目标
	CAST_CTRL_END	= 0x4, //即使控制中也有效，结束后立刻使用
};

//control_end 协议里用
enum
{
	CONTROL_END_BY_JIEYUN = 0x01,
	CONTROL_END_WITH_RESUME = 0x02,
};

class Skill;
class Perform;
class Player;

class AttackFeedbackManager
{
	typedef std::vector<feedback_msg> Feedbacks;
	typedef std::map<unsigned int, Feedbacks> FeedbacksMap;

	FeedbacksMap feedback_map;

public:
	AttackFeedbackManager() {}

	size_t Size() const { return feedback_map.size(); }
	void AddFeedback(const feedback_msg& msg)
	{
		RemoveExpired();
		feedback_map[msg.seq].push_back(msg);
	}
	bool CheckFeedback(unsigned int seq, const int* type, std::vector<XID>* src);

private:
	void RemoveExpired();
};

struct PersistMoveInfo //持续位移信息
{
	int64_t _start_tick;		//开始时刻(>0表示正在持续位移中)
	A3DVECTOR3 _start_pos;		//开始点
	A3DVECTOR3 _dir;		//方向
	float _speed;			//速度(m/ms)
	float _total_dis;		//总距离
	bool _span_perform;		//是跨段位移(仅对主动位移有效)
	bool _redir;			//是否改变朝向到位移方向
	bool _end_skill;		//位移结束是否结束技能(用于跨段位移)

	PersistMoveInfo(): _start_tick(0), _speed(0), _total_dis(0), _span_perform(false), _redir(false), _end_skill(false) {}
	void Clear()
	{
		_start_tick = 0;
		_speed = 0;
		_total_dis = 0;
		_span_perform = false;
		_redir = false;
		_end_skill = false;
	}
};

//typedef void ObjectSkillFunc(object_interface& player, int skill_id, int skill_level);
class SkillWrapper
{
	typedef std::map<int, Skill*> StorageMap;

	StorageMap skillmap;				//TODO: 必须简化
	std::vector<unsigned char> skill_level_bak; // 技能等级备份，用于转职恢复
	Skill *active_skill;				//正在使用的技能
	std::set<Skill*> releaseset;			//等待释放的指针

	//状态相关
	filter_typemask_t state_mask;		//对象状态掩码 是当前存在buff的type_mask集合, 有些技能的释放需要有某buff或者没有某buff
	int state_count[64];				//状态掩码 位计数
	filter_typemask_t immune_mask;		//对象免疫掩码 immune状态效果和Perform参数可以指定immune_mask, 从而阻止具有某些type_mask的buff加上
	int immune_count[64];				//免疫掩码 位计数
	int status_mask;				//和技能相关的一些特殊状态
	int hit_cur;					//当前连击数
	int hit_inform;					//已通知给客户端的连击数
	int64_t hit_timeout_tick;			//连击过期时刻

	//int control_state;				//当前控制状态
	//int64_t control_end;				//控制最晚结束时刻
	//bool st4_control;				//合招导致的控制
	//int suspend;					//浮空？(来自各种控制状态)

	AttackFeedbackManager feedbacks;
	AttackFeedbackManager twin_feedbacks;//来自双生体,有自己的seq,所以要和玩家的分开存
	AttackFeedbackManager mech_feedbacks;//来自机甲,有自己的seq,所以要和玩家的分开存
	AttackFeedbackManager replisome_feedbacks;//来自职业10的分身,有自己的seq,所以要和玩家的分开存
	unsigned int attack_seq;			//每次攻击消息的序列号
	//unsigned int perform_seq;			//给perform的唯一标记号，申请才会有

	bool has_perform_weakpoint;			//当前过程是否存在判定针对弱点

	int no_control_counter;				//是否不受控制
	int immune_control_counter;			//是否免控(可以被无视免控破)
	int immune_control_except_hit_back_counter;	//免控对背刺无效
	int immune_control_except_force_counter;	//免控对强制控制无效
	int immune_control_except_weak_counter;		//免控在有弱点时无效
	int immune_control_except_full_pozhan_counter;	//免控在满破绽时无效
	int immune_control_except_in_action_counter;	//免控在有行为时无效
	int immune_control_except_in_action_except;	//免控在有行为时无效(触发该免控豁免的action不要受影响, 比如格挡技能本身)
	int immune_control_except_no_force_counter;	//免控在空蓝时无效

	int control_time;				//FIXME 基本是给客户端用, 可否不发?
	char control_type;
	char prev_control_type;				//连续控制中，之前的控制类型
	bool is_in_zhuatou_control;			//是否抓投控制
	uint64_t control_begin_tick;			//被控开始时刻, 如果连续被控则是第1次被控开始时刻
	char control_origin;				//控制产生类型
	uint64_t control_can_modify_tick1;		//能连续控制的时间限制(固定值)
	uint64_t control_can_modify_tick2;		//能连续控制的时间限制(可变值)
	bool control_move;				//被控并且带有位移
	A3DVECTOR3 control_move_pos;
	char control_dir;
	bool control_need_inform_client;		//被控制了, 是否要通知客户端
	bool zhuatou_need_feedback;			//被抓投了, 是否要反馈给抓投者

	int dodge_all_counter;				//是否闪避所有攻击
	int dodge_point_attack;				//闪避单体类型攻击
	float dmg_to_force_ratio;			//(格挡生效时)伤害转为内力消耗

	bool chu_pozhan_timeout;			//是否出破绽状态到期

	int no_damage_counter;				//是否不受攻击伤害

	int parry_yingzhi;				//格挡是否产生硬直

	int no_back_counter;				//是否没有背部

	//数值效果调整
	int heal_ratio;					//调整治疗效果比率，取值(-900~3000) 计算时要*0.001
	int energy_ratio;				//调整内力获取比率，取值(-900~3000) 计算时要*0.001
	int rage_ratio;					//调整怒气获取比率，取值(-900~3000) 计算时要*0.001
	int pozhan_add_delta;				//每次增加破绽时的修正值
	int pozhan_add_limit;				//每次可增加破绽的上限

	//临时变量
	uint64_t last_move_tick;			//最后一次移动发生的时刻
	bool move_attacker;				//非控制击退中是否要作为攻击者移动
	A3DVECTOR3 move_attacker_pos;			//非控制击退中要移动攻击者到pos
	XID _target;							//释放技能的目标
	unsigned short _msg_delay_tm;//攻击消息的延时，毫秒

	//bool feedback_grab;				//是否上次被投技抓已反馈给使投技者
	//int throw_type;					//投技抛开控制类型
	//float throw_dist;				//投技抛开距离
	//int throw_time;					//投技抛开动作持续的时间
	//bool throw_to;
	//A3DVECTOR3 throw_pos;

	int xp_level;					//xp技能等级
	time_t stun_timestamp;				//硬直时间

	bool _isWithoutCD;//不产生cd, 供调试用
	float _fake_revive_rate = 1.f;//死亡续航概率
	XID _coexist_target;
	int _damage_block = 0;//伤害格挡
	char  _ignore_some_damage_prop =  (char)IGNORE_INVALID;
	float _ignore_adddamage_prop_ratio = 1.000f;
	float _ignore_redudamage_prop_ratio = 1.00f;

	//恐惧递减
	int _last_fear_tm = 0;
	int _fear_turn = 0;

	//魅惑递减
	int _last_deluded_tm = 0;
	int _deluded_turn = 0;

	//眩晕适应
	int _last_dizzy_tm = 0;
	int _dizzy_turn = 0;

	//沉默适应
	int _last_silent_tm = 0;
	int _silent_turn = 0;
	
	//返控
	int _un_control_rate = 0; // 触发返控概率
	int _un_control_skill_id = 0; // 返控技能id

	bool _chess_mp_full = false;
	unsigned short _chess_damage_arg = 0;//自走棋伤害计算参数
	unsigned short _skill_without_cd_min_ms = 0;
	unsigned short _skill_without_cd_filter_id = 0;

	//技能效果symbiotic_a和symbiotic_b使用
	std::unordered_set<int> _buff_symbiotic_newid_a;//给我加symbiotic_a的人
	std::unordered_set<int> _buff_symbiotic_newid_b;//给我加symbiotic_b的人
	
	int replisome_damage_ratio ; //分身伤害调整  结果乘以0.001f 
	std::map<int, std::map<uint64_t, uint64_t>> _record_data = {}; //记录数据 data_type->{roleid->data}

	bool _refresh_skill_capacity = false;

	int _forbid_buff_serail_id = 0;
	std::map<int, std::vector<int>> _all_forbid_buffs = {};
	std::map<int, int> _forbid_buff_count = {};

	// 挑战目标管理数据结构
	struct ChallengeTargetInfo
	{
		int buff_id;           // 对应的buff配置ID
		int damage_reduce;     // 减伤千分比
		uint64_t expire_tick;  // 超时时间戳（保底机制）
	};

	std::unordered_map<ruid_t, ChallengeTargetInfo> _challenge_targets;  // 被我施加挑战buff的敌人列表
	uint64_t _last_challenge_cleanup_tick = 0;              // 上次清理的时间

public:
	SkillWrapper();
	~SkillWrapper();

public:
	static void Verify();
	static void ReloadChanges();
	static int LoadChanges(const char *encode_code, size_t encode_code_len);
	static void LoadBytesMove(const char *bytes);


	size_t FeedBackSize() const { return feedbacks.Size(); }
	//与GS接口
	//读取和保存PersistentData, 同时Load天生技能
	void LoadDatabase(object_interface& player, archive& ar);
	void StoreDatabase(object_interface& player, archive & ar);
	//发送给客户端的技能数据
	void StorePartial(object_interface& player, archive & ar);
	//将所有的附加技能和附加等级发给客户端
	void SendAllExtraAddonSkill(object_interface& player) const;
	//将state_mask发给客户端
	void SendStateMask(object_interface& player) const;

	//返回新的级别，错误返回-1
	int Learn(int skill_id, object_interface& player, bool is_upgrade);
	int LearnToLevel(int skill_id, object_interface& player, int level_to);
	bool UpgradeAllSkill(object_interface& player, const std::set<int>& canUpgrade, std::map<int,int>& result);
	//洗点 all: 1,洗去所有技能; 0,洗去当前职业技能
	int Forget(bool all, object_interface& player);
	//武器附加属性,某技能级别+level
	int Upgrade(int skill_id, unsigned int level, object_interface& player);
	int Degrade(int skill_id, unsigned int level, object_interface& player);
	//技能石,装备武器后获得特定技能
	int InsertSkill(int skill_id, unsigned int level, object_interface& player);
	int RemoveSkill(int skill_id, unsigned int level, object_interface& player, bool force = false);

	//让被动技能生效
	bool EventReset(object_interface& player, bool only_talent = false, unsigned int functional_label_whitelist_mask = 0x0);
	//变更被动技能 更换职业前调用
	bool OnForgetProfession(object_interface& player);
	//变更被动技能 更换职业后调用
	bool OnResetProfession(object_interface& player);
	//上面2个合体了
	bool OnChangeProf(object_interface& player, int old_prof, const std::vector<int>& old_skill,const std::vector<int>& new_skill);
	//玩家等级提升时调用
	void OnLevelUp(object_interface& player);
	//怪物使用被动技能
	bool NpcPassiveSkill(int skill_id, object_interface& player, int level);
	//被动技能生效(变身)
	bool PassiveSkillTakeEffect(object_interface& player, int skill_id, int level);
	//被动技能失效
	bool PassiveSkillUndoEffect(object_interface& player, int skill_id, int level);
	//遍历所有技能
	//void ForEachSkill(object_interface& player, ObjectSkillFunc* func);
	void ForEachSkill(object_interface& player, const std::function<void(object_interface& player, Skill* skill)>& func);

	bool IsPassiveSkillOf(object_interface& player, Skill* skill);
	void ExtractPassiveSkillInfo(Skill* skill, int& skill_id, int& level, int& functional_label);

	char PrepareSkill(object_interface& player, int skill_id, int level, int item_id, int item_where, int item_index,
	                  bool force=false/*true表示忽略身上是否有该技能*/);
	//开始执行技能，返回值定义: ERROR_START_SKILL
	char StartSkill(object_interface& player, unsigned char flags, const XID& target, const A3DVECTOR3& target_pos,
	                const A3DVECTOR3& self_pos, int& interval, int& next_interval, PersistMoveInfo& pmove, float target_body_size,std::vector<int>&target_list,const A3DVECTOR3& subobj_pos);
	//返回值定义: RETCODE_RUN_SKILL
	char Run(object_interface& player, int& interval, int& next_interval, float charge_ratio, PersistMoveInfo& pmove,std::vector<int>&target_list);
	int OnSkillEnd(object_interface& player, bool _break, PersistMoveInfo& pmove);
	//使用瞬发技能，返回值定义: ERROR_START_SKILL
	char InstantSkill(object_interface& player, int skill_id, const XID& target);
	char InstantSkill(object_interface& player, int skill_id, const A3DVECTOR3& target);
	char InstantSkill(object_interface& player, int skill_id, int level, int stage, unsigned char flags, const XID& target, const A3DVECTOR3& target_pos,
	                  bool force=false, int* delay_tm=NULL, bool skill_dispel_filter=true);
	//使用物品附加技能, 瞬发生效 0 成功, -1 失败
	int CastRune(int skill_id, int level, int cooldown_id, object_interface& player);
	//结束蓄力，返回true应停止当前技能段，执行后续技能段
	//bool CompleteCharge(int skill_id);

	//子物体被触发
	void TriggerSubobj(std::vector<int>&,object_interface& subobj, const XID& trigger_target, const subobj_env& env, attack_msg &msg, const std::set<ruid_t>& exclude, std::vector<XID>& hit_list);
	void TriggerSubobj(object_interface& subobj, const XID& trigger_target, const subobj_env& env, attack_msg &msg, const std::set<ruid_t>& exclude, std::vector<XID>& hit_list);
	//被Enchant(可能被攻击/被祝福/被诅咒)
	bool BeEnchanted(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, int aoe_count, attack_msg& msg);

	//体力耗尽
	void OnStaminaExhaust(object_interface& player);
	//远程控制子物体消失 FIXME
	void RemoteCtrlSubobjectDisappeared(object_interface& player, int param);
	//连击计时器超时
	void HitTimerTimeout(object_interface& player);
	//浮空状态计时器超时
	//void SuspendTimerTimeout(object_interface& player);
	//收到攻击反馈消息
	void NewAttackFeedback(object_interface& player, const XID& who, const feedback_msg& msg);
	//收到攻击反馈检查消息
	void CheckAttackFeedback(object_interface& player, const checkfeedback_msg& msg);
	//获取自己的perform时间
	int GetSkillPerformTimes(int skill_id, std::vector<int>& times);
	void OnPozhanFull(object_interface& player);
	void OnChuPozhanTimeout(object_interface& player);

	void OnHeartbeat();
	void OnHeartbeat(object_interface& oif);

	void SetChessDamageArg(int a) { _chess_damage_arg = a; }
	void SetChessMpFull(bool b) { _chess_mp_full = b; }
	bool IsChessMpFull() { return _chess_mp_full; }

	void AddSymbioticNewID_A(int newid);
	void RemoveSymbioticNewID_A(int newid);
	void AddSymbioticNewID_B(int newid);
	void RemoveSymbioticNewID_B(int newid);
	bool CheckSymbioticA(int newid);
	bool CheckSymbioticB(int newid);
	void StartRecordData(int data_type, uint64_t rid);
	void FinishRecordData(int data_type, uint64_t rid);
	uint64_t GetRecordData(int data_type, uint64_t rid);
	void FillSkillLevelMap(std::map<int, int>& m);
	float SimpleAttackDamage(object_interface& victim, int skill_damage_type, int damage_value, attack_msg msg);
public:
	static float GetSkillMaxRange(int skill_id);
	static float GetSkillMinRange(int skill_id);
	static bool GetSkillRange(int skill_id, float& min, float& max);
	static int GetSkillTimeType(int skill_id);
	static char GetType(int skill_id);
	static bool IsDragonbornSkill(int skill_id); //技能来源
	static bool IsPlayerSkill(int skill_id);
	static bool IsInstant(int skill_id);
	static bool IsDodgeSkill(int skill_id);
	static bool GetSkillInfo(int skill_id, char& forecast_type, char& cast_prior);
	static unsigned int GetSkillHitMask(int skill_id); //取技能第1个攻击段的攻击判定针对
	static bool CanCtrlEndCast(int skill_id);
	static bool CanCastInMask(int skill_id, int mask);
	static bool CanCastInSilent(int skill_id);
	static bool CanCastInDrag(int skill_id);
	static bool CanCastInWater(int skill_id);
	static bool CanCastInChain(int skill_id);
	static void FillAttackMsg4Tanqiang(attack_msg& msg, int skill_id, int level, object_interface& player);
	static int GetJudgeType(int skill_id);
	static unsigned int GetSkillActiveMask(int skill_id);
	static void GetSkillCD(std::set<int>& skill, std::set<int>& cd);//把技能id转化为冷却id
	static void GetSkillCD(object_interface& player, const std::set<int>& skill, std::map<int, int>& cd);//把技能id转化为冷却id和时间
	static int GetSkillCD(int skill);//把技能id转化为冷却id
	static int GetPerformCount(int skill);
	static bool IsPlayerAttacker(const attacker_info_t& ainfo);
public:
	//SkillWrapper内部或者与Filter接口
	void GetSkillLevels(std::map<int,int>& skills) const;	// skill_id -> level
	int GetSkillLevel(int skill_id) const;
	int GetSkillBaseLevel(int skill_id) const;
	filter_typemask_t GetStateMask() const { return state_mask; }
	filter_typemask_t GetImmuneMask() const { return immune_mask; }
	unsigned short GetMsgDelayTm() const { return _msg_delay_tm; }
	//bool IsCasting() const { return status_mask & STATUS_CASTING; }
	//bool IsInhibited() const { return status_mask & STATUS_INHIBITED; }
	//bool IsGrabbed() const { return status_mask & STATUS_GRABBED; }
	bool CanCancel(object_interface& player, int perform_id) const;  //当前技能可否被取消
	bool CanEndNow() const; //当前是否正在冗余段中
	bool CanMoveWhenCasting() const; //当前段是否可移动
	bool CanBreakBySilent() const { return true; } //当前技能是否会被沉默打断
	int GetHit(object_interface& oif);
	//int GetBigControl() const { return control_state; }
	//bool InBigControl() const { return control_state; }
	//bool BigControlCanContinue(int time) const;
	int GetHealRatio() const { return GetRatio(heal_ratio); }
	int GetForceRatio() const { return GetRatio(energy_ratio); }
	int GetRageRatio() const { return GetRatio(rage_ratio); }
	bool NeedMove() const { return move_attacker; }
	const A3DVECTOR3& GetMoveDestPos() const { return move_attacker_pos; }
	bool GetSkillInfo(char& forecast_type, char& cast_prior) const;
	bool CanCharge(object_interface& player, int& max_charge_time);
	void ThrowSubObj(object_interface& player, attack_msg &msg, const XID& target);

	filter_typemask_t ModifyStateMask(object_interface& oif, filter_typemask_t mask, bool add);
	filter_typemask_t ChangeStateMask(object_interface& oif, filter_typemask_t mask_remove, filter_typemask_t mask_add);
	void ModifyImmuneMask(filter_typemask_t mask, bool add);
	void SetStatus(int mask) { status_mask |= mask; }
	void ClearStatus(int mask) { status_mask &= ~mask; }
	//void SetGrabbed();
	//void ClearGrabbed();
	void ClearHit(object_interface& player);
	void UpdateHitTimer(object_interface& player, int tick);
	//void StartBigControl(object_interface& player, int state, int64_t end, bool st4, int time);
	//void ChangeBigControl(object_interface& player, int state, bool st4, int time);
	//void ChangeBigControl(object_interface& player, int state, int time);
	//void StopBigControl(object_interface& player);
	void EnhanceHealRatio(int ratio) { heal_ratio += ratio; }
	void EnhanceForceRatio(int ratio) { energy_ratio += ratio; }
	void EnhanceRageRatio(int ratio) { rage_ratio += ratio; }
	void EnhancePozhanAdd(int delta) { pozhan_add_delta += delta; }
	void SetPozhanAddLimit(int l) { pozhan_add_limit = l; }
	void MoveUpdate();
	void MoveAttacker(const A3DVECTOR3& pos, uint64_t tick);
	//void SetControlEvade(object_interface& player, int state, bool set, bool not_grab, int time);
	void GrabEnemy(const XID& enemy);
	//void SetGrabThrowInfo(int type, float dist, int time, bool throw_to, const A3DVECTOR3& pos);
	//void GetGrabThrowInfo(int& type, float&dist, int& time, bool& throw_to, A3DVECTOR3& pos) const;
	//void AddPozhan(object_interface& player, int delta);
	unsigned int GetAttackSeq() { return ++attack_seq; }
	unsigned int GetAttackSeq(int n) { attack_seq += n; return attack_seq; }
	//unsigned int GetPerformSeq() { return ++perform_seq; }
	void SetHasPerformWeakPoint(bool b) { has_perform_weakpoint = b; }
	
	void noControl(bool b);
	void ImmuneControl(bool b);
	void ImmuneControlExceptHitBack(bool b);
	void ImmuneControlExceptForce(bool b);
	void ImmuneControlExceptWeak(bool b);
	void ImmuneControlExceptFullPozhan(bool b);
	void ImmuneControlExceptInAction(object_interface& player, bool b);
	void ImmuneControlExceptNoForce(bool b);
	time_t GetStunTimestamp() { return stun_timestamp;}
	void SetInControl(object_interface& player, int time, char type, char origin, bool move, const A3DVECTOR3& dest_pos, char dir, bool zhuatou,bool need_check);
	void SetInformClient();
	void SetFeedbackZhuatou();
	void EndControl(object_interface& player, int reason);
	int AdjustControlTime(int time) const;
	char GetControlType() const { return control_type; }
	bool IsInZhuatouControl() const { return (control_type && is_in_zhuatou_control); }
	bool IsWineing() const;

	void DodgeAll(bool b);
	void DodgePointAttack(bool b);
	void SetDmgToForceRatio(float r) { dmg_to_force_ratio = r; }
	void noDamage(bool b);
	void ParryYingzhi(bool b);
	void noBack(bool b);
	void WithoutCD() {_isWithoutCD = true;}
	bool IsWithoutCD() { return _isWithoutCD;}
	bool CanCasting(object_interface& player, int skill_id, int skill_level);
	void Debug_RemoveSkill(object_interface& player, int skill_id);
	void RemoveAllSkill(object_interface& player);
	void SetFakeReviveRate(float r) { _fake_revive_rate = r; }
	bool CanFakeRevive();
	bool HasFakeReviveBuff() { return (state_mask & TYPEMASK_FAKE_REVIVE); }
	void AddDamageBlock(int a) { _damage_block += a; }
	bool CheckTypeMask(filter_typemask_t mask) const;
	static bool IsControlImmuneMove(int control_type);
	void SetCoexistTarget(const XID& xid);
	const XID& GetCoexistTarget() const;
	int GetDependLevel(int skill_id, int skill_level, int player_level);
	int CalcFearReduTm(int tm);
	int CalcDeludedReduTm(int tm);
	void NextSkillWithoutCD(int min_ms, int filter_id);
	bool CheckWithoutCD(object_interface& player, int skill_id, int cd_ms);
	void SetIgnoreSomeDamageProp(int b,int ratio = 1000) 
	{ 
		_ignore_some_damage_prop |= b; 
		if(b & IGNORE_RATIO_ADD)
		{
			_ignore_adddamage_prop_ratio = ratio * 0.001f; 
		}
		if(b & IGNORE_RATIO_REDU)
		{
			_ignore_redudamage_prop_ratio = ratio * 0.001f; 
		}
	}
	void ClrIgnoreSomeDamageProp()
	{
		_ignore_some_damage_prop = 0;
		_ignore_adddamage_prop_ratio = 1.000f;
		_ignore_redudamage_prop_ratio = 1.000f;
	}
	float GetIgnoreAddDamagePropRatio() const { return _ignore_some_damage_prop & IGNORE_RATIO_ADD ? _ignore_adddamage_prop_ratio : 1.000f; }
	float GetIgnoreReduDamagePropRatio() const { return _ignore_some_damage_prop & IGNORE_RATIO_REDU ? _ignore_redudamage_prop_ratio : 1.000f; }

	void SetUnControlRate(int rate) { _un_control_rate = rate; }
	int GetUnControlRate() { return _un_control_rate; }
	void SetUnControlSkill(int skill_id) { _un_control_skill_id = skill_id; }
	int GetUnControlSkill() { return _un_control_skill_id; }
	int GetReplisomeDamageRatio() { return replisome_damage_ratio; }
	void SetReplisomeDamageRatio(int ratio){ replisome_damage_ratio = replisome_damage_ratio <= 2000 && replisome_damage_ratio > 0 ? ratio : 1000; }
	int ForbidBuff(object_interface& player, int count);
	void UnForbidBuff(object_interface& player, int serial_id);
	bool IsBuffForbid(int buff_id);

public:
	// 挑战buff管理接口
	void AddChallengeTarget(ruid_t target_id, int buff_id, int damage_reduce);
	void RemoveChallengeTarget(ruid_t target_id);
	bool HasChallengeTarget(ruid_t target_id, int& out_damage_reduce) const;
	void CleanupExpiredChallengeTargets(); // 保底清理机制

public:
	//打算删掉的
	void ResetCooldown(object_interface& player, int skill_id);
	void BuffSkill(object_interface& player, int id, int level = 1); //buff_skill效果使用
	void BuffSkill_target(object_interface& player, int id, int level, const XID& tar); //可以指定目标
	void BuffSkill_subobj(object_interface& player, int id); //可以生成子物体
	void BuffSkill_subobj_with_level(object_interface& player, int id, int level); //可以生成子物体
	void CalcSilentTime(object_interface& oif, int& c_time);
	void CalcDizzyTime(object_interface& oif, int& c_time);
	void AttenuatControlTime(int rate, int& c_time, int c_count);
	static int GetPerform0StaminaCost(object_interface& player, int skill_id); //FIXME: 这种接口是有问题的, 因为表达式原因
	static bool TestCoolTime(object_interface& player, int skill_id);
	//static bool GetLearnLimit(int skill_id, int level_to, int& max_level, int& player_level_require, int& point_per_level);
	static void CalcStunTime(object_interface& oif,int& time,int attack_stun);
	static bool IsSameCD(int skill_1, int skill_2);
private:
	bool IsTargetBeHit(object_interface& target, attack_msg& msg);
	float AttackDamage(object_interface& victim, const A3DVECTOR3& src_pos, const attack_msg& msg, int &flag, int &feedback, int skill_origin);
	float CalcTreat(object_interface& target, const attack_msg& msg, int &flag);
	filter_typemask_t CalculateMask(filter_typemask_t mask, bool add, int *count);
	int GetRatio(int rawratio) const;
	void OnSkillCast(object_interface& player, Skill* skill);
	char CheckCasting(object_interface& player, Skill* skill, bool check_range,bool check_cooldown = true);
	void SetSkillTalent(Skill* skill, const int* list);
	void LoadInnateSkills(object_interface& player);
	Skill* FindSkill(int skill_id);
	void RefreshPassiveSkill(object_interface& player, Skill *skill, bool only_talent = false, const std::function<bool(object_interface&, Skill *)>& extra_cond = nullptr);
	bool BeAttacked(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill); //被攻击
	bool BeTreated(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill); //被治疗
	bool BeBlessed(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill); //被祝福
	bool BeCursed(object_interface& target, const XID& src, const A3DVECTOR3& src_pos, attack_msg& msg, Skill& skill); //被诅咒
	bool IsImmuneControl(attack_msg& msg, Skill& skill, bool inhibited, const A3DVECTOR3& direct_attacker_pos, bool hit_back);
	bool AddControlFilter(const XID& src, attack_msg& msg, Skill& skill, bool inhibited, const A3DVECTOR3& direct_attacker_pos, bool hit_back, const Perform *perform);
	bool AddParryYingzhi(attack_msg& msg, Skill& skill, bool inhibited, const A3DVECTOR3& direct_attacker_pos);
	void UpdateHit(object_interface& oif, int add, int time, bool inform);
	void CheckSkill(object_interface& player);
	void RefreshSkillCapacity(object_interface& player);
	void DoRefreshSkillCapacity(object_interface& player);

	bool __ClearBaseLevel(int id,Skill* sk,object_interface& player);
	void __BuffSkill(object_interface& player, int id, int level, const XID& tar, bool gen_subobj);
	int __LearnToLevel(int skill_id, int level_to);
	filter_typemask_t _RawModifyStateMask(filter_typemask_t mask, bool add);
	void _CheckControlMaskChange(object_interface& oif, filter_typemask_t old_mask, filter_typemask_t new_mask);
};

struct calc_subobj_create_pos_t
{
	calc_subobj_create_pos_t(const A3DVECTOR3& spos, const A3DVECTOR3& sdir, const A3DVECTOR3& subpos)
	: self_pos(spos), self_dir(sdir), client_subobj_pos(subpos){}
	int ctype = 0;
	int angle = 0;
	float distance = 0;
	const A3DVECTOR3& self_pos;
	const A3DVECTOR3& self_dir;
	const A3DVECTOR3& client_subobj_pos;//客户端想要生成子物体的位置
};

typedef std::function<bool(A3DVECTOR3&)> get_pos_fun_t;
bool CalcSubobjCreatePosDir(const calc_subobj_create_pos_t& args,
	get_pos_fun_t& get_grabbed_pos,
	get_pos_fun_t& get_target_pos,
	A3DVECTOR3& pos, A3DVECTOR3& dir);

};

void __ENABLE_SKPRINTF(bool e);

#endif

