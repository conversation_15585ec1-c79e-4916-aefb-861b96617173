
#ifndef _STATE_FILTER_H
#define _STATE_FILTER_H

#include <vector>
#include <map>

#include "filter.h"
#include "trigger.h"
#include "stateeffect.h"
#include "skillwrapper.h"
#include "debugprint.h"
#include "constant.h"

// FIXME: buff的加上与生效, 设计有点不合理
// FIXME: buff的特殊生效条件, 策划目前还没有实际使用, 不好说可靠否

extern "C"
{
#include <time.h>
}

using namespace WMSKILL;
using namespace std;

struct get_buff_effect_data_t
{
	int effect_idx = 0;
	int data_idx = 0;
	int data_value = 0;
};

class StateFilter: public filter
{
protected:
	typedef vector<Trigger*> TriggerList;

	//特定filter的固定数据
	unsigned int show_icon; //状态包图标, 客户端根据id到模板中查找图标路径
	unsigned short gfx_id; //光效ID~[0,MAX_GFX_STATE_COUNT-1]
	filter_typemask_t type_mask; //包类型 64位掩码 TODO
	filter_eventmask_t event_mask; //状态关心的filter事件类型
	char time_type; //时间类型
	char prob_type; //成功率判断方式
	char resist_type; //对应的属性状态抗性
	char attach_mode; //状态包加上条件
	char poly_rule; //叠加覆盖规则
	char poly_max; //叠加次数上限
	int min_level; //目标最低等级
	int max_level; //目标最高等级
	unsigned int prof_mask; //目标职业掩码
	//阈值型: 所有阈值都满足则生效，只一次
	//事件型: 任一条件满足则生效，可多次
	//阈值+事件型: 前两者的并集
	TriggerList work_trigger; //生效条件管理器
	uint64_t work_mask; //生效条件事件掩码
	//阈值型: 任一阈值满足则生效, 共一次
	//事件型: 任一条件满足则生效
	//阈值+事件型: 前两者的并集
	TriggerList quit_trigger; //结束条件管理器
	uint64_t quit_mask; //结束条件事件掩码
	vector<StateEffect*> effect_stubs; //效果的集合
	int version; //状态包的版本号(用于存盘检查)
	bool skillfx_prio;//是否使用最高等级技能光效
	unsigned int category;//特殊类型
	bool is_no_typemask; //是否无类型（不可被驱散，不可被免疫）(给关卡用)

	StateFilter() { Init(); }

	void RegisterStub(StateEffect *stub, uint64_t p=0);

public:
	explicit StateFilter(object_interface& object): filter(object, 0) { Init(); }
	virtual ~StateFilter();

	int GetStateId() const { return _filter_id - FILTERID_BEGIN; }

	int GetShowTimeout() const; //获取filter设定持续时间(s, 显示用, 通道技能即使有持续时间也返回0)
	bool IsOverInCombat() const { return (quit_mask & OVER_COMBAT); }
	bool IsInstant() const { return time_type == FILTERTIME_INSTANT; }
	bool IsPassive() const { return (extra_mode & BUFFMODE_PASSIVE) != 0; }
	bool IsChannel() const { return (extra_mode & BUFFMODE_CHANNEL) != 0; }
	char GetSucceedType() const { return prob_type; }
	int GetSucceedParam() const { return probability; }
	char GetResistType() const { return resist_type; }
	char GetTimeType() const { return time_type; }
	int GetTimeParam() const { return time; }
	int GetMinLevel() const { return min_level; }
	int GetMaxLevel() const { return max_level; }
	int GetOccupationMask() const { return prof_mask; }
	char GetAttachMode() const { return attach_mode; }
	float GetFirstData() const;
	int GetData(int effect_idx, int data_idx) const;
	void UpdateBuff();
	void ModifyGfx(uint64_t attacker_gfx_modify_mask);
	void SelfModifyGfx();

	void SetSucceedParam(int param) { probability = param; }
	void SetTimeParam(int param) { time = param; }
	void SetPassive() { extra_mode |= BUFFMODE_PASSIVE; }
	void SetChannel();
	void SetClientData(unsigned int index, float data) { client_data[index%5] = data; }
	void SetClientData2(int data) { client_data_1_2 = data; }
	void SetFromName(const char *name, int len) { /*from_name = abase::octets(name, len);*/ }
	void SetFromRid(ruid_t rid) { from_rid = rid; }
	void SetFromSelf();
	void SetSkillfxPrio(bool b) { skillfx_prio = b; }
	virtual ruid_t GetFromRid() const override { return _mask & filter::FM_PERSONAL ? from_rid : 0; }
	virtual ruid_t RawGetFromRid() const override { return from_rid; }
	virtual void SettlePersist(float rate) override;

	virtual filter_typemask_t GetTypeMask(int reason = ER_UNKNOWN) override {
		if (is_no_typemask) {
			return 0;
		}
		return type_mask;
	}
	virtual void SetEffectData(unsigned int index, float data);
	virtual void SetEffectData2(unsigned int index, int data);
	virtual void Setup(Skill *skill, const A3DVECTOR3& caster_pos, attack_msg* atk_msg = nullptr);
	virtual bool PreAttach(); //object_interface::AddFilter之前调用, 对于瞬时包就没必要执行真正的Add了
	virtual void Reset(int prop_idx) override;
	virtual bool IsVisibleByRid(ruid_t rid) const override;
	virtual void Dump(std::ostringstream& oss) override;
	inline bool PassiveLongyu() override {return  category & STATEFILTER_PASSIVELONGYU; }
	inline bool PassiveTalent() override {return  category & STATEFILTER_PASSIVETALENT; }
	bool CheckForbid() override;

protected:
	//继承自filter
	virtual void TranslateSendAttack(const XID& target, attack_msg& msg) override;
	virtual void TranslateSendSubObjAttack(const XID& target,attack_msg& msg) override; //新添加的处理子物体的技能
	virtual void TranslateRecvAttack(const XID& attacker, attack_msg& msg) override { ASSERT(false); } //早于AdjustDamage
	virtual void Heartbeat(int tick) override;
	virtual void AdjustDamage(float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag) override; //早于BeAttacked
	virtual void BeforeAdjustDamage(float& dmg, const XID& attacker, const attack_msg& msg, int& attack_flag) override; //早于BeAttacked
	virtual void EnemyDodge(const XID& target) override { CheckEvent(EFFECT_MISS); }
	virtual void AttackEnemy(const XID& target) override { CheckEvent(EFFECT_HIT); }
	virtual void AttackTarget(const XID& target) override;
	virtual void CritEnemy(const XID& target) override { CheckEvent(EFFECT_CRIT); }
	virtual void GrabEnemy(const XID& target) override { /*_oif.GetSkillWrapper().GrabEnemy(target);*/ } //TODO: delete
	virtual void DamageEnemy(const XID& target) override { CheckEvent(EFFECT_INJURE); }
	virtual void BeAttacked(const XID& who, const attacker_info_t& info, float damage, unsigned int flags) override; //即使闪避也调用
	virtual void BeforeDeath() override { CheckEvent(EFFECT_BEFORE_DEATH); }
	virtual void KillEnemy(const XID& who, int level,bool is_npc) override
	{ 
		CheckEvent(EFFECT_KILL); 
		if(is_npc)
		{
			CheckEvent(EFFECT_KILL_NPC);//单单杀怪生效	
		}
		//__PRINTF("StateFilter KillEnemy who %lx  is_npc %d\n",who.id,is_npc);
	}
	virtual void HPPercentChange() override {CheckEvent(EFFECT_HP_PENCENT);}
	virtual void FatalDamage() override {CheckEvent(EFFECT_FATAL_DAMAGE);}
	virtual void ShieldBreak() override {CheckEvent(EFFECT_SHIELD_BREAK);}
	virtual void LeaveCombat() override;
	virtual void EnterCombat() override {CheckEvent(EFFECT_ENTER_COMBAT);}
	virtual void Move() override;
	virtual void AddFilter(int filter_id) override { CheckEvent(EFFECT_STATE); }
	virtual void ControlStart() override {CheckEvent(EFFECT_CONTROL_START);}
	virtual void ControlEnd() override {CheckEvent(EFFECT_CONTROL_END);}
	virtual void Revive() override {CheckEvent(EFFECT_REVIVE);}
	virtual void UseMP(int value) override;
	virtual void Treat(float& value) override;
	virtual void SetCD(int cd_id) override;
	virtual void FinalAdjustDamage(const XID& attacker, float& damage) override;
	virtual void CastSkill() override {CheckEvent(EFFECT_CAST_SKILL);}
	virtual void ReduceShieldInstant(int value) override;
	//以上函数均对应filter事件, 每次被调用后filter_manager均会检查is_deleted
	virtual void Merge(filter *pFilter) override { ASSERT(false); } //不知道干啥用的
	virtual bool CanSave() override; //是否逻辑可保存, 和FM_SAVE_DB_DATA标志无关
	virtual bool Save(archive& ar) override;
	virtual bool Load(archive& ar, uint save_tm) override;
	virtual void OnAttach() override;
	virtual void OnDetach() override;
	virtual void OnModify(int ctrlname, void *ctrlval, size_t ctrllen) override;
	virtual void OnChangePolyCount(int op, char times) override; //直接修改buff叠加次数
	virtual int OnGetTimeOut() override; //获取filter设定持续时间(s)
	virtual int GetLevel() const override {return poly_count + 1;};

protected:
	void Init();
	void OnInstant();
	bool InitialCheck();
	void FirstActivate();
	void TakeEffect(bool instant_only);
	void TakeEffect_NoInstant();
	void OnHeartbeat(int tick);
	int CheckTimer(TriggerList& list, int to, int expect);
	void CheckEvent(int64_t event);
	int CheckTrigger(TriggerList& list, int64_t event);
	void Activate();
	void Deactivate();
	bool PolyPolicy();
	void SetAttach() { do_attach = true; }

	static void SetupFeedback(uint64_t condition, int& mask);

protected:
	//运行时数据
	int time; //持续时间(ms)
	int probability; //成功概率~[0,1000]
	char poly_count; //当前叠加次数, if(poly_max) poly_count~[0,poly_max-1]
	int work_feedback; //生效条件判断需要的反馈事件类型
	int quit_feedback; //结束条件判断需要的反馈事件类型
	bool is_active; //状态是否处于激活
	int timeout; //状态心跳计数(秒)，用于延时生效判定
	int extra_mode; //状态包特殊类型标识 注意: 不存盘, Load前后可能不一致
	filter_typemask_t immune_mask; //免疫的包类型 64位掩码
	filter_typemask_t dispel_mask; //驱散的包类型 32位掩码

	float client_data[5]; //发给客户端显示用
	int client_data_1_2;//一个技能效果的第二个外参
	abase::octets from_name; //加上者名字
	ruid_t from_rid;//加上者的rid

	bool do_attach; //临时变量
};

#if 0
class ControlFilter: public StateFilter
{
	int control_state;
	float distance; //击退(位移)距离
	bool push; //推开?
	int combo_mask; //起承转合
	A3DVECTOR3 direction; //位移方向
	A3DVECTOR3 _caster_pos;

protected:
	ControlFilter() { Init(); }
	ControlFilter(object_interface& object): StateFilter(object) { Init(); } //extra_mode = BUFFMODE_DOSETUP;

private:
	virtual void SetEffectData(unsigned int index, float data);
	virtual void Setup(Skill *skill, const A3DVECTOR3& caster_pos);
	virtual bool PreAttach();
	virtual bool CanSave() { return false; } //控制buff没必要保存
	virtual void OnAttach();
	virtual void OnDetach();
	virtual void OnModify(int ctrlname, void *ctrlval, size_t ctrllen);
	virtual int OnGetTimeOut() { return 0; } //需要精确定时, 所以这里不用心跳控制状态超时
	virtual int OnTimer();

	void Init();
	void KnockBack();
};

class TimerFilter: public StateFilter
{
protected:
	TimerFilter() {}
	TimerFilter(object_interface& object): StateFilter(object) {}

private:
	virtual bool PreAttach();
	virtual bool CanSave() { return false; } //精确时间buff没必要保存
	virtual void OnAttach();
	virtual void OnDetach();
	virtual void OnModify(int ctrlname, void *ctrlval, size_t ctrllen);
	virtual int OnGetTimeOut() { return 0; } //需要精确定时，所以这里不用心跳控制状态超时
	virtual int OnTimer() { return 1; } //到达结束时刻，返回1删除filter
};
#endif

#endif

